from rest_framework import serializers
from django.contrib.auth import get_user_model
from centers.serializers import CenterListSerializer

User = get_user_model()

class UserSerializer(serializers.ModelSerializer):
    """Serializer for the User model."""
    password = serializers.CharField(write_only=True, required=False)
    center_details = CenterListSerializer(source='center', read_only=True)

    class Meta:
        model = User
        fields = ('id', 'email', 'username', 'first_name', 'last_name', 'role', 'center',
                  'center_details', 'is_active', 'password')
        read_only_fields = ('is_staff', 'is_superuser', 'date_joined', 'last_login')
        extra_kwargs = {'center': {'write_only': True}, 'username': {'required': False}}

    def create(self, validated_data):
        password = validated_data.pop('password', None)
        user = User.objects.create(**validated_data)
        if password:
            user.set_password(password)
            user.save()
        return user

    def update(self, instance, validated_data):
        password = validated_data.pop('password', None)
        user = super().update(instance, validated_data)
        if password:
            user.set_password(password)
            user.save()
        return user

class UserListSerializer(serializers.ModelSerializer):
    """Simplified serializer for listing users."""
    center_name = serializers.CharField(source='center.name', read_only=True)

    class Meta:
        model = User
        fields = ('id', 'email', 'username', 'first_name', 'last_name', 'role', 'center_name', 'is_active')

class LoginSerializer(serializers.Serializer):
    """Serializer for user login with automatic tenant selection."""
    email = serializers.EmailField()
    password = serializers.CharField(style={'input_type': 'password'})
