import os
import django

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'neocamelot.settings')
django.setup()

from django.db import connection

def check_user_table_structure():
    """Check the structure of the accounts_user table in all schemas."""
    print("=== Checking User Table Structure ===")
    
    # Get all schemas in the database
    with connection.cursor() as cursor:
        cursor.execute("""
            SELECT schema_name
            FROM information_schema.schemata
            WHERE schema_name NOT IN ('information_schema', 'pg_catalog', 'pg_toast', 'pg_temp_1', 'pg_toast_temp_1')
            ORDER BY schema_name
        """)
        schemas = cursor.fetchall()
        
        print(f"Found {len(schemas)} schemas in the database")
        
        # Process each schema
        for schema in schemas:
            schema_name = schema[0]
            print(f"\nProcessing schema: {schema_name}")
            
            # Check if the accounts_user table exists in this schema
            cursor.execute(f"""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_schema = '{schema_name}' 
                    AND table_name = 'accounts_user'
                )
            """)
            table_exists = cursor.fetchone()[0]
            
            if not table_exists:
                print(f"  accounts_user table does not exist in schema {schema_name}")
                continue
            
            # Get the columns of the accounts_user table
            cursor.execute(f"""
                SELECT column_name, data_type
                FROM information_schema.columns
                WHERE table_schema = '{schema_name}'
                AND table_name = 'accounts_user'
                ORDER BY ordinal_position
            """)
            columns = cursor.fetchall()
            
            print(f"  Found {len(columns)} columns in accounts_user table in schema {schema_name}")
            
            for column in columns:
                column_name, data_type = column
                print(f"  {column_name}: {data_type}")
            
            # Get a sample user from the table
            try:
                cursor.execute(f"""
                    SELECT *
                    FROM "{schema_name}".accounts_user
                    LIMIT 1
                """)
                user = cursor.fetchone()
                
                if user:
                    print(f"\n  Sample user from {schema_name}.accounts_user:")
                    for i, column in enumerate(columns):
                        column_name = column[0]
                        print(f"  {column_name}: {user[i]}")
                else:
                    print(f"\n  No users found in {schema_name}.accounts_user")
            except Exception as e:
                print(f"\n  Error getting sample user: {str(e)}")

if __name__ == "__main__":
    check_user_table_structure()
