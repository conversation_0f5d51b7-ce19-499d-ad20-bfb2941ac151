"""
Custom authentication for the citizens app.
"""

import logging
from accounts.jwt_authentication import JWTAuthentication

# Configure logging
logger = logging.getLogger(__name__)

class CitizenJWTAuthentication(JWTAuthentication):
    """
    Custom JWT authentication for the citizens app.

    This authentication class allows unauthenticated access to endpoints
    with the 'detail=true' parameter.
    """

    def authenticate(self, request):
        """
        Authenticate the request and return a two-tuple of (user, token).

        If the request has a 'detail=true' parameter, allow unauthenticated access.
        """
        # Log the request path and headers for debugging
        logger.debug(f"CitizenJWTAuthentication: Authenticating request to {request.path}")
        logger.debug(f"CitizenJWTAuthentication: Headers: {dict(request.headers)}")

        # Check if the request has a 'detail' query parameter set to 'true'
        # or if 'detail=true' is in the URL path
        has_detail_param = (
            request.query_params.get('detail') == 'true' or
            'detail=true' in request.get_full_path()
        )

        # If the detail=true parameter is present, allow unauthenticated access
        if has_detail_param:
            logger.info(f"CitizenJWTAuthentication: Allowing unauthenticated access for request with detail=true: {request.path}")
            return None

        # Extract token from request
        token = self._get_token_from_request(request)
        if not token:
            logger.warning(f"CitizenJWTAuthentication: No token found in request to {request.path}")
            return None

        logger.debug(f"CitizenJWTAuthentication: Found token: {token[:10]}...")

        # Use the standard JWT authentication
        result = super().authenticate(request)

        if result:
            user, token = result
            logger.info(f"CitizenJWTAuthentication: Successfully authenticated user {user.email} (ID: {user.id})")
        else:
            logger.warning(f"CitizenJWTAuthentication: Authentication failed for request to {request.path}")

        return result

    def _get_token_from_request(self, request):
        """
        Extract token from request.
        """
        # Try to get token from Authorization header with Bearer prefix
        auth_header = request.headers.get('Authorization', '')
        if auth_header.startswith('Bearer '):
            return auth_header.split(' ')[1].strip()

        # Try to get token from cookies
        token = request.COOKIES.get('jwt_access_token')
        if token:
            return token

        return None
