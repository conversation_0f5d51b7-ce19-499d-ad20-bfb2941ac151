"""
Custom authentication for the citizens app.
"""

import logging
from rest_framework import authentication
from accounts.jwt_authentication import JW<PERSON>uthentication

# Configure logging
logger = logging.getLogger(__name__)

class CitizenJWTAuthentication(JWTAuthentication):
    """
    Custom JWT authentication for the citizens app.
    
    This authentication class allows unauthenticated access to endpoints
    with the 'detail=true' parameter.
    """
    
    def authenticate(self, request):
        """
        Authenticate the request and return a two-tuple of (user, token).
        
        If the request has a 'detail=true' parameter, allow unauthenticated access.
        """
        # Check if the request has a 'detail' query parameter set to 'true'
        # or if 'detail=true' is in the URL path
        has_detail_param = (
            request.query_params.get('detail') == 'true' or 
            'detail=true' in request.get_full_path()
        )
        
        # If the detail=true parameter is present, allow unauthenticated access
        if has_detail_param:
            logger.info(f"Allowing unauthenticated access for request with detail=true: {request.path}")
            return None
        
        # Otherwise, use the standard JWT authentication
        return super().authenticate(request)
