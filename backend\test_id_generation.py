import os
import django
import requests
import json

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'neocamelot.settings')
django.setup()

from django.db import connection
from django_tenants.utils import tenant_context, get_tenant_model
from citizens.models import Citizen

# Configuration
API_BASE_URL = 'http://127.0.0.1:8000/api'
TOKEN = '01aa7be65fbda335a0b29edd56c967ad6112fa6b'
SCHEMA_NAME = 'kebele16'  # Change this to the schema you want to test

# Function to create a test citizen without an ID number
def create_test_citizen(schema_name):
    print(f"\n=== Creating Test Citizen in Schema {schema_name} ===")

    Client = get_tenant_model()
    try:
        tenant = Client.objects.get(schema_name=schema_name)
        with tenant_context(tenant):
            # Check if we already have a test citizen
            test_citizens = Citizen.objects.filter(first_name="Test", last_name="Citizen")
            if test_citizens.exists():
                test_citizen = test_citizens.first()
                print(f"Found existing test citizen: ID {test_citizen.id}, Name: {test_citizen.first_name} {test_citizen.last_name}")

                # Clear the ID number if it exists
                if test_citizen.id_number:
                    test_citizen.id_number = None
                    test_citizen.save()
                    print(f"Cleared ID number for test citizen")

                return test_citizen.id

            # Create a new test citizen
            from datetime import date
            test_citizen = Citizen.objects.create(
                first_name="Test",
                last_name="Citizen",
                gender="MALE",
                date_of_birth=date(1990, 1, 1),
                place_of_birth="Test City",
                nationality="Ethiopian",
                occupation="Tester",
                phone_number="1234567890",
                email="<EMAIL>",
                marital_status="SINGLE",
                religion_id=1,
                region_id=1,
                zone="Test Zone",
                woreda="Test Woreda",
                kebele="Test Kebele",
                house_number="123",
                id_number=None  # Ensure no ID number
            )
            print(f"Created new test citizen: ID {test_citizen.id}, Name: {test_citizen.first_name} {test_citizen.last_name}")
            return test_citizen.id
    except Client.DoesNotExist:
        print(f"Error: Tenant with schema {schema_name} does not exist")
        return None
    except Exception as e:
        print(f"Error creating test citizen: {str(e)}")
        return None

# Function to test ID generation for a citizen
def test_id_generation(schema_name, citizen_id):
    print(f"\n=== Testing ID Generation for Citizen {citizen_id} in Schema {schema_name} ===")

    # First, check if the citizen exists in the database
    Client = get_tenant_model()
    try:
        tenant = Client.objects.get(schema_name=schema_name)
        with tenant_context(tenant):
            try:
                citizen = Citizen.objects.get(pk=citizen_id)
                print(f"Found citizen in database: {citizen.first_name} {citizen.last_name}")
                print(f"Current ID number: {citizen.id_number or 'None'}")
            except Citizen.DoesNotExist:
                print(f"Error: Citizen with ID {citizen_id} does not exist in schema {schema_name}")
                return
    except Client.DoesNotExist:
        print(f"Error: Tenant with schema {schema_name} does not exist")
        return

    # Now test the API endpoint
    url = f"{API_BASE_URL}/tenant/{schema_name}/citizens/{citizen_id}/generate-id/"
    headers = {
        'Authorization': f'Token {TOKEN}',
        'Content-Type': 'application/json'
    }

    print(f"Making POST request to: {url}")
    print(f"Headers: {headers}")

    try:
        response = requests.post(url, headers=headers)
        print(f"Response status code: {response.status_code}")

        if response.status_code == 200:
            data = response.json()
            print(f"Response data: {json.dumps(data, indent=2)}")

            # Verify the ID was actually saved in the database
            with tenant_context(tenant):
                updated_citizen = Citizen.objects.get(pk=citizen_id)
                print(f"Updated citizen ID in database: {updated_citizen.id_number or 'None'}")

                if updated_citizen.id_number == data.get('id_number'):
                    print("Success: ID number in response matches database!")
                else:
                    print("Warning: ID number in response does not match database!")
        else:
            print(f"Error response: {response.text}")
    except Exception as e:
        print(f"Exception occurred: {str(e)}")

# Function to list citizens in a schema
def list_citizens(schema_name):
    print(f"\n=== Listing Citizens in Schema {schema_name} ===")

    Client = get_tenant_model()
    try:
        tenant = Client.objects.get(schema_name=schema_name)
        with tenant_context(tenant):
            citizens = Citizen.objects.all()[:10]  # Get first 10 citizens

            if not citizens:
                print("No citizens found in this schema")
                return

            for citizen in citizens:
                print(f"ID: {citizen.id}, Name: {citizen.first_name} {citizen.last_name}, ID Number: {citizen.id_number or 'None'}")
    except Client.DoesNotExist:
        print(f"Error: Tenant with schema {schema_name} does not exist")
        return

# List available schemas
def list_schemas():
    print("\n=== Available Schemas ===")
    Client = get_tenant_model()
    schemas = Client.objects.exclude(schema_name='public')

    for schema in schemas:
        print(f"Schema: {schema.schema_name}, Name: {schema.name}, Type: {schema.schema_type}")

# Main execution
if __name__ == "__main__":
    # List available schemas
    list_schemas()

    # List citizens in the specified schema
    list_citizens(SCHEMA_NAME)

    # Create a test citizen without an ID number
    test_citizen_id = create_test_citizen(SCHEMA_NAME)

    if test_citizen_id:
        # Test ID generation for the test citizen
        test_id_generation(SCHEMA_NAME, test_citizen_id)
    else:
        # Ask for citizen ID to test
        citizen_id = input("\nEnter citizen ID to test ID generation (or press Enter to skip): ")

        if citizen_id:
            test_id_generation(SCHEMA_NAME, citizen_id)
