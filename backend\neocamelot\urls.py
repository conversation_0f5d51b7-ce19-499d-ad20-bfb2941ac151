"""
URL configuration for neocamelot project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/5.1/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.contrib import admin
from .admin_site import admin_site as neocamelot_admin
from django.urls import path, include, re_path
from django.conf import settings
from django.conf.urls.static import static
from django.views.static import serve
import os
from rest_framework.routers import DefaultRouter
from rest_framework import permissions
from drf_yasg.views import get_schema_view
from drf_yasg import openapi
from drf_yasg.generators import OpenAPISchemaGenerator
from .cors_views import cors_preflight
from .debug_views import debug_cors, health_check

# Apply the drf-yasg patch
from .swagger_patch import patch_drf_yasg
patch_drf_yasg()

# Swagger/OpenAPI schema configuration
schema_view = get_schema_view(
    openapi.Info(
        title="NeoCamelot API",
        default_version='v1',
        description="API documentation for the NeoCamelot ID Card System",
        terms_of_service="https://www.example.com/terms/",
        contact=openapi.Contact(email="<EMAIL>"),
        license=openapi.License(name="BSD License"),
    ),
    public=True,
    permission_classes=(permissions.AllowAny,),
    generator_class=OpenAPISchemaGenerator,
)

# Import views from apps
from centers.views import (CityViewSet, SubcityViewSet, CenterViewSet, CenterTypeViewSet,
                          register_tenant, get_available_tenants, get_regions, CitizenViewSet,
                          IDCardViewSet, IDCardTemplateViewSet, update_tenant_logo, update_tenant_colors)
from accounts.views import (
    UserViewSet, login_view, tenant_info_view, admin_login_view, admin_login_page,
    jwt_login_view, jwt_refresh_view, jwt_validate_view, jwt_exchange_token_view, swagger_token_auth,
    swagger_auth_page
)
from accounts.jwt_views import jwt_debug_view
from accounts.find_user_schema import find_user_schema
from idcards.statistics_view import idcard_statistics
from citizens.public_views import public_register_citizen

# Create a router and register our viewsets with it
router = DefaultRouter()
router.register(r'cities', CityViewSet)
router.register(r'subcities', SubcityViewSet)
router.register(r'centers', CenterViewSet)
router.register(r'center-types', CenterTypeViewSet)
router.register(r'users', UserViewSet)
router.register(r'citizens', CitizenViewSet)
router.register(r'idcards', IDCardViewSet)
router.register(r'idcard-templates', IDCardTemplateViewSet)

urlpatterns = [
    path('admin/', admin.site.urls),  # Standard Django admin
    path('admin-login/', admin_login_page, name='admin-login-page'),
    path('neocamelot-admin/', neocamelot_admin.urls),  # Custom admin with middleware
    path('api/', include(router.urls)),
    path('api-auth/', include('rest_framework.urls')),
    path('api/login/', login_view, name='login'),
    path('api/admin-login/', admin_login_view, name='admin-login'),
    path('api/register-tenant/', register_tenant, name='register-tenant'),
    path('api/available-tenants/', get_available_tenants, name='available-tenants'),
    path('api/regions/', get_regions, name='regions'),
    path('api/tenant-info/<str:schema_name>/', tenant_info_view, name='tenant-info'),
    path('api/update-tenant-logo/', update_tenant_logo, name='update-tenant-logo'),
    path('api/update-tenant-colors/', update_tenant_colors, name='update-tenant-colors'),
    # Legacy token endpoints removed - using JWT authentication only

    # JWT Authentication endpoints
    path('api/jwt/login/', jwt_login_view, name='jwt-login'),
    path('api/jwt/refresh-token/', jwt_refresh_view, name='jwt-refresh-token'),
    path('api/jwt/validate-token/', jwt_validate_view, name='jwt-validate-token'),
    path('api/jwt/exchange-token/', jwt_exchange_token_view, name='jwt-exchange-token'),
    path('api/jwt/debug/', jwt_debug_view, name='jwt-debug'),  # Debug endpoint for JWT authentication
    path('api/find-user-schema/', find_user_schema, name='find-user-schema'),
    path('api/debug-cors/', debug_cors, name='debug-cors'),  # Debug view for CORS
    path('api/health/', health_check, name='health-check'),  # Health check endpoint
    path('api/idcards/statistics/', idcard_statistics, name='idcard-statistics'),  # ID card statistics endpoint
    path('api/', include('centers.urls')),  # Include our simple API endpoints
    path('api/citizens/', include('citizens.urls')),  # Include citizens API endpoints
    path('api/common/', include('common.urls')),  # Include common data API endpoints

    # Tenant-specific API endpoints
    path('api/tenant/<str:schema_name>/citizens/public/register/', public_register_citizen, name='tenant-public-register-citizen'),

    # Add a catch-all OPTIONS handler for API endpoints
    path('api/<path:path>', cors_preflight, name='cors-preflight-api'),
]

# Serve media files in all environments
urlpatterns += [
    path('media/<path:path>', serve, {
        'document_root': os.path.join(settings.MEDIA_ROOT),
    }),
]

# Also add the standard Django way for development
if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)

# Add Swagger/OpenAPI documentation URLs
urlpatterns += [
    re_path(r'^swagger(?P<format>\.json|\.yaml)$', schema_view.without_ui(cache_timeout=0), name='schema-json'),
    path('swagger/', schema_view.with_ui('swagger', cache_timeout=0), name='schema-swagger-ui'),
    path('redoc/', schema_view.with_ui('redoc', cache_timeout=0), name='schema-redoc'),
    path('api/swagger-token-auth/', swagger_token_auth, name='swagger-token-auth'),
    path('swagger-auth/', swagger_auth_page, name='swagger-auth-page'),
]
