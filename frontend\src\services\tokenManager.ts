/**
 * Token Manager - Single source of truth for all token operations
 */
class TokenManager {
  private static instance: TokenManager;

  // Private constructor for singleton pattern
  private constructor() {}

  // Get singleton instance
  public static getInstance(): TokenManager {
    if (!TokenManager.instance) {
      TokenManager.instance = new TokenManager();
    }
    return TokenManager.instance;
  }

  /**
   * Store a refresh token in all required locations
   */
  public storeRefreshToken(token: string, schema: string): void {
    console.log(`TokenManager: Storing refresh token for schema ${schema}`);

    // Format schema for consistency
    const formattedSchema = schema.replace(/\s+/g, '_');

    // 1. Store in localStorage (both generic and schema-specific)
    localStorage.setItem('jwt_refresh_token', token);
    localStorage.setItem(`jwt_refresh_token_${formattedSchema}`, token);

    // 2. Store in cookies (both names for compatibility)
    const expirationDate = new Date();
    expirationDate.setDate(expirationDate.getDate() + 30); // 30 days expiration

    // Set cookies with proper attributes
    document.cookie = `jwt_refresh_token=${encodeURIComponent(token)}; path=/; expires=${expirationDate.toUTCString()}; SameSite=Lax`;
    document.cookie = `refresh_token=${encodeURIComponent(token)}; path=/; expires=${expirationDate.toUTCString()}; SameSite=Lax`;

    console.log('TokenManager: Refresh token stored in all locations');
  }

  /**
   * Store an access token in all required locations
   */
  public storeAccessToken(token: string, schema: string): void {
    console.log(`TokenManager: Storing access token for schema ${schema}`);

    // Format schema for consistency
    const formattedSchema = schema.replace(/\s+/g, '_');

    // Store in localStorage (both generic and schema-specific)
    localStorage.setItem('jwt_access_token', token);
    localStorage.setItem(`jwt_access_token_${formattedSchema}`, token);

    console.log('TokenManager: Access token stored');
  }

  /**
   * Get the best available refresh token
   */
  public getRefreshToken(schema?: string): string | null {
    // Get the schema to use
    const schemaToUse = schema || localStorage.getItem('schema_name') || localStorage.getItem('jwt_schema');
    if (!schemaToUse) {
      console.log('TokenManager: No schema provided or found');
      return null;
    }

    const formattedSchema = schemaToUse.replace(/\s+/g, '_');
    console.log(`TokenManager: Getting refresh token for schema ${formattedSchema}`);

    // Check all possible sources and log their status
    let refreshTokenSources: Record<string, string | null> = {
      schemaSpecific: localStorage.getItem(`jwt_refresh_token_${formattedSchema}`),
      generic: localStorage.getItem('jwt_refresh_token'),
      jwtCookie: null,
      refreshCookie: null
    };

    // Check cookies
    document.cookie.split(';').forEach(cookie => {
      const [name, value] = cookie.trim().split('=');
      if (name === 'jwt_refresh_token') {
        refreshTokenSources.jwtCookie = decodeURIComponent(value);
      }
      if (name === 'refresh_token') {
        refreshTokenSources.refreshCookie = decodeURIComponent(value);
      }
    });

    // Log all sources for debugging
    console.log('TokenManager: Refresh token sources:');
    Object.entries(refreshTokenSources).forEach(([source, token]) => {
      console.log(`- ${source}: ${token ? 'Present' : 'Missing'}`);
    });

    // Prioritize sources (cookies are more reliable for cross-tab synchronization)
    const token = refreshTokenSources.refreshCookie ||
                 refreshTokenSources.jwtCookie ||
                 refreshTokenSources.schemaSpecific ||
                 refreshTokenSources.generic;

    if (token) {
      console.log('TokenManager: Found valid refresh token');
      return token;
    }

    console.log('TokenManager: No refresh token found');
    return null;
  }

  /**
   * Get the best available access token
   */
  public getAccessToken(schema?: string): string | null {
    // Get the schema to use
    const schemaToUse = schema || localStorage.getItem('schema_name') || localStorage.getItem('jwt_schema');
    if (!schemaToUse) {
      console.log('TokenManager: No schema provided or found');
      return null;
    }

    const formattedSchema = schemaToUse.replace(/\s+/g, '_');
    console.log(`TokenManager: Getting access token for schema ${formattedSchema}`);

    // Check all possible sources
    const schemaSpecific = localStorage.getItem(`jwt_access_token_${formattedSchema}`);
    const generic = localStorage.getItem('jwt_access_token');

    // Log sources for debugging
    console.log('TokenManager: Access token sources:');
    console.log(`- schemaSpecific: ${schemaSpecific ? 'Present' : 'Missing'}`);
    console.log(`- generic: ${generic ? 'Present' : 'Missing'}`);

    // Return the first available token
    const token = schemaSpecific || generic;

    if (token) {
      console.log('TokenManager: Found valid access token');
      return token;
    }

    console.log('TokenManager: No access token found');
    return null;
  }

  /**
   * Clear all tokens (for logout or when tokens are invalid)
   */
  public clearAllTokens(): void {
    console.log('TokenManager: Clearing all tokens');

    // 1. Clear localStorage tokens
    const keysToRemove = [];
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && (key.includes('jwt_access_token') || key.includes('jwt_refresh_token'))) {
        keysToRemove.push(key);
      }
    }
    keysToRemove.forEach(key => {
      console.log(`TokenManager: Removing ${key} from localStorage`);
      localStorage.removeItem(key);
    });

    // 2. Clear cookies
    document.cookie = 'jwt_refresh_token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT; SameSite=Lax';
    document.cookie = 'refresh_token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT; SameSite=Lax';

    // 3. Also clear any legacy token references
    localStorage.removeItem('token');
    localStorage.removeItem('tokenStore');

    console.log('TokenManager: All tokens cleared');
  }

  /**
   * Handle invalid refresh token error
   */
  public handleInvalidRefreshToken(schema?: string): void {
    console.log('TokenManager: Handling invalid refresh token');

    // Clear all tokens
    this.clearAllTokens();

    // Set token expired flags for the UI to show appropriate messages
    localStorage.setItem('token_expired', 'true');
    localStorage.setItem('token_expired_reason', 'invalid_refresh_token');
    if (schema) {
      localStorage.setItem('token_expired_schema', schema);
    }
    localStorage.setItem('token_expired_timestamp', Date.now().toString());

    console.log('TokenManager: Invalid refresh token handled');
  }

  /**
   * Synchronize cookies with localStorage
   * This ensures cookies are present even if they were deleted
   */
  public synchronizeCookies(): void {
    console.log('TokenManager: Synchronizing cookies');

    // Check if cookies exist
    let hasJwtRefreshTokenCookie = false;
    let hasRefreshTokenCookie = false;
    let cookieRefreshToken = null;

    document.cookie.split(';').forEach(cookie => {
      const [name, value] = cookie.trim().split('=');
      if (name === 'jwt_refresh_token') {
        hasJwtRefreshTokenCookie = true;
        cookieRefreshToken = decodeURIComponent(value);
      }
      if (name === 'refresh_token') {
        hasRefreshTokenCookie = true;
        if (!cookieRefreshToken) {
          cookieRefreshToken = decodeURIComponent(value);
        }
      }
    });

    // If cookies are missing but we have a token in localStorage, restore them
    if (!hasJwtRefreshTokenCookie || !hasRefreshTokenCookie) {
      const schema = localStorage.getItem('schema_name') || localStorage.getItem('jwt_schema');
      if (schema) {
        const formattedSchema = schema.replace(/\s+/g, '_');
        const refreshToken = localStorage.getItem(`jwt_refresh_token_${formattedSchema}`) ||
                            localStorage.getItem('jwt_refresh_token');

        if (refreshToken) {
          console.log('TokenManager: Restoring missing cookies from localStorage');

          const expirationDate = new Date();
          expirationDate.setDate(expirationDate.getDate() + 30);

          if (!hasJwtRefreshTokenCookie) {
            document.cookie = `jwt_refresh_token=${encodeURIComponent(refreshToken)}; path=/; expires=${expirationDate.toUTCString()}; SameSite=Lax`;
          }

          if (!hasRefreshTokenCookie) {
            document.cookie = `refresh_token=${encodeURIComponent(refreshToken)}; path=/; expires=${expirationDate.toUTCString()}; SameSite=Lax`;
          }
        }
      }
    }

    // If localStorage is missing tokens but we have cookies, restore them
    if (cookieRefreshToken) {
      const schema = localStorage.getItem('schema_name') || localStorage.getItem('jwt_schema');
      if (schema) {
        const formattedSchema = schema.replace(/\s+/g, '_');
        const localStorageToken = localStorage.getItem(`jwt_refresh_token_${formattedSchema}`) ||
                                 localStorage.getItem('jwt_refresh_token');

        if (!localStorageToken) {
          console.log('TokenManager: Restoring missing localStorage tokens from cookies');
          localStorage.setItem('jwt_refresh_token', cookieRefreshToken);

          if (schema) {
            localStorage.setItem(`jwt_refresh_token_${formattedSchema}`, cookieRefreshToken);
          }
        }
      }
    }

    console.log('TokenManager: Cookies synchronized');
  }
}

// Export singleton instance
export const tokenManager = TokenManager.getInstance();
