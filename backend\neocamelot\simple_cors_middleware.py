class SimpleCorsMiddleware:
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        response = self.get_response(request)

        # Add CORS headers to all responses
        # Get the Origin header from the request
        origin = request.headers.get('Origin', '')
        # Allow requests from any origin
        response["Access-Control-Allow-Origin"] = origin or '*'
        response["Access-Control-Allow-Methods"] = "GET, POST, PUT, PATCH, DELETE, OPTIONS"
        response["Access-Control-Allow-Headers"] = "Content-Type, Authorization, X-Requested-With, X-Schema-Name, X-Auth-Token, X-CSRFToken, x-csrftoken"
        response["Access-Control-Allow-Credentials"] = "true"

        return response
