import React, { useState, useEffect } from 'react';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import { useLocation } from 'react-router-dom';
import baseTheme from '../../theme';

interface DynamicThemeProviderProps {
  children: React.ReactNode;
}

const DynamicThemeProvider: React.FC<DynamicThemeProviderProps> = ({ children }) => {
  const [theme, setTheme] = useState(baseTheme);
  const location = useLocation();

  useEffect(() => {
    console.log('DynamicThemeProvider: Applying theme');

    // Only apply the theme once per path to prevent infinite loops
    const currentPath = location.pathname;
    const lastPath = localStorage.getItem('last_theme_path');

    if (currentPath === lastPath) {
      console.log('DynamicThemeProvider: Theme already applied for this path');
      return;
    }

    // Store the current path
    localStorage.setItem('last_theme_path', currentPath);

    // Get tenant information from localStorage
    const tenantStr = localStorage.getItem('tenant');
    if (tenantStr) {
      try {
        const tenant = JSON.parse(tenantStr);

        // Check if tenant has custom colors
        if (tenant.header_color || tenant.accent_color) {
          // Create a new theme with the tenant's colors
          const customTheme = createTheme({
            ...baseTheme,
            palette: {
              ...baseTheme.palette,
              primary: {
                ...baseTheme.palette.primary,
                main: tenant.accent_color || baseTheme.palette.primary.main,
              },
              // You can also customize other colors based on the tenant's preferences
            },
            components: {
              ...baseTheme.components,
              MuiAppBar: {
                styleOverrides: {
                  root: {
                    backgroundColor: tenant.header_color || baseTheme.palette.primary.main,
                    color: '#fff',
                  },
                },
              },
              // Add other component customizations as needed
            },
          });

          setTheme(customTheme);

          // Apply header color to CSS variables for use in other components
          document.documentElement.style.setProperty('--header-color', tenant.header_color || baseTheme.palette.primary.main);
          document.documentElement.style.setProperty('--accent-color', tenant.accent_color || baseTheme.palette.primary.main);
        }
      } catch (error) {
        console.error('Error parsing tenant data:', error);
      }
    }
  }, [location.pathname]); // Re-apply theme when route changes

  return <ThemeProvider theme={theme}>{children}</ThemeProvider>;
};

export default DynamicThemeProvider;
