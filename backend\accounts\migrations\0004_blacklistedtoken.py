# Generated by Django 4.2.7 on 2025-05-02 20:12

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0003_alter_user_role'),
    ]

    operations = [
        migrations.CreateModel(
            name='BlacklistedToken',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('token', models.TextField(unique=True)),
                ('blacklisted_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('expires_at', models.DateTimeField()),
                ('token_type', models.CharField(default='refresh', max_length=10)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='blacklisted_tokens', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Blacklisted Token',
                'verbose_name_plural': 'Blacklisted Tokens',
                'indexes': [models.Index(fields=['token'], name='accounts_bl_token_54efeb_idx'), models.Index(fields=['user'], name='accounts_bl_user_id_05733f_idx'), models.Index(fields=['expires_at'], name='accounts_bl_expires_54dbac_idx')],
            },
        ),
    ]
