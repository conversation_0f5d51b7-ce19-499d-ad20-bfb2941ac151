from rest_framework import viewsets, permissions, filters, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from django.http import HttpResponse
from .models import IDCard, IDCardTemplate
from .serializers import IDCard<PERSON>erializer, IDCardListSerializer, IDCardTemplateSerializer, IDCardTemplateListSerializer
from accounts.role_permissions import (
    IsCenterStaff, IsKebeleLeader, IsSubcityAdmin,
    CanManageIDCards
)
from django.utils import timezone
from django.db import connection

class IDCardTemplateViewSet(viewsets.ModelViewSet):
    """ViewSet for viewing and editing IDCardTemplate instances."""
    queryset = IDCardTemplate.tenant_objects.all()
    serializer_class = IDCardTemplateSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['center', 'is_default']
    search_fields = ['name']
    ordering_fields = ['name', 'created_at']
    authentication_classes = []  # No authentication classes - we'll handle it in the permission classes

    def get_permissions(self):
        """Set custom permissions for different actions based on user roles."""
        if self.action in ['create', 'update', 'partial_update', 'destroy']:
            # Only CENTER_STAFF can create, update, or delete templates
            return [IsCenterStaff()]
        # Anyone authenticated can view templates
        return [permissions.IsAuthenticated()]

    def get_serializer_class(self):
        """Return appropriate serializer class based on action."""
        if self.action == 'list':
            return IDCardTemplateListSerializer
        return IDCardTemplateSerializer

    def get_queryset(self):
        """Filter templates based on user role and center."""
        user = self.request.user
        if user.is_super_admin:
            # Super admins can see all templates
            return IDCardTemplate.objects.all()
        # For other users, the tenant_objects manager will automatically filter by center
        return IDCardTemplate.tenant_objects.all()

    def perform_create(self, serializer):
        """Set center if not provided."""
        user = self.request.user
        data = serializer.validated_data

        # If no center is specified, use user's center
        if not data.get('center') and user.center:
            data['center'] = user.center

        serializer.save()

class IDCardViewSet(viewsets.ModelViewSet):
    """ViewSet for viewing and editing IDCard instances."""
    queryset = IDCard.tenant_objects.all()
    serializer_class = IDCardSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['status', 'citizen__center']
    search_fields = ['card_number', 'citizen__first_name', 'citizen__last_name', 'citizen__registration_number']
    ordering_fields = ['issue_date', 'expiry_date', 'created_at']
    # For testing, allow any user to access this endpoint
    authentication_classes = []

    def get_permissions(self):
        """Set custom permissions for different actions based on user roles."""
        if self.action in ['list', 'retrieve']:
            # Allow anyone to view ID cards, even without authentication
            return [permissions.AllowAny()]
        elif self.action in ['create', 'generate_id']:
            # Only CENTER_STAFF can create ID cards and generate IDs
            return [IsCenterStaff()]
        elif self.action in ['update', 'partial_update', 'destroy']:
            # Only CENTER_STAFF can update or delete ID cards
            return [IsCenterStaff()]
        elif self.action in ['verify_documents', 'reject_documents', 'pending_verification',
                            'apply_kebele_pattern', 'kebele_approve', 'kebele_reject', 'send_to_subcity']:
            # Only KEBELE_LEADER can verify documents, approve/reject ID cards, and apply kebele patterns
            return [IsKebeleLeader()]
        elif self.action in ['print', 'apply_subcity_pattern', 'subcity_approve', 'pending_subcity', 'child_kebele_cards']:
            # Only SUBCITY_ADMIN can print ID cards, apply subcity patterns, and approve ID cards sent from kebeles
            return [IsSubcityAdmin()]
        # Default to authenticated users for other actions
        return [permissions.IsAuthenticated()]

    def get_serializer_class(self):
        """Return appropriate serializer class based on action."""
        if self.action == 'list':
            return IDCardListSerializer
        return IDCardSerializer

    def get_queryset(self):
        """Filter ID cards based on user role and center."""
        user = self.request.user

        # Print detailed authentication information
        print("\n=== IDCardViewSet.get_queryset ===")
        print(f"User: {user.email if hasattr(user, 'email') else 'No email'}, ID: {user.id if hasattr(user, 'id') else 'No ID'}")
        print(f"Current schema: {connection.schema_name}")
        print(f"Request tenant: {getattr(self.request, 'tenant', None)}")
        print(f"X-Schema-Name header: {self.request.headers.get('X-Schema-Name')}")

        if user.is_super_admin:
            # Super admins can see all ID cards
            print("User is super admin, returning all ID cards")
            return IDCard.objects.all()

        # For other users, the tenant_objects manager will automatically filter by center
        queryset = IDCard.tenant_objects.all()
        print(f"Tenant objects queryset count: {queryset.count()}")

        # If this is a kebele leader looking for cards pending verification
        if self.action == 'pending_verification' and user.is_kebele_leader:
            print("User is kebele leader looking for cards pending verification")
            return queryset.filter(status='PENDING', document_verification_status='NOT_VERIFIED')

        return queryset

    def perform_create(self, serializer):
        """Set created_by to current user."""
        serializer.save(created_by=self.request.user)

    @action(detail=True, methods=['post'], permission_classes=[IsCenterStaff])
    def approve(self, request, pk=None):
        """Approve an ID card."""
        id_card = self.get_object()
        if id_card.status != 'PENDING':
            return Response({'error': 'Only pending ID cards can be approved'}, status=status.HTTP_400_BAD_REQUEST)

        # Document verification is optional, but if documents were uploaded and rejected, prevent approval
        if id_card.document_verification_status == 'REJECTED':
            return Response({'error': 'Cannot approve ID card with rejected documents'}, status=status.HTTP_400_BAD_REQUEST)

        # Check if kebele leader has approved
        if id_card.kebele_approval_status != 'APPROVED':
            return Response({'error': 'ID card must be approved by kebele leader before center admin approval'}, status=status.HTTP_400_BAD_REQUEST)

        # Check if kebele pattern has been applied
        if not id_card.kebele_pattern:
            return Response({'error': 'Kebele security pattern must be applied before approval'}, status=status.HTTP_400_BAD_REQUEST)

        id_card.status = 'APPROVED'
        id_card.approved_by = request.user
        id_card.save()

        serializer = self.get_serializer(id_card)
        return Response(serializer.data)

    @action(detail=True, methods=['post'], permission_classes=[IsCenterStaff])
    def revoke(self, request, pk=None):
        """Revoke an ID card."""
        id_card = self.get_object()
        if id_card.status == 'REVOKED':
            return Response({'error': 'ID card is already revoked'}, status=status.HTTP_400_BAD_REQUEST)

        id_card.status = 'REVOKED'
        id_card.save()

        serializer = self.get_serializer(id_card)
        return Response(serializer.data)

    @action(detail=True, methods=['get'])
    def generate_pdf(self, request, pk=None):
        """Generate a PDF for the ID card."""
        id_card = self.get_object()

        # Check if the card has both security patterns applied
        if not id_card.kebele_pattern or not id_card.subcity_pattern:
            return Response(
                {'error': 'Both kebele and subcity security patterns must be applied before printing'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # This is a placeholder for actual PDF generation logic
        # In a real implementation, you would use a library like ReportLab or WeasyPrint
        response = HttpResponse(content_type='application/pdf')
        response['Content-Disposition'] = f'attachment; filename="id_card_{id_card.card_number}.pdf"'

        # Placeholder text for demonstration
        response.write(b'%PDF-1.4\n%\xe2\xe3\xcf\xd3\n')
        response.write(b'This is a placeholder for a real PDF ID card.')

        return response

    @action(detail=True, methods=['post'], permission_classes=[IsSubcityAdmin])
    def print(self, request, pk=None):
        """Mark an ID card as printed."""
        id_card = self.get_object()

        # Check if the card is in the correct status
        if id_card.status != 'APPROVED':
            return Response(
                {'error': 'Only approved ID cards can be printed'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Check if both security patterns have been applied
        if not id_card.kebele_pattern or not id_card.subcity_pattern:
            return Response(
                {'error': 'Both kebele and subcity security patterns must be applied before printing'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Update the status
        id_card.status = 'PRINTED'
        id_card.save()

        serializer = self.get_serializer(id_card)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def statistics(self, request):
        """Return statistics for ID cards based on user's center."""
        # Print detailed authentication information
        print("\n=== ID Card Statistics Authentication ===")
        print(f"User: {request.user}")
        print(f"Is authenticated: {request.user.is_authenticated}")
        print(f"Auth header: {request.headers.get('Authorization', 'No auth header')}")
        print(f"Schema name header: {request.headers.get('X-Schema-Name', 'No schema header')}")
        print(f"Current schema: {connection.schema_name}")
        print("=" * 50)

        queryset = self.get_queryset()

        stats = {
            'total': queryset.count(),
            'draft': queryset.filter(status='DRAFT').count(),
            'pending': queryset.filter(status='PENDING').count(),
            'approved': queryset.filter(status='APPROVED').count(),
            'printed': queryset.filter(status='PRINTED').count(),
            'issued': queryset.filter(status='ISSUED').count(),
            'expired': queryset.filter(status='EXPIRED').count(),
            'revoked': queryset.filter(status='REVOKED').count(),
            'pending_verification': queryset.filter(status='PENDING', document_verification_status='NOT_VERIFIED').count(),
            'verified': queryset.filter(document_verification_status='VERIFIED').count(),
            'rejected': queryset.filter(document_verification_status='REJECTED').count(),
            'kebele_pending': queryset.filter(kebele_approval_status='PENDING').count(),
            'kebele_approved': queryset.filter(kebele_approval_status='APPROVED').count(),
            'kebele_rejected': queryset.filter(kebele_approval_status='REJECTED').count(),
        }

        return Response(stats)

    @action(detail=False, methods=['get'], permission_classes=[IsKebeleLeader])
    def pending_verification(self, request):
        """Return ID cards pending document verification."""
        queryset = self.get_queryset().filter(status='PENDING', document_verification_status='NOT_VERIFIED')
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['post'], permission_classes=[IsKebeleLeader])
    def verify_documents(self, request, pk=None):
        """Verify documents for an ID card."""
        id_card = self.get_object()

        # Check if the card is in the correct status
        if id_card.status != 'PENDING':
            return Response({'error': 'Only pending ID cards can have documents verified'}, status=status.HTTP_400_BAD_REQUEST)

        # Get verification notes from request data
        notes = request.data.get('notes', '')

        # Get whether to apply pattern automatically
        apply_pattern = request.data.get('apply_pattern', True)

        # Update the verification status
        id_card.document_verification_status = 'VERIFIED'
        id_card.document_verification_notes = notes
        id_card.document_verified_at = timezone.now()
        id_card.document_verified_by = request.user
        id_card.save()

        # Apply the kebele pattern if requested
        if apply_pattern and not id_card.kebele_pattern:
            from .security_patterns import apply_kebele_pattern
            apply_kebele_pattern(id_card)
            id_card.refresh_from_db()

        serializer = self.get_serializer(id_card)
        return Response(serializer.data)

    @action(detail=True, methods=['post'], permission_classes=[IsKebeleLeader])
    def reject_documents(self, request, pk=None):
        """Reject documents for an ID card."""
        id_card = self.get_object()

        # Check if the card is in the correct status
        if id_card.status != 'PENDING':
            return Response({'error': 'Only pending ID cards can have documents rejected'}, status=status.HTTP_400_BAD_REQUEST)

        # Get rejection reason from request data
        reason = request.data.get('reason', '')
        if not reason:
            return Response({'error': 'Rejection reason is required'}, status=status.HTTP_400_BAD_REQUEST)

        # Update the verification status
        id_card.document_verification_status = 'REJECTED'
        id_card.document_verification_notes = reason
        id_card.document_verified_at = timezone.now()
        id_card.document_verified_by = request.user
        id_card.save()

        serializer = self.get_serializer(id_card)
        return Response(serializer.data)

    @action(detail=True, methods=['post'], permission_classes=[IsKebeleLeader])
    def apply_kebele_pattern(self, request, pk=None):
        """Apply the kebele half of the security pattern to an ID card."""
        id_card = self.get_object()

        # Only check document verification if documents were explicitly rejected
        if id_card.document_verification_status == 'REJECTED':
            return Response(
                {'error': 'Cannot apply kebele pattern to ID card with rejected documents'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Check if the kebele pattern has already been applied
        if id_card.kebele_pattern:
            return Response(
                {'error': 'Kebele pattern has already been applied to this ID card'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Apply the kebele pattern
        from .security_patterns import apply_kebele_pattern
        apply_kebele_pattern(id_card)

        # Refresh the ID card from the database
        id_card.refresh_from_db()

        serializer = self.get_serializer(id_card)
        return Response(serializer.data)

    @action(detail=True, methods=['post'], permission_classes=[IsSubcityAdmin])
    def apply_subcity_pattern(self, request, pk=None):
        """Apply the subcity half of the security pattern to an ID card."""
        id_card = self.get_object()

        # Check if the card is in the correct status
        if id_card.status != 'APPROVED':
            return Response(
                {'error': 'ID card must be approved before applying the subcity pattern'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Check if the kebele pattern has been applied
        if not id_card.kebele_pattern:
            return Response(
                {'error': 'Kebele pattern must be applied before the subcity pattern'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Check if the subcity pattern has already been applied
        if id_card.subcity_pattern:
            return Response(
                {'error': 'Subcity pattern has already been applied to this ID card'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Apply the subcity pattern
        from .security_patterns import apply_subcity_pattern
        apply_subcity_pattern(id_card)

        # Refresh the ID card from the database
        id_card.refresh_from_db()

        serializer = self.get_serializer(id_card)
        return Response(serializer.data)

    @action(detail=True, methods=['post'], permission_classes=[IsKebeleLeader])
    def kebele_approve(self, request, pk=None):
        """Approve an ID card by the kebele leader."""
        id_card = self.get_object()

        # Only check document verification if documents were explicitly rejected
        if id_card.document_verification_status == 'REJECTED':
            return Response(
                {'error': 'Cannot approve ID card with rejected documents'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # No longer require kebele pattern before approval
        # Pattern application is now optional

        # Get approval notes from request data
        notes = request.data.get('notes', '')

        # Update the approval status
        id_card.kebele_approval_status = 'APPROVED'
        id_card.kebele_approval_notes = notes
        id_card.kebele_approved_at = timezone.now()
        id_card.kebele_approved_by = request.user

        # Update the ID card status to PENDING for center admin approval
        id_card.status = 'PENDING'

        id_card.save()

        serializer = self.get_serializer(id_card)
        return Response(serializer.data)

    @action(detail=True, methods=['post'], permission_classes=[IsKebeleLeader])
    def kebele_reject(self, request, pk=None):
        """Reject an ID card by the kebele leader."""
        id_card = self.get_object()

        # Get rejection reason from request data
        reason = request.data.get('reason', '')
        if not reason:
            return Response({'error': 'Rejection reason is required'}, status=status.HTTP_400_BAD_REQUEST)

        # Update the approval status
        id_card.kebele_approval_status = 'REJECTED'
        id_card.kebele_approval_notes = reason
        id_card.kebele_approved_at = timezone.now()
        id_card.kebele_approved_by = request.user
        id_card.save()

        serializer = self.get_serializer(id_card)
        return Response(serializer.data)

    @action(detail=True, methods=['post'], permission_classes=[IsKebeleLeader])
    def send_to_subcity(self, request, pk=None):
        """Send an approved ID card to the parent subcity for further processing."""
        id_card = self.get_object()

        # Check if the ID card is in the correct status
        if id_card.kebele_approval_status != 'APPROVED':
            return Response(
                {'error': 'ID card must be approved by kebele leader before sending to subcity'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Check if kebele pattern has been applied
        if not id_card.kebele_pattern:
            return Response(
                {'error': 'Kebele security pattern must be applied before sending to subcity'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Update the ID card status to indicate it's ready for subcity processing
        id_card.status = 'PENDING_SUBCITY'
        id_card.save()

        # Get any notes from the request
        notes = request.data.get('notes', '')

        # TODO: In a real implementation, we would notify the subcity here
        # This could be done via email, push notification, or by creating a task in a queue

        serializer = self.get_serializer(id_card)
        return Response(serializer.data)

    @action(detail=True, methods=['post'], permission_classes=[IsSubcityAdmin])
    def subcity_approve(self, request, pk=None):
        """Approve an ID card by the subcity admin that was sent from a kebele."""
        id_card = self.get_object()

        # Check if the ID card is in the correct status
        if id_card.status != 'PENDING_SUBCITY':
            return Response(
                {'error': 'Only ID cards with PENDING_SUBCITY status can be approved by subcity admin'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Check if kebele leader has approved
        if id_card.kebele_approval_status != 'APPROVED':
            return Response(
                {'error': 'ID card must be approved by kebele leader before subcity approval'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Check if kebele pattern has been applied
        if not id_card.kebele_pattern:
            return Response(
                {'error': 'Kebele security pattern must be applied before subcity approval'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Get approval notes from request data
        notes = request.data.get('notes', '')

        # Apply the subcity pattern automatically if it hasn't been applied yet
        if not id_card.subcity_pattern:
            try:
                # Import the pattern application function
                from .security_patterns import apply_subcity_pattern

                # Apply the pattern
                apply_subcity_pattern(id_card)

                # Refresh the ID card to get the updated pattern
                id_card.refresh_from_db()
            except Exception as e:
                # Continue with approval even if pattern application fails
                pass

        # Update the ID card status to APPROVED
        id_card.status = 'APPROVED'
        id_card.approved_by = request.user
        id_card.save()

        serializer = self.get_serializer(id_card)
        return Response(serializer.data)

    @action(detail=False, methods=['get'], permission_classes=[IsSubcityAdmin])
    def pending_subcity(self, request):
        """Return ID cards pending subcity approval."""
        queryset = self.get_queryset().filter(status='PENDING_SUBCITY')
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'], url_path='child-kebele-cards', permission_classes=[IsSubcityAdmin])
    def child_kebele_cards(self, request):
        """Return ID cards from child kebeles that are pending subcity approval."""
        # Get the current tenant schema name
        current_schema = connection.schema_name
        print(f"Current schema: {current_schema}")

        # Check if this is a subcity schema
        if not current_schema.startswith('subcity_'):
            return Response(
                {'error': 'This endpoint is only available for subcity tenants'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Get the child kebele schemas
        # In a real implementation, you would query the tenant hierarchy
        # For now, we'll use hardcoded values
        child_kebeles = ['kebele 14', 'kebele 15', 'kebele 16']

        # Initialize an empty list to store ID cards
        all_cards = []

        # Fetch ID cards from each child kebele
        for kebele_schema in child_kebeles:
            try:
                print(f"Fetching ID cards from {kebele_schema}")

                # Switch to the kebele schema
                with connection.cursor() as cursor:
                    # Query ID cards with kebele_approval_status = 'APPROVED'
                    cursor.execute(f"""
                        SELECT
                            id.id,
                            id.card_number,
                            id.issue_date,
                            id.expiry_date,
                            id.status,
                            id.kebele_approval_status,
                            id.kebele_pattern,
                            c.first_name,
                            c.last_name,
                            c.id_number
                        FROM
                            "{kebele_schema}".idcards_idcard id
                        JOIN
                            "{kebele_schema}".citizens_citizen c ON id.citizen_id = c.id
                        WHERE
                            id.kebele_approval_status = 'APPROVED'
                    """)

                    # Fetch all rows
                    rows = cursor.fetchall()
                    print(f"Found {len(rows)} ID cards in {kebele_schema}")

                    # Convert rows to dictionaries
                    for row in rows:
                        card = {
                            'id': row[0],
                            'card_number': row[1],
                            'issue_date': row[2],
                            'expiry_date': row[3],
                            'status': row[4],
                            'kebele_approval_status': row[5],
                            'kebele_pattern': row[6],
                            'citizen': {
                                'first_name': row[7],
                                'last_name': row[8],
                                'id_number': row[9]
                            },
                            'source_schema': kebele_schema
                        }
                        all_cards.append(card)
            except Exception as e:
                print(f"Error fetching ID cards from {kebele_schema}: {str(e)}")

        return Response(all_cards)
