import os
import django

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'neocamelot.settings')
django.setup()

# Import models
from centers.models import Client
from django.contrib.auth import get_user_model
from django_tenants.utils import tenant_context, schema_context

User = get_user_model()

def fix_admin_users():
    """Fix admin users by deleting all non-superuser users from the public schema."""
    # Delete all non-superuser users from public schema
    with schema_context('public'):
        try:
            # Keep track of superusers
            superusers = User.objects.filter(is_superuser=True)
            superuser_emails = [user.email for user in superusers]
            print(f"Found {superusers.count()} superuser(s) in public schema: {', '.join(superuser_emails)}")

            # Delete all non-superuser users
            users_to_delete = User.objects.filter(is_superuser=False)
            count = users_to_delete.count()
            if count > 0:
                users_to_delete.delete()
                print(f"Deleted {count} non-superuser user(s) from public schema")
            else:
                print("No non-superuser users found in public schema")
        except Exception as e:
            print(f"Error deleting users from public schema: {str(e)}")

if __name__ == "__main__":
    fix_admin_users()
