import { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'
import './index.css'
import App from './App.tsx'
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider'
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns'
import { TenantProvider } from './contexts/TenantContext'
import { AuthProvider } from './contexts/AuthContext'
import { initAuthInterceptor } from './utils/authInterceptor'
import { patchFetch } from './utils/apiUrlFixer'

// Import the API base URL
import { API_BASE_URL } from './config/apiConfig';

// Log the backend URL
console.log('Using backend API URL:', API_BASE_URL);

// Patch the fetch API to fix URL issues
patchFetch();
console.log('Patched fetch API to fix URL issues');

// Initialize the auth interceptor and token synchronization
// We use an IIFE to handle the async function
(async () => {
  try {
    // First, synchronize cookies with localStorage
    try {
      const { tokenManager } = await import('./services/tokenManager');
      console.log('Main: Synchronizing cookies during initialization');
      tokenManager.synchronizeCookies();
    } catch (syncError) {
      console.error('Failed to synchronize cookies:', syncError);
    }

    // Then initialize auth interceptor
    await initAuthInterceptor();
    console.log('Auth interceptor initialized successfully');
  } catch (error) {
    console.error('Failed to initialize auth interceptor:', error);
  }
})();

createRoot(document.getElementById('root')!).render(
  <StrictMode>
    <LocalizationProvider dateAdapter={AdapterDateFns}>
      <TenantProvider>
        <AuthProvider>
          <App />
        </AuthProvider>
      </TenantProvider>
    </LocalizationProvider>
  </StrictMode>,
)
