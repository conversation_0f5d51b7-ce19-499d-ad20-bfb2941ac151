from django.db import migrations
import uuid

def generate_center_codes(apps, schema_editor):
    """Generate unique codes for existing centers."""
    Center = apps.get_model('centers', 'Center')
    for center in Center.objects.all():
        # Create a short unique code based on the first 3 letters of the name + random string
        name_prefix = ''.join(c for c in center.name[:3] if c.isalnum()).upper()
        if not name_prefix:
            name_prefix = 'CTR'
        random_suffix = str(uuid.uuid4())[:4].upper()
        center.code = f"{name_prefix}-{random_suffix}"
        center.save()

class Migration(migrations.Migration):

    dependencies = [
        ('centers', '0001_initial'),
    ]

    operations = [
        migrations.RunPython(generate_center_codes),
    ]
