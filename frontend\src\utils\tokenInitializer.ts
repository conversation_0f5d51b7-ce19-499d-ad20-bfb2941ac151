/**
 * Token Initializer Utility
 *
 * This utility ensures that tokens are properly initialized when the application starts.
 * It checks for tokens in various storage locations and ensures they are properly stored
 * in the token store for the current schema.
 */

import { getAccessTokenForSchema, storeTokensForSchema, getCurrentSchema } from '../services/tokenService';

/**
 * Initialize tokens for the current schema
 * @param schema The schema to initialize tokens for
 * @returns True if a valid token was found and initialized, false otherwise
 */
export const initializeTokensForSchema = (schema: string): boolean => {
  if (!schema) {
    console.error('Cannot initialize tokens: schema is missing');
    return false;
  }

  console.log(`Initializing tokens for schema: ${schema}`);

  // First check if we already have a JWT token for this schema
  const existingJWTToken = getAccessTokenForSchema(schema);
  if (existingJWTToken) {
    console.log(`Found existing JWT token for schema ${schema}`);
    return true;
  }

  // Legacy token support has been removed
  // All tokens are now managed by the unified token service

  // Check localStorage for JWT tokens
  const jwtAccessToken = localStorage.getItem('jwt_access_token');
  const jwtRefreshToken = localStorage.getItem('jwt_refresh_token');
  if (jwtAccessToken && jwtRefreshToken) {
    console.log(`Using JWT tokens from localStorage for schema ${schema}`);
    storeTokensForSchema(schema, jwtAccessToken, jwtRefreshToken);
    return true;
  }

  // Check localStorage for a general token
  const generalToken = localStorage.getItem('token');
  if (generalToken) {
    console.log(`Using general token from localStorage for schema ${schema}`);
    storeTokensForSchema(schema, generalToken, '');
    return true;
  }

  // Check cookies for a JWT token
  const jwtTokenCookie = document.cookie
    .split('; ')
    .find(row => row.startsWith('jwt_access_token='))
    ?.split('=')[1];

  if (jwtTokenCookie) {
    try {
      const decodedToken = decodeURIComponent(jwtTokenCookie);
      console.log(`Using JWT token from cookie for schema ${schema}`);

      // Check for refresh token cookie
      const jwtRefreshTokenCookie = document.cookie
        .split('; ')
        .find(row => row.startsWith('jwt_refresh_token='))
        ?.split('=')[1];

      if (jwtRefreshTokenCookie) {
        const decodedRefreshToken = decodeURIComponent(jwtRefreshTokenCookie);
        storeTokensForSchema(schema, decodedToken, decodedRefreshToken);
        return true;
      }
    } catch (error) {
      console.error('Error decoding JWT token cookie:', error);
    }
  }

  // Legacy token cookie support has been removed
  // All tokens are now managed by the unified token service

  console.log(`No token found for schema ${schema}`);
  return false;
};

/**
 * Initialize tokens for the application
 * This should be called when the application starts
 */
export const initializeTokens = (): void => {
  console.log('Initializing tokens for the application');

  // Get the current schema from token service
  let schema = getCurrentSchema();

  if (!schema) {
    // Try to get schema from cookies
    const schemaCookie = document.cookie
      .split('; ')
      .find(row => row.startsWith('schema_name='))
      ?.split('=')[1];

    if (schemaCookie) {
      try {
        schema = decodeURIComponent(schemaCookie);
        console.log('Using schema name from cookie:', schema);
      } catch (error) {
        console.error('Error decoding schema cookie:', error);
      }
    }
  }

  if (!schema) {
    // Try to get schema from tenant object
    const tenantStr = localStorage.getItem('tenant');
    if (tenantStr) {
      try {
        const tenant = JSON.parse(tenantStr);
        if (tenant && tenant.schema_name) {
          schema = tenant.schema_name;
          console.log('Using schema name from tenant object:', schema);
        }
      } catch (error) {
        console.error('Error parsing tenant from localStorage:', error);
      }
    }
  }

  if (schema) {
    // Initialize tokens for the current schema
    const initialized = initializeTokensForSchema(schema);

    if (!initialized) {
      console.warn(`Failed to initialize tokens for schema ${schema}`);

      // Check if we're already on the login page
      if (!window.location.pathname.includes('/login')) {
        // Redirect to login with no_token error
        window.location.href = `/login?error=no_token&returnUrl=${encodeURIComponent(window.location.pathname)}`;
      }
    }
  } else {
    console.warn('No schema found for token initialization');

    // Check if we're already on the login page
    if (!window.location.pathname.includes('/login')) {
      // Redirect to login with no_schema error
      window.location.href = `/login?error=no_schema&returnUrl=${encodeURIComponent(window.location.pathname)}`;
    }
  }
};

export default {
  initializeTokens,
  initializeTokensForSchema
};
