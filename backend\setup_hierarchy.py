import os
import django
import random

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'neocamelot.settings')
django.setup()

from django.contrib.auth import get_user_model
from centers.models import City, Subcity, Center, CenterType

User = get_user_model()

def setup_hierarchy():
    """Set up the initial hierarchy data for the system."""
    print("Setting up hierarchy data...")

    # Create a super admin user if it doesn't exist
    if not User.objects.filter(email='<EMAIL>').exists():
        super_admin = User.objects.create_user(
            email='<EMAIL>',
            password='admin123',
            first_name='Super',
            last_name='Admin',
            is_super_admin=True,
            is_staff=True,
            is_active=True
        )
        print("Created super admin user")
    else:
        super_admin = User.objects.get(email='<EMAIL>')
        print("Super admin user already exists")

    # Create a city
    city, created = City.objects.get_or_create(
        name='Addis Ababa',
        defaults={
            'description': 'Capital city of Ethiopia',
            'address': 'Addis Ababa, Ethiopia',
            'phone': '+251-111-111-111',
            'email': '<EMAIL>',
            'website': 'https://www.addisababa.gov.et',
            'admin_name': 'City Administrator',
            'admin_email': '<EMAIL>',
            'admin_phone': '+251-111-111-112',
            'created_by': super_admin
        }
    )
    if created:
        print(f"Created city: {city.name}")
    else:
        print(f"City {city.name} already exists")

    # Create subcities
    subcity_names = ['Arada', 'Kirkos', 'Yeka', 'Bole', 'Lideta', 'Kolfe Keranio']
    subcities = []

    for name in subcity_names:
        subcity, created = Subcity.objects.get_or_create(
            name=name,
            city=city,
            defaults={
                'description': f'{name} Subcity in Addis Ababa',
                'address': f'{name}, Addis Ababa, Ethiopia',
                'phone': f'+251-111-{random.randint(100000, 999999)}',
                'email': f'info@{name.lower().replace(" ", "")}.addisababa.gov.et',
                'website': f'https://www.{name.lower().replace(" ", "")}.addisababa.gov.et',
                'admin_name': f'{name} Administrator',
                'admin_email': f'admin@{name.lower().replace(" ", "")}.addisababa.gov.et',
                'admin_phone': f'+251-111-{random.randint(100000, 999999)}',
                'has_printing_facility': True,
                'printing_capacity': random.choice([50, 100, 150, 200]),
                'created_by': super_admin
            }
        )
        subcities.append(subcity)
        if created:
            print(f"Created subcity: {subcity.name}")
        else:
            print(f"Subcity {subcity.name} already exists")

    # Create center types if they don't exist
    center_types = []
    for type_name in ['Main Office', 'Branch Office', 'Service Center', 'Registration Center']:
        center_type, created = CenterType.objects.get_or_create(
            name=type_name,
            defaults={
                'description': f'{type_name} for ID card registration and services',
                'is_active': True
            }
        )
        center_types.append(center_type)
        if created:
            print(f"Created center type: {center_type.name}")
        else:
            print(f"Center type {center_type.name} already exists")

    # Update existing centers to belong to subcities
    existing_centers = Center.objects.filter(subcity__isnull=True)
    for i, center in enumerate(existing_centers):
        # Assign to a random subcity
        center.subcity = subcities[i % len(subcities)]
        center.save()
        print(f"Updated center {center.name} to belong to subcity {center.subcity.name}")

    # Create new centers for each subcity
    for subcity in subcities:
        for i in range(1, 4):  # Create 3 centers per subcity
            center_name = f"{subcity.name} Kebele {i}"
            center, created = Center.objects.get_or_create(
                name=center_name,
                subcity=subcity,
                defaults={
                    'type': random.choice(center_types),
                    'description': f'ID card registration center for {center_name}',
                    'address': f'Kebele {i}, {subcity.name}, Addis Ababa',
                    'phone': f'+251-111-{random.randint(100000, 999999)}',
                    'email': f'info@kebele{i}.{subcity.name.lower().replace(" ", "")}.gov.et',
                    'admin_name': f'Kebele {i} Administrator',
                    'admin_email': f'admin@kebele{i}.{subcity.name.lower().replace(" ", "")}.gov.et',
                    'admin_phone': f'+251-111-{random.randint(100000, 999999)}',
                    'max_users': random.choice([5, 10, 15]),
                    'max_citizens': random.choice([500, 1000, 1500]),
                    'max_id_cards': random.choice([500, 1000, 1500]),
                    'is_active': True,
                    'is_verified': True,
                    'subscription_status': random.choice(['trial', 'basic', 'premium']),
                    'created_by': super_admin
                }
            )
            if created:
                print(f"Created center: {center.name}")
            else:
                print(f"Center {center.name} already exists")

    # Create admin users for each level
    # City admin
    city_admin_email = f'admin@{city.name.lower().replace(" ", "")}.gov.et'
    if not User.objects.filter(email=city_admin_email).exists():
        city_admin = User.objects.create_user(
            email=city_admin_email,
            password='password123',
            first_name=city.name,
            last_name='Admin',
            role='CITY_ADMIN',
            is_active=True
        )
        city_admin.city = city
        city_admin.save()
        print(f"Created city admin for {city.name}")
    else:
        print(f"City admin for {city.name} already exists")

    # Subcity admins
    for subcity in subcities:
        subcity_admin_email = f'admin@{subcity.name.lower().replace(" ", "")}.gov.et'
        if not User.objects.filter(email=subcity_admin_email).exists():
            subcity_admin = User.objects.create_user(
                email=subcity_admin_email,
                password='password123',
                first_name=subcity.name,
                last_name='Admin',
                role='SUBCITY_ADMIN',
                is_active=True
            )
            subcity_admin.subcity = subcity
            subcity_admin.save()
            print(f"Created subcity admin for {subcity.name}")
        else:
            print(f"Subcity admin for {subcity.name} already exists")

    # Center admins
    for center in Center.objects.all():
        center_admin_email = f'admin@{center.slug}.gov.et'
        if not User.objects.filter(email=center_admin_email).exists():
            center_admin = User.objects.create_user(
                email=center_admin_email,
                password='password123',
                first_name=center.name.split()[0],
                last_name='Admin',
                role='CENTER_ADMIN',
                is_active=True
            )
            center_admin.center = center
            center_admin.save()
            print(f"Created center admin for {center.name}")
        else:
            print(f"Center admin for {center.name} already exists")

    print("Hierarchy setup complete!")

if __name__ == '__main__':
    setup_hierarchy()
