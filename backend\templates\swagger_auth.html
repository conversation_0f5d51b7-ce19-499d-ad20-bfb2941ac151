<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Swagger Authentication Helper</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        h1 {
            color: #333;
            border-bottom: 1px solid #ddd;
            padding-bottom: 10px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"],
        input[type="password"] {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #45a049;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 4px;
            display: none;
        }
        .instructions {
            margin-top: 30px;
            padding: 15px;
            background-color: #f0f8ff;
            border-left: 4px solid #007bff;
        }
        code {
            background-color: #f1f1f1;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <h1>Swagger Authentication Helper</h1>
    <p>Use this form to get an authentication token for testing with Swagger UI.</p>
    
    <div class="form-group">
        <label for="email">Email:</label>
        <input type="text" id="email" placeholder="Enter your email">
    </div>
    
    <div class="form-group">
        <label for="password">Password:</label>
        <input type="password" id="password" placeholder="Enter your password">
    </div>
    
    <div class="form-group">
        <label for="schema_name">Schema Name (optional):</label>
        <input type="text" id="schema_name" placeholder="e.g., kebele_16">
    </div>
    
    <button onclick="getToken()">Get Token</button>
    
    <div id="result" class="result">
        <h3>Authentication Result</h3>
        <p id="message"></p>
        <p><strong>Token:</strong> <code id="token"></code></p>
        <p><strong>Schema Name:</strong> <code id="schema"></code></p>
    </div>
    
    <div class="instructions">
        <h3>How to use the token in Swagger UI:</h3>
        <ol>
            <li>Copy the token value shown above</li>
            <li>Go to the <a href="/swagger/" target="_blank">Swagger UI</a></li>
            <li>Click the "Authorize" button at the top right</li>
            <li>In the "Token" section, enter: <code>Token YOUR_TOKEN_HERE</code></li>
            <li>If you're accessing tenant-specific data, also enter your schema name in the "X-Schema-Name" field</li>
            <li>Click "Authorize" and close the dialog</li>
            <li>Now you can test the API endpoints with your authentication</li>
        </ol>
    </div>

    <script>
        function getToken() {
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const schema_name = document.getElementById('schema_name').value;
            
            if (!email || !password) {
                alert('Please enter both email and password');
                return;
            }
            
            fetch('/api/swagger-token-auth/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    email: email,
                    password: password,
                    schema_name: schema_name
                })
            })
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById('result');
                resultDiv.style.display = 'block';
                
                if (data.error) {
                    document.getElementById('message').textContent = data.error;
                    document.getElementById('token').textContent = 'N/A';
                    document.getElementById('schema').textContent = 'N/A';
                } else {
                    document.getElementById('message').textContent = data.message || 'Authentication successful';
                    document.getElementById('token').textContent = data.token;
                    document.getElementById('schema').textContent = data.schema_name || 'public';
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred. Please try again.');
            });
        }
    </script>
</body>
</html>
