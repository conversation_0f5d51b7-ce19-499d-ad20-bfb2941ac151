from django.db import models
from centers.models_region import Timestamp
from centers.models_base import TenantModel
from django.core.exceptions import ValidationError
from datetime import date

class PersonBase(Timestamp):
    """
    Base model for all person-related models with common fields.
    """
    first_name = models.Char<PERSON>ield(max_length=100)
    middle_name = models.CharField(max_length=100, blank=True, null=True)
    last_name = models.Char<PERSON>ield(max_length=100)
    first_name_am = models.Char<PERSON>ield(max_length=100, blank=True, null=True)
    middle_name_am = models.Char<PERSON>ield(max_length=100, blank=True, null=True)
    last_name_am = models.Char<PERSON>ield(max_length=100, blank=True, null=True)
    phone = models.Char<PERSON>ield(max_length=20, blank=True, null=True)
    email = models.EmailField(blank=True, null=True)
    is_active = models.<PERSON>oleanField(default=True)
    is_resident = models.BooleanField(default=False, help_text="Indicates whether the person is a resident of the city.")

    class Meta:
        abstract = True

    def get_full_name(self):
        """Return the full name of the person."""
        if self.middle_name:
            return f"{self.first_name} {self.middle_name} {self.last_name}"
        return f"{self.first_name} {self.last_name}"

    def get_full_name_am(self):
        """Return the full Amharic name of the person."""
        if not self.first_name_am:
            return None

        if self.middle_name_am:
            return f"{self.first_name_am} {self.middle_name_am} {self.last_name_am}"
        return f"{self.first_name_am} {self.last_name_am}"

    def clean(self):
        """Validate the model."""
        # Ensure at least one contact method is provided
        if hasattr(self, 'phone') and hasattr(self, 'email') and not self.phone and not self.email:
            raise ValidationError("At least one contact method (phone or email) must be provided.")

    def __str__(self):
        return self.get_full_name()


class TenantPersonBase(PersonBase, TenantModel):
    """
    Base model for all tenant-aware person-related models.
    Combines PersonBase fields with TenantModel functionality.
    """

    class Meta:
        abstract = True


def citizen_directory_path(instance, filename):
    """
    Function to determine the upload path for citizen-related files.
    Files will be uploaded to: media/citizens/<digital_id>_<full_name>/<file_type>/<filename>
    """
    # Get the citizen instance
    if hasattr(instance, 'citizen'):
        citizen = instance.citizen
    else:
        citizen = instance

    # Create a clean directory name using digital_id and full name
    digital_id = getattr(citizen, 'digital_id', 'unknown')
    full_name = citizen.get_full_name().replace(' ', '_')
    directory = f"citizens/{digital_id}_{full_name}"

    # Determine file type based on the instance class
    if isinstance(instance, models.Model):
        model_name = instance.__class__.__name__.lower()
        return f"{directory}/{model_name}/{filename}"

    return f"{directory}/misc/{filename}"


def validate_age(birth_date, min_age=18):
    """
    Validate that a person is at least min_age years old.
    """
    today = date.today()
    age = today.year - birth_date.year - ((today.month, today.day) < (birth_date.month, birth_date.day))
    if age < min_age:
        raise ValidationError(f"Person must be at least {min_age} years old.")
    return age
