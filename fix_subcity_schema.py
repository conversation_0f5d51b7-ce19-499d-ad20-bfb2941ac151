import os
import django

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'neocamelot.settings')
django.setup()

from django.db import connection

def fix_subcity_schema():
    """Fix the subcity schema by adding missing columns."""
    print("=== Fixing Subcity Schema ===")
    
    # Define the schema to fix
    schema_name = 'subcity_zoble'
    
    # Define the columns that should exist in the idcards_idcard table
    columns_to_check = [
        ('kebele_pattern', 'character varying(255)'),
        ('pattern_seed', 'character varying(64)'),
        ('subcity_pattern', 'character varying(255)'),
        ('kebele_approval_status', 'character varying(20)'),
        ('kebele_approval_notes', 'text'),
        ('kebele_approved_at', 'timestamp with time zone'),
        ('kebele_approved_by_id', 'bigint'),
        ('document_verification_required', 'boolean'),
        ('document_verification_status', 'character varying(20)'),
        ('document_verification_notes', 'text'),
        ('document_verified_at', 'timestamp with time zone'),
        ('document_verified_by_id', 'bigint')
    ]
    
    with connection.cursor() as cursor:
        # Check if the idcards_idcard table exists in this schema
        cursor.execute(f"""
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = '{schema_name}' 
                AND table_name = 'idcards_idcard'
            )
        """)
        table_exists = cursor.fetchone()[0]
        
        if not table_exists:
            print(f"idcards_idcard table does not exist in schema {schema_name}")
            return
        
        # Check each column and add it if it doesn't exist
        for column_name, column_type in columns_to_check:
            cursor.execute(f"""
                SELECT EXISTS (
                    SELECT FROM information_schema.columns 
                    WHERE table_schema = '{schema_name}' 
                    AND table_name = 'idcards_idcard'
                    AND column_name = '{column_name}'
                )
            """)
            column_exists = cursor.fetchone()[0]
            
            if not column_exists:
                print(f"Adding {column_name} column to {schema_name}.idcards_idcard")
                
                # Set default values based on column type
                default_value = ""
                if "boolean" in column_type:
                    default_value = "DEFAULT FALSE"
                elif "character varying" in column_type:
                    default_value = "DEFAULT ''"
                elif "timestamp" in column_type:
                    default_value = "DEFAULT NULL"
                elif "bigint" in column_type:
                    default_value = "DEFAULT NULL"
                
                cursor.execute(f"""
                    ALTER TABLE "{schema_name}".idcards_idcard 
                    ADD COLUMN {column_name} {column_type} {default_value}
                """)
                print(f"{column_name} column added successfully")
            else:
                print(f"{column_name} column already exists")

def check_child_idcards():
    """Check if the subcity can see ID cards from child kebeles."""
    print("\n=== Checking Child ID Cards ===")
    
    # Define the schemas
    subcity_schema = 'subcity_zoble'
    kebele_schema = 'kebele 14'
    
    with connection.cursor() as cursor:
        # Get ID cards from kebele schema
        cursor.execute(f"""
            SELECT id, status, kebele_pattern
            FROM "{kebele_schema}".idcards_idcard
            WHERE status = 'PENDING_SUBCITY'
            ORDER BY id
        """)
        kebele_cards = cursor.fetchall()
        
        print(f"Found {len(kebele_cards)} ID cards with status 'PENDING_SUBCITY' in schema {kebele_schema}")
        
        # Create a view in the subcity schema to see ID cards from child kebeles
        try:
            cursor.execute(f"""
                CREATE OR REPLACE VIEW "{subcity_schema}".child_idcards AS
                SELECT id, card_number, issue_date, expiry_date, status, 
                       kebele_pattern, pattern_seed, subcity_pattern,
                       kebele_approval_status, kebele_approval_notes,
                       kebele_approved_at, kebele_approved_by_id,
                       '{kebele_schema}' as source_schema
                FROM "{kebele_schema}".idcards_idcard
                WHERE status = 'PENDING_SUBCITY'
            """)
            print(f"Created view child_idcards in schema {subcity_schema}")
            
            # Check if the view works
            cursor.execute(f"""
                SELECT id, status, kebele_pattern, source_schema
                FROM "{subcity_schema}".child_idcards
                ORDER BY id
            """)
            view_cards = cursor.fetchall()
            
            print(f"Found {len(view_cards)} ID cards in the child_idcards view")
            
            for card in view_cards:
                card_id, status, kebele_pattern, source_schema = card
                print(f"ID Card {card_id}:")
                print(f"  Status: {status}")
                print(f"  Kebele Pattern: {kebele_pattern is not None}")
                print(f"  Source Schema: {source_schema}")
        except Exception as e:
            print(f"Error creating view: {str(e)}")

if __name__ == "__main__":
    fix_subcity_schema()
    check_child_idcards()
