"""
Django settings for neocamelot project.

Generated by 'django-admin startproject' using Django 5.1.7.

For more information on this file, see
https://docs.djangoproject.com/en/5.1/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/5.1/ref/settings/
"""

from pathlib import Path
import os
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent


# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/5.1/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = os.environ.get('SECRET_KEY', 'django-insecure-sz0)172+)17yjapa^(6)i!f(upj@&8$j4^d*2w6^z+e^4#0%fj')

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = os.environ.get('DEBUG', 'True') == 'True'

ALLOWED_HOSTS = ['localhost', '127.0.0.1', '0.0.0.0']


# Application definition

SHARED_APPS = (
    'django_tenants',  # mandatory
    'centers',  # tenant model

    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',

    # Third-party apps
    'rest_framework',
    'corsheaders',
    # 'rest_framework.authtoken',  # Removed - using JWT authentication only
    'drf_yasg',  # Swagger/OpenAPI

    # Local shared apps
    'accounts',
    'common',  # Common data app for shared reference data
)

TENANT_APPS = (
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',

    # Third-party apps
    'rest_framework',
    # 'rest_framework.authtoken',  # Removed - using JWT authentication only
    'corsheaders',
    'drf_yasg',  # Swagger/OpenAPI

    # Local tenant apps
    'accounts',
    'citizens',
    'idcards',
)

INSTALLED_APPS = list(SHARED_APPS) + [app for app in TENANT_APPS if app not in SHARED_APPS]

MIDDLEWARE = [
    'neocamelot.simple_cors_middleware.SimpleCorsMiddleware',  # Our simple CORS middleware at the very top
    'centers.schema_middleware.SchemaHeaderMiddleware',  # Our schema header middleware before django-tenants
    'django_tenants.middleware.main.TenantMainMiddleware',  # Should be at the top
    'accounts.jwt_middleware.JWTSchemaMiddleware',  # JWT middleware for schema routing
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',  # Django's session middleware
    'neocamelot.session_middleware.SessionMiddlewareBypass',  # Our session bypass middleware (after session middleware)
    'corsheaders.middleware.CorsMiddleware',  # CORS middleware
    'django.middleware.common.CommonMiddleware',
    # Removed CSRF middleware since we're using JWT authentication
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'accounts.admin_auth_middleware.AdminAuthMiddleware',  # Our admin authentication middleware
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
    'centers.tenant_middleware.TenantMiddleware',  # Our custom middleware after django-tenants
]

ROOT_URLCONF = 'neocamelot.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [os.path.join(BASE_DIR, 'templates')],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

WSGI_APPLICATION = 'neocamelot.wsgi.application'


# Database
# https://docs.djangoproject.com/en/5.1/ref/settings/#databases

# SQLite configuration (for development)
# DATABASES = {
#     'default': {
#         'ENGINE': 'django.db.backends.sqlite3',
#         'NAME': BASE_DIR / 'db.sqlite3',
#     }
# }

# PostgreSQL configuration for django-tenants
DATABASES = {
    'default': {
        'ENGINE': 'django_tenants.postgresql_backend',  # Use the django-tenants engine
        'NAME': os.environ.get('DB_NAME', 'neocamelot'),
        'USER': os.environ.get('DB_USER', 'postgres'),
        'PASSWORD': os.environ.get('DB_PASSWORD', 'postgres'),
        'HOST': os.environ.get('DB_HOST', 'localhost'),
        'PORT': os.environ.get('DB_PORT', '5432'),
    }
}

# Django-tenants specific settings
DATABASE_ROUTERS = ('django_tenants.routers.TenantSyncRouter',)

TENANT_MODEL = 'centers.Client'  # app.Model
TENANT_DOMAIN_MODEL = 'centers.Domain'  # app.Model

PUBLIC_SCHEMA_NAME = 'public'
PUBLIC_SCHEMA_URLCONF = 'neocamelot.urls'
ROOT_URLCONF = 'neocamelot.urls'  # Use the same URLs for all tenants for now


# Password validation
# https://docs.djangoproject.com/en/5.1/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]


# Internationalization
# https://docs.djangoproject.com/en/5.1/topics/i18n/

LANGUAGE_CODE = 'en-us'

TIME_ZONE = 'UTC'

USE_I18N = True

USE_TZ = True


# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/5.1/howto/static-files/

STATIC_URL = 'static/'

# Default primary key field type
# https://docs.djangoproject.com/en/5.1/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

# REST Framework settings
REST_FRAMEWORK = {
    'DEFAULT_AUTHENTICATION_CLASSES': [
        'accounts.jwt_authentication.JWTAuthentication',  # JWT authentication as the only option
    ],
    'DEFAULT_PERMISSION_CLASSES': [
        'rest_framework.permissions.IsAuthenticated',
    ],
    'DEFAULT_PAGINATION_CLASS': 'rest_framework.pagination.PageNumberPagination',
    'PAGE_SIZE': 10,
    'DEFAULT_FILTER_BACKENDS': [
        'django_filters.rest_framework.DjangoFilterBackend',
        'rest_framework.filters.SearchFilter',
        'rest_framework.filters.OrderingFilter',
    ],
    'DEFAULT_SCHEMA_CLASS': 'rest_framework.schemas.AutoSchema',
    'UNAUTHENTICATED_USER': None,  # Return None for request.user when not authenticated
    'EXCEPTION_HANDLER': 'neocamelot.exception_handlers.custom_exception_handler',  # Custom exception handler
}

# Authentication classes are already set above

# Media files
MEDIA_URL = '/media/'
MEDIA_ROOT = BASE_DIR / 'media'

# Custom User model
AUTH_USER_MODEL = 'accounts.User'

# Authentication backends
AUTHENTICATION_BACKENDS = [
    'accounts.backends.EmailOrUsernameModelBackend',  # Custom backend for email or username login
    'django.contrib.auth.backends.ModelBackend',  # Default backend
]

# CORS settings
CORS_ALLOW_ALL_ORIGINS = True  # Set to True for development
CORS_ALLOW_CREDENTIALS = True

# These settings are now handled by our SimpleCorsMiddleware
# CORS_ALLOWED_ORIGINS = [
#     'http://localhost:5173',  # Vite dev server
#     'http://127.0.0.1:5173',
#     'http://localhost:5174',  # Alternative Vite dev server port
#     'http://127.0.0.1:5174',
#     'http://localhost:8000',
#     'http://127.0.0.1:8000',
# ]

# Additional CORS settings
CORS_EXPOSE_HEADERS = ['Content-Type']
CORS_PREFLIGHT_MAX_AGE = 86400  # 24 hours
CORS_ALLOW_METHODS = [
    'DELETE',
    'GET',
    'OPTIONS',
    'PATCH',
    'POST',
    'PUT',
]
CORS_ALLOW_HEADERS = [
    'accept',
    'accept-encoding',
    'authorization',
    'content-type',
    'dnt',
    'origin',
    'user-agent',
    'x-requested-with',
    'x-schema-name',
    'x-auth-token',
    'x-csrftoken',
]

# CSRF settings removed - using JWT authentication only

# Session settings
SESSION_COOKIE_SAMESITE = 'None'  # Required for cross-origin requests
SESSION_COOKIE_SECURE = False  # Set to True in production with HTTPS
SESSION_COOKIE_HTTPONLY = True  # Prevent JavaScript from accessing the session cookie
SESSION_SAVE_EVERY_REQUEST = False  # Don't save the session on every request
SESSION_EXPIRE_AT_BROWSER_CLOSE = True  # Session expires when browser closes
# Use database sessions for better compatibility with django-tenants
# SESSION_ENGINE = 'django.contrib.sessions.backends.signed_cookies'

# JWT settings
JWT_SECRET_KEY = os.environ.get('JWT_SECRET_KEY', SECRET_KEY)  # Use Django's SECRET_KEY as fallback
JWT_ALGORITHM = 'HS256'  # HMAC with SHA-256
JWT_ACCESS_TOKEN_EXPIRY = 15 * 60  # 15 minutes in seconds
JWT_REFRESH_TOKEN_EXPIRY = 7 * 24 * 60 * 60  # 7 days in seconds

# JWT authentication is already set as the primary authentication method above

# Swagger settings
SWAGGER_SETTINGS = {
    'SECURITY_DEFINITIONS': {
        'Bearer': {
            'type': 'apiKey',
            'name': 'Authorization',
            'in': 'header',
            'description': 'JWT Token Authentication: Enter "Bearer <token>"'
        },
        'X-Schema-Name': {
            'type': 'apiKey',
            'name': 'X-Schema-Name',
            'in': 'header',
            'description': 'Tenant Schema Name: Enter the schema name (e.g., "kebele 16")'
        }
    },
    'USE_SESSION_AUTH': False,  # Disable session auth since we're using JWT only
    'LOGIN_URL': '/api/jwt/login/',  # Use JWT login endpoint
    'LOGOUT_URL': None,  # No logout URL needed for JWT
    'DEFAULT_MODEL_RENDERING': 'example',
    'DEFAULT_INFO': 'neocamelot.urls.schema_view.info',
}
