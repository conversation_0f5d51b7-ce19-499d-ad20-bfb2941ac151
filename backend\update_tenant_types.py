import os
import django

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'neocamelot.settings')
django.setup()

# Import models
from centers.models import Client
from django.contrib.auth import get_user_model
from django_tenants.utils import tenant_context

User = get_user_model()

def update_tenant_types():
    """Update tenant types from CENTER to KEBELE."""
    print("\n===== UPDATING TENANT TYPES =====\n")
    
    # Get all tenants with schema_type = CENTER
    center_tenants = Client.objects.filter(schema_type='CENTER')
    print(f"Found {center_tenants.count()} CENTER tenants to update")
    
    for tenant in center_tenants:
        print(f"\nUpdating tenant: {tenant.name} (Schema: {tenant.schema_name})")
        
        # Update schema_type from CENTER to KEBELE
        tenant.schema_type = 'KEBELE'
        tenant.save()
        print(f"Updated schema_type to KEBELE")
        
        # Update admin roles in tenant schema
        with tenant_context(tenant):
            # Update CENTER_ADMIN to KEBELE_ADMIN
            admin_users = User.objects.filter(role='CENTER_ADMIN')
            for user in admin_users:
                print(f"Updating user {user.email} role from CENTER_ADMIN to KEBELE_ADMIN")
                user.role = 'KEBELE_ADMIN'
                user.save()
    
    print("\n===== TENANT TYPE UPDATE COMPLETE =====\n")

if __name__ == "__main__":
    update_tenant_types()
