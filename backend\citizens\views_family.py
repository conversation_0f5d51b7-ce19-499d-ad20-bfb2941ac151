from rest_framework import viewsets, permissions, filters, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from .models_family import Child, Parent, EmergencyContact, Spouse
from common.models import RelationshipType
from .serializers_family import ChildSerializer, ParentSerializer, EmergencyContactSerializer, SpouseSerializer, RelationshipTypeSerializer
from accounts.permissions import IsCenterAdmin, IsAdminOrSuperAdmin, IsKebeleLevelOrSuperAdmin

class RelationshipTypeViewSet(viewsets.ModelViewSet):
    """ViewSet for viewing and editing RelationshipType instances."""
    queryset = RelationshipType.objects.all()
    serializer_class = RelationshipTypeSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['name']
    ordering_fields = ['name']

    def get_permissions(self):
        """Set custom permissions for different actions."""
        if self.action in ['create', 'update', 'partial_update', 'destroy']:
            return [IsAdminOrSuperAdmin()]
        return [permissions.IsAuthenticated()]

class ChildViewSet(viewsets.ModelViewSet):
    """ViewSet for viewing and editing Child instances."""
    queryset = Child.objects.all()
    serializer_class = ChildSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['citizen', 'is_active', 'is_resident', 'nationality']
    search_fields = ['first_name', 'middle_name', 'last_name', 'first_name_am', 'middle_name_am', 'last_name_am']
    ordering_fields = ['first_name', 'last_name', 'date_of_birth', 'created_at']

    def get_permissions(self):
        """Set custom permissions for different actions."""
        if self.action in ['create', 'update', 'partial_update', 'destroy']:
            return [IsKebeleLevelOrSuperAdmin()]
        return [permissions.IsAuthenticated()]

    @action(detail=False, methods=['get'])
    def by_citizen(self, request):
        """Get all children for a specific citizen."""
        citizen_id = request.query_params.get('citizen_id')
        if not citizen_id:
            return Response({'error': 'citizen_id is required'}, status=status.HTTP_400_BAD_REQUEST)

        children = Child.objects.filter(citizen_id=citizen_id)
        serializer = self.get_serializer(children, many=True)
        return Response(serializer.data)

class ParentViewSet(viewsets.ModelViewSet):
    """ViewSet for viewing and editing Parent instances."""
    queryset = Parent.objects.all()
    serializer_class = ParentSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['citizen', 'is_active', 'is_resident', 'nationality']
    search_fields = ['first_name', 'middle_name', 'last_name', 'first_name_am', 'middle_name_am', 'last_name_am']
    ordering_fields = ['first_name', 'last_name', 'created_at']

    def get_permissions(self):
        """Set custom permissions for different actions."""
        if self.action in ['create', 'update', 'partial_update', 'destroy']:
            return [IsKebeleLevelOrSuperAdmin()]
        return [permissions.IsAuthenticated()]

    @action(detail=False, methods=['get'])
    def by_citizen(self, request):
        """Get all parents for a specific citizen."""
        citizen_id = request.query_params.get('citizen_id')
        if not citizen_id:
            return Response({'error': 'citizen_id is required'}, status=status.HTTP_400_BAD_REQUEST)

        parents = Parent.objects.filter(citizen_id=citizen_id)
        serializer = self.get_serializer(parents, many=True)
        return Response(serializer.data)

class EmergencyContactViewSet(viewsets.ModelViewSet):
    """ViewSet for viewing and editing EmergencyContact instances."""
    queryset = EmergencyContact.objects.all()
    serializer_class = EmergencyContactSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['citizen', 'is_active', 'is_resident', 'primary_contact', 'relationship']
    search_fields = ['first_name', 'middle_name', 'last_name', 'first_name_am', 'middle_name_am', 'last_name_am', 'phone', 'email']
    ordering_fields = ['first_name', 'last_name', 'created_at']

    def get_permissions(self):
        """Set custom permissions for different actions."""
        if self.action in ['create', 'update', 'partial_update', 'destroy']:
            return [IsKebeleLevelOrSuperAdmin()]
        return [permissions.IsAuthenticated()]

    @action(detail=False, methods=['get'])
    def by_citizen(self, request):
        """Get all emergency contacts for a specific citizen."""
        citizen_id = request.query_params.get('citizen_id')
        if not citizen_id:
            return Response({'error': 'citizen_id is required'}, status=status.HTTP_400_BAD_REQUEST)

        contacts = EmergencyContact.objects.filter(citizen_id=citizen_id)
        serializer = self.get_serializer(contacts, many=True)
        return Response(serializer.data)

class SpouseViewSet(viewsets.ModelViewSet):
    """ViewSet for viewing and editing Spouse instances."""
    queryset = Spouse.objects.all()
    serializer_class = SpouseSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['citizen', 'is_active', 'is_resident', 'primary_contact', 'nationality']
    search_fields = ['first_name', 'middle_name', 'last_name', 'first_name_am', 'middle_name_am', 'last_name_am', 'phone', 'email']
    ordering_fields = ['first_name', 'last_name', 'created_at']

    def get_permissions(self):
        """Set custom permissions for different actions."""
        if self.action in ['create', 'update', 'partial_update', 'destroy']:
            return [IsKebeleLevelOrSuperAdmin()]
        return [permissions.IsAuthenticated()]

    @action(detail=False, methods=['get'])
    def by_citizen(self, request):
        """Get all spouses for a specific citizen."""
        citizen_id = request.query_params.get('citizen_id')
        if not citizen_id:
            return Response({'error': 'citizen_id is required'}, status=status.HTTP_400_BAD_REQUEST)

        spouses = Spouse.objects.filter(citizen_id=citizen_id)
        serializer = self.get_serializer(spouses, many=True)
        return Response(serializer.data)
