"""
Schema Utilities

This module provides utility functions for working with schema names.
"""

import logging
from django.db import connection
from django_tenants.utils import get_public_schema_name
from centers.models import Client

# Configure logging
logger = logging.getLogger(__name__)

def normalize_schema_name(schema_name):
    """
    Normalize a schema name to ensure it's in the correct format.
    
    Args:
        schema_name (str): The schema name to normalize
        
    Returns:
        str: The normalized schema name
    """
    if not schema_name:
        return None
        
    # Convert to lowercase
    schema_name = schema_name.lower()
    
    # Skip public schema
    if schema_name == get_public_schema_name():
        return schema_name
        
    # Try different variations of the schema name
    variations = [schema_name]
    
    # Add variations with spaces and underscores
    if '_' in schema_name:
        variations.append(schema_name.replace('_', ' '))
    if ' ' in schema_name:
        variations.append(schema_name.replace(' ', '_'))
        
    # For kebele schemas, add more variations
    if schema_name.startswith('kebele'):
        # Extract the kebele number if present
        import re
        match = re.match(r'kebele[_ ]?(\d+)', schema_name)
        if match:
            kebele_number = match.group(1)
            variations.append(f'kebele {kebele_number}')
            variations.append(f'kebele_{kebele_number}')
            variations.append(f'kebele{kebele_number}')
    
    logger.info(f"Trying schema variations: {variations}")
    
    # Try each variation to find a matching tenant
    for variation in variations:
        try:
            tenant = Client.objects.get(schema_name=variation)
            logger.info(f"Found matching tenant for schema: {variation}")
            return variation
        except Client.DoesNotExist:
            logger.info(f"No tenant found for schema: {variation}")
        except Exception as e:
            logger.error(f"Error checking schema variation {variation}: {str(e)}")
    
    # If no matching tenant found, return the original schema name
    logger.warning(f"No matching tenant found for any variation of {schema_name}")
    return schema_name

def get_tenant_for_schema(schema_name):
    """
    Get the tenant for a schema name, trying different variations if needed.
    
    Args:
        schema_name (str): The schema name to look up
        
    Returns:
        Client: The tenant object or None if not found
    """
    if not schema_name:
        return None
        
    # Normalize the schema name
    normalized_schema = normalize_schema_name(schema_name)
    
    # Try to get the tenant
    try:
        tenant = Client.objects.get(schema_name=normalized_schema)
        return tenant
    except Client.DoesNotExist:
        logger.warning(f"No tenant found for normalized schema: {normalized_schema}")
        return None
    except Exception as e:
        logger.error(f"Error getting tenant for schema {normalized_schema}: {str(e)}")
        return None

def set_tenant_for_request(request, schema_name):
    """
    Set the tenant for a request based on the schema name.
    
    Args:
        request: The request object
        schema_name (str): The schema name
        
    Returns:
        bool: True if successful, False otherwise
    """
    if not schema_name:
        return False
        
    # Get the tenant
    tenant = get_tenant_for_schema(schema_name)
    
    if not tenant:
        return False
        
    # Set the tenant for this request
    try:
        connection.set_tenant(tenant)
        request.tenant = tenant
        logger.info(f"Set tenant to {tenant.name} (schema: {tenant.schema_name})")
        return True
    except Exception as e:
        logger.error(f"Error setting tenant for schema {schema_name}: {str(e)}")
        return False
