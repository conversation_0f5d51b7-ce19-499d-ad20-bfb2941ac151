import React from 'react';
import { Box, Typography, Paper, Button } from '@mui/material';
import { useNavigate } from 'react-router-dom';
import LockIcon from '@mui/icons-material/Lock';
import HomeIcon from '@mui/icons-material/Home';

interface UnauthorizedAccessProps {
  message?: string;
  showHomeButton?: boolean;
}

/**
 * Component to display when a user doesn't have permission to access a page
 */
const UnauthorizedAccess: React.FC<UnauthorizedAccessProps> = ({
  message = 'You do not have permission to access this page.',
  showHomeButton = true
}) => {
  const navigate = useNavigate();

  return (
    <Paper
      elevation={0}
      sx={{
        p: 4,
        borderRadius: 2,
        textAlign: 'center',
        bgcolor: 'rgba(0, 0, 0, 0.02)',
        border: '1px solid rgba(0, 0, 0, 0.05)',
        my: 4
      }}
    >
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          py: 4
        }}
      >
        <Box
          sx={{
            bgcolor: 'error.light',
            color: 'error.contrastText',
            borderRadius: '50%',
            p: 2,
            mb: 3,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}
        >
          <LockIcon sx={{ fontSize: 40 }} />
        </Box>

        <Typography variant="h5" gutterBottom fontWeight={600} color="error.main">
          Unauthorized Access
        </Typography>

        <Typography variant="body1" color="text.secondary" sx={{ maxWidth: 500, mb: 4 }}>
          {message}
        </Typography>

        {showHomeButton && (
          <Button
            variant="contained"
            color="primary"
            startIcon={<HomeIcon />}
            onClick={() => navigate('/dashboard')}
            sx={{ borderRadius: 2 }}
          >
            Go to Dashboard
          </Button>
        )}
      </Box>
    </Paper>
  );
};

export default UnauthorizedAccess;
