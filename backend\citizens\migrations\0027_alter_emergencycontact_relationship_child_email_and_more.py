# Generated by Django 5.1.7 on 2025-04-18 19:19

import citizens.models_base
import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('citizens', '0026_merge_20250418_2212'),
        ('common', '0008_populate_document_types'),
    ]

    operations = [
        migrations.AlterField(
            model_name='emergencycontact',
            name='relationship',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='common.relationshiptype'),
        ),
        migrations.AddField(
            model_name='child',
            name='email',
            field=models.EmailField(blank=True, max_length=254, null=True),
        ),
        migrations.AddField(
            model_name='child',
            name='is_resident',
            field=models.BooleanField(default=False, help_text='Indicates whether the person is a resident of the city.'),
        ),
        migrations.AddField(
            model_name='child',
            name='linked_citizen',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='linked_as_child', to='citizens.citizen'),
        ),
        migrations.AddField(
            model_name='child',
            name='phone',
            field=models.CharField(blank=True, max_length=20, null=True),
        ),
        migrations.AddField(
            model_name='emergencycontact',
            name='first_name_am',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='emergencycontact',
            name='is_resident',
            field=models.BooleanField(default=False, help_text='Indicates whether the person is a resident of the city.'),
        ),
        migrations.AddField(
            model_name='emergencycontact',
            name='last_name_am',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='emergencycontact',
            name='linked_citizen',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='linked_as_emergency_contact', to='citizens.citizen'),
        ),
        migrations.AddField(
            model_name='emergencycontact',
            name='middle_name_am',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='parent',
            name='email',
            field=models.EmailField(blank=True, max_length=254, null=True),
        ),
        migrations.AddField(
            model_name='parent',
            name='is_resident',
            field=models.BooleanField(default=False, help_text='Indicates whether the person is a resident of the city.'),
        ),
        migrations.AddField(
            model_name='parent',
            name='linked_citizen',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='linked_as_parent', to='citizens.citizen'),
        ),
        migrations.AddField(
            model_name='parent',
            name='phone',
            field=models.CharField(blank=True, max_length=20, null=True),
        ),
        migrations.AddField(
            model_name='spouse',
            name='first_name_am',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='spouse',
            name='is_resident',
            field=models.BooleanField(default=False, help_text='Indicates whether the person is a resident of the city.'),
        ),
        migrations.AddField(
            model_name='spouse',
            name='last_name_am',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='spouse',
            name='linked_citizen',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='linked_as_spouse', to='citizens.citizen'),
        ),
        migrations.AddField(
            model_name='spouse',
            name='middle_name_am',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AlterField(
            model_name='citizen',
            name='citizen_status',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.PROTECT, to='common.citizenstatus'),
        ),
        migrations.AlterField(
            model_name='citizen',
            name='employee_type',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='common.employeetype'),
        ),
        migrations.AlterField(
            model_name='citizen',
            name='employment_type',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='common.employmenttype'),
        ),
        migrations.AlterField(
            model_name='citizen',
            name='ketena',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='citizens', to='common.ketena'),
        ),
        migrations.AlterField(
            model_name='citizen',
            name='nationality',
            field=models.CharField(default='Ethiopian', help_text='Nationality as text (derived from nationality_country if set)', max_length=100),
        ),
        migrations.AlterField(
            model_name='citizen',
            name='nationality_country',
            field=models.ForeignKey(blank=True, help_text='Country of nationality', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='citizens', to='common.country'),
        ),
        migrations.AlterField(
            model_name='citizen',
            name='region',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='common.region'),
        ),
        migrations.AlterField(
            model_name='citizen',
            name='religion',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='common.religion'),
        ),
        migrations.AlterField(
            model_name='document',
            name='document_file',
            field=models.FileField(upload_to=citizens.models_base.citizen_directory_path),
        ),
        migrations.AlterField(
            model_name='document',
            name='document_type',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='common.documenttype'),
        ),
        migrations.AlterField(
            model_name='photo',
            name='photo',
            field=models.ImageField(upload_to=citizens.models_base.citizen_directory_path),
        ),
        migrations.DeleteModel(
            name='Relationship',
        ),
    ]
