from django.http import HttpResponse

def cors_preflight(request, path=None):
    """
    Handle OPTIONS requests for CORS preflight.
    This view can be used as a fallback for any URL that might receive OPTIONS requests.
    """
    if request.method == "OPTIONS":
        response = HttpResponse()
        response["Access-Control-Allow-Origin"] = "http://localhost:5173"
        response["Access-Control-Allow-Methods"] = "GET, POST, PUT, DELETE, OPTIONS"
        response["Access-Control-Allow-Headers"] = "Content-Type, Authorization, X-Requested-With, X-Schema-Name, X-CSRFToken, X-Auth-Token"
        response["Access-Control-Allow-Credentials"] = "true"
        response["Access-Control-Max-Age"] = "86400"  # 24 hours
        return response

    # For non-OPTIONS requests, return a 405 Method Not Allowed
    return HttpResponse(status=405)
