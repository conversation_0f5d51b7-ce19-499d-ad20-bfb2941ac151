import React, { useEffect, useState } from 'react';
import {
  Box,
  Container,
  Typography,
  Paper,
  Grid,
  Button,
  CircularProgress,
  Card,
  CardContent,
  CardActions,
  Divider,
  IconButton,
  Tooltip,
  useTheme
} from '@mui/material';
import { useNavigate } from 'react-router-dom';
import DashboardIcon from '@mui/icons-material/Dashboard';
import PeopleIcon from '@mui/icons-material/People';
import CardMembershipIcon from '@mui/icons-material/CardMembership';
import SettingsIcon from '@mui/icons-material/Settings';
import AssessmentIcon from '@mui/icons-material/Assessment';
import PersonAddIcon from '@mui/icons-material/PersonAdd';
import PrintIcon from '@mui/icons-material/Print';
import SearchIcon from '@mui/icons-material/Search';
import HelpOutlineIcon from '@mui/icons-material/HelpOutline';
import TenantHeader from '../components/tenant/TenantHeader';
import PageBanner from '../components/PageBanner';

interface User {
  id: number;
  email: string;
  first_name: string;
  last_name: string;
  role: string;
}

interface Tenant {
  schema_name: string;
  name: string;
  type: string;
}

interface DashboardCardProps {
  title: string;
  description: string;
  icon: React.ReactNode;
  count?: number;
  path: string;
  color: string;
}

const DashboardCard: React.FC<DashboardCardProps> = ({
  title,
  description,
  icon,
  count,
  path,
  color
}) => {
  const navigate = useNavigate();
  const theme = useTheme();

  return (
    <Card
      sx={{
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        transition: 'transform 0.3s, box-shadow 0.3s',
        '&:hover': {
          transform: 'translateY(-8px)',
          boxShadow: '0 12px 20px rgba(0, 0, 0, 0.15)',
        },
        borderRadius: 3,
        overflow: 'hidden',
        border: 'none',
        boxShadow: '0 6px 12px rgba(0, 0, 0, 0.08)',
        position: 'relative',
        '&::before': {
          content: '""',
          position: 'absolute',
          top: 0,
          left: 0,
          width: '100%',
          height: '4px',
          backgroundColor: color,
          zIndex: 1
        }
      }}
    >
      <Box
        sx={{
          p: 3,
          display: 'flex',
          alignItems: 'center',
          bgcolor: 'white',
          color: theme.palette.text.primary,
          borderBottom: '1px solid rgba(0, 0, 0, 0.05)'
        }}
      >
        <Box
          sx={{
            mr: 2,
            bgcolor: `${color}15`, // 15% opacity of the color
            color: color,
            p: 1.5,
            borderRadius: 2,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}
        >
          {icon}
        </Box>
        <Typography variant="h6" component="div" fontWeight="500">
          {title}
        </Typography>
        {count !== undefined && (
          <Box
            sx={{
              ml: 'auto',
              bgcolor: `${color}15`, // 15% opacity of the color
              color: color,
              borderRadius: '50%',
              width: 40,
              height: 40,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              fontWeight: 'bold'
            }}
          >
            <Typography variant="body1" fontWeight="bold">
              {count}
            </Typography>
          </Box>
        )}
      </Box>
      <CardContent sx={{ flexGrow: 1, p: 3 }}>
        <Typography variant="body1" color="text.secondary" sx={{ lineHeight: 1.6 }}>
          {description}
        </Typography>
      </CardContent>
      <Divider sx={{ opacity: 0.5 }} />
      <CardActions sx={{ p: 2 }}>
        <Button
          size="medium"
          onClick={() => navigate(path)}
          sx={{
            textTransform: 'none',
            color: color,
            fontWeight: 500,
            '&:hover': {
              backgroundColor: `${color}10`, // 10% opacity of the color
            }
          }}
        >
          Open
        </Button>
        <Tooltip title="Help">
          <IconButton
            size="small"
            sx={{
              ml: 'auto',
              color: theme.palette.text.secondary,
              '&:hover': {
                backgroundColor: `${theme.palette.text.secondary}10`, // 10% opacity
              }
            }}
          >
            <HelpOutlineIcon fontSize="small" />
          </IconButton>
        </Tooltip>
      </CardActions>
    </Card>
  );
};

const TenantDashboard: React.FC = () => {
  const navigate = useNavigate();
  const theme = useTheme();
  const [user, setUser] = useState<User | null>(null);
  const [tenant, setTenant] = useState<Tenant | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Load user and tenant from localStorage
    try {
      const storedUser = localStorage.getItem('user');
      const storedTenant = localStorage.getItem('tenant');

      if (storedUser) {
        const parsedUser = JSON.parse(storedUser);
        setUser(parsedUser);
      }

      if (storedTenant) {
        const parsedTenant = JSON.parse(storedTenant);
        setTenant(parsedTenant);
      }
    } catch (error) {
      console.error('Error loading user or tenant data:', error);
    } finally {
      setLoading(false);
    }
  }, []);

  // Dashboard cards based on tenant type
  const getDashboardCards = () => {
    // Common dashboard card for all tenant types
    const dashboardCard = {
      title: 'Dashboard',
      description: 'Overview of your ID card system',
      icon: <DashboardIcon />,
      path: '/dashboard',
      color: theme.palette.primary.main
    };

    // Tenant-specific cards based on tenant type
    if (tenant?.type === 'KEBELE') {
      // Kebele tenants can register citizens and ID cards
      return [
        dashboardCard,
        {
          title: 'Citizens',
          description: 'Manage citizen records and information',
          icon: <PeopleIcon />,
          count: 0,
          path: '/citizens-new',
          color: theme.palette.secondary.main
        },
        {
          title: 'ID Cards',
          description: 'View and manage issued ID cards',
          icon: <CardMembershipIcon />,
          count: 0,
          path: '/id-cards',
          color: '#4caf50'
        }
      ];
    } else if (tenant?.type === 'SUBCITY') {
      // Subcity tenants can print ID cards and view reports
      return [
        dashboardCard,
        {
          title: 'Print ID Cards',
          description: 'Print approved ID cards',
          icon: <PrintIcon />,
          path: '/print-cards',
          color: '#ff9800'
        },
        {
          title: 'Reports',
          description: 'Generate and view reports',
          icon: <AssessmentIcon />,
          path: '/reports',
          color: '#9c27b0'
        }
      ];
    } else if (tenant?.type === 'CITY') {
      // City tenants can view reports and manage settings
      return [
        dashboardCard,
        {
          title: 'Reports',
          description: 'Generate and view reports',
          icon: <AssessmentIcon />,
          path: '/reports',
          color: '#ff9800'
        },
        {
          title: 'Settings',
          description: 'Configure system settings',
          icon: <SettingsIcon />,
          path: '/settings',
          color: '#9c27b0'
        }
      ];
    }

    // Default cards if tenant type is unknown
    return [dashboardCard];
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ bgcolor: '#f8f9fa', minHeight: '100vh' }}>
      <PageBanner
        title="Dashboard"
        subtitle={`Welcome, ${user?.first_name || 'User'}!`}
        icon={<DashboardIcon sx={{ fontSize: 50, color: 'white' }} />}
        actionContent={
          <Box sx={{ textAlign: 'center', width: '100%' }}>
            <Typography variant="body1" sx={{ color: 'white', fontWeight: 500 }}>
              {tenant?.type === 'KEBELE' && 'Manage citizen registration and ID card applications for your kebele.'}
              {tenant?.type === 'SUBCITY' && 'Oversee ID card printing and management for your subcity.'}
              {tenant?.type === 'CITY' && 'Monitor the ID card system across the entire city.'}
              {!tenant?.type && 'Manage your ID card system.'}
            </Typography>
          </Box>
        }
      >
        {/* ID Card Overlapping Elements */}
        <Box
          sx={{
            display: { xs: 'none', md: 'block' },
            position: 'absolute',
            right: '-15%',
            top: '50%',
            transform: 'translateY(-50%)',
            width: '250px',
            height: '180px',
            zIndex: 5
          }}
        >
          <Box
            sx={{
              position: 'absolute',
              top: 0,
              right: 0,
              width: '200px',
              height: '120px',
              borderRadius: '10px',
              background: 'rgba(255,255,255,0.9)',
              boxShadow: '0 10px 20px rgba(0,0,0,0.1)',
              transform: 'rotate(5deg)',
              overflow: 'hidden'
            }}
          >
            <Box
              sx={{
                height: '30px',
                background: '#3f51b5',
                display: 'flex',
                alignItems: 'center',
                px: 2
              }}
            >
              <Typography variant="caption" sx={{ color: 'white', fontWeight: 600 }}>
                GONDAR RESIDENT ID
              </Typography>
            </Box>
            <Box sx={{ p: 1, display: 'flex' }}>
              <Box
                sx={{
                  width: '40px',
                  height: '50px',
                  borderRadius: '4px',
                  background: '#e0e0e0',
                  mr: 1
                }}
              />
              <Box>
                <Typography variant="caption" sx={{ fontWeight: 600, fontSize: '8px', display: 'block' }}>
                  John Doe
                </Typography>
                <Typography variant="caption" sx={{ color: 'text.secondary', fontSize: '6px', display: 'block' }}>
                  Kebele: Azezo 03
                </Typography>
                <Typography variant="caption" sx={{ color: 'text.secondary', fontSize: '6px', display: 'block' }}>
                  ID: GDR-12345678
                </Typography>
              </Box>
            </Box>
          </Box>

          <Box
            sx={{
              position: 'absolute',
              bottom: 0,
              left: 0,
              width: '180px',
              height: '100px',
              borderRadius: '10px',
              background: 'rgba(255,255,255,0.9)',
              boxShadow: '0 10px 20px rgba(0,0,0,0.1)',
              transform: 'rotate(-8deg)',
              overflow: 'hidden'
            }}
          >
            <Box
              sx={{
                height: '30px',
                background: '#5c6bc0',
                display: 'flex',
                alignItems: 'center',
                px: 2
              }}
            >
              <Typography variant="caption" sx={{ color: 'white', fontWeight: 600 }}>
                GONDAR CITY ID
              </Typography>
            </Box>
            <Box sx={{ p: 1, display: 'flex' }}>
              <Box
                sx={{
                  width: '40px',
                  height: '50px',
                  borderRadius: '4px',
                  background: '#e0e0e0',
                  mr: 1
                }}
              />
              <Box>
                <Typography variant="caption" sx={{ fontWeight: 600, fontSize: '8px', display: 'block' }}>
                  Jane Smith
                </Typography>
                <Typography variant="caption" sx={{ color: 'text.secondary', fontSize: '6px', display: 'block' }}>
                  Subcity: Maraki
                </Typography>
                <Typography variant="caption" sx={{ color: 'text.secondary', fontSize: '6px', display: 'block' }}>
                  ID: GDR-87654321
                </Typography>
              </Box>
            </Box>
          </Box>
        </Box>
      </PageBanner>

      <Container maxWidth="lg" sx={{ mt: -6, mb: 6, position: 'relative', zIndex: 10 }}>
        <Grid container spacing={2} sx={{ mb: 4 }}>
          {getDashboardCards().map((card, index) => (
            <Grid item xs={12} sm={6} md={4} key={index}>
              <DashboardCard {...card} />
            </Grid>
          ))}
        </Grid>

        <Box sx={{ mt: 4 }}>
          <Paper
            sx={{
              p: 3,
              borderRadius: 3,
              boxShadow: '0 4px 12px rgba(0,0,0,0.1)',
              overflow: 'hidden',
              position: 'relative',
              mb: 3
            }}
          >
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 3, justifyContent: 'center' }}>
              <AssessmentIcon sx={{ mr: 1.5, color: theme.palette.primary.main }} />
              <Typography variant="h5" fontWeight="600">
                Quick Stats
              </Typography>
            </Box>

            <Grid container spacing={2} sx={{ justifyContent: 'space-between' }}>
              <Grid item xs={12} sm={4} md={4}>
                <Paper
                  elevation={0}
                  sx={{
                    textAlign: 'center',
                    p: 3,
                    borderRadius: 2,
                    bgcolor: `${theme.palette.primary.main}10`,
                    border: `1px solid ${theme.palette.primary.main}20`,
                    boxShadow: '0 4px 12px rgba(0,0,0,0.05)',
                    transition: 'transform 0.3s, box-shadow 0.3s',
                    '&:hover': {
                      transform: 'translateY(-5px)',
                      boxShadow: '0 8px 15px rgba(0,0,0,0.1)'
                    },
                    height: '100%',
                    minHeight: '150px',
                    display: 'flex',
                    flexDirection: 'column',
                    justifyContent: 'center'
                  }}
                >
                  <Typography variant="h3" color="primary" fontWeight="bold">0</Typography>
                  <Typography variant="body1" color="text.secondary" sx={{ mt: 1 }}>
                    Citizens Registered
                  </Typography>
                </Paper>
              </Grid>
              <Grid item xs={12} sm={4} md={4}>
                <Paper
                  elevation={0}
                  sx={{
                    textAlign: 'center',
                    p: 3,
                    borderRadius: 2,
                    bgcolor: `${theme.palette.secondary.main}10`,
                    border: `1px solid ${theme.palette.secondary.main}20`,
                    boxShadow: '0 4px 12px rgba(0,0,0,0.05)',
                    transition: 'transform 0.3s, box-shadow 0.3s',
                    '&:hover': {
                      transform: 'translateY(-5px)',
                      boxShadow: '0 8px 15px rgba(0,0,0,0.1)'
                    },
                    height: '100%',
                    minHeight: '150px',
                    display: 'flex',
                    flexDirection: 'column',
                    justifyContent: 'center'
                  }}
                >
                  <Typography variant="h3" color="secondary" fontWeight="bold">0</Typography>
                  <Typography variant="body1" color="text.secondary" sx={{ mt: 1 }}>
                    ID Cards Issued
                  </Typography>
                </Paper>
              </Grid>
              <Grid item xs={12} sm={4} md={4}>
                <Paper
                  elevation={0}
                  sx={{
                    textAlign: 'center',
                    p: 3,
                    borderRadius: 2,
                    bgcolor: '#4caf5010',
                    border: '1px solid #4caf5020',
                    boxShadow: '0 4px 12px rgba(0,0,0,0.05)',
                    transition: 'transform 0.3s, box-shadow 0.3s',
                    '&:hover': {
                      transform: 'translateY(-5px)',
                      boxShadow: '0 8px 15px rgba(0,0,0,0.1)'
                    },
                    height: '100%',
                    minHeight: '150px',
                    display: 'flex',
                    flexDirection: 'column',
                    justifyContent: 'center'
                  }}
                >
                  <Typography variant="h3" sx={{ color: '#4caf50', fontWeight: 'bold' }}>0</Typography>
                  <Typography variant="body1" color="text.secondary" sx={{ mt: 1 }}>
                    Pending Applications
                  </Typography>
                </Paper>
              </Grid>
            </Grid>
          </Paper>
        </Box>
      </Container>
    </Box>
  );
};

export default TenantDashboard;
