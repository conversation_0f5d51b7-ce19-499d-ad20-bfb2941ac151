import os
import django

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'neocamelot.settings')
django.setup()

from django.db import connection
from django_tenants.utils import tenant_context, get_tenant_model

# Function to check schema for a specific tenant
def check_tenant_schema(schema_name):
    print(f"\n=== Database Schema for idcards_idcard in {schema_name} schema ===")
    Client = get_tenant_model()
    try:
        tenant = Client.objects.get(schema_name=schema_name)
        with tenant_context(tenant):
            with connection.cursor() as cursor:
                cursor.execute(f"""
                    SELECT column_name, data_type
                    FROM information_schema.columns
                    WHERE table_name = 'idcards_idcard' AND table_schema = '{schema_name}'
                """)
                columns = cursor.fetchall()
                if not columns:
                    print(f"No columns found in {schema_name} schema")
                else:
                    # Check if document_verification_required exists
                    has_required_column = False
                    for column in columns:
                        if column[0] == 'document_verification_required':
                            has_required_column = True
                            print(f"✅ document_verification_required: {column[1]}")
                        else:
                            print(f"{column[0]}: {column[1]}")

                    if not has_required_column:
                        print("❌ document_verification_required column is MISSING!")
    except Client.DoesNotExist:
        print(f"Tenant '{schema_name}' does not exist")
    except Exception as e:
        print(f"Error: {str(e)}")

# Check the public schema
print("\n=== Database Schema for idcards_idcard in public schema ===")
with connection.cursor() as cursor:
    cursor.execute("""
        SELECT column_name, data_type
        FROM information_schema.columns
        WHERE table_name = 'idcards_idcard' AND table_schema = 'public'
    """)
    columns = cursor.fetchall()
    if not columns:
        print("No columns found in public schema")
    else:
        # Check if document_verification_required exists
        has_required_column = False
        for column in columns:
            if column[0] == 'document_verification_required':
                has_required_column = True
                print(f"✅ document_verification_required: {column[1]}")
            else:
                print(f"{column[0]}: {column[1]}")

        if not has_required_column:
            print("❌ document_verification_required column is MISSING!")

# List all available schemas
print("\n=== Available Schemas ===")
with connection.cursor() as cursor:
    cursor.execute("""
        SELECT schema_name
        FROM information_schema.schemata
        WHERE schema_name NOT LIKE 'pg_%' AND schema_name != 'information_schema'
    """)
    schemas = cursor.fetchall()
    for schema in schemas:
        print(schema[0])

# Get all tenant schemas from the database
from centers.models import Client
tenant_schemas = [client.schema_name for client in Client.objects.exclude(schema_name='public')]
print(f"\nChecking schemas: {tenant_schemas}")
for schema in tenant_schemas:
    check_tenant_schema(schema)
