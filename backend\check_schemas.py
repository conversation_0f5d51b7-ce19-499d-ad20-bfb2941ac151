import os
import django
import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'neocamelot.settings')
django.setup()

# Import models
from centers.models import Client, Domain
from django.conf import settings

# Connect to PostgreSQL
print("\n=== Checking PostgreSQL Schemas ===")
try:
    conn = psycopg2.connect(
        dbname=settings.DATABASES['default']['NAME'],
        user=settings.DATABASES['default']['USER'],
        password=settings.DATABASES['default']['PASSWORD'],
        host=settings.DATABASES['default']['HOST'],
        port=settings.DATABASES['default']['PORT']
    )
    conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
    cursor = conn.cursor()
    
    # Get all schemas
    cursor.execute("SELECT schema_name FROM information_schema.schemata;")
    schemas = cursor.fetchall()
    print(f"Total schemas in database: {len(schemas)}")
    for schema in schemas:
        print(f"- {schema[0]}")
    
    # Check if our tenant schemas exist
    tenants = Client.objects.all()
    print("\n=== Checking Tenant Schemas ===")
    for tenant in tenants:
        schema_exists = any(schema[0] == tenant.schema_name for schema in schemas)
        print(f"Tenant: {tenant.name}")
        print(f"Schema Name: {tenant.schema_name}")
        print(f"Schema Type: {tenant.schema_type}")
        print(f"Schema Exists: {schema_exists}")
        print("-" * 30)
    
    # Close connection
    cursor.close()
    conn.close()
    
except Exception as e:
    print(f"Error connecting to PostgreSQL: {str(e)}")
