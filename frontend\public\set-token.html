<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Set Token</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            line-height: 1.6;
        }
        h1 {
            color: #333;
        }
        button {
            padding: 10px 15px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 10px 0;
        }
        button:hover {
            background-color: #45a049;
        }
        pre {
            background-color: #f5f5f5;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
        }
        .token-input {
            width: 100%;
            padding: 10px;
            margin: 10px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #f5f5f5;
            font-weight: bold;
        }
        tr:hover {
            background-color: #f9f9f9;
        }
        .search-input {
            width: 100%;
            padding: 10px;
            margin: 10px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        .loading {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 200px;
        }
        .spinner {
            border: 4px solid rgba(0, 0, 0, 0.1);
            width: 36px;
            height: 36px;
            border-radius: 50%;
            border-left-color: #09f;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <h1>Set Token for Testing</h1>

    <div>
        <h2>Dynamic Tenant Selector</h2>
        <p>Select a tenant from the list below to automatically set the token and schema.</p>

        <input type="text" id="searchInput" class="search-input" placeholder="Search tenants...">

        <div id="tenantsContainer">
            <div class="loading">
                <div class="spinner"></div>
            </div>
        </div>
    </div>

    <div>
        <h2>Manual Token Setting</h2>
        <div>
            <label for="token">Token:</label>
            <input type="text" id="token" class="token-input" value="9425ec3171e23a01903152c33f7a212106916a4b">
        </div>
        <div>
            <label for="schema">Schema:</label>
            <input type="text" id="schema" class="token-input" value="subcity_zoble">
        </div>
        <button id="setToken">Set Token</button>
    </div>

    <div>
        <h2>Current Token</h2>
        <pre id="currentToken">Loading...</pre>
    </div>

    <div>
        <h2>Navigation</h2>
        <button id="goToPrintCards">Go to Print Cards</button>
        <button id="goToIDCards">Go to ID Cards</button>
    </div>

    <script>
        // Mock tokens for demonstration - in a real app, these would come from the backend
        const mockTokens = {
            'subcity_zoble': '9425ec3171e23a01903152c33f7a212106916a4b',
            'kebele 14': '5725ebe15e75a87a796254e71110a03cd2244d5c',
            'kebele 15': '6825ebe15e75a87a796254e71110a03cd2244d5e',
            'kebele 16': '7825ebe15e75a87a796254e71110a03cd2244d5d',
            'gondar': '4ca3c3b8cda543a64efacb89c2c4599bd04eda48',
        };

        // Display current token
        function displayCurrentToken() {
            const token = localStorage.getItem('token') || 'No token set';
            const schema = localStorage.getItem('schema_name') || 'No schema set';
            const user = localStorage.getItem('user') || 'No user set';
            const tenant = localStorage.getItem('tenant') || 'No tenant set';
            const persistentLogin = localStorage.getItem('persistentLogin') || 'false';

            let displayText = `Token: ${token}\nSchema: ${schema}\n`;

            // Add user info if available
            try {
                if (user !== 'No user set') {
                    const userObj = JSON.parse(user);
                    displayText += `User: ${userObj.first_name} ${userObj.last_name} (${userObj.role})\n`;
                } else {
                    displayText += `User: Not set\n`;
                }
            } catch (e) {
                displayText += `User: Error parsing\n`;
            }

            // Add tenant info if available
            try {
                if (tenant !== 'No tenant set') {
                    const tenantObj = JSON.parse(tenant);
                    displayText += `Tenant: ${tenantObj.name} (${tenantObj.type})\n`;
                } else {
                    displayText += `Tenant: Not set\n`;
                }
            } catch (e) {
                displayText += `Tenant: Error parsing\n`;
            }

            // Add persistent login status
            displayText += `Persistent Login: ${persistentLogin === 'true' ? 'Enabled' : 'Disabled'}`;

            document.getElementById('currentToken').textContent = displayText;
        }

        // Set token
        document.getElementById('setToken').addEventListener('click', function() {
            const token = document.getElementById('token').value;
            const schema = document.getElementById('schema').value;

            localStorage.setItem('token', token);
            localStorage.setItem('schema_name', schema);

            // Set a persistent flag to prevent redirect
            localStorage.setItem('persistentLogin', 'true');

            // Create and store a mock user object
            const mockUser = {
                id: 1,
                email: '<EMAIL>',
                first_name: 'Test',
                last_name: 'User',
                role: 'ADMIN'
            };
            localStorage.setItem('user', JSON.stringify(mockUser));

            // Find the tenant data if available
            const tenant = window.tenantsData ? window.tenantsData.find(t => t.schema_name === schema) : null;

            // Store the tenant object
            if (tenant) {
                localStorage.setItem('tenant', JSON.stringify({
                    id: tenant.id || 1,
                    name: tenant.name,
                    schema_name: tenant.schema_name,
                    type: tenant.schema_type,
                    parent_id: tenant.parent
                }));
            } else {
                // Create a default tenant object if we don't have tenant data
                localStorage.setItem('tenant', JSON.stringify({
                    id: 1,
                    name: schema,
                    schema_name: schema,
                    type: schema.includes('kebele') ? 'KEBELE' : 'SUBCITY',
                    parent_id: null
                }));
            }

            displayCurrentToken();

            alert(`Token set to: ${token}\nSchema set to: ${schema}`);

            // Add a note about the persistent login
            const noteElement = document.createElement('div');
            noteElement.style.color = 'green';
            noteElement.style.marginTop = '10px';
            noteElement.innerHTML = '<strong>Persistent login enabled!</strong> You can now refresh the page without being logged out.';
            document.getElementById('currentToken').parentNode.appendChild(noteElement);
        });

        // Navigation
        document.getElementById('goToPrintCards').addEventListener('click', function() {
            window.location.href = '/print-cards';
        });

        document.getElementById('goToIDCards').addEventListener('click', function() {
            window.location.href = '/id-cards';
        });

        // Fetch tenants from API
        async function fetchTenants() {
            try {
                const response = await fetch('http://127.0.0.1:8000/api/available-tenants/');

                if (!response.ok) {
                    throw new Error(`API error: ${response.status} ${response.statusText}`);
                }

                const data = await response.json();

                // Add mock tokens to the tenant data
                return data.map(tenant => ({
                    ...tenant,
                    token: mockTokens[tenant.schema_name] || 'no-token-available'
                }));
            } catch (err) {
                console.error('Error fetching tenants:', err);
                document.getElementById('tenantsContainer').innerHTML = `
                    <div style="color: red; padding: 20px;">
                        Failed to fetch tenants. Please try again later.
                    </div>
                `;
                return [];
            }
        }

        // Render tenants table
        function renderTenantsTable(tenants, searchQuery = '') {
            const filteredTenants = tenants.filter(tenant =>
                tenant.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                tenant.schema_name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                tenant.schema_type.toLowerCase().includes(searchQuery.toLowerCase())
            );

            let tableHtml = `
                <table>
                    <thead>
                        <tr>
                            <th>Name</th>
                            <th>Schema Name</th>
                            <th>Type</th>
                            <th>Parent</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
            `;

            if (filteredTenants.length === 0) {
                tableHtml += `
                    <tr>
                        <td colspan="5" style="text-align: center; padding: 20px;">
                            No tenants found matching your search.
                        </td>
                    </tr>
                `;
            } else {
                filteredTenants.forEach(tenant => {
                    tableHtml += `
                        <tr>
                            <td>${tenant.name}</td>
                            <td>${tenant.schema_name}</td>
                            <td>${tenant.schema_type}</td>
                            <td>${tenant.parent || 'None'}</td>
                            <td>
                                <button
                                    onclick="selectTenant('${tenant.schema_name}', '${tenant.token}')"
                                    style="background-color: #3f51b5;"
                                >
                                    Select
                                </button>
                            </td>
                        </tr>
                    `;
                });
            }

            tableHtml += `
                    </tbody>
                </table>
            `;

            document.getElementById('tenantsContainer').innerHTML = tableHtml;
        }

        // Select tenant function
        function selectTenant(schemaName, token) {
            if (token && token !== 'no-token-available') {
                // Find the tenant data
                const tenant = window.tenantsData.find(t => t.schema_name === schemaName);

                // Set token and schema in localStorage
                localStorage.setItem('token', token);
                localStorage.setItem('schema_name', schemaName);

                // Set a persistent flag to prevent redirect
                localStorage.setItem('persistentLogin', 'true');

                // Create and store a mock user object
                const mockUser = {
                    id: 1,
                    email: '<EMAIL>',
                    first_name: 'Test',
                    last_name: 'User',
                    role: 'ADMIN'
                };
                localStorage.setItem('user', JSON.stringify(mockUser));

                // Store the tenant object
                if (tenant) {
                    localStorage.setItem('tenant', JSON.stringify({
                        id: tenant.id || 1,
                        name: tenant.name,
                        schema_name: tenant.schema_name,
                        type: tenant.schema_type,
                        parent_id: tenant.parent
                    }));
                } else {
                    // Create a default tenant object if tenant data is not available
                    localStorage.setItem('tenant', JSON.stringify({
                        id: 1,
                        name: schemaName.charAt(0).toUpperCase() + schemaName.slice(1),
                        schema_name: schemaName,
                        type: schemaName.includes('kebele') ? 'KEBELE' : 'SUBCITY',
                        parent_id: null
                    }));
                }

                // Update the input fields
                document.getElementById('token').value = token;
                document.getElementById('schema').value = schemaName;

                // Update the current token display
                displayCurrentToken();

                // Show success message
                alert(`Logged in to ${schemaName}`);

                // Add a note about the persistent login
                const noteElement = document.createElement('div');
                noteElement.style.color = 'green';
                noteElement.style.marginTop = '10px';
                noteElement.innerHTML = '<strong>Persistent login enabled!</strong> You can now refresh the page without being logged out.';
                document.getElementById('currentToken').parentNode.appendChild(noteElement);
            } else {
                alert(`No token available for ${schemaName}`);
            }
        }

        // Search functionality
        document.getElementById('searchInput').addEventListener('input', function(e) {
            const searchQuery = e.target.value;
            const tenants = window.tenantsData || [];
            renderTenantsTable(tenants, searchQuery);
        });

        // Initialize
        displayCurrentToken();

        // Fetch and render tenants
        (async function() {
            const tenants = await fetchTenants();
            window.tenantsData = tenants; // Store for search functionality
            renderTenantsTable(tenants);
        })();
    </script>
</body>
</html>
