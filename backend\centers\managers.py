from django.db import models
from django.db.models.query import QuerySet

class TenantQuerySet(QuerySet):
    """QuerySet that filters by the current tenant."""

    def for_tenant(self, tenant):
        """Filter queryset by tenant."""
        if tenant:
            # Check if tenant is a FakeTenant or similar non-model object
            if hasattr(tenant, 'id') and not isinstance(tenant, (str, int)):
                return self.filter(center_id=tenant.id)
            return self.filter(center=tenant)
        return self

    def for_subcity(self, subcity):
        """Filter queryset by subcity."""
        if subcity:
            return self.filter(center__subcity=subcity)
        return self

    def for_city(self, city):
        """Filter queryset by city."""
        if city:
            return self.filter(center__subcity__city=city)
        return self

    def current_tenant(self):
        """Filter queryset by the current tenant from thread-local storage."""
        # Get the current tenant from thread-local storage
        from .middleware import get_current_tenant
        tenant = get_current_tenant()

        # Filter by tenant if available
        if tenant:
            return self.for_tenant(tenant)

        # Otherwise, return all records
        return self

class TenantManager(models.Manager):
    """Manager that filters by the current tenant."""

    def get_queryset(self):
        """Return a TenantQuerySet."""
        return TenantQuerySet(self.model, using=self._db)

    def for_tenant(self, tenant):
        """Filter by tenant."""
        return self.get_queryset().for_tenant(tenant)

    def for_subcity(self, subcity):
        """Filter by subcity."""
        return self.get_queryset().for_subcity(subcity)

    def for_city(self, city):
        """Filter by city."""
        return self.get_queryset().for_city(city)

    def current_tenant(self):
        """Filter by the current tenant from thread-local storage."""
        return self.get_queryset().current_tenant()

class TenantAwareManager(TenantManager):
    """Manager that automatically filters by the current tenant."""

    def get_queryset(self):
        """Return a queryset filtered by the current tenant."""
        return super().get_queryset().current_tenant()
