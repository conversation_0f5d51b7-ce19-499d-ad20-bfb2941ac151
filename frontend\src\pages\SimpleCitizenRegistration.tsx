import React, { useState, useEffect } from 'react';
import { Box, Button, Container, TextField, Typography, Grid, MenuItem, CircularProgress } from '@mui/material';
import { format } from 'date-fns';
import { API_BASE_URL } from '../config/apiConfig';
import { getCitizenApiToken } from '../services/citizenAuthService';

const SimpleCitizenRegistration = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [tenant, setTenant] = useState<any>(null);

  // Form data
  const [formData, setFormData] = useState({
    first_name: '',
    middle_name: '',
    last_name: '',
    gender: 'M',
    date_of_birth: format(new Date(), 'yyyy-MM-dd'),
    address: 'Default Address', // Required field
    is_resident: true
  });

  // Load tenant information from localStorage
  useEffect(() => {
    const tenantString = localStorage.getItem('tenant');
    if (tenantString) {
      try {
        const parsedTenant = JSON.parse(tenantString);
        setTenant(parsedTenant);
        console.log('Loaded tenant from localStorage:', parsedTenant);
      } catch (error) {
        console.error('Error parsing tenant from localStorage:', error);
      }
    }
  }, []);

  // Handle form input changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);
    setSuccess(false);

    try {
      // Create FormData object
      const formDataToSend = new FormData();

      // Basic Information
      formDataToSend.append('first_name', formData.first_name);
      formDataToSend.append('middle_name', formData.middle_name || '');
      formDataToSend.append('last_name', formData.last_name);
      formDataToSend.append('gender', formData.gender);
      formDataToSend.append('date_of_birth', formData.date_of_birth);

      // Add required fields with default values
      formDataToSend.append('religion', '1'); // Default to Orthodox
      formDataToSend.append('citizen_status', '1'); // Default to Active
      formDataToSend.append('marital_status', '1'); // Default to Single
      formDataToSend.append('nationality', 'Ethiopian');
      formDataToSend.append('nationality_country', '1'); // Default to Ethiopia
      formDataToSend.append('is_resident', formData.is_resident.toString());
      formDataToSend.append('address', formData.address || '');
      formDataToSend.append('employment', 'false');

      // Add center field - this is required by the backend
      if (tenant?.id) {
        console.log('Using center ID from tenant:', tenant.id);
        formDataToSend.append('center', tenant.id.toString());
      } else {
        throw new Error('No valid center ID available. Please contact support.');
      }

      // Add subcity and kebele fields
      const subcityId = tenant?.parent_id;
      const kebeleId = tenant?.id;

      if (subcityId) {
        formDataToSend.append('subcity', subcityId.toString());
      }

      if (kebeleId) {
        formDataToSend.append('kebele', kebeleId.toString());
      }

      // Get schema name
      const schema = tenant?.schema_name || '';
      console.log('Using schema name:', schema);

      // Construct URL
      const url = `${API_BASE_URL}/api/tenant/${encodeURIComponent(schema)}/citizens/`;
      console.log('Submitting to URL:', url);

      // Get CSRF token
      const csrftoken = document.cookie
        .split('; ')
        .find(row => row.startsWith('csrftoken='))
        ?.split('=')[1] || '';

      // Get authentication token
      const token = await getCitizenApiToken();
      if (!token) {
        throw new Error('Authentication failed. Please log in again.');
      }

      // Make API call
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'X-CSRFToken': csrftoken,
          'Authorization': token.startsWith('eyJ') ? `Bearer ${token}` : `Token ${token}`
        },
        credentials: 'include',
        body: formDataToSend
      });

      console.log('Response status:', response.status);

      if (!response.ok) {
        // Try to get error details
        const responseClone = response.clone();
        try {
          const errorData = await response.json();
          console.error('Error data:', errorData);
          throw new Error(JSON.stringify(errorData));
        } catch (jsonError) {
          const responseText = await responseClone.text();
          console.error('Response text:', responseText);
          throw new Error(`HTTP error ${response.status}: ${responseText || response.statusText}`);
        }
      }

      // Success
      const responseData = await response.json();
      console.log('API call succeeded:', responseData);
      setSuccess(true);

    } catch (error) {
      console.error('Error registering citizen:', error);
      setError(error instanceof Error ? error.message : 'An unknown error occurred');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Container maxWidth="md">
      <Box sx={{ my: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          Simple Citizen Registration
        </Typography>

        {error && (
          <Box sx={{ mb: 2, p: 2, bgcolor: 'error.light', borderRadius: 1 }}>
            <Typography color="error">{error}</Typography>
          </Box>
        )}

        {success && (
          <Box sx={{ mb: 2, p: 2, bgcolor: 'success.light', borderRadius: 1 }}>
            <Typography color="success.dark">Citizen registered successfully!</Typography>
          </Box>
        )}

        <form onSubmit={handleSubmit}>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={4}>
              <TextField
                fullWidth
                required
                label="First Name"
                name="first_name"
                value={formData.first_name}
                onChange={handleChange}
              />
            </Grid>
            <Grid item xs={12} sm={4}>
              <TextField
                fullWidth
                label="Middle Name"
                name="middle_name"
                value={formData.middle_name}
                onChange={handleChange}
              />
            </Grid>
            <Grid item xs={12} sm={4}>
              <TextField
                fullWidth
                required
                label="Last Name"
                name="last_name"
                value={formData.last_name}
                onChange={handleChange}
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                required
                select
                label="Gender"
                name="gender"
                value={formData.gender}
                onChange={handleChange}
              >
                <MenuItem value="M">Male</MenuItem>
                <MenuItem value="F">Female</MenuItem>
              </TextField>
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                required
                type="date"
                label="Date of Birth"
                name="date_of_birth"
                value={formData.date_of_birth}
                onChange={handleChange}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>

            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Address"
                name="address"
                value={formData.address}
                onChange={handleChange}
                multiline
                rows={2}
              />
            </Grid>

            <Grid item xs={12}>
              <Button
                type="submit"
                variant="contained"
                color="primary"
                disabled={loading}
                startIcon={loading ? <CircularProgress size={20} /> : null}
              >
                {loading ? 'Registering...' : 'Register Citizen'}
              </Button>
            </Grid>
          </Grid>
        </form>
      </Box>
    </Container>
  );
};

export default SimpleCitizenRegistration;
