/**
 * API Client
 *
 * This module provides a tenant-aware API client for making requests to the backend.
 * It automatically includes the correct tenant headers and handles authentication.
 */

import { getAccessTokenForSchema, getCurrentSchema } from '../services/tokenService';

// Base API URL
const API_BASE_URL = '/api';

// API client options
interface ApiClientOptions {
  baseUrl?: string;
  defaultHeaders?: Record<string, string>;
}

// API client class
class ApiClient {
  private baseUrl: string;
  private defaultHeaders: Record<string, string>;

  constructor(options: ApiClientOptions = {}) {
    this.baseUrl = options.baseUrl || API_BASE_URL;
    this.defaultHeaders = options.defaultHeaders || {
      'Content-Type': 'application/json',
      'Accept': 'application/json'
    };
  }

  /**
   * Make a request to the API
   * @param endpoint The API endpoint
   * @param options The fetch options
   * @returns The response
   */
  async request<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
    // Get the current schema
    const schema = getCurrentSchema();

    // Get the token for the current schema
    const token = schema ? getAccessTokenForSchema(schema) : null;

    // Create headers
    const headers = new Headers(this.defaultHeaders);

    // Add any custom headers from options
    if (options.headers) {
      Object.entries(options.headers).forEach(([key, value]) => {
        headers.set(key, value);
      });
    }

    // Add the token to the headers if available
    if (token) {
      headers.set('Authorization', `Bearer ${token}`);
    }

    // Add the schema name to the headers if available
    if (schema) {
      headers.set('X-Schema-Name', schema);

      // Also set the schema name as a cookie for backend compatibility
      document.cookie = `schema_name=${encodeURIComponent(schema)}; path=/; SameSite=Lax`;
    }

    // Create the fetch options with the headers
    const fetchOptions: RequestInit = {
      ...options,
      headers,
      credentials: 'include'
    };

    // Make the request
    const url = `${this.baseUrl}/${endpoint.startsWith('/') ? endpoint.slice(1) : endpoint}`;
    const response = await fetch(url, fetchOptions);

    // Handle errors
    if (!response.ok) {
      let errorMessage = `API error: ${response.status} ${response.statusText}`;
      try {
        const errorData = await response.json();
        errorMessage = errorData.error || errorData.detail || errorMessage;
      } catch (e) {
        // If we can't parse the error as JSON, just use the status text
      }
      throw new Error(errorMessage);
    }

    // Parse the response
    try {
      return await response.json();
    } catch (e) {
      // If the response is not JSON, return an empty object
      return {} as T;
    }
  }

  /**
   * Make a GET request to the API
   * @param endpoint The API endpoint
   * @param params The query parameters
   * @param options The fetch options
   * @returns The response
   */
  async get<T>(endpoint: string, params: Record<string, string> = {}, options: RequestInit = {}): Promise<T> {
    // Add query parameters to the endpoint
    const queryParams = new URLSearchParams(params).toString();
    const url = queryParams ? `${endpoint}?${queryParams}` : endpoint;

    // Make the request
    return this.request<T>(url, {
      method: 'GET',
      ...options
    });
  }

  /**
   * Make a POST request to the API
   * @param endpoint The API endpoint
   * @param data The request body
   * @param options The fetch options
   * @returns The response
   */
  async post<T>(endpoint: string, data: any, options: RequestInit = {}): Promise<T> {
    // Make the request
    return this.request<T>(endpoint, {
      method: 'POST',
      body: JSON.stringify(data),
      ...options
    });
  }

  /**
   * Make a PUT request to the API
   * @param endpoint The API endpoint
   * @param data The request body
   * @param options The fetch options
   * @returns The response
   */
  async put<T>(endpoint: string, data: any, options: RequestInit = {}): Promise<T> {
    // Make the request
    return this.request<T>(endpoint, {
      method: 'PUT',
      body: JSON.stringify(data),
      ...options
    });
  }

  /**
   * Make a PATCH request to the API
   * @param endpoint The API endpoint
   * @param data The request body
   * @param options The fetch options
   * @returns The response
   */
  async patch<T>(endpoint: string, data: any, options: RequestInit = {}): Promise<T> {
    // Make the request
    return this.request<T>(endpoint, {
      method: 'PATCH',
      body: JSON.stringify(data),
      ...options
    });
  }

  /**
   * Make a DELETE request to the API
   * @param endpoint The API endpoint
   * @param options The fetch options
   * @returns The response
   */
  async delete<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
    // Make the request
    return this.request<T>(endpoint, {
      method: 'DELETE',
      ...options
    });
  }

  /**
   * Upload a file to the API
   * @param endpoint The API endpoint
   * @param file The file to upload
   * @param fieldName The name of the file field
   * @param additionalData Additional form data
   * @param options The fetch options
   * @returns The response
   */
  async uploadFile<T>(
    endpoint: string,
    file: File,
    fieldName: string = 'file',
    additionalData: Record<string, string> = {},
    options: RequestInit = {}
  ): Promise<T> {
    // Create a FormData object
    const formData = new FormData();
    formData.append(fieldName, file);

    // Add any additional data
    Object.entries(additionalData).forEach(([key, value]) => {
      formData.append(key, value);
    });

    // Make the request
    return this.request<T>(endpoint, {
      method: 'POST',
      body: formData,
      headers: {}, // Let the browser set the Content-Type header with the boundary
      ...options
    });
  }
}

// Create and export a default instance
export const apiClient = new ApiClient();

// Export the class for custom instances
export default ApiClient;
