import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Typography,
  Button,
  Paper,
  List,
  ListItem,
  ListItemText,
  Divider,
  CircularProgress,
  Alert,
} from '@mui/material';
import { getCurrentSchema, isTokenRefreshInProgress } from '../services/tokenService';
import { tenantApi } from '../services/api';

interface TestResult {
  id: number;
  endpoint: string;
  status: 'pending' | 'success' | 'error';
  message: string;
  startTime: number;
  endTime?: number;
}

const TokenRaceTest: React.FC = () => {
  const [results, setResults] = useState<TestResult[]>([]);
  const [isRunning, setIsRunning] = useState(false);
  const [schema, setSchema] = useState<string | null>(null);
  const [refreshInProgress, setRefreshInProgress] = useState(false);

  // Get the current schema
  useEffect(() => {
    const currentSchema = getCurrentSchema();
    setSchema(currentSchema);
  }, []);

  // Check if token refresh is in progress
  useEffect(() => {
    const checkRefreshStatus = () => {
      if (schema) {
        const inProgress = isTokenRefreshInProgress(schema);
        setRefreshInProgress(inProgress);

        // Log debug info to console
        if ((window as any).tokenDebug) {
          console.log('Token refresh status:', (window as any).tokenDebug.getRefreshStatus());
        }
      }
    };

    // Check initially
    checkRefreshStatus();

    // Set up interval to check regularly
    const interval = setInterval(checkRefreshStatus, 500);

    return () => clearInterval(interval);
  }, [schema]);

  // Make a single API request
  const makeRequest = async (endpoint: string, id: number): Promise<void> => {
    if (!schema) {
      console.error('No schema available for API request');
      return;
    }

    // Add result to the list
    setResults(prev => [
      ...prev,
      {
        id,
        endpoint,
        status: 'pending',
        message: 'Request started...',
        startTime: Date.now(),
      }
    ]);

    try {
      // Make the API request
      await tenantApi.get(schema, endpoint);

      // Update result on success
      setResults(prev => prev.map(result =>
        result.id === id
          ? {
              ...result,
              status: 'success',
              message: 'Request successful',
              endTime: Date.now()
            }
          : result
      ));
    } catch (error) {
      // Update result on error
      setResults(prev => prev.map(result =>
        result.id === id
          ? {
              ...result,
              status: 'error',
              message: `Error: ${error.message}`,
              endTime: Date.now()
            }
          : result
      ));
    }
  };

  // Run the race condition test
  const runTest = async () => {
    setIsRunning(true);
    setResults([]);

    // Define endpoints to test
    const endpoints = [
      'citizens/?detail=true',
      'citizens/?detail=true&page=1',
      'citizens/?detail=true&page=2',
      'citizens/count/',
      'id-cards/',
      'id-cards/count/',
      'users/me/',
      'users/list/',
      'settings/',
      'documents/types/'
    ];

    // Make multiple requests simultaneously
    const requests = endpoints.map((endpoint, index) =>
      makeRequest(endpoint, index + 1)
    );

    // Wait for all requests to complete
    await Promise.allSettled(requests);

    setIsRunning(false);
  };

  // Calculate test statistics
  const getStats = () => {
    const completed = results.filter(r => r.status !== 'pending');
    const successful = results.filter(r => r.status === 'success');
    const failed = results.filter(r => r.status === 'error');

    const durations = completed.map(r => (r.endTime || 0) - r.startTime);
    const avgDuration = durations.length > 0
      ? durations.reduce((sum, d) => sum + d, 0) / durations.length
      : 0;

    return {
      total: results.length,
      completed: completed.length,
      successful: successful.length,
      failed: failed.length,
      avgDuration: Math.round(avgDuration)
    };
  };

  const stats = getStats();

  return (
    <Container maxWidth="md">
      <Paper sx={{ p: 3, my: 3 }}>
        <Typography variant="h4" gutterBottom>
          Token Race Condition Test
        </Typography>

        <Typography variant="body1" paragraph>
          This test makes multiple API requests simultaneously to verify if the race condition
          in token refresh has been fixed.
        </Typography>

        <Box sx={{ mb: 3 }}>
          <Typography variant="subtitle1">
            Current Schema: {schema || 'None'}
          </Typography>
          <Typography variant="subtitle1">
            Token Refresh In Progress: {refreshInProgress ? 'Yes' : 'No'}
          </Typography>
        </Box>

        <Box sx={{ mb: 3 }}>
          <Button
            variant="contained"
            color="primary"
            onClick={runTest}
            disabled={isRunning || !schema}
            sx={{ mr: 2 }}
          >
            {isRunning ? <CircularProgress size={24} /> : 'Run Test'}
          </Button>

          <Button
            variant="outlined"
            color="info"
            onClick={() => (window as any).tokenDebug?.diagnose()}
            sx={{ mr: 2 }}
          >
            Run Diagnostics
          </Button>
        </Box>

        {results.length > 0 && (
          <Box sx={{ mb: 3 }}>
            <Typography variant="h6" gutterBottom>
              Test Statistics
            </Typography>
            <Typography>
              Total Requests: {stats.total}
            </Typography>
            <Typography>
              Completed: {stats.completed} / {stats.total}
            </Typography>
            <Typography>
              Successful: {stats.successful} / {stats.completed}
            </Typography>
            <Typography>
              Failed: {stats.failed} / {stats.completed}
            </Typography>
            <Typography>
              Average Duration: {stats.avgDuration}ms
            </Typography>
          </Box>
        )}

        {results.length > 0 && (
          <>
            <Typography variant="h6" gutterBottom>
              Test Results
            </Typography>
            <List>
              {results.map((result) => (
                <React.Fragment key={result.id}>
                  <ListItem>
                    <ListItemText
                      primary={`#${result.id}: ${result.endpoint}`}
                      secondary={
                        <>
                          <Typography component="span" variant="body2" color="text.primary">
                            Status: {result.status.toUpperCase()}
                          </Typography>
                          <br />
                          {result.message}
                          <br />
                          {result.endTime ? (
                            `Duration: ${result.endTime - result.startTime}ms`
                          ) : (
                            'In progress...'
                          )}
                        </>
                      }
                    />
                  </ListItem>
                  <Divider />
                </React.Fragment>
              ))}
            </List>
          </>
        )}
      </Paper>
    </Container>
  );
};

export default TokenRaceTest;
