/**
 * API URL Fixer
 *
 * This utility provides functions to fix API URL issues in the frontend.
 * It detects and corrects URLs that are incorrectly pointing to the frontend server.
 */

import { API_BASE_URL } from '../config/apiConfig';

// The backend server URL
const BACKEND_URL = API_BASE_URL;

// The frontend server URL patterns to detect
const FRONTEND_URL_PATTERNS = [
  'localhost:5173',
  '127.0.0.1:5173',
];

/**
 * Fix an API URL to ensure it points to the backend server
 * @param url The URL to fix
 * @returns The fixed URL
 */
export const fixApiUrl = (url: string): string => {
  // If the URL is already absolute and points to the backend, return it as is
  if (url.startsWith(BACKEND_URL)) {
    return url;
  }

  // If the URL is absolute and points to the frontend, replace the frontend URL with the backend URL
  if (url.startsWith(FRONTEND_URL)) {
    return url.replace(FRONTEND_URL, BACKEND_URL);
  }

  // If the URL is relative and starts with /api, prepend the backend URL
  if (url.startsWith('/api')) {
    return `${BACKEND_URL}${url}`;
  }

  // If the URL is relative but doesn't start with /api, prepend the backend URL and /api
  if (!url.startsWith('http')) {
    return `${BACKEND_URL}/api/${url.startsWith('/') ? url.substring(1) : url}`;
  }

  // If none of the above conditions are met, return the URL as is
  return url;
};

/**
 * Fix a tenant-specific API URL to ensure it points to the backend server
 * @param schema The tenant schema
 * @param endpoint The API endpoint
 * @returns The fixed URL
 */
export const fixTenantApiUrl = (schema: string, endpoint: string): string => {
  // Normalize the endpoint (remove leading /api/ if present)
  const normalizedEndpoint = endpoint.replace(/^\/api\//, '');

  // Create the tenant-specific URL
  return `${BACKEND_URL}/api/tenant/${schema}/${normalizedEndpoint}`;
};

/**
 * Detect if a URL is incorrectly pointing to the frontend server
 * @param url The URL to check
 * @returns True if the URL is incorrectly pointing to the frontend server
 */
export const isIncorrectApiUrl = (url: string): boolean => {
  for (const pattern of FRONTEND_URL_PATTERNS) {
    if (url.includes(pattern)) {
      return true;
    }
  }
  return false;
};

/**
 * Patch the fetch API to automatically fix API URLs
 */
export const patchFetch = (): void => {
  const originalFetch = window.fetch;

  window.fetch = function(input: RequestInfo | URL, init?: RequestInit): Promise<Response> {
    // If input is a string and contains the frontend URL with /api, fix it
    if (typeof input === 'string' && isIncorrectApiUrl(input)) {
      console.warn(`Fixing incorrect API URL: ${input}`);
      input = fixApiUrl(input);
      console.log(`Fixed API URL: ${input}`);
    }

    return originalFetch.call(window, input, init);
  };

  console.log('Patched fetch API to automatically fix API URLs');
};

export default {
  fixApiUrl,
  fixTenantApiUrl,
  isIncorrectApiUrl,
  patchFetch
};
