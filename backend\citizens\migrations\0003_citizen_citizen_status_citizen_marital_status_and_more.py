# Generated by Django 5.1.7 on 2025-04-17 15:13

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('centers', '0011_citizenstatus_maritalstatus_religion'),
        ('citizens', '0002_alter_citizen_center'),
    ]

    operations = [
        migrations.AddField(
            model_name='citizen',
            name='citizen_status',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='centers.citizenstatus'),
        ),
        migrations.AddField(
            model_name='citizen',
            name='marital_status',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='centers.maritalstatus'),
        ),
        migrations.AddField(
            model_name='citizen',
            name='religion',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='centers.religion'),
        ),
        migrations.AlterField(
            model_name='citizen',
            name='gender',
            field=models.CharField(choices=[('M', 'Male'), ('F', 'Female')], max_length=1),
        ),
    ]
