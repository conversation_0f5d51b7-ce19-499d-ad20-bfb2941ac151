import os
import django
import requests
import json

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'neocamelot.settings')
django.setup()

from django.contrib.auth import get_user_model
from centers.models import Client, Domain
from django_tenants.utils import tenant_context, schema_context

User = get_user_model()

def test_schema_tenants():
    """Test the schema-based multi-tenancy."""
    print("Testing schema-based multi-tenancy...")
    
    # Get all tenants
    tenants = Client.objects.all()
    print(f"Found {tenants.count()} tenants")
    
    # Test each tenant schema
    for tenant in tenants:
        print(f"\nTesting tenant: {tenant.name} (schema: {tenant.schema_name})")
        
        # Use tenant_context to switch to the tenant's schema
        with tenant_context(tenant):
            # Get users in this schema
            users = User.objects.all()
            print(f"  Found {users.count()} users in {tenant.name} schema")
            
            # Get center types in this schema
            from centers.models import CenterType
            center_types = CenterType.objects.all()
            print(f"  Found {center_types.count()} center types in {tenant.name} schema")
            
            # If this is a center schema, check for citizens
            if tenant.schema_type == 'CENTER':
                from citizens.models import Citizen
                citizens = Citizen.objects.all()
                print(f"  Found {citizens.count()} citizens in {tenant.name} schema")
                
                # Create a test citizen
                if citizens.count() == 0:
                    try:
                        citizen = Citizen.objects.create(
                            first_name="Test",
                            last_name="Citizen",
                            gender="M",
                            date_of_birth="1990-01-01",
                            phone="1234567890",
                            email="<EMAIL>",
                            address="Test Address",
                            id_number="TEST123456"
                        )
                        print(f"  Created test citizen in {tenant.name} schema")
                    except Exception as e:
                        print(f"  Error creating test citizen: {str(e)}")

def test_api_access():
    """Test API access with different domains."""
    print("\nTesting API access with different domains...")
    
    # Base URL
    base_url = 'http://localhost:8000'
    
    # Get all domains
    domains = Domain.objects.all()
    
    # Test public domain
    print("\nTesting public domain...")
    response = requests.get(f"{base_url}/api/cities/")
    if response.status_code == 200:
        data = response.json()
        print(f"  Found {len(data)} cities in public domain")
    else:
        print(f"  Error accessing cities API: {response.status_code}")
    
    # Login as super admin
    print("\nLogging in as super admin...")
    login_data = {
        'email': '<EMAIL>',
        'password': 'admin123'
    }
    response = requests.post(f"{base_url}/api/login/", json=login_data)
    if response.status_code == 200:
        token = response.json().get('token')
        print(f"  Successfully logged in as super admin")
        
        # Test accessing cities API with token
        headers = {'Authorization': f'Token {token}'}
        response = requests.get(f"{base_url}/api/cities/", headers=headers)
        if response.status_code == 200:
            data = response.json()
            print(f"  Found {len(data)} cities with super admin token")
        else:
            print(f"  Error accessing cities API with token: {response.status_code}")
    else:
        print(f"  Error logging in as super admin: {response.status_code}")

if __name__ == '__main__':
    test_schema_tenants()
    test_api_access()
