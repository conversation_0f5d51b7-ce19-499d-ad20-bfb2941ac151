import os
import django

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'neocamelot.settings')
django.setup()

# Import models and admin
from django.contrib import admin
from citizens.models import Citizen
from idcards.models import IDCard, IDCardTemplate

# Define admin classes if they don't exist
class CitizenAdmin(admin.ModelAdmin):
    list_display = ('registration_number', 'first_name', 'last_name', 'gender', 'date_of_birth',
                    'center', 'is_active', 'created_at')
    list_filter = ('is_active', 'gender', 'center', 'nationality')
    search_fields = ('first_name', 'last_name', 'registration_number', 'id_number', 'email', 'phone')
    readonly_fields = ('registration_number', 'id_number', 'created_at', 'updated_at', 'created_by')

    fieldsets = (
        ('Basic Information', {
            'fields': ('center', 'registration_number', 'id_number', 'first_name', 'last_name',
                      'date_of_birth', 'gender', 'nationality')
        }),
        ('Contact Information', {
            'fields': ('address', 'phone', 'email')
        }),
        ('Additional Information', {
            'fields': ('occupation', 'photo')
        }),
        ('Status', {
            'fields': ('is_active',)
        }),
        ('System Information', {
            'fields': ('created_at', 'updated_at', 'created_by'),
            'classes': ('collapse',)
        }),
    )

class IDCardTemplateAdmin(admin.ModelAdmin):
    list_display = ('name', 'center', 'is_default', 'created_at')
    list_filter = ('center', 'is_default')
    search_fields = ('name', 'center__name')
    readonly_fields = ('created_at', 'updated_at')

    fieldsets = (
        (None, {
            'fields': ('name', 'center', 'is_default', 'background_image')
        }),
        ('Layout Configuration', {
            'fields': ('front_layout', 'back_layout'),
            'classes': ('collapse',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

class IDCardAdmin(admin.ModelAdmin):
    list_display = ('card_number', 'citizen', 'template', 'issue_date', 'expiry_date', 'status')
    list_filter = ('status', 'issue_date', 'citizen__center')
    search_fields = ('card_number', 'citizen__first_name', 'citizen__last_name', 'citizen__registration_number')
    readonly_fields = ('card_number', 'created_at', 'updated_at', 'created_by', 'approved_by')

    fieldsets = (
        ('Card Information', {
            'fields': ('citizen', 'template', 'card_number', 'issue_date', 'expiry_date', 'status')
        }),
        ('Card Data', {
            'fields': ('card_data',),
            'classes': ('collapse',)
        }),
        ('System Information', {
            'fields': ('created_at', 'updated_at', 'created_by', 'approved_by'),
            'classes': ('collapse',)
        }),
    )

# Check if models are already registered
registered_models = [model.__name__ for model, _ in admin.site._registry.items()]
print(f"Currently registered models: {registered_models}")

# Register models if not already registered
if 'Citizen' not in registered_models:
    try:
        admin.site.register(Citizen, CitizenAdmin)
        print("Registered Citizen model in admin")
    except admin.sites.AlreadyRegistered:
        print("Citizen model already registered")

if 'IDCardTemplate' not in registered_models:
    try:
        admin.site.register(IDCardTemplate, IDCardTemplateAdmin)
        print("Registered IDCardTemplate model in admin")
    except admin.sites.AlreadyRegistered:
        print("IDCardTemplate model already registered")

if 'IDCard' not in registered_models:
    try:
        admin.site.register(IDCard, IDCardAdmin)
        print("Registered IDCard model in admin")
    except admin.sites.AlreadyRegistered:
        print("IDCard model already registered")

# Check registration again
registered_models = [model.__name__ for model, _ in admin.site._registry.items()]
print(f"Updated registered models: {registered_models}")
