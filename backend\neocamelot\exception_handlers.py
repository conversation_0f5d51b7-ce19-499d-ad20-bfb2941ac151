"""
Custom exception handlers for the API.
"""

import logging
from rest_framework.views import exception_handler
from rest_framework.exceptions import AuthenticationFailed, NotAuthenticated
from rest_framework.response import Response
from rest_framework import status

# Configure logging
logger = logging.getLogger(__name__)

def custom_exception_handler(exc, context):
    """
    Custom exception handler for the API.
    
    This handler allows unauthenticated access to endpoints with the 'detail=true' parameter.
    """
    # Get the request from the context
    request = context.get('request')
    
    # Check if the request has a 'detail' query parameter set to 'true'
    # or if 'detail=true' is in the URL path
    has_detail_param = False
    if request:
        has_detail_param = (
            request.query_params.get('detail') == 'true' or 
            'detail=true' in request.get_full_path()
        )
    
    # If this is an authentication error and the detail=true parameter is present,
    # allow the request to proceed
    if has_detail_param and (isinstance(exc, AuthenticationFailed) or isinstance(exc, NotAuthenticated)):
        logger.info(f"Bypassing authentication for request with detail=true: {request.path}")
        
        # Return a 200 OK response with empty data
        # The view will handle the actual response
        return None
    
    # Call the default exception handler
    response = exception_handler(exc, context)
    
    # If the response is None, return None
    if response is None:
        return None
    
    # Add CORS headers for authentication errors
    if isinstance(exc, AuthenticationFailed) or isinstance(exc, NotAuthenticated):
        # Add CORS headers
        if request:
            origin = request.headers.get('Origin', '*')
            response['Access-Control-Allow-Origin'] = origin
            response['Access-Control-Allow-Methods'] = 'GET, POST, PUT, PATCH, DELETE, OPTIONS'
            response['Access-Control-Allow-Headers'] = 'Content-Type, Authorization, X-Requested-With, X-Schema-Name'
            response['Access-Control-Allow-Credentials'] = 'true'
            response['Access-Control-Max-Age'] = '86400'  # 24 hours
    
    return response
