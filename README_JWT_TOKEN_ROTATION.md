# JWT Token Rotation Issue

This document explains the JWT token rotation issue and provides solutions to address it.

## Background

The system uses JWT token rotation for security, which means:

1. When a refresh token is used to get a new access token, the old refresh token is blacklisted
2. A new refresh token is issued along with the new access token
3. This prevents refresh token reuse attacks

The issue is that refresh tokens are being immediately blacklisted after they're created, which is causing authentication failures. This is evident by the large number of tokens in the `accounts_blacklistedtoken` table.

## Symptoms

- Authentication errors when submitting forms
- "Your session has expired" messages
- Empty `authtoken_token` table but many entries in `accounts_blacklistedtoken`
- API calls returning 401 Unauthorized errors

## Solution 1: Clean Up Blacklisted Tokens

We've created a script to clean up blacklisted tokens in the `accounts_blacklistedtoken` table. This script can be used to fix authentication issues when tokens accumulate.

### Usage

1. Copy the `cleanup_blacklisted_tokens.py` script to the root directory of your backend project.

2. List all available schemas:

```bash
python cleanup_blacklisted_tokens.py list_schemas
```

3. Count blacklisted tokens in a specific schema:

```bash
python cleanup_blacklisted_tokens.py count <schema_name>
```

Example:
```bash
python cleanup_blacklisted_tokens.py count kebele14
```

4. Clean up all blacklisted tokens in a specific schema:

```bash
python cleanup_blacklisted_tokens.py cleanup <schema_name>
```

Example:
```bash
python cleanup_blacklisted_tokens.py cleanup kebele14
```

5. Clean up tokens older than a specific number of days:

```bash
python cleanup_blacklisted_tokens.py cleanup <schema_name> <days_old>
```

Example:
```bash
python cleanup_blacklisted_tokens.py cleanup kebele14 7
```

6. Clean up tokens in all schemas:

```bash
python cleanup_blacklisted_tokens.py cleanup_all
```

## Solution 2: Enhanced Frontend Token Handling

We've updated the frontend code to handle JWT token rotation issues:

1. Enhanced token handling with multiple fallback mechanisms
2. Automatic token creation when authentication fails
3. Support for both JWT and token-based authentication
4. Proper schema formatting for API URLs

The key changes include:

- Updated `getJWTToken` function to try creating a new token via login if refresh fails
- Updated `createNewToken` function to try both JWT and token-based authentication
- Updated `getEnhancedValidToken` function to use proper schema formatting
- Added validation of existing tokens before using them

## Solution 3: Schedule Regular Token Cleanup

To prevent token accumulation, you should schedule regular cleanup of blacklisted tokens. Django provides a management command for this purpose:

```bash
python manage.py cleanup_blacklisted_tokens
```

You can schedule this command to run daily using a cron job:

```
0 0 * * * cd /path/to/your/project && python manage.py cleanup_blacklisted_tokens
```

## Troubleshooting

If you're still experiencing authentication issues after applying these solutions:

1. Check the database to confirm that tokens were cleaned up:
```sql
-- Connect to the tenant schema
SET search_path TO kebele14;

-- Check blacklisted tokens
SELECT COUNT(*) FROM accounts_blacklistedtoken;
```

2. Check the browser's localStorage and cookies:
   - Open the browser's developer tools
   - Go to the Application tab
   - Check localStorage for tokens
   - Check cookies for JWT tokens

3. Try logging out and logging in again.

4. If all else fails, you can temporarily disable token blacklisting by modifying the `refresh_jwt_token` function in `accounts/jwt_utils.py`:

```python
# Comment out the token blacklisting code
# BlacklistedToken.objects.create(
#     token=refresh_token,
#     user=user,
#     expires_at=expires_at,
#     token_type='refresh'
# )
```

**Note:** This is a temporary solution and should be reverted once the issue is resolved.

## Long-term Solution

The long-term solution is to:

1. Complete the transition to JWT-based authentication
2. Remove the token-based authentication entirely
3. Implement proper token cleanup mechanisms
4. Ensure that the frontend consistently uses the JWT authentication flow

This will prevent conflicts between the two authentication systems and ensure consistent authentication behavior.

## Support

If you continue to experience authentication issues, please contact the development team for assistance.
