# Generated by Django 5.1.7 on 2025-04-17 14:52

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('centers', '0007_client_domain'),
    ]

    operations = [
        migrations.CreateModel(
            name='GlobalCitizen',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('registration_number', models.Char<PERSON>ield(blank=True, max_length=20)),
                ('first_name', models.Char<PERSON>ield(max_length=100)),
                ('last_name', models.Char<PERSON>ield(max_length=100)),
                ('date_of_birth', models.DateField()),
                ('gender', models.Char<PERSON>ield(max_length=1)),
                ('address', models.TextField()),
                ('phone', models.Char<PERSON>ield(blank=True, max_length=20)),
                ('email', models.EmailField(blank=True, max_length=254)),
                ('photo', models.ImageField(blank=True, null=True, upload_to='citizen_photos/')),
                ('id_number', models.CharField(blank=True, max_length=50)),
                ('occupation', models.Char<PERSON>ield(blank=True, max_length=100)),
                ('nationality', models.Char<PERSON>ield(default='Ethiopian', max_length=100)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('tenant_name', models.CharField(blank=True, max_length=100)),
                ('tenant_schema', models.CharField(blank=True, max_length=100)),
                ('tenant_type', models.CharField(blank=True, max_length=10)),
            ],
            options={
                'verbose_name': 'Global Citizen',
                'verbose_name_plural': 'Global Citizens',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='GlobalIDCard',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('card_number', models.CharField(blank=True, max_length=50)),
                ('citizen_name', models.CharField(blank=True, max_length=200)),
                ('issue_date', models.DateField()),
                ('expiry_date', models.DateField(blank=True, null=True)),
                ('status', models.CharField(choices=[('DRAFT', 'Draft'), ('PENDING', 'Pending Approval'), ('APPROVED', 'Approved'), ('PRINTED', 'Printed'), ('ISSUED', 'Issued'), ('EXPIRED', 'Expired'), ('REVOKED', 'Revoked')], default='DRAFT', max_length=20)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('tenant_name', models.CharField(blank=True, max_length=100)),
                ('tenant_schema', models.CharField(blank=True, max_length=100)),
                ('tenant_type', models.CharField(blank=True, max_length=10)),
            ],
            options={
                'verbose_name': 'Global ID Card',
                'verbose_name_plural': 'Global ID Cards',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='GlobalIDCardTemplate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('background_image', models.ImageField(blank=True, null=True, upload_to='id_templates/')),
                ('is_default', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('tenant_name', models.CharField(blank=True, max_length=100)),
                ('tenant_schema', models.CharField(blank=True, max_length=100)),
                ('tenant_type', models.CharField(blank=True, max_length=10)),
            ],
            options={
                'verbose_name': 'Global ID Card Template',
                'verbose_name_plural': 'Global ID Card Templates',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='Citizen',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('registration_number', models.CharField(max_length=20, unique=True)),
                ('first_name', models.CharField(max_length=100)),
                ('middle_name', models.CharField(blank=True, max_length=100, null=True)),
                ('last_name', models.CharField(max_length=100)),
                ('date_of_birth', models.DateField()),
                ('gender', models.CharField(choices=[('M', 'Male'), ('F', 'Female'), ('O', 'Other')], max_length=1)),
                ('marital_status', models.CharField(choices=[('SINGLE', 'Single'), ('MARRIED', 'Married'), ('DIVORCED', 'Divorced'), ('WIDOWED', 'Widowed')], max_length=10)),
                ('phone_number', models.CharField(blank=True, max_length=20, null=True)),
                ('email', models.EmailField(blank=True, max_length=254, null=True)),
                ('address', models.TextField(blank=True, null=True)),
                ('nationality', models.CharField(default='Ethiopian', max_length=100)),
                ('occupation', models.CharField(blank=True, max_length=100, null=True)),
                ('photo', models.ImageField(blank=True, null=True, upload_to='citizen_photos/')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='Country',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(max_length=100, unique=True)),
                ('code', models.CharField(help_text='ISO 3-letter country code', max_length=3, unique=True)),
                ('flag', models.ImageField(blank=True, null=True, upload_to='country_flags/')),
            ],
            options={
                'verbose_name': 'Country',
                'verbose_name_plural': 'Countries',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='IDCardTemplate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('description', models.TextField(blank=True, null=True)),
                ('is_default', models.BooleanField(default=False)),
                ('front_design', models.JSONField(default=dict)),
                ('back_design', models.JSONField(default=dict)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='IDCard',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('card_number', models.CharField(blank=True, max_length=50, null=True, unique=True)),
                ('issue_date', models.DateField()),
                ('expiry_date', models.DateField()),
                ('status', models.CharField(choices=[('DRAFT', 'Draft'), ('PENDING', 'Pending Approval'), ('APPROVED', 'Approved'), ('PRINTED', 'Printed'), ('ISSUED', 'Issued'), ('EXPIRED', 'Expired'), ('REVOKED', 'Revoked')], default='DRAFT', max_length=10)),
                ('card_data', models.JSONField(blank=True, default=dict)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('citizen', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='id_cards', to='centers.citizen')),
                ('template', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='centers.idcardtemplate')),
            ],
        ),
        migrations.CreateModel(
            name='Region',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(max_length=100)),
                ('code', models.CharField(help_text='5-character unique code for the region', max_length=5, unique=True)),
                ('country', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='regions', to='centers.country')),
            ],
            options={
                'verbose_name': 'Region',
                'verbose_name_plural': 'Regions',
                'ordering': ['name', 'code'],
            },
        ),
        migrations.AddField(
            model_name='city',
            name='region',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='cities', to='centers.region'),
        ),
    ]
