import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Container,
  Typo<PERSON>,
  Button,
  Paper,
  TextField,
  Grid,
  Alert,
} from '@mui/material';

const TokenFixPage: React.FC = () => {
  const [message, setMessage] = useState<string>('');
  const [tokens, setTokens] = useState<any>({});
  const [cookies, setCookies] = useState<any>({});
  const [fixedTokens, setFixedTokens] = useState<boolean>(false);

  useEffect(() => {
    // Get all token-related items from localStorage
    const tokenItems: Record<string, string> = {};
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && (key.includes('token') || key.includes('schema'))) {
        tokenItems[key] = localStorage.getItem(key) || '';
      }
    }
    setTokens(tokenItems);
  }, [fixedTokens]);

  const handleFixTokens = () => {
    try {
      // Get the current schema
      const schema = localStorage.getItem('jwt_schema') || localStorage.getItem('schema_name');
      if (!schema) {
        setMessage('No schema found. Cannot fix tokens.');
        return;
      }

      // Get the tokens
      const accessToken = localStorage.getItem(`jwt_access_token_${schema}`) || localStorage.getItem('jwt_access_token');
      const refreshToken = localStorage.getItem(`jwt_refresh_token_${schema}`) || localStorage.getItem('jwt_refresh_token');

      if (!accessToken || !refreshToken) {
        setMessage('Missing tokens. Cannot fix.');
        return;
      }

      // Clear all token-related items
      const keysToRemove: string[] = [];
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key && (key.includes('jwt_access_token') || key.includes('jwt_refresh_token'))) {
          keysToRemove.push(key);
        }
      }
      keysToRemove.forEach(key => localStorage.removeItem(key));

      // Store tokens consistently
      const formattedSchema = schema.replace(/\s+/g, '_');
      localStorage.setItem('jwt_schema', formattedSchema);
      localStorage.setItem('schema_name', formattedSchema);
      localStorage.setItem('current_schema', formattedSchema);

      localStorage.setItem('jwt_access_token', accessToken);
      localStorage.setItem(`jwt_access_token_${formattedSchema}`, accessToken);

      localStorage.setItem('jwt_refresh_token', refreshToken);
      localStorage.setItem(`jwt_refresh_token_${formattedSchema}`, refreshToken);

      // Set cookies for refresh token (both names for compatibility)
      const expirationDate = new Date();
      expirationDate.setDate(expirationDate.getDate() + 30);

      // Set the jwt_refresh_token cookie
      document.cookie = `jwt_refresh_token=${encodeURIComponent(refreshToken)}; path=/; expires=${expirationDate.toUTCString()}; SameSite=Lax`;

      // Set the refresh_token cookie (legacy name)
      document.cookie = `refresh_token=${encodeURIComponent(refreshToken)}; path=/; expires=${expirationDate.toUTCString()}; SameSite=Lax`;

      // Clear any schema_name cookie that might be set to 'public'
      document.cookie = `schema_name=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT; SameSite=Lax`;

      setMessage('Tokens fixed successfully!');
      setFixedTokens(!fixedTokens);
    } catch (error) {
      setMessage(`Error fixing tokens: ${error.message}`);
    }
  };

  const handleShowToken = () => {
    try {
      const schema = localStorage.getItem('jwt_schema') || localStorage.getItem('schema_name');
      if (!schema) {
        setMessage('No schema found.');
        return;
      }

      const accessToken = localStorage.getItem(`jwt_access_token_${schema}`) || localStorage.getItem('jwt_access_token');
      if (!accessToken) {
        setMessage('No access token found.');
        return;
      }

      // Decode JWT token
      const parts = accessToken.split('.');
      if (parts.length !== 3) {
        setMessage('Invalid token format.');
        return;
      }

      const payload = JSON.parse(atob(parts[1]));
      console.log('Token payload:', payload);

      // Check expiration
      if (payload.exp) {
        const expirationDate = new Date(payload.exp * 1000);
        const now = new Date();
        const isExpired = expirationDate < now;
        console.log('Expiration:', expirationDate.toLocaleString());
        console.log('Is Expired:', isExpired);
        console.log('Time Remaining:', isExpired ? 'Expired' : `${Math.floor((expirationDate.getTime() - now.getTime()) / 1000)} seconds`);
      }

      setMessage(`Token info shown in console. Schema: ${schema}, Expires: ${new Date(payload.exp * 1000).toLocaleString()}`);
    } catch (error) {
      setMessage(`Error showing token: ${error.message}`);
    }
  };

  return (
    <Container maxWidth="md">
      <Paper sx={{ p: 3, my: 3 }}>
        <Typography variant="h4" gutterBottom>
          Token Fix Page
        </Typography>

        <Typography variant="body1" paragraph>
          This page helps fix token storage inconsistencies.
        </Typography>

        <Box sx={{ mb: 3 }}>
          <Button
            variant="contained"
            color="primary"
            onClick={handleFixTokens}
            sx={{ mr: 2 }}
          >
            Fix Tokens
          </Button>

          <Button
            variant="outlined"
            color="info"
            onClick={handleShowToken}
          >
            Show Token Info
          </Button>
        </Box>

        {message && (
          <Alert severity="info" sx={{ mb: 3 }}>
            {message}
          </Alert>
        )}

        <Typography variant="h6" gutterBottom>
          Current Token Storage
        </Typography>

        <Box sx={{ maxHeight: '400px', overflow: 'auto', border: '1px solid #eee', p: 2 }}>
          {Object.entries(tokens).map(([key, value]) => (
            <Box key={key} sx={{ mb: 1 }}>
              <Typography variant="subtitle2" component="span" sx={{ fontWeight: 'bold' }}>
                {key}:
              </Typography>
              <Typography variant="body2" component="span" sx={{ ml: 1, wordBreak: 'break-all' }}>
                {typeof value === 'string' && value.length > 50
                  ? `${value.substring(0, 50)}...`
                  : value}
              </Typography>
            </Box>
          ))}
        </Box>
      </Paper>
    </Container>
  );
};

export default TokenFixPage;
