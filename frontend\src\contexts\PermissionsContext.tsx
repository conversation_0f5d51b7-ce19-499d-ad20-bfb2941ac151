import React, { createContext, useContext, useState, useEffect } from 'react';
import { Permission, getCurrentUserPermissions, currentUserHasPermission } from '../utils/permissionsManager';

// Context interface
interface PermissionsContextType {
  permissions: Permission[];
  hasPermission: (permission: Permission) => boolean;
  refreshPermissions: () => void;
  loading: boolean;
}

// Create the context with default values
const PermissionsContext = createContext<PermissionsContextType>({
  permissions: [],
  hasPermission: () => false,
  refreshPermissions: () => {},
  loading: true,
});

// Hook to use the permissions context
export const usePermissions = () => useContext(PermissionsContext);

// Provider component
export const PermissionsProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [permissions, setPermissions] = useState<Permission[]>([]);
  const [loading, setLoading] = useState(true);

  // Function to refresh permissions
  const refreshPermissions = () => {
    try {
      const userPermissions = getCurrentUserPermissions();
      setPermissions(userPermissions);
    } catch (error) {
      console.error('Error refreshing permissions:', error);
    } finally {
      setLoading(false);
    }
  };

  // Check if the user has a specific permission
  const hasPermission = (permission: Permission): boolean => {
    return permissions.includes(permission);
  };

  // Load permissions on mount only - disable storage event listener temporarily
  useEffect(() => {
    console.log('PermissionsContext: Loading permissions on mount');

    // Set a flag in sessionStorage to prevent multiple initializations
    const isInitialized = sessionStorage.getItem('permissions_initialized');

    if (!isInitialized) {
      sessionStorage.setItem('permissions_initialized', 'true');
      setLoading(true);
      refreshPermissions();
    }

    // Temporarily disable storage event listener to debug infinite refresh
    // const handleStorageChange = (event: StorageEvent) => {
    //   if (event.key === 'user' || event.key === 'tenant') {
    //     if (!loading) {
    //       setLoading(true);
    //       refreshPermissions();
    //     }
    //   }
    // };
    //
    // window.addEventListener('storage', handleStorageChange);
    //
    // return () => {
    //   window.removeEventListener('storage', handleStorageChange);
    // };

    return () => {
      console.log('PermissionsContext: Cleanup');
    };
  }, []);

  // Context value
  const value: PermissionsContextType = {
    permissions,
    hasPermission,
    refreshPermissions,
    loading,
  };

  return (
    <PermissionsContext.Provider value={value}>
      {children}
    </PermissionsContext.Provider>
  );
};

// Higher-order component to require specific permissions
export const withPermissionCheck = <P extends object>(
  Component: React.ComponentType<P>,
  requiredPermission: Permission,
  FallbackComponent?: React.ComponentType<P>
) => {
  const WithPermissionCheck: React.FC<P> = (props) => {
    const { hasPermission, loading } = usePermissions();

    if (loading) {
      return null; // Or a loading indicator
    }

    if (hasPermission(requiredPermission)) {
      return <Component {...props} />;
    }

    if (FallbackComponent) {
      return <FallbackComponent {...props} />;
    }

    return null;
  };

  return WithPermissionCheck;
};

// Component to conditionally render children based on permissions
export const PermissionGate: React.FC<{
  permission: Permission;
  children: React.ReactNode;
  fallback?: React.ReactNode;
}> = ({ permission, children, fallback }) => {
  const { hasPermission } = usePermissions();

  if (hasPermission(permission)) {
    return <>{children}</>;
  }

  return <>{fallback}</> || null;
};
