/**
 * Header Fix Script
 *
 * This script fixes specific issues with authentication headers:
 * 1. Duplicated tokens in Authorization header
 * 2. Incorrect schema name format
 * 3. Token not being found in the correct schema
 */

(function() {
  console.log('Running header fix script...');

  // Fix 1: Clean up the token in localStorage
  const token = localStorage.getItem('token');
  if (token) {
    // Check if token contains a comma and "Token" twice (indicating duplication)
    if (token.includes(',') && (token.match(/Token/g) || []).length > 1) {
      console.log('Found duplicated token in localStorage');

      // Extract just the first token value
      const match = token.match(/Token\s+([a-zA-Z0-9]+)/);
      if (match && match[1]) {
        const cleanToken = match[1];
        console.log('Extracted clean token:', cleanToken);
        localStorage.setItem('token', cleanToken);
      } else {
        // If we can't extract it properly, just take the first part before the comma
        const cleanToken = token.split(',')[0].replace(/Token\s+/i, '').trim();
        console.log('Extracted clean token (fallback method):', cleanToken);
        localStorage.setItem('token', cleanToken);
      }
    } else if (token.startsWith('Token ')) {
      // If token starts with "Token ", remove that prefix
      const cleanToken = token.replace(/^Token\s+/i, '').trim();
      console.log('Removed "Token " prefix from token:', cleanToken);
      localStorage.setItem('token', cleanToken);
    }
  }

  // Fix 2: Clean up the schema name
  const schema = localStorage.getItem('schema_name');
  if (schema) {
    // Check if schema contains a comma (indicating duplication)
    if (schema.includes(',')) {
      console.log('Found duplicated schema in localStorage');

      // Take just the first part before the comma
      const cleanSchema = schema.split(',')[0].trim();
      console.log('Extracted clean schema:', cleanSchema);
      localStorage.setItem('schema_name', cleanSchema);

      // Also update the schema cookie
      document.cookie = `schema_name=${encodeURIComponent(cleanSchema)}; path=/; SameSite=Lax`;
    }

    // IMPORTANT: We now know that the schema should KEEP spaces (e.g., "kebele 14")
    // So we're NOT replacing spaces with underscores in the schema name
    console.log('Using schema with spaces:', schema);
  }

  // Fix 3: Update the tokenStore to ensure token is associated with the correct schema
  try {
    const tokenStoreStr = localStorage.getItem('tokenStore');
    const updatedToken = localStorage.getItem('token'); // Get the cleaned token
    const updatedSchema = localStorage.getItem('schema_name'); // Get the cleaned schema

    if (tokenStoreStr && updatedToken && updatedSchema) {
      let tokenStore = JSON.parse(tokenStoreStr);

      // Update the token for the cleaned schema
      tokenStore[updatedSchema] = updatedToken;

      // Remove any entries with problematic schema names (containing spaces or commas)
      Object.keys(tokenStore).forEach(key => {
        if (key.includes(' ') || key.includes(',')) {
          console.log('Removing problematic schema from tokenStore:', key);
          delete tokenStore[key];
        }
      });

      // Save the updated tokenStore
      localStorage.setItem('tokenStore', JSON.stringify(tokenStore));
      console.log('Updated tokenStore with clean schema and token');
    }
  } catch (error) {
    console.error('Error updating tokenStore:', error);
  }

  // Fix 4: Update the tenant object if it exists
  try {
    const tenantStr = localStorage.getItem('tenant');
    const updatedSchema = localStorage.getItem('schema_name'); // Get the cleaned schema

    if (tenantStr && updatedSchema) {
      let tenant = JSON.parse(tenantStr);

      // Update the schema_name in the tenant object
      if (tenant.schema_name) {
        tenant.schema_name = updatedSchema;
        localStorage.setItem('tenant', JSON.stringify(tenant));
        console.log('Updated schema_name in tenant object');
      }
    }
  } catch (error) {
    console.error('Error updating tenant object:', error);
  }

  console.log('Header fix script completed');

  // Create a custom fetch function that ensures headers are correct
  const originalFetch = window.fetch;
  window.fetch = function(url, options = {}) {
    // Only modify requests to API endpoints
    if (typeof url === 'string' && url.includes('/api/')) {
      options = options || {};
      options.headers = options.headers || {};

      // Get the token and schema
      const token = localStorage.getItem('token');
      const schema = localStorage.getItem('schema_name');

      if (token) {
        // Ensure Authorization header is correct (no duplication)
        options.headers['Authorization'] = `Token ${token}`;
      }

      if (schema) {
        // IMPORTANT: For the X-Schema-Name header, we use the schema WITH spaces
        options.headers['X-Schema-Name'] = schema;

        // But for the URL, we need to replace spaces with underscores
        if (typeof url === 'string' && url.includes('/api/tenant/')) {
          // Extract the schema from the URL
          const urlParts = url.split('/');
          const tenantIndex = urlParts.indexOf('tenant');
          if (tenantIndex >= 0 && tenantIndex < urlParts.length - 1) {
            const urlSchema = urlParts[tenantIndex + 1];
            // If the URL schema has underscores but our schema has spaces
            if (urlSchema.includes('_') && schema.includes(' ')) {
              // Replace the schema in the URL with the correct format (spaces -> underscores)
              const correctUrlSchema = schema.replace(/\s+/g, '_');
              if (urlSchema !== correctUrlSchema) {
                urlParts[tenantIndex + 1] = correctUrlSchema;
                url = urlParts.join('/');
                console.log('Modified URL to use correct schema format:', url);
              }
            }
          }
        }
      }

      console.log('Modified fetch request headers:', options.headers);
    }

    return originalFetch.call(this, url, options);
  };

  console.log('Installed custom fetch function to ensure correct headers');
})();
