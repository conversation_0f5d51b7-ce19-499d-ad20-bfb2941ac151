import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  Paper,
  Button,
  Chip,
  Divider,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  CircularProgress,
  Card,
  CardContent,
  Snackbar,
  Alert,
  Container,
  TableContainer,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  MenuItem,
  Select,
  SelectChangeEvent,
  FormControl,
  InputLabel,
  FormHelperText,
} from '@mui/material';
import Grid from '@mui/material/Grid';
import PermissionGuard from '../components/PermissionGuard';
import { useTenant } from '../contexts/TenantContext';
import { tenantApi } from '../services/api';
import tokenService from '../services/tokenService';
import PersonAddIcon from '@mui/icons-material/PersonAdd';
import SupervisorAccountIcon from '@mui/icons-material/SupervisorAccount';
import RefreshIcon from '@mui/icons-material/Refresh';

// Type definitions
interface Tenant {
  id: number;
  name: string;
  schema_name: string;
  schema_type: string;
  domains: string[];
}

interface User {
  id: number;
  username?: string;
  email: string;
  first_name: string;
  last_name: string;
  role: string;
  is_active?: boolean;
  tenant_schema?: string;
}

interface ApiResponse {
  message?: string;
  status?: string;
  success?: boolean;
  data?: any;
  error?: string;
  [key: string]: any;
}

const KebeleUserManagement: React.FC = () => {
  const { tenant, schemaName } = useTenant();
  const [childTenants, setChildTenants] = useState<Tenant[]>([]);
  const [selectedTenant, setSelectedTenant] = useState<Tenant | null>(null);
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [formData, setFormData] = useState({
    email: '',
    username: '',
    password: '',
    first_name: '',
    last_name: '',
    role: 'CENTER_STAFF',
    tenant_schema: '',
  });
  const [formErrors, setFormErrors] = useState({
    email: '',
    username: '',
    password: '',
    first_name: '',
    last_name: '',
  });
  const [submitting, setSubmitting] = useState(false);
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [snackbarSeverity, setSnackbarSeverity] = useState<'success' | 'error'>('success');

  // Helper function to get the current schema
  const getCurrentSchema = () => {
    const currentSchema = localStorage.getItem('schema_name') || localStorage.getItem('jwt_schema') || '';
    console.log('KebeleUserManagement: Getting current schema:', currentSchema);
    return currentSchema;
  };

  // Function to find a subcity schema
  const findSubcitySchema = (): string => {
    // First try to get it from the tenant context
    if (tenant?.schema_name?.startsWith('subcity_')) {
      console.log('KebeleUserManagement: Using tenant context schema:', tenant.schema_name);
      return tenant.schema_name;
    }

    // Then try to get it from the child tenants
    const subcityTenant = childTenants.find(tenant => tenant.schema_name.startsWith('subcity_'));
    if (subcityTenant) {
      console.log('KebeleUserManagement: Using child tenant schema:', subcityTenant.schema_name);
      return subcityTenant.schema_name;
    }

    // Try to find a subcity schema in localStorage
    const keys = Object.keys(localStorage);
    for (const key of keys) {
      if (key.startsWith('jwt_access_token_subcity_')) {
        const schema = key.replace('jwt_access_token_', '');
        console.log('KebeleUserManagement: Found subcity schema in localStorage:', schema);
        return schema;
      }
    }

    // If no subcity schema found in localStorage, check if schema_name is a subcity
    const schemaName = localStorage.getItem('schema_name');
    if (schemaName && schemaName.startsWith('subcity_')) {
      console.log('KebeleUserManagement: Using schema_name as subcity schema:', schemaName);
      return schemaName;
    }

    // Default to subcity_zoble if nothing else works
    console.log('KebeleUserManagement: Defaulting to subcity_zoble');
    return 'subcity_zoble';
  };

  // Fetch child tenants
  const fetchChildTenants = async () => {
    setLoading(true);
    setError(null);

    try {
      // Get the current schema
      const currentSchema = getCurrentSchema();

      // Check if the current schema is a subcity schema
      const isSubcitySchema = currentSchema.startsWith('subcity_');

      // Use the current schema if it's a subcity schema, otherwise try to find a subcity schema
      const subcitySchema = isSubcitySchema ? currentSchema : findSubcitySchema();

      console.log('KebeleUserManagement: Using subcity schema:', subcitySchema);

      // Get authentication headers from token service
      const headers = tokenService.getAuthHeaders(subcitySchema);
      console.log('KebeleUserManagement: Using unified token service for authentication');
      console.log('KebeleUserManagement: Headers:', headers);

      console.log('KebeleUserManagement: Auth header:', headers['Authorization'] ? 'Token present (not shown for security)' : 'No token');
      console.log('KebeleUserManagement: Schema header:', headers['X-Schema-Name']);

      // Try to fetch child tenants using the tenantApi service
      console.log('KebeleUserManagement: Using tenantApi service to fetch child tenants');
      try {
        const data = await tenantApi.get<Tenant[]>(subcitySchema, 'child-tenants');
        console.log('KebeleUserManagement: API call successful, data:', data);
        setChildTenants(data);

        // Select the first tenant by default if available
        if (data.length > 0 && !selectedTenant) {
          setSelectedTenant(data[0]);
          fetchTenantUsers(data[0].schema_name);
        } else {
          setLoading(false);
        }
      } catch (error: any) {
        console.error('KebeleUserManagement: Error with tenantApi service:', error);

        // Try using a direct API call as a fallback
        console.log('KebeleUserManagement: Trying direct API call as a fallback');
        try {
          const response = await fetch(`http://localhost:8000/api/child-tenants/`, {
            method: 'GET',
            headers: headers,
            credentials: 'include'
          });

          if (!response.ok) {
            throw new Error(`API error: ${response.status} ${response.statusText}`);
          }

          const data = await response.json();
          console.log('KebeleUserManagement: Direct API call successful, data:', data);
          setChildTenants(data);

          // Select the first tenant by default if available
          if (data.length > 0 && !selectedTenant) {
            setSelectedTenant(data[0]);
            fetchTenantUsers(data[0].schema_name);
          } else {
            setLoading(false);
          }
        } catch (directError: any) {
          console.error('KebeleUserManagement: Error with direct API call:', directError);

          // Try the tenant-specific endpoint as a last resort
          console.log('KebeleUserManagement: Trying tenant-specific endpoint as a last resort');
          try {
            // Use the correct endpoint for child tenants
            const response = await fetch(`http://localhost:8000/api/child-tenants/`, {
              method: 'GET',
              headers: headers,
              credentials: 'include'
            });

            if (!response.ok) {
              throw new Error(`API error: ${response.status} ${response.statusText}`);
            }

            const data = await response.json();
            console.log('KebeleUserManagement: Tenant-specific endpoint successful, data:', data);
            setChildTenants(data);

            // Select the first tenant by default if available
            if (data.length > 0 && !selectedTenant) {
              setSelectedTenant(data[0]);
              fetchTenantUsers(data[0].schema_name);
            } else {
              setLoading(false);
            }
          } catch (tenantError: any) {
            console.error('KebeleUserManagement: Error with tenant-specific endpoint:', tenantError);
            setError('Failed to fetch child tenants. Please check your network connection and try again.');
            setLoading(false);
          }
        }
      }
    } catch (error: any) {
      console.error('Error fetching child tenants:', error);
      setError(error.message || 'Failed to fetch child tenants');
      setLoading(false);
    }
  };

  // This function has been moved to line 111

  // Fetch users for a specific tenant
  const fetchTenantUsers = async (tenantSchema: string) => {
    setLoading(true);
    setError(null);

    try {
      // Get the current schema
      const currentSchema = getCurrentSchema();

      // Check if the current schema is a subcity schema
      const isSubcitySchema = currentSchema.startsWith('subcity_');

      // Use the current schema if it's a subcity schema, otherwise try to find a subcity schema
      const subcitySchema = isSubcitySchema ? currentSchema : findSubcitySchema();

      console.log('KebeleUserManagement: Using subcity schema for fetchTenantUsers:', subcitySchema);
      console.log('KebeleUserManagement: Fetching users for tenant schema:', tenantSchema);

      // Get authentication headers from token service
      const headers = tokenService.getAuthHeaders(subcitySchema);
      console.log('KebeleUserManagement: Using unified token service for authentication');
      console.log('KebeleUserManagement: Headers:', headers);

      console.log('KebeleUserManagement: Auth header:', headers['Authorization'] ? 'Token present (not shown for security)' : 'No token');
      console.log('KebeleUserManagement: Schema header:', headers['X-Schema-Name']);

      // Try to fetch users using the tenantApi service
      console.log('KebeleUserManagement: Using tenantApi service to fetch tenant users');
      try {
        const data = await tenantApi.get<User[]>(tenantSchema, 'users');
        console.log('KebeleUserManagement: API call successful for users, data:', data);
        setUsers(data);
      } catch (error: any) {
        console.error('KebeleUserManagement: Error fetching tenant users with tenantApi:', error);

        // Try using a direct API call as a fallback
        console.log('KebeleUserManagement: Trying direct API call as a fallback');
        try {
          const response = await fetch(`http://localhost:8000/api/tenant/${tenantSchema}/users/`, {
            method: 'GET',
            headers: headers,
            credentials: 'include'
          });

          if (!response.ok) {
            throw new Error(`API error: ${response.status} ${response.statusText}`);
          }

          const data = await response.json();
          console.log('KebeleUserManagement: Direct API call successful for users, data:', data);
          setUsers(data);
        } catch (directError: any) {
          console.error('KebeleUserManagement: Error with direct API call for users:', directError);

          // Try the child-tenants users endpoint as a last resort
          console.log('KebeleUserManagement: Trying child-tenants users endpoint as a last resort');
          try {
            const response = await fetch(`http://localhost:8000/api/child-tenants/${tenantSchema}/users/`, {
              method: 'GET',
              headers: headers,
              credentials: 'include'
            });

            if (!response.ok) {
              throw new Error(`API error: ${response.status} ${response.statusText}`);
            }

            const data = await response.json();
            console.log('KebeleUserManagement: Child-tenants users endpoint successful, data:', data);
            setUsers(data);
          } catch (childError: any) {
            console.error('KebeleUserManagement: Error with child-tenants users endpoint:', childError);
            setError('Failed to fetch tenant users. Please check your network connection and try again.');
          }
        }
      }
    } catch (error: any) {
      console.error('Error fetching tenant users:', error);
      setError(error.message || 'Failed to fetch tenant users');
    } finally {
      setLoading(false);
    }
  };

  // Edit a user in the selected tenant
  const editUser = async () => {
    if (!selectedTenant || !selectedUser) return;

    // Validate form
    let hasErrors = false;
    const errors = {
      email: '',
      username: '',
      password: '',
      first_name: '',
      last_name: '',
    };

    if (!formData.email) {
      errors.email = 'Email is required';
      hasErrors = true;
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      errors.email = 'Email is invalid';
      hasErrors = true;
    }

    if (formData.password && formData.password.length < 8) {
      errors.password = 'Password must be at least 8 characters';
      hasErrors = true;
    }

    if (!formData.first_name) {
      errors.first_name = 'First name is required';
      hasErrors = true;
    }

    if (!formData.last_name) {
      errors.last_name = 'Last name is required';
      hasErrors = true;
    }

    // Use the selected tenant from the dropdown or the currently selected tenant
    const targetTenantSchema = formData.tenant_schema || selectedTenant.schema_name;
    if (!targetTenantSchema) {
      setSnackbarMessage('Please select a tenant');
      setSnackbarSeverity('error');
      setSnackbarOpen(true);
      return;
    }

    if (hasErrors) {
      setFormErrors(errors);
      return;
    }

    setSubmitting(true);

    try {
      // Use the tenantApi service to update the user
      const response = await tenantApi.put<ApiResponse>(
        targetTenantSchema,
        `users/${selectedUser.id}/`,
        formData
      );

      // Success
      setSnackbarMessage(response.message || 'User updated successfully');
      setSnackbarSeverity('success');
      setSnackbarOpen(true);
      setEditDialogOpen(false);

      // Reset form
      setFormData({
        email: '',
        username: '',
        password: '',
        first_name: '',
        last_name: '',
        role: 'CENTER_STAFF',
        tenant_schema: '',
      });
      setSelectedUser(null);

      // Refresh users
      fetchTenantUsers(selectedTenant.schema_name);
    } catch (error: any) {
      console.error('Error updating user:', error);
      setSnackbarMessage(error.message || 'Failed to update user');
      setSnackbarSeverity('error');
      setSnackbarOpen(true);
    } finally {
      setSubmitting(false);
    }
  };

  // Delete a user from the selected tenant
  const deleteUser = async () => {
    if (!selectedTenant || !selectedUser) return;

    setSubmitting(true);

    try {
      // Use the tenantApi service to delete the user
      const data = await tenantApi.delete<ApiResponse>(
        selectedTenant.schema_name,
        `users/${selectedUser.id}/delete/`
      );

      // Success
      setSnackbarMessage(data.message || 'User deleted successfully');
      setSnackbarSeverity('success');
      setSnackbarOpen(true);
      setDeleteDialogOpen(false);
      setSelectedUser(null);

      // Refresh users
      fetchTenantUsers(selectedTenant.schema_name);
    } catch (error: any) {
      console.error('Error deleting user:', error);
      setSnackbarMessage(error.message || 'Failed to delete user');
      setSnackbarSeverity('error');
      setSnackbarOpen(true);
    } finally {
      setSubmitting(false);
    }
  };

  // Create a new user in the selected tenant
  const createUser = async () => {
    if (!selectedTenant) return;

    // Validate form
    let hasErrors = false;
    const errors = {
      email: '',
      username: '',
      password: '',
      first_name: '',
      last_name: '',
    };

    if (!formData.email) {
      errors.email = 'Email is required';
      hasErrors = true;
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      errors.email = 'Email is invalid';
      hasErrors = true;
    }

    if (!formData.password) {
      errors.password = 'Password is required';
      hasErrors = true;
    } else if (formData.password.length < 8) {
      errors.password = 'Password must be at least 8 characters';
      hasErrors = true;
    }

    if (!formData.first_name) {
      errors.first_name = 'First name is required';
      hasErrors = true;
    }

    if (!formData.last_name) {
      errors.last_name = 'Last name is required';
      hasErrors = true;
    }

    // Use the selected tenant from the dropdown or the currently selected tenant
    const targetTenantSchema = formData.tenant_schema || selectedTenant.schema_name;
    if (!targetTenantSchema) {
      setSnackbarMessage('Please select a tenant');
      setSnackbarSeverity('error');
      setSnackbarOpen(true);
      return;
    }

    if (hasErrors) {
      setFormErrors(errors);
      return;
    }

    setSubmitting(true);

    try {
      // Use the correct API endpoint for creating users in a kebele tenant
      // The endpoint should be /api/child-tenants/{schema_name}/users/create/
      console.log(`KebeleUserManagement: Creating user in tenant ${targetTenantSchema}`);

      // Get the current schema
      const currentSchema = getCurrentSchema();

      // Check if the current schema is a subcity schema
      const isSubcitySchema = currentSchema.startsWith('subcity_');

      // Use the current schema if it's a subcity schema, otherwise try to find a subcity schema
      const subcitySchema = isSubcitySchema ? currentSchema : findSubcitySchema();

      // Get authentication headers from token service
      const headers = tokenService.getAuthHeaders(subcitySchema);

      // Use the form data as is without adding a username field
      console.log('KebeleUserManagement: Form data:', formData);

      // Make a direct API call to the correct endpoint
      const response = await fetch(`http://localhost:8000/api/child-tenants/${targetTenantSchema}/users/create/`, {
        method: 'POST',
        headers: headers,
        body: JSON.stringify(formData),
        credentials: 'include'
      });

      if (!response.ok) {
        throw new Error(`API error: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      console.log('KebeleUserManagement: User creation response:', data);

      // Success - include username information in the message if available
      let successMessage = data.message || 'User created successfully';

      // Add login instructions if available
      if (data.username_info && data.username_info.login_instructions) {
        successMessage += '\n\n' + data.username_info.login_instructions;
      }

      setSnackbarMessage(successMessage);
      setSnackbarSeverity('success');
      setSnackbarOpen(true);
      setCreateDialogOpen(false);

      // Reset form
      setFormData({
        email: '',
        username: '',
        password: '',
        first_name: '',
        last_name: '',
        role: 'CENTER_STAFF',
        tenant_schema: '',
      });

      // Refresh users
      fetchTenantUsers(selectedTenant.schema_name);
    } catch (error: any) {
      console.error('Error creating user:', error);
      setSnackbarMessage(error.message || 'Failed to create user');
      setSnackbarSeverity('error');
      setSnackbarOpen(true);
    } finally {
      setSubmitting(false);
    }
  };

  // Handle tenant selection
  const handleTenantChange = (tenant: Tenant) => {
    setSelectedTenant(tenant);
    fetchTenantUsers(tenant.schema_name);
  };

  // Handle form input changes
  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | { name?: string; value: unknown }> | SelectChangeEvent<string>
  ) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name as string]: value,
    });

    // Clear error for this field
    if (name && Object.keys(formErrors).includes(name as string)) {
      setFormErrors({
        ...formErrors,
        [name]: '',
      });
    }
  };

  // Open edit dialog for a user
  const handleEditUser = (user: User) => {
    setSelectedUser(user);
    setFormData({
      email: user.email,
      username: user.username || '',
      password: '', // Don't set password for editing
      first_name: user.first_name,
      last_name: user.last_name,
      role: user.role,
      tenant_schema: selectedTenant?.schema_name || '',
    });
    setEditDialogOpen(true);
  };

  // Open delete dialog for a user
  const handleDeleteUser = (user: User) => {
    setSelectedUser(user);
    setDeleteDialogOpen(true);
  };

  // Handle snackbar close
  const handleSnackbarClose = () => {
    setSnackbarOpen(false);
  };

  // Load data on component mount
  useEffect(() => {
    // Try to get the token from various sources and ensure it's in sessionStorage
    const initializeToken = async () => {
      console.log('KebeleUserManagement: Initializing token');

      // Get the current schema
      const currentSchema = getCurrentSchema();
      if (!currentSchema) {
        console.error('KebeleUserManagement: No schema found in sessionStorage');
        return;
      }

      console.log('KebeleUserManagement: Using schema:', currentSchema);

      // Try to get the token from localStorage
      let token = localStorage.getItem('jwt_access_token');
      if (!token) {
        token = localStorage.getItem('token');
      }

      if (!token) {
        console.error('KebeleUserManagement: No token found in any storage location');
        return;
      }

      console.log('KebeleUserManagement: Token initialized');

      // Store the token in the token service
      const { storeTokensForSchema } = await import('../services/tokenService');

      // Clean up token - remove any existing prefixes
      let cleanToken = token;
      if (cleanToken.startsWith('Token ')) {
        cleanToken = cleanToken.substring(6);
      } else if (cleanToken.startsWith('Bearer ')) {
        cleanToken = cleanToken.substring(7);
      }

      storeTokensForSchema(currentSchema, cleanToken, '');

      // Test the token with a direct API call
      try {
        console.log('KebeleUserManagement: Testing token with direct API call');
        const testResponse = await fetch('http://localhost:8000/api/validate-token/', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Token ${cleanToken}`,
            'X-Schema-Name': currentSchema
          },
          credentials: 'include'
        });

        console.log('KebeleUserManagement: Token test response status:', testResponse.status);
        if (testResponse.ok) {
          console.log('KebeleUserManagement: Token is valid');
        } else {
          console.warn('KebeleUserManagement: Token validation failed');
          try {
            const errorText = await testResponse.text();
            console.log('KebeleUserManagement: Token validation error:', errorText);

            // If token validation fails, try to get a fresh token
            console.log('KebeleUserManagement: Trying to get a fresh token');

            // Get user info from localStorage
            const userString = localStorage.getItem('user');
            if (userString) {
              try {
                const user = JSON.parse(userString);
                if (user.email) {
                  // Try to login with the user's email
                  console.log('KebeleUserManagement: Trying to login with user email:', user.email);

                  // Import the loginWithJWT function
                  const { loginWithJWT } = await import('../services/tokenService');

                  // Try to login with a default password (this is just for testing)
                  try {
                    const loginResult = await loginWithJWT(user.email, 'password123', currentSchema);
                    console.log('KebeleUserManagement: Login result:', loginResult);

                    if (loginResult && loginResult.access_token) {
                      console.log('KebeleUserManagement: Got fresh token from login');
                      // No need to store the token here, loginWithJWT already does that
                    }
                  } catch (loginError) {
                    console.error('KebeleUserManagement: Error logging in:', loginError);
                  }
                }
              } catch (parseError) {
                console.error('KebeleUserManagement: Error parsing user from sessionStorage:', parseError);
              }
            }
          } catch (error) {
            console.error('KebeleUserManagement: Error getting token validation error:', error);
          }
        }
      } catch (error) {
        console.error('KebeleUserManagement: Error testing token:', error);
      }

      // Fetch child tenants
      fetchChildTenants();
    };

    initializeToken();
  }, [schemaName, tenant]);

  // Get role display name
  const getRoleDisplayName = (role: string) => {
    switch (role) {
      case 'CENTER_STAFF':
        return 'Clerk';
      case 'KEBELE_LEADER':
        return 'Kebele Leader (Liqe Menber)';
      case 'CENTER_ADMIN':
        return 'Center Admin';
      default:
        return role;
    }
  };

  // Get role color
  const getRoleColor = (role: string) => {
    switch (role) {
      case 'CENTER_STAFF':
        return 'primary';
      case 'KEBELE_LEADER':
        return 'success';
      case 'CENTER_ADMIN':
        return 'error';
      default:
        return 'default';
    }
  };

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      <Paper elevation={3} sx={{ p: 3, borderRadius: 2 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
          <SupervisorAccountIcon sx={{ fontSize: 32, mr: 2, color: 'primary.main' }} />
          <Typography variant="h5" component="h1" sx={{ fontWeight: 600 }}>
            Kebele User Management
          </Typography>
        </Box>

        <Typography variant="body1" sx={{ mb: 3 }}>
          Manage users for kebele tenants under your subcity. You can create kebele leaders (liqe menber) and clerks for each kebele.
        </Typography>

        {/* Tenant Selection */}
        <Box sx={{ mb: 4 }}>
          <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
            Select Kebele
          </Typography>
          <Grid container spacing={2}>
            {childTenants.map((childTenant) => (
              <Grid key={childTenant.id}>
                <Card
                  sx={{
                    cursor: 'pointer',
                    border: selectedTenant?.id === childTenant.id ? '2px solid' : '1px solid',
                    borderColor: selectedTenant?.id === childTenant.id ? 'primary.main' : 'divider',
                    boxShadow: selectedTenant?.id === childTenant.id ? 3 : 1,
                  }}
                  onClick={() => handleTenantChange(childTenant)}
                >
                  <CardContent>
                    <Typography variant="h6" sx={{ fontWeight: 600 }}>
                      {childTenant.name}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {childTenant.schema_name}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
            ))}
            {childTenants.length === 0 && !loading && (
              <Grid>
                <Box sx={{ textAlign: 'center', py: 3 }}>
                  <Typography variant="body1" color="text.secondary">
                    No kebele tenants found under your subcity.
                  </Typography>
                </Box>
              </Grid>
            )}
          </Grid>
        </Box>

        <Divider sx={{ my: 3 }} />

        {/* Users List */}
        <Box sx={{ mb: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Typography variant="h6" sx={{ fontWeight: 600 }}>
            {selectedTenant ? `Users in ${selectedTenant.name}` : 'Select a kebele to view users'}
          </Typography>
          <Box>
            <Button
              variant="outlined"
              startIcon={<RefreshIcon />}
              onClick={() => selectedTenant && fetchTenantUsers(selectedTenant.schema_name)}
              sx={{ mr: 1 }}
              disabled={!selectedTenant || loading}
            >
              Refresh
            </Button>
            <PermissionGuard feature="create_kebele_user">
              <Button
                variant="contained"
                startIcon={<PersonAddIcon />}
                onClick={() => setCreateDialogOpen(true)}
                disabled={!selectedTenant || loading}
              >
                Add User
              </Button>
            </PermissionGuard>
          </Box>
        </Box>

        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
            <CircularProgress />
          </Box>
        ) : error ? (
          <Alert severity="error" sx={{ my: 2 }}>
            {error}
          </Alert>
        ) : selectedTenant ? (
          users.length > 0 ? (
            <TableContainer component={Paper} elevation={0} sx={{ mt: 2 }}>
              <Table>
                <TableHead sx={{ bgcolor: 'rgba(0, 0, 0, 0.03)' }}>
                  <TableRow>
                    <TableCell sx={{ fontWeight: 700 }}>Name</TableCell>
                    <TableCell sx={{ fontWeight: 700 }}>Email</TableCell>
                    <TableCell sx={{ fontWeight: 700 }}>Role</TableCell>
                    <TableCell sx={{ fontWeight: 700 }}>Status</TableCell>
                    <TableCell sx={{ fontWeight: 700 }}>Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {users.map((user) => (
                    <TableRow
                      key={user.id}
                      hover
                    >
                      <TableCell>
                        <Typography variant="body1" sx={{ fontWeight: 600 }}>
                          {user.first_name} {user.last_name}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {user.email}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={getRoleDisplayName(user.role)}
                          color={getRoleColor(user.role) as any}
                          size="small"
                          sx={{ fontWeight: 600 }}
                        />
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={user.is_active ? 'Active' : 'Inactive'}
                          color={user.is_active ? 'success' : 'default'}
                          size="small"
                          sx={{ fontWeight: 600 }}
                        />
                      </TableCell>
                      <TableCell>
                        <PermissionGuard feature="edit_kebele_user">
                          <Button
                            size="small"
                            onClick={() => handleEditUser(user)}
                            sx={{ mr: 1 }}
                          >
                            Edit
                          </Button>
                        </PermissionGuard>
                        <PermissionGuard feature="delete_kebele_user">
                          <Button
                            size="small"
                            color="error"
                            onClick={() => handleDeleteUser(user)}
                          >
                            Delete
                          </Button>
                        </PermissionGuard>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          ) : (
            <Box sx={{ textAlign: 'center', my: 4 }}>
              <Typography variant="h6" color="text.secondary">
                No users found in this kebele
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                Click the "Add User" button to create a new user
              </Typography>
            </Box>
          )
        ) : (
          <Box sx={{ textAlign: 'center', my: 4 }}>
            <Typography variant="h6" color="text.secondary">
              Select a kebele to view and manage users
            </Typography>
          </Box>
        )}
      </Paper>

      {/* Create User Dialog */}
      <Dialog open={createDialogOpen} onClose={() => setCreateDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle sx={{ bgcolor: 'primary.main', color: 'white', fontWeight: 600 }}>
          Create New User
        </DialogTitle>
        <Box sx={{ bgcolor: 'info.light', color: 'info.contrastText', p: 2, mb: 0 }}>
          <Typography variant="body2">
            <strong>Important:</strong> This user will be created with the selected role and will be able to log in using the provided username or email.
          </Typography>
        </Box>
        <DialogContent sx={{ mt: 2, p: 3 }}>
          {/* Hidden field for tenant schema - using the selected tenant */}
          <input
            type="hidden"
            name="tenant_schema"
            value={selectedTenant ? selectedTenant.schema_name : ''}
          />
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="First Name"
                name="first_name"
                value={formData.first_name}
                onChange={handleInputChange}
                error={!!formErrors.first_name}
                helperText={formErrors.first_name}
                required
                size="small"
                variant="outlined"
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Last Name"
                name="last_name"
                value={formData.last_name}
                onChange={handleInputChange}
                error={!!formErrors.last_name}
                helperText={formErrors.last_name}
                required
                size="small"
                variant="outlined"
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Email"
                name="email"
                type="email"
                value={formData.email}
                onChange={handleInputChange}
                error={!!formErrors.email}
                helperText={formErrors.email}
                required
                size="small"
                variant="outlined"
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Username (for login)"
                name="username"
                value={formData.username}
                onChange={handleInputChange}
                error={!!formErrors.username}
                helperText={formErrors.username || "Username for login (optional, will be generated from email if not provided)"}
                size="small"
                variant="outlined"
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Password"
                name="password"
                type="password"
                value={formData.password}
                onChange={handleInputChange}
                error={!!formErrors.password}
                helperText={formErrors.password}
                required
                size="small"
                variant="outlined"
              />
            </Grid>
            <Grid item xs={12}>
              <FormControl fullWidth size="small" variant="outlined">
                <InputLabel id="role-label">Role</InputLabel>
                <Select
                  labelId="role-label"
                  name="role"
                  value={formData.role}
                  onChange={handleInputChange}
                  label="Role"
                >
                  <MenuItem value="CENTER_STAFF">Clerk</MenuItem>
                  <PermissionGuard feature="create_kebele_leader">
                    <MenuItem value="KEBELE_LEADER">Kebele Leader (Liqe Menber)</MenuItem>
                  </PermissionGuard>
                  <PermissionGuard feature="create_kebele_user">
                    <MenuItem value="CENTER_ADMIN">Center Admin</MenuItem>
                  </PermissionGuard>
                </Select>
                <FormHelperText>
                  {formData.role === 'KEBELE_LEADER' ?
                    `This user will be created as a Kebele Leader (Liqe Menber)` :
                    `This user will be created as a ${formData.role === 'CENTER_STAFF' ? 'Clerk' : 'Center Admin'}`
                  }
                </FormHelperText>
              </FormControl>
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions sx={{ p: 2 }}>
          <Button onClick={() => setCreateDialogOpen(false)} color="inherit" disabled={submitting}>
            Cancel
          </Button>
          <Button
            onClick={createUser}
            color="primary"
            variant="contained"
            disabled={submitting}
            startIcon={submitting ? <CircularProgress size={20} /> : <PersonAddIcon />}
          >
            {submitting ? 'Creating...' : 'Create User'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Edit User Dialog */}
      <Dialog open={editDialogOpen} onClose={() => setEditDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle sx={{ bgcolor: 'primary.main', color: 'white', fontWeight: 600 }}>
          Edit User: {selectedUser?.first_name} {selectedUser?.last_name}
        </DialogTitle>
        <DialogContent sx={{ mt: 2, p: 3 }}>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="First Name"
                name="first_name"
                value={formData.first_name}
                onChange={handleInputChange}
                error={!!formErrors.first_name}
                helperText={formErrors.first_name}
                required
                size="small"
                variant="outlined"
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Last Name"
                name="last_name"
                value={formData.last_name}
                onChange={handleInputChange}
                error={!!formErrors.last_name}
                helperText={formErrors.last_name}
                required
                size="small"
                variant="outlined"
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Email"
                name="email"
                type="email"
                value={formData.email}
                onChange={handleInputChange}
                error={!!formErrors.email}
                helperText={formErrors.email}
                required
                size="small"
                variant="outlined"
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Username (for login)"
                name="username"
                value={formData.username}
                onChange={handleInputChange}
                error={!!formErrors.username}
                helperText={formErrors.username || "Username for login (optional, will be generated from email if not provided)"}
                size="small"
                variant="outlined"
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Password (leave blank to keep current)"
                name="password"
                type="password"
                value={formData.password}
                onChange={handleInputChange}
                error={!!formErrors.password}
                helperText={formErrors.password || 'Leave blank to keep the current password'}
                size="small"
                variant="outlined"
              />
            </Grid>
            <Grid item xs={12}>
              <FormControl fullWidth size="small" variant="outlined">
                <InputLabel id="role-label">Role</InputLabel>
                <Select
                  labelId="role-label"
                  name="role"
                  value={formData.role}
                  onChange={handleInputChange}
                  label="Role"
                >
                  <MenuItem value="CENTER_STAFF">Clerk</MenuItem>
                  <MenuItem value="KEBELE_LEADER">Kebele Leader (Liqe Menber)</MenuItem>
                  <MenuItem value="CENTER_ADMIN">Center Admin</MenuItem>
                </Select>
                <FormHelperText>
                  {formData.role === 'KEBELE_LEADER' ?
                    `This user will be updated as a Kebele Leader (Liqe Menber)` :
                    `This user will be updated as a ${formData.role === 'CENTER_STAFF' ? 'Clerk' : 'Center Admin'}`
                  }
                </FormHelperText>
              </FormControl>
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions sx={{ p: 2 }}>
          <Button
            onClick={() => setEditDialogOpen(false)}
            color="inherit"
            disabled={submitting}
          >
            Cancel
          </Button>
          <Button
            onClick={editUser}
            variant="contained"
            color="primary"
            disabled={submitting}
            startIcon={submitting ? <CircularProgress size={20} /> : null}
          >
            {submitting ? 'Updating...' : 'Update User'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete User Dialog */}
      <Dialog open={deleteDialogOpen} onClose={() => setDeleteDialogOpen(false)} maxWidth="sm">
        <DialogTitle sx={{ bgcolor: 'error.main', color: 'white', fontWeight: 600 }}>
          Delete User
        </DialogTitle>
        <DialogContent sx={{ mt: 2, p: 3 }}>
          <Typography variant="body1">
            Are you sure you want to delete the user <strong>{selectedUser?.first_name} {selectedUser?.last_name}</strong>?
          </Typography>
          <Typography variant="body2" color="error" sx={{ mt: 2 }}>
            This action cannot be undone. The user will be permanently removed from the system.
          </Typography>
        </DialogContent>
        <DialogActions sx={{ p: 2 }}>
          <Button
            onClick={() => setDeleteDialogOpen(false)}
            color="inherit"
            disabled={submitting}
          >
            Cancel
          </Button>
          <Button
            onClick={deleteUser}
            variant="contained"
            color="error"
            disabled={submitting}
            startIcon={submitting ? <CircularProgress size={20} /> : null}
          >
            {submitting ? 'Deleting...' : 'Delete User'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Edit User Dialog */}
      <Dialog open={editDialogOpen} onClose={() => setEditDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle sx={{ bgcolor: 'primary.main', color: 'white', fontWeight: 600 }}>
          Edit User: {selectedUser?.first_name} {selectedUser?.last_name}
        </DialogTitle>
        <DialogContent sx={{ mt: 2, p: 3 }}>
          <Grid container spacing={2}>
            <Grid>
              <TextField
                fullWidth
                label="First Name"
                name="first_name"
                value={formData.first_name}
                onChange={handleInputChange}
                error={!!formErrors.first_name}
                helperText={formErrors.first_name}
                required
                margin="normal"
              />
            </Grid>
            <Grid>
              <TextField
                fullWidth
                label="Last Name"
                name="last_name"
                value={formData.last_name}
                onChange={handleInputChange}
                error={!!formErrors.last_name}
                helperText={formErrors.last_name}
                required
                margin="normal"
              />
            </Grid>
            <Grid>
              <TextField
                fullWidth
                label="Email"
                name="email"
                type="email"
                value={formData.email}
                onChange={handleInputChange}
                error={!!formErrors.email}
                helperText={formErrors.email}
                required
                margin="normal"
              />
            </Grid>
            <Grid>
              <TextField
                fullWidth
                label="Username (for login)"
                name="username"
                value={formData.username}
                onChange={handleInputChange}
                error={!!formErrors.username}
                helperText={formErrors.username || "Username for login (optional, will be generated from email if not provided)"}
                margin="normal"
              />
            </Grid>
            <Grid>
              <TextField
                fullWidth
                label="Password (leave blank to keep current)"
                name="password"
                type="password"
                value={formData.password}
                onChange={handleInputChange}
                error={!!formErrors.password}
                helperText={formErrors.password || 'Leave blank to keep the current password'}
                margin="normal"
              />
            </Grid>
            <Grid>
              <FormControl fullWidth margin="normal">
                <InputLabel id="role-label">Role</InputLabel>
                <Select
                  labelId="role-label"
                  name="role"
                  value={formData.role}
                  onChange={handleInputChange}
                  label="Role"
                >
                  <MenuItem value="CENTER_STAFF">Clerk</MenuItem>
                  <MenuItem value="KEBELE_LEADER">Kebele Leader (Liqe Menber)</MenuItem>
                  <MenuItem value="CENTER_ADMIN">Center Admin</MenuItem>
                </Select>
                <FormHelperText>
                  {formData.role === 'KEBELE_LEADER' ?
                    `This user will be updated as a Kebele Leader (Liqe Menber)` :
                    `This user will be updated as a ${formData.role === 'CENTER_STAFF' ? 'Clerk' : 'Center Admin'}`
                  }
                </FormHelperText>
              </FormControl>
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions sx={{ p: 2 }}>
          <Button
            onClick={() => setEditDialogOpen(false)}
            color="inherit"
            disabled={submitting}
          >
            Cancel
          </Button>
          <Button
            onClick={editUser}
            variant="contained"
            color="primary"
            disabled={submitting}
            startIcon={submitting ? <CircularProgress size={20} /> : null}
          >
            {submitting ? 'Updating...' : 'Update User'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete User Dialog */}
      <Dialog open={deleteDialogOpen} onClose={() => setDeleteDialogOpen(false)} maxWidth="sm">
        <DialogTitle sx={{ bgcolor: 'error.main', color: 'white', fontWeight: 600 }}>
          Delete User
        </DialogTitle>
        <DialogContent sx={{ mt: 2, p: 3 }}>
          <Typography variant="body1">
            Are you sure you want to delete the user <strong>{selectedUser?.first_name} {selectedUser?.last_name}</strong>?
          </Typography>
          <Typography variant="body2" color="error" sx={{ mt: 2 }}>
            This action cannot be undone. The user will be permanently removed from the system.
          </Typography>
        </DialogContent>
        <DialogActions sx={{ p: 2 }}>
          <Button
            onClick={() => setDeleteDialogOpen(false)}
            color="inherit"
            disabled={submitting}
          >
            Cancel
          </Button>
          <Button
            onClick={deleteUser}
            variant="contained"
            color="error"
            disabled={submitting}
            startIcon={submitting ? <CircularProgress size={20} /> : null}
          >
            {submitting ? 'Deleting...' : 'Delete User'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbarOpen}
        autoHideDuration={10000}
        onClose={handleSnackbarClose}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert
          onClose={handleSnackbarClose}
          severity={snackbarSeverity}
          sx={{
            width: '100%',
            maxWidth: '500px',
            whiteSpace: 'pre-line' // Support multiline messages
          }}
        >
          {snackbarMessage}
        </Alert>
      </Snackbar>
    </Container>
  );
};

export default KebeleUserManagement;
