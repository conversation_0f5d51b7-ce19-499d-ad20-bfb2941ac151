import os
import django

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'neocamelot.settings')
django.setup()

# Import models
from centers.models import Client
from django.contrib.auth import get_user_model
from django_tenants.utils import tenant_context

User = get_user_model()

def check_users_in_tenant(schema_name):
    """Check users in a specific tenant schema."""
    try:
        tenant = Client.objects.get(schema_name=schema_name)
        print(f"\nChecking users in tenant: {tenant.name} (Schema: {tenant.schema_name})")

        # Check users in tenant schema
        with tenant_context(tenant):
            users = User.objects.all()
            print(f"Users in {tenant.schema_name} schema: {users.count()}")
            for user in users:
                print(f"- {user.email} (Role: {getattr(user, 'role', 'N/A')})")
    except Client.DoesNotExist:
        print(f"Tenant with schema name {schema_name} does not exist")
    except Exception as e:
        print(f"Error: {str(e)}")

# Check all tenants
tenants = Client.objects.exclude(schema_name='public')
for tenant in tenants:
    check_users_in_tenant(tenant.schema_name)

# Also check public schema
print("\nChecking users in public schema:")
users = User.objects.all()
print(f"Users in public schema: {users.count()}")
for user in users:
    print(f"- {user.email} (Role: {getattr(user, 'role', 'N/A')})")
