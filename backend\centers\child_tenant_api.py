from rest_framework.decorators import api_view
from rest_framework.response import Response
from rest_framework import status
from django_tenants.utils import tenant_context
from django.contrib.auth import get_user_model
from django.shortcuts import get_object_or_404
from .models import Client, Kebele
from accounts.serializers import User<PERSON>erializer, UserListSerializer
import logging
import traceback

logger = logging.getLogger(__name__)

@api_view(['GET'])
def get_child_tenants(request):
    """
    Get all child kebele tenants for the current subcity tenant.
    """
    try:
        # For debugging, log the user information
        logger.info(f"Processing get_child_tenants request")
        logger.info(f"Request method: {request.method}")
        logger.info(f"Request headers: {dict(request.headers)}")

        # Check for Authorization header
        auth_header = request.headers.get('Authorization', '')
        if auth_header:
            logger.info(f"Authorization header: {auth_header[:15]}...")
        else:
            logger.warning("No Authorization header found")
            return Response({"detail": "Authentication credentials were not provided."}, status=status.HTTP_401_UNAUTHORIZED)

        # Extract token
        if not auth_header.startswith('Bearer '):
            logger.error(f"Invalid Authorization header format: {auth_header[:15]}...")
            return Response({"detail": "Invalid Authorization header format. Use 'Bearer <token>'."}, status=status.HTTP_401_UNAUTHORIZED)

        token = auth_header.split(' ')[1]
        logger.info(f"Extracted token: {token[:10]}...")

        # Validate token
        from accounts.jwt_utils import validate_jwt_token
        payload = validate_jwt_token(token)
        if not payload:
            logger.warning("Invalid JWT token")
            return Response({"detail": "Invalid JWT token."}, status=status.HTTP_401_UNAUTHORIZED)

        # Extract user ID from payload
        user_id = payload.get('sub')
        if not user_id:
            logger.warning("Invalid JWT token payload - missing 'sub' claim")
            return Response({"detail": "Invalid JWT token payload."}, status=status.HTTP_401_UNAUTHORIZED)

        # Get user from token
        User = get_user_model()
        try:
            user = User.objects.get(id=user_id)
            request.user = user
            logger.info(f"User: {user.email}, ID: {user.id}")
        except User.DoesNotExist:
            logger.error(f"User with ID {user_id} not found")
            return Response({"detail": "User not found."}, status=status.HTTP_401_UNAUTHORIZED)

        # Log the authentication headers
        schema_header = request.META.get('HTTP_X_SCHEMA_NAME', '')
        logger.info(f"X-Schema-Name header: {schema_header}")

        # Get the tenant from the request context
        if hasattr(request, 'tenant'):
            # If the request has a tenant attribute, use it
            current_tenant = request.tenant
            logger.info(f"Using tenant from request context: {current_tenant.name}, Schema: {current_tenant.schema_name}")

            # Check if this is a subcity tenant
            if current_tenant.schema_type != 'SUBCITY':
                # If this is not a subcity tenant, try to find its parent subcity
                if current_tenant.schema_type == 'KEBELE' and current_tenant.parent and current_tenant.parent.schema_type == 'SUBCITY':
                    current_tenant = current_tenant.parent
                    logger.info(f"Using parent subcity tenant: {current_tenant.name}, Schema: {current_tenant.schema_name}")
                else:
                    # If we can't find a parent subcity, use any subcity tenant
                    subcity_tenants = Client.objects.filter(schema_type='SUBCITY')
                    if subcity_tenants.exists():
                        current_tenant = subcity_tenants.first()
                        logger.info(f"Using fallback subcity tenant: {current_tenant.name}, Schema: {current_tenant.schema_name}")
                    else:
                        return Response(
                            {"error": "No subcity tenants found"},
                            status=status.HTTP_400_BAD_REQUEST
                        )
        else:
            # If the request doesn't have a tenant attribute, use any subcity tenant
            subcity_tenants = Client.objects.filter(schema_type='SUBCITY')
            if not subcity_tenants.exists():
                return Response(
                    {"error": "No subcity tenants found"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            current_tenant = subcity_tenants.first()
            logger.info(f"Using fallback subcity tenant: {current_tenant.name}, Schema: {current_tenant.schema_name}")

        # Get all kebele tenants that have this subcity as parent
        child_tenants = Client.objects.filter(parent=current_tenant, schema_type='KEBELE')

        # Format the response
        tenant_list = []
        for tenant in child_tenants:
            tenant_data = {
                'id': tenant.id,
                'name': tenant.name,
                'schema_name': tenant.schema_name,
                'schema_type': tenant.schema_type,
                'domains': [domain.domain for domain in tenant.domains.all()]
            }
            tenant_list.append(tenant_data)

        return Response(tenant_list)
    except Exception as e:
        logger.error(f"Error getting child tenants: {str(e)}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return Response(
            {"error": f"An error occurred: {str(e)}"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['GET'])
def get_tenant_users(request, schema_name):
    """
    Get all users for a specific child kebele tenant.
    """
    try:
        # For debugging, log the user information
        logger.info(f"Processing get_tenant_users request for schema {schema_name}")
        logger.info(f"Request method: {request.method}")
        logger.info(f"Request headers: {dict(request.headers)}")

        # Check for Authorization header
        auth_header = request.headers.get('Authorization', '')
        if auth_header:
            logger.info(f"Authorization header: {auth_header[:15]}...")
        else:
            logger.warning("No Authorization header found")
            return Response({"detail": "Authentication credentials were not provided."}, status=status.HTTP_401_UNAUTHORIZED)

        # Extract token
        if not auth_header.startswith('Bearer '):
            logger.error(f"Invalid Authorization header format: {auth_header[:15]}...")
            return Response({"detail": "Invalid Authorization header format. Use 'Bearer <token>'."}, status=status.HTTP_401_UNAUTHORIZED)

        token = auth_header.split(' ')[1]
        logger.info(f"Extracted token: {token[:10]}...")

        # Validate token
        from accounts.jwt_utils import validate_jwt_token
        payload = validate_jwt_token(token)
        if not payload:
            logger.warning("Invalid JWT token")
            return Response({"detail": "Invalid JWT token."}, status=status.HTTP_401_UNAUTHORIZED)

        # Extract user ID from payload
        user_id = payload.get('sub')
        if not user_id:
            logger.warning("Invalid JWT token payload - missing 'sub' claim")
            return Response({"detail": "Invalid JWT token payload."}, status=status.HTTP_401_UNAUTHORIZED)

        # Get user from token
        User = get_user_model()
        try:
            user = User.objects.get(id=user_id)
            request.user = user
            logger.info(f"User: {user.email}, ID: {user.id}")
        except User.DoesNotExist:
            logger.error(f"User with ID {user_id} not found")
            return Response({"detail": "User not found."}, status=status.HTTP_401_UNAUTHORIZED)

        # Get the tenant from the request context
        if hasattr(request, 'tenant'):
            # If the request has a tenant attribute, use it
            current_tenant = request.tenant
            logger.info(f"Using tenant from request context: {current_tenant.name}, Schema: {current_tenant.schema_name}")

            # Check if this is a subcity tenant
            if current_tenant.schema_type != 'SUBCITY':
                # If this is not a subcity tenant, try to find its parent subcity
                if current_tenant.schema_type == 'KEBELE' and current_tenant.parent and current_tenant.parent.schema_type == 'SUBCITY':
                    current_tenant = current_tenant.parent
                    logger.info(f"Using parent subcity tenant: {current_tenant.name}, Schema: {current_tenant.schema_name}")
                else:
                    # If we can't find a parent subcity, use any subcity tenant
                    subcity_tenants = Client.objects.filter(schema_type='SUBCITY')
                    if subcity_tenants.exists():
                        current_tenant = subcity_tenants.first()
                        logger.info(f"Using fallback subcity tenant: {current_tenant.name}, Schema: {current_tenant.schema_name}")
                    else:
                        return Response(
                            {"error": "No subcity tenants found"},
                            status=status.HTTP_400_BAD_REQUEST
                        )
        else:
            # If the request doesn't have a tenant attribute, use any subcity tenant
            subcity_tenants = Client.objects.filter(schema_type='SUBCITY')
            if not subcity_tenants.exists():
                return Response(
                    {"error": "No subcity tenants found"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            current_tenant = subcity_tenants.first()
            logger.info(f"Using fallback subcity tenant: {current_tenant.name}, Schema: {current_tenant.schema_name}")

        # Get the child tenant
        try:
            child_tenant = Client.objects.get(schema_name=schema_name, parent=current_tenant)
        except Client.DoesNotExist:
            return Response(
                {"error": f"Tenant with schema {schema_name} does not exist or is not a child of this subcity"},
                status=status.HTTP_404_NOT_FOUND
            )

        # Switch to the child tenant context
        with tenant_context(child_tenant):
            User = get_user_model()
            users = User.objects.all()
            serializer = UserListSerializer(users, many=True)
            return Response(serializer.data)
    except Exception as e:
        logger.error(f"Error getting tenant users: {str(e)}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return Response(
            {"error": f"An error occurred: {str(e)}"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['POST'])
def create_tenant_user(request, schema_name):
    """
    Create a new user in a specific child kebele tenant.
    """
    try:
        # For debugging, log the user information
        logger.info(f"Processing create_tenant_user request for schema {schema_name}")
        logger.info(f"Request method: {request.method}")
        logger.info(f"Request headers: {dict(request.headers)}")

        # Check for Authorization header
        auth_header = request.headers.get('Authorization', '')
        if auth_header:
            logger.info(f"Authorization header: {auth_header[:15]}...")
        else:
            logger.warning("No Authorization header found")
            return Response({"detail": "Authentication credentials were not provided."}, status=status.HTTP_401_UNAUTHORIZED)

        # Extract token
        if not auth_header.startswith('Bearer '):
            logger.error(f"Invalid Authorization header format: {auth_header[:15]}...")
            return Response({"detail": "Invalid Authorization header format. Use 'Bearer <token>'."}, status=status.HTTP_401_UNAUTHORIZED)

        token = auth_header.split(' ')[1]
        logger.info(f"Extracted token: {token[:10]}...")

        # Validate token
        from accounts.jwt_utils import validate_jwt_token
        payload = validate_jwt_token(token)
        if not payload:
            logger.warning("Invalid JWT token")
            return Response({"detail": "Invalid JWT token."}, status=status.HTTP_401_UNAUTHORIZED)

        # Extract user ID from payload
        user_id = payload.get('sub')
        if not user_id:
            logger.warning("Invalid JWT token payload - missing 'sub' claim")
            return Response({"detail": "Invalid JWT token payload."}, status=status.HTTP_401_UNAUTHORIZED)

        # Get user from token
        User = get_user_model()
        try:
            user = User.objects.get(id=user_id)
            request.user = user
            logger.info(f"User: {user.email}, ID: {user.id}")
        except User.DoesNotExist:
            logger.error(f"User with ID {user_id} not found")
            return Response({"detail": "User not found."}, status=status.HTTP_401_UNAUTHORIZED)

        # Get the tenant from the request context
        if hasattr(request, 'tenant'):
            # If the request has a tenant attribute, use it
            current_tenant = request.tenant
            logger.info(f"Using tenant from request context: {current_tenant.name}, Schema: {current_tenant.schema_name}")

            # Check if this is a subcity tenant
            if current_tenant.schema_type != 'SUBCITY':
                # If this is not a subcity tenant, try to find its parent subcity
                if current_tenant.schema_type == 'KEBELE' and current_tenant.parent and current_tenant.parent.schema_type == 'SUBCITY':
                    current_tenant = current_tenant.parent
                    logger.info(f"Using parent subcity tenant: {current_tenant.name}, Schema: {current_tenant.schema_name}")
                else:
                    # If we can't find a parent subcity, use any subcity tenant
                    subcity_tenants = Client.objects.filter(schema_type='SUBCITY')
                    if subcity_tenants.exists():
                        current_tenant = subcity_tenants.first()
                        logger.info(f"Using fallback subcity tenant: {current_tenant.name}, Schema: {current_tenant.schema_name}")
                    else:
                        return Response(
                            {"error": "No subcity tenants found"},
                            status=status.HTTP_400_BAD_REQUEST
                        )
        else:
            # If the request doesn't have a tenant attribute, use any subcity tenant
            subcity_tenants = Client.objects.filter(schema_type='SUBCITY')
            if not subcity_tenants.exists():
                return Response(
                    {"error": "No subcity tenants found"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            current_tenant = subcity_tenants.first()
            logger.info(f"Using fallback subcity tenant: {current_tenant.name}, Schema: {current_tenant.schema_name}")

        # Get the child tenant (kebele)
        try:
            child_tenant = Client.objects.get(schema_name=schema_name)

            # Verify this is a kebele tenant
            if child_tenant.schema_type != 'KEBELE':
                return Response(
                    {"error": f"Tenant {schema_name} is not a kebele tenant"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Verify this kebele is a child of the current subcity
            if child_tenant.parent != current_tenant and child_tenant.parent is not None:
                return Response(
                    {"error": f"Kebele tenant {schema_name} is not a child of this subcity"},
                    status=status.HTTP_403_FORBIDDEN
                )

        except Client.DoesNotExist:
            return Response(
                {"error": f"Tenant with schema {schema_name} does not exist"},
                status=status.HTTP_404_NOT_FOUND
            )

        # Validate the role
        role = request.data.get('role')
        if role not in ['CENTER_STAFF', 'KEBELE_LEADER', 'CENTER_ADMIN']:
            return Response(
                {"error": f"Invalid role: {role}. Must be one of: CENTER_STAFF, KEBELE_LEADER, CENTER_ADMIN"},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Switch to the child tenant context (the specific kebele)
        with tenant_context(child_tenant):
            User = get_user_model()

            # Check if user with this email already exists in this kebele
            email = request.data.get('email')
            if User.objects.filter(email=email).exists():
                return Response(
                    {"error": f"User with email {email} already exists in kebele {child_tenant.name}"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Get the center for this kebele
            try:
                centers = Center.objects.all()
                if not centers.exists():
                    return Response(
                        {"error": f"No centers found in kebele {child_tenant.name}"},
                        status=status.HTTP_400_BAD_REQUEST
                    )
                center = centers.first()
            except Exception as e:
                return Response(
                    {"error": f"Error getting center: {str(e)}"},
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR
                )

            # Create the user in the kebele's schema
            serializer = UserSerializer(data=request.data)
            if serializer.is_valid():
                # Add center to validated data
                serializer.validated_data['center'] = center

                # Get password from request data
                password = request.data.get('password')
                if not password:
                    return Response(
                        {"error": "Password is required"},
                        status=status.HTTP_400_BAD_REQUEST
                    )

                # Get username from request data or extract from email
                email = serializer.validated_data['email']
                username = request.data.get('username')

                # If username is not provided, extract it from email
                if not username:
                    username = email.split('@')[0]
                    logger.info(f"Generated username '{username}' from email '{email}'")
                else:
                    logger.info(f"Using provided username '{username}'")

                # If child_tenant has a custom_domain, append it to the username
                if child_tenant.custom_domain:
                    domain_username = f"{username}@{child_tenant.custom_domain}"
                    logger.info(f"Created domain-specific username: {domain_username}")
                else:
                    # If no custom domain, use the schema name as domain
                    domain_username = f"{username}@{child_tenant.schema_name}"
                    logger.info(f"Created schema-based username: {domain_username}")

                # Log the user creation details
                logger.info(f"Creating user with email: {email}, username: {domain_username}, role: {serializer.validated_data.get('role', 'CENTER_STAFF')}")

                try:
                    # Create the user in the kebele's schema
                    user = User.objects.create_user(
                        email=serializer.validated_data['email'],
                        password=password,
                        first_name=serializer.validated_data.get('first_name', ''),
                        last_name=serializer.validated_data.get('last_name', ''),
                        role=serializer.validated_data.get('role', 'CENTER_STAFF'),
                        center=center,
                        is_active=True,
                        username=domain_username  # Set the domain-specific username
                    )
                    logger.info(f"Successfully created user with ID: {user.id}, email: {user.email}, username: {user.username}")
                except Exception as e:
                    logger.error(f"Error creating user: {str(e)}")
                    return Response(
                        {"error": f"Error creating user: {str(e)}"},
                        status=status.HTTP_500_INTERNAL_SERVER_ERROR
                    )

                # Return success response with tenant information and username details
                return Response({
                    'user': UserSerializer(user).data,
                    'tenant': {
                        'id': child_tenant.id,
                        'name': child_tenant.name,
                        'schema_name': child_tenant.schema_name,
                        'schema_type': child_tenant.schema_type
                    },
                    'username_info': {
                        'email': email,
                        'username': domain_username,
                        'login_instructions': f"The user can log in with the username '{domain_username}' and the provided password."
                    },
                    'message': f"User created successfully in kebele {child_tenant.name} with role {role}. Username '{domain_username}' was automatically generated."
                }, status=status.HTTP_201_CREATED)
            else:
                return Response(
                    serializer.errors,
                    status=status.HTTP_400_BAD_REQUEST
                )
    except Exception as e:
        logger.error(f"Error creating tenant user: {str(e)}")
        logger.error(f"Traceback: {traceback.format_exc()}")
        return Response(
            {"error": f"An error occurred: {str(e)}"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['PUT'])
def update_tenant_user(request, schema_name, user_id):
    """
    Update a user in a specific child kebele tenant.
    """
    try:
        # For debugging, log the user information
        logger.info(f"Processing update_tenant_user request for schema {schema_name}, user_id {user_id}")
        logger.info(f"Request method: {request.method}")
        logger.info(f"Request headers: {dict(request.headers)}")

        # Check for Authorization header
        auth_header = request.headers.get('Authorization', '')
        if auth_header:
            logger.info(f"Authorization header: {auth_header[:15]}...")
        else:
            logger.warning("No Authorization header found")
            return Response({"detail": "Authentication credentials were not provided."}, status=status.HTTP_401_UNAUTHORIZED)

        # Extract token
        if not auth_header.startswith('Bearer '):
            logger.error(f"Invalid Authorization header format: {auth_header[:15]}...")
            return Response({"detail": "Invalid Authorization header format. Use 'Bearer <token>'."}, status=status.HTTP_401_UNAUTHORIZED)

        token = auth_header.split(' ')[1]
        logger.info(f"Extracted token: {token[:10]}...")

        # Validate token
        from accounts.jwt_utils import validate_jwt_token
        payload = validate_jwt_token(token)
        if not payload:
            logger.warning("Invalid JWT token")
            return Response({"detail": "Invalid JWT token."}, status=status.HTTP_401_UNAUTHORIZED)

        # Extract user ID from payload
        user_id_from_token = payload.get('sub')
        if not user_id_from_token:
            logger.warning("Invalid JWT token payload - missing 'sub' claim")
            return Response({"detail": "Invalid JWT token payload."}, status=status.HTTP_401_UNAUTHORIZED)

        # Get user from token
        User = get_user_model()
        try:
            user = User.objects.get(id=user_id_from_token)
            request.user = user
            logger.info(f"User: {user.email}, ID: {user.id}")
        except User.DoesNotExist:
            logger.error(f"User with ID {user_id_from_token} not found")
            return Response({"detail": "User not found."}, status=status.HTTP_401_UNAUTHORIZED)

        # Get the tenant from the request context
        if hasattr(request, 'tenant'):
            # If the request has a tenant attribute, use it
            current_tenant = request.tenant
            logger.info(f"Using tenant from request context: {current_tenant.name}, Schema: {current_tenant.schema_name}")

            # Check if this is a subcity tenant
            if current_tenant.schema_type != 'SUBCITY':
                # If this is not a subcity tenant, try to find its parent subcity
                if current_tenant.schema_type == 'KEBELE' and current_tenant.parent and current_tenant.parent.schema_type == 'SUBCITY':
                    current_tenant = current_tenant.parent
                    logger.info(f"Using parent subcity tenant: {current_tenant.name}, Schema: {current_tenant.schema_name}")
                else:
                    # If we can't find a parent subcity, use any subcity tenant
                    subcity_tenants = Client.objects.filter(schema_type='SUBCITY')
                    if subcity_tenants.exists():
                        current_tenant = subcity_tenants.first()
                        logger.info(f"Using fallback subcity tenant: {current_tenant.name}, Schema: {current_tenant.schema_name}")
                    else:
                        return Response(
                            {"error": "No subcity tenants found"},
                            status=status.HTTP_400_BAD_REQUEST
                        )
        else:
            # If the request doesn't have a tenant attribute, use any subcity tenant
            subcity_tenants = Client.objects.filter(schema_type='SUBCITY')
            if not subcity_tenants.exists():
                return Response(
                    {"error": "No subcity tenants found"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            current_tenant = subcity_tenants.first()
            logger.info(f"Using fallback subcity tenant: {current_tenant.name}, Schema: {current_tenant.schema_name}")

        # Get the child tenant (kebele)
        try:
            child_tenant = Client.objects.get(schema_name=schema_name)

            # Verify this is a kebele tenant
            if child_tenant.schema_type != 'KEBELE':
                return Response(
                    {"error": f"Tenant {schema_name} is not a kebele tenant"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Verify this kebele is a child of the current subcity
            if child_tenant.parent != current_tenant and child_tenant.parent is not None:
                return Response(
                    {"error": f"Kebele tenant {schema_name} is not a child of this subcity"},
                    status=status.HTTP_403_FORBIDDEN
                )

        except Client.DoesNotExist:
            return Response(
                {"error": f"Tenant with schema {schema_name} does not exist"},
                status=status.HTTP_404_NOT_FOUND
            )

        # Validate the role if it's being updated
        if 'role' in request.data:
            role = request.data.get('role')
            if role not in ['CENTER_STAFF', 'KEBELE_LEADER', 'CENTER_ADMIN']:
                return Response(
                    {"error": f"Invalid role: {role}. Must be one of: CENTER_STAFF, KEBELE_LEADER, CENTER_ADMIN"},
                    status=status.HTTP_400_BAD_REQUEST
                )

        # Switch to the child tenant context (the specific kebele)
        with tenant_context(child_tenant):
            User = get_user_model()

            # Get the user to update
            try:
                user_to_update = get_object_or_404(User, id=user_id)
            except Exception as e:
                return Response(
                    {"error": f"User with ID {user_id} not found in tenant {child_tenant.name}: {str(e)}"},
                    status=status.HTTP_404_NOT_FOUND
                )

            # Check if email is being changed and if it already exists
            if 'email' in request.data and request.data['email'] != user_to_update.email:
                if User.objects.filter(email=request.data['email']).exists():
                    return Response(
                        {"error": f"User with email {request.data['email']} already exists in tenant {child_tenant.name}"},
                        status=status.HTTP_400_BAD_REQUEST
                    )

            # Update the user
            serializer = UserSerializer(user_to_update, data=request.data, partial=True)
            if serializer.is_valid():
                # Update user fields
                if 'first_name' in serializer.validated_data:
                    user_to_update.first_name = serializer.validated_data['first_name']
                if 'last_name' in serializer.validated_data:
                    user_to_update.last_name = serializer.validated_data['last_name']
                if 'email' in serializer.validated_data:
                    user_to_update.email = serializer.validated_data['email']
                if 'role' in serializer.validated_data:
                    user_to_update.role = serializer.validated_data['role']
                if 'is_active' in serializer.validated_data:
                    user_to_update.is_active = serializer.validated_data['is_active']

                # Update password if provided
                if 'password' in request.data and request.data['password']:
                    user_to_update.set_password(request.data['password'])

                # Save the updated user
                user_to_update.save()

                # Return success response with tenant information
                return Response({
                    'user': UserSerializer(user_to_update).data,
                    'tenant': {
                        'id': child_tenant.id,
                        'name': child_tenant.name,
                        'schema_name': child_tenant.schema_name,
                        'schema_type': child_tenant.schema_type
                    },
                    'message': f"User updated successfully in kebele {child_tenant.name}"
                }, status=status.HTTP_200_OK)
            else:
                return Response(
                    serializer.errors,
                    status=status.HTTP_400_BAD_REQUEST
                )
    except Exception as e:
        logger.error(f"Error updating tenant user: {str(e)}")
        logger.error(f"Traceback: {traceback.format_exc()}")
        return Response(
            {"error": f"An error occurred: {str(e)}"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['DELETE'])
def delete_tenant_user(request, schema_name, user_id):
    """
    Delete a user from a specific child kebele tenant.
    """
    try:
        # For debugging, log the user information
        logger.info(f"Processing delete_tenant_user request for schema {schema_name}, user_id {user_id}")
        logger.info(f"Request method: {request.method}")
        logger.info(f"Request headers: {dict(request.headers)}")

        # Check for Authorization header
        auth_header = request.headers.get('Authorization', '')
        if auth_header:
            logger.info(f"Authorization header: {auth_header[:15]}...")
        else:
            logger.warning("No Authorization header found")
            return Response({"detail": "Authentication credentials were not provided."}, status=status.HTTP_401_UNAUTHORIZED)

        # Extract token
        if not auth_header.startswith('Bearer '):
            logger.error(f"Invalid Authorization header format: {auth_header[:15]}...")
            return Response({"detail": "Invalid Authorization header format. Use 'Bearer <token>'."}, status=status.HTTP_401_UNAUTHORIZED)

        token = auth_header.split(' ')[1]
        logger.info(f"Extracted token: {token[:10]}...")

        # Validate token
        from accounts.jwt_utils import validate_jwt_token
        payload = validate_jwt_token(token)
        if not payload:
            logger.warning("Invalid JWT token")
            return Response({"detail": "Invalid JWT token."}, status=status.HTTP_401_UNAUTHORIZED)

        # Extract user ID from payload
        user_id_from_token = payload.get('sub')
        if not user_id_from_token:
            logger.warning("Invalid JWT token payload - missing 'sub' claim")
            return Response({"detail": "Invalid JWT token payload."}, status=status.HTTP_401_UNAUTHORIZED)

        # Get user from token
        User = get_user_model()
        try:
            user = User.objects.get(id=user_id_from_token)
            request.user = user
            logger.info(f"User: {user.email}, ID: {user.id}")
        except User.DoesNotExist:
            logger.error(f"User with ID {user_id_from_token} not found")
            return Response({"detail": "User not found."}, status=status.HTTP_401_UNAUTHORIZED)

        # Get the tenant from the request context
        if hasattr(request, 'tenant'):
            # If the request has a tenant attribute, use it
            current_tenant = request.tenant
            logger.info(f"Using tenant from request context: {current_tenant.name}, Schema: {current_tenant.schema_name}")

            # Check if this is a subcity tenant
            if current_tenant.schema_type != 'SUBCITY':
                # If this is not a subcity tenant, try to find its parent subcity
                if current_tenant.schema_type == 'KEBELE' and current_tenant.parent and current_tenant.parent.schema_type == 'SUBCITY':
                    current_tenant = current_tenant.parent
                    logger.info(f"Using parent subcity tenant: {current_tenant.name}, Schema: {current_tenant.schema_name}")
                else:
                    # If we can't find a parent subcity, use any subcity tenant
                    subcity_tenants = Client.objects.filter(schema_type='SUBCITY')
                    if subcity_tenants.exists():
                        current_tenant = subcity_tenants.first()
                        logger.info(f"Using fallback subcity tenant: {current_tenant.name}, Schema: {current_tenant.schema_name}")
                    else:
                        return Response(
                            {"error": "No subcity tenants found"},
                            status=status.HTTP_400_BAD_REQUEST
                        )
        else:
            # If the request doesn't have a tenant attribute, use any subcity tenant
            subcity_tenants = Client.objects.filter(schema_type='SUBCITY')
            if not subcity_tenants.exists():
                return Response(
                    {"error": "No subcity tenants found"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            current_tenant = subcity_tenants.first()
            logger.info(f"Using fallback subcity tenant: {current_tenant.name}, Schema: {current_tenant.schema_name}")

        # Get the child tenant (kebele)
        try:
            child_tenant = Client.objects.get(schema_name=schema_name)

            # Verify this is a kebele tenant
            if child_tenant.schema_type != 'KEBELE':
                return Response(
                    {"error": f"Tenant {schema_name} is not a kebele tenant"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Verify this kebele is a child of the current subcity
            if child_tenant.parent != current_tenant and child_tenant.parent is not None:
                return Response(
                    {"error": f"Kebele tenant {schema_name} is not a child of this subcity"},
                    status=status.HTTP_403_FORBIDDEN
                )

        except Client.DoesNotExist:
            return Response(
                {"error": f"Tenant with schema {schema_name} does not exist"},
                status=status.HTTP_404_NOT_FOUND
            )

        # Switch to the child tenant context (the specific kebele)
        with tenant_context(child_tenant):
            User = get_user_model()

            # Get the user to delete
            try:
                user_to_delete = get_object_or_404(User, id=user_id)
            except Exception as e:
                return Response(
                    {"error": f"User with ID {user_id} not found in tenant {child_tenant.name}: {str(e)}"},
                    status=status.HTTP_404_NOT_FOUND
                )

            # Store user info for response
            user_info = {
                'id': user_to_delete.id,
                'email': user_to_delete.email,
                'first_name': user_to_delete.first_name,
                'last_name': user_to_delete.last_name,
                'role': user_to_delete.role
            }

            # Delete the user
            user_to_delete.delete()

            # Return success response
            return Response({
                'user': user_info,
                'tenant': {
                    'id': child_tenant.id,
                    'name': child_tenant.name,
                    'schema_name': child_tenant.schema_name,
                    'schema_type': child_tenant.schema_type
                },
                'message': f"User deleted successfully from kebele {child_tenant.name}"
            }, status=status.HTTP_200_OK)
    except Exception as e:
        logger.error(f"Error deleting tenant user: {str(e)}")
        logger.error(f"Traceback: {traceback.format_exc()}")
        return Response(
            {"error": f"An error occurred: {str(e)}"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )
