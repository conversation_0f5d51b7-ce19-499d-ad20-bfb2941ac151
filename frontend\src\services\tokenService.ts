/**
 * Token Service
 *
 * This service provides a centralized way to manage JWT tokens in the application.
 * It handles token storage, retrieval, and management for all schemas.
 *
 * This is a consolidated service that replaces:
 * - unifiedTokenService.ts
 * - tokenManager.ts
 * - unifiedTokenManager.ts
 *
 * NOTE: This service now uses the tokenStorage module for all token storage operations.
 */

import { API_BASE_URL } from '../config/apiConfig';
import * as tokenStorage from './tokenStorage';

// Token response interface
export interface JWTTokenResponse {
  access_token: string;
  refresh_token: string;
  user?: any;
  tenant?: any;
}

// Token store interface
interface TokenStore {
  [schema: string]: {
    accessToken: string;
    refreshToken: string;
    expiresAt?: number;
  };
}

// In-memory token store
let tokenStore: TokenStore = {};

// Function to redirect to login page
const redirectToLogin = () => {
  console.log('Redirecting to login page due to authentication failure');

  // Use the global navigate function if available
  if (window && (window as any).__navigateFunction) {
    (window as any).__navigateFunction('/login');
    return;
  }

  // Fallback to window.location if navigate function is not available
  window.location.href = '/login';
};

/**
 * Initialize the token store from localStorage
 */
const initializeTokenStore = (): void => {
  try {
    // Get from localStorage
    const storedTokens = localStorage.getItem('jwtTokenStore');

    if (storedTokens) {
      tokenStore = JSON.parse(storedTokens);
      console.log('Token store loaded from localStorage');
    }

    // Also try to migrate tokens from legacy storage
    migrateFromLegacyStorage();
  } catch (error) {
    console.error('Error initializing token store:', error);
  }
};

/**
 * Migrate tokens from legacy storage
 *
 * This function is now a no-op as we've completely removed legacy token authentication.
 */
const migrateFromLegacyStorage = (): void => {
  // No-op: Legacy token authentication has been completely removed
};

/**
 * Save the token store to localStorage
 */
const saveTokenStore = (): void => {
  try {
    localStorage.setItem('jwtTokenStore', JSON.stringify(tokenStore));
  } catch (error) {
    console.error('Error saving token store to localStorage:', error);
  }
};

/**
 * Clean a token by removing any prefixes
 * @param token The token to clean
 * @returns The cleaned token
 */
const cleanToken = (token: string): string => {
  if (token.startsWith('Bearer ')) {
    return token.substring(7);
  }
  return token;
};

/**
 * Store tokens for a specific schema
 * @param schema The schema name
 * @param accessToken The access token
 * @param refreshToken The refresh token
 * @param expiresIn Optional expiration time in seconds
 */
export const storeTokensForSchema = (
  schema: string,
  accessToken: string,
  refreshToken: string,
  expiresIn?: number
): void => {
  if (!schema) {
    console.error('Cannot store tokens: schema is missing');
    return;
  }

  // Clean tokens
  const cleanAccessToken = cleanToken(accessToken);
  const cleanRefreshToken = cleanToken(refreshToken);

  // Set current schema
  tokenStorage.setCurrentSchema(schema);

  // Store tokens
  tokenStorage.setAccessToken(cleanAccessToken, schema);
  tokenStorage.setRefreshToken(cleanRefreshToken, schema);

  console.log(`Stored tokens for schema: ${schema}`);
};

/**
 * Store the current schema
 * @param schema The schema name
 */
export const storeCurrentSchema = (schema: string): void => {
  tokenStorage.setCurrentSchema(schema);
};

/**
 * Get the current schema
 * @returns The current schema or null if not found
 */
export const getCurrentSchema = (): string | null => {
  return tokenStorage.getCurrentSchema();
};

/**
 * Check if a token refresh is in progress for a schema
 * @param schema The schema to check
 * @returns True if a refresh is in progress
 */
export const isTokenRefreshInProgress = (schema: string): boolean => {
  if (!schema) return false;

  // Format schema for consistency
  const formattedSchema = tokenStorage.formatSchema(schema);
  return !!refreshInProgress[formattedSchema];
};

/**
 * Get access token for a specific schema
 * @param schema The schema name
 * @returns The access token or null if not found
 */
export const getAccessTokenForSchema = (schema: string): string | null => {
  return tokenStorage.getAccessToken(schema);
};

/**
 * Get refresh token for a specific schema
 * @param schema The schema name
 * @returns The refresh token or null if not found
 */
export const getRefreshTokenForSchema = (schema: string): string | null => {
  return tokenStorage.getRefreshToken(schema);
};

/**
 * Clear access token for a specific schema
 * @param schema The schema name
 */
export const clearAccessTokenForSchema = (schema: string): void => {
  if (!schema) {
    console.error('Cannot clear access token: schema is missing');
    return;
  }

  // Remove from token store
  if (tokenStore[schema]) {
    tokenStore[schema].accessToken = '';
    saveTokenStore();
  }

  // Remove from localStorage
  localStorage.removeItem(`jwt_access_token_${schema}`);

  // If this is the current schema, clear the current token too
  const currentSchema = getCurrentSchema();
  if (currentSchema === schema) {
    localStorage.removeItem('jwt_access_token');
  }
};

/**
 * Clear tokens for a specific schema
 * @param schema The schema name
 */
export const clearTokensForSchema = (schema: string): void => {
  tokenStorage.clearTokens(schema);
};

/**
 * Clear all tokens
 */
export const clearAllTokens = (): void => {
  // Get all schemas from localStorage
  const schemas: string[] = [];

  // Find all schema-specific tokens
  for (const key of Object.keys(localStorage)) {
    if (key.startsWith('jwt_access_token_') || key.startsWith('jwt_refresh_token_')) {
      const schema = key.split('_').slice(3).join('_');
      if (schema && !schemas.includes(schema)) {
        schemas.push(schema);
      }
    }
  }

  // Clear tokens for each schema
  schemas.forEach(schema => {
    tokenStorage.clearTokens(schema);
  });

  // Clear current schema
  const currentSchema = tokenStorage.getCurrentSchema();
  if (currentSchema) {
    tokenStorage.clearTokens(currentSchema);
  }

  // Clear user and tenant info
  localStorage.removeItem('user');
  localStorage.removeItem('tenant');
  localStorage.removeItem('user_role');
  localStorage.removeItem('tenant_type');

  // Clear sessionStorage for completeness
  sessionStorage.removeItem('user');
  sessionStorage.removeItem('tenant');

  console.log('Cleared all tokens');
};

/**
 * Get authentication headers for API requests
 * @param schema The schema name
 * @returns Headers object with authentication
 */
export const getAuthHeaders = (schema?: string): Record<string, string> => {
  return tokenStorage.getAuthHeaders(schema);
};

/**
 * Login with JWT
 * @param email The user's email
 * @param password The user's password
 * @param schema Optional schema name
 * @returns The JWT token response
 */
export const loginWithJWT = async (
  email: string,
  password: string,
  schema?: string
): Promise<JWTTokenResponse> => {
  try {
    console.log('Logging in with JWT:', email, 'schema:', schema || 'not provided');

    const response = await fetch(`${API_BASE_URL}/api/jwt/login/`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        ...(schema ? { 'X-Schema-Name': schema } : {})
      },
      body: JSON.stringify({ email, password }),
      credentials: 'include', // Important: include credentials to receive cookies
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Login failed: ${response.status} ${response.statusText} - ${errorText}`);
    }

    const data = await response.json();
    console.log('Login successful, received tokens and cookies');

    // Check if cookies were set
    const cookies = document.cookie;
    console.log('Current cookies:', cookies);

    // Store tokens using the centralized token storage
    if (data.access_token && data.refresh_token) {
      const schemaName = data.tenant?.schema_name || schema || 'default';

      // Store tokens
      tokenStorage.setCurrentSchema(schemaName);
      tokenStorage.setAccessToken(data.access_token, schemaName);
      tokenStorage.setRefreshToken(data.refresh_token, schemaName);

      console.log(`Stored tokens for schema ${schemaName} using centralized token storage`);

      // Store user and tenant info
      if (data.user) {
        localStorage.setItem('user', JSON.stringify(data.user));
      }
      if (data.tenant) {
        localStorage.setItem('tenant', JSON.stringify(data.tenant));
      }
    }

    return data;
  } catch (error) {
    console.error('Error logging in with JWT:', error);
    throw error;
  }
};

// Global token refresh lock mechanism
// Track refresh attempts to prevent multiple simultaneous refreshes
let refreshInProgress: { [schema: string]: Promise<JWTTokenResponse> | null } = {};
let lastRefreshTime: { [schema: string]: number } = {};
let refreshCount: { [schema: string]: number } = {}; // Track number of refresh attempts
const MIN_REFRESH_INTERVAL = 10000; // 10 seconds minimum between refresh attempts
const MAX_REFRESH_ATTEMPTS = 3; // Maximum number of refresh attempts in a short period
const REFRESH_ATTEMPT_WINDOW = 60000; // 1 minute window for counting refresh attempts

// Add a global debug object to window for testing
if (typeof window !== 'undefined') {
  (window as any).tokenDebug = {
    refreshInProgress,
    lastRefreshTime,
    refreshCount,
    getRefreshStatus: () => {
      const schema = getCurrentSchema();
      if (!schema) return { inProgress: false, lastTime: null, count: 0 };

      const formattedSchema = schema.replace(/\s+/g, '_');
      return {
        inProgress: !!refreshInProgress[formattedSchema],
        lastTime: lastRefreshTime[formattedSchema] || null,
        count: refreshCount[formattedSchema] || 0,
        schema: formattedSchema
      };
    },
    diagnose: () => {
      const schema = getCurrentSchema();
      if (!schema) {
        console.log('No current schema found');
        return;
      }

      const formattedSchema = schema.replace(/\s+/g, '_');
      console.log('Token Debug Diagnostics:');
      console.log('- Current schema:', formattedSchema);
      console.log('- Refresh in progress:', !!refreshInProgress[formattedSchema]);
      console.log('- Last refresh time:', lastRefreshTime[formattedSchema] ? new Date(lastRefreshTime[formattedSchema]).toISOString() : 'never');
      console.log('- Refresh count:', refreshCount[formattedSchema] || 0);
      console.log('- Access token exists:', !!getAccessTokenForSchema(formattedSchema));
      console.log('- Refresh token exists:', !!getRefreshTokenForSchema(formattedSchema));
    }
  };
}

/**
 * Refresh JWT tokens
 * @param schema The schema name
 * @param force Force refresh even if one is in progress
 * @returns The JWT token response
 */
export const refreshJWTTokens = async (schema: string, force: boolean = false): Promise<JWTTokenResponse> => {
  if (!schema) {
    throw new Error('Cannot refresh tokens: schema is missing');
  }

  // Format schema for consistency
  const formattedSchema = schema.replace(/\s+/g, '_');
  const now = Date.now();

  // Check if a refresh is already in progress for this schema
  if (!force && refreshInProgress[formattedSchema]) {
    console.log(`Token refresh already in progress for schema ${formattedSchema}, returning existing promise`);
    return refreshInProgress[formattedSchema]!;
  }

  // Track refresh attempts to prevent rapid refresh cycles
  const lastRefresh = lastRefreshTime[formattedSchema] || 0;

  // Initialize or update refresh count
  if (!refreshCount[formattedSchema]) {
    refreshCount[formattedSchema] = 1;
  } else if ((now - lastRefresh) > REFRESH_ATTEMPT_WINDOW) {
    // Reset count if outside the window
    refreshCount[formattedSchema] = 1;
  } else {
    // Increment count if within the window
    refreshCount[formattedSchema]++;
  }

  // Log refresh attempt count
  console.log(`Refresh attempt ${refreshCount[formattedSchema]} for schema ${formattedSchema} in the last ${REFRESH_ATTEMPT_WINDOW/1000} seconds`);

  // Check if we've exceeded the maximum number of refresh attempts
  if (!force && refreshCount[formattedSchema] > MAX_REFRESH_ATTEMPTS) {
    console.error(`Too many refresh attempts (${refreshCount[formattedSchema]}) for schema ${formattedSchema} in a short period`);

    // Clear tokens and redirect to login
    clearTokensForSchema(formattedSchema);
    localStorage.setItem('token_expired', 'true');
    localStorage.setItem('token_expired_reason', 'too_many_refresh_attempts');
    localStorage.setItem('token_expired_schema', formattedSchema);
    localStorage.setItem('token_expired_timestamp', Date.now().toString());

    // Redirect to login with a delay
    setTimeout(() => {
      redirectToLogin();
    }, 100);

    throw new Error('Too many token refresh attempts. Please log in again.');
  }

  // Check if we've refreshed too recently
  if (!force && (now - lastRefresh) < MIN_REFRESH_INTERVAL) {
    console.log(`Token was refreshed too recently for schema ${formattedSchema}, waiting...`);

    // Instead of waiting, throw a specific error that can be caught by the resilient API client
    throw new Error(`Token was refreshed too recently for schema ${formattedSchema}. Please try again in ${Math.ceil((MIN_REFRESH_INTERVAL - (now - lastRefresh))/1000)} seconds.`);

    // The following code is commented out because we're now throwing an error instead of waiting
    // await new Promise(resolve => setTimeout(resolve, MIN_REFRESH_INTERVAL - (now - lastRefresh)));
  }

  // Create a refresh promise and store it
  const refreshPromise = (async () => {
    try {
      // Record the start time of this refresh attempt
      lastRefreshTime[formattedSchema] = Date.now();

      // DIAGNOSTIC: Check for schema mismatches
      console.log('TOKEN REFRESH SCHEMA DIAGNOSTIC:');
      console.log('- Schema passed to refreshJWTTokens:', schema);
      console.log('- Formatted schema:', formattedSchema);
      console.log('- localStorage.getItem("schema_name"):', localStorage.getItem('schema_name'));
      console.log('- localStorage.getItem("jwt_schema"):', localStorage.getItem('jwt_schema'));
      console.log('- Current cookies:', document.cookie);

      // DIAGNOSTIC: Check if we have tokens
      const diagnosticAccessToken = getAccessTokenForSchema(formattedSchema);
      const diagnosticRefreshToken = getRefreshTokenForSchema(formattedSchema);
      console.log('- Has access token:', !!diagnosticAccessToken);
      console.log('- Has refresh token:', !!diagnosticRefreshToken);

      // If we don't have an access token but have a refresh token, this is a good sign
      // that we need to refresh the token
      if (!diagnosticAccessToken && diagnosticRefreshToken) {
        console.log('Missing access token but have refresh token - good candidate for refresh');
      }

    // Check if there's a mismatch
    if (formattedSchema !== localStorage.getItem('schema_name') ||
        formattedSchema !== localStorage.getItem('jwt_schema')) {
      console.warn('SCHEMA MISMATCH DETECTED in token refresh!');

      // Store the formatted schema for consistency
      localStorage.setItem('jwt_schema', formattedSchema);
      localStorage.setItem('schema_name', formattedSchema);
    }

    console.log(`Refreshing JWT tokens for schema: ${formattedSchema}`);

    // Check if we have refresh token cookies - prioritize refresh_token as it's more stable
    const hasRefreshTokenCookie = document.cookie.includes('refresh_token');
    const hasJwtRefreshTokenCookie = document.cookie.includes('jwt_refresh_token');
    const hasCookie = hasRefreshTokenCookie || hasJwtRefreshTokenCookie;

    console.log('Has refresh_token cookie (primary):', hasRefreshTokenCookie);
    console.log('Has jwt_refresh_token cookie (secondary):', hasJwtRefreshTokenCookie);
    console.log('Has any refresh token cookie:', hasCookie);

    // Get refresh token from centralized storage
    const refreshToken = tokenStorage.getRefreshToken(formattedSchema);
    console.log('Has refresh token in storage:', !!refreshToken);

    // Check all possible token sources for debugging
    console.log('DETAILED TOKEN SOURCE CHECK:');
    console.log('- localStorage jwt_refresh_token:', localStorage.getItem('jwt_refresh_token') ? 'Present' : 'Missing');
    console.log('- localStorage refresh_token:', localStorage.getItem('refresh_token') ? 'Present' : 'Missing');
    console.log(`- localStorage jwt_refresh_token_${formattedSchema}:`, localStorage.getItem(`jwt_refresh_token_${formattedSchema}`) ? 'Present' : 'Missing');
    console.log(`- localStorage refresh_token_${formattedSchema}:`, localStorage.getItem(`refresh_token_${formattedSchema}`) ? 'Present' : 'Missing');
    console.log('- sessionStorage jwt_refresh_token:', sessionStorage.getItem('jwt_refresh_token') ? 'Present' : 'Missing');
    console.log('- sessionStorage refresh_token:', sessionStorage.getItem('refresh_token') ? 'Present' : 'Missing');

    // Log all cookies for debugging
    console.log('All cookies:');
    document.cookie.split(';').forEach(cookie => {
      console.log(`- ${cookie.trim()}`);
    });

    // If we don't have a refresh token in either cookie or storage, try to set it
    if (!hasCookie && !refreshToken) {
      console.warn('No refresh token available in cookie or storage. Checking if we can set it...');

      // Try to get the refresh token from legacy storage
      const refreshTokenFromStorage = localStorage.getItem(`jwt_refresh_token_${formattedSchema}`);
      if (refreshTokenFromStorage) {
        console.log('Found refresh token in legacy storage, setting cookie...');
        document.cookie = `jwt_refresh_token=${encodeURIComponent(refreshTokenFromStorage)}; path=/; SameSite=Lax`;

        // Also store it in the centralized storage
        tokenStorage.setRefreshToken(refreshTokenFromStorage, formattedSchema);

        // Get access token if available
        const accessToken = localStorage.getItem(`jwt_access_token_${formattedSchema}`);
        if (accessToken) {
          tokenStorage.setAccessToken(accessToken, formattedSchema);
        }
      }
    }

    // Check if we have a refresh token in any form before proceeding
    if (!hasCookie && !refreshToken) {
      console.error(`No refresh token available for schema ${formattedSchema} in any storage location`);

      // Clear tokens for this schema to ensure a clean state
      clearTokensForSchema(formattedSchema);

      // Set a flag to indicate that the user needs to log in again
      localStorage.setItem('token_expired', 'true');
      localStorage.setItem('token_expired_reason', 'missing_refresh_token');

      // Redirect to login page
      redirectToLogin();

      throw new Error('No refresh token available. Please log in again.');
    }

    // Make the refresh request
    let response;
    let refreshTokenUsed = '';

    try {
      // Try cookies first, then localStorage, then direct token
      if (hasCookie) {
        console.log('Using refresh token from cookie');

        // PRIORITIZE refresh_token cookie as it's more stable
        if (hasRefreshTokenCookie) {
          console.log('Using refresh_token cookie (primary)');

          // Extract the refresh token from the cookie
          let refreshTokenFromCookie = '';
          document.cookie.split(';').forEach(cookie => {
            const [name, value] = cookie.trim().split('=');
            if (name === 'refresh_token') {
              refreshTokenFromCookie = decodeURIComponent(value);
            }
          });

          // Make the refresh request with the token in the body
          response = await fetch(`${API_BASE_URL}/api/jwt/refresh-token/`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'X-Schema-Name': formattedSchema
            },
            body: JSON.stringify({ refresh_token: refreshTokenFromCookie }),
            credentials: 'include', // Still include credentials to receive cookies
          });
          refreshTokenUsed = 'refresh_token_cookie';
        }
        // Fallback to jwt_refresh_token cookie if refresh_token is not available
        else if (hasJwtRefreshTokenCookie) {
          console.log('Using jwt_refresh_token cookie (fallback)');
          // Make the refresh request using HttpOnly cookie
          // The refresh token will be sent automatically in the cookie
          response = await fetch(`${API_BASE_URL}/api/jwt/refresh-token/`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'X-Schema-Name': formattedSchema
            },
            // Empty body since we're using the cookie
            body: JSON.stringify({}),
            credentials: 'include', // Important: include credentials to send cookies
          });
          refreshTokenUsed = 'jwt_refresh_token_cookie';
        }
      } else if (refreshToken === 'REFRESH_TOKEN_COOKIE') {
        console.log('Using refresh_token cookie placeholder from storage');

        // Extract the refresh token from the cookie
        let refreshTokenFromCookie = '';
        document.cookie.split(';').forEach(cookie => {
          const [name, value] = cookie.trim().split('=');
          if (name === 'refresh_token') {
            refreshTokenFromCookie = decodeURIComponent(value);
          }
        });

        if (!refreshTokenFromCookie) {
          console.error('refresh_token cookie placeholder found in storage but actual cookie is missing');
          throw new Error('SESSION_EXPIRED');
        }

        // Make the refresh request with the token in the body
        response = await fetch(`${API_BASE_URL}/api/jwt/refresh-token/`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'X-Schema-Name': formattedSchema
          },
          body: JSON.stringify({ refresh_token: refreshTokenFromCookie }),
          credentials: 'include', // Still include credentials to receive cookies
        });
        refreshTokenUsed = 'refresh_token_cookie_placeholder';
      } else if (refreshToken === 'HTTPONLY_COOKIE') {
        console.log('Using jwt_refresh_token cookie placeholder from storage');

        // Make the refresh request using HttpOnly cookie
        // The refresh token will be sent automatically in the cookie
        response = await fetch(`${API_BASE_URL}/api/jwt/refresh-token/`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'X-Schema-Name': formattedSchema
          },
          // Empty body since we're using the cookie
          body: JSON.stringify({}),
          credentials: 'include', // Important: include credentials to send cookies
        });
        refreshTokenUsed = 'jwt_refresh_token_cookie_placeholder';
      } else if (refreshToken) {
        console.log('No cookie found, using refresh token from storage');
        // Make the refresh request with the token in the body
        response = await fetch(`${API_BASE_URL}/api/jwt/refresh-token/`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'X-Schema-Name': formattedSchema
          },
          body: JSON.stringify({ refresh_token: refreshToken }),
          credentials: 'include', // Still include credentials to receive cookies
        });
        refreshTokenUsed = 'storage';
      } else {
        // Last resort - try to get the refresh token from any available source
        const refreshTokenFromStorage = localStorage.getItem(`jwt_refresh_token_${formattedSchema}`) ||
                                      localStorage.getItem('jwt_refresh_token');

        if (refreshTokenFromStorage) {
          console.log('Using refresh token from alternative storage');
          response = await fetch(`${API_BASE_URL}/api/jwt/refresh-token/`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'X-Schema-Name': formattedSchema
            },
            body: JSON.stringify({ refresh_token: refreshTokenFromStorage }),
            credentials: 'include',
          });
          refreshTokenUsed = 'alternative';
        } else {
          console.error('No refresh token available (neither in cookie nor any localStorage location)');
          throw new Error('No refresh token available. Please log in again.');
        }
      }

      console.log(`Refresh token request completed with status: ${response.status}, using ${refreshTokenUsed} token`);
    } catch (fetchError) {
      console.error('Network error during token refresh:', fetchError);
      throw new Error(`Network error during token refresh: ${fetchError.message}`);
    }

    if (!response.ok) {
      let errorText;
      try {
        errorText = await response.text();
        // Log the full error response for debugging
        console.log('DETAILED TOKEN REFRESH ERROR:');
        console.log('- Status:', response.status, response.statusText);
        console.log('- Response body:', errorText);
        console.log('- Schema used:', formattedSchema);
        console.log('- Refresh token source:', refreshTokenUsed);
        console.log('- Headers sent:', {
          'Content-Type': 'application/json',
          'X-Schema-Name': formattedSchema
        });

        // Try to parse the error as JSON for more details
        try {
          const errorJson = JSON.parse(errorText);
          console.log('- Parsed error JSON:', errorJson);
        } catch (jsonError) {
          console.log('- Error response is not valid JSON');
        }
      } catch (e) {
        errorText = 'Could not read error response';
      }
      console.error(`Token refresh failed: ${response.status} ${response.statusText} - ${errorText}`);

      // Check if this is an invalid refresh token error
      if (response.status === 401 || (errorText && errorText.includes('Invalid refresh token'))) {
        console.log('Invalid refresh token detected. User needs to log in again.');

        // Use TokenManager to handle invalid refresh token
        import('./tokenManager').then(({ tokenManager }) => {
          tokenManager.handleInvalidRefreshToken(formattedSchema);
        }).catch(error => {
          console.error('Error importing tokenManager:', error);

          // Fallback to traditional token clearing if TokenManager fails
          // Clear all tokens for this schema
          clearTokensForSchema(formattedSchema);

          // Clear cookies
          document.cookie = 'jwt_refresh_token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT; SameSite=Lax';
          document.cookie = 'refresh_token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT; SameSite=Lax';

          // Set a flag to indicate that the user needs to log in again
          localStorage.setItem('token_expired', 'true');
          localStorage.setItem('token_expired_reason', 'invalid_refresh_token');
          localStorage.setItem('token_expired_schema', formattedSchema);
          localStorage.setItem('token_expired_timestamp', Date.now().toString());
        });

        // Don't automatically redirect - let the component handle it
        // This allows for a better user experience with a clear message and login button

        // Throw a specific error that can be caught by the calling code
        if (errorText && errorText.includes('Invalid refresh token')) {
          throw new Error('INVALID_REFRESH_TOKEN');
        } else {
          throw new Error('SESSION_EXPIRED');
        }
      }

      // If the error is related to the schema, try to extract the correct schema
      if (errorText.includes('schema') || errorText.includes('tenant')) {
        console.log('Schema-related error detected, trying to find correct schema');

        // Try to get the schema from the tenant object
        try {
          const tenantStr = localStorage.getItem('tenant');
          if (tenantStr) {
            const tenant = JSON.parse(tenantStr);
            if (tenant && tenant.schema_name && tenant.schema_name !== schema) {
              const altSchema = tenant.schema_name;
              console.log(`Trying refresh with schema from tenant: ${altSchema}`);

              // Try with the tenant schema
              try {
                const altRefreshToken = getRefreshTokenForSchema(altSchema);
                if (altRefreshToken) {
                  const altResponse = await fetch(`${API_BASE_URL}/api/jwt/refresh-token/`, {
                    method: 'POST',
                    headers: {
                      'Content-Type': 'application/json',
                      'X-Schema-Name': altSchema
                    },
                    body: JSON.stringify({ refresh_token: altRefreshToken }),
                    credentials: 'include',
                  });

                  if (altResponse.ok) {
                    const altData = await altResponse.json();

                    if (altData.access_token && altData.refresh_token) {
                      console.log(`Successfully refreshed token using tenant schema: ${altSchema}`);

                      // Store tokens for both schemas using centralized storage
                      tokenStorage.setAccessToken(altData.access_token, schema);
                      tokenStorage.setRefreshToken(altData.refresh_token, schema);
                      tokenStorage.setAccessToken(altData.access_token, altSchema);
                      tokenStorage.setRefreshToken(altData.refresh_token, altSchema);

                      // Update current schema
                      tokenStorage.setCurrentSchema(altSchema);

                      return altData;
                    }
                  } else if (altResponse.status === 401) {
                    // If alternative schema also fails with 401, we should redirect to login
                    console.log('Alternative schema refresh also failed with 401. User needs to log in again.');
                    clearTokensForSchema(altSchema);
                    localStorage.setItem('token_expired', 'true');
                    redirectToLogin();
                    throw new Error('SESSION_EXPIRED');
                  }
                } else {
                  // Try with the same refresh token but different schema
                  // Make sure refreshToken is defined
                  const originalRefreshToken = getRefreshTokenForSchema(schema);
                  if (originalRefreshToken) {
                    const altResponse = await fetch(`${API_BASE_URL}/api/jwt/refresh-token/`, {
                      method: 'POST',
                      headers: {
                        'Content-Type': 'application/json',
                        'X-Schema-Name': altSchema
                      },
                      body: JSON.stringify({ refresh_token: originalRefreshToken }),
                      credentials: 'include',
                    });

                    if (altResponse.ok) {
                      const altData = await altResponse.json();

                      if (altData.access_token && altData.refresh_token) {
                        console.log(`Successfully refreshed token using tenant schema with original token: ${altSchema}`);

                        // Store tokens for both schemas using centralized storage
                        tokenStorage.setAccessToken(altData.access_token, schema);
                        tokenStorage.setRefreshToken(altData.refresh_token, schema);
                        tokenStorage.setAccessToken(altData.access_token, altSchema);
                        tokenStorage.setRefreshToken(altData.refresh_token, altSchema);

                        // Update current schema
                        tokenStorage.setCurrentSchema(altSchema);

                        return altData;
                      }
                    } else if (altResponse.status === 401) {
                      // If alternative schema also fails with 401, we should redirect to login
                      console.log('Alternative schema refresh with original token also failed with 401. User needs to log in again.');
                      clearTokensForSchema(altSchema);
                      localStorage.setItem('token_expired', 'true');
                      redirectToLogin();
                      throw new Error('SESSION_EXPIRED');
                    }
                  }
                }
              } catch (altSchemaError) {
                console.error(`Error during alternative schema refresh for ${altSchema}:`, altSchemaError);
              }
            }
          }
        } catch (tenantError) {
          console.error('Error parsing tenant for schema fallback:', tenantError);
        }
      }

      // If we get here, all refresh attempts have failed
      console.log('All token refresh attempts failed. User needs to log in again.');
      clearTokensForSchema(formattedSchema);
      localStorage.setItem('token_expired', 'true');
      redirectToLogin();
      throw new Error(`Token refresh failed: ${response.status} ${response.statusText} - ${errorText}`);
    }

    const data = await response.json();

    // Store new tokens
    if (data.access_token && data.refresh_token) {
      // With token rotation, we always get a new refresh token
      // The old refresh token is now blacklisted on the server

      // Store tokens using centralized storage
      tokenStorage.setCurrentSchema(formattedSchema);
      tokenStorage.setAccessToken(data.access_token, formattedSchema);
      tokenStorage.setRefreshToken(data.refresh_token, formattedSchema);

      // Also use TokenManager to ensure tokens are stored in all locations
      import('./tokenManager').then(({ tokenManager }) => {
        tokenManager.storeAccessToken(data.access_token, formattedSchema);
        tokenManager.storeRefreshToken(data.refresh_token, formattedSchema);
        tokenManager.synchronizeCookies();
      }).catch(error => {
        console.error('Error importing tokenManager:', error);
      });

      console.log(`Stored new tokens for schema ${formattedSchema} after refresh (token rotation)`);
    } else {
      console.warn(`Refresh response did not contain both tokens for schema ${formattedSchema}`);
      clearTokensForSchema(formattedSchema);
      throw new Error('Invalid token refresh response');
    }

      return data;
    } catch (error) {
      console.error(`Error refreshing JWT tokens for schema ${formattedSchema}:`, error);
      // Don't clear tokens on error to allow for retries with different schemas
      // Only clear if we're explicitly told to
      if (error.message && error.message.includes('clear tokens')) {
        clearTokensForSchema(formattedSchema);
      }
      throw error;
    } finally {
      // Clear the in-progress promise
      refreshInProgress[formattedSchema] = null;
    }
  })();

  // Store the promise so we can return it for concurrent requests
  refreshInProgress[formattedSchema] = refreshPromise;

  // Return the promise
  return refreshPromise;
};

/**
 * Validate JWT token
 * @param schema The schema name
 * @returns Whether the token is valid
 */
export const validateJWTToken = async (schema: string): Promise<boolean> => {
  if (!schema) {
    console.error('Cannot validate token: schema is missing');
    return false;
  }

  try {
    // DIAGNOSTIC: Check for schema mismatches
    console.log('TOKEN VALIDATION SCHEMA DIAGNOSTIC:');
    console.log('- Schema passed to validateJWTToken:', schema);
    console.log('- Current schema from tokenStorage:', tokenStorage.getCurrentSchema());

    // Format schema (replace spaces with underscores if needed)
    const formattedSchema = tokenStorage.formatSchema(schema);
    console.log('- Formatted schema:', formattedSchema);

    // Check if there's a mismatch
    if (formattedSchema !== tokenStorage.getCurrentSchema()) {
      console.warn('SCHEMA MISMATCH DETECTED in token validation!');

      // Store the formatted schema for consistency
      tokenStorage.setCurrentSchema(formattedSchema);
    }

    // Get the access token
    const accessToken = getAccessTokenForSchema(formattedSchema);
    if (!accessToken) {
      console.error(`No access token found for schema ${formattedSchema}`);
      return false;
    }

    console.log(`Validating token for schema ${formattedSchema}`);

    // Make the validation request
    const response = await fetch(`${API_BASE_URL}/api/jwt/validate-token/`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${cleanToken(accessToken)}`,
        'X-Schema-Name': formattedSchema
      },
      credentials: 'include',
    });

    console.log(`Validation response status: ${response.status}`);

    if (response.ok) {
      console.log(`Token is valid for schema ${formattedSchema}`);
      return true;
    }

    // If unauthorized, try to refresh token and validate again
    if (response.status === 401) {
      try {
        console.log(`Attempting to refresh token for schema ${formattedSchema} during validation`);
        const refreshResult = await refreshJWTTokens(formattedSchema);
        if (!refreshResult || !refreshResult.access_token) {
          console.error(`Failed to refresh token for schema ${formattedSchema} during validation`);
          return false; // Don't clear tokens here, let the caller decide
        }

        // Try validation again with new token
        const newAccessToken = refreshResult.access_token;
        const retryResponse = await fetch(`${API_BASE_URL}/api/jwt/validate-token/`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${cleanToken(newAccessToken)}`,
            'X-Schema-Name': formattedSchema
          },
          credentials: 'include',
        });

        console.log(`Retry validation response status: ${retryResponse.status}`);
        return retryResponse.ok;
      } catch (error) {
        console.error(`Error refreshing token during validation for schema ${formattedSchema}:`, error);
        return false; // Don't clear tokens here, let the caller decide
      }
    }

    return false;
  } catch (error) {
    // Make sure formattedSchema is defined in the catch block
    const errorSchema = schema.replace(/\s+/g, '_'); // Re-format schema for error handling
    console.error(`Error validating JWT token for schema ${errorSchema}:`, error);
    return false;
  }
};

/**
 * Create an authenticated fetch function for a specific schema
 * @param schema The schema name
 * @returns A fetch function that includes authentication headers
 */
export const createAuthenticatedFetch = (schema: string) => {
  return async (url: string, options: RequestInit = {}): Promise<Response> => {
    if (!schema) {
      console.error('No schema provided to createAuthenticatedFetch');
      throw new Error('No schema provided. Please log in again.');
    }

    // Get access token
    let accessToken = getAccessTokenForSchema(schema);

    // If no access token, try to refresh
    if (!accessToken) {
      try {
        console.log(`No access token found for schema ${schema}, attempting to refresh`);
        const refreshResult = await refreshJWTTokens(schema);
        if (!refreshResult || !refreshResult.access_token) {
          console.error(`Failed to refresh token for schema ${schema}`);
          clearTokensForSchema(schema);
          throw new Error('Authentication failed. Please log in again.');
        }
        accessToken = refreshResult.access_token;
      } catch (error) {
        console.error(`Error refreshing token for schema ${schema}:`, error);
        // Clear tokens for this schema to ensure user is redirected to login
        clearTokensForSchema(schema);
        throw new Error('Authentication failed. Please log in again.');
      }
    }

    // Create headers
    const headers = new Headers(options.headers || {});
    headers.set('Authorization', `Bearer ${cleanToken(accessToken)}`);
    headers.set('X-Schema-Name', schema);

    // Create fetch options
    const fetchOptions: RequestInit = {
      ...options,
      headers,
      credentials: 'include',
    };

    // Make the request
    const response = await fetch(url, fetchOptions);

    // If unauthorized, try to refresh token and retry
    if (response.status === 401) {
      try {
        console.log(`Received 401 response, attempting token refresh for schema ${schema}`);

        // Clear the existing token before refreshing
        // This is important because the token might be blacklisted
        clearAccessTokenForSchema(schema);

        const refreshResult = await refreshJWTTokens(schema);
        if (!refreshResult || !refreshResult.access_token) {
          console.error(`Failed to refresh token for schema ${schema}`);
          clearTokensForSchema(schema);
          throw new Error('Authentication failed. Please log in again.');
        }

        // Retry the request with the new token
        const newAccessToken = refreshResult.access_token;
        const newHeaders = new Headers(options.headers || {});
        newHeaders.set('Authorization', `Bearer ${cleanToken(newAccessToken)}`);
        newHeaders.set('X-Schema-Name', schema);

        const retryOptions: RequestInit = {
          ...options,
          headers: newHeaders,
          credentials: 'include',
        };

        return fetch(url, retryOptions);
      } catch (error) {
        console.error(`Error refreshing token for schema ${schema}:`, error);
        // Clear tokens for this schema to ensure user is redirected to login
        clearTokensForSchema(schema);
        throw new Error('Authentication failed. Please log in again.');
      }
    }

    return response;
  };
};

/**
 * Legacy compatibility function for authenticatedFetch
 * @param url The URL to fetch
 * @param options The fetch options
 * @returns The response
 */
export const authenticatedFetch = async (url: string, options: RequestInit = {}): Promise<Response> => {
  const schema = getCurrentSchema();
  if (!schema) {
    throw new Error('No schema found. Please log in again.');
  }

  return createAuthenticatedFetch(schema)(url, options);
};

// Initialize token store
initializeTokenStore();

export default {
  storeTokensForSchema,
  getAccessTokenForSchema,
  getRefreshTokenForSchema,
  clearTokensForSchema,
  clearAllTokens,
  getAuthHeaders,
  loginWithJWT,
  refreshJWTTokens,
  validateJWTToken,
  createAuthenticatedFetch,
  authenticatedFetch,
  getCurrentSchema,
  storeCurrentSchema,
};
