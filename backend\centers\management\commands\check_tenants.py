from django.core.management.base import BaseCommand
from centers.models import Client

class Command(BaseCommand):
    help = 'Check available tenants, subcities, and centers'

    def handle(self, *args, **options):
        self.stdout.write('Available tenants:')
        for tenant in Client.objects.all():
            self.stdout.write(f'ID: {tenant.id}, Name: {tenant.name}, Schema: {tenant.schema_name}, Type: {tenant.schema_type}')
            if hasattr(tenant, 'parent') and tenant.parent:
                self.stdout.write(f'  Parent: ID: {tenant.parent.id}, Name: {tenant.parent.name}')
            else:
                self.stdout.write('  Parent: None')
