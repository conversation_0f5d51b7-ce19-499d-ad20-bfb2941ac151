/**
 * Fixed Authentication Utilities
 *
 * This module provides a fixed authentication solution for the RegisterCitizen component.
 * It uses a hardcoded token and schema name to ensure authentication works.
 */

// Get the token from localStorage or cookies
const getToken = (): string => {
  // Try to get the token from localStorage
  const token = localStorage.getItem('token');
  if (token) {
    return token;
  }

  // Try to get the adminToken from cookies
  const adminToken = document.cookie
    .split('; ')
    .find(row => row.startsWith('adminToken='))
    ?.split('=')[1];

  if (adminToken) {
    return adminToken;
  }

  // Return an empty string if no token is found
  return '';
};

// Get the schema name from localStorage or cookies
const getSchemaName = (): string => {
  // Try to get the schema name from localStorage
  const schemaName = localStorage.getItem('schema_name');
  if (schemaName) {
    return schemaName;
  }

  // Try to get the schema name from the tenant object
  try {
    const tenantStr = localStorage.getItem('tenant');
    if (tenantStr) {
      const tenant = JSON.parse(tenantStr);
      if (tenant && tenant.schema_name) {
        return tenant.schema_name;
      }
    }
  } catch (error) {
    console.error('Error parsing tenant from localStorage:', error);
  }

  // Try to get the schema name from cookies
  const schemaCookie = document.cookie
    .split('; ')
    .find(row => row.startsWith('schema_name='))
    ?.split('=')[1];

  if (schemaCookie) {
    try {
      return decodeURIComponent(schemaCookie);
    } catch (error) {
      console.error('Error decoding schema cookie:', error);
    }
  }

  // Return null if no schema name is found
  return null;
};

/**
 * Make a fixed authenticated request to the backend
 * @param url The URL to fetch
 * @param options The fetch options
 * @returns The response
 */
export const fixedAuthFetch = async (url: string, options: RequestInit = {}): Promise<Response> => {
  console.log('fixedAuthFetch: Making authenticated request to', url);

  // Get the token and schema name
  const token = getToken();
  const schemaName = getSchemaName();

  console.log('fixedAuthFetch: Using token:', token ? 'Token exists' : 'No token');
  console.log('fixedAuthFetch: Using schema name:', schemaName);

  // Create headers
  const headers = new Headers(options.headers || {});

  // Add the token to the headers
  if (token) {
    // Use Bearer prefix for JWT tokens
    const tokenWithPrefix = token.includes('.') ? `Bearer ${token}` : `Bearer ${token}`;
    headers.set('Authorization', tokenWithPrefix);
    console.log('fixedAuthFetch: Added Authorization header with Bearer token');
  }

  // Add the schema name to the headers
  if (schemaName) {
    headers.set('X-Schema-Name', schemaName);

    // Also set the schema name as a cookie
    document.cookie = `schema_name=${encodeURIComponent(schemaName)}; path=/; SameSite=Lax`;
  }

  // Log the headers
  console.log('fixedAuthFetch: Headers:', Object.fromEntries(headers.entries()));

  // Make the request
  try {
    const response = await fetch(url, {
      ...options,
      headers,
      credentials: 'include'
    });

    // Log the response
    console.log('fixedAuthFetch: Response status:', response.status);

    return response;
  } catch (error) {
    console.error('fixedAuthFetch: Error making request:', error);
    throw error;
  }
};

/**
 * Make a fixed authenticated GET request
 * @param url The URL to fetch
 * @returns The response data
 */
export const fixedAuthGet = async <T>(url: string): Promise<T> => {
  const response = await fixedAuthFetch(url, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json'
    }
  });

  if (!response.ok) {
    throw new Error(`Error fetching ${url}: ${response.status} ${response.statusText}`);
  }

  return response.json();
};

/**
 * Make a fixed authenticated POST request
 * @param url The URL to fetch
 * @param data The data to send
 * @returns The response data
 */
export const fixedAuthPost = async <T>(url: string, data: any): Promise<T> => {
  const response = await fixedAuthFetch(url, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(data)
  });

  if (!response.ok) {
    throw new Error(`Error posting to ${url}: ${response.status} ${response.statusText}`);
  }

  return response.json();
};

/**
 * Make a fixed authenticated POST request with FormData
 * @param url The URL to fetch
 * @param formData The FormData to send
 * @returns The response data
 */
export const fixedAuthPostFormData = async <T>(url: string, formData: FormData): Promise<T> => {
  const response = await fixedAuthFetch(url, {
    method: 'POST',
    body: formData
  });

  if (!response.ok) {
    throw new Error(`Error posting form data to ${url}: ${response.status} ${response.statusText}`);
  }

  return response.json();
};
