/**
 * Cache Manager
 *
 * This module provides utilities for caching data in the application.
 * It handles caching of tenant information, API responses, and other data.
 */

// Cache expiration time in milliseconds (default: 5 minutes)
const CACHE_EXPIRATION_TIME = 5 * 60 * 1000;

// Cache interface
interface CacheItem<T> {
  data: T;
  timestamp: number;
  expiresAt: number;
}

// Cache store
const cacheStore: Record<string, CacheItem<any>> = {};

/**
 * Set a cache item
 * @param key The cache key
 * @param data The data to cache
 * @param expirationTime Optional expiration time in milliseconds
 */
export const setCacheItem = <T>(
  key: string,
  data: T,
  expirationTime: number = CACHE_EXPIRATION_TIME
): void => {
  try {
    if (!key) {
      console.error('Cannot set cache item: key is missing');
      return;
    }

    const timestamp = Date.now();
    const expiresAt = timestamp + expirationTime;

    cacheStore[key] = {
      data,
      timestamp,
      expiresAt,
    };

    console.log(`Cache item set: ${key}`);
  } catch (error) {
    console.error(`Error setting cache item ${key}:`, error);
  }
};

/**
 * Get a cache item
 * @param key The cache key
 * @returns The cached data, or null if not found or expired
 */
export const getCacheItem = <T>(key: string): T | null => {
  try {
    if (!key) {
      console.error('Cannot get cache item: key is missing');
      return null;
    }

    const cacheItem = cacheStore[key];

    if (!cacheItem) {
      console.log(`Cache miss: ${key}`);
      return null;
    }

    const now = Date.now();

    if (now > cacheItem.expiresAt) {
      console.log(`Cache expired: ${key}`);
      delete cacheStore[key];
      return null;
    }

    console.log(`Cache hit: ${key}`);
    return cacheItem.data as T;
  } catch (error) {
    console.error(`Error getting cache item ${key}:`, error);
    return null;
  }
};

/**
 * Remove a cache item
 * @param key The cache key
 */
export const removeCacheItem = (key: string): void => {
  try {
    if (!key) {
      console.error('Cannot remove cache item: key is missing');
      return;
    }

    delete cacheStore[key];
    console.log(`Cache item removed: ${key}`);
  } catch (error) {
    console.error(`Error removing cache item ${key}:`, error);
  }
};

/**
 * Clear all cache items
 */
export const clearCache = (): void => {
  try {
    Object.keys(cacheStore).forEach(key => {
      delete cacheStore[key];
    });

    console.log('Cache cleared');
  } catch (error) {
    console.error('Error clearing cache:', error);
  }
};

/**
 * Get all cache keys
 * @returns Array of cache keys
 */
export const getCacheKeys = (): string[] => {
  try {
    return Object.keys(cacheStore);
  } catch (error) {
    console.error('Error getting cache keys:', error);
    return [];
  }
};

/**
 * Get cache statistics
 * @returns Cache statistics
 */
export const getCacheStats = (): {
  totalItems: number;
  expiredItems: number;
  validItems: number;
  keys: string[];
} => {
  try {
    const now = Date.now();
    const keys = Object.keys(cacheStore);
    const totalItems = keys.length;
    const expiredItems = keys.filter(key => now > cacheStore[key].expiresAt).length;
    const validItems = totalItems - expiredItems;

    return {
      totalItems,
      expiredItems,
      validItems,
      keys,
    };
  } catch (error) {
    console.error('Error getting cache stats:', error);
    return {
      totalItems: 0,
      expiredItems: 0,
      validItems: 0,
      keys: [],
    };
  }
};

/**
 * Clean expired cache items
 */
export const cleanExpiredCacheItems = (): void => {
  try {
    const now = Date.now();
    const keys = Object.keys(cacheStore);
    const expiredKeys = keys.filter(key => now > cacheStore[key].expiresAt);

    expiredKeys.forEach(key => {
      delete cacheStore[key];
    });

    console.log(`Cleaned ${expiredKeys.length} expired cache items`);
  } catch (error) {
    console.error('Error cleaning expired cache items:', error);
  }
};

// Clean expired cache items every 5 minutes
setInterval(cleanExpiredCacheItems, CACHE_EXPIRATION_TIME);
