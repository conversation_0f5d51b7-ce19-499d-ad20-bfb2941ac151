from rest_framework import serializers
from .models_biometric import Biometric, Photo

class BiometricSerializer(serializers.ModelSerializer):
    """Serializer for the Biometric model."""
    citizen_name = serializers.CharField(source='citizen.get_full_name', read_only=True)
    
    class Meta:
        model = Biometric
        fields = '__all__'
        read_only_fields = ('created_at', 'updated_at')

class PhotoSerializer(serializers.ModelSerializer):
    """Serializer for the Photo model."""
    citizen_name = serializers.CharField(source='citizen.get_full_name', read_only=True)
    photo_url = serializers.SerializerMethodField()
    
    class Meta:
        model = Photo
        fields = '__all__'
        read_only_fields = ('created_at', 'updated_at', 'upload_date', 'photo_url')
    
    def get_photo_url(self, obj):
        """Get the URL of the photo."""
        if obj.photo:
            request = self.context.get('request')
            if request:
                return request.build_absolute_uri(obj.photo.url)
            return obj.photo.url
        return None
