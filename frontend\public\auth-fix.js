/**
 * Authentication Fix Script
 *
 * This script runs when the application loads to fix authentication issues.
 * It ensures that the token and schema are properly set in localStorage and cookies.
 */

(function() {
  console.log('Running authentication fix script...');

  // Check if token exists
  const token = localStorage.getItem('token');
  if (!token) {
    console.log('No token found in localStorage, skipping fix');
    return;
  }

  // Check if schema exists
  const schema = localStorage.getItem('schema_name');
  if (!schema) {
    console.log('No schema found in localStorage, checking tenant');

    // Try to get schema from tenant
    try {
      const tenantStr = localStorage.getItem('tenant');
      if (tenantStr) {
        const tenant = JSON.parse(tenantStr);
        if (tenant && tenant.schema_name) {
          console.log('Found schema in tenant:', tenant.schema_name);
          localStorage.setItem('schema_name', tenant.schema_name);

          // Set schema cookie
          document.cookie = `schema_name=${encodeURIComponent(tenant.schema_name)}; path=/; SameSite=Lax`;
          console.log('Set schema cookie:', tenant.schema_name);
        }
      }
    } catch (error) {
      console.error('Error parsing tenant:', error);
    }
  } else {
    // Ensure schema cookie is set
    document.cookie = `schema_name=${encodeURIComponent(schema)}; path=/; SameSite=Lax`;
    console.log('Set schema cookie:', schema);
  }

  // Clean token thoroughly - remove ALL spaces, not just leading/trailing
  const cleanToken = token.replace(/\s+/g, '');
  if (cleanToken !== token) {
    console.log('Cleaned token (removed all spaces)');
    localStorage.setItem('token', cleanToken);
  }

  // Set token in tokenStore
  try {
    const schema = localStorage.getItem('schema_name');
    if (schema) {
      const tokenStoreStr = localStorage.getItem('tokenStore');
      let tokenStore = tokenStoreStr ? JSON.parse(tokenStoreStr) : {};

      // Set token for schema
      tokenStore[schema] = cleanToken;

      // Save tokenStore
      localStorage.setItem('tokenStore', JSON.stringify(tokenStore));
      console.log('Updated tokenStore with token for schema:', schema);
    }
  } catch (error) {
    console.error('Error updating tokenStore:', error);
  }

  console.log('Authentication fix script completed');
})();
