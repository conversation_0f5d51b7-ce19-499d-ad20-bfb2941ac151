"""
<PERSON><PERSON><PERSON> to force create tokens in the authtoken_token table for all users in a schema.
This script bypasses the normal token creation process and directly creates tokens in the database.
"""

import os
import sys
import django
import secrets
import binascii

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'neocamelot.settings')
django.setup()

from django.db import connection
from django_tenants.utils import tenant_context
from centers.models import Client
from django.contrib.auth import get_user_model
from rest_framework.authtoken.models import Token

User = get_user_model()

def generate_token_key():
    """
    Generate a token key.
    This is the same method used by the Token model to generate keys.
    """
    return binascii.hexlify(secrets.token_bytes(20)).decode()

def force_create_tokens_in_schema(schema_name):
    """
    Force create tokens in the authtoken_token table for all users in a schema.
    
    Args:
        schema_name (str): The schema name of the tenant
        
    Returns:
        list: A list of created token keys
    """
    print(f"\n=== Force creating tokens in schema {schema_name} ===")
    
    try:
        # Get the tenant
        tenant = Client.objects.get(schema_name=schema_name)
        print(f"Found tenant: {tenant.name} (Schema: {tenant.schema_name})")
        
        # Switch to the tenant context
        with tenant_context(tenant):
            # Get all users
            users = User.objects.all()
            print(f"Found {len(users)} users in {schema_name}")
            
            if not users.exists():
                print(f"No users found in {schema_name}")
                return []
            
            tokens = []
            for user in users:
                # Delete any existing tokens for this user
                Token.objects.filter(user=user).delete()
                
                # Generate a new token key
                token_key = generate_token_key()
                
                # Create a new token directly in the database
                token = Token.objects.create(key=token_key, user=user)
                tokens.append(token.key)
                
                print(f"Created token for user {user.email}: {token.key}")
            
            return tokens
    except Client.DoesNotExist:
        print(f"Tenant with schema {schema_name} does not exist")
        return []
    except Exception as e:
        print(f"Error creating tokens: {str(e)}")
        return []

def force_create_token_for_user(email, schema_name):
    """
    Force create a token in the authtoken_token table for a specific user in a schema.
    
    Args:
        email (str): The email of the user
        schema_name (str): The schema name of the tenant
        
    Returns:
        str: The created token key or None if the user is not found
    """
    print(f"\n=== Force creating token for user {email} in schema {schema_name} ===")
    
    try:
        # Get the tenant
        tenant = Client.objects.get(schema_name=schema_name)
        print(f"Found tenant: {tenant.name} (Schema: {tenant.schema_name})")
        
        # Switch to the tenant context
        with tenant_context(tenant):
            # Try to find the user
            try:
                user = User.objects.get(email=email)
                print(f"Found user: {user.email} (ID: {user.id})")
                
                # Delete any existing tokens for this user
                Token.objects.filter(user=user).delete()
                
                # Generate a new token key
                token_key = generate_token_key()
                
                # Create a new token directly in the database
                token = Token.objects.create(key=token_key, user=user)
                print(f"Created token for user {user.email}: {token.key}")
                
                return token.key
            except User.DoesNotExist:
                print(f"User with email {email} not found in schema {schema_name}")
                return None
    except Client.DoesNotExist:
        print(f"Tenant with schema {schema_name} does not exist")
        return None
    except Exception as e:
        print(f"Error creating token: {str(e)}")
        return None

def list_all_schemas():
    """
    List all available schemas.
    
    Returns:
        list: A list of schema names
    """
    try:
        # Get all tenants
        tenants = Client.objects.all()
        schemas = [tenant.schema_name for tenant in tenants]
        return schemas
    except Exception as e:
        print(f"Error listing schemas: {str(e)}")
        return []

def verify_tokens_in_schema(schema_name):
    """
    Verify that tokens exist in the authtoken_token table for all users in a schema.
    
    Args:
        schema_name (str): The schema name of the tenant
        
    Returns:
        bool: True if all users have tokens, False otherwise
    """
    print(f"\n=== Verifying tokens in schema {schema_name} ===")
    
    try:
        # Get the tenant
        tenant = Client.objects.get(schema_name=schema_name)
        print(f"Found tenant: {tenant.name} (Schema: {tenant.schema_name})")
        
        # Switch to the tenant context
        with tenant_context(tenant):
            # Get all users
            users = User.objects.all()
            print(f"Found {len(users)} users in {schema_name}")
            
            if not users.exists():
                print(f"No users found in {schema_name}")
                return True
            
            # Check if all users have tokens
            users_without_tokens = []
            for user in users:
                if not Token.objects.filter(user=user).exists():
                    users_without_tokens.append(user.email)
            
            if users_without_tokens:
                print(f"Found {len(users_without_tokens)} users without tokens:")
                for email in users_without_tokens:
                    print(f"  {email}")
                return False
            else:
                print(f"All users have tokens")
                return True
    except Client.DoesNotExist:
        print(f"Tenant with schema {schema_name} does not exist")
        return False
    except Exception as e:
        print(f"Error verifying tokens: {str(e)}")
        return False

if __name__ == "__main__":
    # Check if we have command line arguments
    if len(sys.argv) < 2:
        print("Usage:")
        print("  python force_create_tokens.py list_schemas")
        print("  python force_create_tokens.py verify <schema_name>")
        print("  python force_create_tokens.py create_for_user <email> <schema_name>")
        print("  python force_create_tokens.py create_for_all <schema_name>")
        sys.exit(1)
    
    command = sys.argv[1]
    
    if command == "list_schemas":
        schemas = list_all_schemas()
        print("\nAvailable schemas:")
        for schema in schemas:
            print(f"  {schema}")
    
    elif command == "verify":
        if len(sys.argv) < 3:
            print("Usage: python force_create_tokens.py verify <schema_name>")
            sys.exit(1)
        
        schema_name = sys.argv[2]
        verify_tokens_in_schema(schema_name)
    
    elif command == "create_for_user":
        if len(sys.argv) < 4:
            print("Usage: python force_create_tokens.py create_for_user <email> <schema_name>")
            sys.exit(1)
        
        email = sys.argv[2]
        schema_name = sys.argv[3]
        
        token = force_create_token_for_user(email, schema_name)
        if token:
            print(f"\nToken created successfully: {token}")
            print(f"Use this token in your API calls with header: Authorization: Token {token}")
    
    elif command == "create_for_all":
        if len(sys.argv) < 3:
            print("Usage: python force_create_tokens.py create_for_all <schema_name>")
            sys.exit(1)
        
        schema_name = sys.argv[2]
        
        tokens = force_create_tokens_in_schema(schema_name)
        if tokens:
            print(f"\nCreated {len(tokens)} tokens successfully")
    
    else:
        print(f"Unknown command: {command}")
        print("Usage:")
        print("  python force_create_tokens.py list_schemas")
        print("  python force_create_tokens.py verify <schema_name>")
        print("  python force_create_tokens.py create_for_user <email> <schema_name>")
        print("  python force_create_tokens.py create_for_all <schema_name>")
