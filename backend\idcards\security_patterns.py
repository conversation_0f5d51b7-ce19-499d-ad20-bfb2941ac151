"""
Security pattern generation and management for ID cards.

This module provides functions to generate and manage the two-part security patterns
used in ID cards. The pattern is split between kebele and subcity approval stages.
"""

import hashlib
import random
import string
import base64
from typing import Tu<PERSON>, Dict, Optional
import json

def generate_pattern_seed() -> str:
    """
    Generate a random seed for the security pattern.

    Returns:
        str: A random string to use as a seed for pattern generation
    """
    # Generate a random string of 32 characters
    chars = string.ascii_letters + string.digits
    random_string = ''.join(random.choice(chars) for _ in range(32))

    # Create a hash of the random string for additional security
    hash_obj = hashlib.sha256(random_string.encode())
    return hash_obj.hexdigest()

def generate_pattern_halves(seed: str) -> Tuple[str, str]:
    """
    Generate two complementary halves of a security pattern based on a seed.

    Args:
        seed (str): The seed to use for pattern generation

    Returns:
        Tuple[str, str]: The kebele pattern and subcity pattern halves
    """
    # Use the seed to initialize the random generator for deterministic results
    random.seed(seed)

    # Generate pattern parameters - using fewer elements to keep JSON size under 255 chars
    pattern_data = {
        # Common parameters
        "seed": seed,

        # Kebele pattern parameters (first half)
        "kebele": {
            "color": f"rgba({random.randint(0, 100)}, {random.randint(0, 100)}, {random.randint(100, 255)}, 0.3)",
            "lines": [
                {
                    "x1": round(random.uniform(0, 0.5), 2),
                    "y1": round(random.uniform(0, 1), 2),
                    "x2": round(random.uniform(0, 0.5), 2),
                    "y2": round(random.uniform(0, 1), 2),
                    "width": round(random.uniform(0.5, 2), 1),
                    "opacity": round(random.uniform(0.1, 0.3), 1)
                } for _ in range(5)  # Generate 5 random lines instead of 10
            ],
            "dots": [
                {
                    "x": round(random.uniform(0, 0.5), 2),
                    "y": round(random.uniform(0, 1), 2),
                    "radius": round(random.uniform(0.5, 2), 1),
                    "opacity": round(random.uniform(0.1, 0.3), 1)
                } for _ in range(5)  # Generate 5 random dots instead of 15
            ]
        },

        # Subcity pattern parameters (second half)
        "subcity": {
            "color": f"rgba({random.randint(100, 255)}, {random.randint(0, 100)}, {random.randint(0, 100)}, 0.3)",
            "lines": [
                {
                    "x1": round(random.uniform(0.5, 1), 2),
                    "y1": round(random.uniform(0, 1), 2),
                    "x2": round(random.uniform(0.5, 1), 2),
                    "y2": round(random.uniform(0, 1), 2),
                    "width": round(random.uniform(0.5, 2), 1),
                    "opacity": round(random.uniform(0.1, 0.3), 1)
                } for _ in range(5)  # Generate 5 random lines instead of 10
            ],
            "dots": [
                {
                    "x": round(random.uniform(0.5, 1), 2),
                    "y": round(random.uniform(0, 1), 2),
                    "radius": round(random.uniform(0.5, 2), 1),
                    "opacity": round(random.uniform(0.1, 0.3), 1)
                } for _ in range(5)  # Generate 5 random dots instead of 15
            ]
        }
    }

    # Convert to JSON strings
    kebele_pattern = json.dumps(pattern_data["kebele"])
    subcity_pattern = json.dumps(pattern_data["subcity"])

    return kebele_pattern, subcity_pattern

def apply_kebele_pattern(id_card) -> None:
    """
    Apply the kebele half of the security pattern to an ID card.

    Args:
        id_card: The ID card model instance
    """
    try:
        # Generate a new seed if one doesn't exist
        if not id_card.pattern_seed:
            id_card.pattern_seed = generate_pattern_seed()

        # Generate both pattern halves
        kebele_pattern, subcity_pattern = generate_pattern_halves(id_card.pattern_seed)

        # Check if the pattern is too long for the database field
        if len(kebele_pattern) > 250:  # Leave some margin for safety
            print(f"Warning: Kebele pattern is too long ({len(kebele_pattern)} chars). Simplifying...")
            # Create a simplified pattern if it's too long
            simplified_pattern = {
                "color": "rgba(0, 0, 150, 0.3)",
                "lines": [{"x1": 0.1, "y1": 0.1, "x2": 0.4, "y2": 0.9, "width": 1, "opacity": 0.2}],
                "dots": [{"x": 0.2, "y": 0.5, "radius": 1, "opacity": 0.2}]
            }
            kebele_pattern = json.dumps(simplified_pattern)

        # Apply only the kebele half
        id_card.kebele_pattern = kebele_pattern

        # Save the ID card
        id_card.save(update_fields=['pattern_seed', 'kebele_pattern'])

        print(f"Kebele pattern applied successfully. Pattern length: {len(kebele_pattern)} chars")
    except Exception as e:
        print(f"Error applying kebele pattern: {str(e)}")
        raise

def apply_subcity_pattern(id_card) -> None:
    """
    Apply the subcity half of the security pattern to an ID card.

    Args:
        id_card: The ID card model instance
    """
    try:
        # Ensure we have a seed and kebele pattern
        if not id_card.pattern_seed or not id_card.kebele_pattern:
            raise ValueError("ID card must have a pattern seed and kebele pattern before applying subcity pattern")

        # Generate both pattern halves
        _, subcity_pattern = generate_pattern_halves(id_card.pattern_seed)

        # Check if the pattern is too long for the database field
        if len(subcity_pattern) > 250:  # Leave some margin for safety
            print(f"Warning: Subcity pattern is too long ({len(subcity_pattern)} chars). Simplifying...")
            # Create a simplified pattern if it's too long
            simplified_pattern = {
                "color": "rgba(150, 0, 0, 0.3)",
                "lines": [{"x1": 0.6, "y1": 0.1, "x2": 0.9, "y2": 0.9, "width": 1, "opacity": 0.2}],
                "dots": [{"x": 0.8, "y": 0.5, "radius": 1, "opacity": 0.2}]
            }
            subcity_pattern = json.dumps(simplified_pattern)

        # Apply the subcity half
        id_card.subcity_pattern = subcity_pattern

        # Save the ID card
        id_card.save(update_fields=['subcity_pattern'])

        print(f"Subcity pattern applied successfully. Pattern length: {len(subcity_pattern)} chars")
    except Exception as e:
        print(f"Error applying subcity pattern: {str(e)}")
        raise

def get_pattern_status(id_card) -> Dict[str, bool]:
    """
    Get the status of the security pattern for an ID card.

    Args:
        id_card: The ID card model instance

    Returns:
        Dict[str, bool]: A dictionary with keys 'kebele' and 'subcity' indicating
                         whether each half of the pattern has been applied
    """
    return {
        'kebele': bool(id_card.kebele_pattern),
        'subcity': bool(id_card.subcity_pattern),
        'complete': bool(id_card.kebele_pattern and id_card.subcity_pattern)
    }

def get_pattern_svg(id_card, width: int = 300, height: int = 200) -> Optional[str]:
    """
    Generate an SVG representation of the security pattern for an ID card.

    Args:
        id_card: The ID card model instance
        width: The width of the SVG in pixels
        height: The height of the SVG in pixels

    Returns:
        Optional[str]: An SVG string representing the security pattern, or None if no pattern exists
    """
    if not id_card.pattern_seed:
        return None

    # Start the SVG
    svg = f'<svg width="{width}" height="{height}" xmlns="http://www.w3.org/2000/svg">'

    # Add the kebele pattern if it exists
    if id_card.kebele_pattern:
        try:
            kebele_data = json.loads(id_card.kebele_pattern)

            # Add lines
            for line in kebele_data.get('lines', []):
                x1 = line['x1'] * width
                y1 = line['y1'] * height
                x2 = line['x2'] * width
                y2 = line['y2'] * height
                stroke_width = line['width']
                opacity = line['opacity']

                svg += f'<line x1="{x1}" y1="{y1}" x2="{x2}" y2="{y2}" '
                svg += f'stroke="{kebele_data["color"]}" stroke-width="{stroke_width}" '
                svg += f'opacity="{opacity}" />'

            # Add dots
            for dot in kebele_data.get('dots', []):
                cx = dot['x'] * width
                cy = dot['y'] * height
                r = dot['radius']
                opacity = dot['opacity']

                svg += f'<circle cx="{cx}" cy="{cy}" r="{r}" '
                svg += f'fill="{kebele_data["color"]}" opacity="{opacity}" />'
        except (json.JSONDecodeError, KeyError):
            # If there's an error parsing the pattern, skip it
            pass

    # Add the subcity pattern if it exists
    if id_card.subcity_pattern:
        try:
            subcity_data = json.loads(id_card.subcity_pattern)

            # Add lines
            for line in subcity_data.get('lines', []):
                x1 = line['x1'] * width
                y1 = line['y1'] * height
                x2 = line['x2'] * width
                y2 = line['y2'] * height
                stroke_width = line['width']
                opacity = line['opacity']

                svg += f'<line x1="{x1}" y1="{y1}" x2="{x2}" y2="{y2}" '
                svg += f'stroke="{subcity_data["color"]}" stroke-width="{stroke_width}" '
                svg += f'opacity="{opacity}" />'

            # Add dots
            for dot in subcity_data.get('dots', []):
                cx = dot['x'] * width
                cy = dot['y'] * height
                r = dot['radius']
                opacity = dot['opacity']

                svg += f'<circle cx="{cx}" cy="{cy}" r="{r}" '
                svg += f'fill="{subcity_data["color"]}" opacity="{opacity}" />'
        except (json.JSONDecodeError, KeyError):
            # If there's an error parsing the pattern, skip it
            pass

    # Close the SVG
    svg += '</svg>'

    return svg

def get_pattern_data_url(id_card, width: int = 300, height: int = 200) -> Optional[str]:
    """
    Generate a data URL for the security pattern SVG.

    Args:
        id_card: The ID card model instance
        width: The width of the SVG in pixels
        height: The height of the SVG in pixels

    Returns:
        Optional[str]: A data URL containing the SVG, or None if no pattern exists
    """
    svg = get_pattern_svg(id_card, width, height)
    if not svg:
        return None

    # Encode the SVG as base64
    svg_bytes = svg.encode('utf-8')
    base64_svg = base64.b64encode(svg_bytes).decode('utf-8')

    # Create a data URL
    return f'data:image/svg+xml;base64,{base64_svg}'
