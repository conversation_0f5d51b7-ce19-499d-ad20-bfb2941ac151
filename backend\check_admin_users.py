import os
import django

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'neocamelot.settings')
django.setup()

# Import models
from centers.models import Client, Domain
from django.contrib.auth import get_user_model
from django_tenants.utils import tenant_context, schema_context

User = get_user_model()

# Check admin users in each tenant schema
print("\n=== Checking Admin Users in Tenant Schemas ===")
tenants = Client.objects.all()
for tenant in tenants:
    print(f"\nTenant: {tenant.name}")
    print(f"Schema Name: {tenant.schema_name}")
    print(f"Schema Type: {tenant.schema_type}")
    
    # Check users in public schema
    with schema_context('public'):
        public_users = User.objects.filter(email=tenant.admin_email)
        print(f"Users in public schema with email {tenant.admin_email}: {public_users.count()}")
        if public_users.exists():
            for user in public_users:
                print(f"- {user.email} (Role: {user.role if hasattr(user, 'role') else 'N/A'})")
    
    # Check users in tenant schema
    try:
        with tenant_context(tenant):
            tenant_users = User.objects.all()
            print(f"Total users in {tenant.schema_name} schema: {tenant_users.count()}")
            for user in tenant_users:
                print(f"- {user.email} (Role: {user.role if hasattr(user, 'role') else 'N/A'})")
    except Exception as e:
        print(f"Error accessing tenant schema: {str(e)}")
    
    print("-" * 50)
