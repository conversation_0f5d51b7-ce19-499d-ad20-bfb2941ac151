"""
Views for the IDCard model.
"""
from rest_framework import viewsets, permissions, filters, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from idcards.models import IDCard
from idcards.serializers import IDCardSerializer, IDCardListSerializer
from rest_framework.permissions import IsA<PERSON><PERSON>icated, IsAdminUser
from django.db import connection

class IDCardViewSet(viewsets.ModelViewSet):
    """ViewSet for the IDCard model."""
    queryset = IDCard.objects.all()
    serializer_class = IDCardSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['status', 'citizen__center']
    search_fields = ['card_number', 'citizen__first_name', 'citizen__last_name', 'citizen__registration_number']
    ordering_fields = ['issue_date', 'expiry_date', 'created_at']

    def get_serializer_class(self):
        """Return different serializers for list and detail views."""
        if self.action == 'list':
            return IDCardListSerializer
        return IDCardSerializer

    @action(detail=False, methods=['get'])
    def statistics(self, request):
        """Return statistics for ID cards."""
        # Print detailed authentication information
        print("\n=== ID Card Statistics Authentication ===")
        print(f"User: {request.user}")
        print(f"Is authenticated: {request.user.is_authenticated}")
        print(f"Auth: {request.auth}")
        print(f"X-Schema-Name header: {request.headers.get('X-Schema-Name')}")
        print(f"Schema name from cookie: {request.COOKIES.get('schema_name')}")
        print(f"Effective schema name: {connection.schema_name}")
        print(f"Authorization header: {request.headers.get('Authorization')}")
        print("=" * 50)

        queryset = self.get_queryset()

        stats = {
            'total': queryset.count(),
            'draft': queryset.filter(status='DRAFT').count(),
            'pending': queryset.filter(status='PENDING').count(),
            'approved': queryset.filter(status='APPROVED').count(),
            'printed': queryset.filter(status='PRINTED').count(),
            'issued': queryset.filter(status='ISSUED').count(),
            'expired': queryset.filter(status='EXPIRED').count(),
            'revoked': queryset.filter(status='REVOKED').count(),
            'kebele_pending': queryset.filter(kebele_approval_status='PENDING').count(),
            'kebele_approved': queryset.filter(kebele_approval_status='APPROVED').count(),
            'kebele_rejected': queryset.filter(kebele_approval_status='REJECTED').count(),
        }

        return Response(stats)
