# Generated by Django 5.1.7 on 2025-04-14 16:03

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0001_initial'),
        ('centers', '0006_alter_center_options_alter_center_name_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='user',
            name='city',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='users', to='centers.city'),
        ),
        migrations.AddField(
            model_name='user',
            name='subcity',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='users', to='centers.subcity'),
        ),
        migrations.AlterField(
            model_name='user',
            name='role',
            field=models.CharField(choices=[('SUPER_ADMIN', 'Super Admin'), ('CITY_ADMIN', 'City Admin'), ('SUBCITY_ADMIN', 'Subcity Admin'), ('CENTER_ADMIN', 'Center Admin'), ('CENTER_STAFF', 'Center Staff')], default='CENTER_STAFF', max_length=20),
        ),
    ]
