from rest_framework import permissions

class IsCenterStaff(permissions.BasePermission):
    """
    Permission class for CENTER_STAFF/Clerk role.
    
    CENTER_STAFF can:
    - Register citizens
    - View citizens list
    - View citizen details
    - Generate ID cards
    - View ID cards list
    - Send ID cards to kebele leader
    """
    
    def has_permission(self, request, view):
        # Check if the user is authenticated
        if not request.user.is_authenticated:
            return False
            
        # Check if the user has CENTER_STAFF role
        return request.user.role == 'CENTER_STAFF'


class IsKebeleLeader(permissions.BasePermission):
    """
    Permission class for KEBELE_LEADER role.
    
    KEBELE_LEADER can:
    - View citizens list
    - View citizen details
    - View ID cards list
    - Approve ID cards
    - Send ID cards to parent sub-city admin
    - Verify documents
    - View reports for their specific kebele tenant
    """
    
    def has_permission(self, request, view):
        # Check if the user is authenticated
        if not request.user.is_authenticated:
            return False
            
        # Check if the user has KEBELE_LEADER role
        return request.user.role == 'KEBELE_LEADER'


class IsSubcityAdmin(permissions.BasePermission):
    """
    Permission class for SUBCITY_ADMIN role.
    
    SUBCITY_ADMIN can:
    - Create kebele users and assign roles
    - View citizens list from child kebele tenants
    - View ID cards list
    - Approve ID cards
    - View reports for their specific sub-city tenant
    """
    
    def has_permission(self, request, view):
        # Check if the user is authenticated
        if not request.user.is_authenticated:
            return False
            
        # Check if the user has SUBCITY_ADMIN role
        return request.user.role == 'SUBCITY_ADMIN'


class IsCityAdmin(permissions.BasePermission):
    """
    Permission class for CITY_ADMIN role.
    
    CITY_ADMIN can:
    - Create sub-city users and assign roles
    - View citizens list from child sub-city tenants
    - View reports for their specific city tenant
    """
    
    def has_permission(self, request, view):
        # Check if the user is authenticated
        if not request.user.is_authenticated:
            return False
            
        # Check if the user has CITY_ADMIN role
        return request.user.role == 'CITY_ADMIN'


class IsCenterStaffOrKebeleLeader(permissions.BasePermission):
    """
    Permission class that allows either CENTER_STAFF or KEBELE_LEADER roles.
    
    This is useful for endpoints that both roles can access, such as:
    - View citizens list
    - View citizen details
    - View ID cards list
    """
    
    def has_permission(self, request, view):
        # Check if the user is authenticated
        if not request.user.is_authenticated:
            return False
            
        # Check if the user has either CENTER_STAFF or KEBELE_LEADER role
        return request.user.role in ['CENTER_STAFF', 'KEBELE_LEADER']


class IsSubcityAdminOrHigher(permissions.BasePermission):
    """
    Permission class that allows SUBCITY_ADMIN or higher roles.
    
    This includes:
    - SUBCITY_ADMIN
    - CITY_ADMIN
    - SUPER_ADMIN
    """
    
    def has_permission(self, request, view):
        # Check if the user is authenticated
        if not request.user.is_authenticated:
            return False
            
        # Check if the user has SUBCITY_ADMIN or higher role
        return request.user.role in ['SUBCITY_ADMIN', 'CITY_ADMIN', 'SUPER_ADMIN']


class CanViewCitizens(permissions.BasePermission):
    """
    Permission class for users who can view citizens.
    
    This includes:
    - CENTER_STAFF
    - KEBELE_LEADER
    - SUBCITY_ADMIN
    - CITY_ADMIN
    - SUPER_ADMIN
    """
    
    def has_permission(self, request, view):
        # Check if the user is authenticated
        if not request.user.is_authenticated:
            return False
            
        # Check if the user has a role that can view citizens
        return request.user.role in ['CENTER_STAFF', 'KEBELE_LEADER', 'SUBCITY_ADMIN', 'CITY_ADMIN', 'SUPER_ADMIN']


class CanRegisterCitizens(permissions.BasePermission):
    """
    Permission class for users who can register citizens.
    
    This includes:
    - CENTER_STAFF
    """
    
    def has_permission(self, request, view):
        # Check if the user is authenticated
        if not request.user.is_authenticated:
            return False
            
        # Check if the user has a role that can register citizens
        return request.user.role == 'CENTER_STAFF'


class CanManageIDCards(permissions.BasePermission):
    """
    Permission class for users who can manage ID cards.
    
    This includes:
    - CENTER_STAFF (generate, view, send to kebele leader)
    - KEBELE_LEADER (view, approve, send to subcity admin)
    - SUBCITY_ADMIN (view, approve)
    """
    
    def has_permission(self, request, view):
        # Check if the user is authenticated
        if not request.user.is_authenticated:
            return False
            
        # Check if the user has a role that can manage ID cards
        return request.user.role in ['CENTER_STAFF', 'KEBELE_LEADER', 'SUBCITY_ADMIN']
