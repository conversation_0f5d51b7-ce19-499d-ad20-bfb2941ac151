from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import AllowAny, IsAuthenticated
from .models import Center, CenterType

class CenterListAPI(APIView):
    """API endpoint to list centers."""
    permission_classes = [AllowAny]
    
    def get(self, request):
        centers = Center.objects.all()
        data = [
            {
                'id': center.id,
                'name': center.name,
                'code': center.code,
                'slug': center.slug,
                'type': center.type.name if center.type else None,
                'description': center.description,
                'address': center.address,
                'city': center.city,
                'state': center.state,
                'email': center.email,
                'phone': center.phone,
                'website': center.website,
                'header_color': center.header_color,
                'accent_color': center.accent_color,
                'admin_name': center.admin_name,
                'subscription_status': center.subscription_status,
                'max_users': center.max_users,
                'max_citizens': center.max_citizens,
                'max_id_cards': center.max_id_cards,
                'is_active': center.is_active,
                'is_verified': center.is_verified,
                'created_at': center.created_at.isoformat() if center.created_at else None,
                'updated_at': center.updated_at.isoformat() if center.updated_at else None,
            }
            for center in centers
        ]
        return Response(data)

class CenterTypeListAPI(APIView):
    """API endpoint to list center types."""
    permission_classes = [AllowAny]
    
    def get(self, request):
        types = CenterType.objects.all()
        data = [
            {
                'id': type_obj.id,
                'name': type_obj.name,
                'is_active': type_obj.is_active,
            }
            for type_obj in types
        ]
        return Response(data)

class CreateCenterAPI(APIView):
    """API endpoint to create a new center."""
    permission_classes = [IsAuthenticated]
    
    def post(self, request):
        # Check if user is a super admin
        if not request.user.is_super_admin:
            return Response({'error': 'Only super admins can create centers'}, 
                           status=status.HTTP_403_FORBIDDEN)
        
        # Validate required fields
        required_fields = ['name', 'address']
        for field in required_fields:
            if field not in request.data:
                return Response({'error': f'{field} is required'}, 
                               status=status.HTTP_400_BAD_REQUEST)
        
        # Create the center
        center = Center(
            name=request.data['name'],
            address=request.data['address'],
            created_by=request.user
        )
        
        # Set optional fields if provided
        optional_fields = [
            'description', 'city', 'state', 'postal_code', 'country',
            'phone', 'alternate_phone', 'email', 'website',
            'header_color', 'accent_color',
            'admin_name', 'admin_email', 'admin_phone',
            'max_users', 'max_citizens', 'max_id_cards',
            'is_active', 'is_verified', 'subscription_status'
        ]
        
        for field in optional_fields:
            if field in request.data:
                setattr(center, field, request.data[field])
        
        # Set center type if provided
        if 'type_id' in request.data:
            try:
                center_type = CenterType.objects.get(id=request.data['type_id'])
                center.type = center_type
            except CenterType.DoesNotExist:
                return Response({'error': f'Center type with ID {request.data["type_id"]} does not exist'}, 
                               status=status.HTTP_400_BAD_REQUEST)
        
        # Save the center
        center.save()
        
        # Return the created center
        return Response({
            'id': center.id,
            'name': center.name,
            'code': center.code,
            'slug': center.slug,
            'type': center.type.name if center.type else None,
            'description': center.description,
            'address': center.address,
            'city': center.city,
            'state': center.state,
            'email': center.email,
            'phone': center.phone,
            'website': center.website,
            'is_active': center.is_active,
            'is_verified': center.is_verified,
            'created_at': center.created_at.isoformat() if center.created_at else None,
        }, status=status.HTTP_201_CREATED)
