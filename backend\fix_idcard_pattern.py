import os
import django
import json

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'neocamelot.settings')
django.setup()

from idcards.models import IDCard
from idcards.security_patterns import apply_kebele_pattern, generate_pattern_seed, generate_pattern_halves
from django.db import connection

# Function to fix the database schema
def fix_database_schema():
    print("=== Checking Database Schema ===")
    with connection.cursor() as cursor:
        # Define all the columns that should exist
        columns_to_check = [
            ('kebele_pattern', 'character varying(255)'),
            ('pattern_seed', 'character varying(64)'),
            ('subcity_pattern', 'character varying(255)'),
            ('kebele_approval_status', 'character varying(20)'),
            ('kebele_approval_notes', 'text'),
            ('kebele_approved_at', 'timestamp with time zone'),
            ('kebele_approved_by_id', 'bigint'),
            ('document_verification_required', 'boolean'),
            ('document_verification_status', 'character varying(20)'),
            ('document_verification_notes', 'text'),
            ('document_verified_at', 'timestamp with time zone'),
            ('document_verified_by_id', 'bigint')
        ]

        # Check each column and add it if it doesn't exist
        for column_name, column_type in columns_to_check:
            cursor.execute(f"""
                SELECT column_name, data_type
                FROM information_schema.columns
                WHERE table_name = 'idcards_idcard' AND column_name = '{column_name}'
            """)
            column = cursor.fetchone()

            if not column:
                print(f"{column_name} column does not exist. Adding it...")

                # Set default values based on column type
                default_value = ""
                if "boolean" in column_type:
                    default_value = "DEFAULT FALSE"
                elif "character varying" in column_type:
                    default_value = "DEFAULT ''"
                elif "timestamp" in column_type:
                    default_value = "DEFAULT NULL"
                elif "bigint" in column_type:
                    default_value = "DEFAULT NULL"

                cursor.execute(f"""
                    ALTER TABLE idcards_idcard ADD COLUMN {column_name} {column_type} {default_value};
                """)
                print(f"{column_name} column added successfully.")
            else:
                print(f"{column_name} column exists: {column}")

        # Update existing ID cards to set kebele_approval_status if it's null
        cursor.execute("""
            UPDATE idcards_idcard
            SET kebele_approval_status = 'APPROVED'
            WHERE status = 'PENDING_SUBCITY' AND (kebele_approval_status IS NULL OR kebele_approval_status = '');
        """)
        print("Updated kebele_approval_status for ID cards with status = 'PENDING_SUBCITY'")

        # Update existing ID cards to set kebele_approval_status if it's null
        cursor.execute("""
            UPDATE idcards_idcard
            SET kebele_approval_status = 'APPROVED'
            WHERE status = 'APPROVED' AND (kebele_approval_status IS NULL OR kebele_approval_status = '');
        """)
        print("Updated kebele_approval_status for ID cards with status = 'APPROVED'")

# Function to fix ID cards with missing patterns
def fix_idcard_patterns():
    print("\n=== Fixing ID Card Patterns ===")

    # First, let's check all ID cards
    print("Checking all ID cards...")
    with connection.cursor() as cursor:
        cursor.execute("""
            SELECT id, status, kebele_approval_status, kebele_pattern
            FROM idcards_idcard
            ORDER BY id
        """)
        all_cards = cursor.fetchall()

        print(f"Found {len(all_cards)} ID cards in total")

        # Find cards that need patterns
        cards_to_fix = []
        for card in all_cards:
            card_id, status, kebele_approval_status, kebele_pattern = card

            # If the card is approved but has no pattern, it needs to be fixed
            if (status == 'APPROVED' or status == 'PENDING_SUBCITY' or
                (status == 'PENDING' and kebele_approval_status == 'APPROVED')):
                if not kebele_pattern:
                    cards_to_fix.append(card_id)

        print(f"Found {len(cards_to_fix)} ID cards that need patterns")

        # Fix each card
        for card_id in cards_to_fix:
            print(f"\nFixing ID Card {card_id}:")

            try:
                # Get the card from the database
                cursor.execute(f"""
                    SELECT status, kebele_approval_status
                    FROM idcards_idcard
                    WHERE id = {card_id}
                """)
                card_data = cursor.fetchone()
                status, kebele_approval_status = card_data

                print(f"  Status: {status}")
                print(f"  Kebele Approval Status: {kebele_approval_status}")

                # Generate a pattern seed
                pattern_seed = generate_pattern_seed()
                print(f"  Generated pattern seed: {pattern_seed}")

                # Generate both pattern halves
                kebele_pattern, subcity_pattern = generate_pattern_halves(pattern_seed)

                # Apply the kebele pattern
                print(f"  Applying kebele pattern: {kebele_pattern[:30]}...")

                # Update the database
                cursor.execute(f"""
                    UPDATE idcards_idcard
                    SET pattern_seed = '{pattern_seed}',
                        kebele_pattern = '{kebele_pattern}'
                    WHERE id = {card_id}
                """)
                print(f"  ID card updated successfully")

                # Check if the ID card should be sent to subcity
                if status == 'PENDING' and kebele_approval_status == 'APPROVED':
                    print(f"  This ID card can be sent to subcity. Setting status to PENDING_SUBCITY...")
                    cursor.execute(f"""
                        UPDATE idcards_idcard
                        SET status = 'PENDING_SUBCITY'
                        WHERE id = {card_id}
                    """)
                    print(f"  Status updated to PENDING_SUBCITY")
            except Exception as e:
                print(f"  Error fixing ID card: {str(e)}")

# Main function
def main():
    print("=== ID Card Pattern Fix Script ===")

    # Fix database schema
    fix_database_schema()

    # Fix ID card patterns
    fix_idcard_patterns()

    print("\n=== Script Completed ===")

if __name__ == "__main__":
    main()
