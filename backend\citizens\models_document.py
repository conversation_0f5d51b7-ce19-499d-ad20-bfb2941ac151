from django.db import models
from centers.models_region import Timestamp
from common.models import DocumentType
from .models_base import citizen_directory_path

class Document(Timestamp):
    """Model representing a document uploaded for a citizen."""
    document_type = models.ForeignKey(DocumentType, on_delete=models.CASCADE)
    citizen = models.ForeignKey('citizens.Citizen', on_delete=models.CASCADE, related_name='documents')
    document_file = models.FileField(upload_to=citizen_directory_path)
    issue_date = models.DateField(null=True, blank=True)
    expiry_date = models.DateField(null=True, blank=True)
    is_active = models.BooleanField(default=True)  # Boolean field for activity status

    def __str__(self):
        return f"{self.document_type.name} for {self.citizen.first_name} {self.citizen.last_name}"

    class Meta:
        verbose_name = "Document"
        verbose_name_plural = "Documents"
