from rest_framework.decorators import api_view, permission_classes, authentication_classes
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import AllowAny
from django.views.decorators.csrf import csrf_exempt
from django.contrib.auth import get_user_model
from django.db import connection
from django_tenants.utils import tenant_context
from centers.models import Client
from .jwt_utils import validate_jwt_token
import logging

logger = logging.getLogger(__name__)
User = get_user_model()

@csrf_exempt
@api_view(['GET', 'POST', 'OPTIONS'])
@permission_classes([AllowAny])
@authentication_classes([])
def validate_token(request):
    """
    Validate a JWT token and return user information if valid.
    This endpoint accepts both GET and POST requests for flexibility.

    This endpoint checks if the provided token is valid.
    It returns information about the token, including the user and tenant.
    """
    # Handle preflight OPTIONS request
    if request.method == 'OPTIONS':
        response = Response()
        origin = request.headers.get('Origin', '')
        allowed_origins = ['http://localhost:5173', 'http://127.0.0.1:5173',
                          'http://localhost:5174', 'http://127.0.0.1:5174',
                          'http://localhost:3000', 'http://127.0.0.1:3000']

        if origin in allowed_origins or origin.startswith('http://localhost'):
            response['Access-Control-Allow-Origin'] = origin
            response['Access-Control-Allow-Methods'] = 'GET, POST, OPTIONS'
            response['Access-Control-Allow-Headers'] = 'Content-Type, Authorization, X-Requested-With, X-CSRFToken, X-Schema-Name'
            response['Access-Control-Allow-Credentials'] = 'true'
            response['Access-Control-Max-Age'] = '86400'  # 24 hours
        return response

    # Get the token from the Authorization header
    auth_header = request.headers.get('Authorization', '')

    if not auth_header or not auth_header.startswith('Bearer '):
        # If no token in header, check if it's in the request data
        token = None
        if request.method == 'POST':
            token = request.data.get('token')
        elif request.method == 'GET':
            token = request.query_params.get('token')

        if not token:
            return Response(
                {"error": "Authentication credentials were not provided."},
                status=status.HTTP_401_UNAUTHORIZED
            )
    else:
        # Extract token from Authorization header
        token = auth_header.split(' ')[1].strip()

    # Validate the JWT token
    payload = validate_jwt_token(token)

    if not payload:
        return Response(
            {"valid": False, "error": "Invalid token"},
            status=status.HTTP_401_UNAUTHORIZED
        )

    # Get user ID and schema from payload
    user_id = payload.get('sub')
    schema_name = payload.get('schema')

    if not user_id or not schema_name:
        return Response(
            {"valid": False, "error": "Invalid token payload"},
            status=status.HTTP_401_UNAUTHORIZED
        )

    # Get the tenant by schema name
    try:
        tenant = Client.objects.get(schema_name=schema_name)
    except Client.DoesNotExist:
        return Response(
            {"valid": False, "error": f"Tenant with schema {schema_name} not found"},
            status=status.HTTP_404_NOT_FOUND
        )

    # Set the tenant for this request
    connection.set_tenant(tenant)

    # Get the user
    try:
        with tenant_context(tenant):
            user = User.objects.get(id=user_id)

            # Check if the user is active
            if not user.is_active:
                return Response(
                    {"error": "User account is disabled."},
                    status=status.HTTP_401_UNAUTHORIZED
                )

            # Token is valid
            return Response({
                "valid": True,
                "user": {
                    "id": user.id,
                    "username": getattr(user, 'username', ''),
                    "email": user.email,
                    "role": getattr(user, 'role', None),
                    "first_name": getattr(user, 'first_name', ''),
                    "last_name": getattr(user, 'last_name', '')
                },
                "tenant": {
                    "id": tenant.id,
                    "name": tenant.name,
                    "schema_name": tenant.schema_name,
                    "schema_type": tenant.schema_type
                },
                "token_type": payload.get('token_type', 'access'),
                "exp": payload.get('exp')
            })
    except User.DoesNotExist:
        return Response(
            {"valid": False, "error": f"User with ID {user_id} not found in tenant {schema_name}"},
            status=status.HTTP_404_NOT_FOUND
        )
    except Exception as e:
        logger.error(f"Error validating token: {str(e)}")
        return Response(
            {"valid": False, "error": f"Error validating token: {str(e)}"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )
