from django.utils.deprecation import MiddlewareMixin
from django.http import HttpResponseForbidden
from threading import local
from django_tenants.utils import get_tenant_model, get_public_schema_name
from django.db import connection
from .models import City, Subcity, Kebele, Client, Domain

# Thread local storage for hierarchy context
_hierarchy_context = local()

def get_current_tenant():
    """Get the current tenant from the database connection."""
    return connection.tenant

def get_current_city():
    """Get the current city from thread local storage."""
    return getattr(_hierarchy_context, 'city', None)

def set_current_city(city):
    """Set the current city in thread local storage."""
    _hierarchy_context.city = city

def get_current_subcity():
    """Get the current subcity from thread local storage."""
    return getattr(_hierarchy_context, 'subcity', None)

def set_current_subcity(subcity):
    """Set the current subcity in thread local storage."""
    _hierarchy_context.subcity = subcity

class TenantMiddleware(MiddlewareMixin):
    """
    Middleware to handle multi-tenancy in the application.

    This middleware works with django-tenants to provide schema-based multi-tenancy.
    It also maintains the hierarchical structure of city, subcity, and center.
    """

    def process_request(self, request):
        # Clear hierarchy context at the beginning of request
        set_current_city(None)
        set_current_subcity(None)

        # Skip for admin, API authentication URLs, and login endpoint in public schema
        if connection.schema_name == get_public_schema_name() and (
            request.path.startswith('/admin/') or
            request.path.startswith('/api-auth/') or
            request.path == '/api/login/'
        ):
            return None

        # Get the current tenant from the connection
        tenant = get_current_tenant()

        # Set request attributes based on the tenant
        if tenant and tenant.schema_name != get_public_schema_name():
            request.tenant = tenant

            # Set hierarchy based on tenant type
            if tenant.schema_type == 'CITY':
                request.city = tenant
                set_current_city(tenant)
                request.subcity = None
                request.center = None
            elif tenant.schema_type == 'SUBCITY':
                request.subcity = tenant
                set_current_subcity(tenant)
                request.city = tenant.parent if tenant.parent and tenant.parent.schema_type == 'CITY' else None
                if request.city:
                    set_current_city(request.city)
                request.center = None
            elif tenant.schema_type == 'KEBELE':
                request.center = tenant
                request.subcity = tenant.parent if tenant.parent and tenant.parent.schema_type == 'SUBCITY' else None
                if request.subcity:
                    set_current_subcity(request.subcity)
                    request.city = request.subcity.parent if request.subcity.parent and request.subcity.parent.schema_type == 'CITY' else None
                    if request.city:
                        set_current_city(request.city)
        else:
            # In public schema, no tenant-specific context
            request.tenant = None
            request.city = None
            request.subcity = None
            request.center = None

        # Skip access control for public endpoints
        if request.path == '/api/register-tenant/' or request.path == '/api/login/':
            return None

        # For authenticated users, enforce hierarchy-based access control
        if request.path.startswith('/api/') and hasattr(request, 'user') and request.user.is_authenticated:
            # Super admins can access everything
            if hasattr(request.user, 'is_super_admin') and request.user.is_super_admin:
                return None

            # Check if user has access to the current tenant
            if tenant and tenant.schema_name != get_public_schema_name():
                # City admins can access their city and everything under it
                if hasattr(request.user, 'is_city_admin') and request.user.is_city_admin:
                    if request.city and request.user.city != request.city:
                        return HttpResponseForbidden("You don't have access to this city")
                    return None

                # Subcity admins can access their subcity and everything under it
                if hasattr(request.user, 'is_subcity_admin') and request.user.is_subcity_admin:
                    if request.subcity and request.user.subcity != request.subcity:
                        return HttpResponseForbidden("You don't have access to this subcity")
                    return None

                # Center admins and staff can only access their center
                if hasattr(request.user, 'center') and request.center and request.user.center != request.center:
                    return HttpResponseForbidden("You don't have access to this center")

        return None

    def process_response(self, request, response):
        # Clear hierarchy context at the end of request
        set_current_city(None)
        set_current_subcity(None)
        return response
