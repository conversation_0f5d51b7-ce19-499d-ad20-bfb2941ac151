/**
 * Role-based permissions utility
 * This file defines which roles have access to which routes/features
 */

// Define all possible roles in the system
export type UserRole = 'CITY_ADMIN' | 'SUBCITY_ADMIN' | 'KEBELE_LEADER' | 'ADMIN' | 'CENTER_STAFF' | 'CENTER_ADMIN';

// Define all routes that require permission checks
export type ProtectedRoute =
  | '/dashboard'
  | '/citizens'
  | '/citizens/new'
  | '/citizens/:id'
  | '/id-cards'
  | '/id-cards/new'
  | '/id-cards/:id'
  | '/print-cards'
  | '/document-verification'
  | '/kebele-users'
  | '/subcity-users'
  | '/reports'
  | '/settings'
  | '/profile'
  | '/help';

// Define which roles have access to which routes
export const routePermissions: Record<ProtectedRoute, UserRole[]> = {
  // Common routes for all roles
  '/dashboard': ['CITY_ADMIN', 'SUBCITY_ADMIN', 'KEBELE_LEADER', 'ADMIN', 'CENTER_STAFF', 'CENTER_ADMIN'],
  '/profile': ['CITY_ADMIN', 'SUBCITY_ADMIN', 'KE<PERSON>LE_LEADER', 'ADMIN', 'CENTER_STAFF', 'CENTER_ADMIN'],
  '/help': ['CITY_ADMIN', 'SUBCITY_ADMIN', 'KEBELE_LEADER', 'ADMIN', 'CENTER_STAFF', 'CENTER_ADMIN'],

  // Citizen management routes
  '/citizens': ['SUBCITY_ADMIN', 'KEBELE_LEADER', 'ADMIN', 'CENTER_STAFF', 'CENTER_ADMIN'], // All roles can view citizens list
  '/citizens/new': ['ADMIN', 'CENTER_STAFF', 'CENTER_ADMIN'], // Only CENTER_STAFF can register new citizens
  '/citizens/:id': ['SUBCITY_ADMIN', 'KEBELE_LEADER', 'ADMIN', 'CENTER_STAFF', 'CENTER_ADMIN'], // All roles can view citizen details

  // ID Card management routes
  '/id-cards': ['SUBCITY_ADMIN', 'KEBELE_LEADER', 'ADMIN', 'CENTER_STAFF', 'CENTER_ADMIN'], // All roles can view ID cards list
  '/id-cards/new': ['ADMIN', 'CENTER_STAFF', 'CENTER_ADMIN'], // Only CENTER_STAFF can generate new ID cards
  '/id-cards/:id': ['SUBCITY_ADMIN', 'KEBELE_LEADER', 'ADMIN', 'CENTER_STAFF', 'CENTER_ADMIN'], // All roles can view ID card details

  // Subcity admin routes
  '/print-cards': ['SUBCITY_ADMIN', 'ADMIN'],
  '/kebele-users': ['SUBCITY_ADMIN', 'ADMIN'],  // Only SUBCITY_ADMIN and ADMIN can manage kebele users

  // Document verification - only kebele leaders can verify documents
  '/document-verification': ['KEBELE_LEADER', 'ADMIN'],

  // City admin routes
  '/subcity-users': ['CITY_ADMIN', 'ADMIN'],

  // Report routes - only admins and leaders can view reports
  '/reports': ['CITY_ADMIN', 'SUBCITY_ADMIN', 'KEBELE_LEADER', 'ADMIN'],

  // Settings route
  '/settings': ['CITY_ADMIN', 'SUBCITY_ADMIN', 'KEBELE_LEADER', 'ADMIN'],
};

// Helper function to check if a user has permission to access a route
export const hasRoutePermission = (
  role: UserRole | string | null | undefined,
  route: string
): boolean => {
  if (!role) return false;

  // Admin has access to everything
  if (role === 'ADMIN') return true;

  // Find the most specific matching route
  const matchingRoute = Object.keys(routePermissions).find(pattern => {
    // Convert route pattern to regex
    // Replace :id with any character pattern
    const regexPattern = pattern
      .replace(/:\w+/g, '[^/]+')
      .replace(/\//g, '\\/');

    const regex = new RegExp(`^${regexPattern}$`);
    return regex.test(route);
  }) as ProtectedRoute | undefined;

  if (!matchingRoute) {
    console.warn(`No permission configuration found for route: ${route}`);
    return false;
  }

  return routePermissions[matchingRoute].includes(role as UserRole);
};

// Define feature permissions (for UI elements, buttons, etc.)
export type Feature =
  | 'create_citizen'
  | 'edit_citizen'
  | 'delete_citizen'
  | 'create_idcard'
  | 'approve_idcard'
  | 'reject_idcard'
  | 'print_idcard'
  | 'send_to_subcity'
  | 'create_kebele_user'
  | 'edit_kebele_user'
  | 'delete_kebele_user'
  | 'create_subcity_user'
  | 'edit_subcity_user'
  | 'delete_subcity_user'
  | 'create_kebele_leader'
  | 'create_clerk';

// Define which roles have access to which features
export const featurePermissions: Record<Feature, UserRole[]> = {
  // Citizen management features
  'create_citizen': ['ADMIN', 'CENTER_STAFF', 'CENTER_ADMIN'], // Only CENTER_STAFF can register citizens
  'edit_citizen': ['ADMIN', 'CENTER_STAFF', 'CENTER_ADMIN'], // Only CENTER_STAFF can edit citizens
  'delete_citizen': ['ADMIN'], // Only admin can delete citizens

  // ID Card management features
  'create_idcard': ['ADMIN', 'CENTER_STAFF', 'CENTER_ADMIN'], // Only CENTER_STAFF can generate ID cards
  'approve_idcard': ['KEBELE_LEADER', 'SUBCITY_ADMIN', 'ADMIN'], // Kebele leaders and subcity admins can approve ID cards
  'reject_idcard': ['KEBELE_LEADER', 'SUBCITY_ADMIN', 'ADMIN'], // Kebele leaders and subcity admins can reject ID cards
  'print_idcard': ['SUBCITY_ADMIN', 'ADMIN'], // Only subcity admins can print ID cards
  'send_to_subcity': ['KEBELE_LEADER', 'ADMIN'], // Only kebele leaders can send ID cards to subcity

  // Kebele user management permissions
  'create_kebele_user': ['SUBCITY_ADMIN', 'ADMIN'],
  'edit_kebele_user': ['SUBCITY_ADMIN', 'ADMIN'],
  'delete_kebele_user': ['SUBCITY_ADMIN', 'ADMIN'],
  'create_kebele_leader': ['SUBCITY_ADMIN', 'ADMIN'],
  'create_clerk': ['SUBCITY_ADMIN', 'KEBELE_LEADER', 'ADMIN'],

  // Subcity user management permissions
  'create_subcity_user': ['CITY_ADMIN', 'ADMIN'],
  'edit_subcity_user': ['CITY_ADMIN', 'ADMIN'],
  'delete_subcity_user': ['CITY_ADMIN', 'ADMIN'],
};

// Helper function to check if a user has permission to use a feature
export const hasFeaturePermission = (
  role: UserRole | string | null | undefined,
  feature: Feature
): boolean => {
  if (!role) return false;

  // Admin has access to everything
  if (role === 'ADMIN') return true;

  return featurePermissions[feature].includes(role as UserRole);
};
