<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Login</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f5f5f5;
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
        }
        .login-container {
            background-color: white;
            padding: 30px;
            border-radius: 5px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            width: 350px;
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            color: #555;
        }
        input[type="email"],
        input[type="password"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            width: 100%;
            padding: 10px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background-color: #45a049;
        }
        .error-message {
            color: red;
            margin-top: 15px;
            text-align: center;
            display: none;
        }
        .success-message {
            color: green;
            margin-top: 15px;
            text-align: center;
            display: none;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <h1>Admin Login</h1>
        <form id="loginForm">
            <div class="form-group">
                <label for="email">Email</label>
                <input type="email" id="email" name="email" required>
            </div>
            <div class="form-group">
                <label for="password">Password</label>
                <input type="password" id="password" name="password" required>
            </div>
            <button type="submit">Login</button>
            <div id="errorMessage" class="error-message"></div>
            <div id="successMessage" class="success-message"></div>
        </form>
    </div>

    <script>
        document.getElementById('loginForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const errorMessage = document.getElementById('errorMessage');
            const successMessage = document.getElementById('successMessage');

            // Hide messages
            errorMessage.style.display = 'none';
            successMessage.style.display = 'none';

            try {
                // First try JWT login
                const jwtResponse = await fetch('/api/jwt/login/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        email,
                        password,
                        schema_name: 'public'  // Use public schema for admin
                    }),
                    credentials: 'include'
                });

                const jwtData = await jwtResponse.json();

                if (jwtResponse.ok && jwtData.access_token) {
                    // Store tokens in localStorage
                    localStorage.setItem('jwt_token', jwtData.access_token);
                    localStorage.setItem('jwt_refresh_token', jwtData.refresh_token);

                    // Set tokens in cookies (HTTPOnly for refresh token)
                    document.cookie = `jwt_access_token=${jwtData.access_token}; path=/; max-age=900`; // 15 minutes

                    // Also try the admin login endpoint for backward compatibility
                    try {
                        const adminResponse = await fetch('/api/admin-login/', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'Authorization': `Bearer ${jwtData.access_token}`
                            },
                            body: JSON.stringify({ email, password }),
                            credentials: 'include'
                        });

                        const adminData = await adminResponse.json();

                        if (adminResponse.ok) {
                            // Store token in localStorage for backward compatibility
                            localStorage.setItem('adminToken', adminData.token);
                        }
                    } catch (adminError) {
                        console.warn('Admin login endpoint failed, but JWT login succeeded:', adminError);
                    }

                    // Show success message
                    successMessage.textContent = 'Login successful! Redirecting to admin...';
                    successMessage.style.display = 'block';

                    // Redirect to admin page after a short delay
                    setTimeout(() => {
                        window.location.href = '/admin/';
                    }, 1000);
                } else {
                    // Try the old admin login endpoint as fallback
                    const adminResponse = await fetch('/api/admin-login/', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({ email, password }),
                        credentials: 'include'
                    });

                    const adminData = await adminResponse.json();

                    if (adminResponse.ok) {
                        // Store token in localStorage
                        localStorage.setItem('adminToken', adminData.token);

                        // Set token in cookie
                        document.cookie = `adminToken=${adminData.token}; path=/; max-age=86400`;

                        // Show success message
                        successMessage.textContent = 'Login successful! Redirecting to admin...';
                        successMessage.style.display = 'block';

                        // Redirect to admin page after a short delay
                        setTimeout(() => {
                            window.location.href = '/admin/';
                        }, 1000);
                    } else {
                        // Show error message
                        errorMessage.textContent = (jwtData.error || adminData.error || 'Login failed') +
                            ' (Make sure you are using an admin account)';
                        errorMessage.style.display = 'block';
                    }
                }
            } catch (error) {
                console.error('Error:', error);
                errorMessage.textContent = 'An error occurred. Please try again.';
                errorMessage.style.display = 'block';
            }
        });
    </script>
</body>
</html>
