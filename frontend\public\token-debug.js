/**
 * Token Debug Utility
 * 
 * This script provides utility functions to debug token issues in the browser console.
 * You can call these functions from the browser console to diagnose and fix token issues.
 */

// Make the functions available globally
window.tokenDebug = {
  /**
   * Diagnose token issues
   */
  diagnose: function() {
    console.log('Token Debug: Diagnosing token issues...');
    
    // Get the token from localStorage
    const token = localStorage.getItem('token');
    if (!token) {
      console.error('No token found in localStorage');
      return;
    }
    
    console.log('Token from localStorage:', token);
    console.log('Token length:', token.length);
    
    // Check for spaces
    const hasSpaces = /\s/.test(token);
    console.log('Token has spaces:', hasSpaces);
    
    if (hasSpaces) {
      console.log('Spaces found at positions:');
      for (let i = 0; i < token.length; i++) {
        if (/\s/.test(token[i])) {
          console.log(`  Position ${i}: '${token[i].replace(' ', '␣')}'`);
        }
      }
    }
    
    // Get the tokenStore
    const tokenStoreStr = localStorage.getItem('tokenStore');
    if (tokenStoreStr) {
      try {
        const tokenStore = JSON.parse(tokenStoreStr);
        console.log('TokenStore:', tokenStore);
        
        // Check each token in the tokenStore
        Object.keys(tokenStore).forEach(schema => {
          const schemaToken = tokenStore[schema];
          console.log(`Token for schema ${schema}:`, schemaToken);
          console.log(`Token length for schema ${schema}:`, schemaToken.length);
          
          // Check for spaces
          const hasSpaces = /\s/.test(schemaToken);
          console.log(`Token for schema ${schema} has spaces:`, hasSpaces);
          
          if (hasSpaces) {
            console.log(`Spaces found in token for schema ${schema} at positions:`);
            for (let i = 0; i < schemaToken.length; i++) {
              if (/\s/.test(schemaToken[i])) {
                console.log(`  Position ${i}: '${schemaToken[i].replace(' ', '␣')}'`);
              }
            }
          }
        });
      } catch (error) {
        console.error('Error parsing tokenStore:', error);
      }
    } else {
      console.log('No tokenStore found in localStorage');
    }
    
    // Get the schema_name
    const schema = localStorage.getItem('schema_name');
    console.log('Schema name:', schema);
    
    // Get the tenant
    const tenantStr = localStorage.getItem('tenant');
    if (tenantStr) {
      try {
        const tenant = JSON.parse(tenantStr);
        console.log('Tenant:', tenant);
      } catch (error) {
        console.error('Error parsing tenant:', error);
      }
    } else {
      console.log('No tenant found in localStorage');
    }
    
    // Get all cookies
    console.log('Cookies:', document.cookie);
  },
  
  /**
   * Fix token issues
   */
  fix: function() {
    console.log('Token Debug: Fixing token issues...');
    
    // Get the token from localStorage
    const token = localStorage.getItem('token');
    if (!token) {
      console.error('No token found in localStorage');
      return;
    }
    
    // Clean the token
    const cleanToken = token.replace(/\s+/g, '');
    console.log('Original token:', token);
    console.log('Cleaned token:', cleanToken);
    
    // Update the token in localStorage
    localStorage.setItem('token', cleanToken);
    console.log('Updated token in localStorage');
    
    // Get the schema_name
    const schema = localStorage.getItem('schema_name');
    if (schema) {
      // Update the token in the tokenStore
      const tokenStoreStr = localStorage.getItem('tokenStore');
      let tokenStore = tokenStoreStr ? JSON.parse(tokenStoreStr) : {};
      
      // Update the token for the schema
      tokenStore[schema] = cleanToken;
      
      // Save the tokenStore
      localStorage.setItem('tokenStore', JSON.stringify(tokenStore));
      console.log(`Updated token for schema ${schema} in tokenStore`);
      
      // Set the token in a cookie
      document.cookie = `auth_token=${encodeURIComponent(cleanToken)}; path=/; SameSite=Lax`;
      console.log('Set auth_token cookie');
    }
    
    console.log('Token issues fixed');
  },
  
  /**
   * Show the token in a readable format
   */
  showToken: function() {
    const token = localStorage.getItem('token');
    if (!token) {
      console.error('No token found in localStorage');
      return;
    }
    
    // Display the token with each character separated
    let output = 'Token characters:\n';
    for (let i = 0; i < token.length; i++) {
      const char = token[i];
      const charCode = token.charCodeAt(i);
      output += `Position ${i}: '${char}' (charCode: ${charCode})\n`;
    }
    
    console.log(output);
  }
};

console.log('Token debug utilities loaded. Call window.tokenDebug.diagnose() to diagnose token issues, window.tokenDebug.fix() to fix them, or window.tokenDebug.showToken() to show the token in a readable format.');
