"""
<PERSON><PERSON><PERSON> to update the parent-child relationship for a tenant.
This script directly updates the database using SQL.
"""
import psycopg2
import sys

# Database connection parameters - update these to match your configuration
DB_NAME = "neocamelot"
DB_USER = "postgres"
DB_PASSWORD = "postgres"
DB_HOST = "localhost"
DB_PORT = "5432"

def list_tenants():
    """List all tenants in the system."""
    try:
        # Connect to the database
        conn = psycopg2.connect(
            dbname=DB_NAME,
            user=DB_USER,
            password=DB_PASSWORD,
            host=DB_HOST,
            port=DB_PORT
        )
        
        # Create a cursor
        cur = conn.cursor()
        
        # Execute the query to list all tenants
        cur.execute("""
            SELECT c1.id, c1.name, c1.schema_name, c1.schema_type, c1.parent_id, c2.name as parent_name
            FROM centers_client c1
            LEFT JOIN centers_client c2 ON c1.parent_id = c2.id
            ORDER BY c1.schema_type, c1.name
        """)
        
        # Fetch all results
        tenants = cur.fetchall()
        
        # Print the results
        print(f"Found {len(tenants)} tenants:")
        for tenant in tenants:
            tenant_id, name, schema_name, schema_type, parent_id, parent_name = tenant
            print(f"  {name} (ID: {tenant_id}, Schema: {schema_name}, Type: {schema_type}, Parent: {parent_name or 'None'})")
        
        # Close the cursor and connection
        cur.close()
        conn.close()
        
        return True
    
    except Exception as e:
        print(f"Error listing tenants: {str(e)}")
        return False

def update_tenant_parent(tenant_name, parent_name):
    """
    Update the parent for a tenant.
    
    Args:
        tenant_name (str): The name of the tenant to update
        parent_name (str): The name of the parent tenant
    """
    try:
        # Connect to the database
        conn = psycopg2.connect(
            dbname=DB_NAME,
            user=DB_USER,
            password=DB_PASSWORD,
            host=DB_HOST,
            port=DB_PORT
        )
        
        # Create a cursor
        cur = conn.cursor()
        
        # Get the tenant ID
        cur.execute("SELECT id, schema_name, schema_type, parent_id FROM centers_client WHERE name = %s", (tenant_name,))
        tenant = cur.fetchone()
        
        if not tenant:
            print(f"Error: Tenant '{tenant_name}' not found.")
            cur.close()
            conn.close()
            return False
        
        tenant_id, tenant_schema, tenant_type, current_parent_id = tenant
        
        # Get the parent ID
        cur.execute("SELECT id, schema_name, schema_type FROM centers_client WHERE name = %s", (parent_name,))
        parent = cur.fetchone()
        
        if not parent:
            print(f"Error: Parent tenant '{parent_name}' not found.")
            cur.close()
            conn.close()
            return False
        
        parent_id, parent_schema, parent_type = parent
        
        # Get the current parent name (if any)
        current_parent_name = None
        if current_parent_id:
            cur.execute("SELECT name FROM centers_client WHERE id = %s", (current_parent_id,))
            current_parent = cur.fetchone()
            if current_parent:
                current_parent_name = current_parent[0]
        
        # Print current state
        print(f"Current state:")
        print(f"  Tenant: {tenant_name} (ID: {tenant_id}, Schema: {tenant_schema}, Type: {tenant_type})")
        print(f"  Current parent: {current_parent_name or 'None'} (ID: {current_parent_id or 'None'})")
        print(f"  Setting parent to: {parent_name} (ID: {parent_id}, Schema: {parent_schema}, Type: {parent_type})")
        
        # Update the parent
        cur.execute("UPDATE centers_client SET parent_id = %s WHERE id = %s", (parent_id, tenant_id))
        conn.commit()
        
        # Verify the update
        cur.execute("""
            SELECT c1.id, c1.name, c1.schema_name, c1.schema_type, c1.parent_id, c2.name as parent_name
            FROM centers_client c1
            LEFT JOIN centers_client c2 ON c1.parent_id = c2.id
            WHERE c1.id = %s
        """, (tenant_id,))
        
        updated_tenant = cur.fetchone()
        tenant_id, name, schema_name, schema_type, parent_id, parent_name = updated_tenant
        
        print(f"Updated state:")
        print(f"  Tenant: {name} (ID: {tenant_id}, Schema: {schema_name}, Type: {schema_type})")
        print(f"  New parent: {parent_name or 'None'} (ID: {parent_id or 'None'})")
        
        # Close the cursor and connection
        cur.close()
        conn.close()
        
        return True
    
    except Exception as e:
        print(f"Error updating tenant parent: {str(e)}")
        return False

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("Usage:")
        print("  python update_tenant_parent.py list")
        print("  python update_tenant_parent.py update <tenant_name> <parent_name>")
        sys.exit(1)
    
    command = sys.argv[1]
    
    if command == "list":
        list_tenants()
    elif command == "update" and len(sys.argv) == 4:
        tenant_name = sys.argv[2]
        parent_name = sys.argv[3]
        update_tenant_parent(tenant_name, parent_name)
    else:
        print("Invalid command or missing arguments.")
        print("Usage:")
        print("  python update_tenant_parent.py list")
        print("  python update_tenant_parent.py update <tenant_name> <parent_name>")
        sys.exit(1)
