// Authentication utilities for handling tokens and authentication errors

/**
 * Handles authentication errors by redirecting to login page if needed
 * @param error The error object
 * @returns True if the error was handled, false otherwise
 */
export const handleAuthError = (error: any): boolean => {
  // Check if the error is an authentication error (401 Unauthorized)
  if (error.message && (
    error.message.includes('401') ||
    error.message.includes('Unauthorized') ||
    error.message.toLowerCase().includes('authentication')
  )) {
    console.error('Authentication error detected:', error.message);

    // Clear authentication data
    localStorage.removeItem('token');
    localStorage.removeItem('user');

    // Redirect to login page
    window.location.href = '/login?error=session_expired';
    return true;
  }

  return false;
};

/**
 * Gets the authentication token from localStorage
 * @returns The authentication token or empty string if not found
 */
export const getAuthToken = (): string => {
  return localStorage.getItem('token') || '';
};

/**
 * Validates if the current token is valid
 * @returns True if the token is valid, false otherwise
 */
export const isTokenValid = (): boolean => {
  const token = getAuthToken();

  // Check if token exists
  if (!token) {
    console.error('No authentication token found');
    return false;
  }

  // Check if token is properly formatted (simple check)
  if (token.length < 10) {
    console.error('Token appears to be invalid (too short)');
    return false;
  }

  return true;
};

/**
 * Validates a token by making a test API call to the server
 * @param token The token to validate
 * @returns Promise<boolean> - True if token is valid, false otherwise
 */
export const validateTokenWithServer = async (token: string): Promise<boolean> => {
  try {
    // Try to validate the token by making a simple API call to user-info
    // This endpoint should be accessible with a valid token
    const response = await fetch('/api/user-info/', {
      method: 'GET',
      headers: {
        'Authorization': `Token ${token}`,
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      mode: 'cors'
    });

    // If user-info fails, try a different endpoint
    if (response.status === 405 || response.status === 404) {
      console.log('User-info endpoint not available, trying tenants endpoint');
      const tenantsResponse = await fetch('/api/tenants/', {
        method: 'GET',
        headers: {
          'Authorization': `Token ${token}`,
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        mode: 'cors'
      });

      return tenantsResponse.ok;
    }

    return response.ok;
  } catch (error) {
    console.error('Error validating token with server:', error);
    return false;
  }
};

/**
 * Gets a valid token, validating the current one or redirecting to login
 * @param redirectOnFailure Whether to redirect to login page on failure
 * @returns Promise<string | null> - Valid token or null
 */
export const getValidToken = async (redirectOnFailure: boolean = true): Promise<string | null> => {
  const currentToken = getAuthToken();

  if (!currentToken) {
    console.error('No token found in localStorage');
    if (redirectOnFailure) {
      // Redirect to login page
      window.location.href = '/login?error=no_token';
    }
    return null;
  }

  // First do a basic validation
  if (!isTokenValid()) {
    console.error('Token failed basic validation');
    if (redirectOnFailure) {
      // Redirect to login page
      window.location.href = '/login?error=invalid_token';
    }
    return null;
  }

  // Then validate with the server
  const isValid = await validateTokenWithServer(currentToken);

  if (isValid) {
    return currentToken;
  } else {
    console.error('Token validation failed on server');
    if (redirectOnFailure) {
      // Clear authentication data
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      localStorage.removeItem('tenant');
      localStorage.removeItem('schema_name');

      // Redirect to login page
      window.location.href = '/login?error=session_expired';
    }
    return null;
  }
};

/**
 * Validates the current authentication state and redirects to login if invalid
 * @param redirectOnFailure Whether to redirect to login page if validation fails
 * @returns Promise<boolean> - True if authentication is valid, false otherwise
 */
export const validateAuth = async (redirectOnFailure: boolean = false): Promise<boolean> => {
  // First do a basic validation
  if (!isTokenValid()) {
    console.warn('Basic authentication validation failed');

    if (redirectOnFailure) {
      // Clear authentication data
      localStorage.removeItem('token');
      localStorage.removeItem('user');

      // Redirect to login page
      window.location.href = '/login?error=invalid_token';
    }

    return false;
  }

  // Then validate with the server
  const token = getAuthToken();
  const isValid = await validateTokenWithServer(token);

  if (!isValid) {
    console.warn('Server authentication validation failed');

    if (redirectOnFailure) {
      // Clear authentication data
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      localStorage.removeItem('tenant');
      localStorage.removeItem('schema_name');

      // Redirect to login page
      window.location.href = '/login?error=session_expired';
    }

    return false;
  }

  return true;
};

/**
 * Creates headers with authentication token and schema name
 * @param schema The schema name
 * @returns Headers object with authentication token and schema name
 */
export const createAuthHeaders = (schema: string): HeadersInit => {
  const token = getAuthToken();

  // Log the token being used
  console.log('Creating auth headers with token:', token);

  return {
    'Authorization': `Token ${token}`,
    'Content-Type': 'application/json',
    'X-Schema-Name': schema
  };
};

/**
 * Makes an authenticated API request with proper error handling
 * @param url The URL to fetch
 * @param options The fetch options
 * @param schema The schema name
 * @param redirectOnAuthFailure Whether to redirect to login page if authentication fails
 * @param validateTokenFirst Whether to validate the token with the server before making the request
 * @returns The response data
 */
export const fetchWithAuth = async (
  url: string,
  options: RequestInit = {},
  schema: string = '',
  redirectOnAuthFailure: boolean = true,
  validateTokenFirst: boolean = true
): Promise<any> => {
  try {
    // Get a valid token
    let token;

    if (validateTokenFirst) {
      // Validate token with server before making the request
      token = await getValidToken(redirectOnAuthFailure);
      if (!token) {
        throw new Error('Authentication failed. Please log in again.');
      }
    } else {
      // Just get the token without validation
      token = getAuthToken();
      if (!token) {
        if (redirectOnAuthFailure) {
          window.location.href = '/login?error=no_token';
        }
        throw new Error('No authentication token found. Please log in again.');
      }
    }

    // Create headers with the validated token
    const headers = {
      'Authorization': `Token ${token}`,
      'Content-Type': 'application/json',
      'X-Schema-Name': schema
    };

    // Merge headers with existing options
    const mergedOptions = {
      ...options,
      headers: {
        ...headers,
        ...(options.headers || {})
      },
      credentials: 'include' as RequestCredentials
    };

    // Log the request details for debugging
    console.log('Making authenticated request to:', url);
    console.log('Using token:', token.substring(0, 10) + '...');
    console.log('Using schema:', schema);
    console.log('Request headers:', JSON.stringify(mergedOptions.headers, null, 2));

    // Make the request
    const response = await fetch(url, mergedOptions);

    // Log response status for debugging
    console.log(`Response status: ${response.status} ${response.statusText}`);

    // Handle non-OK responses
    if (!response.ok) {
      // Handle 401 Unauthorized errors
      if (response.status === 401) {
        console.error('Received 401 Unauthorized response');

        // Try to get more details from the response
        try {
          const errorData = await response.json();
          console.error('Error details:', errorData);
        } catch (e) {
          console.error('Could not parse error response');
        }

        if (redirectOnAuthFailure) {
          // Clear authentication data
          localStorage.removeItem('token');
          localStorage.removeItem('user');
          localStorage.removeItem('tenant');
          localStorage.removeItem('schema_name');

          // Redirect to login page
          window.location.href = '/login?error=session_expired';
        }

        throw new Error('Authentication failed. Please log in again.');
      }

      // Try to parse error response as JSON
      try {
        const errorData = await response.json();
        throw new Error(errorData.error || errorData.detail || `HTTP error ${response.status}: ${response.statusText}`);
      } catch (jsonError) {
        // If parsing fails, use status text
        throw new Error(`HTTP error ${response.status}: ${response.statusText}`);
      }
    }

    // Parse response as JSON
    try {
      return await response.json();
    } catch (jsonError) {
      console.error('Error parsing JSON response:', jsonError);

      // Check if the response is empty (which is valid in some cases)
      const text = await response.text();
      if (!text) {
        return null; // Return null for empty responses
      }

      throw new Error('Failed to parse server response');
    }
  } catch (error) {
    // Handle authentication errors
    if (error instanceof Error && redirectOnAuthFailure && handleAuthError(error)) {
      throw new Error('Authentication failed. Please log in again.');
    }

    // Re-throw other errors
    throw error;
  }
};
