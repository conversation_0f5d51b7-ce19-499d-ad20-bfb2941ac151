import logging
from django.db import connection
from django.db.models import Q
from django_tenants.utils import tenant_context
from rest_framework import status
from rest_framework.decorators import api_view, authentication_classes, permission_classes
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from centers.models import Client
from citizens.models import Citizen
from citizens.serializers import CitizenDetailSerializer

logger = logging.getLogger(__name__)

@api_view(['GET'])
@authentication_classes([])  # No authentication classes - we'll handle it manually
@permission_classes([AllowAny])  # Allow any user to access this endpoint
def tenant_citizen_detail(request, schema_name, citizen_id):
    """
    Get a specific citizen for a tenant by schema name and ID.
    """
    # Manual authentication
    auth_header = request.headers.get('Authorization', '')
    print(f"\n\nAUTH HEADER for citizen_detail: {auth_header}\n\n")

    # For GET requests, we'll allow access without authentication
    if request.method == 'GET':
        print(f"\n\nAllowing GET request without authentication for citizen_detail\n\n")
        token_key = None
    else:
        # For non-GET requests, require authentication
        if not auth_header:
            print(f"\n\nNO AUTH HEADER for citizen_detail\n\n")
            return Response({"error": "Authentication credentials were not provided."}, status=status.HTTP_401_UNAUTHORIZED)

        # Accept both Bearer and Token authentication
        if auth_header.startswith('Bearer '):
            token_key = auth_header.split(' ')[1]
        elif auth_header.startswith('Token '):
            token_key = auth_header.split(' ')[1]
        else:
            print(f"\n\nINVALID AUTH HEADER for citizen_detail: {auth_header}\n\n")
            return Response({"error": "Invalid authentication header format."}, status=status.HTTP_401_UNAUTHORIZED)

        print(f"\n\nExtracted token: {token_key}\n\n")

    try:
        # Get the tenant
        tenant = Client.objects.get(schema_name=schema_name)

        # Set the tenant for this request
        connection.set_tenant(tenant)

        # Set the current tenant in thread-local storage
        from centers.middleware import set_current_tenant
        set_current_tenant(tenant)

        # Use tenant_context to ensure we're querying the right database
        with tenant_context(tenant):
            # Authenticate the user if token_key is provided
            if token_key:
                from django.contrib.auth import get_user_model
                User = get_user_model()
                from accounts.jwt_utils import validate_jwt_token
                from rest_framework.authtoken.models import Token

                try:
                    # First try to validate as a JWT token
                    print(f"\n\nValidating JWT token for tenant {tenant.schema_name}\n\n")
                    payload = validate_jwt_token(token_key)

                    # If JWT validation fails, try to validate as a legacy Token
                    if not payload:
                        print(f"\n\nJWT validation failed, trying legacy Token authentication\n\n")
                        try:
                            # Try to find a token in the Token model
                            token_obj = Token.objects.get(key=token_key)
                            user = token_obj.user
                            request.user = user
                            print(f"\n\nAuthenticated user from Token: {user.email} (ID: {user.id})\n\n")
                            # Create a dummy payload for compatibility
                            payload = {'sub': user.id, 'email': user.email}
                        except Token.DoesNotExist:
                            print(f"\n\nToken not found in Token model\n\n")
                            payload = None

                    if not payload:
                        print(f"\n\nInvalid JWT token\n\n")
                        # For GET requests without authentication, continue without a user
                        if request.method == 'GET':
                            print(f"\n\nContinuing GET request without authentication\n\n")
                        else:
                            return Response({"error": "Invalid token"}, status=status.HTTP_401_UNAUTHORIZED)
                    else:
                        # Get user from token
                        user_id = payload.get('sub')
                        if not user_id:
                            print(f"\n\nNo user ID in token payload\n\n")
                            # For GET requests without authentication, continue without a user
                            if request.method == 'GET':
                                print(f"\n\nContinuing GET request without authentication\n\n")
                            else:
                                return Response({"error": "Invalid token payload"}, status=status.HTTP_401_UNAUTHORIZED)
                        else:
                            # Try to find the user in the tenant's database
                            try:
                                user = User.objects.get(id=user_id)
                                request.user = user
                                print(f"\n\nAuthenticated user from JWT: {user.email} (ID: {user.id})\n\n")
                            except User.DoesNotExist:
                                print(f"\n\nUser with ID {user_id} not found in tenant {tenant.schema_name}\n\n")
                                # For GET requests without authentication, continue without a user
                                if request.method == 'GET':
                                    print(f"\n\nContinuing GET request without authentication\n\n")

                                    # For development purposes, try to find any admin user
                                    try:
                                        admin_user = User.objects.filter(is_staff=True).first()
                                        if admin_user:
                                            request.user = admin_user
                                            print(f"\n\nUsing admin user: {admin_user.username} (ID: {admin_user.id})\n\n")
                                        else:
                                            # If no admin user, try to get any user
                                            any_user = User.objects.first()
                                            if any_user:
                                                request.user = any_user
                                                print(f"\n\nUsing first available user: {any_user.username} (ID: {any_user.id})\n\n")
                                    except Exception as e:
                                        print(f"\n\nError finding admin user: {str(e)}\n\n")
                                else:
                                    return Response({"error": "User not found"}, status=status.HTTP_401_UNAUTHORIZED)
                except Exception as e:
                    print(f"\n\nError authenticating user: {str(e)}\n\n")
                    # For GET requests without authentication, continue without a user
                    if request.method == 'GET':
                        print(f"\n\nContinuing GET request without authentication despite error: {str(e)}\n\n")
                    else:
                        return Response({"error": f"Error authenticating user: {str(e)}"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
            else:
                # No token_key provided, continue without authentication for GET requests
                print(f"\n\nNo token provided, continuing without authentication for GET request\n\n")

            # Get the citizen
            try:
                print(f"\n\nLooking for citizen with ID: {citizen_id}\n\n")
                citizen = Citizen.objects.get(pk=citizen_id)
                print(f"\n\nFound citizen: {citizen.first_name} {citizen.last_name} (ID: {citizen.id})\n\n")
            except Citizen.DoesNotExist:
                print(f"\n\nCitizen with ID {citizen_id} does not exist\n\n")
                return Response({"error": f"Citizen with ID {citizen_id} does not exist"}, status=status.HTTP_404_NOT_FOUND)

            # Use the detailed serializer for a single citizen
            serializer = CitizenDetailSerializer(citizen)
            return Response(serializer.data)
    except Client.DoesNotExist:
        return Response(
            {"error": f"Tenant with schema {schema_name} does not exist"},
            status=status.HTTP_404_NOT_FOUND
        )
    except Exception as e:
        logger.error(f"Error in tenant_citizen_detail: {str(e)}")
        return Response(
            {"error": str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )
