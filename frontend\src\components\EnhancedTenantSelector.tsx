import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  CircularProgress,
  TextField,
  InputAdornment,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Collapse,
  Button,
  Divider,
  Alert,
  Chip
} from '@mui/material';
import SearchIcon from '@mui/icons-material/Search';
import LocationCityIcon from '@mui/icons-material/LocationCity';
import ApartmentIcon from '@mui/icons-material/Apartment';
import HomeIcon from '@mui/icons-material/Home';
import ExpandLess from '@mui/icons-material/ExpandLess';
import ExpandMore from '@mui/icons-material/ExpandMore';
import { apiClient } from '../utils/apiClient';
import { switchTenantContext } from '../utils/tenantContextManager';

// Tenant interface
interface Tenant {
  id: number;
  name: string;
  schema_name: string;
  schema_type: string;
  parent_id: number | null;
  children?: Tenant[];
}

// Props interface
interface EnhancedTenantSelectorProps {
  onTenantSelected?: (tenant: Tenant) => void;
  showHierarchy?: boolean;
  currentTenantSchema?: string | null;
}

const EnhancedTenantSelector: React.FC<EnhancedTenantSelectorProps> = ({
  onTenantSelected,
  showHierarchy = true,
  currentTenantSchema = null
}) => {
  // State
  const [tenants, setTenants] = useState<Tenant[]>([]);
  const [filteredTenants, setFilteredTenants] = useState<Tenant[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [expandedTenants, setExpandedTenants] = useState<Record<number, boolean>>({});
  const [selectedTenant, setSelectedTenant] = useState<Tenant | null>(null);
  const [switchingTenant, setSwitchingTenant] = useState<number | null>(null);

  // Fetch tenants on component mount
  useEffect(() => {
    fetchTenants();
  }, []);

  // Filter tenants when search query changes
  useEffect(() => {
    if (searchQuery) {
      const filtered = tenants.filter(tenant =>
        tenant.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        tenant.schema_name.toLowerCase().includes(searchQuery.toLowerCase())
      );
      setFilteredTenants(filtered);
    } else {
      setFilteredTenants(tenants);
    }
  }, [searchQuery, tenants]);

  // Set selected tenant based on currentTenantSchema
  useEffect(() => {
    if (currentTenantSchema && tenants.length > 0) {
      const tenant = tenants.find(t => t.schema_name === currentTenantSchema);
      if (tenant) {
        setSelectedTenant(tenant);
      }
    }
  }, [currentTenantSchema, tenants]);

  // Fetch tenants from the API
  const fetchTenants = async () => {
    setLoading(true);
    setError(null);

    try {
      const response = await apiClient.get<Tenant[]>('available-tenants/');
      
      // Organize tenants into a hierarchy
      const tenantsWithHierarchy = organizeTenantsHierarchy(response);
      
      setTenants(tenantsWithHierarchy);
      setFilteredTenants(tenantsWithHierarchy);
    } catch (err) {
      console.error('Error fetching tenants:', err);
      setError('Failed to fetch tenants. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  // Organize tenants into a hierarchy
  const organizeTenantsHierarchy = (tenants: Tenant[]): Tenant[] => {
    // Create a map of tenants by ID
    const tenantMap: Record<number, Tenant> = {};
    tenants.forEach(tenant => {
      tenantMap[tenant.id] = { ...tenant, children: [] };
    });

    // Create the hierarchy
    const rootTenants: Tenant[] = [];
    tenants.forEach(tenant => {
      if (tenant.parent_id === null) {
        // This is a root tenant (city)
        rootTenants.push(tenantMap[tenant.id]);
      } else if (tenantMap[tenant.parent_id]) {
        // This is a child tenant (subcity or kebele)
        tenantMap[tenant.parent_id].children = tenantMap[tenant.parent_id].children || [];
        tenantMap[tenant.parent_id].children!.push(tenantMap[tenant.id]);
      }
    });

    return rootTenants;
  };

  // Toggle expanded state for a tenant
  const toggleExpanded = (tenantId: number) => {
    setExpandedTenants(prev => ({
      ...prev,
      [tenantId]: !prev[tenantId]
    }));
  };

  // Handle tenant selection
  const handleSelectTenant = async (tenant: Tenant) => {
    setSelectedTenant(tenant);
    setSwitchingTenant(tenant.id);

    try {
      // Switch to the selected tenant context
      const success = await switchTenantContext(tenant.schema_name);
      
      if (success) {
        // Call the onTenantSelected callback if provided
        if (onTenantSelected) {
          onTenantSelected(tenant);
        }
        
        // Redirect to login page with the schema parameter if needed
        if (window.location.pathname === '/') {
          window.location.href = `/login?schema=${encodeURIComponent(tenant.schema_name)}`;
        }
      } else {
        setError(`Failed to switch to tenant ${tenant.name}. Please try again.`);
      }
    } catch (err) {
      console.error('Error selecting tenant:', err);
      setError(`Failed to select ${tenant.name}. Please try again later.`);
    } finally {
      setSwitchingTenant(null);
    }
  };

  // Render a tenant item
  const renderTenantItem = (tenant: Tenant, level: number = 0) => {
    const isExpanded = expandedTenants[tenant.id] || false;
    const hasChildren = tenant.children && tenant.children.length > 0;
    const isSelected = selectedTenant?.id === tenant.id;
    const isSwitching = switchingTenant === tenant.id;

    // Get the appropriate icon based on tenant type
    const getTenantIcon = () => {
      switch (tenant.schema_type) {
        case 'CITY':
          return <LocationCityIcon />;
        case 'SUBCITY':
          return <ApartmentIcon />;
        case 'KEBELE':
          return <HomeIcon />;
        default:
          return <LocationCityIcon />;
      }
    };

    return (
      <React.Fragment key={tenant.id}>
        <ListItem
          button
          onClick={() => hasChildren ? toggleExpanded(tenant.id) : handleSelectTenant(tenant)}
          sx={{
            pl: level * 2 + 2,
            bgcolor: isSelected ? 'rgba(0, 0, 0, 0.08)' : 'transparent',
            '&:hover': {
              bgcolor: isSelected ? 'rgba(0, 0, 0, 0.12)' : 'rgba(0, 0, 0, 0.04)'
            }
          }}
        >
          <ListItemIcon sx={{ minWidth: 36 }}>
            {getTenantIcon()}
          </ListItemIcon>
          <ListItemText
            primary={tenant.name}
            secondary={tenant.schema_name}
            primaryTypographyProps={{
              fontWeight: isSelected ? 'bold' : 'normal'
            }}
          />
          {hasChildren ? (
            isExpanded ? <ExpandLess /> : <ExpandMore />
          ) : (
            <Button
              variant="contained"
              color="primary"
              size="small"
              onClick={(e) => {
                e.stopPropagation();
                handleSelectTenant(tenant);
              }}
              disabled={isSwitching}
              sx={{ ml: 1 }}
            >
              {isSwitching ? <CircularProgress size={20} /> : 'Select'}
            </Button>
          )}
        </ListItem>
        {hasChildren && (
          <Collapse in={isExpanded} timeout="auto" unmountOnExit>
            <List component="div" disablePadding>
              {tenant.children!.map(child => renderTenantItem(child, level + 1))}
            </List>
          </Collapse>
        )}
      </React.Fragment>
    );
  };

  // Render the flat list of tenants
  const renderFlatList = () => {
    return (
      <List>
        {filteredTenants.map(tenant => (
          <ListItem
            key={tenant.id}
            button
            onClick={() => handleSelectTenant(tenant)}
            sx={{
              bgcolor: selectedTenant?.id === tenant.id ? 'rgba(0, 0, 0, 0.08)' : 'transparent',
              '&:hover': {
                bgcolor: selectedTenant?.id === tenant.id ? 'rgba(0, 0, 0, 0.12)' : 'rgba(0, 0, 0, 0.04)'
              }
            }}
          >
            <ListItemIcon>
              {tenant.schema_type === 'CITY' && <LocationCityIcon />}
              {tenant.schema_type === 'SUBCITY' && <ApartmentIcon />}
              {tenant.schema_type === 'KEBELE' && <HomeIcon />}
            </ListItemIcon>
            <ListItemText
              primary={tenant.name}
              secondary={tenant.schema_name}
              primaryTypographyProps={{
                fontWeight: selectedTenant?.id === tenant.id ? 'bold' : 'normal'
              }}
            />
            <Button
              variant="contained"
              color="primary"
              size="small"
              onClick={(e) => {
                e.stopPropagation();
                handleSelectTenant(tenant);
              }}
              disabled={switchingTenant === tenant.id}
            >
              {switchingTenant === tenant.id ? <CircularProgress size={20} /> : 'Select'}
            </Button>
          </ListItem>
        ))}
      </List>
    );
  };

  return (
    <Box sx={{ width: '100%', maxWidth: 800, mx: 'auto' }}>
      <Typography variant="h5" gutterBottom sx={{ fontWeight: 600 }}>
        Select a Tenant
      </Typography>

      {currentTenantSchema && selectedTenant && (
        <Box sx={{ mb: 2 }}>
          <Chip
            icon={
              selectedTenant.schema_type === 'CITY' ? <LocationCityIcon /> :
              selectedTenant.schema_type === 'SUBCITY' ? <ApartmentIcon /> :
              <HomeIcon />
            }
            label={`Current: ${selectedTenant.name} (${selectedTenant.schema_name})`}
            color="primary"
            variant="outlined"
          />
        </Box>
      )}

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      <TextField
        fullWidth
        variant="outlined"
        placeholder="Search tenants..."
        value={searchQuery}
        onChange={(e) => setSearchQuery(e.target.value)}
        sx={{ mb: 2 }}
        InputProps={{
          startAdornment: (
            <InputAdornment position="start">
              <SearchIcon />
            </InputAdornment>
          ),
        }}
      />

      <Paper
        elevation={1}
        sx={{
          mb: 4,
          boxShadow: '0 4px 12px rgba(0,0,0,0.1)',
          borderRadius: 2,
          overflow: 'hidden'
        }}
      >
        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
            <CircularProgress />
          </Box>
        ) : filteredTenants.length === 0 ? (
          <Box sx={{ p: 4, textAlign: 'center' }}>
            <Typography variant="body1">
              No tenants found matching your search.
            </Typography>
          </Box>
        ) : showHierarchy ? (
          <List>
            {filteredTenants.map(tenant => renderTenantItem(tenant))}
          </List>
        ) : (
          renderFlatList()
        )}
      </Paper>

      <Divider sx={{ mb: 2 }} />

      <Typography variant="body2" color="textSecondary">
        Select a tenant to set the context for API requests. This will set the token and schema name for all subsequent operations.
      </Typography>
    </Box>
  );
};

export default EnhancedTenantSelector;
