import os
import django

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'neocamelot.settings')
django.setup()

# Import models and admin
from django.contrib import admin
from citizens.models import Citizen
from idcards.models import IDCard, IDCardTemplate

# Check if the models are registered in the admin site
registered_models = [model.__name__ for model, _ in admin.site._registry.items()]
print(f"Currently registered models: {registered_models}")

# Check the admin classes for each model
for model, admin_class in admin.site._registry.items():
    if model.__name__ in ['Citizen', 'IDCard', 'IDCardTemplate']:
        print(f"Model: {model.__name__}, Admin class: {admin_class.__class__.__name__}")

print("\nDone!")
