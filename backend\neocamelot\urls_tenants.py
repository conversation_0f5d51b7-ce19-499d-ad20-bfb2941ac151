"""
URL configuration for tenant schemas.
This includes all tenant-specific endpoints.
"""
from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
from rest_framework.routers import DefaultRouter

# Import views from apps
from centers.views import CenterViewSet, CenterTypeViewSet
from accounts.views import UserViewSet, login_view
from citizens.views import CitizenViewSet
from idcards.views import IDCardViewSet, IDCardTemplateViewSet

# Create a router and register our viewsets with it
router = DefaultRouter()
router.register(r'centers', CenterViewSet)
router.register(r'center-types', CenterTypeViewSet)
router.register(r'users', UserViewSet)
router.register(r'citizens', CitizenViewSet)
router.register(r'idcards', IDCardViewSet)
router.register(r'idcard-templates', IDCardTemplateViewSet)

urlpatterns = [
    path('admin/', admin.site.urls),
    path('api/', include(router.urls)),
    path('api-auth/', include('rest_framework.urls')),
    path('api/login/', login_view, name='login'),
    path('', include('centers.urls')),  # Include our simple API endpoints
]

# Serve media files in development
if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
