"""
Tenant-specific API endpoint for ID card statistics.
"""
from rest_framework.decorators import api_view, authentication_classes, permission_classes
from rest_framework.authentication import TokenAuthentication
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from rest_framework import status
from django.db import connection
from django_tenants.utils import tenant_context
from centers.models import Client
from idcards.models import IDCard
from django.contrib.auth import get_user_model
from rest_framework.authtoken.models import Token

User = get_user_model()

@api_view(['GET'])
@authentication_classes([TokenAuthentication])
@permission_classes([AllowAny])  # We'll handle authentication manually
def tenant_idcard_statistics(request, schema_name):
    """
    Get statistics for ID cards in a specific tenant.
    """
    # Manual authentication
    auth_header = request.headers.get('Authorization', '')
    print(f"\n\nAUTH HEADER for idcard_statistics: {auth_header}\n\n")

    if not auth_header or not auth_header.startswith('Token '):
        print(f"\n\nINVALID AUTH HEADER for idcard_statistics: {auth_header}\n\n")
        return Response({"error": "Authentication credentials were not provided."}, status=status.HTTP_401_UNAUTHORIZED)

    token_key = auth_header.split(' ')[1]
    print(f"\n\nToken key: {token_key}\n\n")

    try:
        # Get the tenant by schema name
        tenant = Client.objects.get(schema_name=schema_name)

        # Set the tenant for this request
        connection.set_tenant(tenant)

        # Use tenant_context to ensure we're querying the right database
        with tenant_context(tenant):
            # Authenticate the user
            try:
                # First try to find the token in the tenant's database
                print(f"\n\nLooking for token {token_key} in tenant {tenant.schema_name}\n\n")
                token = Token.objects.get(key=token_key)
                user = token.user
                request.user = user
                print(f"\n\nAuthenticated user: {user.username} (ID: {user.id})\n\n")
            except Token.DoesNotExist:
                # If not found, try the public schema
                print(f"\n\nToken not found in tenant {tenant.schema_name}, trying public schema\n\n")
                connection.set_schema_to_public()
                try:
                    token = Token.objects.get(key=token_key)
                    print(f"\n\nFound token in public schema for user {token.user.username} (ID: {token.user.id})\n\n")
                    # Now switch back to the tenant schema
                    connection.set_tenant(tenant)
                    # Try to find the user in the tenant's database
                    try:
                        user = User.objects.get(email=token.user.email)
                        request.user = user
                    except User.DoesNotExist:
                        # If the user doesn't exist in the tenant's database, use the public user
                        request.user = token.user
                except Token.DoesNotExist:
                    print(f"\n\nToken not found in public schema\n\n")
                    return Response({"error": "Invalid token."}, status=status.HTTP_401_UNAUTHORIZED)

            # Get all ID cards for this tenant
            queryset = IDCard.objects.all()

            # Calculate statistics
            stats = {
                'total': queryset.count(),
                'draft': queryset.filter(status='DRAFT').count(),
                'pending': queryset.filter(status='PENDING').count(),
                'approved': queryset.filter(status='APPROVED').count(),
                'printed': queryset.filter(status='PRINTED').count(),
                'issued': queryset.filter(status='ISSUED').count(),
                'expired': queryset.filter(status='EXPIRED').count(),
                'revoked': queryset.filter(status='REVOKED').count(),
                'kebele_pending': queryset.filter(kebele_approval_status='PENDING').count(),
                'kebele_approved': queryset.filter(kebele_approval_status='APPROVED').count(),
                'kebele_rejected': queryset.filter(kebele_approval_status='REJECTED').count(),
            }

            return Response(stats)

    except Client.DoesNotExist:
        return Response({"error": f"Tenant with schema name '{schema_name}' not found."}, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
