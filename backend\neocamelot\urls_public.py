
"""
URL configuration for the public schema.
This includes login, tenant creation, and other public endpoints.
"""
from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
from rest_framework.routers import DefaultRouter

# Import views from apps
from accounts.views import login_view, register_view
from centers.views import CityViewSet, SubcityViewSet, register_tenant

# Create a router and register our viewsets with it
router = DefaultRouter()
router.register(r'cities', CityViewSet)
router.register(r'subcities', SubcityViewSet)

urlpatterns = [
    path('admin/', admin.site.urls),
    path('api/', include(router.urls)),
    path('api-auth/', include('rest_framework.urls')),
    path('api/login/', login_view, name='login'),
    path('api/register/', register_view, name='register'),
    path('api/register-tenant/', register_tenant, name='register-tenant'),
]

# Serve media files in development
if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
