/**
 * Authentication Migration Utility
 *
 * This utility helps migrate from legacy token authentication to JWT authentication.
 * It provides functions to check if JWT authentication is available and to migrate
 * from legacy token authentication to JWT authentication.
 */

import { getAccessTokenForSchema, storeTokensForSchema, getCurrentSchema } from '../services/tokenService';

/**
 * Check if JWT authentication is available for a schema
 * @param schema The schema name
 * @returns True if JWT authentication is available, false otherwise
 */
export const isJWTAuthAvailable = (schema: string): boolean => {
  if (!schema) {
    return false;
  }

  // Check if we have a JWT token for this schema
  const jwtToken = getAccessTokenForSchema(schema);
  return !!jwtToken;
};

/**
 * Migrate from legacy token authentication to JWT authentication
 * This function is called when a user logs in with a legacy token
 * and we want to migrate them to JWT authentication.
 * @param schema The schema name
 * @param legacyToken The legacy token
 * @returns A promise that resolves when the migration is complete
 */
export const migrateToJWTAuth = async (
  schema: string,
  legacyToken: string
): Promise<boolean> => {
  if (!schema || !legacyToken) {
    console.error('Cannot migrate to JWT auth: schema or token is missing');
    return false;
  }

  try {
    console.log(`Migrating from legacy token to JWT for schema ${schema}`);

    // Call the backend to exchange the legacy token for a JWT token
    const response = await fetch('/api/jwt/exchange-token/', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Token ${legacyToken}`,
        'X-Schema-Name': schema
      },
      body: JSON.stringify({ schema })
    });

    if (!response.ok) {
      console.error(`Failed to exchange token: ${response.status} ${response.statusText}`);
      return false;
    }

    const data = await response.json();

    if (data.access_token && data.refresh_token) {
      console.log('Successfully exchanged legacy token for JWT tokens');

      // Store the JWT tokens
      storeTokensForSchema(schema, data.access_token, data.refresh_token);

      return true;
    } else {
      console.error('Token exchange response did not contain tokens');
      return false;
    }
  } catch (error) {
    console.error('Error migrating to JWT auth:', error);
    return false;
  }
};

/**
 * Check if a user has legacy token authentication
 * @param schema The schema name
 * @returns True if the user has legacy token authentication, false otherwise
 */
export const hasLegacyAuth = (schema: string): boolean => {
  if (!schema) {
    return false;
  }

  // Check if we have a legacy token for this schema
  // Since we've migrated to unified token service, we'll check localStorage directly
  const legacyToken = localStorage.getItem(`token_${schema}`) || localStorage.getItem('token');
  return !!legacyToken;
};

/**
 * Try to migrate all users with legacy token authentication to JWT authentication
 * @returns A promise that resolves when the migration is complete
 */
export const migrateAllUsersToJWT = async (): Promise<void> => {
  try {
    console.log('Attempting to migrate all users to JWT authentication');

    // Get the current schema using the unified token service
    const schema = getCurrentSchema();

    if (!schema) {
      console.log('No schema found, skipping migration');
      return;
    }

    // Check if we have JWT authentication for this schema
    if (isJWTAuthAvailable(schema)) {
      console.log(`JWT authentication already available for schema ${schema}`);
      return;
    }

    // Check if we have legacy token authentication for this schema
    if (hasLegacyAuth(schema)) {
      console.log(`Found legacy token for schema ${schema}, migrating to JWT`);

      // Get the legacy token
      const legacyToken = localStorage.getItem(`token_${schema}`) || localStorage.getItem('token');

      if (legacyToken) {
        // Migrate to JWT authentication
        const success = await migrateToJWTAuth(schema, legacyToken);

        if (success) {
          console.log(`Successfully migrated schema ${schema} to JWT authentication`);
        } else {
          console.error(`Failed to migrate schema ${schema} to JWT authentication`);
        }
      }
    } else {
      console.log(`No legacy token found for schema ${schema}, skipping migration`);
    }
  } catch (error) {
    console.error('Error migrating all users to JWT:', error);
  }
};

export default {
  isJWTAuthAvailable,
  migrateToJWTAuth,
  hasLegacyAuth,
  migrateAllUsersToJWT
};
