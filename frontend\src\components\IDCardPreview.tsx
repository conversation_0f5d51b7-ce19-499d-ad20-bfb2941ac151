import React from 'react';
import { Box, Typography, Paper, Avatar } from '@mui/material';
import { getPatternStatus } from '../utils/patternUtils';

interface IDCardPreviewProps {
  idCard: any;
}

const IDCardPreview: React.FC<IDCardPreviewProps> = ({ idCard }) => {
  // Process pattern status
  const patternStatus = idCard.pattern_status || getPatternStatus(idCard);

  // Fallback image URL
  const fallbackImageUrl = 'https://www.gravatar.com/avatar/00000000000000000000000000000000?d=mp&f=y';

  // Get photo URL
  const photoUrl = idCard.citizen?.photo || fallbackImageUrl;

  // Format date function
  const formatDate = (dateString: string) => {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  return (
    <Paper
      elevation={3}
      sx={{
        width: '100%',
        maxWidth: '500px',
        height: '300px',
        position: 'relative',
        overflow: 'hidden',
        borderRadius: 1,
        background: 'linear-gradient(to bottom, #e3f2fd 0%, #f5f9fc 100%)',
        mb: 2
      }}
    >
      {/* Card Header */}
      <Box sx={{
        background: 'linear-gradient(to right, #0091ea 0%, #0277bd 100%)',
        color: 'white',
        py: 0.8,
        px: 1.5,
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        borderBottom: '1px solid rgba(0, 0, 0, 0.1)',
        height: '45px',
        position: 'relative',
      }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', width: '100%' }}>
          <Box
            component="img"
            src="/ethiopian-flag.svg"
            alt="Ethiopian Flag"
            sx={{ width: 32, height: 22, ml: 0.5 }}
          />
          <Box sx={{ textAlign: 'center', flex: 1, mx: 1 }}>
            <Typography variant="subtitle2" sx={{
              fontWeight: 700,
              fontSize: '0.55rem',
              textAlign: 'center',
              color: '#fff',
              lineHeight: 1.2
            }}>
              በኢትዮጵያ ፌደራላዊ ዲሞክራሲያዊ ሪፐብሊክ በአማራ ብሔራዊ ክልላዊ መንግሥት
            </Typography>
            <Typography variant="subtitle2" sx={{
              fontWeight: 500,
              fontSize: '0.55rem',
              textAlign: 'center',
              color: '#fff',
              opacity: 0.9
            }}>
              በጎንደር ከተማ አስተዳደር በዞብል ክ/ከተማ የገብርኤል ቀበሌ አስተዳደር ጽ/ቤት
            </Typography>
          </Box>
          <Box
            component="img"
            src="/flag-icon.jpg"
            alt="Flag"
            sx={{ width: 28, height: 28, mr: 0.5, borderRadius: '50%', objectFit: 'contain' }}
          />
        </Box>
      </Box>

      {/* Main Content */}
      <Box sx={{
        display: 'flex',
        p: 1.5,
        bgcolor: 'white',
        flexGrow: 1,
        position: 'relative',
        zIndex: 2
      }}>
        {/* Security Pattern Overlays */}
        {patternStatus?.kebele && (
          <Box
            sx={{
              position: 'absolute',
              top: 0,
              left: 0,
              width: '50%',
              height: '100%',
              backgroundImage: `url(/castle-watermark.png)`,
              backgroundSize: 'cover',
              backgroundPosition: 'left center',
              opacity: 0.3,
              pointerEvents: 'none',
              zIndex: 0,
              mixBlendMode: 'multiply'
            }}
          />
        )}

        {patternStatus?.subcity && (
          <Box
            sx={{
              position: 'absolute',
              top: 0,
              left: '50%',
              width: '50%',
              height: '100%',
              backgroundImage: `url(/castle-watermark.png)`,
              backgroundSize: 'cover',
              backgroundPosition: 'right center',
              opacity: 0.3,
              pointerEvents: 'none',
              zIndex: 0,
              mixBlendMode: 'color-burn'
            }}
          />
        )}

        {/* Left Side - Photo */}
        <Box sx={{ mr: 2, display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
          <Box sx={{
            width: 100,
            height: 120,
            border: '1px solid rgba(0,0,0,0.1)',
            overflow: 'hidden',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            bgcolor: '#f8f8f8',
            boxShadow: '0 2px 4px rgba(0,0,0,0.08)',
            position: 'relative',
            borderRadius: 0.5,
            mb: 1
          }}>
            <Avatar
              src={photoUrl}
              alt={`${idCard.citizen?.first_name} ${idCard.citizen?.last_name}`}
              variant="square"
              sx={{
                width: 100,
                height: 120,
                objectFit: 'cover'
              }}
            >
              {idCard.citizen?.first_name?.[0]}
            </Avatar>
          </Box>

          {/* ID Number */}
          <Typography sx={{
            color: '#0063b1',
            fontWeight: 700,
            fontSize: '0.9rem',
            letterSpacing: '0.5px',
            textAlign: 'center'
          }}>
            {idCard.card_number || 'NOT ASSIGNED'}
          </Typography>
        </Box>

        {/* Right Side - Personal Info */}
        <Box sx={{ flexGrow: 1, display: 'flex', flexDirection: 'column', justifyContent: 'space-between' }}>
          {/* Name */}
          <Box sx={{ mb: 1 }}>
            <Typography variant="subtitle2" sx={{ fontSize: '0.7rem', color: 'rgba(0,0,0,0.6)' }}>
              Full Name / ሙሉ ስም
            </Typography>
            <Typography variant="body1" sx={{ fontWeight: 600, fontSize: '0.9rem' }}>
              {`${idCard.citizen?.first_name || ''} ${idCard.citizen?.last_name || ''}`}
            </Typography>
          </Box>

          {/* ID Number */}
          <Box sx={{ mb: 1 }}>
            <Typography variant="subtitle2" sx={{ fontSize: '0.7rem', color: 'rgba(0,0,0,0.6)' }}>
              ID Number / መታወቂያ ቁጥር
            </Typography>
            <Typography variant="body1" sx={{ fontWeight: 600, fontSize: '0.9rem' }}>
              {idCard.citizen?.id_number || 'N/A'}
            </Typography>
          </Box>

          {/* Issue Date */}
          <Box sx={{ mb: 1 }}>
            <Typography variant="subtitle2" sx={{ fontSize: '0.7rem', color: 'rgba(0,0,0,0.6)' }}>
              Issue Date / የተሰጠበት ቀን
            </Typography>
            <Typography variant="body1" sx={{ fontWeight: 600, fontSize: '0.9rem' }}>
              {formatDate(idCard.issue_date)}
            </Typography>
          </Box>

          {/* Expiry Date */}
          <Box sx={{ mb: 1 }}>
            <Typography variant="subtitle2" sx={{ fontSize: '0.7rem', color: 'rgba(0,0,0,0.6)' }}>
              Expiry Date / የሚያበቃበት ቀን
            </Typography>
            <Typography variant="body1" sx={{ fontWeight: 600, fontSize: '0.9rem' }}>
              {formatDate(idCard.expiry_date)}
            </Typography>
          </Box>
        </Box>
      </Box>

      {/* Footer */}
      <Box sx={{
        height: '20px',
        bgcolor: '#f5f5f5',
        borderTop: '1px solid rgba(0, 0, 0, 0.1)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        px: 2,
        py: 0.25,
      }}>
        <Typography variant="caption" sx={{ fontSize: '0.6rem', color: 'rgba(0,0,0,0.6)' }}>
          Ethiopian Digital ID Card • የኢትዮጵያ ዲጂታል መታወቂያ ካርድ
        </Typography>
      </Box>
    </Paper>
  );
};

export default IDCardPreview;
