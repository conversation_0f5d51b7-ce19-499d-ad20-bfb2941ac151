from django.db import models
from django.core.exceptions import ValidationError
from centers.models_region import Country
from common.models import RelationshipType
from .models_base import PersonBase, citizen_directory_path, validate_age

class Child(PersonBase):
    """Model representing a child of a citizen."""
    citizen = models.ForeignKey('citizens.Citizen', on_delete=models.CASCADE, related_name='children')
    date_of_birth = models.DateField()
    nationality = models.ForeignKey(Country, on_delete=models.SET_NULL, null=True, blank=True)
    linked_citizen = models.ForeignKey('citizens.Citizen', on_delete=models.SET_NULL, null=True, blank=True, related_name='linked_as_child')

    def __str__(self):
        return f"{self.first_name} {self.last_name} (Child of {self.citizen.id_number})"


class Parent(PersonBase):
    """Model representing a parent of a citizen."""
    RELATIONSHIP_CHOICES = (
        ('Mother', 'Mother'),
        ('Father', 'Father'),
        ('Guardian', 'Guardian'),
    )

    citizen = models.ForeignKey('citizens.Citizen', on_delete=models.CASCADE, related_name='parents')
    nationality = models.ForeignKey(Country, on_delete=models.SET_NULL, null=True, blank=True)
    linked_citizen = models.ForeignKey('citizens.Citizen', on_delete=models.SET_NULL, null=True, blank=True, related_name='linked_as_parent')
    relationship_type = models.CharField(max_length=20, choices=RELATIONSHIP_CHOICES, default='Parent')

    def __str__(self):
        return f"{self.relationship_type}: {self.first_name} {self.middle_name or ''} {self.last_name}"


class EmergencyContact(PersonBase):
    """Model representing an emergency contact for a citizen."""
    citizen = models.ForeignKey('citizens.Citizen', on_delete=models.CASCADE, related_name="emergency_contacts")
    relationship = models.ForeignKey(RelationshipType, on_delete=models.SET_NULL, null=True, blank=True)
    nationality = models.ForeignKey(Country, on_delete=models.SET_NULL, null=True, blank=True, related_name="emergency_contact_nationalities")
    primary_contact = models.BooleanField(default=False, help_text="Indicates whether this is the primary emergency contact.")
    linked_citizen = models.ForeignKey('citizens.Citizen', on_delete=models.SET_NULL, null=True, blank=True, related_name='linked_as_emergency_contact')

    def clean(self):
        if not self.phone and not self.email:
            raise ValidationError("At least one contact method (phone or email) must be provided.")

    def __str__(self):
        return f"{self.first_name} {self.last_name} - {self.citizen.first_name} {self.citizen.last_name}"

    class Meta:
        verbose_name = "Emergency Contact"
        verbose_name_plural = "Emergency Contacts"
        unique_together = ('citizen', 'phone')  # Ensure no duplicate phone number for the same citizen


class Spouse(PersonBase):
    """Model representing a spouse of a citizen."""
    citizen = models.ForeignKey('citizens.Citizen', on_delete=models.CASCADE, related_name="spouses")
    nationality = models.ForeignKey(Country, on_delete=models.SET_NULL, null=True, blank=True, related_name="spouse_nationalities")
    primary_contact = models.BooleanField(default=False, help_text="Indicates whether this is the primary emergency contact.")
    linked_citizen = models.ForeignKey('citizens.Citizen', on_delete=models.SET_NULL, null=True, blank=True, related_name='linked_as_spouse')

    def clean(self):
        if not self.phone and not self.email:
            raise ValidationError("At least one contact method (phone or email) must be provided.")

    def __str__(self):
        return f"{self.first_name} {self.last_name} - {self.citizen.first_name} {self.citizen.last_name}"

    class Meta:
        verbose_name = "Spouse"
        verbose_name_plural = "Spouses"
        unique_together = ('citizen', 'phone')  # Ensure no duplicate phone number for the same citizen
