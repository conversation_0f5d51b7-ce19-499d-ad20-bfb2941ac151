import os
import django
import uuid

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'neocamelot.settings')
django.setup()

from centers.models import Center

def update_center_codes():
    """Update all centers with empty codes to have unique codes."""
    centers = Center.objects.filter(code='')
    
    for center in centers:
        # Create a short unique code based on the first 3 letters of the name + random string
        name_prefix = ''.join(c for c in center.name[:3] if c.isalnum()).upper()
        if not name_prefix:
            name_prefix = 'CTR'
        random_suffix = str(uuid.uuid4())[:4].upper()
        center.code = f"{name_prefix}-{random_suffix}"
        center.save(update_fields=['code'])
        print(f"Updated center {center.name} with code {center.code}")

if __name__ == '__main__':
    update_center_codes()
    print("Done updating center codes.")
