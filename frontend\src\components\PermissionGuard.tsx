import React from 'react';
import { Feature } from '../utils/rolePermissions';
import usePermissions from '../hooks/usePermissions';

interface PermissionGuardProps {
  feature: Feature;
  children: React.ReactNode;
  fallback?: React.ReactNode;
}

/**
 * A component that conditionally renders its children based on user permissions
 * If the user has permission to use the specified feature, the children are rendered
 * Otherwise, the fallback is rendered (or nothing if no fallback is provided)
 */
const PermissionGuard: React.FC<PermissionGuardProps> = ({ 
  feature, 
  children, 
  fallback = null 
}) => {
  const { canUseFeature } = usePermissions();
  
  if (canUseFeature(feature)) {
    return <>{children}</>;
  }
  
  return <>{fallback}</>;
};

export default PermissionGuard;
