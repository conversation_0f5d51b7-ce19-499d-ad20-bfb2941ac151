<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Authentication Diagnostic</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
      line-height: 1.6;
    }
    h1, h2 {
      color: #3f51b5;
    }
    .card {
      border: 1px solid #ddd;
      border-radius: 8px;
      padding: 20px;
      margin-bottom: 20px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    button {
      background-color: #3f51b5;
      color: white;
      border: none;
      padding: 10px 20px;
      border-radius: 4px;
      cursor: pointer;
      font-size: 16px;
      margin-right: 10px;
      margin-bottom: 10px;
    }
    button:hover {
      background-color: #303f9f;
    }
    pre {
      background-color: #f5f5f5;
      padding: 10px;
      border-radius: 4px;
      overflow-x: auto;
      white-space: pre-wrap;
      word-break: break-all;
    }
    .success { color: green; }
    .error { color: red; }
    .warning { color: orange; }
    input[type="text"] {
      width: 100%;
      padding: 8px;
      margin: 5px 0 15px;
      box-sizing: border-box;
      border: 1px solid #ddd;
      border-radius: 4px;
    }
    .hidden {
      display: none;
    }
    table {
      width: 100%;
      border-collapse: collapse;
      margin-bottom: 15px;
    }
    th, td {
      border: 1px solid #ddd;
      padding: 8px;
      text-align: left;
    }
    th {
      background-color: #f2f2f2;
    }
  </style>
</head>
<body>
  <h1>Authentication Diagnostic Tool</h1>
  
  <div class="card">
    <h2>Current Token Information</h2>
    <div id="token-info">Loading...</div>
  </div>
  
  <div class="card">
    <h2>Test API Request</h2>
    <div>
      <label for="schema-name">Schema Name:</label>
      <input type="text" id="schema-name" placeholder="e.g., gondar_city">
      
      <label for="endpoint">API Endpoint:</label>
      <input type="text" id="endpoint" value="citizens/?limit=1">
      
      <button onclick="testApiRequest()">Test API Request</button>
    </div>
    <div id="api-result" class="hidden">
      <h3>API Request Result</h3>
      <pre id="api-response"></pre>
    </div>
  </div>
  
  <div class="card">
    <h2>Generate New Token</h2>
    <div>
      <label for="email">Email:</label>
      <input type="text" id="email" placeholder="Your email">
      
      <label for="password">Password:</label>
      <input type="password" id="password" placeholder="Your password">
      
      <label for="login-schema">Schema Name (for login):</label>
      <input type="text" id="login-schema" placeholder="e.g., gondar_city">
      
      <button onclick="generateNewToken()">Generate New Token</button>
    </div>
    <div id="login-result" class="hidden">
      <h3>Login Result</h3>
      <pre id="login-response"></pre>
    </div>
  </div>
  
  <div class="card">
    <h2>Manual Token Entry</h2>
    <div>
      <label for="manual-token">Token:</label>
      <input type="text" id="manual-token" placeholder="Enter token">
      
      <label for="manual-schema">Schema Name:</label>
      <input type="text" id="manual-schema" placeholder="e.g., gondar_city">
      
      <button onclick="setManualToken()">Set Token</button>
    </div>
    <div id="manual-result" class="hidden">
      <h3>Result</h3>
      <pre id="manual-response"></pre>
    </div>
  </div>
  
  <div class="card">
    <h2>Advanced Tools</h2>
    <button onclick="clearAllStorage()">Clear All Storage</button>
    <button onclick="exportStorage()">Export Storage</button>
    <button onclick="window.location.href = '/'">Go to Home</button>
    <button onclick="window.location.href = '/login'">Go to Login</button>
  </div>
  
  <script>
    // Initialize the page
    document.addEventListener('DOMContentLoaded', function() {
      displayTokenInfo();
      
      // Pre-fill schema name if available
      const schema = localStorage.getItem('schema_name');
      if (schema) {
        document.getElementById('schema-name').value = schema;
        document.getElementById('login-schema').value = schema;
        document.getElementById('manual-schema').value = schema;
      }
    });
    
    // Display token information
    function displayTokenInfo() {
      const tokenInfoDiv = document.getElementById('token-info');
      
      try {
        // Get token from localStorage
        const token = localStorage.getItem('token');
        const schema = localStorage.getItem('schema_name');
        
        let html = '<h3>Token Details</h3>';
        
        if (token) {
          html += '<table>';
          html += '<tr><th>Property</th><th>Value</th></tr>';
          html += `<tr><td>Token</td><td>${token}</td></tr>`;
          html += `<tr><td>Token Length</td><td>${token.length} characters</td></tr>`;
          
          // Check for spaces
          const hasSpaces = /\s/.test(token);
          html += `<tr><td>Contains Spaces</td><td class="${hasSpaces ? 'error' : 'success'}">${hasSpaces ? 'Yes (Problem!)' : 'No'}</td></tr>`;
          
          // Check for non-alphanumeric characters
          const hasNonAlphaNumeric = /[^a-zA-Z0-9]/.test(token);
          html += `<tr><td>Contains Non-Alphanumeric</td><td class="${hasNonAlphaNumeric ? 'warning' : 'success'}">${hasNonAlphaNumeric ? 'Yes' : 'No'}</td></tr>`;
          
          // Character breakdown
          html += '<tr><td>Character Breakdown</td><td>';
          html += '<div style="max-height: 200px; overflow-y: auto;">';
          html += '<table style="width: 100%;">';
          html += '<tr><th>Position</th><th>Character</th><th>ASCII Code</th></tr>';
          
          for (let i = 0; i < token.length; i++) {
            const char = token[i];
            const code = token.charCodeAt(i);
            const isSpace = /\s/.test(char);
            html += `<tr class="${isSpace ? 'error' : ''}">`;
            html += `<td>${i}</td>`;
            html += `<td>${isSpace ? '(space)' : char}</td>`;
            html += `<td>${code}</td>`;
            html += '</tr>';
          }
          
          html += '</table>';
          html += '</div>';
          html += '</td></tr>';
          
          html += `<tr><td>Schema Name</td><td>${schema || 'Not set'}</td></tr>`;
          html += '</table>';
          
          // Add token store information
          const tokenStoreStr = localStorage.getItem('tokenStore');
          if (tokenStoreStr) {
            try {
              const tokenStore = JSON.parse(tokenStoreStr);
              html += '<h3>Token Store</h3>';
              html += '<table>';
              html += '<tr><th>Schema</th><th>Token</th><th>Length</th><th>Has Spaces</th></tr>';
              
              for (const [schemaName, schemaToken] of Object.entries(tokenStore)) {
                const hasSpaces = /\s/.test(schemaToken);
                html += '<tr>';
                html += `<td>${schemaName}</td>`;
                html += `<td>${schemaToken}</td>`;
                html += `<td>${schemaToken.length}</td>`;
                html += `<td class="${hasSpaces ? 'error' : 'success'}">${hasSpaces ? 'Yes' : 'No'}</td>`;
                html += '</tr>';
              }
              
              html += '</table>';
            } catch (e) {
              html += `<p class="error">Error parsing token store: ${e.message}</p>`;
            }
          }
          
          // Add tenant information
          const tenantStr = localStorage.getItem('tenant');
          if (tenantStr) {
            try {
              const tenant = JSON.parse(tenantStr);
              html += '<h3>Tenant Information</h3>';
              html += '<table>';
              html += '<tr><th>Property</th><th>Value</th></tr>';
              
              for (const [key, value] of Object.entries(tenant)) {
                html += '<tr>';
                html += `<td>${key}</td>`;
                html += `<td>${typeof value === 'object' ? JSON.stringify(value) : value}</td>`;
                html += '</tr>';
              }
              
              html += '</table>';
            } catch (e) {
              html += `<p class="error">Error parsing tenant: ${e.message}</p>`;
            }
          }
          
          // Add cookie information
          html += '<h3>Cookies</h3>';
          html += '<p>' + (document.cookie || 'No cookies') + '</p>';
          
        } else {
          html += '<p class="error">No token found in localStorage</p>';
        }
        
        tokenInfoDiv.innerHTML = html;
      } catch (error) {
        tokenInfoDiv.innerHTML = `<p class="error">Error displaying token info: ${error.message}</p>`;
      }
    }
    
    // Test API request
    async function testApiRequest() {
      const schemaName = document.getElementById('schema-name').value.trim();
      const endpoint = document.getElementById('endpoint').value.trim();
      const apiResultDiv = document.getElementById('api-result');
      const apiResponsePre = document.getElementById('api-response');
      
      if (!schemaName) {
        alert('Please enter a schema name');
        return;
      }
      
      if (!endpoint) {
        alert('Please enter an API endpoint');
        return;
      }
      
      const token = localStorage.getItem('token');
      if (!token) {
        alert('No token found in localStorage');
        return;
      }
      
      apiResultDiv.classList.remove('hidden');
      apiResponsePre.textContent = 'Making request...';
      
      try {
        // Make the API request
        const url = `/api/tenant/${encodeURIComponent(schemaName)}/${endpoint}`;
        
        // Create headers with the token
        const headers = new Headers({
          'Authorization': `Token ${token}`,
          'Content-Type': 'application/json',
          'X-Schema-Name': schemaName
        });
        
        // Set schema cookie
        document.cookie = `schema_name=${encodeURIComponent(schemaName)}; path=/; SameSite=Lax`;
        
        // Make the request
        const response = await fetch(url, {
          method: 'GET',
          headers,
          credentials: 'include'
        });
        
        // Get response details
        const status = response.status;
        const statusText = response.statusText;
        const headers_received = Array.from(response.headers.entries())
          .map(([key, value]) => `${key}: ${value}`)
          .join('\n');
        
        let responseBody;
        try {
          responseBody = await response.json();
        } catch (e) {
          responseBody = await response.text();
        }
        
        // Display response
        apiResponsePre.textContent = `Status: ${status} ${statusText}\n\nHeaders:\n${headers_received}\n\nBody:\n${typeof responseBody === 'object' ? JSON.stringify(responseBody, null, 2) : responseBody}`;
        
      } catch (error) {
        apiResponsePre.textContent = `Error: ${error.message}`;
      }
    }
    
    // Generate new token
    async function generateNewToken() {
      const email = document.getElementById('email').value.trim();
      const password = document.getElementById('password').value;
      const schema = document.getElementById('login-schema').value.trim();
      const loginResultDiv = document.getElementById('login-result');
      const loginResponsePre = document.getElementById('login-response');
      
      if (!email || !password) {
        alert('Please enter email and password');
        return;
      }
      
      loginResultDiv.classList.remove('hidden');
      loginResponsePre.textContent = 'Logging in...';
      
      try {
        // Create headers
        const headers = new Headers({
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        });
        
        // Set schema cookie if provided
        if (schema) {
          document.cookie = `schema_name=${encodeURIComponent(schema)}; path=/; SameSite=Lax`;
          headers.set('X-Schema-Name', schema);
        }
        
        // Make the login request
        const response = await fetch('/api/login/', {
          method: 'POST',
          headers,
          body: JSON.stringify({
            email,
            password,
            schema_name: schema || undefined
          }),
          credentials: 'include'
        });
        
        // Get response details
        const status = response.status;
        const statusText = response.statusText;
        
        let responseBody;
        try {
          responseBody = await response.json();
        } catch (e) {
          responseBody = await response.text();
        }
        
        // Display response
        loginResponsePre.textContent = `Status: ${status} ${statusText}\n\nBody:\n${typeof responseBody === 'object' ? JSON.stringify(responseBody, null, 2) : responseBody}`;
        
        // If login successful, store the token
        if (response.ok && responseBody && responseBody.token) {
          // Clean the token
          const cleanToken = responseBody.token.replace(/\s+/g, '');
          
          // Store in localStorage
          localStorage.setItem('token', cleanToken);
          
          // Store user and tenant if available
          if (responseBody.user) {
            localStorage.setItem('user', JSON.stringify(responseBody.user));
          }
          
          if (responseBody.tenant) {
            localStorage.setItem('tenant', JSON.stringify(responseBody.tenant));
            
            // Set schema_name if available
            if (responseBody.tenant.schema_name) {
              localStorage.setItem('schema_name', responseBody.tenant.schema_name);
              document.cookie = `schema_name=${encodeURIComponent(responseBody.tenant.schema_name)}; path=/; SameSite=Lax`;
            }
          }
          
          // Update token store
          try {
            const tokenStoreStr = localStorage.getItem('tokenStore');
            let tokenStore = tokenStoreStr ? JSON.parse(tokenStoreStr) : {};
            
            if (schema) {
              tokenStore[schema] = cleanToken;
            }
            
            if (responseBody.tenant && responseBody.tenant.schema_name) {
              tokenStore[responseBody.tenant.schema_name] = cleanToken;
            }
            
            localStorage.setItem('tokenStore', JSON.stringify(tokenStore));
          } catch (e) {
            console.error('Error updating token store:', e);
          }
          
          // Refresh token info
          displayTokenInfo();
          
          loginResponsePre.textContent += '\n\nToken stored successfully!';
        }
        
      } catch (error) {
        loginResponsePre.textContent = `Error: ${error.message}`;
      }
    }
    
    // Set manual token
    function setManualToken() {
      const token = document.getElementById('manual-token').value.trim();
      const schema = document.getElementById('manual-schema').value.trim();
      const manualResultDiv = document.getElementById('manual-result');
      const manualResponsePre = document.getElementById('manual-response');
      
      if (!token) {
        alert('Please enter a token');
        return;
      }
      
      if (!schema) {
        alert('Please enter a schema name');
        return;
      }
      
      manualResultDiv.classList.remove('hidden');
      
      try {
        // Clean the token
        const cleanToken = token.replace(/\s+/g, '');
        
        // Store in localStorage
        localStorage.setItem('token', cleanToken);
        localStorage.setItem('schema_name', schema);
        
        // Set schema cookie
        document.cookie = `schema_name=${encodeURIComponent(schema)}; path=/; SameSite=Lax`;
        
        // Update token store
        try {
          const tokenStoreStr = localStorage.getItem('tokenStore');
          let tokenStore = tokenStoreStr ? JSON.parse(tokenStoreStr) : {};
          tokenStore[schema] = cleanToken;
          localStorage.setItem('tokenStore', JSON.stringify(tokenStore));
        } catch (e) {
          console.error('Error updating token store:', e);
        }
        
        // Refresh token info
        displayTokenInfo();
        
        manualResponsePre.textContent = `Token set successfully!\n\nOriginal token: ${token}\nCleaned token: ${cleanToken}\nSchema: ${schema}`;
      } catch (error) {
        manualResponsePre.textContent = `Error: ${error.message}`;
      }
    }
    
    // Clear all storage
    function clearAllStorage() {
      if (confirm('Are you sure you want to clear all storage? This will log you out.')) {
        localStorage.clear();
        sessionStorage.clear();
        
        // Clear cookies
        document.cookie.split(';').forEach(function(c) {
          document.cookie = c.trim().split('=')[0] + '=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
        });
        
        alert('All storage cleared. You will be redirected to the login page.');
        window.location.href = '/login';
      }
    }
    
    // Export storage
    function exportStorage() {
      try {
        const storage = {
          localStorage: {},
          sessionStorage: {},
          cookies: document.cookie
        };
        
        // Export localStorage
        for (let i = 0; i < localStorage.length; i++) {
          const key = localStorage.key(i);
          storage.localStorage[key] = localStorage.getItem(key);
        }
        
        // Export sessionStorage
        for (let i = 0; i < sessionStorage.length; i++) {
          const key = sessionStorage.key(i);
          storage.sessionStorage[key] = sessionStorage.getItem(key);
        }
        
        // Create a download link
        const dataStr = "data:text/json;charset=utf-8," + encodeURIComponent(JSON.stringify(storage, null, 2));
        const downloadAnchorNode = document.createElement('a');
        downloadAnchorNode.setAttribute("href", dataStr);
        downloadAnchorNode.setAttribute("download", "storage_export.json");
        document.body.appendChild(downloadAnchorNode);
        downloadAnchorNode.click();
        downloadAnchorNode.remove();
      } catch (error) {
        alert(`Error exporting storage: ${error.message}`);
      }
    }
  </script>
</body>
</html>
