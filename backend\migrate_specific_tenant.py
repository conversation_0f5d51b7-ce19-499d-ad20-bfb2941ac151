import os
import django
import sys

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'neocamelot.settings')
django.setup()

# Import models
from centers.models import Client
from django.core.management import call_command
from django_tenants.utils import tenant_context

def migrate_specific_tenant(schema_name):
    """Run migrations for a specific tenant."""
    try:
        # Get the tenant
        tenant = Client.objects.get(schema_name=schema_name)
        print(f"\n===== MIGRATING TENANT: {tenant.name} (Schema: {tenant.schema_name}) =====\n")
        
        # Run migrations for the tenant
        with tenant_context(tenant):
            call_command('migrate')
            
        print(f"\n===== MIGRATION COMPLETE FOR {tenant.name} =====\n")
        
    except Client.DoesNotExist:
        print(f"Tenant with schema {schema_name} does not exist")
    except Exception as e:
        print(f"Error migrating tenant {schema_name}: {str(e)}")

if __name__ == '__main__':
    # Get schema name from command line
    if len(sys.argv) > 1:
        schema_name = sys.argv[1]
        migrate_specific_tenant(schema_name)
    else:
        print("Please provide a schema name as an argument")
