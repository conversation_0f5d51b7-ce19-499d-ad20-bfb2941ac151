"""
Enhanced authentication classes for multi-tenant support (deprecated).

This module previously contained enhanced token authentication classes that have been
replaced by JWT authentication. These classes are kept as stubs for backward
compatibility but are no longer used.
"""

from rest_framework.authentication import BaseAuthentication
from django.db import connection
from django.contrib.auth import get_user_model
from centers.models import Client
from django_tenants.utils import get_public_schema_name
import logging

logger = logging.getLogger(__name__)

User = get_user_model()

class EnhancedTokenAuthentication:
    """
    DEPRECATED: This class has been replaced by JWT authentication.

    This stub is kept for backward compatibility but is no longer used.
    """

    def authenticate(self, request):
        """
        Always returns None to indicate that this authentication method is not available.
        """
        logger.warning("EnhancedTokenAuthentication is deprecated and no longer used. Use JWT authentication instead.")
        return None


class TenantAwareAuthentication(BaseAuthentication):
    """
    Authentication class that sets the tenant context based on the X-Schema-Name header.

    This class doesn't actually authenticate the user, but it sets the tenant context
    for the request. It should be used in combination with other authentication classes.
    """

    def authenticate(self, request):
        """
        Set the tenant context for the request.
        """
        # Get the schema name from the request
        schema_name = request.headers.get('X-Schema-Name') or request.COOKIES.get('schema_name')

        if not schema_name or schema_name == get_public_schema_name():
            # No schema name or public schema, nothing to do
            return None

        try:
            # Get the tenant by schema name
            tenant = Client.objects.get(schema_name=schema_name)

            # Set the tenant for this request
            connection.set_tenant(tenant)
            request.tenant = tenant

            logger.debug(f"Set tenant context to {tenant.name} (schema: {tenant.schema_name})")

            # Return None to indicate that this class doesn't authenticate the user
            return None
        except Client.DoesNotExist:
            logger.warning(f"Tenant with schema {schema_name} does not exist")
            return None
        except Exception as e:
            logger.error(f"Error setting tenant context: {str(e)}")
            return None
