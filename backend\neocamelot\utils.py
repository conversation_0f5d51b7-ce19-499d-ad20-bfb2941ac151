"""
Utility functions for the NeoCamelot project.
"""

def get_view_description(view_cls, html=False):
    """
    Return the description for a given view class.
    
    This is a custom implementation to fix issues with drf-yasg.
    """
    description = view_cls.__doc__ or ""
    description = description.strip()
    
    if html:
        import markdown
        try:
            return markdown.markdown(description)
        except ImportError:
            return description.replace("\n", "<br />")
    return description
