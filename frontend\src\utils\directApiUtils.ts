/**
 * Direct API utilities for specific endpoints
 * This is a last resort for endpoints that are not working with the standard approach
 */

/**
 * Directly fetches citizens from the API with hardcoded schema and token
 * @returns Promise<any> The citizens data
 */
export const fetchCitizensDirectly = async (): Promise<any> => {
  try {
    console.log('Fetching citizens directly with hardcoded schema and token');

    // Get the token from localStorage
    const token = localStorage.getItem('token');
    if (!token) {
      console.error('No token found in localStorage');
      return null;
    }

    // Try multiple schemas
    const schemas = ['kebele16', 'kebele 16', 'kebele_16'];

    for (const schema of schemas) {
      try {
        console.log(`Trying schema: ${schema}`);

        // Format the schema for URL
        const urlSchema = schema.replace(/\s+/g, '_');

        // Make the request with explicit schema and token
        const response = await fetch(`/api/tenant/${urlSchema}/citizens/?limit=1000`, {
          method: 'GET',
          headers: {
            'Authorization': `Token ${token}`,
            'X-Schema-Name': schema,
            'Accept': 'application/json',
            'Content-Type': 'application/json'
          },
          credentials: 'include'
        });

        if (response.ok) {
          const data = await response.json();
          console.log(`Successfully fetched citizens with schema: ${schema}`);
          return data;
        }

        console.log(`Failed to fetch citizens with schema: ${schema}`);
      } catch (error) {
        console.error(`Error fetching citizens with schema ${schema}:`, error);
      }
    }

    // No hardcoded tokens - only use the token from localStorage

    console.error('All attempts to fetch citizens directly failed');
    return null;
  } catch (error) {
    console.error('Error in fetchCitizensDirectly:', error);
    return null;
  }
};

export default {
  fetchCitizensDirectly
};
