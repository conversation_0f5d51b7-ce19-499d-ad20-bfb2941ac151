from rest_framework.decorators import api_view, permission_classes, authentication_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework import status
from django_tenants.utils import tenant_context
from django.contrib.auth import get_user_model
from django.shortcuts import get_object_or_404
from .models import Client, Kebele
from accounts.serializers import UserSerializer, UserListSerializer
from accounts.authentication import MultiTenantTokenAuthentication
import logging

logger = logging.getLogger(__name__)

@api_view(['GET'])
@authentication_classes([MultiTenantTokenAuthentication])
@permission_classes([IsAuthenticated])
def get_child_subcities(request):
    """
    Get all child subcity tenants for the current city tenant.
    """
    try:
        # For debugging, log the user information
        user = request.user
        logger.info(f"User: {user.email if hasattr(user, 'email') else 'No email'}, ID: {user.id if hasattr(user, 'id') else 'No ID'}")

        # Get the tenant from the request context
        if hasattr(request, 'tenant'):
            # If the request has a tenant attribute, use it
            current_tenant = request.tenant
            logger.info(f"Using tenant from request context: {current_tenant.name}, Schema: {current_tenant.schema_name}")

            # Check if this is a city tenant
            if current_tenant.schema_type != 'CITY':
                # If this is not a city tenant, try to find its parent city
                if current_tenant.schema_type == 'SUBCITY' and current_tenant.parent and current_tenant.parent.schema_type == 'CITY':
                    current_tenant = current_tenant.parent
                    logger.info(f"Using parent city tenant: {current_tenant.name}, Schema: {current_tenant.schema_name}")
                else:
                    # If we can't find a parent city, use any city tenant
                    city_tenants = Client.objects.filter(schema_type='CITY')
                    if city_tenants.exists():
                        current_tenant = city_tenants.first()
                        logger.info(f"Using fallback city tenant: {current_tenant.name}, Schema: {current_tenant.schema_name}")
                    else:
                        return Response(
                            {"error": "No city tenants found"},
                            status=status.HTTP_400_BAD_REQUEST
                        )
        else:
            # If the request doesn't have a tenant attribute, use any city tenant
            city_tenants = Client.objects.filter(schema_type='CITY')
            if not city_tenants.exists():
                return Response(
                    {"error": "No city tenants found"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            current_tenant = city_tenants.first()
            logger.info(f"Using fallback city tenant: {current_tenant.name}, Schema: {current_tenant.schema_name}")

        # Get all subcity tenants that have this city as parent
        child_tenants = Client.objects.filter(parent=current_tenant, schema_type='SUBCITY')

        # Format the response
        tenant_list = []
        for tenant in child_tenants:
            tenant_data = {
                'id': tenant.id,
                'name': tenant.name,
                'schema_name': tenant.schema_name,
                'schema_type': tenant.schema_type,
                'domains': [domain.domain for domain in tenant.domains.all()]
            }
            tenant_list.append(tenant_data)

        return Response(tenant_list)
    except Exception as e:
        logger.error(f"Error getting child subcities: {str(e)}")
        return Response(
            {"error": f"An error occurred: {str(e)}"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['GET'])
@authentication_classes([MultiTenantTokenAuthentication])
@permission_classes([IsAuthenticated])
def get_subcity_users(request, schema_name):
    """
    Get all users for a specific child subcity tenant.
    """
    try:
        # For debugging, log the user information
        user = request.user
        logger.info(f"User: {user.email if hasattr(user, 'email') else 'No email'}, ID: {user.id if hasattr(user, 'id') else 'No ID'}")

        # Get the tenant from the request context
        if hasattr(request, 'tenant'):
            # If the request has a tenant attribute, use it
            current_tenant = request.tenant
            logger.info(f"Using tenant from request context: {current_tenant.name}, Schema: {current_tenant.schema_name}")

            # Check if this is a city tenant
            if current_tenant.schema_type != 'CITY':
                # If this is not a city tenant, try to find its parent city
                if current_tenant.schema_type == 'SUBCITY' and current_tenant.parent and current_tenant.parent.schema_type == 'CITY':
                    current_tenant = current_tenant.parent
                    logger.info(f"Using parent city tenant: {current_tenant.name}, Schema: {current_tenant.schema_name}")
                else:
                    # If we can't find a parent city, use any city tenant
                    city_tenants = Client.objects.filter(schema_type='CITY')
                    if city_tenants.exists():
                        current_tenant = city_tenants.first()
                        logger.info(f"Using fallback city tenant: {current_tenant.name}, Schema: {current_tenant.schema_name}")
                    else:
                        return Response(
                            {"error": "No city tenants found"},
                            status=status.HTTP_400_BAD_REQUEST
                        )
        else:
            # If the request doesn't have a tenant attribute, use any city tenant
            city_tenants = Client.objects.filter(schema_type='CITY')
            if not city_tenants.exists():
                return Response(
                    {"error": "No city tenants found"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            current_tenant = city_tenants.first()
            logger.info(f"Using fallback city tenant: {current_tenant.name}, Schema: {current_tenant.schema_name}")

        # Get the child tenant
        try:
            child_tenant = Client.objects.get(schema_name=schema_name, parent=current_tenant)
        except Client.DoesNotExist:
            return Response(
                {"error": f"Tenant with schema {schema_name} does not exist or is not a child of this city"},
                status=status.HTTP_404_NOT_FOUND
            )

        # Switch to the child tenant context
        with tenant_context(child_tenant):
            User = get_user_model()
            users = User.objects.all()
            serializer = UserListSerializer(users, many=True)
            return Response(serializer.data)
    except Exception as e:
        logger.error(f"Error getting subcity users: {str(e)}")
        return Response(
            {"error": f"An error occurred: {str(e)}"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['POST'])
@authentication_classes([MultiTenantTokenAuthentication])
@permission_classes([IsAuthenticated])
def create_subcity_user(request, schema_name):
    """
    Create a new user in a specific child subcity tenant.
    """
    try:
        # For debugging, log the user information
        user = request.user
        logger.info(f"User: {user.email if hasattr(user, 'email') else 'No email'}, ID: {user.id if hasattr(user, 'id') else 'No ID'}")

        # Get the tenant from the request context
        if hasattr(request, 'tenant'):
            # If the request has a tenant attribute, use it
            current_tenant = request.tenant
            logger.info(f"Using tenant from request context: {current_tenant.name}, Schema: {current_tenant.schema_name}")

            # Check if this is a city tenant
            if current_tenant.schema_type != 'CITY':
                # If this is not a city tenant, try to find its parent city
                if current_tenant.schema_type == 'SUBCITY' and current_tenant.parent and current_tenant.parent.schema_type == 'CITY':
                    current_tenant = current_tenant.parent
                    logger.info(f"Using parent city tenant: {current_tenant.name}, Schema: {current_tenant.schema_name}")
                else:
                    # If we can't find a parent city, use any city tenant
                    city_tenants = Client.objects.filter(schema_type='CITY')
                    if city_tenants.exists():
                        current_tenant = city_tenants.first()
                        logger.info(f"Using fallback city tenant: {current_tenant.name}, Schema: {current_tenant.schema_name}")
                    else:
                        return Response(
                            {"error": "No city tenants found"},
                            status=status.HTTP_400_BAD_REQUEST
                        )
        else:
            # If the request doesn't have a tenant attribute, use any city tenant
            city_tenants = Client.objects.filter(schema_type='CITY')
            if not city_tenants.exists():
                return Response(
                    {"error": "No city tenants found"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            current_tenant = city_tenants.first()
            logger.info(f"Using fallback city tenant: {current_tenant.name}, Schema: {current_tenant.schema_name}")

        # Get the child tenant (subcity)
        try:
            child_tenant = Client.objects.get(schema_name=schema_name)

            # Verify this is a subcity tenant
            if child_tenant.schema_type != 'SUBCITY':
                return Response(
                    {"error": f"Tenant {schema_name} is not a subcity tenant"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Verify this subcity is a child of the current city
            if child_tenant.parent != current_tenant and child_tenant.parent is not None:
                return Response(
                    {"error": f"Subcity tenant {schema_name} is not a child of this city"},
                    status=status.HTTP_403_FORBIDDEN
                )

        except Client.DoesNotExist:
            return Response(
                {"error": f"Tenant with schema {schema_name} does not exist"},
                status=status.HTTP_404_NOT_FOUND
            )

        # Validate the role
        role = request.data.get('role')
        if role not in ['SUBCITY_ADMIN', 'SUBCITY_STAFF', 'SUBCITY_MANAGER']:
            return Response(
                {"error": f"Invalid role: {role}. Must be one of: SUBCITY_ADMIN, SUBCITY_STAFF, SUBCITY_MANAGER"},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Switch to the child tenant context (the specific subcity)
        with tenant_context(child_tenant):
            User = get_user_model()

            # Check if user with this email already exists in this subcity
            email = request.data.get('email')
            if User.objects.filter(email=email).exists():
                return Response(
                    {"error": f"User with email {email} already exists in subcity {child_tenant.name}"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Get the center for this subcity
            try:
                centers = Kebele.objects.all()
                if not centers.exists():
                    return Response(
                        {"error": f"No centers found in subcity {child_tenant.name}"},
                        status=status.HTTP_400_BAD_REQUEST
                    )
                center = centers.first()
            except Exception as e:
                return Response(
                    {"error": f"Error getting center: {str(e)}"},
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR
                )

            # Create the user in the subcity's schema
            serializer = UserSerializer(data=request.data)
            if serializer.is_valid():
                # Add center to validated data
                serializer.validated_data['center'] = center

                # Get password from request data
                password = request.data.get('password')
                if not password:
                    return Response(
                        {"error": "Password is required"},
                        status=status.HTTP_400_BAD_REQUEST
                    )

                # Create the user in the subcity's schema
                user = User.objects.create_user(
                    email=serializer.validated_data['email'],
                    password=password,
                    first_name=serializer.validated_data.get('first_name', ''),
                    last_name=serializer.validated_data.get('last_name', ''),
                    role=serializer.validated_data.get('role', 'SUBCITY_STAFF'),
                    center=center,
                    is_active=True
                )

                # Return success response with tenant information
                return Response({
                    'user': UserSerializer(user).data,
                    'tenant': {
                        'id': child_tenant.id,
                        'name': child_tenant.name,
                        'schema_name': child_tenant.schema_name,
                        'schema_type': child_tenant.schema_type
                    },
                    'message': f"User created successfully in subcity {child_tenant.name} with role {role}"
                }, status=status.HTTP_201_CREATED)
            else:
                return Response(
                    serializer.errors,
                    status=status.HTTP_400_BAD_REQUEST
                )
    except Exception as e:
        logger.error(f"Error creating subcity user: {str(e)}")
        return Response(
            {"error": f"An error occurred: {str(e)}"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['PUT'])
@authentication_classes([MultiTenantTokenAuthentication])
@permission_classes([IsAuthenticated])
def update_subcity_user(request, schema_name, user_id):
    """
    Update a user in a specific child subcity tenant.
    """
    try:
        # For debugging, log the user information
        user = request.user
        logger.info(f"User: {user.email if hasattr(user, 'email') else 'No email'}, ID: {user.id if hasattr(user, 'id') else 'No ID'}")

        # Get the tenant from the request context
        if hasattr(request, 'tenant'):
            # If the request has a tenant attribute, use it
            current_tenant = request.tenant
            logger.info(f"Using tenant from request context: {current_tenant.name}, Schema: {current_tenant.schema_name}")

            # Check if this is a city tenant
            if current_tenant.schema_type != 'CITY':
                # If this is not a city tenant, try to find its parent city
                if current_tenant.schema_type == 'SUBCITY' and current_tenant.parent and current_tenant.parent.schema_type == 'CITY':
                    current_tenant = current_tenant.parent
                    logger.info(f"Using parent city tenant: {current_tenant.name}, Schema: {current_tenant.schema_name}")
                else:
                    # If we can't find a parent city, use any city tenant
                    city_tenants = Client.objects.filter(schema_type='CITY')
                    if city_tenants.exists():
                        current_tenant = city_tenants.first()
                        logger.info(f"Using fallback city tenant: {current_tenant.name}, Schema: {current_tenant.schema_name}")
                    else:
                        return Response(
                            {"error": "No city tenants found"},
                            status=status.HTTP_400_BAD_REQUEST
                        )
        else:
            # If the request doesn't have a tenant attribute, use any city tenant
            city_tenants = Client.objects.filter(schema_type='CITY')
            if not city_tenants.exists():
                return Response(
                    {"error": "No city tenants found"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            current_tenant = city_tenants.first()
            logger.info(f"Using fallback city tenant: {current_tenant.name}, Schema: {current_tenant.schema_name}")

        # Get the child tenant (subcity)
        try:
            child_tenant = Client.objects.get(schema_name=schema_name)

            # Verify this is a subcity tenant
            if child_tenant.schema_type != 'SUBCITY':
                return Response(
                    {"error": f"Tenant {schema_name} is not a subcity tenant"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Verify this subcity is a child of the current city
            if child_tenant.parent != current_tenant and child_tenant.parent is not None:
                return Response(
                    {"error": f"Subcity tenant {schema_name} is not a child of this city"},
                    status=status.HTTP_403_FORBIDDEN
                )

        except Client.DoesNotExist:
            return Response(
                {"error": f"Tenant with schema {schema_name} does not exist"},
                status=status.HTTP_404_NOT_FOUND
            )

        # Validate the role if it's being updated
        if 'role' in request.data:
            role = request.data.get('role')
            if role not in ['SUBCITY_ADMIN', 'SUBCITY_STAFF', 'SUBCITY_MANAGER']:
                return Response(
                    {"error": f"Invalid role: {role}. Must be one of: SUBCITY_ADMIN, SUBCITY_STAFF, SUBCITY_MANAGER"},
                    status=status.HTTP_400_BAD_REQUEST
                )

        # Switch to the child tenant context (the specific subcity)
        with tenant_context(child_tenant):
            User = get_user_model()

            # Get the user to update
            try:
                user_to_update = get_object_or_404(User, id=user_id)
            except Exception as e:
                return Response(
                    {"error": f"User with ID {user_id} not found in tenant {child_tenant.name}: {str(e)}"},
                    status=status.HTTP_404_NOT_FOUND
                )

            # Check if email is being changed and if it already exists
            if 'email' in request.data and request.data['email'] != user_to_update.email:
                if User.objects.filter(email=request.data['email']).exists():
                    return Response(
                        {"error": f"User with email {request.data['email']} already exists in tenant {child_tenant.name}"},
                        status=status.HTTP_400_BAD_REQUEST
                    )

            # Update the user
            serializer = UserSerializer(user_to_update, data=request.data, partial=True)
            if serializer.is_valid():
                # Update user fields
                if 'first_name' in serializer.validated_data:
                    user_to_update.first_name = serializer.validated_data['first_name']
                if 'last_name' in serializer.validated_data:
                    user_to_update.last_name = serializer.validated_data['last_name']
                if 'email' in serializer.validated_data:
                    user_to_update.email = serializer.validated_data['email']
                if 'role' in serializer.validated_data:
                    user_to_update.role = serializer.validated_data['role']
                if 'is_active' in serializer.validated_data:
                    user_to_update.is_active = serializer.validated_data['is_active']

                # Update password if provided
                if 'password' in request.data and request.data['password']:
                    user_to_update.set_password(request.data['password'])

                # Save the updated user
                user_to_update.save()

                # Return success response with tenant information
                return Response({
                    'user': UserSerializer(user_to_update).data,
                    'tenant': {
                        'id': child_tenant.id,
                        'name': child_tenant.name,
                        'schema_name': child_tenant.schema_name,
                        'schema_type': child_tenant.schema_type
                    },
                    'message': f"User updated successfully in subcity {child_tenant.name}"
                }, status=status.HTTP_200_OK)
            else:
                return Response(
                    serializer.errors,
                    status=status.HTTP_400_BAD_REQUEST
                )
    except Exception as e:
        logger.error(f"Error updating subcity user: {str(e)}")
        return Response(
            {"error": f"An error occurred: {str(e)}"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['DELETE'])
@authentication_classes([MultiTenantTokenAuthentication])
@permission_classes([IsAuthenticated])
def delete_subcity_user(request, schema_name, user_id):
    """
    Delete a user from a specific child subcity tenant.
    """
    try:
        # For debugging, log the user information
        user = request.user
        logger.info(f"User: {user.email if hasattr(user, 'email') else 'No email'}, ID: {user.id if hasattr(user, 'id') else 'No ID'}")

        # Get the tenant from the request context
        if hasattr(request, 'tenant'):
            # If the request has a tenant attribute, use it
            current_tenant = request.tenant
            logger.info(f"Using tenant from request context: {current_tenant.name}, Schema: {current_tenant.schema_name}")

            # Check if this is a city tenant
            if current_tenant.schema_type != 'CITY':
                # If this is not a city tenant, try to find its parent city
                if current_tenant.schema_type == 'SUBCITY' and current_tenant.parent and current_tenant.parent.schema_type == 'CITY':
                    current_tenant = current_tenant.parent
                    logger.info(f"Using parent city tenant: {current_tenant.name}, Schema: {current_tenant.schema_name}")
                else:
                    # If we can't find a parent city, use any city tenant
                    city_tenants = Client.objects.filter(schema_type='CITY')
                    if city_tenants.exists():
                        current_tenant = city_tenants.first()
                        logger.info(f"Using fallback city tenant: {current_tenant.name}, Schema: {current_tenant.schema_name}")
                    else:
                        return Response(
                            {"error": "No city tenants found"},
                            status=status.HTTP_400_BAD_REQUEST
                        )
        else:
            # If the request doesn't have a tenant attribute, use any city tenant
            city_tenants = Client.objects.filter(schema_type='CITY')
            if not city_tenants.exists():
                return Response(
                    {"error": "No city tenants found"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            current_tenant = city_tenants.first()
            logger.info(f"Using fallback city tenant: {current_tenant.name}, Schema: {current_tenant.schema_name}")

        # Get the child tenant (subcity)
        try:
            child_tenant = Client.objects.get(schema_name=schema_name)

            # Verify this is a subcity tenant
            if child_tenant.schema_type != 'SUBCITY':
                return Response(
                    {"error": f"Tenant {schema_name} is not a subcity tenant"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Verify this subcity is a child of the current city
            if child_tenant.parent != current_tenant and child_tenant.parent is not None:
                return Response(
                    {"error": f"Subcity tenant {schema_name} is not a child of this city"},
                    status=status.HTTP_403_FORBIDDEN
                )

        except Client.DoesNotExist:
            return Response(
                {"error": f"Tenant with schema {schema_name} does not exist"},
                status=status.HTTP_404_NOT_FOUND
            )

        # Switch to the child tenant context (the specific subcity)
        with tenant_context(child_tenant):
            User = get_user_model()

            # Get the user to delete
            try:
                user_to_delete = get_object_or_404(User, id=user_id)
            except Exception as e:
                return Response(
                    {"error": f"User with ID {user_id} not found in tenant {child_tenant.name}: {str(e)}"},
                    status=status.HTTP_404_NOT_FOUND
                )

            # Store user info for response
            user_info = {
                'id': user_to_delete.id,
                'email': user_to_delete.email,
                'first_name': user_to_delete.first_name,
                'last_name': user_to_delete.last_name,
                'role': user_to_delete.role
            }

            # Delete the user
            user_to_delete.delete()

            # Return success response
            return Response({
                'user': user_info,
                'tenant': {
                    'id': child_tenant.id,
                    'name': child_tenant.name,
                    'schema_name': child_tenant.schema_name,
                    'schema_type': child_tenant.schema_type
                },
                'message': f"User deleted successfully from subcity {child_tenant.name}"
            }, status=status.HTTP_200_OK)
    except Exception as e:
        logger.error(f"Error deleting subcity user: {str(e)}")
        return Response(
            {"error": f"An error occurred: {str(e)}"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )
