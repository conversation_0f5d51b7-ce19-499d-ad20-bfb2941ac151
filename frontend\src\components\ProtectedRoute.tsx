import React, { useEffect, useState } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { CircularProgress, Box, Typography } from '@mui/material';
import { validateJWTToken } from '../services/tokenService';
import { hasRoutePermission } from '../utils/rolePermissions';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredRole?: string | string[]; // Optional role requirement
}

/**
 * A component that protects routes requiring authentication
 * Redirects to login if user is not authenticated
 * Can also check for specific roles
 */
const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  requiredRole
}) => {
  const auth = useAuth();
  const { isAuthenticated, user, token, validateToken, isValidating } = auth;
  const [isValidatingToken, setIsValidatingToken] = useState(true);
  const [isTokenValid, setIsTokenValid] = useState(false);
  const location = useLocation();

  useEffect(() => {
    // Log the current path for debugging
    console.log(`ProtectedRoute: Checking authentication for path: ${location.pathname}`);

    // Prevent refresh loops by checking if we've recently validated
    const currentTime = Date.now();
    const pathKey = `validated_path_${location.pathname.replace(/\//g, '_')}`;
    const lastPathValidationTime = localStorage.getItem(pathKey);

    // If we've validated this specific path in the last 30 seconds, don't validate again
    // Increased from 5 seconds to 30 seconds to reduce validation frequency
    if (lastPathValidationTime && (currentTime - parseInt(lastPathValidationTime)) < 30000) {
      console.log(`Skipping token validation for path ${location.pathname} - recently validated`);
      setIsTokenValid(true);
      setIsValidatingToken(false);
      return;
    }

    // If we have a user and token, assume it's valid for this session
    // This prevents unnecessary token validations that might fail
    if (user && token) {
      console.log('User and token exist, assuming valid for this session');
      // Still record the validation time to prevent future validations
      localStorage.setItem('last_token_validation_time', currentTime.toString());
      localStorage.setItem(pathKey, currentTime.toString());
      setIsTokenValid(true);
      setIsValidatingToken(false);
      return;
    }

    const checkToken = async () => {
      if (!token) {
        console.log('No token found in auth context');
        setIsValidatingToken(false);
        return;
      }

      try {
        // Record validation time to prevent loops
        localStorage.setItem('last_token_validation_time', currentTime.toString());
        localStorage.setItem(pathKey, currentTime.toString());

        // Get the current schema from localStorage
        const schema = localStorage.getItem('schema_name') ||
                       localStorage.getItem('jwt_schema') ||
                       localStorage.getItem('currentSchema');

        if (!schema) {
          console.log('No schema found in storage');

          // Try to get from tenant object in localStorage
          try {
            const tenantStr = localStorage.getItem('tenant');
            if (tenantStr) {
              const tenant = JSON.parse(tenantStr);
              if (tenant && tenant.schema_name) {
                const schemaFromTenant = tenant.schema_name;
                console.log('Using schema from tenant object:', schemaFromTenant);

                // Store it for future use
                localStorage.setItem('schema_name', schemaFromTenant);
                localStorage.setItem('jwt_schema', schemaFromTenant);
                localStorage.setItem('currentSchema', schemaFromTenant);

                // Continue with validation using this schema
                console.log(`Validating token for schema from tenant: ${schemaFromTenant}`);

                // Try to validate the JWT token
                const isJwtValid = await validateJWTToken(schemaFromTenant);

                if (isJwtValid) {
                  console.log('JWT token is valid with schema from tenant');
                  setIsTokenValid(true);
                  setIsValidatingToken(false);
                  return;
                }
              }
            }
          } catch (e) {
            console.error('Error parsing tenant from localStorage:', e);
          }

          setIsTokenValid(false);
          setIsValidatingToken(false);
          return;
        }

        console.log(`Validating token for schema: ${schema}, path: ${location.pathname}`);

        // First try JWT validation
        try {
          console.log('Validating JWT token in ProtectedRoute');

          // Check if we have a refresh token in cookies
          const cookies = document.cookie.split(';');
          const refreshTokenCookie = cookies.find(cookie => cookie.trim().startsWith('refresh_token='));

          if (refreshTokenCookie) {
            console.log('Found refresh token in cookies');
          }

          // Try to validate the JWT token
          const isJwtValid = await validateJWTToken(schema);

          if (isJwtValid) {
            console.log('JWT token is valid');
            setIsTokenValid(true);
            setIsValidatingToken(false);
            return;
          } else {
            console.log('JWT token is invalid, trying to refresh');

            // Try to refresh JWT token using auth context
            try {
              const refreshResult = await auth.refreshJWTToken();

              if (refreshResult) {
                console.log('JWT token refreshed successfully');
                setIsTokenValid(true);
                setIsValidatingToken(false);
                return;
              } else {
                console.log('JWT token refresh failed, falling back to legacy token');
              }
            } catch (refreshError) {
              console.error('Error refreshing JWT token:', refreshError);
              console.log('Falling back to legacy token validation');
            }
          }
        } catch (jwtError) {
          console.error('Error validating JWT token:', jwtError);
          console.log('Falling back to legacy token validation');
        }

        // Fall back to legacy token validation
        console.log('Using legacy token validation');
        const isValid = await validateToken();

        if (isValid) {
          console.log('Legacy token validation successful');
          setIsTokenValid(true);
        } else {
          console.log('Legacy token validation failed');
          setIsTokenValid(false);
        }
      } catch (error) {
        console.error('Error validating token:', error);
        setIsTokenValid(false);
      } finally {
        setIsValidatingToken(false);
      }
    };

    checkToken();
  }, [token, validateToken, auth, location.pathname]);

  // Show loading while validating
  if (isValidatingToken || isValidating) {
    return (
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          height: '100vh'
        }}
      >
        <CircularProgress size={60} />
        <Typography variant="h6" sx={{ mt: 2 }}>
          Verifying authentication...
        </Typography>
      </Box>
    );
  }

  // Redirect to login if not authenticated or token is invalid
  if (!isAuthenticated || !isTokenValid) {
    // Store the current location to redirect back after login
    localStorage.setItem('redirectAfterLogin', location.pathname);

    return <Navigate to="/login" replace state={{ from: location, reason: 'unauthenticated' }} />;
  }

  // Check permissions for the current route
  if (user) {
    // Get user role from user object or localStorage
    const userRole = user.role || localStorage.getItem('user_role');

    console.log('Checking route access:', {
      userRole,
      path: location.pathname
    });

    // Check if user has permission to access this route
    const hasPermission = hasRoutePermission(userRole, location.pathname);

    // If requiredRole is specified, also check that
    let hasRequiredRole = true;
    if (requiredRole) {
      hasRequiredRole = Array.isArray(requiredRole)
        ? requiredRole.some(role => role === userRole || role === 'ADMIN')
        : userRole === requiredRole || userRole === 'ADMIN';
    }

    // For debugging
    if (!hasPermission) {
      console.warn(`Access denied: User role "${userRole}" does not have permission to access route: ${location.pathname}`);
    }

    if (!hasRequiredRole) {
      console.warn(`Access denied: User role "${userRole}" does not match required role(s):`,
        Array.isArray(requiredRole) ? requiredRole.join(', ') : requiredRole
      );
    }

    // Special case for SUBCITY_ADMIN - they should have access to most pages
    if ((!hasPermission || !hasRequiredRole) && userRole === 'SUBCITY_ADMIN') {
      console.log('SUBCITY_ADMIN override: Granting access despite permission mismatch');
      return <>{children}</>;
    }

    // Special case for CENTER_STAFF - they should have access to specific pages
    if ((!hasPermission || !hasRequiredRole) && userRole === 'CENTER_STAFF' &&
        (location.pathname === '/dashboard' ||
         location.pathname.startsWith('/citizens') ||
         location.pathname.startsWith('/id-cards'))) {
      console.log('CENTER_STAFF override: Granting access to citizen and ID card pages');
      return <>{children}</>;
    }

    // Special case for CENTER_ADMIN - they should have access to specific pages
    if ((!hasPermission || !hasRequiredRole) && userRole === 'CENTER_ADMIN' &&
        (location.pathname === '/dashboard' ||
         location.pathname.startsWith('/citizens') ||
         location.pathname.startsWith('/id-cards'))) {
      console.log('CENTER_ADMIN override: Granting access to citizen and ID card pages');
      return <>{children}</>;
    }

    if (!hasPermission || !hasRequiredRole) {
      return <Navigate to="/unauthorized" replace state={{ from: location, reason: 'unauthorized' }} />;
    }
  }

  // If authenticated and has required role, render the children
  return <>{children}</>;
};

export default ProtectedRoute;
