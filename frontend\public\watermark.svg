<?xml version="1.0" encoding="UTF-8"?>
<svg width="500" height="500" viewBox="0 0 500 500" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3f51b5;stop-opacity:0.3" />
      <stop offset="100%" style="stop-color:#3f51b5;stop-opacity:0.1" />
    </linearGradient>
  </defs>
  <g opacity="0.1">
    <circle cx="250" cy="250" r="200" fill="none" stroke="url(#grad1)" stroke-width="2" />
    <circle cx="250" cy="250" r="150" fill="none" stroke="url(#grad1)" stroke-width="2" />
    <circle cx="250" cy="250" r="100" fill="none" stroke="url(#grad1)" stroke-width="2" />
    <path d="M250,50 L250,450" stroke="url(#grad1)" stroke-width="1" />
    <path d="M50,250 L450,250" stroke="url(#grad1)" stroke-width="1" />
    <path d="M100,100 L400,400" stroke="url(#grad1)" stroke-width="1" />
    <path d="M400,100 L100,400" stroke="url(#grad1)" stroke-width="1" />
    <text x="250" y="250" font-family="Arial" font-size="24" fill="#3f51b5" text-anchor="middle" dominant-baseline="middle">OFFICIAL ID</text>
    <text x="250" y="280" font-family="Arial" font-size="16" fill="#3f51b5" text-anchor="middle" dominant-baseline="middle">GONDAR CITY</text>
  </g>
</svg>
