import React, { useEffect, useState } from 'react';
import { useLocation } from 'react-router-dom';

/**
 * CookiePreserver component
 *
 * This component preserves cookies during navigation by:
 * 1. Monitoring location changes
 * 2. Using TokenManager to synchronize cookies and localStorage
 * 3. Ensuring both cookies have the same expiration
 */
const CookiePreserver: React.FC = () => {
  const location = useLocation();
  const [tokenManager, setTokenManager] = useState<any>(null);

  // Load TokenManager on mount
  useEffect(() => {
    import('../services/tokenManager').then(({ tokenManager }) => {
      setTokenManager(tokenManager);
    }).catch(error => {
      console.error('Error importing tokenManager:', error);
    });
  }, []);

  // Function to preserve cookies
  const preserveCookies = () => {
    console.log('CookiePreserver: Preserving cookies on location change to', location.pathname);

    // Use TokenManager if available
    if (tokenManager) {
      console.log('CookiePreserver: Using TokenManager to synchronize cookies');
      tokenManager.synchronizeCookies();
      return;
    }

    // Fallback to traditional cookie preservation if TokenManager is not available
    // Parse cookies
    const cookies: Record<string, string> = {};
    document.cookie.split(';').forEach(cookie => {
      const [name, value] = cookie.trim().split('=');
      if (name) cookies[name] = value;
    });

    // Always ensure refresh_token cookie exists (our primary cookie)
    if (cookies['refresh_token']) {
      console.log('CookiePreserver: refresh_token exists (primary cookie)');

      // If jwt_refresh_token is missing, restore it from refresh_token
      if (!cookies['jwt_refresh_token']) {
        console.log('CookiePreserver: jwt_refresh_token is missing. Restoring from refresh_token...');

        // Set expiration to 30 days
        const expirationDate = new Date();
        expirationDate.setDate(expirationDate.getDate() + 30);

        // Copy refresh_token to jwt_refresh_token
        document.cookie = `jwt_refresh_token=${cookies['refresh_token']}; path=/; expires=${expirationDate.toUTCString()}; SameSite=Lax`;

        console.log('CookiePreserver: jwt_refresh_token cookie restored from refresh_token');
      }
    }
    // If refresh_token is missing but jwt_refresh_token exists, restore refresh_token
    else if (cookies['jwt_refresh_token']) {
      console.log('CookiePreserver: refresh_token is missing but jwt_refresh_token exists. Restoring...');

      // Set expiration to 30 days
      const expirationDate = new Date();
      expirationDate.setDate(expirationDate.getDate() + 30);

      // Check if the value is the placeholder
      if (cookies['jwt_refresh_token'] === 'HTTPONLY_COOKIE') {
        console.log('CookiePreserver: Found HTTPONLY_COOKIE placeholder, checking localStorage for actual token');

        // Try to get the actual token from localStorage
        const schema = localStorage.getItem('schema_name') || localStorage.getItem('jwt_schema');
        let actualToken = '';

        if (schema) {
          // Try schema-specific token first
          const formattedSchema = schema.replace(/\s+/g, '_');
          actualToken = localStorage.getItem(`jwt_refresh_token_${formattedSchema}`) || '';

          // If it's also a placeholder, try to find a real token
          if (actualToken === 'HTTPONLY_COOKIE') {
            actualToken = '';
          }
        }

        // If we still don't have a token, try the generic one
        if (!actualToken) {
          actualToken = localStorage.getItem('jwt_refresh_token') || '';
          if (actualToken === 'HTTPONLY_COOKIE') {
            actualToken = '';
          }
        }

        if (actualToken) {
          // Copy actual token to refresh_token
          document.cookie = `refresh_token=${actualToken}; path=/; expires=${expirationDate.toUTCString()}; SameSite=Lax`;
          console.log('CookiePreserver: refresh_token cookie restored from localStorage token');
        } else {
          console.log('CookiePreserver: No actual token found in localStorage, cannot restore refresh_token');
        }
      } else {
        // Copy jwt_refresh_token to refresh_token
        document.cookie = `refresh_token=${cookies['jwt_refresh_token']}; path=/; expires=${expirationDate.toUTCString()}; SameSite=Lax`;
        console.log('CookiePreserver: refresh_token cookie restored from jwt_refresh_token');
      }
    }
    // If neither cookie exists, log a warning
    else {
      console.warn('CookiePreserver: No refresh token cookies found. User may need to log in again.');
    }

    // Verify cookies after restoration
    const verificationCookies: Record<string, string> = {};
    document.cookie.split(';').forEach(cookie => {
      const [name, value] = cookie.trim().split('=');
      if (name) verificationCookies[name] = value;
    });

    console.log('CookiePreserver: Verification after preservation:');
    console.log('- jwt_refresh_token:', verificationCookies['jwt_refresh_token'] ? 'Present' : 'Missing');
    console.log('- refresh_token:', verificationCookies['refresh_token'] ? 'Present' : 'Missing');

    // Check localStorage and sessionStorage
    console.log('CookiePreserver: Checking other storage locations:');
    console.log('- localStorage jwt_refresh_token:', localStorage.getItem('jwt_refresh_token') ? 'Present' : 'Missing');
    console.log('- localStorage refresh_token:', localStorage.getItem('refresh_token') ? 'Present' : 'Missing');
    console.log('- sessionStorage jwt_refresh_token:', sessionStorage.getItem('jwt_refresh_token') ? 'Present' : 'Missing');
    console.log('- sessionStorage refresh_token:', sessionStorage.getItem('refresh_token') ? 'Present' : 'Missing');

    // Check schema-specific tokens
    const schema = localStorage.getItem('schema_name') || localStorage.getItem('jwt_schema');
    if (schema) {
      console.log(`- localStorage jwt_refresh_token_${schema}:`, localStorage.getItem(`jwt_refresh_token_${schema}`) ? 'Present' : 'Missing');
      console.log(`- localStorage refresh_token_${schema}:`, localStorage.getItem(`refresh_token_${schema}`) ? 'Present' : 'Missing');
    }

    // Log all cookies for debugging
    console.log('CookiePreserver: All cookies:');
    document.cookie.split(';').forEach(cookie => {
      console.log(`- ${cookie.trim()}`);
    });
  };

  // Run on mount and location change
  useEffect(() => {
    preserveCookies();

    // Set up an interval to check cookies every second
    const intervalId = setInterval(() => {
      preserveCookies();
    }, 1000);

    // Clean up interval on unmount
    return () => clearInterval(intervalId);
  }, [location]);

  // This component doesn't render anything
  return null;
};

export default CookiePreserver;
