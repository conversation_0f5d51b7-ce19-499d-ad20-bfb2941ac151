import React, { useState, useEffect, useRef } from 'react';
import {
  Box,
  Container,
  Typo<PERSON>,
  Button,
  Chip,
  CircularProgress,
  Alert,
  Card,
  CardContent,
  Divider,
  Paper,
  Grid,
  Avatar,
  IconButton,
  Tooltip,
  Tab,
  Tabs,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemSecondaryAction,
  Fade,
  Zoom
} from '@mui/material';
import { useNavigate, useParams } from 'react-router-dom';
import UnauthorizedAccess from '../components/tenant/UnauthorizedAccess';
import PageBanner from '../components/PageBanner';

// Icons
import PersonIcon from '@mui/icons-material/Person';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import EditIcon from '@mui/icons-material/Edit';
import CreditCardIcon from '@mui/icons-material/CreditCard';
import EmailIcon from '@mui/icons-material/Email';
import PhoneIcon from '@mui/icons-material/Phone';
import HomeIcon from '@mui/icons-material/Home';
import CalendarTodayIcon from '@mui/icons-material/CalendarToday';
import WcIcon from '@mui/icons-material/Wc';
import FamilyRestroomIcon from '@mui/icons-material/FamilyRestroom';
import WorkIcon from '@mui/icons-material/Work';
import SchoolIcon from '@mui/icons-material/School';
import LanguageIcon from '@mui/icons-material/Language';
import BadgeIcon from '@mui/icons-material/Badge';
import ChildCareIcon from '@mui/icons-material/ChildCare';
import ElderlyIcon from '@mui/icons-material/Elderly';
import PeopleIcon from '@mui/icons-material/People';
import LocationOnIcon from '@mui/icons-material/LocationOn';
import LocationCityIcon from '@mui/icons-material/LocationCity';
import HomeWorkIcon from '@mui/icons-material/HomeWork';
import ApartmentIcon from '@mui/icons-material/Apartment';
import AddIcon from '@mui/icons-material/Add';
import ContactPhoneIcon from '@mui/icons-material/ContactPhone';
import DescriptionIcon from '@mui/icons-material/Description';
import FingerprintIcon from '@mui/icons-material/Fingerprint';
import PhotoCameraIcon from '@mui/icons-material/PhotoCamera';
import VerifiedUserIcon from '@mui/icons-material/VerifiedUser';
import PrintIcon from '@mui/icons-material/Print';
import DownloadIcon from '@mui/icons-material/Download';
import ZoomInIcon from '@mui/icons-material/ZoomIn';
import ShareIcon from '@mui/icons-material/Share';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import ErrorIcon from '@mui/icons-material/Error';
import InfoIcon from '@mui/icons-material/Info';
import WarningIcon from '@mui/icons-material/Warning';
import AccessTimeIcon from '@mui/icons-material/AccessTime';
import LocalHospitalIcon from '@mui/icons-material/LocalHospital';

import QrCodeIcon from '@mui/icons-material/QrCode';
import VisibilityIcon from '@mui/icons-material/Visibility';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`citizen-tabpanel-${index}`}
      aria-labelledby={`citizen-tab-${index}`}
      {...other}
      style={{ paddingTop: '20px' }}
    >
      {value === index && (
        <Box sx={{ p: { xs: 2, md: 3 }, backgroundColor: '#fafafa', borderBottomLeftRadius: 3, borderBottomRightRadius: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

function a11yProps(index: number) {
  return {
    id: `citizen-tab-${index}`,
    'aria-controls': `citizen-tabpanel-${index}`,
  };
}

// Common card style for consistent appearance
const cardStyle = {
  borderRadius: 3,
  overflow: 'hidden',
  boxShadow: '0 8px 24px rgba(0,0,0,0.12)',
  border: '1px solid rgba(0, 0, 0, 0.05)',
  position: 'relative',
  background: 'linear-gradient(to bottom, #ffffff 0%, #fafafa 100%)',
  height: '100%',
};

// Card header style for consistent appearance
const cardHeaderStyle = {
  p: 3,
  display: 'flex',
  alignItems: 'center',
  bgcolor: 'white',
  color: 'text.primary',
  borderBottom: '1px solid rgba(0, 0, 0, 0.05)'
};

// Card icon container style
const iconContainerStyle = (bgColor: string) => ({
  mr: 2,
  bgcolor: bgColor,
  color: 'white',
  p: 1.5,
  borderRadius: 2,
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center'
});

// Card content style
const cardContentStyle = {
  p: 3
};

// Nested card style for family members, documents, etc.
const nestedCardStyle = {
  borderRadius: 2,
  boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
  height: '100%',
  position: 'relative',
  overflow: 'hidden'
};

// Style for colored left border on nested cards
const leftBorderStyle = (color: string) => ({
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    width: '4px',
    height: '100%',
    bgcolor: color
  }
});

// Tab Panel component is already defined elsewhere in the file

const CitizenDetails: React.FC = () => {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();

  // State for citizen data
  const [citizen, setCitizen] = useState<any>(null);
  // Ref to store the raw citizen data
  const rawCitizenDataRef = useRef<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  // State for tabs
  const [tabValue, setTabValue] = useState(0);

  // Reference data
  const [religions, setReligions] = useState<any[]>([]);
  const [maritalStatuses, setMaritalStatuses] = useState<any[]>([]);
  const [citizenStatuses, setCitizenStatuses] = useState<any[]>([]);
  const [employmentTypes, setEmploymentTypes] = useState<any[]>([]);
  const [regions, setRegions] = useState<any[]>([]);
  const [subcities, setSubcities] = useState<any[]>([]);
  const [kebeles, setKebeles] = useState<any[]>([]);
  const [ketenas, setKetenas] = useState<any[]>([]);

  // Handle tab change
  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  // Helper functions for getting names from IDs
  const getReligionName = (id: number | string) => {
    if (!id) return 'Not specified';

    // If it's already a string with a name, return it
    if (typeof id === 'string' && isNaN(Number(id))) {
      return id;
    }

    // Convert to number if it's a numeric string
    const numericId = typeof id === 'string' ? Number(id) : id;

    // Try to find the corresponding religion
    if (Array.isArray(religions) && religions.length > 0) {
      const religion = religions.find(r => r.id === numericId);
      if (religion) return religion.name;
    }

    // Fallback
    return `Religion ${id}`;
  };

  const getMaritalStatusName = (id: number | string) => {
    if (!id) return 'Not specified';

    // For marital statuses, the API returns string IDs like "MARRIED"
    // If it's a string, try to find it directly or use it as is
    if (typeof id === 'string') {
      // Try to find the corresponding status by ID (for string IDs like "MARRIED")
      if (Array.isArray(maritalStatuses) && maritalStatuses.length > 0) {
        const status = maritalStatuses.find(s => s.id === id);
        if (status) return status.name;

        // If not found by ID, check if any status name matches the string
        const statusByName = maritalStatuses.find(s =>
          s.name.toLowerCase() === id.toLowerCase());
        if (statusByName) return statusByName.name;
      }

      // If it's a string that looks like a name, return it
      if (isNaN(Number(id))) {
        return id;
      }
    }

    // If it's a number, try to find by numeric ID
    if (typeof id === 'number' && Array.isArray(maritalStatuses) && maritalStatuses.length > 0) {
      const status = maritalStatuses.find(s => s.id === id);
      if (status) return status.name;
    }

    // Fallback
    return `Marital Status ${id}`;
  };

  const getCitizenStatusName = (id: number | string) => {
    if (!id) return 'Not specified';

    // If it's already a string with a name, return it
    if (typeof id === 'string' && isNaN(Number(id))) {
      return id;
    }

    // Convert to number if it's a numeric string
    const numericId = typeof id === 'string' ? Number(id) : id;

    // Try to find the corresponding status
    if (Array.isArray(citizenStatuses) && citizenStatuses.length > 0) {
      const status = citizenStatuses.find(s => s.id === numericId);
      if (status) return status.name;
    }

    // Fallback
    return `Citizen Status ${id}`;
  };

  const getEmploymentTypeName = (id: number | string) => {
    if (!id) return 'Not specified';

    // If it's already a string with a name, return it
    if (typeof id === 'string' && isNaN(Number(id))) {
      return id;
    }

    // Convert to number if it's a numeric string
    const numericId = typeof id === 'string' ? Number(id) : id;

    // Try to find the corresponding type
    if (Array.isArray(employmentTypes) && employmentTypes.length > 0) {
      const type = employmentTypes.find(t => t.id === numericId);
      if (type) return type.name;
    }

    // Fallback
    return `Employment Type ${id}`;
  };

  const getRegionName = (id: number | string) => {
    if (!id) return 'Not specified';

    // If it's already a string with a name, return it
    if (typeof id === 'string' && isNaN(Number(id))) {
      return id;
    }

    // Convert to number if it's a numeric string
    const numericId = typeof id === 'string' ? Number(id) : id;

    // Try to find the corresponding region
    if (Array.isArray(regions) && regions.length > 0) {
      const region = regions.find(r => r.id === numericId);
      if (region) return region.name;
    }

    // Fallback
    return `Region ${id}`;
  };

  const getSubcityName = (id: number | string) => {
    if (!id) return 'Not specified';

    // If it's already a string with a name, return it
    if (typeof id === 'string' && isNaN(Number(id))) {
      return id;
    }

    // Convert to number if it's a numeric string
    const numericId = typeof id === 'string' ? Number(id) : id;

    // Try to find the corresponding subcity
    if (Array.isArray(subcities) && subcities.length > 0) {
      const subcity = subcities.find(s => s.id === numericId);
      if (subcity) return subcity.name;
    }

    // Fallback
    return `Subcity ${id}`;
  };

  const getKebeleName = (id: number | string) => {
    if (!id) return 'Not specified';

    // If it's already a string with a name, return it
    if (typeof id === 'string') {
      // Check if it's a numeric string
      if (!isNaN(Number(id))) {
        // It's a numeric string, format it as 'Kebele XX'
        return `Kebele ${id}`;
      }
      // It's a non-numeric string, assume it's already the name
      return id;
    }

    // It's a number, format it as 'Kebele XX'
    return `Kebele ${id}`;
  };

  const getKetenaName = (id: number | string) => {
    if (!id) return 'Not specified';

    // If it's already a string with a name, return it
    if (typeof id === 'string') {
      // Check if it's a numeric string
      if (!isNaN(Number(id))) {
        // It's a numeric string, try to find the corresponding ketena
        if (Array.isArray(ketenas) && ketenas.length > 0) {
          const ketena = ketenas.find(k => k.id === Number(id));
          if (ketena) return ketena.name;
        }
        // If not found but within valid range, format it
        if (Number(id) > 0 && Number(id) <= 3) {
          return `Ketena ${String(Number(id)).padStart(2, '0')}`;
        }
        return `Ketena ${id}`;
      }
      // It's a non-numeric string, assume it's already the name
      return id;
    }

    // It's a number, try to find the corresponding ketena
    if (Array.isArray(ketenas) && ketenas.length > 0) {
      const ketena = ketenas.find(k => k.id === id);
      if (ketena) return ketena.name;
    }

    // If not found but within valid range, format it
    if (id > 0 && id <= 3) {
      return `Ketena ${String(id).padStart(2, '0')}`;
    }

    // Fallback
    return `Ketena ${id}`;
  };

  // No longer needed with tabbed interface
  // const [expandedSections, setExpandedSections] = useState<{[key: string]: boolean}>({});
  // const toggleSection = (section: string) => {
  //   setExpandedSections(prev => ({
  //     ...prev,
  //     [section]: !prev[section]
  //   }));
  // };

  // Get the current tenant and authentication info from localStorage
  const tenantString = localStorage.getItem('tenant');
  const tenant = tenantString ? JSON.parse(tenantString) : null;
  const token = localStorage.getItem('token');
  const schemaName = localStorage.getItem('schema_name');

  // Log tenant information for debugging
  console.log('Tenant information:', tenant);
  console.log('Schema name:', schemaName);
  console.log('Tenant parent:', tenant?.parent);
  console.log('Tenant type:', tenant?.type);
  console.log('Tenant name:', tenant?.name);

  // Redirect to login if not authenticated
  // Skip this check for testing
  // useEffect(() => {
  //   if (!token) {
  //     navigate('/login');
  //   }
  // }, [token, navigate]);

  // Check if tenant type is allowed to access this page
  // Always return true for testing
  const isTenantAuthorized = true; // tenant?.type === 'KEBELE';

  // Initialize reference data with empty arrays
  // The actual data will be fetched from the API
  useEffect(() => {
    // Initialize with empty arrays
    // The actual data will be fetched in fetchReferenceData
    setReligions([]);
    setMaritalStatuses([]);
    setCitizenStatuses([]);
    setEmploymentTypes([]);
    setRegions([]);
    setSubcities([]);
    setKetenas([]);
  }, []);

  // Fetch reference data
  const fetchReferenceData = async () => {
    console.log('Fetching reference data...');
    try {
      // Use the tenant-specific API endpoint
      let schema = schemaName || tenant?.schema_name || '';
      const encodedSchema = encodeURIComponent(schema);

      // Get JWT token from localStorage
      const jwtToken = localStorage.getItem('jwt_access_token');
      console.log('JWT token for reference data:', jwtToken ? 'Present' : 'Not found');

      if (!jwtToken) {
        console.warn('No JWT token found for reference data. Some data may not be available.');
        // Continue without a token - reference data might be publicly accessible
      }

      // Create headers object to use for all fetch requests
      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
        'X-Schema-Name': schema
      };

      // Add Authorization header if JWT token is available
      if (jwtToken) {
        headers['Authorization'] = `Bearer ${jwtToken}`;
      }

      // Log the headers for debugging
      console.log('Using headers for reference data:', headers);

      // Fetch religions
      try {

        const religionsResponse = await fetch(`http://localhost:8000/api/common/religions/`, {
          method: 'GET',
          headers,
          credentials: 'include'
        });

        if (religionsResponse.ok) {
          const religionsData = await religionsResponse.json();
          console.log('Fetched religions:', religionsData);
          // Store the results array, not the paginated response
          if (religionsData.results && Array.isArray(religionsData.results)) {
            console.log('Setting religions from results array:', religionsData.results);
            setReligions(religionsData.results);
          } else {
            console.log('Setting religions directly:', religionsData);
            setReligions(religionsData);
          }
        } else {
          console.error('Failed to fetch religions:', religionsResponse.status, religionsResponse.statusText);
        }
      } catch (error) {
        console.warn('Error fetching religions:', error);
      }

      // Fetch marital statuses
      try {
        const maritalStatusesResponse = await fetch(`http://localhost:8000/api/common/marital-statuses/`, {
          method: 'GET',
          headers,
          credentials: 'include'
        });

        if (maritalStatusesResponse.ok) {
          const maritalStatusesData = await maritalStatusesResponse.json();
          console.log('Fetched marital statuses:', maritalStatusesData);
          // Store the results array, not the paginated response
          if (maritalStatusesData.results && Array.isArray(maritalStatusesData.results)) {
            setMaritalStatuses(maritalStatusesData.results);
          } else {
            setMaritalStatuses(maritalStatusesData);
          }
        }
      } catch (error) {
        console.warn('Error fetching marital statuses:', error);
      }

      // Fetch citizen statuses
      try {
        const citizenStatusesResponse = await fetch(`http://localhost:8000/api/common/citizen-statuses/`, {
          method: 'GET',
          headers,
          credentials: 'include'
        });

        if (citizenStatusesResponse.ok) {
          const citizenStatusesData = await citizenStatusesResponse.json();
          console.log('Fetched citizen statuses:', citizenStatusesData);
          // Store the results array, not the paginated response
          if (citizenStatusesData.results && Array.isArray(citizenStatusesData.results)) {
            setCitizenStatuses(citizenStatusesData.results);
          } else {
            setCitizenStatuses(citizenStatusesData);
          }
        }
      } catch (error) {
        console.warn('Error fetching citizen statuses:', error);
      }

      // Fetch employment types
      try {
        const employmentTypesResponse = await fetch(`http://localhost:8000/api/common/employment-types/`, {
          method: 'GET',
          headers,
          credentials: 'include'
        });

        if (employmentTypesResponse.ok) {
          const employmentTypesData = await employmentTypesResponse.json();
          console.log('Fetched employment types:', employmentTypesData);
          // Store the results array, not the paginated response
          if (employmentTypesData.results && Array.isArray(employmentTypesData.results)) {
            setEmploymentTypes(employmentTypesData.results);
          } else {
            setEmploymentTypes(employmentTypesData);
          }
        }
      } catch (error) {
        console.warn('Error fetching employment types:', error);
      }

      // Fetch regions
      try {
        const regionsResponse = await fetch(`http://localhost:8000/api/common/regions/`, {
          method: 'GET',
          headers,
          credentials: 'include'
        });

        if (regionsResponse.ok) {
          const regionsData = await regionsResponse.json();
          console.log('Fetched regions:', regionsData);
          // Store the results array, not the paginated response
          if (regionsData.results && Array.isArray(regionsData.results)) {
            setRegions(regionsData.results);
          } else {
            setRegions(regionsData);
          }
        } else {
          console.warn('Failed to fetch regions');
        }
      } catch (error) {
        console.warn('Error fetching regions:', error);
      }

      // Fetch ketenas
      try {
        const ketenasResponse = await fetch(`http://localhost:8000/api/common/ketenas/`, {
          method: 'GET',
          headers,
          credentials: 'include'
        });

        if (ketenasResponse.ok) {
          const ketenasData = await ketenasResponse.json();
          console.log('Fetched ketenas:', ketenasData);
          // Store the results array, not the paginated response
          if (ketenasData.results && Array.isArray(ketenasData.results)) {
            setKetenas(ketenasData.results);
          } else {
            setKetenas(ketenasData);
          }
        } else {
          console.warn('Failed to fetch ketenas');
        }
      } catch (error) {
        console.warn('Error fetching ketenas:', error);
      }

      // Fetch subcities
      try {
        const subcitiesResponse = await fetch(`http://localhost:8000/api/subcities/`, {
          method: 'GET',
          headers,
          credentials: 'include'
        });

        if (subcitiesResponse.ok) {
          const subcitiesData = await subcitiesResponse.json();
          console.log('Fetched subcities:', subcitiesData);
          // Store the results array, not the paginated response
          if (subcitiesData.results && Array.isArray(subcitiesData.results)) {
            setSubcities(subcitiesData.results);
          } else {
            setSubcities(subcitiesData);
          }
        } else {
          console.warn('Failed to fetch subcities');
        }
      } catch (error) {
        console.warn('Error fetching subcities:', error);
      }

      // Don't fetch kebeles as they are tenants
      // We'll use the tenant information directly from the citizen data

    } catch (error) {
      console.error('Error fetching reference data:', error);
    }
  };

  useEffect(() => {
    // First fetch reference data, then fetch citizen data
    const loadData = async () => {
      console.log('Starting data loading sequence');
      await fetchReferenceData();
      console.log('Reference data loaded, now fetching citizen data');
      await fetchCitizen();
    };

    loadData();
  }, []);

  // Fetch photo URL directly
  const fetchPhotoUrl = async (citizenId: string) => {
    try {
      let schema = schemaName || tenant?.schema_name || '';
      const encodedSchema = encodeURIComponent(schema);

      // Try a different approach - fetch the citizen data again which should include the photo
      const citizenUrl = `/api/tenant/${encodedSchema}/citizens/${citizenId}/`;

      console.log('Fetching citizen data to get photo:', citizenUrl);

      // We need a JWT token for authentication

      // Get JWT token from localStorage
      const jwtToken = localStorage.getItem('jwt_access_token');
      console.log('JWT token for photo URL:', jwtToken ? 'Present' : 'Not found');

      if (!jwtToken) {
        console.error('No JWT token found for photo URL');
        return null;
      }

      console.log('Using Bearer authentication for photo URL with JWT token');

      const response = await fetch(citizenUrl, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${jwtToken}`,
          'Content-Type': 'application/json',
          'X-Schema-Name': schema
        },
        credentials: 'include'
      });

      if (response.ok) {
        console.log('Citizen data fetch successful');
        const data = await response.json();

        // Log the entire response for easier inspection
        console.log('PHOTO FETCH - Full API response:', JSON.stringify(data, null, 2));

        // Check if the response contains a photo field
        console.log('PHOTO FETCH - Checking for photo in citizen data:', data.photo);

        // Try to find the photo URL in the response
        let photoUrl = null;

        // Check if there's a photo field that's an object with an id property
        // We'll skip this approach since we know it's returning 404 errors
        // if (data.photo && typeof data.photo === 'object' && data.photo.id) {
        //   console.log('PHOTO FETCH - Found photo object with ID:', data.photo.id);
        //
        //   // Construct a URL to the photo based on the ID
        //   const photoId = data.photo.id;
        //   photoUrl = `http://localhost:8000/media/photos/${photoId}.jpg`;
        //   console.log('PHOTO FETCH - Constructed photo URL from ID:', photoUrl);
        // }
        // Check if there's a photo field with a photo property
        if (data.photo && typeof data.photo === 'object' && data.photo.photo) {
          photoUrl = data.photo.photo;
          console.log('PHOTO FETCH - Found photo.photo:', photoUrl);
        }
        // Check if there's a photo_obj field with a photo property
        else if (data.photo_obj && typeof data.photo_obj === 'object' && data.photo_obj.photo) {
          photoUrl = data.photo_obj.photo;
          console.log('PHOTO FETCH - Found photo_obj.photo:', photoUrl);
        }
        // Check if there's a photo field that's a string
        else if (data.photo && typeof data.photo === 'string') {
          photoUrl = data.photo;
          console.log('PHOTO FETCH - Found direct photo string:', photoUrl);
        }
        // Check if there's a photo_url field
        else if (data.photo_url) {
          photoUrl = data.photo_url;
          console.log('PHOTO FETCH - Found photo_url field:', photoUrl);
        }

        // If we found a photo URL, store it and return it
        if (photoUrl) {
          console.log('PHOTO FETCH - Found photo URL:', photoUrl);

          // Check if it's a relative path and convert it to an absolute URL
          if (typeof photoUrl === 'string' && photoUrl.startsWith('/')) {
            // Use the current origin as the base URL
            const baseUrl = window.location.origin;
            const absoluteUrl = `${baseUrl}${photoUrl}`;
            console.log('PHOTO FETCH - Converting relative URL to absolute URL:', photoUrl, '->', absoluteUrl);
            photoUrl = absoluteUrl;
          }

          (window as any).citizenPhotoUrl = photoUrl;
          return photoUrl;
        }

        // Check if there's a base64 encoded photo data
        if (data.photo && typeof data.photo === 'object' && data.photo.data) {
          const base64Data = data.photo.data;
          console.log('PHOTO FETCH - Found base64 photo data');
          // Create a data URL from the base64 data
          const dataUrl = `data:image/jpeg;base64,${base64Data}`;
          console.log('PHOTO FETCH - Created data URL from base64 data');
          (window as any).citizenPhotoUrl = dataUrl;
          return dataUrl;
        }

        // Check if there's a photo field with a binary data property
        if (data.photo && typeof data.photo === 'object' && data.photo.binary_data) {
          const binaryData = data.photo.binary_data;
          console.log('PHOTO FETCH - Found binary photo data');
          // Create a data URL from the binary data
          const dataUrl = `data:image/jpeg;base64,${binaryData}`;
          console.log('PHOTO FETCH - Created data URL from binary data');
          (window as any).citizenPhotoUrl = dataUrl;
          return dataUrl;
        }

        // If we still don't have a photo URL, try to find any field that might contain a URL
        for (const key in data) {
          if (typeof data[key] === 'string' &&
              (data[key].includes('/media/') ||
               data[key].includes('.jpg') ||
               data[key].includes('.png') ||
               data[key].includes('.jpeg'))) {
            photoUrl = data[key];
            console.log(`PHOTO FETCH - Found potential photo URL in ${key}:`, photoUrl);
            (window as any).citizenPhotoUrl = photoUrl;
            return photoUrl;
          }

          // Check nested objects
          if (typeof data[key] === 'object' && data[key] !== null) {
            for (const subKey in data[key]) {
              if (typeof data[key][subKey] === 'string' &&
                  (data[key][subKey].includes('/media/') ||
                   data[key][subKey].includes('.jpg') ||
                   data[key][subKey].includes('.png') ||
                   data[key][subKey].includes('.jpeg'))) {
                photoUrl = data[key][subKey];
                console.log(`PHOTO FETCH - Found potential photo URL in ${key}.${subKey}:`, photoUrl);
                (window as any).citizenPhotoUrl = photoUrl;
                return photoUrl;
              }
            }
          }
        }

        console.log('PHOTO FETCH - No photo URL found in citizen data');

        // As a last resort, try a hardcoded test image URL
        const testImageUrl = 'https://www.gravatar.com/avatar/00000000000000000000000000000000?d=mp&f=y';
        console.log('PHOTO FETCH - Using hardcoded test image URL as last resort:', testImageUrl);
        (window as any).citizenPhotoUrl = testImageUrl;
        return testImageUrl;
      } else {
        console.log('PHOTO FETCH - Citizen data fetch failed with status:', response.status);
        return null;
      }
    } catch (error) {
      console.error('PHOTO FETCH - Error fetching photo URL:', error);
      return null;
    }
  };

  // Fetch citizen from the API
  const fetchCitizen = async () => {
    // Skip token check for testing
    // if (!token) {
    //   setError('Authentication token not found. Please log in again.');
    //   setLoading(false);
    //   return;
    // }

    setLoading(true);
    try {
      // Use the tenant-specific API endpoint
      let schema = schemaName || tenant?.schema_name || '';
      // Make sure to properly encode the schema name to handle spaces
      const encodedSchema = encodeURIComponent(schema);
      const url = `http://localhost:8000/api/tenant/${encodedSchema}/citizens/${id}/`;
      console.log('Fetching citizen with URL:', url);

      // Also construct a photo URL to check if it exists
      const photoUrl = `http://localhost:8000/api/tenant/${encodedSchema}/citizens/${id}/photo/`;
      console.log('Constructed photo URL to check:', photoUrl);

      try {
        // We need a JWT token for authentication
        // Log the actual token from localStorage for debugging
        console.log('Actual token from localStorage:', token || 'No token');

        // Get JWT token from localStorage
        const jwtToken = localStorage.getItem('jwt_access_token');
        console.log('JWT token from localStorage:', jwtToken ? 'Present' : 'Not found');

        if (!jwtToken) {
          throw new Error('No JWT token found. Please log in again.');
        }

        console.log('Using Bearer authentication with JWT token');

        const response = await fetch(url, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${jwtToken}`,
            'Content-Type': 'application/json',
            'X-Schema-Name': schema
          },
          credentials: 'include'
        });

        // We know the direct photo URL endpoint doesn't work, so we'll skip the HEAD check

        if (!response.ok) {
          throw new Error(`Failed to fetch citizen: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();
        console.log('%c DIRECT API RESPONSE', 'background: #00ff00; color: black; font-size: 20px;');
        console.log('Fetched citizen data from API:', data);

        // Log the entire response as a string for easier inspection
        console.log('Full API response as string:', JSON.stringify(data, null, 2));

        // Check for photo in the API response
        console.log('%c PHOTO IN API RESPONSE', 'background: #00ff00; color: black; font-size: 20px;');
        console.log('Photo in API response:', data.photo);

        // Examine all fields that might contain photo information
        for (const key in data) {
          if (typeof key === 'string' && key.toLowerCase().includes('photo')) {
            console.log(`Found photo-related field: ${key}`, data[key]);
            console.log(`Type of ${key}:`, typeof data[key]);

            if (typeof data[key] === 'object' && data[key] !== null) {
              console.log(`Keys in ${key}:`, Object.keys(data[key]));

              // Log all properties of this object
              for (const subKey in data[key]) {
                console.log(`${key}.${subKey}:`, data[key][subKey]);
                console.log(`Type of ${key}.${subKey}:`, typeof data[key][subKey]);
              }
            }
          }
        }

        // Check if there's a photo field in the citizen object
        if (data.photo) {
          console.log('Photo field exists:', data.photo);
          console.log('Photo type:', typeof data.photo);

          if (typeof data.photo === 'object') {
            console.log('Photo keys:', Object.keys(data.photo));

            // Log all properties of the photo object
            for (const key in data.photo) {
              console.log(`data.photo.${key}:`, data.photo[key]);
              console.log(`Type of data.photo.${key}:`, typeof data.photo[key]);
            }
          }
        } else {
          console.log('No photo field in the API response');
        }

        // Check for photo_obj in the API response
        if (data.photo_obj) {
          console.log('Photo_obj field exists:', data.photo_obj);
          console.log('Photo_obj type:', typeof data.photo_obj);

          if (typeof data.photo_obj === 'object') {
            console.log('Photo_obj keys:', Object.keys(data.photo_obj));

            // Log all properties of the photo_obj object
            for (const key in data.photo_obj) {
              console.log(`data.photo_obj.${key}:`, data.photo_obj[key]);
              console.log(`Type of data.photo_obj.${key}:`, typeof data.photo_obj[key]);
            }
          }
        } else {
          console.log('No photo_obj field in the API response');
        }

        // Log the entire response for debugging
        console.log('Full API response:', JSON.stringify(data, null, 2));

        // Log all photo-related fields in the API response
        console.log('API PHOTO FIELDS ANALYSIS:');
        for (const key in data) {
          if (key.toLowerCase().includes('photo')) {
            console.log(`Found photo-related field: ${key}`, data[key]);
          }
        }

        // Check if photo is a string and convert it to an object format if needed
        if (typeof data.photo === 'string') {
          console.log('Photo is a direct string URL, converting to object format');
          data.photo = {
            photo_url: data.photo
          };
        }

        // Check if photo is missing but photo_obj exists
        if (!data.photo && data.photo_obj) {
          console.log('Photo is missing but photo_obj exists, copying to photo');
          data.photo = data.photo_obj;
        }

        // Check if photo is an object but doesn't have photo_url
        if (data.photo && typeof data.photo === 'object' && !data.photo.photo_url) {
          console.log('Photo is an object but missing photo_url');

          // Try to find a URL property
          if (data.photo.url) {
            console.log('Found url property instead, using as photo_url');
            data.photo.photo_url = data.photo.url;
          }
          // Try to find a photo property
          else if (data.photo.photo) {
            console.log('Found photo property instead, using as photo_url');
            // Check if photo.photo is a string URL
            if (typeof data.photo.photo === 'string') {
              data.photo.photo_url = data.photo.photo;
            }
            // Check if photo.photo is an object with a url property
            else if (typeof data.photo.photo === 'object' && data.photo.photo.url) {
              data.photo.photo_url = data.photo.photo.url;
            }
          }
          // Try to find any property that might be a URL
          else {
            const possibleProps = ['file', 'src', 'path', 'image', 'img'];
            for (const prop of possibleProps) {
              if (data.photo[prop]) {
                console.log(`Found ${prop} property, using as photo_url`);
                data.photo.photo_url = data.photo[prop];
                break;
              }
            }
          }
        }

        // We'll skip trying to construct a URL since we know it's returning 404 errors
        // if ((!data.photo || (typeof data.photo === 'object' && !data.photo.photo_url)) && data.id) {
        //   console.log('No photo URL found, constructing one');
        //   const baseUrl = 'http://localhost:8000';
        //   const schema = encodeURIComponent(localStorage.getItem('schema_name') || '');
        //   const constructedUrl = `${baseUrl}/api/tenant/${schema}/citizens/${data.id}/photo/`;
        //   console.log('Constructed photo URL:', constructedUrl);
        //
        //   if (!data.photo) {
        //     data.photo = { photo_url: constructedUrl };
        //   } else {
        //     data.photo.photo_url = constructedUrl;
        //   }
        // }

        // Store the raw data first
        const rawData = {
          ...data,
          // Ensure all required arrays are initialized
          spouses: data.spouses || [],
          parents: data.parents || [],
          children: data.children || [],
          emergency_contacts: data.emergency_contacts || [],
          documents: data.documents || [],
          id_cards: data.id_cards || []
        };

        // Store the raw data in a ref for later use
        rawCitizenDataRef.current = rawData;

        // Enrich the data with reference names
        enrichCitizenData(rawData);
      } catch (fetchErr) {
        console.error('Error fetching citizen data:', fetchErr);
        setError('Failed to load citizen data. Please try again later.');
        // Don't set any mock data - we only want to use real data from the API
      }
    } catch (error: any) {
      console.error('Error in citizen details:', error);
      setError(error.message || 'Failed to load citizen details');
    } finally {
      setLoading(false);
    }
  };

  // Navigate back to citizens list
  const handleBackToList = () => {
    navigate('/citizens');
  };

  // Navigate to edit citizen
  const handleEditCitizen = () => {
    navigate(`/citizens/edit/${id}`);
  };

  // Navigate to register ID card for this citizen
  const handleRegisterIDCard = () => {
    navigate(`/id-cards/new?citizen=${id}`);
  };

  // State for ID generation
  const [generatingId, setGeneratingId] = useState(false);
  const [idGenerationError, setIdGenerationError] = useState('');
  const [idGenerationSuccess, setIdGenerationSuccess] = useState(false);

  // Handle generating ID number for the citizen
  const handleGenerateID = async () => {
    setGeneratingId(true);
    setIdGenerationError('');
    setIdGenerationSuccess(false);

    try {
      // Use the tenant-specific API endpoint
      let schema = schemaName || tenant?.schema_name || '';
      // Make sure to properly encode the schema name to handle spaces
      const encodedSchema = encodeURIComponent(schema);
      const url = `http://localhost:8000/api/tenant/${encodedSchema}/citizens/${id}/generate-id/`;
      console.log('Generating ID with URL:', url);

      // We need a JWT token for authentication

      // Get JWT token from localStorage
      const jwtToken = localStorage.getItem('jwt_access_token');
      console.log('JWT token for generate ID:', jwtToken ? 'Present' : 'Not found');

      if (!jwtToken) {
        throw new Error('No JWT token found. Please log in again.');
      }

      console.log('Using Bearer authentication for generate ID with JWT token');

      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${jwtToken}`,
          'Content-Type': 'application/json',
          'X-Schema-Name': schema
        },
        credentials: 'include'
      });

      if (!response.ok) {
        // If API call fails, throw an error with the response data
        try {
          const errorData = await response.json();
          throw new Error(errorData.detail || errorData.error || JSON.stringify(errorData) || 'Failed to generate ID');
        } catch (jsonError) {
          throw new Error(`HTTP error ${response.status}: ${response.statusText}`);
        }
      }

      const data = await response.json();
      console.log('ID generated successfully:', data);

      // Update the citizen with the new ID number
      if (citizen && data.id_number) {
        setCitizen({
          ...citizen,
          id_number: data.id_number
        });
      }

      // Show success message
      setIdGenerationSuccess(true);

      // Refresh the citizen details to show the new ID
      fetchCitizen();
    } catch (error: any) {
      console.error('Error generating ID:', error);
      setIdGenerationError(error.message || 'Failed to generate ID');
    } finally {
      setGeneratingId(false);
    }
  };

  // Handle adding a spouse
  const handleAddSpouse = () => {
    // Navigate to a form to add spouse information
    navigate(`/citizens/${id}/add-spouse`);
  };

  // Handle adding a parent
  const handleAddParent = () => {
    // Navigate to a form to add parent information
    navigate(`/citizens/${id}/add-parent`);
  };

  // Handle adding a child
  const handleAddChild = () => {
    // Navigate to a form to add child information
    navigate(`/citizens/${id}/add-child`);
  };

  // Handle adding an emergency contact
  const handleAddEmergencyContact = () => {
    // Navigate to a form to add emergency contact information
    navigate(`/citizens/${id}/add-emergency-contact`);
  };

  // Handle uploading documents
  const handleUploadDocuments = () => {
    // Navigate to a form to upload documents
    navigate(`/citizens/${id}/upload-documents`);
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    if (!dateString) return 'Not provided';
    const options: Intl.DateTimeFormatOptions = { year: 'numeric', month: 'long', day: 'numeric' };
    return new Date(dateString).toLocaleDateString(undefined, options);
  };

  // Calculate age from date of birth
  const calculateAge = (dateOfBirth: string) => {
    if (!dateOfBirth) return 'N/A';
    const today = new Date();
    const birthDate = new Date(dateOfBirth);
    let age = today.getFullYear() - birthDate.getFullYear();
    const m = today.getMonth() - birthDate.getMonth();
    if (m < 0 || (m === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }
    return age;
  };

  // Utility function to get photo URL from citizen data
  const getPhotoUrl = (citizenData: any) => {
    if (!citizenData) {
      console.log('getPhotoUrl: citizenData is null or undefined');
      return null;
    }

    console.log('getPhotoUrl: Examining citizen data:', citizenData);
    console.log('getPhotoUrl: Photo property:', citizenData.photo);
    console.log('getPhotoUrl: Photo_obj property:', citizenData.photo_obj);

    // Try all possible ways to get the photo URL
    let photoUrl = null;

    // Option 1: Direct photo URL
    if (typeof citizenData.photo === 'string') {
      photoUrl = citizenData.photo;
      console.log('getPhotoUrl: Using direct photo string URL:', photoUrl);
    }
    // Option 2: photo.photo_url
    else if (citizenData.photo?.photo_url) {
      photoUrl = citizenData.photo.photo_url;
      console.log('getPhotoUrl: Using citizen.photo.photo_url:', photoUrl);
    }
    // Option 3: photo_obj.photo_url
    else if (citizenData.photo_obj?.photo_url) {
      photoUrl = citizenData.photo_obj.photo_url;
      console.log('getPhotoUrl: Using citizen.photo_obj.photo_url:', photoUrl);
    }
    // Option 4: photo.photo
    else if (citizenData.photo?.photo) {
      photoUrl = citizenData.photo.photo;
      console.log('getPhotoUrl: Using citizen.photo.photo:', photoUrl);
    }
    // Option 5: photo_obj.photo
    else if (citizenData.photo_obj?.photo) {
      photoUrl = citizenData.photo_obj.photo;
      console.log('getPhotoUrl: Using citizen.photo_obj.photo:', photoUrl);
    }
    // Option 6: photo.url
    else if (citizenData.photo?.url) {
      photoUrl = citizenData.photo.url;
      console.log('getPhotoUrl: Using citizen.photo.url:', photoUrl);
    }
    // Option 7: photo_obj.url
    else if (citizenData.photo_obj?.url) {
      photoUrl = citizenData.photo_obj.url;
      console.log('getPhotoUrl: Using citizen.photo_obj.url:', photoUrl);
    }
    // Option 8: Check if photo is an object with a file property
    else if (citizenData.photo?.file) {
      photoUrl = citizenData.photo.file;
      console.log('getPhotoUrl: Using citizen.photo.file:', photoUrl);
    }
    // Option 9: Check if photo_obj is an object with a file property
    else if (citizenData.photo_obj?.file) {
      photoUrl = citizenData.photo_obj.file;
      console.log('getPhotoUrl: Using citizen.photo_obj.file:', photoUrl);
    }

    // If we found a URL, check if it's a relative path and convert it to an absolute URL
    if (photoUrl && typeof photoUrl === 'string' && photoUrl.startsWith('/')) {
      // Use the current origin as the base URL
      const baseUrl = window.location.origin;
      const absoluteUrl = `${baseUrl}${photoUrl}`;
      console.log('getPhotoUrl: Converting relative URL to absolute URL:', photoUrl, '->', absoluteUrl);
      photoUrl = absoluteUrl;
    }

    console.log('getPhotoUrl: Final determined URL:', photoUrl);
    return photoUrl;
  };

  // Lookup functions for reference data
  const lookupReligionName = (id: number | string | null | undefined) => {
    console.log('Looking up religion name for ID:', id, 'Type:', typeof id);
    console.log('Available religions:', religions);

    if (id === null || id === undefined) {
      console.log('Religion ID is null or undefined, returning "Not specified"');
      return 'Not specified';
    }

    // If it's already a string that's not a number, return it
    if (typeof id === 'string' && isNaN(Number(id))) {
      console.log('Religion ID is a non-numeric string, returning as is:', id);
      return id;
    }

    // Convert to number if it's a numeric string
    const numericId = typeof id === 'string' ? Number(id) : id;
    console.log('Converted religion ID to numeric:', numericId);

    // Try to find the corresponding religion
    if (Array.isArray(religions) && religions.length > 0) {
      console.log('Searching for religion with ID:', numericId);
      const religion = religions.find(r => r.id === numericId);
      if (religion) {
        console.log('Found religion:', religion);
        return religion.name;
      }
      console.log('Religion not found in the array');
    } else {
      console.log('Religions array is empty or not an array');
    }

    // Fallback
    console.log('Using fallback for religion ID:', id);
    return `Religion ${id}`;
  };

  const lookupMaritalStatusName = (id: number | string | null | undefined) => {
    if (id === null || id === undefined) return 'Not specified';

    // If it's already a string that's not a number, return it
    if (typeof id === 'string' && isNaN(Number(id))) {
      return id;
    }

    // Convert to number if it's a numeric string
    const numericId = typeof id === 'string' ? Number(id) : id;

    // Try to find the corresponding marital status
    if (Array.isArray(maritalStatuses) && maritalStatuses.length > 0) {
      const status = maritalStatuses.find(s => s.id === numericId);
      if (status) return status.name;
    }

    // Fallback
    return `Marital Status ${id}`;
  };

  const lookupCitizenStatusName = (id: number | string | null | undefined) => {
    if (id === null || id === undefined) return 'Not specified';

    // If it's already a string that's not a number, return it
    if (typeof id === 'string' && isNaN(Number(id))) {
      return id;
    }

    // Convert to number if it's a numeric string
    const numericId = typeof id === 'string' ? Number(id) : id;

    // Try to find the corresponding citizen status
    if (Array.isArray(citizenStatuses) && citizenStatuses.length > 0) {
      const status = citizenStatuses.find(s => s.id === numericId);
      if (status) return status.name;
    }

    // Fallback
    return `Citizen Status ${id}`;
  };

  const lookupEmploymentTypeName = (id: number | string | null | undefined) => {
    if (id === null || id === undefined) return 'Not specified';

    // If it's already a string that's not a number, return it
    if (typeof id === 'string' && isNaN(Number(id))) {
      return id;
    }

    // Convert to number if it's a numeric string
    const numericId = typeof id === 'string' ? Number(id) : id;

    // Try to find the corresponding employment type
    if (Array.isArray(employmentTypes) && employmentTypes.length > 0) {
      const type = employmentTypes.find(t => t.id === numericId);
      if (type) return type.name;
    }

    // Fallback
    return `Employment Type ${id}`;
  };

  const lookupRegionName = (id: number | string | null | undefined) => {
    console.log('Looking up region name for ID:', id, 'Type:', typeof id);
    console.log('Available regions:', regions);

    if (id === null || id === undefined) {
      console.log('Region ID is null or undefined, returning "Not specified"');
      return 'Not specified';
    }

    // If it's already a string that's not a number, return it
    if (typeof id === 'string' && isNaN(Number(id))) {
      console.log('Region ID is a non-numeric string, returning as is:', id);
      return id;
    }

    // Convert to number if it's a numeric string
    const numericId = typeof id === 'string' ? Number(id) : id;
    console.log('Converted region ID to numeric:', numericId);

    // Try to find the corresponding region
    if (Array.isArray(regions) && regions.length > 0) {
      console.log('Searching for region with ID:', numericId);
      const region = regions.find(r => r.id === numericId);
      if (region) {
        console.log('Found region:', region);
        return region.name;
      }
      console.log('Region not found in the array');
    } else {
      console.log('Regions array is empty or not an array');
    }

    // Fallback
    console.log('Using fallback for region ID:', id);
    return `Region ${id}`;
  };

  const lookupSubcityName = (id: number | string | null | undefined) => {
    if (id === null || id === undefined) return 'Not specified';

    // If it's already a string that's not a number, return it
    if (typeof id === 'string' && isNaN(Number(id))) {
      return id;
    }

    // Convert to number if it's a numeric string
    const numericId = typeof id === 'string' ? Number(id) : id;

    // Try to find the corresponding subcity
    if (Array.isArray(subcities) && subcities.length > 0) {
      const subcity = subcities.find(s => s.id === numericId);
      if (subcity) return subcity.name;
    }

    // Fallback
    return `Subcity ${id}`;
  };

  const lookupKetenaName = (id: number | string | null | undefined) => {
    console.log('Looking up ketena name for ID:', id, 'Type:', typeof id);
    console.log('Available ketenas:', ketenas);

    if (id === null || id === undefined) {
      console.log('Ketena ID is null or undefined, returning "Not specified"');
      return 'Not specified';
    }

    // If it's already a string that's not a number, return it
    if (typeof id === 'string' && isNaN(Number(id))) {
      console.log('Ketena ID is a non-numeric string, returning as is:', id);
      return id;
    }

    // Convert to number if it's a numeric string
    const numericId = typeof id === 'string' ? Number(id) : id;
    console.log('Converted ketena ID to numeric:', numericId);

    // Try to find the corresponding ketena
    if (Array.isArray(ketenas) && ketenas.length > 0) {
      console.log('Searching for ketena with ID:', numericId);
      const ketena = ketenas.find(k => k.id === numericId);
      if (ketena) {
        console.log('Found ketena:', ketena);
        return ketena.name;
      }
      console.log('Ketena not found in the array');
    } else {
      console.log('Ketenas array is empty or not an array');
    }

    // If not found but within valid range, format it
    if (typeof numericId === 'number' && numericId > 0 && numericId <= 3) {
      const formattedName = `Ketena ${String(numericId).padStart(2, '0')}`;
      console.log('Using formatted ketena name for valid range:', formattedName);
      return formattedName;
    }

    // Fallback
    console.log('Using fallback for ketena ID:', id);
    return `Ketena ${id}`;
  };

  // We're using the existing helper functions defined earlier in the file

  // Function to enrich citizen data with reference names
  const enrichCitizenData = (data: any) => {
    if (!data) {
      console.log('No data to enrich');
      return;
    }

    console.log('Enriching citizen data with reference names');

    // Look up reference data names
    const enrichedData = {
      ...data,
      // Convert reference IDs to names
      religion_name: lookupReligionName(data.religion),
      region_name: lookupRegionName(data.region),
      marital_status_name: lookupMaritalStatusName(data.marital_status),
      citizen_status_name: lookupCitizenStatusName(data.citizen_status),
      employment_type_name: lookupEmploymentTypeName(data.employment_type),
      ketena_name: lookupKetenaName(data.ketena),
      subcity_name: lookupSubcityName(data.subcity),
    };

    console.log('Enriched citizen data:', enrichedData);
    setCitizen(enrichedData);

    // Fetch the photo URL directly
    if (data.id) {
      console.log('Fetching photo URL for citizen ID:', data.id);
      fetchPhotoUrl(data.id).then(photoUrl => {
        if (photoUrl) {
          console.log('Successfully fetched photo URL:', photoUrl);
          // Update the citizen data with the photo URL
          setCitizen(prevCitizen => ({
            ...prevCitizen,
            photo: {
              ...prevCitizen.photo,
              photo_url: photoUrl
            }
          }));
        }
      });
    }
  };

  // Re-enrich citizen data whenever reference data changes
  useEffect(() => {
    if (rawCitizenDataRef.current) {
      console.log('Reference data changed, re-enriching citizen data');
      enrichCitizenData(rawCitizenDataRef.current);
    }
  }, [religions, regions, maritalStatuses, citizenStatuses, employmentTypes, ketenas, subcities]);

  // Log reference data whenever it changes
  useEffect(() => {
    console.log('Reference data updated:');
    console.log('Religions:', religions);
    console.log('Regions:', regions);
    console.log('Marital Statuses:', maritalStatuses);
    console.log('Citizen Statuses:', citizenStatuses);
    console.log('Employment Types:', employmentTypes);
    console.log('Ketenas:', ketenas);
    console.log('Subcities:', subcities);
  }, [religions, regions, maritalStatuses, citizenStatuses, employmentTypes, ketenas, subcities]);

  // Log the citizen data for debugging
  useEffect(() => {
    if (citizen) {
      console.log('%c CITIZEN DATA DEBUGGING', 'background: #ff0000; color: white; font-size: 20px;');
      console.log('Current citizen data in state:', citizen);
      console.log('Citizen data type:', typeof citizen);
      console.log('Citizen data keys:', Object.keys(citizen));

      // Check for missing data
      const missingFields = [];
      if (!citizen.first_name) missingFields.push('first_name');
      if (!citizen.last_name) missingFields.push('last_name');
      if (!citizen.gender) missingFields.push('gender');
      if (!citizen.date_of_birth) missingFields.push('date_of_birth');
      if (!citizen.id_number) missingFields.push('id_number');
      if (!citizen.spouses) missingFields.push('spouses');
      if (!citizen.parents) missingFields.push('parents');
      if (!citizen.children) missingFields.push('children');
      if (!citizen.emergency_contacts) missingFields.push('emergency_contacts');
      if (!citizen.documents) missingFields.push('documents');
      if (!citizen.id_cards) missingFields.push('id_cards');

      if (missingFields.length > 0) {
        console.warn('Missing citizen data fields:', missingFields);
      }

      // Check for fields that might be IDs instead of display values
      console.log('Religion:', citizen.religion);
      console.log('Marital Status:', citizen.marital_status);
      console.log('Citizen Status:', citizen.citizen_status);
      console.log('Employment Type:', citizen.employment_type);

      // Log photo information
      console.log('%c PHOTO DEBUGGING', 'background: #0000ff; color: white; font-size: 20px;');
      console.log('Photo object:', citizen.photo);
      console.log('Photo_obj:', citizen.photo_obj);

      // Try to access photo directly from the raw data
      if (rawCitizenDataRef.current) {
        console.log('Raw citizen data photo:', rawCitizenDataRef.current.photo);
        console.log('Raw citizen data photo_obj:', rawCitizenDataRef.current.photo_obj);
      }

      // Check all possible photo-related fields
      for (const key of Object.keys(citizen)) {
        if (key.toLowerCase().includes('photo')) {
          console.log(`Found photo-related field: ${key}`, citizen[key]);
        }
      }

      if (citizen.photo) {
        console.log('Photo URL:', citizen.photo.photo_url);
        console.log('Photo object type:', typeof citizen.photo);
        console.log('Photo object keys:', Object.keys(citizen.photo));

        // Check if photo is a string URL directly
        if (typeof citizen.photo === 'string') {
          console.log('Photo is a direct string URL');
        }
      } else {
        console.log('No photo found for this citizen');
        // Check if photo_obj exists instead
        if (citizen.photo_obj) {
          console.log('Found photo_obj instead:', citizen.photo_obj);
          console.log('Photo URL from photo_obj:', citizen.photo_obj.photo_url);
        }
      }
    }
  }, [citizen]);

  // If tenant is not authorized, show unauthorized access component
  if (!isTenantAuthorized) {
    return (
      <UnauthorizedAccess
        message="Your tenant type does not have permission to view citizen details."
        tenantType={tenant?.type}
      />
    );
  }

  return (
    <Box sx={{ py: 4 }}>
      {/* Banner */}
      <PageBanner
        title="Citizen Details"
        subtitle="View and manage citizen information"
        icon={<PersonIcon sx={{ fontSize: 50, color: 'white' }} />}
        actionContent={
          <>
            <Box sx={{ textAlign: 'center' }}>
              <Typography variant="overline" sx={{ color: 'rgba(255,255,255,0.7)', fontWeight: 500, display: 'block' }}>
                VIEW
              </Typography>
              <Typography variant="body1" sx={{ color: 'white', fontWeight: 600 }}>
                Personal Info
              </Typography>
            </Box>
            <Box sx={{ textAlign: 'center' }}>
              <Typography variant="overline" sx={{ color: 'rgba(255,255,255,0.7)', fontWeight: 500, display: 'block' }}>
                MANAGE
              </Typography>
              <Typography variant="body1" sx={{ color: 'white', fontWeight: 600 }}>
                ID Cards
              </Typography>
            </Box>
            <Box sx={{ textAlign: 'center' }}>
              <Typography variant="overline" sx={{ color: 'rgba(255,255,255,0.7)', fontWeight: 500, display: 'block' }}>
                UPDATE
              </Typography>
              <Typography variant="body1" sx={{ color: 'white', fontWeight: 600 }}>
                Information
              </Typography>
            </Box>
          </>
        }
      />

      <Container maxWidth="lg">
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            mb: 4,
            mt: 1,
            backgroundColor: 'rgba(255,255,255,0.8)',
            borderRadius: 3,
            p: 1.5,
            boxShadow: '0 4px 20px rgba(0,0,0,0.08)'
          }}
        >
          <Button
            startIcon={<ArrowBackIcon />}
            onClick={handleBackToList}
            variant="outlined"
            sx={{
              borderRadius: 2,
              px: 3,
              py: 1.2,
              boxShadow: '0 2px 8px rgba(0,0,0,0.05)',
              borderColor: '#0066b2',
              color: '#0066b2',
              fontWeight: 600,
              '&:hover': {
                boxShadow: '0 4px 12px rgba(0,0,0,0.1)',
                backgroundColor: 'rgba(0, 102, 178, 0.04)',
                borderColor: '#0077cc'
              }
            }}
          >
            Back to Citizens
          </Button>

          {/* Right side of the navigation bar - intentionally left empty to remove duplicate buttons */}
        </Box>

        {/* Error message */}
        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}

        {/* Conditional rendering based on loading and citizen state */}
        {loading && (
          <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', py: 8 }}>
            <CircularProgress size={60} />
            <Typography variant="h6" color="text.secondary" sx={{ mt: 3 }}>
              Loading citizen details...
            </Typography>
          </Box>
        )}

        {!loading && citizen && (
          <Card
            sx={{
              borderRadius: 3,
              overflow: 'hidden',
              boxShadow: '0 8px 24px rgba(0,0,0,0.12)',
              border: '1px solid rgba(0, 0, 0, 0.05)',
              position: 'relative',
              background: 'linear-gradient(to bottom, #ffffff 0%, #fafafa 100%)',
              mb: 4
            }}
          >
            {/* Header with photo and basic info */}
            <Box sx={{ p: 4, display: 'flex', flexDirection: { xs: 'column', md: 'row' }, alignItems: 'center', bgcolor: '#f9f9f9' }}>
              <Box
                sx={{
                  position: 'relative',
                  mr: { xs: 0, md: 4 },
                  mb: { xs: 2, md: 0 },
                  '&:hover .photo-overlay': {
                    opacity: 1
                  }
                }}
              >
                {/* Use the getPhotoUrl utility function with direct URL access */}
                {(() => {
                  // Try to get the photo URL directly from the API response
                  let photoUrl = null;

                  // First try with our utility function
                  photoUrl = getPhotoUrl(citizen);
                  console.log('Avatar: Photo URL from utility function:', photoUrl);

                  // If that fails, try with raw data
                  if (!photoUrl && rawCitizenDataRef.current) {
                    photoUrl = getPhotoUrl(rawCitizenDataRef.current);
                    console.log('Avatar: Photo URL from raw data:', photoUrl);
                  }

                  // If that still fails, try direct access to photo field
                  if (!photoUrl && citizen.photo) {
                    if (typeof citizen.photo === 'string') {
                      photoUrl = citizen.photo;
                      console.log('Avatar: Using direct string photo:', photoUrl);
                    } else if (typeof citizen.photo === 'object') {
                      // Try all possible properties
                      const possibleProps = ['photo_url', 'url', 'photo', 'file', 'src', 'path'];
                      for (const prop of possibleProps) {
                        if (citizen.photo[prop]) {
                          photoUrl = citizen.photo[prop];
                          console.log(`Avatar: Found photo URL in citizen.photo.${prop}:`, photoUrl);
                          break;
                        }
                      }
                    }
                  }

                  // If still no URL, try to use the citizenPhotoUrl from our direct fetch
                  if (!photoUrl && (window as any).citizenPhotoUrl) {
                    console.log('Avatar: Using direct fetch photo URL:', (window as any).citizenPhotoUrl);
                    photoUrl = (window as any).citizenPhotoUrl;
                  }
                  // We're not using confirmedPhotoUrl anymore since the HEAD check doesn't work
                  // We'll skip trying to construct a URL since we know it's returning 404 errors
                  // else if (!photoUrl && citizen.id) {
                  //   // Try to construct a URL based on the API structure
                  //   const baseUrl = 'http://localhost:8000';
                  //   const schema = encodeURIComponent(localStorage.getItem('schema_name') || '');
                  //   const constructedUrl = `${baseUrl}/api/tenant/${schema}/citizens/${citizen.id}/photo/`;
                  //   console.log('Avatar: Constructed photo URL:', constructedUrl);
                  //   photoUrl = constructedUrl;
                  // }

                  // If we still don't have a photo URL, use a hardcoded test image URL
                  if (!photoUrl) {
                    photoUrl = 'https://www.gravatar.com/avatar/00000000000000000000000000000000?d=mp&f=y';
                    console.log('Avatar: Using hardcoded test image URL as last resort:', photoUrl);
                  }

                  console.log('Avatar: Final photo URL to display:', photoUrl);

                  return (
                    <Avatar
                      src={photoUrl}
                      alt={`${citizen.first_name} ${citizen.last_name}`}
                      sx={{
                        width: { xs: 120, md: 150 },
                        height: { xs: 120, md: 150 },
                        boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
                        border: '4px solid white',
                        cursor: photoUrl ? 'pointer' : 'default'
                      }}
                      onClick={() => photoUrl && window.open(photoUrl, '_blank')}
                    >
                      {citizen.first_name?.[0]}
                    </Avatar>
                  );
                })()}
                {(() => {
                  // Try to get the photo URL using the same approach as the Avatar
                  let photoUrl = null;

                  // First try with our utility function
                  photoUrl = getPhotoUrl(citizen);

                  // If that fails, try with raw data
                  if (!photoUrl && rawCitizenDataRef.current) {
                    photoUrl = getPhotoUrl(rawCitizenDataRef.current);
                  }

                  // If that still fails, try direct access to photo field
                  if (!photoUrl && citizen.photo) {
                    if (typeof citizen.photo === 'string') {
                      photoUrl = citizen.photo;
                    } else if (typeof citizen.photo === 'object') {
                      // Try all possible properties
                      const possibleProps = ['photo_url', 'url', 'photo', 'file', 'src', 'path'];
                      for (const prop of possibleProps) {
                        if (citizen.photo[prop]) {
                          photoUrl = citizen.photo[prop];
                          break;
                        }
                      }
                    }
                  }

                  // If still no URL, try to use the citizenPhotoUrl from our direct fetch
                  if (!photoUrl && (window as any).citizenPhotoUrl) {
                    photoUrl = (window as any).citizenPhotoUrl;
                  }
                  // We're not using confirmedPhotoUrl anymore since the HEAD check doesn't work
                  // We'll skip trying to construct a URL since we know it's returning 404 errors
                  // else if (!photoUrl && citizen.id) {
                  //   // Try to construct a URL based on the API structure
                  //   const baseUrl = 'http://localhost:8000';
                  //   const schema = encodeURIComponent(localStorage.getItem('schema_name') || '');
                  //   const constructedUrl = `${baseUrl}/api/tenant/${schema}/citizens/${citizen.id}/photo/`;
                  //   photoUrl = constructedUrl;
                  // }

                  return photoUrl && (
                  <Box
                    className="photo-overlay"
                    sx={{
                      position: 'absolute',
                      top: 0,
                      left: 0,
                      right: 0,
                      bottom: 0,
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      backgroundColor: 'rgba(0, 0, 0, 0.5)',
                      borderRadius: '50%',
                      opacity: 0,
                      transition: 'opacity 0.3s ease',
                      cursor: 'pointer'
                    }}
                    onClick={() => {
                      // Try to get the photo URL using the same approach as the Avatar
                      let photoUrl = null;

                      // First try with our utility function
                      photoUrl = getPhotoUrl(citizen);

                      // If that fails, try with raw data
                      if (!photoUrl && rawCitizenDataRef.current) {
                        photoUrl = getPhotoUrl(rawCitizenDataRef.current);
                      }

                      // If that still fails, try direct access to photo field
                      if (!photoUrl && citizen.photo) {
                        if (typeof citizen.photo === 'string') {
                          photoUrl = citizen.photo;
                        } else if (typeof citizen.photo === 'object') {
                          // Try all possible properties
                          const possibleProps = ['photo_url', 'url', 'photo', 'file', 'src', 'path'];
                          for (const prop of possibleProps) {
                            if (citizen.photo[prop]) {
                              photoUrl = citizen.photo[prop];
                              break;
                            }
                          }
                        }
                      }

                      // If still no URL, try to use the citizenPhotoUrl from our direct fetch
                      if (!photoUrl && (window as any).citizenPhotoUrl) {
                        console.log('Overlay click: Using direct fetch photo URL:', (window as any).citizenPhotoUrl);
                        photoUrl = (window as any).citizenPhotoUrl;
                      }
                      // We're not using confirmedPhotoUrl anymore since the HEAD check doesn't work
                      // We'll skip trying to construct a URL since we know it's returning 404 errors
                      // else if (!photoUrl && citizen.id) {
                      //   // Try to construct a URL based on the API structure
                      //   const baseUrl = 'http://localhost:8000';
                      //   const schema = encodeURIComponent(localStorage.getItem('schema_name') || '');
                      //   const constructedUrl = `${baseUrl}/api/tenant/${schema}/citizens/${citizen.id}/photo/`;
                      //   photoUrl = constructedUrl;
                      // }

                      // If we still don't have a photo URL, use a hardcoded test image URL
                      if (!photoUrl) {
                        photoUrl = 'https://www.gravatar.com/avatar/00000000000000000000000000000000?d=mp&f=y';
                        console.log('Overlay click: Using hardcoded test image URL as last resort:', photoUrl);
                      }

                      console.log('Overlay click: Opening photo URL:', photoUrl);
                      if (photoUrl) window.open(photoUrl, '_blank');
                    }}
                  >
                    <Tooltip title="View full photo">
                      <ZoomInIcon sx={{ color: 'white', fontSize: 40 }} />
                    </Tooltip>
                  </Box>
                  );
                })()}
              </Box>

              <Box sx={{ textAlign: { xs: 'center', md: 'left' }, flex: 1 }}>
                <Typography variant="h4" sx={{ fontWeight: 700, mb: 0.5 }}>
                  {`${citizen.first_name} ${citizen.middle_name || ''} ${citizen.last_name}`}
                </Typography>

                {(citizen.first_name_am || citizen.middle_name_am || citizen.last_name_am) && (
                  <Typography variant="body1" color="text.secondary" sx={{ mb: 1 }}>
                    {`${citizen.first_name_am || ''} ${citizen.middle_name_am || ''} ${citizen.last_name_am || ''}`}
                  </Typography>
                )}

                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mt: 1, justifyContent: { xs: 'center', md: 'flex-start' } }}>
                  {/* Success message */}
                  {idGenerationSuccess && (
                    <Alert severity="success" sx={{ mb: 2, width: '100%' }}>
                      ID number generated successfully!
                    </Alert>
                  )}

                  {/* Error message */}
                  {idGenerationError && (
                    <Alert severity="error" sx={{ mb: 2, width: '100%' }}>
                      {idGenerationError}
                    </Alert>
                  )}
                  {citizen.id_number ? (
                    <Chip
                      icon={<BadgeIcon />}
                      label={citizen.id_number}
                      color="primary"
                      sx={{ fontWeight: 600 }}
                    />
                  ) : (
                    <Button
                      variant="contained"
                      color="primary"
                      size="small"
                      startIcon={generatingId ? null : <BadgeIcon />}
                      onClick={handleGenerateID}
                      disabled={generatingId}
                      sx={{ fontWeight: 600, borderRadius: 2 }}
                    >
                      {generatingId ? 'Generating...' : 'Generate ID Number'}
                    </Button>
                  )}

                  {citizen.digital_id && (
                    <Chip
                      icon={<FingerprintIcon />}
                      label={`Digital ID: ${citizen.digital_id}`}
                      variant="outlined"
                      color="primary"
                    />
                  )}

                  {citizen.citizen_status && (
                    <Chip
                      icon={<VerifiedUserIcon />}
                      label={getCitizenStatusName(citizen.citizen_status)}
                      color={getCitizenStatusName(citizen.citizen_status).toLowerCase() === 'resident' ? 'success' : 'default'}
                      variant="outlined"
                    />
                  )}
                </Box>
              </Box>

              <Box sx={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: { xs: 'center', md: 'flex-end' },
                mt: { xs: 2, md: 0 },
                minWidth: { md: '200px' }
              }}>
                <Button
                  startIcon={<CreditCardIcon />}
                  variant="contained"
                  color="primary"
                  onClick={handleRegisterIDCard}
                  sx={{
                    borderRadius: 2,
                    px: 3,
                    py: 1.2,
                    fontWeight: 600,
                    mb: 1,
                    width: { xs: '100%', md: 'auto' }
                  }}
                >
                  Register ID Card
                </Button>

                <Button
                  startIcon={<EditIcon />}
                  variant="outlined"
                  onClick={handleEditCitizen}
                  sx={{
                    borderRadius: 2,
                    px: 3,
                    py: 1.2,
                    fontWeight: 600,
                    width: { xs: '100%', md: 'auto' }
                  }}
                >
                  Edit Citizen
                </Button>
              </Box>
            </Box>

            {/* Main content with tabs */}
            <Box sx={{ p: 0 }}>
              <Tabs
                value={tabValue}
                onChange={handleTabChange}
                variant="scrollable"
                scrollButtons="auto"
                sx={{
                  borderBottom: 1,
                  borderColor: 'divider',
                  bgcolor: 'white',
                  '& .MuiTab-root': {
                    py: 2,
                    px: 3,
                    fontWeight: 600,
                    textTransform: 'none',
                    fontSize: '0.95rem',
                    minHeight: '64px',
                    transition: 'all 0.2s ease-in-out',
                    '&.Mui-selected': {
                      color: 'primary.main',
                      fontWeight: 700,
                      backgroundColor: 'rgba(0, 102, 178, 0.04)'
                    }
                  }
                }}
                TabIndicatorProps={{
                  style: {
                    height: 3,
                    borderTopLeftRadius: 3,
                    borderTopRightRadius: 3,
                    backgroundColor: '#0066b2'
                  }
                }}
              >
                <Tab
                  icon={<PersonIcon sx={{ mr: 1 }} />}
                  iconPosition="start"
                  label="Personal Info"
                />
                <Tab
                  icon={<HomeIcon sx={{ mr: 1 }} />}
                  iconPosition="start"
                  label="Contact & Location"
                />
                <Tab
                  icon={<FamilyRestroomIcon sx={{ mr: 1 }} />}
                  iconPosition="start"
                  label="Family"
                />
                <Tab
                  icon={<WorkIcon sx={{ mr: 1 }} />}
                  iconPosition="start"
                  label="Employment"
                />
                <Tab
                  icon={<DescriptionIcon sx={{ mr: 1 }} />}
                  iconPosition="start"
                  label="Documents"
                />
                <Tab
                  icon={<CreditCardIcon sx={{ mr: 1 }} />}
                  iconPosition="start"
                  label="ID Cards"
                />

              </Tabs>

              {/* Tab Panel 0: Personal Information */}
              <TabPanel value={tabValue} index={0}>


                <Grid container spacing={3}>
                  <Grid item xs={12} md={6}>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                      <WcIcon sx={{ color: 'primary.main', mr: 2 }} />
                      <Box>
                        <Typography variant="body2" color="text.secondary">
                          Gender
                        </Typography>
                        <Typography variant="body1" sx={{ fontWeight: 600 }}>
                          {citizen.gender === 'M' ? 'Male' : 'Female'}
                        </Typography>
                      </Box>
                    </Box>

                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                      <CalendarTodayIcon sx={{ color: 'primary.main', mr: 2 }} />
                      <Box>
                        <Typography variant="body2" color="text.secondary">
                          Date of Birth
                        </Typography>
                        <Typography variant="body1" sx={{ fontWeight: 600 }}>
                          {formatDate(citizen.date_of_birth)} ({calculateAge(citizen.date_of_birth)} years)
                        </Typography>
                      </Box>
                    </Box>

                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                      <FamilyRestroomIcon sx={{ color: 'primary.main', mr: 2 }} />
                      <Box>
                        <Typography variant="body2" color="text.secondary">
                          Marital Status
                        </Typography>
                        <Typography variant="body1" sx={{ fontWeight: 600 }}>
                          {/* Display the marital status name using the helper function */}
                          {getMaritalStatusName(citizen.marital_status)}
                        </Typography>
                      </Box>
                    </Box>
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                      <LanguageIcon sx={{ color: 'primary.main', mr: 2 }} />
                      <Box>
                        <Typography variant="body2" color="text.secondary">
                          Nationality
                        </Typography>
                        <Typography variant="body1" sx={{ fontWeight: 600 }}>
                          {citizen.nationality || 'Ethiopian'}
                        </Typography>
                      </Box>
                    </Box>

                    {citizen.religion && (
                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                        <InfoIcon sx={{ color: 'primary.main', mr: 2 }} />
                        <Box>
                          <Typography variant="body2" color="text.secondary">
                            Religion
                          </Typography>
                          <Typography variant="body1" sx={{ fontWeight: 600 }}>
                            {/* Display the religion name using the helper function */}
                            {getReligionName(citizen.religion)}
                          </Typography>
                        </Box>
                      </Box>
                    )}

                    {citizen.registration_number && (
                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                        <BadgeIcon sx={{ color: 'primary.main', mr: 2 }} />
                        <Box>
                          <Typography variant="body2" color="text.secondary">
                            Registration Number
                          </Typography>
                          <Typography variant="body1" sx={{ fontWeight: 600 }}>
                            {citizen.registration_number}
                          </Typography>
                        </Box>
                      </Box>
                    )}
                  </Grid>
                </Grid>
              </TabPanel>

              {/* Tab Panel 1: Contact & Location */}
              <TabPanel value={tabValue} index={1}>
                <Grid container spacing={3}>
                  <Grid item xs={12} md={6}>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                      <PhoneIcon sx={{ color: 'secondary.main', mr: 2 }} />
                      <Box>
                        <Typography variant="body2" color="text.secondary">
                          Phone Number
                        </Typography>
                        <Typography variant="body1" sx={{ fontWeight: 600 }}>
                          {citizen.phone || 'Not provided'}
                        </Typography>
                      </Box>
                    </Box>

                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                      <EmailIcon sx={{ color: 'secondary.main', mr: 2 }} />
                      <Box>
                        <Typography variant="body2" color="text.secondary">
                          Email Address
                        </Typography>
                        <Typography variant="body1" sx={{ fontWeight: 600 }}>
                          {citizen.email || 'Not provided'}
                        </Typography>
                      </Box>
                    </Box>
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <Box sx={{ display: 'flex', alignItems: 'flex-start', mb: 3 }}>
                      <HomeIcon sx={{ color: 'secondary.main', mr: 2, mt: 0.5 }} />
                      <Box>
                        <Typography variant="body2" color="text.secondary">
                          Address
                        </Typography>
                        <Typography variant="body1" sx={{ fontWeight: 600 }}>
                          {citizen.address || 'Not provided'}
                        </Typography>
                        {citizen.house_number && (
                          <Typography variant="body2" sx={{ mt: 0.5 }}>
                            House Number: <span style={{ fontWeight: 600 }}>{citizen.house_number}</span>
                          </Typography>
                        )}
                      </Box>
                    </Box>
                  </Grid>

                  <Grid item xs={12}>
                    <Box sx={{ mt: 3, mb: 2 }}>
                      <Typography variant="h6" sx={{ fontWeight: 600, display: 'flex', alignItems: 'center', mb: 2 }}>
                        <LocationOnIcon sx={{ mr: 1, color: 'secondary.main' }} />
                        Location Information
                      </Typography>
                      <Divider sx={{ mb: 3 }} />
                    </Box>

                    <Box sx={{
                      p: 3,
                      borderRadius: 3,
                      bgcolor: 'white',
                      boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
                      position: 'relative',
                      overflow: 'hidden',
                      '&::before': {
                        content: '""',
                        position: 'absolute',
                        top: 0,
                        left: 0,
                        width: '100%',
                        height: '4px',
                        background: 'linear-gradient(to right, #9c27b0, #7b1fa2, #9c27b0)'
                      }
                    }}>
                      <Grid container spacing={3}>
                        {citizen.region && (
                          <Grid item xs={12} sm={6} md={3}>
                            <Box sx={{ textAlign: 'center' }}>
                              <Avatar sx={{ bgcolor: 'secondary.light', width: 56, height: 56, mx: 'auto', mb: 1 }}>
                                <LocationOnIcon />
                              </Avatar>
                              <Typography variant="body2" color="text.secondary" gutterBottom>
                                Region
                              </Typography>
                              <Typography variant="h6" sx={{ fontWeight: 600 }}>
                                {/* Display the region name using the helper function */}
                                {getRegionName(citizen.region)}
                              </Typography>
                            </Box>
                          </Grid>
                        )}

                        {citizen.subcity && (
                          <Grid item xs={12} sm={6} md={3}>
                            <Box sx={{ textAlign: 'center' }}>
                              <Avatar sx={{ bgcolor: 'secondary.light', width: 56, height: 56, mx: 'auto', mb: 1 }}>
                                <LocationCityIcon />
                              </Avatar>
                              <Typography variant="body2" color="text.secondary" gutterBottom>
                                Subcity
                              </Typography>
                              <Typography variant="h6" sx={{ fontWeight: 600 }}>
                                {/* Use parent tenant for subcity with fallback to hardcoded values */}
                                {(() => {
                                  // First priority: use parent tenant name if available
                                  if (tenant && tenant.parent && tenant.parent.name) {
                                    console.log('Using parent tenant name for subcity:', tenant.parent.name);
                                    return tenant.parent.name;
                                  }

                                  // Second priority: use hardcoded value based on kebele number
                                  if (tenant && tenant.name) {
                                    console.log('Trying to extract kebele number from tenant name:', tenant.name);
                                    const kebeleMatch = tenant.name.match(/Kebele (\d+)/);
                                    if (kebeleMatch) {
                                      const kebeleNumber = Number(kebeleMatch[1]);
                                      console.log('Extracted kebele number:', kebeleNumber);

                                      // Map kebele numbers to subcities
                                      if (kebeleNumber >= 10 && kebeleNumber <= 19) {
                                        console.log('Mapping kebele to Zoble subcity');
                                        return 'Zoble';
                                      } else if (kebeleNumber >= 1 && kebeleNumber <= 9) {
                                        console.log('Mapping kebele to Azezo subcity');
                                        return 'Azezo';
                                      }
                                    }
                                  }

                                  // Third priority: use schema name to determine subcity
                                  if (schemaName) {
                                    console.log('Trying to extract kebele number from schema name:', schemaName);
                                    const schemaMatch = schemaName.match(/kebele[_ ](\d+)/);
                                    if (schemaMatch) {
                                      const kebeleNumber = Number(schemaMatch[1]);
                                      console.log('Extracted kebele number from schema:', kebeleNumber);

                                      // Map kebele numbers to subcities
                                      if (kebeleNumber >= 10 && kebeleNumber <= 19) {
                                        console.log('Mapping schema to Zoble subcity');
                                        return 'Zoble';
                                      } else if (kebeleNumber >= 1 && kebeleNumber <= 9) {
                                        console.log('Mapping schema to Azezo subcity');
                                        return 'Azezo';
                                      }
                                    }
                                  }

                                  // Fourth priority: use subcity from citizen data if available
                                  if (citizen.subcity) {
                                    console.log('Using subcity from citizen data:', citizen.subcity);
                                    return getSubcityName(citizen.subcity);
                                  }

                                  // Fallback
                                  console.log('No subcity information found, using default');
                                  return 'Zoble'; // Default to Zoble as a fallback
                                })()}
                              </Typography>
                            </Box>
                          </Grid>
                        )}

                        <Grid item xs={12} sm={6} md={3}>
                          <Box sx={{ textAlign: 'center' }}>
                            <Avatar sx={{ bgcolor: 'secondary.light', width: 56, height: 56, mx: 'auto', mb: 1 }}>
                              <HomeWorkIcon />
                            </Avatar>
                            <Typography variant="body2" color="text.secondary" gutterBottom>
                              Kebele
                            </Typography>
                            <Typography variant="h6" sx={{ fontWeight: 600 }}>
                              {/* Use the current tenant name for kebele */}
                              {tenant ? tenant.name : (citizen.center_name || 'Not specified')}
                            </Typography>
                          </Box>
                        </Grid>

                        {citizen.ketena && (
                          <Grid item xs={12} sm={6} md={3}>
                            <Box sx={{ textAlign: 'center' }}>
                              <Avatar sx={{ bgcolor: 'secondary.light', width: 56, height: 56, mx: 'auto', mb: 1 }}>
                                <ApartmentIcon />
                              </Avatar>
                              <Typography variant="body2" color="text.secondary" gutterBottom>
                                Ketena
                              </Typography>
                              <Typography variant="h6" sx={{ fontWeight: 600 }}>
                                {/* Display the ketena name using the helper function */}
                                {getKetenaName(citizen.ketena)}
                              </Typography>
                            </Box>
                          </Grid>
                        )}
                      </Grid>
                    </Box>
                  </Grid>
                </Grid>
              </TabPanel>

              {/* Tab Panel 2: Family Information */}
              <TabPanel value={tabValue} index={2}>
                <Grid container spacing={3}>
                  {/* Spouse Information */}
                  <Grid item xs={12}>
                    <Divider sx={{ my: 2 }}>
                      <Chip icon={<PeopleIcon />} label="Spouse Information" color="success" />
                    </Divider>

                    {citizen.spouses && Array.isArray(citizen.spouses) && citizen.spouses.length > 0 ? (
                      <Grid container spacing={2}>
                        {citizen.spouses.map((spouse: any) => (
                          <Grid item xs={12} md={6} key={spouse.id}>
                            <Paper sx={{
                              p: 2,
                              borderRadius: 2,
                              boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
                              position: 'relative',
                              overflow: 'hidden',
                              '&::before': {
                                content: '""',
                                position: 'absolute',
                                top: 0,
                                left: 0,
                                width: '4px',
                                height: '100%',
                                bgcolor: 'success.main'
                              }
                            }}>
                              <Typography variant="h6" sx={{ fontWeight: 600, mb: 1 }}>
                                {`${spouse.first_name} ${spouse.middle_name || ''} ${spouse.last_name}`}
                              </Typography>

                              {spouse.phone && (
                                <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                                  <PhoneIcon sx={{ fontSize: 18, mr: 1, color: 'text.secondary' }} />
                                  <Typography variant="body2">{spouse.phone}</Typography>
                                </Box>
                              )}

                              {spouse.is_resident && (
                                <Chip
                                  icon={<CheckCircleIcon />}
                                  label="Resident of this Kebele"
                                  color="success"
                                  size="small"
                                  sx={{ mt: 2 }}
                                />
                              )}
                            </Paper>
                          </Grid>
                        ))}
                      </Grid>
                    ) : (
                      <Box sx={{ textAlign: 'center', py: 3, bgcolor: 'rgba(0,0,0,0.02)', borderRadius: 2 }}>
                        <PeopleIcon sx={{ fontSize: 40, color: 'text.disabled', mb: 1 }} />
                        <Typography variant="body1" color="text.secondary" gutterBottom>
                          No spouse information recorded.
                        </Typography>
                        <Typography variant="body2" color="text.secondary" sx={{ mb: 2, maxWidth: 400, mx: 'auto' }}>
                          {getMaritalStatusName(citizen.marital_status).toLowerCase() === 'married' ?
                            'This citizen is marked as married but no spouse information has been added yet.' :
                            'This citizen is not marked as married.'}
                        </Typography>
                        {getMaritalStatusName(citizen.marital_status).toLowerCase() === 'married' && (
                          <Button
                            variant="outlined"
                            size="small"
                            startIcon={<AddIcon />}
                            onClick={handleAddSpouse}
                            sx={{ mt: 1, borderRadius: 2 }}
                          >
                            Add Spouse Information
                          </Button>
                        )}
                      </Box>
                    )}
                  </Grid>

                  {/* Parents Information */}
                  <Grid item xs={12}>
                    <Divider sx={{ my: 2 }}>
                      <Chip icon={<ElderlyIcon />} label="Parents Information" color="success" />
                    </Divider>

                    {citizen.parents && Array.isArray(citizen.parents) && citizen.parents.length > 0 ? (
                      <Grid container spacing={2}>
                        {citizen.parents.map((parent: any) => (
                          <Grid item xs={12} md={6} key={parent.id}>
                            <Paper sx={{
                              p: 2,
                              borderRadius: 2,
                              boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
                              position: 'relative',
                              overflow: 'hidden',
                              '&::before': {
                                content: '""',
                                position: 'absolute',
                                top: 0,
                                left: 0,
                                width: '4px',
                                height: '100%',
                                bgcolor: parent.relationship_type === 'Mother' ? 'secondary.main' :
                                         parent.relationship_type === 'Father' ? 'info.main' : 'success.main'
                              }
                            }}>
                              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                                <Box sx={{ display: 'flex', flexDirection: 'column' }}>
                                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                    <Avatar
                                      sx={{
                                        width: 32,
                                        height: 32,
                                        mr: 1,
                                        bgcolor: parent.relationship_type === 'Mother' ? 'secondary.light' :
                                                 parent.relationship_type === 'Father' ? 'info.light' : 'success.light'
                                      }}
                                    >
                                      {parent.relationship_type === 'Mother' ?
                                        <PersonIcon fontSize="small" sx={{ transform: 'translateY(-2px)' }} /> :
                                        parent.relationship_type === 'Father' ?
                                          <PersonIcon fontSize="small" /> :
                                          <ElderlyIcon fontSize="small" />}
                                    </Avatar>
                                    <Typography variant="h6" sx={{ fontWeight: 600 }}>
                                      {`${parent.first_name} ${parent.middle_name || ''} ${parent.last_name}`}
                                    </Typography>
                                  </Box>
                                  <Box
                                    sx={{
                                      ml: 5,
                                      mt: 0.5,
                                      display: 'flex',
                                      alignItems: 'center',
                                      bgcolor: parent.relationship_type === 'Mother' ? 'secondary.light' :
                                               parent.relationship_type === 'Father' ? 'info.light' : 'grey.200',
                                      px: 1.5,
                                      py: 0.5,
                                      borderRadius: 1,
                                      display: 'inline-flex'
                                    }}
                                  >
                                    <Typography
                                      variant="h6"
                                      sx={{
                                        fontWeight: 600,
                                        color: parent.relationship_type === 'Mother' ? 'secondary.dark' :
                                               parent.relationship_type === 'Father' ? 'info.dark' : 'text.secondary',
                                      }}
                                    >
                                      {parent.relationship_type || 'Parent'}
                                    </Typography>
                                  </Box>
                                </Box>
                                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                  {parent.is_resident && (
                                    <Chip
                                      icon={<CheckCircleIcon />}
                                      label="Resident"
                                      color="success"
                                      size="small"
                                      sx={{ mr: 1 }}
                                    />
                                  )}
                                </Box>
                              </Box>

                              {/* Resident chip moved to header */}
                            </Paper>
                          </Grid>
                        ))}
                      </Grid>
                    ) : (
                      <Box sx={{ textAlign: 'center', py: 3, bgcolor: 'rgba(0,0,0,0.02)', borderRadius: 2 }}>
                        <ElderlyIcon sx={{ fontSize: 40, color: 'text.disabled', mb: 1 }} />
                        <Typography variant="body1" color="text.secondary" gutterBottom>
                          No parent information recorded.
                        </Typography>
                        <Typography variant="body2" color="text.secondary" sx={{ mb: 2, maxWidth: 400, mx: 'auto' }}>
                          Adding parent information helps establish family relationships and can be useful for verification purposes.
                        </Typography>
                        <Button
                          variant="outlined"
                          size="small"
                          startIcon={<AddIcon />}
                          onClick={handleAddParent}
                          sx={{ mt: 1, borderRadius: 2 }}
                        >
                          Add Parent Information
                        </Button>
                      </Box>
                    )}
                  </Grid>

                  {/* Children Information */}
                  <Grid item xs={12}>
                    <Divider sx={{ my: 2 }}>
                      <Chip icon={<ChildCareIcon />} label="Children Information" color="success" />
                    </Divider>

                    {citizen.children && Array.isArray(citizen.children) && citizen.children.length > 0 ? (
                      <Grid container spacing={2}>
                        {citizen.children.map((child: any) => (
                          <Grid item xs={12} sm={6} md={4} key={child.id}>
                            <Paper sx={{
                              p: 2,
                              borderRadius: 2,
                              boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
                              position: 'relative',
                              overflow: 'hidden',
                              '&::before': {
                                content: '""',
                                position: 'absolute',
                                top: 0,
                                left: 0,
                                width: '4px',
                                height: '100%',
                                bgcolor: 'success.main'
                              }
                            }}>
                              <Typography variant="h6" sx={{ fontWeight: 600, mb: 1 }}>
                                {`${child.first_name} ${child.middle_name || ''} ${child.last_name}`}
                              </Typography>

                              <Box sx={{ display: 'flex', gap: 2, mt: 1 }}>
                                <Chip
                                  label={`Age: ${calculateAge(child.date_of_birth)}`}
                                  size="small"
                                  variant="outlined"
                                />
                                <Chip
                                  label={child.gender === 'M' ? 'Male' : 'Female'}
                                  size="small"
                                  variant="outlined"
                                  color={child.gender === 'M' ? 'info' : 'secondary'}
                                />
                              </Box>

                              {child.is_resident && (
                                <Chip
                                  icon={<CheckCircleIcon />}
                                  label="Resident of this Kebele"
                                  color="success"
                                  size="small"
                                  sx={{ mt: 2 }}
                                />
                              )}
                            </Paper>
                          </Grid>
                        ))}
                      </Grid>
                    ) : (
                      <Box sx={{ textAlign: 'center', py: 3, bgcolor: 'rgba(0,0,0,0.02)', borderRadius: 2 }}>
                        <ChildCareIcon sx={{ fontSize: 40, color: 'text.disabled', mb: 1 }} />
                        <Typography variant="body1" color="text.secondary" gutterBottom>
                          No children information recorded.
                        </Typography>
                        <Typography variant="body2" color="text.secondary" sx={{ mb: 2, maxWidth: 400, mx: 'auto' }}>
                          {getMaritalStatusName(citizen.marital_status).toLowerCase() === 'single' ?
                            'This citizen is marked as single.' :
                            'You can add children information to establish family relationships.'}
                        </Typography>
                        {getMaritalStatusName(citizen.marital_status).toLowerCase() !== 'single' && (
                          <Button
                            variant="outlined"
                            size="small"
                            startIcon={<AddIcon />}
                            onClick={handleAddChild}
                            sx={{ mt: 1, borderRadius: 2 }}
                          >
                            Add Children Information
                          </Button>
                        )}
                      </Box>
                    )}
                  </Grid>

                  {/* Emergency Contacts */}
                  <Grid item xs={12}>
                    <Divider sx={{ my: 2 }}>
                      <Chip icon={<ContactPhoneIcon />} label="Emergency Contacts" color="error" />
                    </Divider>

                    {citizen.emergency_contacts && Array.isArray(citizen.emergency_contacts) && citizen.emergency_contacts.length > 0 ? (
                      <Grid container spacing={2}>
                        {citizen.emergency_contacts.map((contact: any) => (
                          <Grid item xs={12} md={6} key={contact.id}>
                            <Paper sx={{
                              p: 2,
                              borderRadius: 2,
                              boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
                              position: 'relative',
                              overflow: 'hidden',
                              '&::before': {
                                content: '""',
                                position: 'absolute',
                                top: 0,
                                left: 0,
                                width: '4px',
                                height: '100%',
                                bgcolor: 'error.main'
                              }
                            }}>
                              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                                <Typography variant="h6" sx={{ fontWeight: 600 }}>
                                  {`${contact.first_name} ${contact.middle_name || ''} ${contact.last_name}`}
                                </Typography>
                                <Chip
                                  label={contact.relationship_type || 'Contact'}
                                  size="small"
                                  color="error"
                                  variant="outlined"
                                />
                              </Box>

                              {contact.phone && (
                                <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                                  <PhoneIcon sx={{ fontSize: 18, mr: 1, color: 'error.main' }} />
                                  <Typography variant="body2">{contact.phone}</Typography>
                                </Box>
                              )}

                              {contact.is_resident && (
                                <Chip
                                  icon={<CheckCircleIcon />}
                                  label="Resident of this Kebele"
                                  color="success"
                                  size="small"
                                  sx={{ mt: 2 }}
                                />
                              )}
                            </Paper>
                          </Grid>
                        ))}
                      </Grid>
                    ) : (
                      <Box sx={{ textAlign: 'center', py: 3, bgcolor: 'rgba(0,0,0,0.02)', borderRadius: 2 }}>
                        <ContactPhoneIcon sx={{ fontSize: 40, color: 'text.disabled', mb: 1 }} />
                        <Typography variant="body1" color="text.secondary" gutterBottom>
                          No emergency contacts recorded.
                        </Typography>
                        <Typography variant="body2" color="text.secondary" sx={{ mb: 2, maxWidth: 400, mx: 'auto' }}>
                          Emergency contacts are important for reaching out to someone in case of an emergency.
                        </Typography>
                        <Button
                          variant="outlined"
                          size="small"
                          startIcon={<AddIcon />}
                          onClick={handleAddEmergencyContact}
                          sx={{ mt: 1, borderRadius: 2 }}
                        >
                          Add Emergency Contact
                        </Button>
                      </Box>
                    )}
                  </Grid>
                </Grid>
              </TabPanel>

              {/* Tab Panel 3: Employment & Education */}
              <TabPanel value={tabValue} index={3}>
                <Grid container spacing={3}>
                  <Grid item xs={12} md={6}>
                    <Paper sx={{ p: 3, borderRadius: 2, boxShadow: '0 2px 8px rgba(0,0,0,0.1)', height: '100%' }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                        <Avatar sx={{ bgcolor: 'warning.main', mr: 2 }}>
                          <WorkIcon />
                        </Avatar>
                        <Typography variant="h6" sx={{ fontWeight: 600 }}>
                          Employment Information
                        </Typography>
                      </Box>

                      <Divider sx={{ mb: 3 }} />

                      <Box sx={{ mb: 3 }}>
                        <Typography variant="body2" color="text.secondary" gutterBottom>
                          Occupation
                        </Typography>
                        <Typography variant="h6" sx={{ fontWeight: 600 }}>
                          {citizen.occupation || 'Not provided'}
                        </Typography>
                      </Box>

                      {citizen.organization_name && (
                        <Box sx={{ mb: 3 }}>
                          <Typography variant="body2" color="text.secondary" gutterBottom>
                            Organization
                          </Typography>
                          <Typography variant="h6" sx={{ fontWeight: 600 }}>
                            {citizen.organization_name}
                          </Typography>
                        </Box>
                      )}

                      {citizen.employment_type && (
                        <Box sx={{ mb: 3 }}>
                          <Typography variant="body2" color="text.secondary" gutterBottom>
                            Employment Type
                          </Typography>
                          <Chip
                            label={getEmploymentTypeName(citizen.employment_type)}
                            color="warning"
                            variant="outlined"
                          />
                        </Box>
                      )}
                    </Paper>
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <Paper sx={{ p: 3, borderRadius: 2, boxShadow: '0 2px 8px rgba(0,0,0,0.1)', height: '100%' }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                        <Avatar sx={{ bgcolor: 'info.main', mr: 2 }}>
                          <SchoolIcon />
                        </Avatar>
                        <Typography variant="h6" sx={{ fontWeight: 600 }}>
                          Education Information
                        </Typography>
                      </Box>

                      <Divider sx={{ mb: 3 }} />

                      <Box sx={{ mb: 3 }}>
                        <Typography variant="body2" color="text.secondary" gutterBottom>
                          Education Level
                        </Typography>
                        <Typography variant="h6" sx={{ fontWeight: 600 }}>
                          {citizen.education_level || 'Not provided'}
                        </Typography>
                      </Box>
                    </Paper>
                  </Grid>
                </Grid>
              </TabPanel>

              {/* Tab Panel 4: Documents */}
              <TabPanel value={tabValue} index={4}>
                {citizen.documents && Array.isArray(citizen.documents) && citizen.documents.length > 0 ? (
                  <Grid container spacing={2}>
                    {citizen.documents.map((doc: any) => (
                      <Grid item xs={12} md={6} key={doc.id}>
                        <Paper sx={{
                          p: 3,
                          borderRadius: 2,
                          boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
                          position: 'relative',
                          overflow: 'hidden',
                          '&::before': {
                            content: '""',
                            position: 'absolute',
                            top: 0,
                            left: 0,
                            width: '4px',
                            height: '100%',
                            bgcolor: '#673ab7'
                          }
                        }}>
                          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                            <Typography variant="h6" sx={{ fontWeight: 600 }}>
                              {doc.document_type_name}
                            </Typography>
                            {doc.is_active ? (
                              <Chip
                                label="Active"
                                size="small"
                                color="success"
                              />
                            ) : (
                              <Chip
                                label="Inactive"
                                size="small"
                                color="error"
                              />
                            )}
                          </Box>

                          <Divider sx={{ my: 2 }} />

                          <Grid container spacing={2}>
                            {doc.issue_date && (
                              <Grid item xs={6}>
                                <Typography variant="body2" color="text.secondary" gutterBottom>
                                  Issue Date
                                </Typography>
                                <Typography variant="body1" sx={{ fontWeight: 600 }}>
                                  {formatDate(doc.issue_date)}
                                </Typography>
                              </Grid>
                            )}
                            {doc.expiry_date && (
                              <Grid item xs={6}>
                                <Typography variant="body2" color="text.secondary" gutterBottom>
                                  Expiry Date
                                </Typography>
                                <Typography variant="body1" sx={{ fontWeight: 600 }}>
                                  {formatDate(doc.expiry_date)}
                                </Typography>
                              </Grid>
                            )}
                          </Grid>

                          <Box sx={{ mt: 3, display: 'flex', justifyContent: 'flex-end', gap: 1 }}>
                            {doc.document_file && (
                              <Button
                                variant="outlined"
                                size="small"
                                href={doc.document_file}
                                target="_blank"
                                startIcon={<VisibilityIcon />}
                                sx={{ borderRadius: 2 }}
                              >
                                View
                              </Button>
                            )}
                            {doc.document_file && (
                              <Button
                                variant="contained"
                                size="small"
                                href={doc.document_file}
                                download
                                startIcon={<DownloadIcon />}
                                sx={{ borderRadius: 2 }}
                              >
                                Download
                              </Button>
                            )}
                          </Box>
                        </Paper>
                      </Grid>
                    ))}
                  </Grid>
                ) : (
                  <Box sx={{ textAlign: 'center', py: 6, bgcolor: 'rgba(0,0,0,0.02)', borderRadius: 2, mx: 2 }}>
                    <DescriptionIcon sx={{ fontSize: 80, color: 'text.disabled', mb: 2 }} />
                    <Typography variant="h5" color="text.secondary" gutterBottom>
                      No Documents Found
                    </Typography>
                    <Typography variant="body1" color="text.secondary" sx={{ mb: 3, maxWidth: 500, mx: 'auto' }}>
                      This citizen doesn't have any documents uploaded yet. Documents like birth certificates, marriage certificates, and other official papers can be added here.
                    </Typography>
                    <Button
                      variant="contained"
                      color="primary"
                      startIcon={<AddIcon />}
                      onClick={handleUploadDocuments}
                      sx={{ borderRadius: 2, px: 3, py: 1 }}
                    >
                      Upload Documents
                    </Button>
                  </Box>
                )}
              </TabPanel>

              {/* Tab Panel 5: ID Cards */}
              <TabPanel value={tabValue} index={5}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
                  <Typography variant="h6" sx={{ fontWeight: 600, display: 'flex', alignItems: 'center' }}>
                    <CreditCardIcon sx={{ mr: 1, color: 'primary.main' }} />
                    ID Cards
                  </Typography>
                </Box>

                {citizen.id_cards && Array.isArray(citizen.id_cards) && citizen.id_cards.length > 0 ? (
                  <Grid container spacing={2}>
                    {citizen.id_cards.map((card: any) => (
                      <Grid item xs={12} md={6} key={card.id}>
                        <Paper sx={{
                          p: 3,
                          borderRadius: 2,
                          boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
                          position: 'relative',
                          overflow: 'hidden',
                          '&::before': {
                            content: '""',
                            position: 'absolute',
                            top: 0,
                            left: 0,
                            width: '4px',
                            height: '100%',
                            bgcolor: card.status === 'ACTIVE' ? 'success.main' :
                                    card.status === 'EXPIRED' ? 'error.main' :
                                    card.status === 'PENDING' ? 'warning.main' : 'info.main'
                          }
                        }}>
                          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                            <Typography variant="h6" sx={{ fontWeight: 600 }}>
                              {card.card_number}
                            </Typography>
                            <Chip
                              label={card.status}
                              size="small"
                              color={card.status === 'ACTIVE' ? 'success' :
                                     card.status === 'EXPIRED' ? 'error' :
                                     card.status === 'PENDING' ? 'warning' : 'info'}
                            />
                          </Box>

                          <Divider sx={{ my: 2 }} />

                          <Grid container spacing={2}>
                            <Grid item xs={6}>
                              <Typography variant="body2" color="text.secondary" gutterBottom>
                                Issue Date
                              </Typography>
                              <Typography variant="body1" sx={{ fontWeight: 600 }}>
                                {formatDate(card.issue_date)}
                              </Typography>
                            </Grid>
                            <Grid item xs={6}>
                              <Typography variant="body2" color="text.secondary" gutterBottom>
                                Expiry Date
                              </Typography>
                              <Typography variant="body1" sx={{ fontWeight: 600 }}>
                                {formatDate(card.expiry_date)}
                              </Typography>
                            </Grid>
                          </Grid>

                          <Box sx={{ mt: 3, display: 'flex', justifyContent: 'flex-end' }}>
                            <Button
                              variant="contained"
                              size="medium"
                              onClick={() => navigate(`/id-cards/${card.id}`)}
                              startIcon={<VisibilityIcon />}
                              sx={{ borderRadius: 2 }}
                            >
                              View Details
                            </Button>
                          </Box>
                        </Paper>
                      </Grid>
                    ))}
                  </Grid>
                ) : (
                  <Box sx={{ textAlign: 'center', py: 6, bgcolor: 'rgba(0,0,0,0.02)', borderRadius: 2, mx: 2 }}>
                    <CreditCardIcon sx={{ fontSize: 80, color: 'text.disabled', mb: 2 }} />
                    <Typography variant="h5" color="text.secondary" gutterBottom>
                      No ID Cards Found
                    </Typography>
                    <Typography variant="body1" color="text.secondary" sx={{ mb: 3, maxWidth: 500, mx: 'auto' }}>
                      This citizen doesn't have any ID cards registered yet. ID cards are official identification documents issued by the kebele.
                    </Typography>
                    <Button
                      variant="contained"
                      color="primary"
                      startIcon={<AddIcon />}
                      onClick={handleRegisterIDCard}
                      sx={{ borderRadius: 2, px: 3, py: 1.5 }}
                    >
                      Register New ID Card
                    </Button>
                  </Box>
                )}
              </TabPanel>
            </Box>
          </Card>
        )}

        {!loading && !citizen && (
          <Box sx={{ textAlign: 'center', py: 8 }}>
            <PersonIcon sx={{ fontSize: 80, color: 'text.disabled', mb: 2 }} />
            <Typography variant="h5" color="text.secondary" gutterBottom>
              Citizen Not Found
            </Typography>
            <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
              The citizen you're looking for doesn't exist or you don't have permission to view it.
            </Typography>
            <Button
              variant="contained"
              color="primary"
              startIcon={<ArrowBackIcon />}
              onClick={handleBackToList}
              sx={{ borderRadius: 2 }}
            >
              Back to Citizens List
            </Button>
          </Box>
        )}
      </Container>
    </Box>
  );
};

export default CitizenDetails;
