from django.db import models
import uuid
from django.core.exceptions import ValidationError
from centers.models_base import TenantModel
from common.models import Religion, CitizenStatus, MaritalStatus, EmploymentType, EmployeeType, Country, Region, DocumentType
from centers.models import Subcity as SubCity, Center as <PERSON><PERSON><PERSON>
from common.models import Keten<PERSON>
from .models_citizen import CurrentStatus
from .models_document import Document
from .models_family import Child, Parent, EmergencyContact, Spouse
from .models_biometric import Biometric, Photo
from .models_base import validate_age

class Citizen(TenantModel):
    """Model representing a citizen registered in a center."""
    GENDER_CHOICES = (
        ('M', 'Male'),
        ('F', 'Female'),
    )

    # ID fields
    digital_id = models.CharField(max_length=50, unique=True, blank=True, null=True)
    uuid = models.CharField(max_length=36, unique=True)

    # Name Fields
    first_name = models.Char<PERSON>ield(max_length=100)
    middle_name = models.Char<PERSON><PERSON>(max_length=100, blank=True, null=True)
    last_name = models.Char<PERSON>ield(max_length=100)
    first_name_am = models.CharField(max_length=100, blank=True, null=True)
    middle_name_am = models.CharField(max_length=100, blank=True, null=True)
    last_name_am = models.CharField(max_length=100, blank=True, null=True)

    # Basic information
    date_of_birth = models.DateField()
    gender = models.CharField(max_length=1, choices=GENDER_CHOICES)

    # Relations
    religion = models.ForeignKey(Religion, on_delete=models.SET_NULL, null=True, blank=True)
    subcity = models.ForeignKey(SubCity, on_delete=models.SET_NULL, null=True, blank=True)
    kebele = models.ForeignKey(Kebele, on_delete=models.SET_NULL, null=True, blank=True)
    ketena = models.ForeignKey(Ketena, on_delete=models.PROTECT, null=True, blank=True, related_name='citizens')
    citizen_status = models.ForeignKey(CitizenStatus, on_delete=models.PROTECT, null=True)

    # Address/Contact
    house_number = models.CharField(max_length=20, blank=True, null=True)
    address = models.TextField(blank=True)
    phone = models.CharField(max_length=20, blank=True, null=True)
    email = models.EmailField(blank=True, null=True)
    nationality = models.CharField(max_length=100, default='Ethiopian', help_text="Nationality as text (derived from nationality_country if set)")
    nationality_country = models.ForeignKey(Country, on_delete=models.SET_NULL, null=True, blank=True, related_name='citizens', help_text="Country of nationality")
    region = models.ForeignKey(Region, on_delete=models.SET_NULL, null=True, blank=True)

    # ID Info
    id_number = models.CharField(max_length=50, unique=True, blank=True)
    id_issue_date = models.DateField(null=True, blank=True)
    id_expiry_date = models.DateField(null=True, blank=True)

    # Employment Info
    employment = models.BooleanField(default=False)
    employee_type = models.ForeignKey(EmployeeType, on_delete=models.SET_NULL, null=True, blank=True)
    organization_name = models.CharField(max_length=255, blank=True, null=True)
    occupation = models.CharField(max_length=100, blank=True)
    employment_type = models.ForeignKey(EmploymentType, on_delete=models.SET_NULL, null=True, blank=True)

    # Marital / Family Info
    marital_status = models.CharField(max_length=20, null=True, blank=True)
    spouse = models.ForeignKey(Spouse, on_delete=models.SET_NULL, null=True, blank=True, related_name='citizen_as_spouse')

    # Parents Relationship
    mother = models.ForeignKey(Parent, related_name='children_as_mother', on_delete=models.SET_NULL, null=True, blank=True)
    father = models.ForeignKey(Parent, related_name='children_as_father', on_delete=models.SET_NULL, null=True, blank=True)

    # Child and Emergency Contact Logic
    emergency_contact = models.ForeignKey(EmergencyContact, on_delete=models.SET_NULL, null=True, blank=True, related_name='citizen_as_emergency')

    # Is Resident of City
    is_resident = models.BooleanField(default=False, help_text="Indicates whether the citizen is a resident of the city.")

    # Current Status (Transferred, Not Alive, etc.)
    current_status = models.ForeignKey(CurrentStatus, on_delete=models.SET_NULL, null=True, blank=True)
    is_active = models.BooleanField(default=True)

    # Relationships with other models
    biometric_data = models.OneToOneField(Biometric, on_delete=models.SET_NULL, null=True, blank=True, related_name='citizen_biometric')
    photo_record = models.OneToOneField(Photo, on_delete=models.SET_NULL, null=True, blank=True, related_name='citizen_photo')

    # System fields
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey('accounts.User', on_delete=models.SET_NULL, null=True, related_name='created_citizens')

    class Meta:
        ordering = ['-created_at']
        verbose_name = "Citizen"
        verbose_name_plural = "Citizens"

    def __str__(self):
        return f"{self.first_name} {self.middle_name or ''} {self.last_name} ({self.digital_id})"

    def save(self, *args, **kwargs):
        print(f"\n\nSaving citizen: {self.first_name} {self.last_name}\n\n")
        print(f"\n\nCenter: {self.center}\n\n")
        print(f"\n\nSubcity: {self.subcity}\n\n")
        print(f"\n\nKebele: {self.kebele}\n\n")
        print(f"\n\nKetena: {self.ketena}\n\n")

        # First ensure we have a center set (from TenantModel)
        needs_save = False

        # Generate UUID if not provided
        if not self.uuid:
            self.uuid = str(uuid.uuid4())
            needs_save = True
            print(f"\n\nGenerated UUID: {self.uuid}\n\n")

        if not self.digital_id:
            # Generate a unique digital ID based on center information
            # Get city code from center's parent's parent (city)
            city_code = ''
            if self.center and hasattr(self.center, 'parent') and self.center.parent:
                subcity = self.center.parent
                print(f"\n\nFound subcity from center: {subcity}\n\n")
                if hasattr(subcity, 'parent') and subcity.parent:
                    city = subcity.parent
                    print(f"\n\nFound city from subcity: {city}\n\n")
                    if hasattr(city, 'city_code'):
                        city_code = city.city_code
                        print(f"\n\nFound city code: {city_code}\n\n")

            # If we couldn't get the city code, use a default
            if not city_code:
                city_code = 'GD'
                print(f"\n\nUsing default city code: {city_code}\n\n")

            # Generate a unique ID using UUID
            unique_id = str(uuid.uuid4())[:8].upper()
            self.digital_id = f"{city_code}-{unique_id}"
            needs_save = True
            print(f"\n\nGenerated digital ID: {self.digital_id}\n\n")

        if not self.id_number:
            # Generate a unique ID number based on location hierarchy
            if self.subcity and self.kebele:
                print(f"\n\nGenerating ID number with subcity and kebele\n\n")
                # Get the first letter of the city name
                city_prefix = ''
                if self.center and hasattr(self.center, 'parent') and self.center.parent:
                    subcity = self.center.parent
                    print(f"\n\nFound subcity from center for ID: {subcity}\n\n")
                    if hasattr(subcity, 'parent') and subcity.parent:
                        city = subcity.parent
                        print(f"\n\nFound city from subcity for ID: {city}\n\n")
                        if hasattr(city, 'city_name') and city.city_name:
                            city_prefix = city.city_name[0].upper()
                            print(f"\n\nFound city prefix: {city_prefix}\n\n")

                # If we couldn't get the city prefix, use a default
                if not city_prefix:
                    city_prefix = 'G'
                    print(f"\n\nUsing default city prefix: {city_prefix}\n\n")

                # Get the first two letters of the subcity name
                subcity_prefix = 'XX'  # Default value

                # Try to get the subcity name from the current schema
                from django.db import connection
                from centers.models import Client

                # Get the current schema name
                current_schema = connection.schema_name
                print(f"\n\nCurrent schema: {current_schema}\n\n")

                # Try to get the tenant from the current schema
                try:
                    # Get the tenant for the current schema
                    tenant = Client.objects.get(schema_name=current_schema)
                    if tenant and hasattr(tenant, 'parent') and tenant.parent:
                        subcity_name = tenant.parent.name
                        print(f"\n\nTenant parent name (subcity): {subcity_name}\n\n")
                        # Some subcity names might be like "Azezo (Default City)" - extract just the first part
                        subcity_name = subcity_name.split('(')[0].strip()
                        # Get the first two letters
                        subcity_prefix = subcity_name[:2].upper()
                except Exception as e:
                    print(f"\n\nError getting tenant: {e}\n\n")
                    # Fallback to the subcity from the citizen record
                    if self.subcity and self.subcity.name:
                        # Some subcity names might be like "Azezo (Default City)" - extract just the first part
                        subcity_name = self.subcity.name.split('(')[0].strip()
                        print(f"\n\nSubcity name from citizen record: {subcity_name}\n\n")
                        # Get the first two letters
                        subcity_prefix = subcity_name[:2].upper()

                print(f"\n\nSubcity prefix: {subcity_prefix}\n\n")

                # Get the kebele number from the current schema
                kebele_num = '01'  # Default kebele number

                # Try to extract kebele number from schema name
                current_schema = connection.schema_name
                print(f"\n\nCurrent schema for kebele number: {current_schema}\n\n")

                import re
                schema_digits = re.findall(r'\d+', current_schema)
                if schema_digits:
                    kebele_num = schema_digits[-1].zfill(2)[:2]  # Ensure it's 2 digits with leading zeros
                    print(f"\n\nExtracted kebele number from schema: {kebele_num}\n\n")
                # Fallback to kebele name if schema extraction fails
                elif hasattr(self.kebele, 'name') and self.kebele.name:
                    # Extract kebele number from name (e.g., "Kebele 14")
                    kebele_name = self.kebele.name
                    print(f"\n\nKebele name: {kebele_name}\n\n")
                    # Use regex to extract digits from the kebele name
                    digits = re.findall(r'\d+', kebele_name)
                    if digits:
                        # Use the last group of digits found (most likely the kebele number)
                        kebele_num = digits[-1].zfill(2)[:2]  # Ensure it's 2 digits with leading zeros
                        print(f"\n\nExtracted kebele number from kebele name: {kebele_num}\n\n")
                    else:
                        # Fallback to extracting from code if available
                        if hasattr(self.kebele, 'code') and self.kebele.code:
                            kebele_code = self.kebele.code
                            print(f"\n\nKebele code: {kebele_code}\n\n")
                            if '-' in kebele_code:
                                kebele_num = kebele_code.split('-')[1][2:4]  # Extract the numeric part
                                print(f"\n\nExtracted kebele number from code: {kebele_num}\n\n")

                # Generate a sequential 6-digit number
                # In a real implementation, this would use a database sequence
                # For now, we'll use the current timestamp to ensure uniqueness
                import time
                timestamp = int(time.time() * 1000) % 1000000  # Last 6 digits of current timestamp in milliseconds
                random_suffix = f"{timestamp:06d}"  # Ensure it's 6 digits with leading zeros
                print(f"\n\nRandom suffix: {random_suffix}\n\n")

                # Format: First letter of city + First 2 letters of subcity + Kebele number + 6 digit sequence
                self.id_number = f"{city_prefix}{subcity_prefix}{kebele_num}{random_suffix}"
                print(f"\n\nGenerated ID number: {self.id_number}\n\n")
            else:
                # Fallback if subcity or kebele not set
                print(f"\n\nFalling back to default ID number (subcity or kebele not set)\n\n")
                print(f"\n\nSubcity: {self.subcity}\n\n")
                print(f"\n\nKebele: {self.kebele}\n\n")
                self.id_number = f"ID-{str(uuid.uuid4())[:12].upper()}"
                print(f"\n\nGenerated fallback ID number: {self.id_number}\n\n")

            needs_save = True

        try:
            # Call the parent save method
            print(f"\n\nCalling super().save()\n\n")
            super().save(*args, **kwargs)
            print(f"\n\nsuper().save() completed successfully\n\n")

            if needs_save:
                # Use update to avoid infinite recursion
                print(f"\n\nUpdating citizen with ID {self.pk}\n\n")
                Citizen.objects.filter(pk=self.pk).update(
                    digital_id=self.digital_id,
                    id_number=self.id_number,
                    uuid=self.uuid
                )
                print(f"\n\nUpdate completed successfully\n\n")
        except Exception as e:
            print(f"\n\nError saving citizen: {str(e)}\n\n")
            import traceback
            traceback.print_exc()

    def clean(self):
        """Validate the model."""
        super().clean()

        # Validate age - citizen must be at least 18 years old
        if self.date_of_birth:
            validate_age(self.date_of_birth, min_age=18)

        # Validate that kebele belongs to the selected subcity
        if self.subcity and self.kebele and hasattr(self.kebele, 'parent'):
            if self.kebele.parent != self.subcity:
                raise ValidationError({
                    'kebele': f"The selected kebele does not belong to the selected subcity {self.subcity.name}."
                })

        # Validate that ketena belongs to the selected kebele
        if self.kebele and self.ketena:
            # Check if ketena has kebele_id field and it matches the kebele's ID
            if hasattr(self.ketena, 'kebele_id') and str(self.kebele.id) != str(self.ketena.kebele_id):
                raise ValidationError({
                    'ketena': f"The selected ketena does not belong to the selected kebele {self.kebele.name}."
                })
            # For backward compatibility, also check center if it exists
            elif hasattr(self.ketena, 'center') and self.ketena.center and self.ketena.center != self.kebele:
                raise ValidationError({
                    'ketena': f"The selected ketena does not belong to the selected kebele {self.kebele.name}."
                })

        # Validate that nationality and nationality_country are consistent
        if self.nationality and self.nationality_country and self.nationality != self.nationality_country.name:
            # Instead of raising an error, we'll update the nationality field to match the country
            self.nationality = self.nationality_country.name

    def get_full_name(self):
        """Return the full name of the citizen."""
        if self.middle_name:
            return f"{self.first_name} {self.middle_name} {self.last_name}"
        return f"{self.first_name} {self.last_name}"

    @property
    def full_name(self):
        return self.get_full_name()
