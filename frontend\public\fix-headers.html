<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Fix Authentication Headers</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
      line-height: 1.6;
    }
    h1, h2 {
      color: #3f51b5;
    }
    .card {
      border: 1px solid #ddd;
      border-radius: 8px;
      padding: 20px;
      margin-bottom: 20px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    button {
      background-color: #3f51b5;
      color: white;
      border: none;
      padding: 10px 20px;
      border-radius: 4px;
      cursor: pointer;
      font-size: 16px;
      margin-right: 10px;
      margin-bottom: 10px;
    }
    button:hover {
      background-color: #303f9f;
    }
    pre {
      background-color: #f5f5f5;
      padding: 10px;
      border-radius: 4px;
      overflow-x: auto;
      white-space: pre-wrap;
      word-break: break-all;
    }
    .success { color: green; }
    .error { color: red; }
    .warning { color: orange; }
    table {
      width: 100%;
      border-collapse: collapse;
      margin-bottom: 15px;
    }
    th, td {
      border: 1px solid #ddd;
      padding: 8px;
      text-align: left;
    }
    th {
      background-color: #f2f2f2;
    }
  </style>
</head>
<body>
  <h1>Fix Authentication Headers</h1>
  
  <div class="card">
    <h2>Current Authentication State</h2>
    <div id="before-fix">Loading...</div>
  </div>
  
  <div class="card">
    <h2>Apply Fix</h2>
    <p>This will fix the following issues:</p>
    <ul>
      <li>Duplicated tokens in Authorization header</li>
      <li>Incorrect schema name format</li>
      <li>Token not being found in the correct schema</li>
    </ul>
    <button id="apply-fix">Apply Fix</button>
    <div id="fix-result" style="display: none;"></div>
  </div>
  
  <div class="card">
    <h2>Test API Request</h2>
    <button id="test-api">Test API Request</button>
    <div id="api-result" style="display: none;"></div>
  </div>
  
  <div class="card">
    <h2>Navigation</h2>
    <button onclick="window.location.href = '/'">Go to Home</button>
    <button onclick="window.location.href = '/citizens'">Go to Citizens</button>
    <button onclick="window.location.href = '/login'">Go to Login</button>
  </div>
  
  <script>
    // Display current authentication state
    function displayAuthState() {
      const beforeFixDiv = document.getElementById('before-fix');
      
      try {
        // Get token and schema from localStorage
        const token = localStorage.getItem('token');
        const schema = localStorage.getItem('schema_name');
        
        let html = '<h3>Authentication Details</h3>';
        html += '<table>';
        html += '<tr><th>Property</th><th>Value</th><th>Issues</th></tr>';
        
        // Token
        if (token) {
          const tokenIssues = [];
          if (token.includes(',')) tokenIssues.push('Contains comma (possible duplication)');
          if (token.includes('Token')) tokenIssues.push('Contains "Token" prefix');
          if (/\s/.test(token)) tokenIssues.push('Contains spaces');
          
          html += '<tr>';
          html += `<td>Token</td>`;
          html += `<td>${token}</td>`;
          html += `<td class="${tokenIssues.length > 0 ? 'error' : 'success'}">${tokenIssues.length > 0 ? tokenIssues.join('<br>') : 'No issues'}</td>`;
          html += '</tr>';
        } else {
          html += '<tr>';
          html += `<td>Token</td>`;
          html += `<td colspan="2" class="error">Not found</td>`;
          html += '</tr>';
        }
        
        // Schema
        if (schema) {
          const schemaIssues = [];
          if (schema.includes(',')) schemaIssues.push('Contains comma (possible duplication)');
          if (/\s/.test(schema)) schemaIssues.push('Contains spaces (should use underscores)');
          
          html += '<tr>';
          html += `<td>Schema Name</td>`;
          html += `<td>${schema}</td>`;
          html += `<td class="${schemaIssues.length > 0 ? 'error' : 'success'}">${schemaIssues.length > 0 ? schemaIssues.join('<br>') : 'No issues'}</td>`;
          html += '</tr>';
        } else {
          html += '<tr>';
          html += `<td>Schema Name</td>`;
          html += `<td colspan="2" class="error">Not found</td>`;
          html += '</tr>';
        }
        
        html += '</table>';
        
        // Token Store
        const tokenStoreStr = localStorage.getItem('tokenStore');
        if (tokenStoreStr) {
          try {
            const tokenStore = JSON.parse(tokenStoreStr);
            html += '<h3>Token Store</h3>';
            html += '<table>';
            html += '<tr><th>Schema</th><th>Token</th><th>Issues</th></tr>';
            
            for (const [schemaName, schemaToken] of Object.entries(tokenStore)) {
              const issues = [];
              if (schemaName.includes(',')) issues.push('Schema contains comma');
              if (/\s/.test(schemaName)) issues.push('Schema contains spaces');
              if (schemaToken.includes(',')) issues.push('Token contains comma');
              if (schemaToken.includes('Token')) issues.push('Token contains "Token" prefix');
              if (/\s/.test(schemaToken)) issues.push('Token contains spaces');
              
              html += '<tr>';
              html += `<td>${schemaName}</td>`;
              html += `<td>${schemaToken}</td>`;
              html += `<td class="${issues.length > 0 ? 'error' : 'success'}">${issues.length > 0 ? issues.join('<br>') : 'No issues'}</td>`;
              html += '</tr>';
            }
            
            html += '</table>';
          } catch (e) {
            html += `<p class="error">Error parsing token store: ${e.message}</p>`;
          }
        }
        
        // Tenant
        const tenantStr = localStorage.getItem('tenant');
        if (tenantStr) {
          try {
            const tenant = JSON.parse(tenantStr);
            html += '<h3>Tenant Information</h3>';
            html += '<table>';
            html += '<tr><th>Property</th><th>Value</th></tr>';
            
            for (const [key, value] of Object.entries(tenant)) {
              html += '<tr>';
              html += `<td>${key}</td>`;
              html += `<td>${typeof value === 'object' ? JSON.stringify(value) : value}</td>`;
              html += '</tr>';
            }
            
            html += '</table>';
          } catch (e) {
            html += `<p class="error">Error parsing tenant: ${e.message}</p>`;
          }
        }
        
        beforeFixDiv.innerHTML = html;
      } catch (error) {
        beforeFixDiv.innerHTML = `<p class="error">Error displaying authentication state: ${error.message}</p>`;
      }
    }
    
    // Apply the fix
    function applyFix() {
      const fixResultDiv = document.getElementById('fix-result');
      fixResultDiv.style.display = 'block';
      
      try {
        // Load and execute the header fix script
        const script = document.createElement('script');
        script.src = '/header-fix.js';
        script.onload = function() {
          // Display the results after the fix
          fixResultDiv.innerHTML = '<p class="success">Fix applied successfully!</p>';
          displayAuthState();
        };
        script.onerror = function() {
          fixResultDiv.innerHTML = '<p class="error">Error loading fix script</p>';
        };
        document.head.appendChild(script);
      } catch (error) {
        fixResultDiv.innerHTML = `<p class="error">Error applying fix: ${error.message}</p>`;
      }
    }
    
    // Test API request
    async function testApi() {
      const apiResultDiv = document.getElementById('api-result');
      apiResultDiv.style.display = 'block';
      apiResultDiv.innerHTML = '<p>Testing API request...</p>';
      
      try {
        // Get schema from localStorage
        const schema = localStorage.getItem('schema_name');
        if (!schema) {
          apiResultDiv.innerHTML = '<p class="error">No schema found in localStorage</p>';
          return;
        }
        
        // Make a test request to the citizens endpoint
        const url = `/api/tenant/${encodeURIComponent(schema)}/citizens/?limit=1`;
        
        // Get the token
        const token = localStorage.getItem('token');
        if (!token) {
          apiResultDiv.innerHTML = '<p class="error">No token found in localStorage</p>';
          return;
        }
        
        // Create headers
        const headers = {
          'Authorization': `Token ${token}`,
          'Content-Type': 'application/json',
          'X-Schema-Name': schema
        };
        
        // Make the request
        const response = await fetch(url, {
          method: 'GET',
          headers,
          credentials: 'include'
        });
        
        // Get response details
        const status = response.status;
        const statusText = response.statusText;
        
        let responseBody;
        try {
          responseBody = await response.json();
        } catch (e) {
          responseBody = await response.text();
        }
        
        // Display response
        apiResultDiv.innerHTML = `
          <h3>API Request Result</h3>
          <p><strong>URL:</strong> ${url}</p>
          <p><strong>Status:</strong> ${status} ${statusText}</p>
          <p><strong>Headers Sent:</strong></p>
          <pre>${JSON.stringify(headers, null, 2)}</pre>
          <p><strong>Response:</strong></p>
          <pre>${typeof responseBody === 'object' ? JSON.stringify(responseBody, null, 2) : responseBody}</pre>
        `;
      } catch (error) {
        apiResultDiv.innerHTML = `<p class="error">Error testing API: ${error.message}</p>`;
      }
    }
    
    // Initialize the page
    document.addEventListener('DOMContentLoaded', function() {
      displayAuthState();
      
      // Add event listeners
      document.getElementById('apply-fix').addEventListener('click', applyFix);
      document.getElementById('test-api').addEventListener('click', testApi);
    });
  </script>
</body>
</html>
