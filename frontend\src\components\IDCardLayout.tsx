import React from 'react';
import {
  Box,
  Typography,
  Avatar,
} from '@mui/material';
import CalendarTodayIcon from '@mui/icons-material/CalendarToday';
import SecurityIcon from '@mui/icons-material/Security';
import VerifiedUserIcon from '@mui/icons-material/VerifiedUser';
import QRCode from 'react-qr-code';
import PhoneIcon from '@mui/icons-material/Phone';
import EmailIcon from '@mui/icons-material/Email';
import LanguageIcon from '@mui/icons-material/Language';
import QrCodeIcon from '@mui/icons-material/QrCode';
// Import pattern utilities
import { generateSplitPatternDataUrls } from '../utils/patternUtils';

// Utility function to convert relative URLs to absolute URLs
const getAbsoluteUrl = (url: string | undefined): string | undefined => {
  if (!url) return undefined;

  // Check if the URL is already absolute
  if (url.startsWith('http://') || url.startsWith('https://')) {
    return url;
  }

  // Check if the URL is a relative path
  if (url.startsWith('/')) {
    // Use the current origin as the base URL
    const baseUrl = window.location.origin;
    return `${baseUrl}${url}`;
  }

  // Return the URL as is if it's not a relative path
  return url;
};

// Function to format or parse ID number: G (Gondar) + ZO (Zoble) + 14 (Kebele 14) + 6 digit sequence
const formatIdNumber = (idNumber: string | undefined): string => {
  if (!idNumber) return 'NOT ASSIGNED';

  // Check if the ID is already in our format (starts with a letter followed by 2 letters and 2 digits)
  const idPattern = /^[A-Z][A-Z]{2}\d{2}\d{6}$/;
  if (idPattern.test(idNumber)) {
    // ID is already in the correct format
    return idNumber;
  }

  // For demonstration, we'll use a fixed format for the prefix
  // In a real app, you would get these values from the tenant/center data
  const cityFirstLetter = 'G'; // G for Gondar
  const subcityFirstTwoLetters = 'ZO'; // ZO for Zoble
  const kebeleNumber = '14'; // 14 for Kebele 14

  // Extract a unique 6-digit sequence from the existing ID
  // This ensures the ID remains consistent for the same citizen
  let sequenceNumber = '';

  // Extract digits from the existing ID, or generate a hash from the ID string
  const digits = idNumber.replace(/\D/g, '');
  if (digits.length >= 6) {
    // Use the last 6 digits if available
    sequenceNumber = digits.slice(-6);
  } else {
    // Generate a hash-based number from the ID string
    // This ensures the same ID always generates the same sequence
    let hash = 0;
    for (let i = 0; i < idNumber.length; i++) {
      hash = ((hash << 5) - hash) + idNumber.charCodeAt(i);
      hash |= 0; // Convert to 32bit integer
    }
    // Make sure it's positive and 6 digits
    hash = Math.abs(hash) % 900000 + 100000;
    sequenceNumber = hash.toString();
  }

  // Combine all parts to create the formatted ID
  return `${cityFirstLetter}${subcityFirstTwoLetters}${kebeleNumber}${sequenceNumber}`;
};

// Common styles for consistent dimensions
const styles = {
  // Main card container - Ethiopian ID style
  card: {
    border: '1px solid rgba(0, 0, 0, 0.12)',
    borderRadius: 1, // Less rounded corners like Ethiopian ID
    p: 0,
    mb: 3,
    position: 'relative',
    width: '100%',
    maxWidth: '500px',
    height: '300px', // Taller to match Ethiopian ID proportions
    background: '#ffffff',
    overflow: 'hidden',
    boxShadow: '0 4px 8px rgba(0,0,0,0.1)',
    display: 'flex',
    flexDirection: 'column',
    // Light blue background with subtle gradient like Ethiopian ID sample
    backgroundImage: 'linear-gradient(to bottom, #e3f2fd 0%, #f5f9fc 100%)',
    // No decorative patterns in the background - Ethiopian IDs have clean backgrounds
    '&::before': {
      content: '""',
      position: 'absolute',
      top: 0,
      left: 0,
      width: '100%',
      height: '100%',
      backgroundImage: 'linear-gradient(to right, rgba(0,120,215,0.02) 0%, rgba(0,120,215,0.04) 50%, rgba(0,120,215,0.02) 100%)',
      pointerEvents: 'none',
      zIndex: 1
    }
  },
  // Header styles for the title section - Ethiopian ID style
  header: {
    background: 'linear-gradient(to right, #0091ea 0%, #0277bd 100%)', // Light blue like Ethiopian ID sample
    color: 'white',
    py: 0.8, // Increased padding to accommodate larger images
    px: 1.5,
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    borderBottom: '1px solid rgba(0, 0, 0, 0.1)',
    height: '45px', // Increased height to accommodate larger images
    position: 'relative',
  },
  // Main content area - Ethiopian ID style
  content: {
    display: 'flex',
    p: 1.5, // Slightly more padding
    bgcolor: 'white',
    flexGrow: 1,
    position: 'relative',
    zIndex: 2
  },
  // Footer styles
  footer: {
    height: '20px',
    bgcolor: '#f5f5f5',
    borderTop: '1px solid rgba(0, 0, 0, 0.1)',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    px: 2,
    py: 0.25,
  },

  // ID number display - Ethiopian ID style
  idNumber: {
    color: '#0063b1', // Blue instead of red
    fontWeight: 700,
    fontSize: '0.9rem',
    letterSpacing: '0.5px', // Spaced out like Ethiopian ID
    position: 'absolute',
    bottom: '15px', // At the bottom like Ethiopian ID
    right: '20px'
  },
  // Class display (like Class C in driver's license)
  classDisplay: {
    position: 'absolute',
    top: '80px',
    right: '20px',
    bgcolor: 'rgba(0,0,0,0.05)',
    border: '1px solid rgba(0,0,0,0.1)',
    borderRadius: 1,
    px: 1,
    py: 0.5,
    fontWeight: 700
  },
  // Info row for personal details
  infoRow: {
    display: 'flex',
    justifyContent: 'space-between',
    borderBottom: '1px solid rgba(0,0,0,0.05)',
    py: 0.5,
    '&:last-child': {
      borderBottom: 'none'
    }
  },
  // Label for info items
  infoLabel: {
    color: 'rgba(0,0,0,0.6)',
    fontSize: '0.7rem',
    fontWeight: 500,
    width: '80px'
  },
  // Value for info items
  infoValue: {
    color: 'rgba(0,0,0,0.87)',
    fontSize: '0.7rem',
    fontWeight: 600,
    textAlign: 'right'
  },
  // Back side magnetic strip
  magneticStrip: {
    width: '100%',
    height: '40px',
    bgcolor: '#000',
    mb: 2
  },
  // Back side barcode
  barcode: {
    position: 'absolute',
    right: '20px',
    bottom: '40px',
    width: '120px',
    height: '60px',
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center'
  },
  // Back side info section
  backInfo: {
    p: 2,
    pt: 3
  },
  // Back side ID number
  backIdNumber: {
    color: '#ff0000',
    fontWeight: 700,
    fontSize: '1.2rem',
    mb: 1
  },
  // World map background
  worldMap: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
    width: '80%',
    height: '80%',
    opacity: 0.05,
    zIndex: 0,
    pointerEvents: 'none'
  }
};

interface IDCardLayoutProps {
  idCard: any;
  showFront: boolean;
  formatDate: (date: string) => string;
  primaryColor?: string;
  secondaryColor?: string;
  cityName?: string;
  subcityName?: string;
  kebeleName?: string;
}

const IDCardLayout: React.FC<IDCardLayoutProps> = ({
  idCard,
  showFront,
  formatDate,
  primaryColor = '#0066b2',
  secondaryColor = '#000066',
  cityName = 'ጎንደር', // Default: Gondar
  subcityName = 'ዞብል', // Default: Zobil
  kebeleName = 'ገብርኤል ቀበሌ' // Default: Gabriel Kebele
}) => {
  // Debug the photo URL and pattern data
  console.log('IDCardLayout - idCard:', idCard);
  console.log('IDCardLayout - citizen_photo:', idCard.citizen_photo);
  console.log('IDCardLayout - pattern_status:', idCard.pattern_status);
  console.log('IDCardLayout - kebele_pattern:', idCard.kebele_pattern);
  console.log('IDCardLayout - subcity_pattern:', idCard.subcity_pattern);
  console.log('IDCardLayout - kebele_approval_status:', idCard.kebele_approval_status);
  console.log('IDCardLayout - subcity_approval_status:', idCard.subcity_approval_status);

  // Debug date of birth fields
  console.log('IDCardLayout - citizen_birth_date:', idCard.citizen_birth_date);
  console.log('IDCardLayout - citizen_dob:', idCard.citizen_dob);
  console.log('IDCardLayout - birth_date:', idCard.birth_date);
  console.log('IDCardLayout - dob:', idCard.dob);

  // Log all keys in the idCard object to find any date-related fields
  console.log('IDCardLayout - All keys in idCard:', Object.keys(idCard));

  // Check approval status
  const kebeleApproved = idCard.kebele_approval_status === 'APPROVED';
  const subcityApproved = idCard.subcity_approval_status === 'APPROVED';

  console.log('IDCardLayout - kebeleApproved:', kebeleApproved);
  console.log('IDCardLayout - subcityApproved:', subcityApproved);

  // Generate split patterns based on ID card data
  const { kebelePatternUrl, subcityPatternUrl } = generateSplitPatternDataUrls(idCard);

  console.log('IDCardLayout - kebelePatternUrl:', kebelePatternUrl ? 'Generated' : 'None');
  console.log('IDCardLayout - subcityPatternUrl:', subcityPatternUrl ? 'Generated' : 'None');

  // Log the actual pattern URLs for debugging
  console.log('IDCardLayout - kebelePatternUrl actual:', kebelePatternUrl);
  console.log('IDCardLayout - subcityPatternUrl actual:', subcityPatternUrl);

  // Process the photo URL
  const photoUrl = getAbsoluteUrl(idCard.citizen_photo);
  console.log('IDCardLayout - processed photoUrl:', photoUrl);

  // Fallback image URL
  const fallbackImageUrl = 'https://www.gravatar.com/avatar/00000000000000000000000000000000?d=mp&f=y';
  console.log('IDCardLayout - fallbackImageUrl:', fallbackImageUrl);
  return (
    <>
      {showFront ? (
        /* Front Side of ID Card */
        <Box sx={styles.card}>
          {/* Card Header - Ethiopian ID style */}
          <Box sx={{
            ...styles.header,
            background: 'linear-gradient(to right, #0091ea 0%, #0277bd 100%)'
          }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', width: '100%' }}>
              <Box
                component="img"
                src="/src/assets/ethiopian-flag.svg"
                alt="Ethiopian Flag"
                sx={{ width: 32, height: 22, ml: 0.5 }}
              />
              <Box sx={{ textAlign: 'center', flex: 1, mx: 1 }}>
                <Typography variant="subtitle2" sx={{
                  fontWeight: 700,
                  fontSize: '0.55rem',
                  textAlign: 'center',
                  color: '#fff',
                  lineHeight: 1.2
                }}>
                  በኢትዮጵያ ፌደራላዊ ዲሞክራሲያዊ ሪፐብሊክ በአማራ ብሔራዊ ክልላዊ መንግሥት
                </Typography>
                <Typography variant="subtitle2" sx={{
                  fontWeight: 500,
                  fontSize: '0.55rem',
                  textAlign: 'center',
                  color: '#fff',
                  opacity: 0.9
                }}>
                  በጎንደር ከተማ አስተዳደር በዞብል ክ/ከተማ የገብርኤል ቀበሌ አስተዳደር ጽ/ቤት
                </Typography>
              </Box>
              <Box
                component="img"
                src="/src/assets/flag-icon.jpg"
                alt="Flag"
                sx={{ width: 28, height: 28, mr: 0.5, borderRadius: '50%', objectFit: 'contain' }}
              />
            </Box>
          </Box>

          {/* Main Content - Ethiopian ID style */}
          <Box sx={styles.content}>
            {/* Security Pattern Overlay - Left half (Kebele) */}
            {kebeleApproved && (
              <>
                <Box
                  sx={{
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    width: '50%', // Left half of the card
                    height: '100%',
                    backgroundImage: `url(/castle-watermark.svg)`,
                    backgroundSize: 'cover',
                    backgroundPosition: 'left center',
                    opacity: 0.3,
                    pointerEvents: 'none',
                    zIndex: 0,
                    mixBlendMode: 'multiply'
                  }}
                  data-testid="kebele-pattern-overlay"
                />
                <Typography
                  sx={{
                    position: 'absolute',
                    bottom: '20px',
                    left: '25%',
                    transform: 'translateX(-50%)',
                    color: 'rgba(210, 170, 90, 0.5)',
                    fontWeight: 'bold',
                    fontSize: '12px',
                    zIndex: 0,
                    pointerEvents: 'none',
                    textAlign: 'center'
                  }}
                >
                  {idCard.tenant_name || idCard.kebele_name || 'ቀበሌ ማረጋገጫ'}
                </Typography>
              </>
            )}

            {/* Security Pattern Overlay - Right half (Subcity) */}
            {subcityApproved && (
              <>
                <Box
                  sx={{
                    position: 'absolute',
                    top: 0,
                    left: '50%', // Right half of the card
                    width: '50%',
                    height: '100%',
                    backgroundImage: `url(/castle-watermark.svg)`,
                    backgroundSize: 'cover',
                    backgroundPosition: 'right center',
                    opacity: 0.3,
                    pointerEvents: 'none',
                    zIndex: 0,
                    mixBlendMode: 'color-burn'
                  }}
                  data-testid="subcity-pattern-overlay"
                />
                <Typography
                  sx={{
                    position: 'absolute',
                    bottom: '20px',
                    left: '75%',
                    transform: 'translateX(-50%)',
                    color: 'rgba(210, 170, 90, 0.5)',
                    fontWeight: 'bold',
                    fontSize: '12px',
                    zIndex: 0,
                    pointerEvents: 'none',
                    textAlign: 'center'
                  }}
                >
                  {idCard.parent_tenant_name || idCard.subcity_name || 'ክ/ከተማ ማረጋገጫ'}
                </Typography>
              </>
            )}

            {/* Debug Pattern Display - Always visible for testing */}
            <Box
              sx={{
                position: 'absolute',
                top: 0,
                left: 0,
                width: '50%',
                height: '100%',
                backgroundImage: `url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSI1MDAiIGhlaWdodD0iMzAwIiB2aWV3Qm94PSIwIDAgNTAwIDMwMCI+PHJlY3Qgd2lkdGg9IjUwMCIgaGVpZ2h0PSIzMDAiIGZpbGw9InJnYmEoMCwgMCwgMjU1LCAwLjA1KSIvPjx0ZXh0IHg9IjEyNSIgeT0iMTUwIiBmb250LWZhbWlseT0ic2Fucy1zZXJpZiIgZm9udC1zaXplPSIyMCIgZmlsbD0icmdiYSgwLCAwLCAyNTUsIDAuMikiPktlYmVsZSBQYXR0ZXJuPC90ZXh0Pjwvc3ZnPg==)`,
                backgroundSize: 'cover',
                backgroundPosition: 'left center',
                opacity: 1,
                pointerEvents: 'none',
                zIndex: 0,
                mixBlendMode: 'multiply'
              }}
              data-testid="debug-kebele-pattern"
            />

            <Box
              sx={{
                position: 'absolute',
                top: 0,
                left: '50%',
                width: '50%',
                height: '100%',
                backgroundImage: `url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSI1MDAiIGhlaWdodD0iMzAwIiB2aWV3Qm94PSIwIDAgNTAwIDMwMCI+PHJlY3Qgd2lkdGg9IjUwMCIgaGVpZ2h0PSIzMDAiIGZpbGw9InJnYmEoMjU1LCAwLCAwLCAwLjA1KSIvPjx0ZXh0IHg9IjM3NSIgeT0iMTUwIiBmb250LWZhbWlseT0ic2Fucy1zZXJpZiIgZm9udC1zaXplPSIyMCIgZmlsbD0icmdiYSgyNTUsIDAsIDAsIDAuMikiPlN1YmNpdHkgUGF0dGVybjwvdGV4dD48L3N2Zz4=)`,
                backgroundSize: 'cover',
                backgroundPosition: 'right center',
                opacity: 1,
                pointerEvents: 'none',
                zIndex: 0,
                mixBlendMode: 'color-burn'
              }}
              data-testid="debug-subcity-pattern"
            />

            {/* Left Side - Photo (Ethiopian ID style) */}
            <Box sx={{ mr: 2, display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
              <Box sx={{
                width: 100,
                height: 120,
                border: '1px solid rgba(0,0,0,0.1)',
                overflow: 'hidden',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                bgcolor: '#f8f8f8',
                boxShadow: '0 2px 4px rgba(0,0,0,0.08)',
                position: 'relative',
                borderRadius: 0.5, // Slightly rounded corners like Ethiopian ID
                mb: 1
              }}>
                <Avatar
                  src={photoUrl || fallbackImageUrl}
                  alt={idCard.citizen_name}
                  variant="square"
                  sx={{
                    width: 100,
                    height: 120,
                    objectFit: 'cover'
                  }}
                  onError={() => {
                    console.log('Image failed to load, using fallback');
                  }}
                >
                  {idCard.citizen_name?.[0]}
                </Avatar>

                {/* Security approval indicators - more subtle */}
                <Box sx={{
                  position: 'absolute',
                  bottom: 2,
                  right: 2,
                  display: 'flex',
                  alignItems: 'center',
                  gap: 0.5
                }}>
                  {/* Kebele approval icon */}
                  <SecurityIcon sx={{
                    fontSize: 12,
                    color: kebeleApproved ? '#0078d7' : '#9e9e9e',
                    opacity: kebeleApproved ? 0.8 : 0.3
                  }} />

                  {/* Subcity approval icon */}
                  <VerifiedUserIcon sx={{
                    fontSize: 12,
                    color: subcityApproved ? '#0078d7' : '#9e9e9e',
                    opacity: subcityApproved ? 0.8 : 0.3
                  }} />
                </Box>
              </Box>

              {/* ID Number Display - Ethiopian style (below photo) */}
              <Box sx={{
                textAlign: 'center',
                width: '100%',
                mb: 0.5
              }}>
                <Typography variant="caption" sx={{
                  fontWeight: 700,
                  color: '#0063b1',
                  fontSize: '0.8rem',
                  letterSpacing: '0.5px'
                }}>
                  {formatIdNumber(idCard.citizen_id_number)}
                </Typography>
              </Box>

              {/* Spacer to maintain layout */}
              <Box sx={{
                height: '10px',
                mt: 1
              }}>
              </Box>
            </Box>

            {/* Right Side - Information - Ethiopian ID style */}
            <Box sx={{ flexGrow: 1, display: 'flex', flexDirection: 'column', pl: 1, position: 'relative', zIndex: 1 }}>
              {/* Name Field - Ethiopian ID style */}
              <Box sx={{ mb: 1, pb: 0.5 }}>
                <Typography variant="caption" sx={{
                  fontWeight: 600,
                  fontSize: '0.6rem',
                  display: 'block',
                  mb: 0.25,
                  color: '#666',
                  textTransform: 'uppercase'
                }}>
                  FULL NAME / ሙሉ ስም
                </Typography>
                <Typography variant="body1" sx={{
                  fontWeight: 700,
                  color: '#000',
                  fontSize: '0.85rem',
                  borderBottom: '1px solid rgba(0,0,0,0.08)',
                  pb: 0.5
                }}>
                  {idCard.citizen_name}
                </Typography>
              </Box>

              {/* Personal details in Ethiopian ID style - single column with label/value pairs */}
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 0.5 }}>
                {/* Date of Birth */}
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <Typography variant="caption" sx={{
                    fontSize: '0.6rem',
                    color: '#666',
                    textTransform: 'uppercase',
                    mr: 1
                  }}>
                    DATE OF BIRTH / የትውልድ ቀን:
                  </Typography>
                  <Typography variant="caption" sx={{
                    fontSize: '0.7rem',
                    fontWeight: 600,
                    color: '#000',
                    flexGrow: 1
                  }}>
                    {/* Log the date of birth values for debugging */}
                    {console.log('Date of birth values in IDCardLayout:', {
                      citizen_birth_date: idCard.citizen_birth_date,
                      citizenDetails_date_of_birth: idCard.citizenDetails?.date_of_birth,
                      date_of_birth: idCard.date_of_birth
                    })}

                    {/* Use the date of birth from any available source */}
                    {idCard.citizen_birth_date ? formatDate(idCard.citizen_birth_date) :
                     idCard.citizenDetails?.date_of_birth ? formatDate(idCard.citizenDetails.date_of_birth) :
                     idCard.date_of_birth ? formatDate(idCard.date_of_birth) :
                     'N/A'}
                  </Typography>
                </Box>

                {/* Gender */}
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <Typography variant="caption" sx={{
                    fontSize: '0.6rem',
                    color: '#666',
                    textTransform: 'uppercase',
                    mr: 1
                  }}>
                    SEX / ፆታ:
                  </Typography>
                  <Typography variant="caption" sx={{
                    fontSize: '0.7rem',
                    fontWeight: 600,
                    color: '#000',
                    flexGrow: 1
                  }}>
                    {
                      (idCard.citizenDetails && (idCard.citizenDetails.gender === 'Female' || idCard.citizenDetails.gender === 'FEMALE' || idCard.citizenDetails.gender === 'F')) ||
                      (idCard.citizen_gender === 'Female' || idCard.citizen_gender === 'FEMALE' || idCard.citizen_gender === 'F') ||
                      (idCard.gender === 'Female' || idCard.gender === 'FEMALE' || idCard.gender === 'F') ? 'F / ሴት' : 'M / ወንድ'
                    }
                  </Typography>
                </Box>

                {/* Issue Date */}
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <Typography variant="caption" sx={{
                    fontSize: '0.6rem',
                    color: '#666',
                    textTransform: 'uppercase',
                    mr: 1
                  }}>
                    ISSUE DATE / የተሰጠበት ቀን:
                  </Typography>
                  <Typography variant="caption" sx={{
                    fontSize: '0.7rem',
                    fontWeight: 600,
                    color: '#000',
                    flexGrow: 1
                  }}>
                    {idCard.issue_date ? formatDate(idCard.issue_date) : formatDate(new Date().toISOString())}
                  </Typography>
                </Box>

                {/* Expiry Date */}
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <Typography variant="caption" sx={{
                    fontSize: '0.6rem',
                    color: '#666',
                    textTransform: 'uppercase',
                    mr: 1
                  }}>
                    EXPIRY DATE / የሚያበቃበት ቀን:
                  </Typography>
                  <Typography variant="caption" sx={{
                    fontSize: '0.7rem',
                    fontWeight: 600,
                    color: '#000',
                    flexGrow: 1
                  }}>
                    {idCard.expiry_date ? formatDate(idCard.expiry_date) : 'N/A'}
                  </Typography>
                </Box>

                {/* Nationality */}
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <Typography variant="caption" sx={{
                    fontSize: '0.6rem',
                    color: '#666',
                    textTransform: 'uppercase',
                    mr: 1
                  }}>
                    NATIONALITY / ዜግነት:
                  </Typography>
                  <Typography variant="caption" sx={{
                    fontSize: '0.7rem',
                    fontWeight: 600,
                    color: '#000',
                    flexGrow: 1
                  }}>
                    ETHIOPIAN / ኢትዮጵያዊ
                  </Typography>
                </Box>
              </Box>

              {/* Spacer to maintain layout */}
              <Box sx={{
                mt: 'auto',
                pt: 0.5,
                height: '10px'
              }}>
              </Box>
            </Box>
          </Box>

          {/* Micro-text security feature */}
          <Box sx={{
            position: 'absolute',
            bottom: 25,
            left: 0,
            width: '100%',
            height: '10px',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            overflow: 'hidden',
            zIndex: 1
          }}>
            <Typography variant="caption" sx={{
              fontSize: '4px',
              color: 'rgba(0,0,0,0.4)',
              whiteSpace: 'nowrap',
              letterSpacing: '0.5px',
              fontWeight: 600,
              textTransform: 'uppercase'
            }}>
              {Array(20).fill(`${cityName.toUpperCase()} CITY ADMINISTRATION OFFICIAL ID CARD `).join(' ')}
            </Typography>
          </Box>

          {/* Footer */}
          <Box sx={styles.footer}>
            <Box sx={{ display: 'flex', alignItems: 'center', maxWidth: '100%', overflow: 'hidden' }}>
              <SecurityIcon sx={{ color: '#0066b2', fontSize: 14, mr: 0.5, flexShrink: 0 }} />
              <Typography noWrap variant="caption" sx={{ fontWeight: 600, color: '#0066b2', fontSize: '0.65rem' }}>
                በ{cityName} ከተማ አስተዳደር የተሰጠ ህጋዊ መታወቂያ
              </Typography>
            </Box>
          </Box>
        </Box>
      ) : (
        /* Back Side of ID Card */
        <Box sx={styles.card}>
          {/* Back Side Header */}
          <Box sx={{
            ...styles.header,
            justifyContent: 'center',
            alignItems: 'center',
            background: `linear-gradient(135deg, ${primaryColor} 0%, ${primaryColor === '#0066b2' ? '#004c99' : primaryColor} 100%)`
          }}>
            <Box sx={{ textAlign: 'center', width: '100%' }}>
              <Typography variant="subtitle2" sx={{
                fontWeight: 700,
                fontSize: '0.6rem',
                textAlign: 'center',
                color: '#fff',
                textShadow: '0 1px 1px rgba(0,0,0,0.3)',
                lineHeight: 1.4
              }}>
                በኢትዮጵያ ፌደራላዊ ዲሞክራሲያዊ ሪፐብሊክ በአማራ ብሔራዊ ክልላዊ መንግሥት<br/>
                በ{cityName} ከተማ አስተዳደር በ{subcityName} ክ/ከተማ የ{kebeleName} አስተዳደር ጽ/ቤት
              </Typography>
              <Typography variant="subtitle2" sx={{
                fontWeight: 700,
                fontSize: '0.7rem',
                textAlign: 'center',
                color: '#fff',
                textShadow: '0 1px 1px rgba(0,0,0,0.3)',
                mt: 0.5
              }}>
                መታወቂያ ካርድ - ጀርባ
              </Typography>
            </Box>
          </Box>

          {/* Main Content - Driver's License Style */}
          <Box sx={{...styles.content, height: '210px', position: 'relative'}}>

            {/* Security Pattern Overlay for back side */}
            {/* Show watermark patterns based on approval status */}
            <>
              {/* Kebele Watermark Pattern (Left Half) */}
              {kebeleApproved && (
                <>
                  <Box
                    sx={{
                      position: 'absolute',
                      top: 0,
                      left: 0,
                      width: '50%', // Left half
                      height: '100%',
                      backgroundImage: `url(/castle-watermark.svg)`,
                      backgroundSize: 'cover',
                      backgroundPosition: 'left center',
                      opacity: 0.3, // Reduced opacity for better visibility of content
                      pointerEvents: 'none',
                      zIndex: 1,
                      mixBlendMode: 'multiply'
                    }}
                    data-testid="back-kebele-pattern-overlay"
                  />
                  <Typography
                    sx={{
                      position: 'absolute',
                      bottom: '20px',
                      left: '25%',
                      transform: 'translateX(-50%)',
                      color: 'rgba(210, 170, 90, 0.5)',
                      fontWeight: 'bold',
                      fontSize: '12px',
                      zIndex: 1,
                      pointerEvents: 'none',
                      textAlign: 'center'
                    }}
                  >
                    {idCard.tenant_name || idCard.kebele_name || 'ቀበሌ ማረጋገጫ'}
                  </Typography>
                </>
              )}

              {/* Subcity Watermark Pattern (Right Half) */}
              {subcityApproved && (
                <>
                  <Box
                    sx={{
                      position: 'absolute',
                      top: 0,
                      left: '50%', // Right half
                      width: '50%',
                      height: '100%',
                      backgroundImage: `url(/castle-watermark.svg)`,
                      backgroundSize: 'cover',
                      backgroundPosition: 'right center',
                      opacity: 0.3, // Reduced opacity for better visibility of content
                      pointerEvents: 'none',
                      zIndex: 1, // Same z-index for both halves
                      mixBlendMode: 'color-burn' // Different blend mode for visual distinction
                    }}
                    data-testid="back-subcity-pattern-overlay"
                  />
                  <Typography
                    sx={{
                      position: 'absolute',
                      bottom: '20px',
                      left: '75%',
                      transform: 'translateX(-50%)',
                      color: 'rgba(210, 170, 90, 0.5)',
                      fontWeight: 'bold',
                      fontSize: '12px',
                      zIndex: 1,
                      pointerEvents: 'none',
                      textAlign: 'center'
                    }}
                  >
                    {idCard.parent_tenant_name || idCard.subcity_name || 'ክ/ከተማ ማረጋገጫ'}
                  </Typography>
                </>
              )}

              {/* Debug Pattern Display - Always visible for testing */}
              <Box
                sx={{
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  width: '50%',
                  height: '100%',
                  backgroundImage: `url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSI1MDAiIGhlaWdodD0iMzAwIiB2aWV3Qm94PSIwIDAgNTAwIDMwMCI+PHJlY3Qgd2lkdGg9IjUwMCIgaGVpZ2h0PSIzMDAiIGZpbGw9InJnYmEoMCwgMCwgMjU1LCAwLjA1KSIvPjx0ZXh0IHg9IjEyNSIgeT0iMTUwIiBmb250LWZhbWlseT0ic2Fucy1zZXJpZiIgZm9udC1zaXplPSIyMCIgZmlsbD0icmdiYSgwLCAwLCAyNTUsIDAuMikiPktlYmVsZSBQYXR0ZXJuPC90ZXh0Pjwvc3ZnPg==)`,
                  backgroundSize: 'cover',
                  backgroundPosition: 'left center',
                  opacity: 1,
                  pointerEvents: 'none',
                  zIndex: 0,
                  mixBlendMode: 'multiply'
                }}
                data-testid="back-debug-kebele-pattern"
              />

              <Box
                sx={{
                  position: 'absolute',
                  top: 0,
                  left: '50%',
                  width: '50%',
                  height: '100%',
                  backgroundImage: `url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSI1MDAiIGhlaWdodD0iMzAwIiB2aWV3Qm94PSIwIDAgNTAwIDMwMCI+PHJlY3Qgd2lkdGg9IjUwMCIgaGVpZ2h0PSIzMDAiIGZpbGw9InJnYmEoMjU1LCAwLCAwLCAwLjA1KSIvPjx0ZXh0IHg9IjM3NSIgeT0iMTUwIiBmb250LWZhbWlseT0ic2Fucy1zZXJpZiIgZm9udC1zaXplPSIyMCIgZmlsbD0icmdiYSgyNTUsIDAsIDAsIDAuMikiPlN1YmNpdHkgUGF0dGVybjwvdGV4dD48L3N2Zz4=)`,
                  backgroundSize: 'cover',
                  backgroundPosition: 'right center',
                  opacity: 1,
                  pointerEvents: 'none',
                  zIndex: 0,
                  mixBlendMode: 'color-burn'
                }}
                data-testid="back-debug-subcity-pattern"
              />
            </>
            {/* QR Code at the top left */}
            <Box sx={{
              width: '100%',
              display: 'flex',
              justifyContent: 'flex-start',
              alignItems: 'flex-start',
              mt: 1,
              ml: 1
            }}>
              <Box sx={{
                width: 70,
                height: 70,
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center',
                bgcolor: 'white',
                border: '1px solid rgba(0,0,0,0.15)',
                borderRadius: 1,
                p: 0.5,
                boxShadow: '0 1px 3px rgba(0,0,0,0.1)',
                position: 'relative',
              }}>
              {idCard.citizen_id_number ? (
                <>
                  <Box sx={{ position: 'relative', width: 60, height: 60 }}>
                    <QRCode
                      value={`https://id.gondar.gov.et/verify/${formatIdNumber(idCard.citizen_id_number)}`}
                      size={60}
                      style={{ height: 'auto', maxWidth: '100%', width: '100%' }}
                      viewBox={`0 0 256 256`}
                      level="M"
                      fgColor={secondaryColor}
                    />
                    {/* Logo overlay in the center of QR code */}
                    <Box sx={{
                      position: 'absolute',
                      top: '50%',
                      left: '50%',
                      transform: 'translate(-50%, -50%)',
                      width: '20px',
                      height: '20px',
                      bgcolor: 'white',
                      borderRadius: '50%',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      boxShadow: '0 0 3px rgba(0,0,0,0.1)'
                    }}>
                      <SecurityIcon sx={{ fontSize: 12, color: primaryColor }} />
                    </Box>
                  </Box>
                </>
              ) : (
                <>
                  <Box sx={{ width: 60, height: 60, bgcolor: '#f5f5f5', display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center' }}>
                    <QrCodeIcon sx={{ fontSize: 35, color: '#666' }} />
                  </Box>
                </>
              )}
              </Box>
            </Box>

            {/* Information content - no divider, reduced spacing */}
            <Box sx={{ display: 'flex', width: '100%', position: 'relative', zIndex: 5, mt: 0.5, ml: -1 }}>

              {/* Information Section - Driver's License Style */}
              <Box sx={{ width: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'space-between', pl: 0.5, pr: 2 }}>
              {/* Important Information */}
              <Box sx={{ mt: -1, ml: -1 }}>
                <Typography variant="subtitle2" sx={{
                  fontWeight: 700,
                  color: '#0066b2',
                  mb: 0.5,
                  fontSize: '0.75rem',
                  display: 'flex',
                  alignItems: 'center',
                  '&::before': {
                    content: '""',
                    display: 'inline-block',
                    width: '6px',
                    height: '6px',
                    borderRadius: '50%',
                    background: 'linear-gradient(135deg, #0066b2, #0077cc)',
                    marginRight: '6px',
                    boxShadow: '0 1px 2px rgba(0,0,0,0.1)'
                  }
                }}>
                  አስፈላጊ መረጃ / IMPORTANT INFORMATION
                </Typography>
                <Box sx={{
                  bgcolor: 'rgba(0,102,178,0.05)',
                  p: 0.5,
                  borderRadius: 1,
                  mb: 0.5,
                  border: '1px solid rgba(0,102,178,0.08)',
                  boxShadow: 'inset 0 1px 0 rgba(255,255,255,0.8), 0 1px 3px rgba(0,0,0,0.03)',
                  width: 'calc(100% + 10px)'
                }}>
                  <Typography variant="caption" sx={{ display: 'block', fontSize: '0.65rem', mb: 0.5 }}>
                    1. ይህ መታወቂያ ካርድ የመንግስት ንብረት ነው።
                  </Typography>
                  <Typography variant="caption" sx={{ display: 'block', fontSize: '0.65rem', mb: 0.5 }}>
                    2. ካርዱን ማበላሸት፣ ማጭበርበር ወይም ማሻሻል ህገወጥ ነው።
                  </Typography>
                  <Typography variant="caption" sx={{ display: 'block', fontSize: '0.65rem' }}>
                    3. ካርዱ ከጠፋ ወዲያውኑ ለቀበሌ አስተዳደር ማሳወቅ ይኖርብዎታል።
                  </Typography>
                </Box>
              </Box>

              {/* Contact Information */}
              <Box sx={{ mt: 0.5, ml: -1 }}>
                <Typography variant="subtitle2" sx={{
                  fontWeight: 700,
                  color: '#0066b2',
                  mb: 0.5,
                  fontSize: '0.75rem',
                  display: 'flex',
                  alignItems: 'center',
                  '&::before': {
                    content: '""',
                    display: 'inline-block',
                    width: '6px',
                    height: '6px',
                    borderRadius: '50%',
                    background: 'linear-gradient(135deg, #0066b2, #0077cc)',
                    marginRight: '6px',
                    boxShadow: '0 1px 2px rgba(0,0,0,0.1)'
                  }
                }}>
                  የመገኛ አድራሻ / CONTACT
                </Typography>
                <Box sx={{
                  display: 'flex',
                  flexDirection: 'row',
                  flexWrap: 'wrap',
                  gap: 0.5,
                  bgcolor: 'rgba(0,102,178,0.05)',
                  p: 0.5,
                  borderRadius: 1,
                  border: '1px solid rgba(0,102,178,0.08)',
                  width: 'calc(100% + 10px)'
                }}>
                  <Box sx={{ width: '48%' }}>
                    <Typography variant="caption" sx={{ display: 'flex', alignItems: 'center', fontSize: '0.65rem' }}>
                      <PhoneIcon sx={{ fontSize: 12, mr: 0.5, color: '#0066b2' }} />
                      +251-58-119-7060
                    </Typography>
                  </Box>
                  <Box sx={{ width: '48%' }}>
                    <Typography variant="caption" sx={{ display: 'flex', alignItems: 'center', fontSize: '0.65rem' }}>
                      <EmailIcon sx={{ fontSize: 12, mr: 0.5, color: '#0066b2' }} />
                      <EMAIL>
                    </Typography>
                  </Box>
                  <Box sx={{ width: '100%' }}>
                    <Typography variant="caption" sx={{ display: 'flex', alignItems: 'center', fontSize: '0.65rem' }}>
                      <LanguageIcon sx={{ fontSize: 12, mr: 0.5, color: '#0066b2' }} />
                      www.gondar.gov.et
                    </Typography>
                  </Box>
                </Box>
              </Box>

              {/* Barcode Section */}
              <Box sx={{ mb: 0.5, mt: 1, ml: -1 }}>
                <Typography variant="subtitle2" sx={{
                  fontWeight: 700,
                  color: '#0066b2',
                  mb: 0.5,
                  fontSize: '0.75rem',
                  display: 'flex',
                  alignItems: 'center',
                  '&::before': {
                    content: '""',
                    display: 'inline-block',
                    width: '6px',
                    height: '6px',
                    borderRadius: '50%',
                    background: 'linear-gradient(135deg, #0066b2, #0077cc)',
                    marginRight: '6px',
                    boxShadow: '0 1px 2px rgba(0,0,0,0.1)'
                  }
                }}>
                  ባርኮድ / BARCODE
                </Typography>
                <Box sx={{
                  bgcolor: 'white',
                  p: 1,
                  borderRadius: 1,
                  border: '1px solid rgba(0,0,0,0.1)',
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                  height: '40px',
                  position: 'relative',
                  overflow: 'hidden',
                  width: 'calc(100% + 10px)'
                }}>
                  {/* Simulated barcode pattern */}
                  <Box sx={{
                    width: '100%',
                    height: '100%',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}>
                    {Array.from({ length: 30 }).map((_, index) => (
                      <Box
                        key={index}
                        sx={{
                          height: Math.random() * 20 + 10,
                          width: '2px',
                          mx: '1px',
                          bgcolor: '#000',
                          opacity: Math.random() * 0.5 + 0.5
                        }}
                      />
                    ))}
                  </Box>

                  {/* ID number overlay */}
                  <Typography
                    variant="caption"
                    sx={{
                      position: 'absolute',
                      bottom: 2,
                      fontWeight: 600,
                      fontSize: '0.6rem',
                      color: '#000'
                    }}
                  >
                    {idCard.citizen_id_number || 'ID0000000000'}
                  </Typography>
                </Box>
              </Box>

              {/* Signature Area */}
              <Box sx={{
                mt: 1,
                pt: 0.5,
                borderTop: '1px dashed rgba(0,0,0,0.1)',
                display: 'flex',
                justifyContent: 'flex-end',
                alignItems: 'center'
              }}>
                <Box sx={{
                  width: 120,
                  height: 30,
                  borderBottom: '1px solid rgba(0,0,0,0.3)',
                  display: 'flex',
                  alignItems: 'flex-end',
                  justifyContent: 'center',
                  pb: 0.5
                }}>
                  <Typography variant="caption" sx={{ fontSize: '0.6rem', color: 'rgba(0,0,0,0.5)' }}>
                    Signature
                  </Typography>
                </Box>
              </Box>
              </Box>
            </Box>
          </Box>

          {/* Footer */}
          <Box sx={styles.footer}>
            <Box sx={{ display: 'flex', alignItems: 'center', maxWidth: '100%', overflow: 'hidden' }}>
              <SecurityIcon sx={{ color: '#0066b2', fontSize: 14, mr: 0.5, flexShrink: 0 }} />
              <Typography noWrap variant="caption" sx={{ fontWeight: 600, color: '#0066b2', fontSize: '0.65rem' }}>
                በ{cityName} ከተማ አስተዳደር የተሰጠ ህጋዊ መታወቂያ
              </Typography>
            </Box>
          </Box>
        </Box>
      )}
    </>
  );
};

export default IDCardLayout;
