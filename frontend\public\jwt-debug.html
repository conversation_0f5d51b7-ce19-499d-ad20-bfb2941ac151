<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>JWT Authentication Debug</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      line-height: 1.6;
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
    }
    h1, h2 {
      color: #333;
    }
    .container {
      display: flex;
      flex-wrap: wrap;
      gap: 20px;
    }
    .panel {
      flex: 1;
      min-width: 300px;
      border: 1px solid #ddd;
      border-radius: 5px;
      padding: 15px;
      margin-bottom: 20px;
    }
    .form-group {
      margin-bottom: 15px;
    }
    label {
      display: block;
      margin-bottom: 5px;
      font-weight: bold;
    }
    input, select {
      width: 100%;
      padding: 8px;
      border: 1px solid #ddd;
      border-radius: 4px;
    }
    button {
      background-color: #4CAF50;
      color: white;
      border: none;
      padding: 10px 15px;
      border-radius: 4px;
      cursor: pointer;
      margin-right: 10px;
    }
    button:hover {
      background-color: #45a049;
    }
    pre {
      background-color: #f5f5f5;
      padding: 10px;
      border-radius: 4px;
      overflow-x: auto;
      white-space: pre-wrap;
      word-wrap: break-word;
    }
    .error {
      color: red;
    }
    .success {
      color: green;
    }
    .hidden {
      display: none;
    }
  </style>
</head>
<body>
  <h1>JWT Authentication Debug</h1>
  
  <div class="container">
    <div class="panel">
      <h2>Login</h2>
      <div class="form-group">
        <label for="email">Email:</label>
        <input type="email" id="email" placeholder="Enter your email">
      </div>
      <div class="form-group">
        <label for="password">Password:</label>
        <input type="password" id="password" placeholder="Enter your password">
      </div>
      <div class="form-group">
        <label for="schema">Schema Name:</label>
        <input type="text" id="schema" placeholder="Enter schema name (e.g., kebele14)">
      </div>
      <div class="form-group">
        <button id="login-btn">Login</button>
        <button id="clear-btn">Clear Storage</button>
      </div>
      <div id="login-result" class="hidden">
        <h3>Login Result:</h3>
        <pre id="login-response"></pre>
      </div>
    </div>
    
    <div class="panel">
      <h2>JWT Debug</h2>
      <div class="form-group">
        <button id="debug-btn">Run JWT Debug</button>
      </div>
      <div id="debug-result" class="hidden">
        <h3>Debug Result:</h3>
        <pre id="debug-response"></pre>
      </div>
    </div>
  </div>
  
  <div class="container">
    <div class="panel">
      <h2>Current Storage</h2>
      <div class="form-group">
        <button id="show-storage-btn">Show Storage</button>
      </div>
      <div id="storage-result" class="hidden">
        <h3>LocalStorage:</h3>
        <pre id="storage-response"></pre>
        <h3>Cookies:</h3>
        <pre id="cookies-response"></pre>
      </div>
    </div>
    
    <div class="panel">
      <h2>Test Endpoints</h2>
      <div class="form-group">
        <button id="test-validate-btn">Test Validate Token</button>
        <button id="test-refresh-btn">Test Refresh Token</button>
      </div>
      <div id="test-result" class="hidden">
        <h3>Test Result:</h3>
        <pre id="test-response"></pre>
      </div>
    </div>
  </div>
  
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // Add event listeners
      document.getElementById('login-btn').addEventListener('click', handleLogin);
      document.getElementById('clear-btn').addEventListener('click', clearStorage);
      document.getElementById('debug-btn').addEventListener('click', runJwtDebug);
      document.getElementById('show-storage-btn').addEventListener('click', showStorage);
      document.getElementById('test-validate-btn').addEventListener('click', testValidateToken);
      document.getElementById('test-refresh-btn').addEventListener('click', testRefreshToken);
    });
    
    async function handleLogin() {
      const email = document.getElementById('email').value.trim();
      const password = document.getElementById('password').value;
      const schema = document.getElementById('schema').value.trim();
      
      const loginResultDiv = document.getElementById('login-result');
      const loginResponsePre = document.getElementById('login-response');
      
      if (!email || !password) {
        alert('Please enter both email and password');
        return;
      }
      
      // Show the result div
      loginResultDiv.classList.remove('hidden');
      loginResponsePre.textContent = 'Logging in...';
      
      try {
        // Clear any existing authentication data
        localStorage.removeItem('token');
        localStorage.removeItem('schema_name');
        localStorage.removeItem('tokenStore');
        
        // Set schema cookie
        document.cookie = `schema_name=${encodeURIComponent(schema)}; path=/; SameSite=None; Secure`;
        
        // Create request headers
        const headers = new Headers({
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        });
        
        if (schema) {
          headers.set('X-Schema-Name', schema);
        }
        
        // Make the login request
        const response = await fetch('/api/jwt/login/', {
          method: 'POST',
          headers: headers,
          body: JSON.stringify({
            email: email,
            password: password,
            schema_name: schema
          }),
          credentials: 'include'
        });
        
        // Get response details
        const status = response.status;
        const statusText = response.statusText;
        
        let responseData;
        try {
          responseData = await response.json();
        } catch (e) {
          responseData = await response.text();
        }
        
        // Display the response
        loginResponsePre.textContent = JSON.stringify({
          status,
          statusText,
          data: responseData
        }, null, 2);
        
        // If login successful, store the tokens
        if (status === 200 && responseData.access_token) {
          localStorage.setItem('jwt_access_token', responseData.access_token);
          localStorage.setItem('jwt_refresh_token', responseData.refresh_token);
          localStorage.setItem('schema_name', schema || 'public');
          
          loginResponsePre.classList.add('success');
          loginResponsePre.classList.remove('error');
        } else {
          loginResponsePre.classList.add('error');
          loginResponsePre.classList.remove('success');
        }
      } catch (error) {
        loginResponsePre.textContent = `Error: ${error.message}`;
        loginResponsePre.classList.add('error');
        loginResponsePre.classList.remove('success');
      }
    }
    
    function clearStorage() {
      // Clear localStorage
      localStorage.clear();
      
      // Clear cookies
      document.cookie.split(";").forEach(function(c) {
        document.cookie = c.replace(/^ +/, "").replace(/=.*/, "=;expires=" + new Date().toUTCString() + ";path=/");
      });
      
      alert('Storage cleared');
    }
    
    async function runJwtDebug() {
      const debugResultDiv = document.getElementById('debug-result');
      const debugResponsePre = document.getElementById('debug-response');
      
      // Show the result div
      debugResultDiv.classList.remove('hidden');
      debugResponsePre.textContent = 'Running JWT debug...';
      
      try {
        // Get access token from localStorage
        const accessToken = localStorage.getItem('jwt_access_token');
        
        // Create request headers
        const headers = new Headers({
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        });
        
        // Add authorization header if token exists
        if (accessToken) {
          headers.set('Authorization', `Bearer ${accessToken}`);
        }
        
        // Get schema from localStorage
        const schema = localStorage.getItem('schema_name');
        if (schema) {
          headers.set('X-Schema-Name', schema);
        }
        
        // Make the debug request
        const response = await fetch('/api/jwt/debug/', {
          method: 'GET',
          headers: headers,
          credentials: 'include'
        });
        
        // Get response details
        const status = response.status;
        const statusText = response.statusText;
        
        let responseData;
        try {
          responseData = await response.json();
        } catch (e) {
          responseData = await response.text();
        }
        
        // Display the response
        debugResponsePre.textContent = JSON.stringify({
          status,
          statusText,
          data: responseData
        }, null, 2);
      } catch (error) {
        debugResponsePre.textContent = `Error: ${error.message}`;
      }
    }
    
    function showStorage() {
      const storageResultDiv = document.getElementById('storage-result');
      const storageResponsePre = document.getElementById('storage-response');
      const cookiesResponsePre = document.getElementById('cookies-response');
      
      // Show the result div
      storageResultDiv.classList.remove('hidden');
      
      // Get localStorage items
      const localStorage = {};
      for (let i = 0; i < window.localStorage.length; i++) {
        const key = window.localStorage.key(i);
        localStorage[key] = window.localStorage.getItem(key);
      }
      
      // Display localStorage
      storageResponsePre.textContent = JSON.stringify(localStorage, null, 2);
      
      // Display cookies
      cookiesResponsePre.textContent = document.cookie;
    }
    
    async function testValidateToken() {
      await testEndpoint('/api/jwt/validate-token/');
    }
    
    async function testRefreshToken() {
      await testEndpoint('/api/jwt/refresh-token/', 'POST');
    }
    
    async function testEndpoint(url, method = 'GET') {
      const testResultDiv = document.getElementById('test-result');
      const testResponsePre = document.getElementById('test-response');
      
      // Show the result div
      testResultDiv.classList.remove('hidden');
      testResponsePre.textContent = `Testing ${url}...`;
      
      try {
        // Get access token from localStorage
        const accessToken = localStorage.getItem('jwt_access_token');
        const refreshToken = localStorage.getItem('jwt_refresh_token');
        
        // Create request headers
        const headers = new Headers({
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        });
        
        // Add authorization header if token exists
        if (accessToken) {
          headers.set('Authorization', `Bearer ${accessToken}`);
        }
        
        // Get schema from localStorage
        const schema = localStorage.getItem('schema_name');
        if (schema) {
          headers.set('X-Schema-Name', schema);
        }
        
        // Prepare request options
        const options = {
          method: method,
          headers: headers,
          credentials: 'include'
        };
        
        // Add body for POST requests
        if (method === 'POST' && refreshToken) {
          options.body = JSON.stringify({
            refresh_token: refreshToken
          });
        }
        
        // Make the request
        const response = await fetch(url, options);
        
        // Get response details
        const status = response.status;
        const statusText = response.statusText;
        
        let responseData;
        try {
          responseData = await response.json();
        } catch (e) {
          responseData = await response.text();
        }
        
        // Display the response
        testResponsePre.textContent = JSON.stringify({
          status,
          statusText,
          data: responseData
        }, null, 2);
        
        // If refresh token response contains new tokens, update localStorage
        if (method === 'POST' && status === 200 && responseData.access_token) {
          localStorage.setItem('jwt_access_token', responseData.access_token);
          if (responseData.refresh_token) {
            localStorage.setItem('jwt_refresh_token', responseData.refresh_token);
          }
          testResponsePre.classList.add('success');
          testResponsePre.classList.remove('error');
        }
      } catch (error) {
        testResponsePre.textContent = `Error: ${error.message}`;
        testResponsePre.classList.add('error');
        testResponsePre.classList.remove('success');
      }
    }
  </script>
</body>
</html>
