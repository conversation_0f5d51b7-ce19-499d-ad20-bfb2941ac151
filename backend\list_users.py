import os
import django
import sys

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'neocamelot.settings')
django.setup()

from django.db import connection
from django.contrib.auth import get_user_model
User = get_user_model()

# Set to public schema
connection.set_schema_to_public()

# List all users
users = User.objects.all()
print(f"Total users: {users.count()}")
for user in users:
    print(f"User ID: {user.id}, Email: {user.email}, First Name: {user.first_name}, Last Name: {user.last_name}")
