<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Direct Authentication Fix</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
      line-height: 1.6;
    }
    h1, h2, h3 {
      color: #3f51b5;
    }
    .card {
      border: 1px solid #ddd;
      border-radius: 8px;
      padding: 20px;
      margin-bottom: 20px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    button {
      background-color: #3f51b5;
      color: white;
      border: none;
      padding: 10px 20px;
      border-radius: 4px;
      cursor: pointer;
      font-size: 16px;
      margin-right: 10px;
      margin-bottom: 10px;
    }
    button:hover {
      background-color: #303f9f;
    }
    pre {
      background-color: #f5f5f5;
      padding: 10px;
      border-radius: 4px;
      overflow-x: auto;
      white-space: pre-wrap;
      word-break: break-all;
    }
    .success { color: green; }
    .error { color: red; }
    .warning { color: orange; }
    input[type="text"],
    input[type="password"] {
      width: 100%;
      padding: 10px;
      margin: 5px 0 15px;
      border: 1px solid #ddd;
      border-radius: 4px;
      box-sizing: border-box;
    }
    label {
      font-weight: bold;
    }
    .form-group {
      margin-bottom: 15px;
    }
    .note {
      background-color: #fff3cd;
      border-left: 4px solid #ffc107;
      padding: 10px;
      margin-bottom: 15px;
    }
    .step {
      background-color: #e8f4fd;
      border-left: 4px solid #2196f3;
      padding: 10px;
      margin-bottom: 15px;
    }
  </style>
</head>
<body>
  <h1>Direct Authentication Fix</h1>
  
  <div class="note">
    <p><strong>Important:</strong> This page directly fixes the specific issues identified in the backend logs:</p>
    <ol>
      <li>Duplicated X-Schema-Name header: <code>kebele 14, kebele 14</code></li>
      <li>Duplicated Authorization header: <code>Token value, Token value</code></li>
      <li>Token key extraction issue with trailing comma</li>
    </ol>
  </div>
  
  <div class="card">
    <h2>Step 1: Clear Current Authentication Data</h2>
    <p>This will clear all current authentication data to start fresh.</p>
    <button id="clear-auth">Clear Authentication Data</button>
    <div id="clear-result" style="margin-top: 10px;"></div>
  </div>
  
  <div class="card">
    <h2>Step 2: Set New Authentication Data</h2>
    <div class="form-group">
      <label for="token">Authentication Token:</label>
      <input type="text" id="token" placeholder="Enter your authentication token">
    </div>
    <button id="set-auth">Set Authentication Data</button>
    <div id="set-result" style="margin-top: 10px;"></div>
  </div>
  
  <div class="card">
    <h2>Step 3: Test API Request</h2>
    <button id="test-api">Test API Request</button>
    <div id="api-result" style="margin-top: 10px; display: none;"></div>
  </div>
  
  <div class="card">
    <h2>Step 4: Login to Generate New Token</h2>
    <div class="form-group">
      <label for="email">Email:</label>
      <input type="text" id="email" placeholder="Enter your email">
    </div>
    <div class="form-group">
      <label for="password">Password:</label>
      <input type="password" id="password" placeholder="Enter your password">
    </div>
    <button id="login">Login</button>
    <div id="login-result" style="margin-top: 10px; display: none;"></div>
  </div>
  
  <div class="card">
    <h2>Navigation</h2>
    <button onclick="window.location.href = '/'">Go to Home</button>
    <button onclick="window.location.href = '/citizens'">Go to Citizens</button>
  </div>
  
  <script>
    // Constants
    const SCHEMA_NAME = 'kebele 14';
    const URL_SCHEMA = 'kebele_14';
    
    // Initialize the page
    document.addEventListener('DOMContentLoaded', function() {
      // Add event listeners
      document.getElementById('clear-auth').addEventListener('click', clearAuth);
      document.getElementById('set-auth').addEventListener('click', setAuth);
      document.getElementById('test-api').addEventListener('click', testApi);
      document.getElementById('login').addEventListener('click', login);
      
      // Check if token exists
      const token = localStorage.getItem('token');
      if (token) {
        document.getElementById('token').value = token;
      }
    });
    
    // Clear authentication data
    function clearAuth() {
      const clearResultDiv = document.getElementById('clear-result');
      
      try {
        // Clear localStorage
        localStorage.removeItem('token');
        localStorage.removeItem('schema_name');
        localStorage.removeItem('tokenStore');
        localStorage.removeItem('tenant');
        localStorage.removeItem('user');
        
        // Clear cookies
        document.cookie.split(';').forEach(function(c) {
          document.cookie = c.trim().split('=')[0] + '=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
        });
        
        clearResultDiv.innerHTML = '<p class="success">Authentication data cleared successfully!</p>';
      } catch (error) {
        clearResultDiv.innerHTML = `<p class="error">Error clearing authentication data: ${error.message}</p>`;
      }
    }
    
    // Set authentication data
    function setAuth() {
      const token = document.getElementById('token').value.trim();
      const setResultDiv = document.getElementById('set-result');
      
      if (!token) {
        setResultDiv.innerHTML = '<p class="error">Please enter a token</p>';
        return;
      }
      
      try {
        // Clean the token (remove ALL spaces and commas)
        const cleanToken = token.replace(/[\s,]+/g, '');
        
        // Store the token in localStorage
        localStorage.setItem('token', cleanToken);
        
        // Store the schema name
        localStorage.setItem('schema_name', SCHEMA_NAME);
        
        // Set the schema cookie
        document.cookie = `schema_name=${encodeURIComponent(SCHEMA_NAME)}; path=/; SameSite=Lax`;
        
        // Create a basic tenant object
        const tenant = {
          schema_name: SCHEMA_NAME,
          name: SCHEMA_NAME
        };
        localStorage.setItem('tenant', JSON.stringify(tenant));
        
        // Create a tokenStore
        const tokenStore = {};
        tokenStore[SCHEMA_NAME] = cleanToken;
        localStorage.setItem('tokenStore', JSON.stringify(tokenStore));
        
        setResultDiv.innerHTML = `
          <p class="success">Authentication data set successfully!</p>
          <p><strong>Original token:</strong> ${token}</p>
          <p><strong>Cleaned token:</strong> ${cleanToken}</p>
          <p><strong>Schema name:</strong> ${SCHEMA_NAME}</p>
        `;
      } catch (error) {
        setResultDiv.innerHTML = `<p class="error">Error setting authentication data: ${error.message}</p>`;
      }
    }
    
    // Test API request
    async function testApi() {
      const apiResultDiv = document.getElementById('api-result');
      apiResultDiv.style.display = 'block';
      apiResultDiv.innerHTML = '<p>Testing API request...</p>';
      
      try {
        // Get the token from localStorage
        const token = localStorage.getItem('token');
        if (!token) {
          apiResultDiv.innerHTML = '<p class="error">No token found in localStorage. Please set authentication data first.</p>';
          return;
        }
        
        // Create the URL
        const url = `/api/tenant/${URL_SCHEMA}/citizens/?limit=1`;
        
        // Create a custom fetch function to ensure headers are correct
        const response = await customFetch(url);
        
        // Get response details
        const status = response.status;
        const statusText = response.statusText;
        
        let responseData;
        try {
          responseData = await response.json();
        } catch (e) {
          responseData = await response.text();
        }
        
        // Display the response
        apiResultDiv.innerHTML = `
          <h3>API Request Result</h3>
          <p><strong>URL:</strong> ${url}</p>
          <p><strong>Status:</strong> ${status} ${statusText}</p>
          <p><strong>Response:</strong></p>
          <pre>${typeof responseData === 'object' ? JSON.stringify(responseData, null, 2) : responseData}</pre>
        `;
        
        // If successful, show a success message
        if (response.ok) {
          apiResultDiv.innerHTML += '<p class="success">API request successful!</p>';
        }
      } catch (error) {
        apiResultDiv.innerHTML = `<p class="error">Error testing API: ${error.message}</p>`;
      }
    }
    
    // Login to generate new token
    async function login() {
      const email = document.getElementById('email').value.trim();
      const password = document.getElementById('password').value;
      const loginResultDiv = document.getElementById('login-result');
      
      if (!email || !password) {
        alert('Please enter both email and password');
        return;
      }
      
      loginResultDiv.style.display = 'block';
      loginResultDiv.innerHTML = '<p>Logging in...</p>';
      
      try {
        // Clear current authentication data
        clearAuth();
        
        // Create the login request
        const response = await fetch('/api/login/', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'X-Schema-Name': SCHEMA_NAME
          },
          body: JSON.stringify({
            email,
            password,
            schema_name: SCHEMA_NAME
          })
        });
        
        // Get response details
        const status = response.status;
        const statusText = response.statusText;
        
        let responseData;
        try {
          responseData = await response.json();
        } catch (e) {
          responseData = await response.text();
        }
        
        // Display the response
        loginResultDiv.innerHTML = `
          <h3>Login Result</h3>
          <p><strong>Status:</strong> ${status} ${statusText}</p>
          <p><strong>Response:</strong></p>
          <pre>${typeof responseData === 'object' ? JSON.stringify(responseData, null, 2) : responseData}</pre>
        `;
        
        // If login successful, store the token
        if (response.ok && responseData && responseData.token) {
          // Clean the token
          const cleanToken = responseData.token.replace(/[\s,]+/g, '');
          
          // Store the token in localStorage
          localStorage.setItem('token', cleanToken);
          
          // Store the schema name
          localStorage.setItem('schema_name', SCHEMA_NAME);
          
          // Set the schema cookie
          document.cookie = `schema_name=${encodeURIComponent(SCHEMA_NAME)}; path=/; SameSite=Lax`;
          
          // Store user data if available
          if (responseData.user) {
            localStorage.setItem('user', JSON.stringify(responseData.user));
          }
          
          // Store tenant data if available
          if (responseData.tenant) {
            localStorage.setItem('tenant', JSON.stringify(responseData.tenant));
          } else {
            // Create a basic tenant object
            const tenant = {
              schema_name: SCHEMA_NAME,
              name: SCHEMA_NAME
            };
            localStorage.setItem('tenant', JSON.stringify(tenant));
          }
          
          // Create a tokenStore
          const tokenStore = {};
          tokenStore[SCHEMA_NAME] = cleanToken;
          localStorage.setItem('tokenStore', JSON.stringify(tokenStore));
          
          loginResultDiv.innerHTML += `
            <p class="success">Login successful! Token stored.</p>
            <p><strong>Token:</strong> ${cleanToken}</p>
          `;
          
          // Update the token input field
          document.getElementById('token').value = cleanToken;
        }
      } catch (error) {
        loginResultDiv.innerHTML = `<p class="error">Error logging in: ${error.message}</p>`;
      }
    }
    
    // Custom fetch function to ensure headers are correct
    async function customFetch(url, options = {}) {
      // Get the token from localStorage
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('No token found in localStorage');
      }
      
      // Create headers
      const headers = new Headers();
      
      // Set the Authorization header with a clean token (no spaces, no commas)
      const cleanToken = token.replace(/[\s,]+/g, '');
      headers.set('Authorization', `Token ${cleanToken}`);
      
      // Set the X-Schema-Name header
      headers.set('X-Schema-Name', SCHEMA_NAME);
      
      // Set the Content-Type header
      headers.set('Content-Type', 'application/json');
      
      // Set the schema cookie
      document.cookie = `schema_name=${encodeURIComponent(SCHEMA_NAME)}; path=/; SameSite=Lax`;
      
      // Log the request details
      console.log('Making request to:', url);
      console.log('Headers:', Object.fromEntries(headers.entries()));
      
      // Make the request
      const response = await fetch(url, {
        ...options,
        headers,
        credentials: 'include'
      });
      
      return response;
    }
  </script>
</body>
</html>
