import React, { useState, useEffect } from 'react';
import { Box, Container, Typography, Paper, Divider, Alert } from '@mui/material';
import EnhancedTenantSelector from '../components/EnhancedTenantSelector';
import { getCurrentSchema } from '../services/tokenService';
import PageBanner from '../components/PageBanner';
import LocationCityIcon from '@mui/icons-material/LocationCity';

// Tenant interface
interface Tenant {
  id: number;
  name: string;
  schema_name: string;
  schema_type: string;
  parent_id: number | null;
}

const TenantSelection: React.FC = () => {
  const [currentSchema, setCurrentSchema] = useState<string | null>(null);
  const [selectedTenant, setSelectedTenant] = useState<Tenant | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Get the current schema on component mount
  useEffect(() => {
    const schema = getCurrentSchema();
    setCurrentSchema(schema);
  }, []);

  // Handle tenant selection
  const handleTenantSelected = (tenant: Tenant) => {
    setSelectedTenant(tenant);
    setSuccess(`Successfully switched to tenant: ${tenant.name} (${tenant.schema_name})`);

    // Update the current schema
    setCurrentSchema(tenant.schema_name);

    // Clear the success message after 5 seconds
    setTimeout(() => {
      setSuccess(null);
    }, 5000);
  };

  return (
    <Box sx={{ bgcolor: '#f8f9fa', minHeight: '100vh' }}>
      <PageBanner
        title="Tenant Selection"
        subtitle="Choose a tenant to work with"
        icon={<LocationCityIcon sx={{ fontSize: 50, color: 'white' }} />}
        actionContent={
          <Box sx={{ textAlign: 'center', width: '100%' }}>
            <Typography variant="body1" sx={{ color: 'white', fontWeight: 500 }}>
              Select a tenant to set the context for all API requests. This will ensure you're working with the correct data.
            </Typography>
          </Box>
        }
      />

      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Paper
          elevation={1}
          sx={{
            p: 4,
            borderRadius: 2,
            boxShadow: '0 4px 12px rgba(0,0,0,0.1)',
            bgcolor: 'white'
          }}
        >
          {success && (
            <Alert severity="success" sx={{ mb: 3 }}>
              {success}
            </Alert>
          )}

          <Box sx={{ mb: 3 }}>
            <Typography variant="h5" gutterBottom sx={{ fontWeight: 600 }}>
              Tenant Hierarchy
            </Typography>
            <Typography variant="body1" color="textSecondary">
              The system has a three-level hierarchy: City, Subcity, and Kebele. Select the tenant you want to work with.
            </Typography>
            <Divider sx={{ my: 2 }} />
          </Box>

          <EnhancedTenantSelector
            onTenantSelected={handleTenantSelected}
            showHierarchy={true}
            currentTenantSchema={currentSchema}
          />
        </Paper>
      </Container>
    </Box>
  );
};

export default TenantSelection;
