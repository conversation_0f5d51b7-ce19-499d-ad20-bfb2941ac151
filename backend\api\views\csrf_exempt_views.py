"""
CSRF exempt versions of the citizen relation views.
This is a temporary solution to allow the frontend to make POST requests to these endpoints.
"""
from django.views.decorators.csrf import csrf_exempt
from rest_framework.decorators import api_view
from rest_framework.response import Response
from rest_framework import status
from .citizen_relations import add_spouse, add_parent, add_child, add_emergency_contact, add_document

# Create wrapper functions that handle OPTIONS requests
@csrf_exempt
@api_view(['POST', 'OPTIONS'])
def add_spouse_csrf_exempt(request, citizen_id):
    """CSRF exempt wrapper for add_spouse that handles OPTIONS requests"""
    if request.method == 'OPTIONS':
        response = Response()
        response['Access-Control-Allow-Origin'] = 'http://localhost:5173'
        response['Access-Control-Allow-Methods'] = 'POST, OPTIONS'
        response['Access-Control-Allow-Headers'] = 'Content-Type, Authorization, X-Requested-With, X-Schema-Name, X-CSRFToken'
        response['Access-Control-Allow-Credentials'] = 'true'
        response['Access-Control-Max-Age'] = '86400'  # 24 hours
        return response
    return add_spouse(request, citizen_id)

@csrf_exempt
@api_view(['POST', 'OPTIONS'])
def add_parent_csrf_exempt(request, citizen_id):
    """CSRF exempt wrapper for add_parent that handles OPTIONS requests"""
    if request.method == 'OPTIONS':
        response = Response()
        response['Access-Control-Allow-Origin'] = 'http://localhost:5173'
        response['Access-Control-Allow-Methods'] = 'POST, OPTIONS'
        response['Access-Control-Allow-Headers'] = 'Content-Type, Authorization, X-Requested-With, X-Schema-Name, X-CSRFToken'
        response['Access-Control-Allow-Credentials'] = 'true'
        response['Access-Control-Max-Age'] = '86400'  # 24 hours
        return response
    return add_parent(request, citizen_id)

@csrf_exempt
@api_view(['POST', 'OPTIONS'])
def add_child_csrf_exempt(request, citizen_id):
    """CSRF exempt wrapper for add_child that handles OPTIONS requests"""
    if request.method == 'OPTIONS':
        response = Response()
        response['Access-Control-Allow-Origin'] = 'http://localhost:5173'
        response['Access-Control-Allow-Methods'] = 'POST, OPTIONS'
        response['Access-Control-Allow-Headers'] = 'Content-Type, Authorization, X-Requested-With, X-Schema-Name, X-CSRFToken'
        response['Access-Control-Allow-Credentials'] = 'true'
        response['Access-Control-Max-Age'] = '86400'  # 24 hours
        return response
    return add_child(request, citizen_id)

@csrf_exempt
@api_view(['POST', 'OPTIONS'])
def add_emergency_contact_csrf_exempt(request, citizen_id):
    """CSRF exempt wrapper for add_emergency_contact that handles OPTIONS requests"""
    if request.method == 'OPTIONS':
        response = Response()
        response['Access-Control-Allow-Origin'] = 'http://localhost:5173'
        response['Access-Control-Allow-Methods'] = 'POST, OPTIONS'
        response['Access-Control-Allow-Headers'] = 'Content-Type, Authorization, X-Requested-With, X-Schema-Name, X-CSRFToken'
        response['Access-Control-Allow-Credentials'] = 'true'
        response['Access-Control-Max-Age'] = '86400'  # 24 hours
        return response
    return add_emergency_contact(request, citizen_id)

@csrf_exempt
@api_view(['POST', 'OPTIONS'])
def add_document_csrf_exempt(request, citizen_id):
    """CSRF exempt wrapper for add_document that handles OPTIONS requests"""
    if request.method == 'OPTIONS':
        response = Response()
        response['Access-Control-Allow-Origin'] = 'http://localhost:5173'
        response['Access-Control-Allow-Methods'] = 'POST, OPTIONS'
        response['Access-Control-Allow-Headers'] = 'Content-Type, Authorization, X-Requested-With, X-Schema-Name, X-CSRFToken'
        response['Access-Control-Allow-Credentials'] = 'true'
        response['Access-Control-Max-Age'] = '86400'  # 24 hours
        return response
    return add_document(request, citizen_id)
