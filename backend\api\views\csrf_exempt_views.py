"""
CSRF exempt versions of the citizen relation views.
This is a temporary solution to allow the frontend to make POST requests to these endpoints.
"""
from django.views.decorators.csrf import csrf_exempt
from rest_framework.decorators import api_view, authentication_classes, permission_classes
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from rest_framework import status
from citizens.authentication import Citizen<PERSON><PERSON><PERSON>uthentication
from .citizen_relations import add_spouse, add_parent, add_child, add_emergency_contact, add_document
import logging

# Configure logging
logger = logging.getLogger(__name__)

# Create wrapper functions that handle OPTIONS requests
@csrf_exempt
@api_view(['POST', 'OPTIONS'])
@permission_classes([IsAuthenticated])
@authentication_classes([CitizenJWTAuthentication])
def add_spouse_csrf_exempt(request, citizen_id):
    """CSRF exempt wrapper for add_spouse that handles OPTIONS requests"""
    logger.debug(f"Processing spouse request for citizen {citizen_id}")
    logger.debug(f"Request headers: {dict(request.headers)}")

    if request.method == 'OPTIONS':
        response = Response()
        origin = request.headers.get('Origin', '')
        allowed_origins = ['http://localhost:5173', 'http://127.0.0.1:5173',
                          'http://localhost:5174', 'http://127.0.0.1:5174',
                          'http://localhost:5175', 'http://127.0.0.1:5175',
                          'http://localhost:5176', 'http://127.0.0.1:5176']

        if origin in allowed_origins:
            response['Access-Control-Allow-Origin'] = origin
            response['Access-Control-Allow-Methods'] = 'POST, OPTIONS'
            response['Access-Control-Allow-Headers'] = 'Content-Type, Authorization, X-Requested-With, X-Schema-Name, X-CSRFToken'
            response['Access-Control-Allow-Credentials'] = 'true'
            response['Access-Control-Max-Age'] = '86400'  # 24 hours
        return response

    logger.info(f"Adding spouse for citizen {citizen_id}")
    return add_spouse(request, citizen_id)

@csrf_exempt
@api_view(['POST', 'OPTIONS'])
@permission_classes([IsAuthenticated])
@authentication_classes([CitizenJWTAuthentication])
def add_parent_csrf_exempt(request, citizen_id):
    """CSRF exempt wrapper for add_parent that handles OPTIONS requests"""
    logger.debug(f"Processing parent request for citizen {citizen_id}")
    logger.debug(f"Request headers: {dict(request.headers)}")

    if request.method == 'OPTIONS':
        response = Response()
        origin = request.headers.get('Origin', '')
        allowed_origins = ['http://localhost:5173', 'http://127.0.0.1:5173',
                          'http://localhost:5174', 'http://127.0.0.1:5174',
                          'http://localhost:5175', 'http://127.0.0.1:5175',
                          'http://localhost:5176', 'http://127.0.0.1:5176']

        if origin in allowed_origins:
            response['Access-Control-Allow-Origin'] = origin
            response['Access-Control-Allow-Methods'] = 'POST, OPTIONS'
            response['Access-Control-Allow-Headers'] = 'Content-Type, Authorization, X-Requested-With, X-Schema-Name, X-CSRFToken'
            response['Access-Control-Allow-Credentials'] = 'true'
            response['Access-Control-Max-Age'] = '86400'  # 24 hours
        return response

    logger.info(f"Adding parent for citizen {citizen_id}")
    return add_parent(request, citizen_id)

@csrf_exempt
@api_view(['POST', 'OPTIONS'])
@permission_classes([IsAuthenticated])
@authentication_classes([CitizenJWTAuthentication])
def add_child_csrf_exempt(request, citizen_id):
    """CSRF exempt wrapper for add_child that handles OPTIONS requests"""
    logger.debug(f"Processing child request for citizen {citizen_id}")
    logger.debug(f"Request headers: {dict(request.headers)}")

    if request.method == 'OPTIONS':
        response = Response()
        origin = request.headers.get('Origin', '')
        allowed_origins = ['http://localhost:5173', 'http://127.0.0.1:5173',
                          'http://localhost:5174', 'http://127.0.0.1:5174',
                          'http://localhost:5175', 'http://127.0.0.1:5175',
                          'http://localhost:5176', 'http://127.0.0.1:5176']

        if origin in allowed_origins:
            response['Access-Control-Allow-Origin'] = origin
            response['Access-Control-Allow-Methods'] = 'POST, OPTIONS'
            response['Access-Control-Allow-Headers'] = 'Content-Type, Authorization, X-Requested-With, X-Schema-Name, X-CSRFToken'
            response['Access-Control-Allow-Credentials'] = 'true'
            response['Access-Control-Max-Age'] = '86400'  # 24 hours
        return response

    logger.info(f"Adding child for citizen {citizen_id}")
    return add_child(request, citizen_id)

@csrf_exempt
@api_view(['POST', 'OPTIONS'])
@permission_classes([IsAuthenticated])
@authentication_classes([CitizenJWTAuthentication])
def add_emergency_contact_csrf_exempt(request, citizen_id):
    """CSRF exempt wrapper for add_emergency_contact that handles OPTIONS requests"""
    logger.debug(f"Processing emergency contact request for citizen {citizen_id}")
    logger.debug(f"Request headers: {dict(request.headers)}")

    if request.method == 'OPTIONS':
        response = Response()
        origin = request.headers.get('Origin', '')
        allowed_origins = ['http://localhost:5173', 'http://127.0.0.1:5173',
                          'http://localhost:5174', 'http://127.0.0.1:5174',
                          'http://localhost:5175', 'http://127.0.0.1:5175',
                          'http://localhost:5176', 'http://127.0.0.1:5176']

        if origin in allowed_origins:
            response['Access-Control-Allow-Origin'] = origin
            response['Access-Control-Allow-Methods'] = 'POST, OPTIONS'
            response['Access-Control-Allow-Headers'] = 'Content-Type, Authorization, X-Requested-With, X-Schema-Name, X-CSRFToken'
            response['Access-Control-Allow-Credentials'] = 'true'
            response['Access-Control-Max-Age'] = '86400'  # 24 hours
        return response

    logger.info(f"Adding emergency contact for citizen {citizen_id}")
    return add_emergency_contact(request, citizen_id)

@csrf_exempt
@api_view(['POST', 'OPTIONS'])
@permission_classes([IsAuthenticated])
@authentication_classes([CitizenJWTAuthentication])
def add_document_csrf_exempt(request, citizen_id):
    """CSRF exempt wrapper for add_document that handles OPTIONS requests"""
    logger.debug(f"Processing document request for citizen {citizen_id}")
    logger.debug(f"Request headers: {dict(request.headers)}")

    if request.method == 'OPTIONS':
        response = Response()
        origin = request.headers.get('Origin', '')
        allowed_origins = ['http://localhost:5173', 'http://127.0.0.1:5173',
                          'http://localhost:5174', 'http://127.0.0.1:5174',
                          'http://localhost:5175', 'http://127.0.0.1:5175',
                          'http://localhost:5176', 'http://127.0.0.1:5176']

        if origin in allowed_origins:
            response['Access-Control-Allow-Origin'] = origin
            response['Access-Control-Allow-Methods'] = 'POST, OPTIONS'
            response['Access-Control-Allow-Headers'] = 'Content-Type, Authorization, X-Requested-With, X-Schema-Name, X-CSRFToken'
            response['Access-Control-Allow-Credentials'] = 'true'
            response['Access-Control-Max-Age'] = '86400'  # 24 hours
        return response

    logger.info(f"Adding document for citizen {citizen_id}")
    return add_document(request, citizen_id)
