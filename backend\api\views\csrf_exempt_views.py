"""
CSRF exempt versions of the citizen relation views.
This is a temporary solution to allow the frontend to make POST requests to these endpoints.
"""
from django.views.decorators.csrf import csrf_exempt
from rest_framework.decorators import api_view
from rest_framework.response import Response
from rest_framework import status
from .citizen_relations import add_spouse, add_parent, add_child, add_emergency_contact, add_document
import logging
import traceback

# Configure logging with more detail
logger = logging.getLogger(__name__)

# Create wrapper functions that handle OPTIONS requests
@csrf_exempt
@api_view(['POST', 'OPTIONS'])
def add_spouse_csrf_exempt(request, citizen_id):
    """CSRF exempt wrapper for add_spouse that handles OPTIONS requests"""
    logger.info(f"Processing spouse request for citizen {citizen_id}")
    logger.info(f"Request method: {request.method}")
    logger.info(f"Request headers: {dict(request.headers)}")

    # Check for Authorization header
    auth_header = request.headers.get('Authorization', '')
    if auth_header:
        logger.info(f"Authorization header: {auth_header[:15]}...")
    else:
        logger.warning("No Authorization header found")

    if request.method == 'OPTIONS':
        response = Response()
        origin = request.headers.get('Origin', '')
        allowed_origins = ['http://localhost:5173', 'http://127.0.0.1:5173',
                          'http://localhost:5174', 'http://127.0.0.1:5174',
                          'http://localhost:5175', 'http://127.0.0.1:5175',
                          'http://localhost:5176', 'http://127.0.0.1:5176']

        if origin in allowed_origins:
            response['Access-Control-Allow-Origin'] = origin
            response['Access-Control-Allow-Methods'] = 'POST, OPTIONS'
            response['Access-Control-Allow-Headers'] = 'Content-Type, Authorization, X-Requested-With, X-Schema-Name, X-CSRFToken'
            response['Access-Control-Allow-Credentials'] = 'true'
            response['Access-Control-Max-Age'] = '86400'  # 24 hours
        return response

    try:
        # CRITICAL FIX: Manually authenticate the request
        if not auth_header:
            logger.error("No Authorization header found in spouse request")
            return Response({"detail": "Authentication credentials were not provided."}, status=status.HTTP_401_UNAUTHORIZED)

        # Extract token
        if not auth_header.startswith('Bearer '):
            logger.error(f"Invalid Authorization header format: {auth_header[:15]}...")
            return Response({"detail": "Invalid Authorization header format. Use 'Bearer <token>'."}, status=status.HTTP_401_UNAUTHORIZED)

        token = auth_header.split(' ')[1]
        logger.info(f"Extracted token: {token[:10]}...")

        # Forward the request to the actual handler
        logger.info(f"Forwarding spouse request to add_spouse for citizen {citizen_id}")
        return add_spouse(request, citizen_id)
    except Exception as e:
        logger.error(f"Error in add_spouse_csrf_exempt: {str(e)}")
        logger.error(f"Traceback: {traceback.format_exc()}")
        # Return a more helpful error response
        return Response({"detail": f"Error processing spouse request: {str(e)}"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@csrf_exempt
@api_view(['POST', 'OPTIONS'])
def add_parent_csrf_exempt(request, citizen_id):
    """CSRF exempt wrapper for add_parent that handles OPTIONS requests"""
    logger.info(f"Processing parent request for citizen {citizen_id}")
    logger.info(f"Request method: {request.method}")
    logger.info(f"Request headers: {dict(request.headers)}")

    # Check for Authorization header
    auth_header = request.headers.get('Authorization', '')
    if auth_header:
        logger.info(f"Authorization header: {auth_header[:15]}...")
    else:
        logger.warning("No Authorization header found")

    if request.method == 'OPTIONS':
        response = Response()
        origin = request.headers.get('Origin', '')
        allowed_origins = ['http://localhost:5173', 'http://127.0.0.1:5173',
                          'http://localhost:5174', 'http://127.0.0.1:5174',
                          'http://localhost:5175', 'http://127.0.0.1:5175',
                          'http://localhost:5176', 'http://127.0.0.1:5176']

        if origin in allowed_origins:
            response['Access-Control-Allow-Origin'] = origin
            response['Access-Control-Allow-Methods'] = 'POST, OPTIONS'
            response['Access-Control-Allow-Headers'] = 'Content-Type, Authorization, X-Requested-With, X-Schema-Name, X-CSRFToken'
            response['Access-Control-Allow-Credentials'] = 'true'
            response['Access-Control-Max-Age'] = '86400'  # 24 hours
        return response

    try:
        # CRITICAL FIX: Manually authenticate the request
        if not auth_header:
            logger.error("No Authorization header found in parent request")
            return Response({"detail": "Authentication credentials were not provided."}, status=status.HTTP_401_UNAUTHORIZED)

        # Extract token
        if not auth_header.startswith('Bearer '):
            logger.error(f"Invalid Authorization header format: {auth_header[:15]}...")
            return Response({"detail": "Invalid Authorization header format. Use 'Bearer <token>'."}, status=status.HTTP_401_UNAUTHORIZED)

        token = auth_header.split(' ')[1]
        logger.info(f"Extracted token: {token[:10]}...")

        # Forward the request to the actual handler
        logger.info(f"Forwarding parent request to add_parent for citizen {citizen_id}")
        return add_parent(request, citizen_id)
    except Exception as e:
        logger.error(f"Error in add_parent_csrf_exempt: {str(e)}")
        logger.error(f"Traceback: {traceback.format_exc()}")
        # Return a more helpful error response
        return Response({"detail": f"Error processing parent request: {str(e)}"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@csrf_exempt
@api_view(['POST', 'OPTIONS'])
def add_child_csrf_exempt(request, citizen_id):
    """CSRF exempt wrapper for add_child that handles OPTIONS requests"""
    logger.info(f"Processing child request for citizen {citizen_id}")
    logger.info(f"Request method: {request.method}")
    logger.info(f"Request headers: {dict(request.headers)}")

    # Check for Authorization header
    auth_header = request.headers.get('Authorization', '')
    if auth_header:
        logger.info(f"Authorization header: {auth_header[:15]}...")
    else:
        logger.warning("No Authorization header found")

    if request.method == 'OPTIONS':
        response = Response()
        origin = request.headers.get('Origin', '')
        allowed_origins = ['http://localhost:5173', 'http://127.0.0.1:5173',
                          'http://localhost:5174', 'http://127.0.0.1:5174',
                          'http://localhost:5175', 'http://127.0.0.1:5175',
                          'http://localhost:5176', 'http://127.0.0.1:5176']

        if origin in allowed_origins:
            response['Access-Control-Allow-Origin'] = origin
            response['Access-Control-Allow-Methods'] = 'POST, OPTIONS'
            response['Access-Control-Allow-Headers'] = 'Content-Type, Authorization, X-Requested-With, X-Schema-Name, X-CSRFToken'
            response['Access-Control-Allow-Credentials'] = 'true'
            response['Access-Control-Max-Age'] = '86400'  # 24 hours
        return response

    try:
        # CRITICAL FIX: Manually authenticate the request
        if not auth_header:
            logger.error("No Authorization header found in child request")
            return Response({"detail": "Authentication credentials were not provided."}, status=401)

        # Extract token
        if not auth_header.startswith('Bearer '):
            logger.error(f"Invalid Authorization header format: {auth_header[:15]}...")
            return Response({"detail": "Invalid Authorization header format. Use 'Bearer <token>'."}, status=401)

        token = auth_header.split(' ')[1]
        logger.info(f"Extracted token: {token[:10]}...")

        # Forward the request to the actual handler
        logger.info(f"Forwarding child request to add_child for citizen {citizen_id}")
        return add_child(request, citizen_id)
    except Exception as e:
        logger.error(f"Error in add_child_csrf_exempt: {str(e)}")
        logger.error(f"Traceback: {traceback.format_exc()}")
        # Return a more helpful error response
        return Response({"detail": f"Error processing child request: {str(e)}"}, status=500)

@csrf_exempt
@api_view(['POST', 'OPTIONS'])
def add_emergency_contact_csrf_exempt(request, citizen_id):
    """CSRF exempt wrapper for add_emergency_contact that handles OPTIONS requests"""
    logger.info(f"Processing emergency contact request for citizen {citizen_id}")
    logger.info(f"Request method: {request.method}")
    logger.info(f"Request headers: {dict(request.headers)}")

    # Check for Authorization header
    auth_header = request.headers.get('Authorization', '')
    if auth_header:
        logger.info(f"Authorization header: {auth_header[:15]}...")
    else:
        logger.warning("No Authorization header found")

    if request.method == 'OPTIONS':
        response = Response()
        origin = request.headers.get('Origin', '')
        allowed_origins = ['http://localhost:5173', 'http://127.0.0.1:5173',
                          'http://localhost:5174', 'http://127.0.0.1:5174',
                          'http://localhost:5175', 'http://127.0.0.1:5175',
                          'http://localhost:5176', 'http://127.0.0.1:5176']

        if origin in allowed_origins:
            response['Access-Control-Allow-Origin'] = origin
            response['Access-Control-Allow-Methods'] = 'POST, OPTIONS'
            response['Access-Control-Allow-Headers'] = 'Content-Type, Authorization, X-Requested-With, X-Schema-Name, X-CSRFToken'
            response['Access-Control-Allow-Credentials'] = 'true'
            response['Access-Control-Max-Age'] = '86400'  # 24 hours
        return response

    try:
        # CRITICAL FIX: Manually authenticate the request
        if not auth_header:
            logger.error("No Authorization header found in emergency contact request")
            return Response({"detail": "Authentication credentials were not provided."}, status=401)

        # Extract token
        if not auth_header.startswith('Bearer '):
            logger.error(f"Invalid Authorization header format: {auth_header[:15]}...")
            return Response({"detail": "Invalid Authorization header format. Use 'Bearer <token>'."}, status=401)

        token = auth_header.split(' ')[1]
        logger.info(f"Extracted token: {token[:10]}...")

        # Forward the request to the actual handler
        logger.info(f"Forwarding emergency contact request to add_emergency_contact for citizen {citizen_id}")
        return add_emergency_contact(request, citizen_id)
    except Exception as e:
        logger.error(f"Error in add_emergency_contact_csrf_exempt: {str(e)}")
        logger.error(f"Traceback: {traceback.format_exc()}")
        # Return a more helpful error response
        return Response({"detail": f"Error processing emergency contact request: {str(e)}"}, status=500)

@csrf_exempt
@api_view(['POST', 'OPTIONS'])
def add_document_csrf_exempt(request, citizen_id):
    """CSRF exempt wrapper for add_document that handles OPTIONS requests"""
    logger.info(f"Processing document request for citizen {citizen_id}")
    logger.info(f"Request method: {request.method}")
    logger.info(f"Request headers: {dict(request.headers)}")

    # Check for Authorization header
    auth_header = request.headers.get('Authorization', '')
    if auth_header:
        logger.info(f"Authorization header: {auth_header[:15]}...")
    else:
        logger.warning("No Authorization header found")

    if request.method == 'OPTIONS':
        response = Response()
        origin = request.headers.get('Origin', '')
        allowed_origins = ['http://localhost:5173', 'http://127.0.0.1:5173',
                          'http://localhost:5174', 'http://127.0.0.1:5174',
                          'http://localhost:5175', 'http://127.0.0.1:5175',
                          'http://localhost:5176', 'http://127.0.0.1:5176']

        if origin in allowed_origins:
            response['Access-Control-Allow-Origin'] = origin
            response['Access-Control-Allow-Methods'] = 'POST, OPTIONS'
            response['Access-Control-Allow-Headers'] = 'Content-Type, Authorization, X-Requested-With, X-Schema-Name, X-CSRFToken'
            response['Access-Control-Allow-Credentials'] = 'true'
            response['Access-Control-Max-Age'] = '86400'  # 24 hours
        return response

    try:
        # CRITICAL FIX: Manually authenticate the request
        if not auth_header:
            logger.error("No Authorization header found in document request")
            return Response({"detail": "Authentication credentials were not provided."}, status=401)

        # Extract token
        if not auth_header.startswith('Bearer '):
            logger.error(f"Invalid Authorization header format: {auth_header[:15]}...")
            return Response({"detail": "Invalid Authorization header format. Use 'Bearer <token>'."}, status=401)

        token = auth_header.split(' ')[1]
        logger.info(f"Extracted token: {token[:10]}...")

        # Forward the request to the actual handler
        logger.info(f"Forwarding document request to add_document for citizen {citizen_id}")
        return add_document(request, citizen_id)
    except Exception as e:
        logger.error(f"Error in add_document_csrf_exempt: {str(e)}")
        logger.error(f"Traceback: {traceback.format_exc()}")
        # Return a more helpful error response
        return Response({"detail": f"Error processing document request: {str(e)}"}, status=500)
