import React from 'react';
import {
  <PERSON>,
  <PERSON><PERSON>,
  Container,
  <PERSON><PERSON><PERSON>,
  Grid as <PERSON><PERSON><PERSON><PERSON>,
  Card,
  CardContent,
  Stack,
  Paper,
  Avatar,
} from '@mui/material';
import { Link as RouterLink } from 'react-router-dom';
import SecurityIcon from '@mui/icons-material/Security';
import SpeedIcon from '@mui/icons-material/Speed';
import StorageIcon from '@mui/icons-material/Storage';
import PeopleIcon from '@mui/icons-material/People';
import VerifiedUserIcon from '@mui/icons-material/VerifiedUser';
import AnalyticsIcon from '@mui/icons-material/Analytics';
import HowToRegIcon from '@mui/icons-material/HowToReg';
import PrintIcon from '@mui/icons-material/Print';
import AssignmentTurnedInIcon from '@mui/icons-material/AssignmentTurnedIn';
import PageBanner from '../components/PageBanner';
import CardMembershipIcon from '@mui/icons-material/CardMembership';

const Home: React.FC = () => {

  // Features section data
  const features = [
    {
      title: 'Secure ID Management',
      description: 'Advanced security features to protect citizen data and prevent fraud with end-to-end encryption.',
      icon: <SecurityIcon fontSize="large" />,
      color: '#1976d2',
    },
    {
      title: 'Multi-tenant Architecture',
      description: 'Hierarchical system supporting city, subcity, and kebele levels with proper access controls.',
      icon: <StorageIcon fontSize="large" />,
      color: '#388e3c',
    },
    {
      title: 'Fast Processing',
      description: 'Streamlined workflows for quick ID card issuance and renewal with minimal waiting time.',
      icon: <SpeedIcon fontSize="large" />,
      color: '#f57c00',
    },
    {
      title: 'User-friendly Interface',
      description: 'Intuitive design for administrators and citizens alike, requiring minimal training.',
      icon: <PeopleIcon fontSize="large" />,
      color: '#7b1fa2',
    },
    {
      title: 'Verification System',
      description: 'Easy verification of ID cards using QR codes and online portal for instant validation.',
      icon: <VerifiedUserIcon fontSize="large" />,
      color: '#0288d1',
    },
    {
      title: 'Comprehensive Reporting',
      description: 'Detailed analytics and reports for all administrative levels with customizable dashboards.',
      icon: <AnalyticsIcon fontSize="large" />,
      color: '#d32f2f',
    },
  ];

  // How it works section data
  const workflowSteps = [
    {
      title: 'Registration',
      description: 'Citizens register at their local kebele center with required documentation and personal information.',
      icon: <HowToRegIcon fontSize="large" />,
      color: '#1976d2',
    },
    {
      title: 'Processing',
      description: 'Subcity offices verify and process the applications for ID card printing with quality assurance.',
      icon: <PrintIcon fontSize="large" />,
      color: '#388e3c',
    },
    {
      title: 'Distribution',
      description: 'Printed ID cards are distributed back to kebele centers for citizen pickup with secure verification.',
      icon: <AssignmentTurnedInIcon fontSize="large" />,
      color: '#f57c00',
    },
  ];

  return (
    <Box sx={{ overflow: 'hidden' }}>
      {/* Hero Section */}
      <PageBanner
        title="Gondar ID Card Management System"
        subtitle="A comprehensive solution for issuing, tracking, and managing citizen ID cards across all administrative levels"
        icon={<CardMembershipIcon sx={{ fontSize: 50, color: 'white' }} />}
        actionContent={
          <Box sx={{ textAlign: 'center', width: '100%' }}>
            <Typography variant="body1" sx={{ color: 'white', fontWeight: 500, mb: 3 }}>
              Our platform provides end-to-end solutions for all of Gondar city's identification needs,
              from kebele centers to subcity and city administration.
            </Typography>

            <Stack
              direction={{ xs: 'column', sm: 'row' }}
              spacing={2}
              sx={{ mt: 2, justifyContent: 'center' }}
            >
              <Button
                variant="contained"
                color="secondary"
                size="large"
                component={RouterLink}
                to="/tenant-registration"
                sx={{
                  py: 1.5,
                  px: 4,
                  fontSize: '1rem',
                  fontWeight: 600,
                  borderRadius: 8,
                  textTransform: 'none',
                  boxShadow: '0 8px 20px rgba(56, 142, 60, 0.25)',
                }}
              >
                Register Your Center
              </Button>
              <Button
                variant="contained"
                color="primary"
                size="large"
                component={RouterLink}
                to="/login"
                sx={{
                  py: 1.5,
                  px: 4,
                  fontSize: '1rem',
                  fontWeight: 600,
                  borderRadius: 8,
                  textTransform: 'none',
                  boxShadow: '0 8px 20px rgba(25, 118, 210, 0.25)',
                }}
              >
                Login
              </Button>
              <Button
                variant="outlined"
                size="large"
                component={RouterLink}
                to="/about"
                sx={{
                  py: 1.5,
                  px: 4,
                  fontSize: '1rem',
                  fontWeight: 600,
                  borderRadius: 8,
                  textTransform: 'none',
                  borderWidth: 2,
                  borderColor: 'white',
                  color: 'white',
                  '&:hover': {
                    borderColor: 'white',
                    backgroundColor: 'rgba(255, 255, 255, 0.1)',
                  }
                }}
              >
                Learn More
              </Button>
            </Stack>
          </Box>
        }
      >
        {/* ID Card Overlapping Elements */}
        <Box
          sx={{
            display: { xs: 'none', md: 'block' },
            position: 'absolute',
            right: '-15%',
            top: '50%',
            transform: 'translateY(-50%)',
            width: '250px',
            height: '180px',
            zIndex: 5
          }}
        >
          <Box
            sx={{
              position: 'absolute',
              top: 0,
              right: 0,
              width: '200px',
              height: '120px',
              borderRadius: '10px',
              background: 'rgba(255,255,255,0.9)',
              boxShadow: '0 10px 20px rgba(0,0,0,0.1)',
              transform: 'rotate(5deg)',
              overflow: 'hidden'
            }}
          >
            <Box
              sx={{
                width: '100%',
                height: '30px',
                background: 'linear-gradient(90deg, #3f51b5, #5c6bc0)',
                display: 'flex',
                alignItems: 'center',
                px: 2
              }}
            >
              <Box
                sx={{
                  width: '10px',
                  height: '10px',
                  borderRadius: '50%',
                  bgcolor: 'white',
                  opacity: 0.7,
                  mr: 1
                }}
              />
              <Box
                sx={{
                  width: '10px',
                  height: '10px',
                  borderRadius: '50%',
                  bgcolor: 'white',
                  opacity: 0.7,
                  mr: 1
                }}
              />
              <Box
                sx={{
                  width: '10px',
                  height: '10px',
                  borderRadius: '50%',
                  bgcolor: 'white',
                  opacity: 0.7
                }}
              />
            </Box>
            <Box
              sx={{
                p: 2,
                display: 'flex',
                flexDirection: 'column',
                gap: 1
              }}
            >
              <Box
                sx={{
                  height: '8px',
                  width: '80%',
                  borderRadius: '4px',
                  background: 'linear-gradient(90deg, #e0e0e0, #f5f5f5)'
                }}
              />
              <Box
                sx={{
                  height: '8px',
                  width: '60%',
                  borderRadius: '4px',
                  background: 'linear-gradient(90deg, #e0e0e0, #f5f5f5)'
                }}
              />
              <Box
                sx={{
                  height: '8px',
                  width: '70%',
                  borderRadius: '4px',
                  background: 'linear-gradient(90deg, #e0e0e0, #f5f5f5)'
                }}
              />
            </Box>
          </Box>
          <Box
            sx={{
              position: 'absolute',
              bottom: 0,
              left: 0,
              width: '180px',
              height: '100px',
              borderRadius: '10px',
              background: 'rgba(255,255,255,0.9)',
              boxShadow: '0 10px 20px rgba(0,0,0,0.1)',
              transform: 'rotate(-8deg)',
              overflow: 'hidden'
            }}
          >
            <Box
              sx={{
                width: '100%',
                height: '100%',
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'center',
                alignItems: 'center',
                p: 1
              }}
            >
              <Box
                sx={{
                  width: '40px',
                  height: '40px',
                  borderRadius: '50%',
                  background: 'linear-gradient(135deg, #3f51b5, #7986cb)',
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                  mb: 1
                }}
              >
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
                  <circle cx="9" cy="7" r="4"></circle>
                  <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
                  <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
                </svg>
              </Box>
              <Box
                sx={{
                  height: '6px',
                  width: '80%',
                  borderRadius: '3px',
                  background: 'linear-gradient(90deg, #e0e0e0, #f5f5f5)',
                  mb: 0.5
                }}
              />
              <Box
                sx={{
                  height: '6px',
                  width: '60%',
                  borderRadius: '3px',
                  background: 'linear-gradient(90deg, #e0e0e0, #f5f5f5)'
                }}
              />
            </Box>
          </Box>
        </Box>
      </PageBanner>

      {/* Features Section */}
      <Box
        sx={{
          py: { xs: 8, md: 12 },
          background: '#f8f9fa',
          position: 'relative',
        }}
      >
        <Container maxWidth="lg">
          <Box textAlign="center" mb={6}>
            <Typography
              variant="h3"
              component="h2"
              sx={{
                fontWeight: 800,
                fontSize: { xs: '2rem', sm: '2.5rem', md: '2.75rem' },
                mb: 2,
              }}
            >
              Key Features
            </Typography>
            <Typography
              variant="h6"
              color="text.secondary"
              sx={{
                maxWidth: 700,
                mx: 'auto',
                mb: 6,
              }}
            >
              Our comprehensive ID card management system offers everything you need to efficiently manage citizen identification
            </Typography>
          </Box>

          <MuiGrid container spacing={3} justifyContent="center">
            {features.map((feature, index) => (
              <MuiGrid item xs={12} sm={4} md={4} key={index}>
                <Card
                  sx={{
                    height: '100%',
                    minHeight: 280,
                    maxWidth: 350,
                    mx: 'auto',
                    width: '100%',
                    borderRadius: 4,
                    boxShadow: '0 10px 30px rgba(0, 0, 0, 0.08)',
                    overflow: 'hidden',
                    transition: 'transform 0.3s, box-shadow 0.3s',
                    '&:hover': {
                      transform: 'translateY(-10px)',
                      boxShadow: '0 15px 35px rgba(0, 0, 0, 0.12)',
                    }
                  }}
                >
                  <CardContent sx={{ p: 4 }}>
                    <Box
                      sx={{
                        display: 'flex',
                        alignItems: 'center',
                        mb: 3,
                      }}
                    >
                      <Avatar
                        sx={{
                          bgcolor: `${feature.color}15`,
                          color: feature.color,
                          width: 56,
                          height: 56,
                          mr: 2,
                        }}
                      >
                        {feature.icon}
                      </Avatar>
                      <Typography
                        variant="h5"
                        component="h3"
                        sx={{
                          fontWeight: 700,
                          color: feature.color,
                        }}
                      >
                        {feature.title}
                      </Typography>
                    </Box>
                    <Typography
                      variant="body1"
                      color="text.secondary"
                      sx={{ lineHeight: 1.7 }}
                    >
                      {feature.description}
                    </Typography>
                  </CardContent>
                </Card>
              </MuiGrid>
            ))}
          </MuiGrid>
        </Container>
      </Box>

      {/* How It Works Section */}
      <Box
        sx={{
          py: { xs: 8, md: 12 },
          background: '#ffffff',
        }}
      >
        <Container maxWidth="lg">
          <Box textAlign="center" mb={6}>
            <Typography
              variant="h3"
              component="h2"
              sx={{
                fontWeight: 800,
                fontSize: { xs: '2rem', sm: '2.5rem', md: '2.75rem' },
                mb: 2,
              }}
            >
              How It Works
            </Typography>
            <Typography
              variant="h6"
              color="text.secondary"
              sx={{
                maxWidth: 700,
                mx: 'auto',
                mb: 6,
              }}
            >
              Our streamlined process makes ID card management simple and efficient
            </Typography>
          </Box>

          <MuiGrid container spacing={3} justifyContent="center">
            {workflowSteps.map((step, index) => (
              <MuiGrid item xs={12} sm={4} md={4} key={index}>
                <Paper
                  elevation={0}
                  sx={{
                    p: 4,
                    height: '100%',
                    minHeight: 320,
                    maxWidth: 350,
                    mx: 'auto',
                    borderRadius: 4,
                    boxShadow: '0 10px 30px rgba(0, 0, 0, 0.08)',
                    position: 'relative',
                    textAlign: 'center',
                    transition: 'transform 0.3s, box-shadow 0.3s',
                    '&:hover': {
                      transform: 'translateY(-10px)',
                      boxShadow: '0 15px 35px rgba(0, 0, 0, 0.12)',
                    }
                  }}
                >
                  <Box
                    sx={{
                      width: 80,
                      height: 80,
                      borderRadius: '50%',
                      backgroundColor: `${step.color}15`,
                      color: step.color,
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      mb: 3,
                      mx: 'auto',
                      position: 'relative',
                    }}
                  >
                    {step.icon}
                    <Typography
                      sx={{
                        position: 'absolute',
                        top: -10,
                        right: -10,
                        width: 30,
                        height: 30,
                        borderRadius: '50%',
                        backgroundColor: step.color,
                        color: 'white',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        fontWeight: 'bold',
                        boxShadow: '0 4px 8px rgba(0, 0, 0, 0.1)',
                      }}
                    >
                      {index + 1}
                    </Typography>
                  </Box>
                  <Typography
                    variant="h5"
                    component="h3"
                    sx={{
                      fontWeight: 700,
                      mb: 2,
                      color: step.color,
                    }}
                  >
                    {step.title}
                  </Typography>
                  <Typography
                    variant="body1"
                    color="text.secondary"
                    sx={{ lineHeight: 1.7 }}
                  >
                    {step.description}
                  </Typography>
                </Paper>
              </MuiGrid>
            ))}
          </MuiGrid>
        </Container>
      </Box>

      {/* CTA Section */}
      <Box
        sx={{
          py: { xs: 8, md: 10 },
          background: 'linear-gradient(135deg, #1976d2 0%, #115293 100%)',
          color: 'white',
          position: 'relative',
          overflow: 'hidden',
        }}
      >
        <Container maxWidth="md" sx={{ position: 'relative', zIndex: 1, textAlign: 'center' }}>
          <Typography
            variant="h3"
            sx={{
              fontWeight: 800,
              fontSize: { xs: '2rem', sm: '2.5rem', md: '2.75rem' },
              mb: 3,
            }}
          >
            Ready to Get Started?
          </Typography>
          <Typography
            variant="h6"
            sx={{
              opacity: 0.9,
              mb: 5,
              maxWidth: 700,
              mx: 'auto',
            }}
          >
            Join the growing number of Ethiopian administrative units using our system
            for efficient and secure ID card management
          </Typography>
          <Stack
            direction={{ xs: 'column', sm: 'row' }}
            spacing={3}
            sx={{ justifyContent: 'center' }}
          >
            <Button
              variant="contained"
              color="secondary"
              size="large"
              component={RouterLink}
              to="/register"
              sx={{
                py: 1.5,
                px: 4,
                fontSize: '1.1rem',
                fontWeight: 600,
                borderRadius: 8,
                textTransform: 'none',
                boxShadow: '0 8px 20px rgba(0, 0, 0, 0.2)',
              }}
            >
              Register Your Center Today
            </Button>
            <Button
              variant="outlined"
              color="inherit"
              size="large"
              component={RouterLink}
              to="/login"
              sx={{
                py: 1.5,
                px: 4,
                fontSize: '1.1rem',
                fontWeight: 600,
                borderRadius: 8,
                textTransform: 'none',
                borderWidth: 2,
                borderColor: 'white',
                color: 'white',
                '&:hover': {
                  borderColor: 'white',
                  backgroundColor: 'rgba(255, 255, 255, 0.1)',
                },
                boxShadow: '0 8px 20px rgba(0, 0, 0, 0.2)',
              }}
            >
              Login to Your Account
            </Button>
          </Stack>
        </Container>
      </Box>
    </Box>
  );
};

export default Home;
