"""
Views for the centers app.
"""
import uuid
import os
from datetime import datetime
from django.shortcuts import get_object_or_404
from django.http import JsonResponse
from django.views.decorators.http import require_http_methods
from django.contrib.auth import get_user_model
from django.db import transaction, connections
from django.utils.text import slugify
from django_tenants.utils import tenant_context, schema_context
from django.views.decorators.csrf import csrf_exempt
from django.conf import settings
from .models import City, Subcity, Center, CenterType, Client, Domain, Citizen, IDCardTemplate, IDCard
from .models_region import Region, Country
from .serializers import (CitySerializer, CityListSerializer, SubcitySerializer, SubcityListSerializer,
                         CenterSerializer, CenterListSerializer, CenterTypeSerializer, CitizenSerializer,
                         CitizenListSerializer, IDCardTemplateSerializer, IDCardSerializer, IDCardListSerializer)
from rest_framework import viewsets, permissions, status
from rest_framework.decorators import api_view, permission_classes, action, authentication_classes
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from accounts.authentication import TenantTokenAuthentication

User = get_user_model()

class IsSuperAdmin(permissions.BasePermission):
    """Permission to allow only super admins to perform certain actions."""

    def has_permission(self, request, view):
        return request.user.is_authenticated and request.user.is_super_admin


class IsCityAdmin(permissions.BasePermission):
    """Permission to allow only city admins to perform certain actions."""

    def has_permission(self, request, view):
        return request.user.is_authenticated and hasattr(request.user, 'is_city_admin') and request.user.is_city_admin

    def has_object_permission(self, request, view, obj):
        # For City objects
        if isinstance(obj, City):
            # City admins can only access their own city
            return obj == request.user.city
        # For Subcity objects
        elif isinstance(obj, Subcity):
            # City admins can access subcities in their city
            return obj.city == request.user.city
        # For Center objects
        elif isinstance(obj, Center):
            # City admins can access centers in their city
            return obj.subcity.city == request.user.city
        # Default deny
        return False


class IsSubcityAdmin(permissions.BasePermission):
    """Permission to allow only subcity admins to perform certain actions."""

    def has_permission(self, request, view):
        return request.user.is_authenticated and hasattr(request.user, 'is_subcity_admin') and request.user.is_subcity_admin

    def has_object_permission(self, request, view, obj):
        # For Subcity objects
        if isinstance(obj, Subcity):
            # Subcity admins can only access their own subcity
            return obj == request.user.subcity
        # For Center objects
        elif isinstance(obj, Center):
            # Subcity admins can access centers in their subcity
            return obj.subcity == request.user.subcity
        # Default deny
        return False


class CityViewSet(viewsets.ModelViewSet):
    """ViewSet for the City model."""
    queryset = City.objects.all()
    serializer_class = CitySerializer
    permission_classes = [IsAuthenticated]

    def get_serializer_class(self):
        """Return different serializers for list and detail views."""
        if self.action == 'list':
            return CityListSerializer
        return CitySerializer

    def get_queryset(self):
        """Filter cities based on user role."""
        # Check if this is a schema generation request from Swagger
        if getattr(self, 'swagger_fake_view', False):
            # Return a simple queryset for Swagger schema generation
            return City.objects.none()

        user = self.request.user
        if user.is_super_admin:
            return City.objects.all()
        elif user.is_city_admin and user.city:
            return City.objects.filter(id=user.city.id)
        elif user.is_subcity_admin and user.subcity:
            return City.objects.filter(subcities=user.subcity)
        elif user.is_center_admin and user.center:
            return City.objects.filter(subcities__centers=user.center)
        return City.objects.none()

    def perform_create(self, serializer):
        """Set the created_by field to the current user."""
        serializer.save(created_by=self.request.user)

    @action(detail=True, methods=['get'])
    def subcities(self, request, pk=None):
        """Return all subcities for a specific city."""
        city = self.get_object()
        subcities = city.subcities.all()
        serializer = SubcityListSerializer(subcities, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['get'])
    def centers(self, request, pk=None):
        """Return all centers for a specific city."""
        city = self.get_object()
        centers = Center.objects.filter(subcity__city=city)
        serializer = CenterListSerializer(centers, many=True)
        return Response(serializer.data)


class SubcityViewSet(viewsets.ModelViewSet):
    """ViewSet for the Subcity model."""
    queryset = Subcity.objects.all()
    serializer_class = SubcitySerializer
    permission_classes = [IsAuthenticated]

    def get_serializer_class(self):
        """Return different serializers for list and detail views."""
        if self.action == 'list':
            return SubcityListSerializer
        return SubcitySerializer

    def get_queryset(self):
        """Filter subcities based on user role."""
        # Check if this is a schema generation request from Swagger
        if getattr(self, 'swagger_fake_view', False):
            # Return a simple queryset for Swagger schema generation
            return Subcity.objects.none()

        user = self.request.user
        if user.is_super_admin:
            return Subcity.objects.all()
        elif user.is_city_admin and user.city:
            return Subcity.objects.filter(city=user.city)
        elif user.is_subcity_admin and user.subcity:
            return Subcity.objects.filter(id=user.subcity.id)
        elif user.is_center_admin and user.center:
            return Subcity.objects.filter(centers=user.center)
        return Subcity.objects.none()

    def perform_create(self, serializer):
        """Set the created_by field to the current user."""
        serializer.save(created_by=self.request.user)

    @action(detail=True, methods=['get'])
    def centers(self, request, pk=None):
        """Return all centers for a specific subcity."""
        subcity = self.get_object()
        centers = subcity.centers.all()
        serializer = CenterListSerializer(centers, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['get'])
    def pending_id_cards(self, request, pk=None):
        """Return all pending ID cards for a specific subcity."""
        subcity = self.get_object()
        from idcards.models import IDCard
        from idcards.serializers import IDCardListSerializer
        id_cards = IDCard.objects.filter(
            center__subcity=subcity,
            status='pending'
        )
        serializer = IDCardListSerializer(id_cards, many=True)
        return Response(serializer.data)


class CenterTypeViewSet(viewsets.ModelViewSet):
    """ViewSet for the CenterType model."""
    queryset = CenterType.objects.all()
    serializer_class = CenterTypeSerializer
    permission_classes = [IsAuthenticated]


class CenterViewSet(viewsets.ModelViewSet):
    """ViewSet for the Center model."""
    queryset = Center.objects.all()
    serializer_class = CenterSerializer
    permission_classes = [IsAuthenticated]

    def get_serializer_class(self):
        """Return different serializers for list and detail views."""
        if self.action == 'list':
            return CenterListSerializer
        return CenterSerializer

    def get_queryset(self):
        """Filter centers based on user role."""
        # Check if this is a schema generation request from Swagger
        if getattr(self, 'swagger_fake_view', False):
            # Return a simple queryset for Swagger schema generation
            return Center.objects.none()

        user = self.request.user
        if user.is_super_admin:
            return Center.objects.all()
        elif user.is_city_admin and user.city:
            return Center.objects.filter(subcity__city=user.city)
        elif user.is_subcity_admin and user.subcity:
            return Center.objects.filter(subcity=user.subcity)
        elif user.is_center_admin and user.center:
            return Center.objects.filter(id=user.center.id)
        return Center.objects.none()

    def perform_create(self, serializer):
        """Set the created_by field to the current user."""
        serializer.save(created_by=self.request.user)

    @action(detail=True, methods=['get'])
    def statistics(self, request, pk=None):
        """Return detailed statistics for a specific center."""
        center = self.get_object()

        # Get counts from related models
        from citizens.models import Citizen
        from idcards.models import IDCard

        citizen_count = Citizen.objects.filter(center=center).count()
        id_card_count = IDCard.objects.filter(center=center).count()
        pending_id_cards = IDCard.objects.filter(center=center, status='pending').count()
        approved_id_cards = IDCard.objects.filter(center=center, status='approved').count()
        rejected_id_cards = IDCard.objects.filter(center=center, status='rejected').count()
        printed_id_cards = IDCard.objects.filter(center=center, status='printed').count()
        delivered_id_cards = IDCard.objects.filter(center=center, status='delivered').count()

        # Get user counts
        user_count = User.objects.filter(center=center).count()
        admin_count = User.objects.filter(center=center, role='CENTER_ADMIN').count()
        staff_count = User.objects.filter(center=center, role='CENTER_STAFF').count()

        # Calculate percentages
        id_card_percentage = (id_card_count / center.max_id_cards * 100) if center.max_id_cards > 0 else 0
        user_percentage = (user_count / center.max_users * 100) if center.max_users > 0 else 0
        citizen_percentage = (citizen_count / center.max_citizens * 100) if center.max_citizens > 0 else 0

        # Return statistics
        return Response({
            'citizen_count': citizen_count,
            'id_card_count': id_card_count,
            'pending_id_cards': pending_id_cards,
            'approved_id_cards': approved_id_cards,
            'rejected_id_cards': rejected_id_cards,
            'printed_id_cards': printed_id_cards,
            'delivered_id_cards': delivered_id_cards,
            'user_count': user_count,
            'admin_count': admin_count,
            'staff_count': staff_count,
            'id_card_percentage': id_card_percentage,
            'user_percentage': user_percentage,
            'citizen_percentage': citizen_percentage,
            'max_id_cards': center.max_id_cards,
            'max_users': center.max_users,
            'max_citizens': center.max_citizens,
        })

    @action(detail=True, methods=['get', 'put', 'patch'])
    def settings(self, request, pk=None):
        """Get or update center settings."""
        center = self.get_object()

        if request.method == 'GET':
            return Response(center.settings)
        else:
            # Update settings
            settings_data = request.data
            if not isinstance(settings_data, dict):
                return Response({'error': 'Settings must be a JSON object'}, status=status.HTTP_400_BAD_REQUEST)

            # Merge with existing settings
            center.settings.update(settings_data)
            center.save()
            return Response(center.settings)

    @action(detail=True, methods=['post'])
    def verify(self, request, pk=None):
        """Verify a center."""
        center = self.get_object()
        center.is_verified = True
        center.save()
        serializer = self.get_serializer(center)
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def unverify(self, request, pk=None):
        """Unverify a center."""
        center = self.get_object()
        center.is_verified = False
        center.save()
        serializer = self.get_serializer(center)
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def update_subscription(self, request, pk=None):
        """Update center subscription status and expiry."""
        center = self.get_object()

        # Update subscription status
        if 'subscription_status' in request.data:
            center.subscription_status = request.data['subscription_status']

        # Update subscription expiry
        if 'subscription_expiry' in request.data:
            center.subscription_expiry = request.data['subscription_expiry']

        center.save()
        serializer = self.get_serializer(center)
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def update_limits(self, request, pk=None):
        """Update center resource limits."""
        center = self.get_object()

        # Update max users
        if 'max_users' in request.data:
            center.max_users = request.data['max_users']

        # Update max citizens
        if 'max_citizens' in request.data:
            center.max_citizens = request.data['max_citizens']

        # Update max ID cards
        if 'max_id_cards' in request.data:
            center.max_id_cards = request.data['max_id_cards']

        center.save()
        serializer = self.get_serializer(center)
        return Response(serializer.data)

    @action(detail=True, methods=['get'])
    def users(self, request, pk=None):
        """Return all users for a specific center."""
        center = self.get_object()
        from accounts.serializers import UserListSerializer
        users = User.objects.filter(center=center)
        serializer = UserListSerializer(users, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['get'])
    def citizens(self, request, pk=None):
        """Return all citizens for a specific center."""
        center = self.get_object()
        from citizens.serializers import CitizenListSerializer
        from citizens.models import Citizen
        citizens = Citizen.objects.filter(center=center)
        serializer = CitizenListSerializer(citizens, many=True)
        return Response(serializer.data)


@api_view(['GET'])
@permission_classes([permissions.AllowAny])
def get_regions(request):
    """Get a list of all regions."""
    try:
        regions = Region.objects.all().select_related('country')

        # Format the response
        region_list = []
        for region in regions:
            region_data = {
                'id': region.id,
                'name': region.name,
                'code': region.code,
                'country_id': region.country.id,
                'country_name': region.country.name
            }
            region_list.append(region_data)

        return Response(region_list)
    except Exception as e:
        print(f"Error fetching regions: {str(e)}")
        return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([permissions.AllowAny])
def get_available_tenants(request):
    """Get a list of available tenants."""
    try:
        # Get query parameters
        schema_type = request.query_params.get('type', None)
        parent_schema = request.query_params.get('parent', None)

        # Start with all active tenants, excluding the public schema
        from django_tenants.utils import get_public_schema_name
        tenants = Client.objects.filter(is_active=True).exclude(schema_name=get_public_schema_name())

        # Filter by schema type if provided
        if schema_type:
            tenants = tenants.filter(schema_type=schema_type.upper())

        # Filter by parent if provided
        if parent_schema:
            try:
                parent = Client.objects.get(schema_name=parent_schema)
                tenants = tenants.filter(parent=parent)
            except Client.DoesNotExist:
                return Response({'error': f'Parent tenant {parent_schema} does not exist'},
                               status=status.HTTP_404_NOT_FOUND)

        # Format the response
        tenant_list = []
        for tenant in tenants:
            tenant_data = {
                'id': tenant.id,
                'name': tenant.name,
                'schema_name': tenant.schema_name,
                'schema_type': tenant.schema_type,
                'parent_id': tenant.parent.id if tenant.parent else None,
                'parent_schema_name': tenant.parent.schema_name if tenant.parent else None,
                'domains': [domain.domain for domain in tenant.domains.all()]
            }
            tenant_list.append(tenant_data)

        return Response(tenant_list)
    except Exception as e:
        print(f"Error fetching available tenants: {str(e)}")
        return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([permissions.AllowAny])
@csrf_exempt
def register_tenant(request):
    """Register a new tenant with schema creation."""
    print("\n\n===== REGISTER TENANT API CALLED =====\n")
    print(f"Request method: {request.method}")
    print(f"Request headers: {request.headers}")
    print(f"Request content type: {request.content_type}")

    # Check if this is a multipart form (with file upload)
    is_multipart = request.content_type and 'multipart/form-data' in request.content_type

    # Extract data based on request type
    if is_multipart:
        # For multipart form with file upload
        data = request.POST.dict()
        # Check if we have tenant_data as JSON
        if 'tenant_data' in request.POST:
            import json
            try:
                data = json.loads(request.POST.get('tenant_data'))
            except json.JSONDecodeError:
                return Response({'error': 'Invalid JSON in tenant_data'}, status=status.HTTP_400_BAD_REQUEST)

        # Get the logo file if it exists
        logo_file = request.FILES.get('logo')
        print(f"Logo file received: {logo_file.name if logo_file else None}")
    else:
        # Regular JSON data
        data = request.data
        logo_file = None

    print(f"Request data: {data}\n")

    # Validate required fields
    required_fields = ['schema_name', 'name', 'schema_type', 'admin_email', 'admin_password']
    for field in required_fields:
        if field not in data:
            return Response({'error': f'Missing required field: {field}'}, status=status.HTTP_400_BAD_REQUEST)

    # Extract schema name
    schema_name = data['schema_name']

    # Check if schema name is valid (alphanumeric and underscores only)
    if not schema_name.replace(' ', '').replace('_', '').isalnum():
        return Response({'error': 'Schema name must contain only alphanumeric characters, spaces, and underscores'},
                       status=status.HTTP_400_BAD_REQUEST)

    # Check if schema already exists
    if Client.objects.filter(schema_name=schema_name).exists():
        return Response({'error': f'Tenant with schema name "{schema_name}" already exists'},
                       status=status.HTTP_400_BAD_REQUEST)

    try:
        # Create the tenant with transaction to ensure atomicity
        with transaction.atomic():
            # Create the tenant
            # Extract email domain from the email address
            email = data.get('email', '')
            email_domain = ''
            if '@' in email:
                email_domain = email.split('@')[1]

            tenant = Client(
                schema_name=schema_name,
                name=data['name'],
                schema_type=data['schema_type'],
                description=data.get('description', ''),
                address=data.get('address', ''),
                phone=data.get('phone', ''),
                email=data.get('email', ''),
                email_domain=email_domain,  # Set the email_domain field
                custom_domain=data.get('custom_domain', ''),
                admin_name=data.get('admin_name', ''),
                admin_email=data['admin_email'],
                admin_phone=data.get('admin_phone', ''),
                is_active=data.get('is_active', True)
                # auto_create_schema is set in the save method
            )

            # Set city-specific fields if this is a city tenant
            if data['schema_type'] == 'CITY':
                tenant.city_code = data.get('city_code', '')
                tenant.mayor_name = data.get('mayor_name', '')
                tenant.deputy_mayor = data.get('deputy_mayor', '')
                tenant.motto_slogan = data.get('motto_slogan', '')
                tenant.city_intro = data.get('city_intro', '')
                tenant.established_date = data.get('established_date', None)
                tenant.area_sq_km = data.get('area_sq_km', None)
                tenant.elevation_meters = data.get('elevation_meters', None)
                tenant.population = data.get('population', None)
                tenant.website = data.get('website', '')
                tenant.google_maps_url = data.get('google_maps_url', '')
                tenant.header_color = data.get('header_color', '#007bff')
                tenant.accent_color = data.get('accent_color', '#28a745')

            # Set subcity-specific fields if this is a subcity tenant
            elif data['schema_type'] == 'SUBCITY':
                tenant.subcity_code = data.get('subcity_code', '')
                tenant.description = data.get('description', '')
                # Always set printing facility to True for subcities since printing is done at subcity level
                tenant.has_printing_facility = True
                tenant.printing_capacity = 100  # Default printing capacity
                tenant.website = data.get('website', '')
                tenant.header_color = data.get('header_color', '#007bff')
                tenant.accent_color = data.get('accent_color', '#28a745')

                # Set region if provided
                if 'region' in data and data['region']:
                    try:
                        region_id = int(data['region'])
                        region = Region.objects.get(id=region_id)
                        tenant.region = region
                        print(f"Found region: {region.name} (ID: {region.id})")
                    except (ValueError, Region.DoesNotExist) as e:
                        print(f"Error finding region: {str(e)}")
                        # Don't return an error, just log it

            # Set parent tenant if provided
            if 'parent_tenant' in data and data['parent_tenant']:
                try:
                    # Try to find parent by schema_name first
                    try:
                        parent = Client.objects.get(schema_name=data['parent_tenant'])
                        tenant.parent = parent
                        print(f"Found parent tenant by schema_name: {parent.name} (ID: {parent.id}, Schema: {parent.schema_name})")
                    except Client.DoesNotExist:
                        # If not found by schema_name, try by ID
                        try:
                            parent_id = int(data['parent_tenant'])
                            parent = Client.objects.get(id=parent_id)
                            tenant.parent = parent
                            print(f"Found parent tenant by ID: {parent.name} (ID: {parent.id}, Schema: {parent.schema_name})")
                        except (ValueError, Client.DoesNotExist):
                            return Response({'error': f'Parent tenant "{data["parent_tenant"]}" does not exist'},
                                           status=status.HTTP_400_BAD_REQUEST)
                except Exception as e:
                    print(f"Error finding parent tenant: {str(e)}")
                    return Response({'error': f'Error finding parent tenant: {str(e)}'},
                                   status=status.HTTP_400_BAD_REQUEST)

            # Save the tenant - this creates the schema
            tenant.save()

            # Run migrations for the tenant schema to ensure all tables are created
            from django_tenants.utils import tenant_context
            from django.core.management import call_command

            try:
                with tenant_context(tenant):
                    # Run migrations for all apps
                    call_command('migrate')
                    print(f"Migrations applied successfully to {tenant.schema_name} schema")
            except Exception as e:
                print(f"Error applying migrations to {tenant.schema_name} schema: {str(e)}")

            # Create a domain for the tenant
            domain_name = f"{schema_name}.localhost"
            domain = Domain(
                domain=domain_name,
                tenant=tenant,
                is_primary=True
            )
            domain.save()

            # Add custom domain if provided
            custom_domain = data.get('custom_domain')
            if custom_domain and custom_domain.strip():
                # Validate domain format
                if '.' in custom_domain:
                    custom_domain_obj = Domain(
                        domain=custom_domain.strip(),
                        tenant=tenant,
                        is_primary=False
                    )
                    custom_domain_obj.save()
                    print(f"Added custom domain: {custom_domain} for tenant {tenant.schema_name}")
                else:
                    print(f"Invalid custom domain format: {custom_domain}. Skipping.")

            # Create an admin user in the tenant schema using Django ORM
            from django_tenants.utils import tenant_context
            from django.contrib.auth import get_user_model

            # Determine the role based on tenant type
            role = 'KEBELE_ADMIN' if data['schema_type'] == 'KEBELE' else \
                   'SUBCITY_ADMIN' if data['schema_type'] == 'SUBCITY' else \
                   'CITY_ADMIN'

            try:
                # Create the admin user in the tenant schema
                with tenant_context(tenant):
                    User = get_user_model()

                    # Get admin email and username from data
                    admin_email = data['admin_email']
                    admin_username = data.get('admin_username', '')

                    # If custom domain is provided, append it to the username
                    if tenant.custom_domain:
                        # Create a domain-specific username for login by appending the domain
                        domain_username = f"{admin_username}@{tenant.custom_domain}"
                        print(f"Created domain-specific username: {domain_username}")
                    else:
                        # If no custom domain, use the original username
                        domain_username = admin_username

                    # Check if the user already exists
                    if not User.objects.filter(email=admin_email).exists():
                        # Create the admin user with domain-specific username
                        admin_user = User.objects.create_user(
                            email=admin_email,
                            password=data['admin_password'],
                            first_name=data.get('admin_first_name', 'Admin'),
                            last_name=data.get('admin_last_name', 'User'),
                            role=role,
                            is_active=True,
                            username=domain_username  # Set the domain-specific username
                        )
                        print(f"Created admin user {admin_email} in {tenant.schema_name} schema")
                    else:
                        print(f"Admin user {admin_email} already exists in {tenant.schema_name} schema")
            except Exception as e:
                print(f"Error creating admin user in tenant schema: {str(e)}")

            # Save the logo file if provided
            if logo_file:
                # Create the directory if it doesn't exist
                logo_dir = os.path.join(settings.MEDIA_ROOT, 'tenant_logos')
                os.makedirs(logo_dir, exist_ok=True)

                # Save the logo file
                logo_path = os.path.join(logo_dir, f"{tenant.schema_name}_logo.png")
                with open(logo_path, 'wb+') as destination:
                    for chunk in logo_file.chunks():
                        destination.write(chunk)
                print(f"Logo saved to {logo_path}")

                # Add logo URL to response with timestamp to prevent caching
                logo_path = os.path.join(settings.MEDIA_ROOT, f'tenant_logos/{tenant.schema_name}_logo.png')
                timestamp = int(os.path.getmtime(logo_path))
                logo_url = f"http://localhost:8000/media/tenant_logos/{tenant.schema_name}_logo.png?t={timestamp}"
            else:
                logo_url = None

        # Return success response
        response_data = {
            'id': tenant.id,
            'name': tenant.name,
            'schema_name': tenant.schema_name,
            'domain': domain_name,
            'custom_domain': tenant.custom_domain,
            'email_domain': tenant.email_domain
        }

        if logo_url:
            response_data['logo_url'] = logo_url

        return Response(response_data, status=status.HTTP_201_CREATED)

    except Exception as e:
        print(f"Error registering tenant: {str(e)}")
        return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# API endpoint to update tenant logo - simplified version without token authentication
@api_view(['POST'])
@permission_classes([permissions.AllowAny])
@csrf_exempt
def update_tenant_logo(request):
    """Update the logo for a tenant."""
    print("\n\n===== UPDATE TENANT LOGO API CALLED =====\n")
    print(f"Request method: {request.method}")
    print(f"Request headers: {request.headers}")
    print(f"Request content type: {request.content_type}")

    # Get schema name from various sources
    schema_name_form = request.data.get('schema_name')
    schema_name_header = request.headers.get('X-Schema-Name')
    schema_name_cookie = request.COOKIES.get('schema_name')

    # Use the first available schema name
    schema_name = schema_name_form or schema_name_header or schema_name_cookie

    print(f"Schema name from form: {schema_name_form}")
    print(f"Schema name from header: {schema_name_header}")
    print(f"Schema name from cookie: {schema_name_cookie}")
    print(f"Effective schema name: {schema_name}")

    if not schema_name:
        return Response({"error": "Schema name is required."}, status=status.HTTP_400_BAD_REQUEST)

    # Check if the tenant exists
    try:
        tenant = Client.objects.get(schema_name=schema_name)
        print(f"Found tenant: {tenant.name} (schema: {tenant.schema_name})")
    except Client.DoesNotExist:
        print(f"Tenant with schema name '{schema_name}' does not exist")
        return Response({'error': f'Tenant with schema name "{schema_name}" does not exist'},
                       status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        print(f"Error finding tenant: {str(e)}")
        return Response({'error': f'Error finding tenant: {str(e)}'},
                       status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    try:
        # Check if the request has a file
        if 'logo' not in request.FILES:
            return Response({'error': 'No logo file provided'}, status=status.HTTP_400_BAD_REQUEST)

        logo_file = request.FILES['logo']
        schema_name = request.data.get('schema_name')

        # If schema_name is not provided, use the schema_name from the X-Schema-Name header
        if not schema_name:
            schema_name = request.headers.get('X-Schema-Name')

        # If still no schema_name, use the current tenant's schema_name
        if not schema_name:
            from django.db import connection
            schema_name = connection.schema_name

        print(f"Using schema_name: {schema_name}")

        # This section is now handled above

        # Create the directory if it doesn't exist
        logo_dir = os.path.join(settings.MEDIA_ROOT, 'tenant_logos')
        os.makedirs(logo_dir, exist_ok=True)

        # Save the logo file
        logo_path = os.path.join(logo_dir, f"{tenant.schema_name}_logo.png")
        with open(logo_path, 'wb+') as destination:
            for chunk in logo_file.chunks():
                destination.write(chunk)

        # Add timestamp to prevent caching
        timestamp = int(os.path.getmtime(logo_path))
        logo_url = f"http://localhost:8000/media/tenant_logos/{tenant.schema_name}_logo.png?t={timestamp}"

        return Response({
            'success': True,
            'message': 'Logo updated successfully',
            'logo_url': logo_url
        })
    except Exception as e:
        return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# API endpoint to update tenant colors
@api_view(['POST'])
@permission_classes([permissions.AllowAny])
@csrf_exempt
def update_tenant_colors(request):
    """Update the header and accent colors for a tenant."""
    print("\n\n===== UPDATE TENANT COLORS API CALLED =====\n")
    print(f"Request method: {request.method}")
    print(f"Request headers: {request.headers}")
    print(f"Request content type: {request.content_type}")

    # Get schema name from various sources
    schema_name_form = request.data.get('schema_name')
    schema_name_header = request.headers.get('X-Schema-Name')
    schema_name_cookie = request.COOKIES.get('schema_name')

    # Use the first available schema name
    schema_name = schema_name_form or schema_name_header or schema_name_cookie

    print(f"Schema name from form: {schema_name_form}")
    print(f"Schema name from header: {schema_name_header}")
    print(f"Schema name from cookie: {schema_name_cookie}")
    print(f"Effective schema name: {schema_name}")

    if not schema_name:
        return Response({"error": "Schema name is required."}, status=status.HTTP_400_BAD_REQUEST)

    # Check if the tenant exists
    try:
        tenant = Client.objects.get(schema_name=schema_name)
        print(f"Found tenant: {tenant.name} (schema: {tenant.schema_name})")
    except Client.DoesNotExist:
        print(f"Tenant with schema name '{schema_name}' does not exist")
        return Response({'error': f'Tenant with schema name "{schema_name}" does not exist'},
                       status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        print(f"Error finding tenant: {str(e)}")
        return Response({'error': f'Error finding tenant: {str(e)}'},
                       status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    try:
        # Get the header and accent colors from the request
        header_color = request.data.get('header_color')
        accent_color = request.data.get('accent_color')

        if not header_color or not accent_color:
            return Response({'error': 'Header color and accent color are required'},
                           status=status.HTTP_400_BAD_REQUEST)

        # Update the tenant's colors
        tenant.header_color = header_color
        tenant.accent_color = accent_color
        tenant.save()

        print(f"Updated tenant colors: header={header_color}, accent={accent_color}")

        # Return the updated colors
        return Response({
            'header_color': header_color,
            'accent_color': accent_color
        })
    except Exception as e:
        print(f"Error updating colors: {str(e)}")
        return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# Viewsets for ID Card Registration
from django.db.models import Q

class CitizenViewSet(viewsets.ModelViewSet):
    """ViewSet for the Citizen model."""
    queryset = Citizen.objects.all()
    serializer_class = CitizenSerializer
    permission_classes = [IsAuthenticated]

    def get_serializer_class(self):
        """Return different serializers for list and detail views."""
        if self.action == 'list':
            return CitizenListSerializer
        return CitizenSerializer

    def get_queryset(self):
        """Filter citizens based on search parameters."""
        queryset = Citizen.objects.all()

        # Search functionality
        search_query = self.request.query_params.get('search', None)
        if search_query:
            queryset = queryset.filter(
                Q(first_name__icontains=search_query) |
                Q(last_name__icontains=search_query) |
                Q(registration_number__icontains=search_query) |
                Q(phone_number__icontains=search_query)
            )

        return queryset


class IDCardTemplateViewSet(viewsets.ModelViewSet):
    """ViewSet for the IDCardTemplate model."""
    queryset = IDCardTemplate.objects.all()
    serializer_class = IDCardTemplateSerializer
    permission_classes = [IsAuthenticated]


class IDCardViewSet(viewsets.ModelViewSet):
    """ViewSet for the IDCard model."""
    queryset = IDCard.objects.all()
    serializer_class = IDCardSerializer
    permission_classes = [IsAuthenticated]

    def get_serializer_class(self):
        """Return different serializers for list and detail views."""
        if self.action == 'list':
            return IDCardListSerializer
        return IDCardSerializer

    def get_queryset(self):
        """Filter ID cards based on search parameters."""
        queryset = IDCard.objects.all()

        # Search functionality
        search_query = self.request.query_params.get('search', None)
        if search_query:
            queryset = queryset.filter(
                Q(card_number__icontains=search_query) |
                Q(citizen__first_name__icontains=search_query) |
                Q(citizen__last_name__icontains=search_query) |
                Q(status__icontains=search_query)
            )

        # Filter by status
        status_filter = self.request.query_params.get('status', None)
        if status_filter:
            queryset = queryset.filter(status=status_filter)

        return queryset

    @action(detail=True, methods=['post'])
    def change_status(self, request, pk=None):
        """Change the status of an ID card."""
        id_card = self.get_object()

        # Get the new status from the request data
        new_status = request.data.get('status', None)
        if not new_status:
            return Response({'error': 'Status is required'}, status=status.HTTP_400_BAD_REQUEST)

        # Validate the status
        valid_statuses = [choice[0] for choice in IDCard.STATUS_CHOICES]
        if new_status not in valid_statuses:
            return Response({'error': f'Invalid status. Must be one of: {valid_statuses}'},
                           status=status.HTTP_400_BAD_REQUEST)

        # Update the status
        id_card.status = new_status
        id_card.save()

        serializer = self.get_serializer(id_card)
        return Response(serializer.data)