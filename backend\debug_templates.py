import os
import django
import requests
import json

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'neocamelot.settings')
django.setup()

from django.db import connection
from django_tenants.utils import tenant_context, get_tenant_model
from idcards.models import IDCardTemplate
from centers.models import Center

def debug_templates_api(schema_name):
    """Debug the ID card templates API for a specific schema."""
    print(f"\n=== Debugging ID Card Templates API for Schema {schema_name} ===")
    
    # First, check the database directly
    Client = get_tenant_model()
    try:
        tenant = Client.objects.get(schema_name=schema_name)
        with tenant_context(tenant):
            # Check if any templates exist
            templates = IDCardTemplate.objects.all()
            print(f"Database check: Found {templates.count()} templates in schema {schema_name}")
            
            if templates.exists():
                for template in templates:
                    print(f"  - {template.name} (ID: {template.id}, Default: {template.is_default})")
                    print(f"    Center: {template.center.name} (ID: {template.center.id})")
            else:
                print("  No templates found in database")
                
            # Check if any centers exist
            centers = Center.objects.all()
            print(f"Found {centers.count()} centers in schema {schema_name}")
            
            if centers.exists():
                for center in centers:
                    print(f"  - {center.name} (ID: {center.id})")
            else:
                print("  No centers found in database")
    except Client.DoesNotExist:
        print(f"Error: Tenant with schema {schema_name} does not exist")
        return
    except Exception as e:
        print(f"Error accessing schema {schema_name}: {str(e)}")
        return
    
    # Now test the API endpoint
    url = f"http://127.0.0.1:8000/api/tenant/{schema_name}/idcard-templates/"
    headers = {
        'Authorization': 'Token 01aa7be65fbda335a0b29edd56c967ad6112fa6b',
        'Content-Type': 'application/json'
    }
    
    print(f"\nAPI test: Making GET request to {url}")
    print(f"Headers: {headers}")
    
    try:
        response = requests.get(url, headers=headers)
        print(f"Response status code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"Response data: {json.dumps(data, indent=2)}")
            print(f"Found {len(data)} templates via API")
        else:
            print(f"Error response: {response.text}")
            
            # Try to parse the error response
            try:
                error_data = response.json()
                print(f"Error data: {json.dumps(error_data, indent=2)}")
            except:
                print("Could not parse error response as JSON")
    except Exception as e:
        print(f"Exception occurred: {str(e)}")

def debug_all_schemas():
    """Debug templates for all schemas."""
    print("=== Debugging Templates for All Schemas ===")
    
    # Get all tenants
    Client = get_tenant_model()
    tenants = Client.objects.exclude(schema_name='public')
    
    for tenant in tenants:
        debug_templates_api(tenant.schema_name)

if __name__ == "__main__":
    # Debug all schemas
    debug_all_schemas()
