"""
JWT Token Utilities

This module provides functions for generating, validating, and refreshing JWT tokens
for the multi-tenant system.
"""

import jwt
import datetime
import os
import logging
from django.conf import settings
from django.contrib.auth import get_user_model
from centers.models import Client

# Configure logging
logger = logging.getLogger(__name__)

User = get_user_model()

# JWT Settings
# In production, these should be in settings.py and use environment variables
JWT_SECRET_KEY = getattr(settings, 'JWT_SECRET_KEY', os.environ.get('JWT_SECRET_KEY', 'your-secret-key-here'))
JWT_ALGORITHM = getattr(settings, 'JWT_ALGORITHM', 'HS256')
JWT_ACCESS_TOKEN_EXPIRY = getattr(settings, 'JWT_ACCESS_TOKEN_EXPIRY', 15 * 60)  # 15 minutes in seconds
JWT_REFRESH_TOKEN_EXPIRY = getattr(settings, 'JWT_REFRESH_TOKEN_EXPIRY', 7 * 24 * 60 * 60)  # 7 days in seconds

def get_tenant_hierarchy(tenant_schema):
    """
    Get the tenant hierarchy (kebele -> subcity -> city) for a given schema.

    Args:
        tenant_schema (str): The schema name of the tenant

    Returns:
        dict: A dictionary containing tenant hierarchy information
    """
    try:
        # Log the request
        logger.info(f"Getting tenant hierarchy for schema '{tenant_schema}'")

        # Get the tenant by schema name
        try:
            tenant = Client.objects.get(schema_name=tenant_schema)
            logger.info(f"Found tenant with schema '{tenant_schema}': {tenant.name} (ID: {tenant.id})")
        except Client.DoesNotExist:
            logger.warning(f"Tenant with schema '{tenant_schema}' not found")
            return None

        # Initialize hierarchy data
        hierarchy = {
            'tenant_id': tenant.id,
            'tenant_schema': tenant.schema_name,
            'tenant_name': tenant.name,
            'tenant_level': tenant.schema_type.lower() if tenant.schema_type else 'unknown',
            'parent_id': None,
            'parent_schema': None,
            'parent_name': None,
            'parent_level': None,
            'city_id': None,
            'city_schema': None,
            'city_name': None,
        }

        # If tenant has a parent, add parent info
        if tenant.parent:
            try:
                parent = tenant.parent
                logger.info(f"Found parent tenant: {parent.name} (ID: {parent.id})")

                hierarchy.update({
                    'parent_id': parent.id,
                    'parent_schema': parent.schema_name,
                    'parent_name': parent.name,
                    'parent_level': parent.schema_type.lower() if parent.schema_type else 'unknown',
                })

                # If parent has a parent (city level), add city info
                if parent.parent:
                    try:
                        city = parent.parent
                        logger.info(f"Found city tenant: {city.name} (ID: {city.id})")

                        hierarchy.update({
                            'city_id': city.id,
                            'city_schema': city.schema_name,
                            'city_name': city.name,
                        })
                    except Exception as e:
                        logger.warning(f"Error getting city info: {str(e)}")
                elif parent.schema_type == 'CITY':
                    # If parent is a city
                    logger.info(f"Parent tenant is a city: {parent.name} (ID: {parent.id})")

                    hierarchy.update({
                        'city_id': parent.id,
                        'city_schema': parent.schema_name,
                        'city_name': parent.name,
                    })
            except Exception as e:
                logger.warning(f"Error getting parent info: {str(e)}")
        elif tenant.schema_type == 'CITY':
            # If tenant is a city
            logger.info(f"Tenant is a city: {tenant.name} (ID: {tenant.id})")

            hierarchy.update({
                'city_id': tenant.id,
                'city_schema': tenant.schema_name,
                'city_name': tenant.name,
            })

        logger.info(f"Successfully built tenant hierarchy for schema '{tenant_schema}'")
        return hierarchy
    except Exception as e:
        logger.exception(f"Error getting tenant hierarchy for schema '{tenant_schema}': {str(e)}")
        return None

def generate_jwt_token(user, tenant_schema, is_refresh=False):
    """
    Generate a JWT token for a user in a specific tenant.

    Args:
        user (User): The user object
        tenant_schema (str): The schema name of the tenant
        is_refresh (bool): Whether to generate a refresh token (longer expiry)

    Returns:
        str: The JWT token
    """
    try:
        # Get current time
        now = datetime.datetime.now(datetime.timezone.utc)

        # Set expiry time based on token type
        if is_refresh:
            expiry = now + datetime.timedelta(seconds=JWT_REFRESH_TOKEN_EXPIRY)
        else:
            expiry = now + datetime.timedelta(seconds=JWT_ACCESS_TOKEN_EXPIRY)

        # Log the tenant schema for debugging
        logger.info(f"Generating JWT token for user {user.id} in schema '{tenant_schema}'")

        # Get tenant hierarchy
        tenant_info = get_tenant_hierarchy(tenant_schema)
        if not tenant_info:
            logger.error(f"Could not get tenant hierarchy for schema '{tenant_schema}'")

            # Try with schema variations
            alt_schema = tenant_schema.replace(' ', '_')
            logger.info(f"Trying with underscore variation: '{alt_schema}'")
            tenant_info = get_tenant_hierarchy(alt_schema)

            if not tenant_info:
                alt_schema = tenant_schema.replace('_', ' ')
                logger.info(f"Trying with space variation: '{alt_schema}'")
                tenant_info = get_tenant_hierarchy(alt_schema)

                if not tenant_info:
                    logger.error(f"Could not get tenant hierarchy for any variation of schema '{tenant_schema}'")
                    return None
                else:
                    tenant_schema = alt_schema
            else:
                tenant_schema = alt_schema

        # Prepare token payload
        payload = {
            'sub': str(user.id),  # Subject (user ID)
            'email': user.email,
            'name': f"{user.first_name} {user.last_name}".strip(),
            'is_superuser': user.is_superuser,
            'is_staff': user.is_staff,
            'role': user.role if hasattr(user, 'role') else None,
            'tenant_level': tenant_info['tenant_level'],
            'tenant_id': tenant_info['tenant_id'],
            'tenant_name': tenant_info['tenant_name'],
            'schema': tenant_schema,
            'iat': int(now.timestamp()),  # Issued at (as integer timestamp)
            'exp': int(expiry.timestamp()),  # Expiry time (as integer timestamp)
            'token_type': 'refresh' if is_refresh else 'access',
        }

        # Add parent tenant info if available
        if tenant_info['parent_schema']:
            payload.update({
                'parent_id': tenant_info['parent_id'],
                'parent_schema': tenant_info['parent_schema'],
                'parent_name': tenant_info['parent_name'],
                'parent_level': tenant_info['parent_level'],
            })

        # Add city info if available
        if tenant_info['city_schema']:
            payload.update({
                'city_id': tenant_info['city_id'],
                'city_schema': tenant_info['city_schema'],
                'city_name': tenant_info['city_name'],
            })

        # Generate token
        token = jwt.encode(payload, JWT_SECRET_KEY, algorithm=JWT_ALGORITHM)

        # Log success
        logger.info(f"Successfully generated JWT token for user {user.id} in schema '{tenant_schema}'")

        return token
    except Exception as e:
        logger.exception(f"Error generating JWT token: {str(e)}")
        return None

def validate_jwt_token(token):
    """
    Validate a JWT token and return the payload if valid.

    Args:
        token (str): The JWT token to validate

    Returns:
        dict: The token payload if valid, None otherwise
    """
    try:
        # Decode and verify the token
        payload = jwt.decode(token, JWT_SECRET_KEY, algorithms=[JWT_ALGORITHM])

        # Check if token is expired
        # JWT already checks for token expiration during decode, so this is just an extra check
        now = datetime.datetime.now(datetime.timezone.utc)
        exp_time = datetime.datetime.fromtimestamp(payload['exp'], tz=datetime.timezone.utc)
        if exp_time < now:
            logger.warning(f"Token expired for user {payload.get('sub')}")
            return None

        # Check if token is blacklisted
        if is_token_blacklisted(token):
            logger.warning(f"Token is blacklisted for user {payload.get('sub')}")
            return None

        return payload
    except jwt.ExpiredSignatureError:
        logger.warning("Token has expired")
        return None
    except jwt.InvalidTokenError as e:
        logger.warning(f"Invalid token: {str(e)}")
        return None
    except Exception as e:
        logger.error(f"Error validating JWT token: {str(e)}")
        return None

def is_token_blacklisted(token):
    """
    Check if a token is blacklisted.

    Args:
        token (str): The token to check

    Returns:
        bool: True if the token is blacklisted, False otherwise
    """
    try:
        from .models import BlacklistedToken

        # Check if token is in blacklist
        return BlacklistedToken.objects.filter(token=token).exists()
    except Exception as e:
        logger.error(f"Error checking if token is blacklisted: {str(e)}")
        # If there's an error, assume the token is not blacklisted
        # to prevent blocking legitimate requests
        return False

def generate_jwt_tokens(user, tenant):
    """
    Generate both access and refresh JWT tokens for a user in a specific tenant.

    Args:
        user (User): The user object
        tenant (Client): The tenant object

    Returns:
        dict: A dictionary containing access_token and refresh_token
    """
    try:
        # Generate access token
        access_token = generate_jwt_token(user, tenant.schema_name, is_refresh=False)

        # Generate refresh token
        refresh_token = generate_jwt_token(user, tenant.schema_name, is_refresh=True)

        if not access_token or not refresh_token:
            logger.error(f"Failed to generate tokens for user {user.id} in tenant {tenant.schema_name}")
            return None

        return {
            'access_token': access_token,
            'refresh_token': refresh_token
        }
    except Exception as e:
        logger.error(f"Error generating JWT tokens: {str(e)}")
        return None

def refresh_jwt_tokens(refresh_token):
    """
    Refresh JWT tokens using a valid refresh token.

    Args:
        refresh_token (str): The refresh token

    Returns:
        dict: A dictionary containing new access_token and refresh_token if successful, None otherwise
    """
    try:
        # Get new tokens
        access_token, new_refresh_token = refresh_jwt_token(refresh_token)

        if not access_token or not new_refresh_token:
            return None

        return {
            'access_token': access_token,
            'refresh_token': new_refresh_token
        }
    except Exception as e:
        logger.error(f"Error refreshing JWT tokens: {str(e)}")
        return None

def refresh_jwt_token(refresh_token):
    """
    Refresh a JWT token using a valid refresh token.

    Implements token rotation - the old refresh token is blacklisted to prevent reuse.

    Args:
        refresh_token (str): The refresh token

    Returns:
        tuple: (access_token, refresh_token) if successful, (None, None) otherwise
    """
    try:
        # Validate the refresh token
        payload = validate_jwt_token(refresh_token)
        if not payload:
            logger.warning("Invalid refresh token")
            return None, None

        # Check if it's actually a refresh token
        if payload.get('token_type') != 'refresh':
            logger.warning("Token is not a refresh token")
            return None, None

        # Get the user
        try:
            user = User.objects.get(id=payload['sub'])
        except User.DoesNotExist:
            logger.warning(f"User with ID {payload.get('sub')} not found")
            return None, None

        # Get the schema
        schema = payload.get('schema')
        if not schema:
            logger.warning("No schema in refresh token")
            return None, None

        # Log the schema from the token
        logger.info(f"Schema from token: {schema}")

        # Check if the token is blacklisted
        from .models import BlacklistedToken
        if BlacklistedToken.objects.filter(token=refresh_token).exists():
            logger.warning(f"Refresh token for user {user.id} is blacklisted - possible token reuse attempt")
            return None, None

        # Check if the tenant exists with this schema
        try:
            # Just check if the tenant exists, we don't need the actual tenant object
            Client.objects.get(schema_name=schema)
        except Client.DoesNotExist:
            logger.warning(f"Tenant with schema {schema} not found, trying variations")

            # Try with schema variations
            try:
                # Try with underscores instead of spaces
                alt_schema = schema.replace(' ', '_')
                Client.objects.get(schema_name=alt_schema)
                schema = alt_schema
                logger.info(f"Found tenant with schema using underscore: {schema}")
            except Client.DoesNotExist:
                # Try with spaces instead of underscores
                alt_schema = schema.replace('_', ' ')
                try:
                    Client.objects.get(schema_name=alt_schema)
                    schema = alt_schema
                    logger.info(f"Found tenant with schema using spaces: {schema}")
                except Client.DoesNotExist:
                    logger.warning(f"Tenant with schema {schema} or variations not found")
                    return None, None

        # Generate new tokens
        access_token = generate_jwt_token(user, schema, is_refresh=False)
        new_refresh_token = generate_jwt_token(user, schema, is_refresh=True)

        # Blacklist the old refresh token to prevent reuse (token rotation)
        try:
            # Get expiry time from payload
            expires_at = datetime.datetime.fromtimestamp(payload['exp'], tz=datetime.timezone.utc)

            # Create blacklist entry
            BlacklistedToken.objects.create(
                token=refresh_token,
                user=user,
                expires_at=expires_at,
                token_type='refresh'
            )
            logger.info(f"Blacklisted old refresh token for user {user.id}")
        except Exception as e:
            logger.error(f"Error blacklisting old refresh token: {str(e)}")
            # Continue even if blacklisting fails - we don't want to block token refresh

        logger.info(f"Generated new tokens for user {user.id} in schema {schema}")

        return access_token, new_refresh_token
    except Exception as e:
        logger.error(f"Error refreshing JWT token: {str(e)}")
        return None, None

def get_schema_from_token(token):
    """
    Extract the schema name from a JWT token.

    Args:
        token (str): The JWT token

    Returns:
        str: The schema name if found, None otherwise
    """
    try:
        # Decode the token without verification (just to extract the schema)
        payload = jwt.decode(token, options={"verify_signature": False})
        return payload.get('schema')
    except Exception as e:
        logger.error(f"Error extracting schema from token: {str(e)}")
        return None

def extract_token_from_request(request):
    """
    Extract JWT token from request.

    This function tries to extract the token from:
    1. Authorization header (Bearer token)
    2. Authorization header (Token prefix for backward compatibility)
    3. Request parameters (GET or POST)
    4. Cookies

    Args:
        request: The HTTP request object

    Returns:
        str: The token if found, None otherwise
    """
    # Try to get token from Authorization header with Bearer prefix
    auth_header = request.headers.get('Authorization', '')
    if auth_header.startswith('Bearer '):
        return auth_header.split(' ')[1].strip()

    # Try to get token from Authorization header with Token prefix (backward compatibility)
    if auth_header.startswith('Token '):
        return auth_header.split(' ')[1].strip()

    # Try to get token from request parameters
    token = request.GET.get('token') or request.POST.get('token')
    if token:
        return token

    # Try to get token from cookies
    token = request.COOKIES.get('jwt_access_token')
    if token:
        return token

    return None

def get_schema_from_request(request):
    """
    Get schema name from request.

    This function tries to get the schema name from:
    1. X-Schema-Name header
    2. schema_name cookie
    3. JWT token in the request

    Args:
        request: The HTTP request object

    Returns:
        str: The schema name if found, None otherwise
    """
    # Try to get schema from X-Schema-Name header
    schema_name = request.headers.get('X-Schema-Name')
    if schema_name:
        return schema_name

    # Try to get schema from cookies
    schema_name = request.COOKIES.get('schema_name')
    if schema_name:
        return schema_name

    # Try to get schema from JWT token
    token = extract_token_from_request(request)
    if token:
        return get_schema_from_token(token)

    return None
