# Generated by Django 5.1.7 on 2025-04-17 17:04

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('centers', '0013_employeetype'),
    ]

    operations = [
        migrations.AddField(
            model_name='client',
            name='area_sq_km',
            field=models.FloatField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='client',
            name='city_code',
            field=models.CharField(blank=True, max_length=10, null=True),
        ),
        migrations.AddField(
            model_name='client',
            name='city_intro',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='client',
            name='deputy_mayor',
            field=models.Char<PERSON>ield(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='client',
            name='elevation_meters',
            field=models.FloatField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='client',
            name='established_date',
            field=models.DateField(blank=True, null=True),
        ),
        migrations.Add<PERSON>ield(
            model_name='client',
            name='mayor_name',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='client',
            name='motto_slogan',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='client',
            name='population',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='client',
            name='timezone',
            field=models.CharField(blank=True, max_length=50, null=True),
        ),
    ]
