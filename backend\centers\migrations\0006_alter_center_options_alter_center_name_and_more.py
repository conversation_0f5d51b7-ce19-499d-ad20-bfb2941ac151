# Generated by Django 5.1.7 on 2025-04-14 16:00

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('centers', '0005_update_tenant_data'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='center',
            options={'ordering': ['subcity', 'name']},
        ),
        migrations.AlterField(
            model_name='center',
            name='name',
            field=models.CharField(max_length=100),
        ),
        migrations.AlterField(
            model_name='center',
            name='slug',
            field=models.SlugField(blank=True, max_length=120),
        ),
        migrations.CreateModel(
            name='City',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True)),
                ('slug', models.SlugField(blank=True, max_length=120, unique=True)),
                ('code', models.CharField(blank=True, help_text='Unique code for the city', max_length=10, unique=True)),
                ('description', models.TextField(blank=True)),
                ('address', models.TextField(blank=True)),
                ('phone', models.CharField(blank=True, max_length=20)),
                ('email', models.EmailField(blank=True, max_length=254)),
                ('website', models.URLField(blank=True)),
                ('logo', models.ImageField(blank=True, null=True, upload_to='city_logos/')),
                ('header_color', models.CharField(blank=True, default='#3498db', max_length=20)),
                ('accent_color', models.CharField(blank=True, default='#2980b9', max_length=20)),
                ('admin_name', models.CharField(blank=True, max_length=100)),
                ('admin_email', models.EmailField(blank=True, max_length=254)),
                ('admin_phone', models.CharField(blank=True, max_length=20)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_cities', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name_plural': 'Cities',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Subcity',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('slug', models.SlugField(blank=True, max_length=120)),
                ('code', models.CharField(blank=True, help_text='Unique code for the subcity', max_length=10)),
                ('description', models.TextField(blank=True)),
                ('address', models.TextField(blank=True)),
                ('phone', models.CharField(blank=True, max_length=20)),
                ('email', models.EmailField(blank=True, max_length=254)),
                ('website', models.URLField(blank=True)),
                ('logo', models.ImageField(blank=True, null=True, upload_to='subcity_logos/')),
                ('header_color', models.CharField(blank=True, default='#3498db', max_length=20)),
                ('accent_color', models.CharField(blank=True, default='#2980b9', max_length=20)),
                ('admin_name', models.CharField(blank=True, max_length=100)),
                ('admin_email', models.EmailField(blank=True, max_length=254)),
                ('admin_phone', models.CharField(blank=True, max_length=20)),
                ('has_printing_facility', models.BooleanField(default=True, help_text='Whether this subcity can print ID cards')),
                ('printing_capacity', models.PositiveIntegerField(default=100, help_text='Number of ID cards that can be printed per day')),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('city', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='subcities', to='centers.city')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_subcities', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name_plural': 'Subcities',
                'ordering': ['city', 'name'],
                'unique_together': {('city', 'name')},
            },
        ),
        migrations.AddField(
            model_name='center',
            name='subcity',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='centers', to='centers.subcity'),
        ),
        migrations.AlterUniqueTogether(
            name='center',
            unique_together={('subcity', 'name')},
        ),
        migrations.RemoveField(
            model_name='center',
            name='city',
        ),
    ]
