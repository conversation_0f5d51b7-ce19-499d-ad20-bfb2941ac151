/**
 * JWT Authenticated Fetch Utility
 *
 * This utility provides functions for making authenticated API requests using JWT.
 */

import { getAccessTokenForSchema, refreshJWTTokens, getCurrentSchema } from '../services/tokenService';

/**
 * Make an authenticated API request using JWT
 * @param url The URL to fetch
 * @param options Fetch options
 * @param schema The schema name
 * @returns A promise that resolves to the fetch response
 */
export const jwtFetch = async (
  url: string,
  options: RequestInit = {},
  schema?: string
): Promise<Response> => {
  // Get schema from unified token service if not provided
  const schemaName = schema || getCurrentSchema() || '';

  if (!schemaName) {
    console.error('No schema name provided for JWT fetch');
    throw new Error('No schema name provided for JWT fetch');
  }

  // Get access token
  let accessToken = getAccessTokenForSchema(schemaName);

  // If no access token, try to refresh
  if (!accessToken) {
    try {
      console.log(`No access token found for schema ${schemaName}, trying to refresh`);
      const refreshResult = await refreshJWTTokens(schemaName);
      accessToken = refreshResult.access_token;
    } catch (error) {
      console.error(`Error refreshing token for schema ${schemaName}:`, error);
      throw new Error('Authentication failed. Please log in again.');
    }
  }

  // Create headers with authentication
  const headers = new Headers(options.headers || {});
  // Use Bearer prefix for JWT tokens
  headers.set('Authorization', `Bearer ${accessToken}`);
  headers.set('Content-Type', headers.get('Content-Type') || 'application/json');
  headers.set('X-Schema-Name', schemaName);
  console.log('jwtFetch: Added Authorization header with Bearer prefix');

  // Make the request
  let response = await fetch(url, {
    ...options,
    headers,
    credentials: 'include',
  });

  // If unauthorized, try to refresh token and retry
  if (response.status === 401) {
    try {
      console.log(`Received 401 response, trying to refresh token for schema ${schemaName}`);
      const refreshResult = await refreshJWTTokens(schemaName);
      accessToken = refreshResult.access_token;

      // Retry the request with new token
      // Use Bearer prefix for JWT tokens
      headers.set('Authorization', `Bearer ${accessToken}`);
      console.log('jwtFetch: Added Authorization header with Bearer prefix (retry)');

      response = await fetch(url, {
        ...options,
        headers,
        credentials: 'include',
      });
    } catch (error) {
      console.error(`Error refreshing token for schema ${schemaName}:`, error);
      throw new Error('Authentication failed. Please log in again.');
    }
  }

  return response;
};

/**
 * Create a JWT fetch function for a specific schema
 * @param schema The schema name
 * @returns A fetch function that includes authentication headers
 */
export const createJWTFetch = (schema: string) => {
  return (url: string, options: RequestInit = {}) => jwtFetch(url, options, schema);
};

export default jwtFetch;
