class SessionMiddlewareBypass:
    """
    Middleware to bypass Django's session middleware for API endpoints.
    This middleware should be placed AFTER Django's session middleware.
    """
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        # Process the request
        response = self.get_response(request)

        # Check if this is an API request and if session exists
        if hasattr(request, 'session') and request.path.startswith('/api/'):
            # For API requests, mark the session as not modified after processing
            # This prevents the session middleware from saving the session
            request.session.modified = False

        return response
