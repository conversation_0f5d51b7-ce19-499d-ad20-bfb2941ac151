from rest_framework import serializers
from .models_family import Child, Parent, EmergencyContact, Spouse
from common.models import RelationshipType
from .models_base import validate_age
from datetime import date

class RelationshipTypeSerializer(serializers.ModelSerializer):
    """Serializer for the RelationshipType model."""
    class Meta:
        model = RelationshipType
        fields = '__all__'
        ref_name = "CitizenRelationshipType"

class ChildSerializer(serializers.ModelSerializer):
    """Serializer for the Child model."""
    age = serializers.SerializerMethodField()

    class Meta:
        model = Child
        fields = '__all__'
        read_only_fields = ('created_at', 'updated_at')

    def get_age(self, obj):
        """Calculate the age of the child."""
        today = date.today()
        born = obj.date_of_birth
        return today.year - born.year - ((today.month, today.day) < (born.month, born.day))

    def validate(self, data):
        """Validate the data."""
        # Check if is_resident is True and linked_citizen is provided
        if data.get('is_resident') and not data.get('linked_citizen'):
            raise serializers.ValidationError("If the child is a resident, you must link to an existing citizen record.")

        # If linked_citizen is provided, copy data from the linked citizen
        if data.get('linked_citizen'):
            linked = data.get('linked_citizen')
            data['first_name'] = linked.first_name
            data['middle_name'] = linked.middle_name
            data['last_name'] = linked.last_name
            data['first_name_am'] = linked.first_name_am
            data['middle_name_am'] = linked.middle_name_am
            data['last_name_am'] = linked.last_name_am
            data['date_of_birth'] = linked.date_of_birth
            data['is_resident'] = True

        return data

class ParentSerializer(serializers.ModelSerializer):
    """Serializer for the Parent model."""

    class Meta:
        model = Parent
        fields = '__all__'
        read_only_fields = ('created_at', 'updated_at')

    def validate(self, data):
        """Validate the data."""
        # Check if is_resident is True and linked_citizen is provided
        if data.get('is_resident') and not data.get('linked_citizen'):
            raise serializers.ValidationError("If the parent is a resident, you must link to an existing citizen record.")

        # If linked_citizen is provided, copy data from the linked citizen
        if data.get('linked_citizen'):
            linked = data.get('linked_citizen')
            data['first_name'] = linked.first_name
            data['middle_name'] = linked.middle_name
            data['last_name'] = linked.last_name
            data['first_name_am'] = linked.first_name_am
            data['middle_name_am'] = linked.middle_name_am
            data['last_name_am'] = linked.last_name_am
            data['is_resident'] = True

        return data

class EmergencyContactSerializer(serializers.ModelSerializer):
    """Serializer for the EmergencyContact model."""
    relationship_name = serializers.CharField(source='relationship.name', read_only=True, required=False)

    class Meta:
        model = EmergencyContact
        fields = '__all__'
        read_only_fields = ('created_at', 'updated_at')

    def validate(self, data):
        """Validate the data."""
        # Check if is_resident is True and linked_citizen is provided
        if data.get('is_resident') and not data.get('linked_citizen'):
            raise serializers.ValidationError("If the emergency contact is a resident, you must link to an existing citizen record.")

        # If linked_citizen is provided, copy data from the linked citizen
        if data.get('linked_citizen'):
            linked = data.get('linked_citizen')
            data['first_name'] = linked.first_name
            data['middle_name'] = linked.middle_name
            data['last_name'] = linked.last_name
            data['first_name_am'] = linked.first_name_am
            data['middle_name_am'] = linked.middle_name_am
            data['last_name_am'] = linked.last_name_am
            data['phone'] = linked.phone
            data['email'] = linked.email
            data['is_resident'] = True

        return data

class SpouseSerializer(serializers.ModelSerializer):
    """Serializer for the Spouse model."""

    class Meta:
        model = Spouse
        fields = '__all__'
        read_only_fields = ('created_at', 'updated_at')

    def validate(self, data):
        """Validate the data."""
        # Check if is_resident is True and linked_citizen is provided
        if data.get('is_resident') and not data.get('linked_citizen'):
            raise serializers.ValidationError("If the spouse is a resident, you must link to an existing citizen record.")

        # If linked_citizen is provided, copy data from the linked citizen
        if data.get('linked_citizen'):
            linked = data.get('linked_citizen')
            data['first_name'] = linked.first_name
            data['middle_name'] = linked.middle_name
            data['last_name'] = linked.last_name
            data['first_name_am'] = linked.first_name_am
            data['middle_name_am'] = linked.middle_name_am
            data['last_name_am'] = linked.last_name_am
            data['phone'] = linked.phone
            data['email'] = linked.email
            data['is_resident'] = True

        return data
