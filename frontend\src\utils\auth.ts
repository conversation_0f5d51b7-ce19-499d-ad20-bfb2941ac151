/**
 * Authentication utility functions for token management and validation
 */

// Base API URL - use relative URL to leverage the proxy
const API_BASE_URL = '';

/**
 * Get the current token from localStorage
 */
export const getToken = (): string | null => {
  return localStorage.getItem('token');
};

/**
 * Set a token in localStorage
 */
export const setToken = (token: string): void => {
  localStorage.setItem('token', token);
};

/**
 * Remove the token from localStorage (logout)
 */
export const removeToken = (): void => {
  localStorage.removeItem('token');
};

/**
 * Check if a token exists in localStorage
 */
export const hasToken = (): boolean => {
  return !!getToken();
};

/**
 * Validate a token by making a test API call
 * @returns Promise<boolean> - True if token is valid, false otherwise
 */
export const validateToken = async (token: string): Promise<boolean> => {
  try {
    // Try to validate the token by making a simple API call
    // You can replace this with a dedicated token validation endpoint if available
    const response = await fetch(`${API_BASE_URL}/api/tenant-info/`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });

    return response.ok;
  } catch (error) {
    console.error('Error validating token:', error);
    return false;
  }
};

/**
 * Get a valid token, validating the current one or redirecting to login
 * @param redirectOnFailure - Whether to redirect to login page on failure
 * @returns Promise<string | null> - Valid token or null
 */
export const getValidToken = async (redirectOnFailure: boolean = true): Promise<string | null> => {
  const currentToken = getToken();

  if (!currentToken) {
    console.error('No token found in localStorage');
    if (redirectOnFailure) {
      redirectToLogin('No authentication token found. Please log in.');
    }
    return null;
  }

  const isValid = await validateToken(currentToken);

  if (isValid) {
    return currentToken;
  } else {
    console.error('Token validation failed');
    if (redirectOnFailure) {
      redirectToLogin('Your session has expired. Please log in again.');
    }
    return null;
  }
};

/**
 * Redirect to login page with an optional message
 */
export const redirectToLogin = (message?: string): void => {
  if (message) {
    // Store the message to be displayed on the login page
    localStorage.setItem('auth_error', message);
  }

  // Redirect to login page
  window.location.href = '/login';
};

/**
 * Create headers with authentication token for API requests
 */
export const createAuthHeaders = (token: string, includeContentType: boolean = true): HeadersInit => {
  const headers: HeadersInit = {
    'Authorization': `Bearer ${token}`
  };

  if (includeContentType) {
    headers['Content-Type'] = 'application/json';
  }

  return headers;
};

/**
 * Make an authenticated API request
 * @param url - API endpoint URL
 * @param options - Fetch options
 * @param validateTokenFirst - Whether to validate the token before making the request
 * @returns Promise with the response
 */
export const authenticatedFetch = async (
  url: string,
  options: RequestInit = {},
  validateTokenFirst: boolean = true
): Promise<Response> => {
  const token = validateTokenFirst
    ? await getValidToken()
    : getToken();

  if (!token) {
    throw new Error('Authentication failed. Please log in again.');
  }

  // Create default headers with authentication
  const headers = createAuthHeaders(token, options.body instanceof FormData ? false : true);

  // Merge with any headers provided in options
  const mergedHeaders = {
    ...headers,
    ...(options.headers || {})
  };

  // Get CSRF token if needed
  const csrftoken = document.cookie
    .split('; ')
    .find(row => row.startsWith('csrftoken='))
    ?.split('=')[1] || '';

  if (csrftoken) {
    mergedHeaders['X-CSRFToken'] = csrftoken;
  }

  // Make the request with authentication
  const response = await fetch(url, {
    ...options,
    headers: mergedHeaders,
    credentials: 'include'
  });

  // Handle 401 Unauthorized errors
  if (response.status === 401) {
    removeToken();
    redirectToLogin('Your session has expired. Please log in again.');
    throw new Error('Authentication failed. Please log in again.');
  }

  return response;
};

/**
 * Get the current tenant from localStorage
 */
export const getTenant = () => {
  const tenantString = localStorage.getItem('tenant');
  return tenantString ? JSON.parse(tenantString) : null;
};

/**
 * Get the schema name from localStorage or tenant
 */
export const getSchemaName = () => {
  return localStorage.getItem('schema_name') || getTenant()?.schema_name || '';
};

/**
 * Format a tenant-specific API URL
 */
export const getTenantApiUrl = (endpoint: string, queryParams: Record<string, string> = {}) => {
  const schema = getSchemaName();

  if (!schema) {
    throw new Error('No tenant schema found. Please select a tenant or log in again.');
  }

  let url = `${API_BASE_URL}/api/tenant/${encodeURIComponent(schema)}/${endpoint}/`;

  // Add query parameters if any
  const queryString = Object.entries(queryParams)
    .map(([key, value]) => `${encodeURIComponent(key)}=${encodeURIComponent(value)}`)
    .join('&');

  if (queryString) {
    url += `?${queryString}`;
  }

  return url;
};

/**
 * Format a common API URL (not tenant-specific)
 */
export const getCommonApiUrl = (endpoint: string) => {
  return `${API_BASE_URL}/api/common/${endpoint}/`;
};
