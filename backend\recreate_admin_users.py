import os
import django

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'neocamelot.settings')
django.setup()

# Import models
from centers.models import Client
from django.contrib.auth import get_user_model
from django_tenants.utils import tenant_context, schema_context

User = get_user_model()

def recreate_admin_users():
    """Recreate admin users in tenant schemas."""
    # Get all tenants except public
    tenants = Client.objects.exclude(schema_name='public')
    
    for tenant in tenants:
        try:
            print(f"\nRecreating admin user for {tenant.name} (Schema: {tenant.schema_name})")
            
            # Skip if no admin email is set
            if not tenant.admin_email:
                print(f"No admin email set for {tenant.name}, skipping")
                continue
            
            # Determine the role based on tenant type
            role = 'CENTER_ADMIN' if tenant.schema_type == 'CENTER' else \
                   'SUBCITY_ADMIN' if tenant.schema_type == 'SUBCITY' else \
                   'CITY_ADMIN'
            
            # Create the admin user in the tenant schema
            with tenant_context(tenant):
                # Check if user already exists
                if User.objects.filter(email=tenant.admin_email).exists():
                    print(f"Admin user {tenant.admin_email} already exists in {tenant.schema_name} schema")
                    continue
                
                # Create the admin user
                admin_user = User.objects.create_user(
                    email=tenant.admin_email,
                    password='password123',  # Default password, should be changed
                    first_name='Admin',
                    last_name=tenant.name,
                    role=role,
                    is_active=True
                )
                print(f"Created admin user {tenant.admin_email} in {tenant.schema_name} schema")
        except Exception as e:
            print(f"Error recreating admin user for {tenant.name}: {str(e)}")

if __name__ == "__main__":
    recreate_admin_users()
