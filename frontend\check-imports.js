/**
 * <PERSON><PERSON><PERSON> to check for imports of old token services
 *
 * This script searches for imports of the old token services in the codebase.
 * Run it with: node check-imports.js
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Get the current directory
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Files to search for
const oldServices = [
  'jwtTokenService.simple.ts',
  'jwtTokenService.ts',
  'tokenService.ts'
];

// Directories to exclude
const excludeDirs = [
  'node_modules',
  'dist',
  '.git'
];

// Function to check if a file contains imports of old services
function checkFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');

    // Check for imports of old services
    for (const service of oldServices) {
      if (content.includes(`from '../services/${service.replace('.ts', '')}'`) ||
          content.includes(`from './services/${service.replace('.ts', '')}'`) ||
          content.includes(`from '../../services/${service.replace('.ts', '')}'`)) {
        console.log(`Found import of ${service} in ${filePath}`);
      }
    }
  } catch (error) {
    console.error(`Error reading file ${filePath}: ${error.message}`);
  }
}

// Function to recursively search directories
function searchDirectory(dir) {
  try {
    const files = fs.readdirSync(dir);

    for (const file of files) {
      const filePath = path.join(dir, file);
      const stat = fs.statSync(filePath);

      if (stat.isDirectory()) {
        // Skip excluded directories
        if (excludeDirs.includes(file)) {
          continue;
        }

        // Recursively search subdirectories
        searchDirectory(filePath);
      } else if (stat.isFile() && (file.endsWith('.ts') || file.endsWith('.tsx'))) {
        // Check TypeScript files
        checkFile(filePath);
      }
    }
  } catch (error) {
    console.error(`Error searching directory ${dir}: ${error.message}`);
  }
}

// Start the search from the src directory
console.log('Searching for imports of old token services...');
searchDirectory(path.join(__dirname, 'src'));
console.log('Search complete.');
