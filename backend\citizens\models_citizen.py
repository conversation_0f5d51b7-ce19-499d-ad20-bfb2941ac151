from django.db import models
from centers.models_region import Timestamp

class CurrentStatus(Timestamp):
    """Model representing the current status of a citizen."""
    name = models.CharField(max_length=50, unique=True, help_text="Current status of the citizen")
    is_active = models.BooleanField(default=True)  # Boolean field for activity status

    def __str__(self):
        return self.name

    class Meta:
        verbose_name = "Current Status"
        verbose_name_plural = "Current Statuses"
