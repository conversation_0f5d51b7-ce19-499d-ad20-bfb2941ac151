"""
Patch for drf-yasg to fix the view description function issue.
"""
from functools import wraps
from rest_framework.views import get_view_description as drf_get_view_description

# Patch the get_view_description method in rest_framework.views
def patched_get_view_description(view_cls, html=False):
    """
    Return the description for a given view class.
    
    This is a patched version that doesn't rely on settings.
    """
    description = view_cls.__doc__ or ""
    description = description.strip()
    
    if html:
        import markdown
        try:
            return markdown.markdown(description)
        except ImportError:
            return description.replace("\n", "<br />")
    return description

# Monkey patch the drf_yasg.inspectors.view module
def patch_drf_yasg():
    """
    Apply monkey patches to fix drf-yasg issues.
    """
    try:
        from drf_yasg.inspectors import view as yasg_view
        
        # Save the original method
        original_get_summary_and_description = yasg_view.SwaggerAutoSchema.get_summary_and_description
        
        @wraps(original_get_summary_and_description)
        def patched_get_summary_and_description(self):
            """
            Patched version that uses our custom get_view_description function.
            """
            try:
                return original_get_summary_and_description(self)
            except AttributeError:
                # If there's an AttributeError, use our custom function
                view_cls = self.view.__class__
                description = patched_get_view_description(view_cls)
                return view_cls.__name__, description
        
        # Apply the patch
        yasg_view.SwaggerAutoSchema.get_summary_and_description = patched_get_summary_and_description
        
        print("Successfully patched drf-yasg")
    except ImportError:
        print("Could not patch drf-yasg (not installed)")
    except Exception as e:
        print(f"Error patching drf-yasg: {e}")
