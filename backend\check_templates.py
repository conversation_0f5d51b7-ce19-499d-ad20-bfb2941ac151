import os
import django
import sys

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'neocamelot.settings')
django.setup()

from django.db import connection
from centers.models import Client
from idcards.models import IDCardTemplate

# Get the schema name from command line argument
if len(sys.argv) > 1:
    schema_name = sys.argv[1]
else:
    print("Please provide a schema name as a command line argument")
    sys.exit(1)

# Set to public schema
connection.set_schema_to_public()

try:
    # Get the tenant by schema name
    tenant = Client.objects.get(schema_name=schema_name)
    print(f"Found tenant: {tenant.name} (ID: {tenant.id})")
    print(f"Schema name: {tenant.schema_name}")
    print(f"Schema type: {tenant.schema_type}")

    # Set the tenant for this request
    connection.set_tenant(tenant)

    # Get all templates in this tenant
    templates = IDCardTemplate.objects.all()
    print(f"\nID Card Templates in tenant {tenant.name}:")
    if templates.exists():
        for template in templates:
            print(f"- {template.name} (ID: {template.id})")
            print(f"  Is default: {template.is_default}")
            print(f"  Created: {template.created_at}")
            print(f"  Front layout: {template.front_layout}")
            print(f"  Back layout: {template.back_layout}")
    else:
        print("No templates found in this tenant.")

except Client.DoesNotExist:
    print(f"Tenant with schema {schema_name} does not exist")

except Exception as e:
    print(f"Error: {str(e)}")
