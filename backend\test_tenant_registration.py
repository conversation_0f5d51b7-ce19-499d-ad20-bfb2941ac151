import os
import django
import requests
import json
import uuid

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'neocamelot.settings')
django.setup()

# Import models
from centers.models import Client, Domain
from django.contrib.auth import get_user_model
from django_tenants.utils import tenant_context, schema_context

User = get_user_model()

def test_tenant_registration():
    """Test the tenant registration process."""
    print("\n=== Testing Tenant Registration ===")

    # Generate a unique name for the test tenant
    unique_id = str(uuid.uuid4())[:8]
    tenant_name = f"Test Kebele {unique_id}"
    tenant_code = f"TK{unique_id}"
    admin_email = f"admin.{tenant_code.lower()}@test.com"

    # Prepare the data for API call
    tenant_data = {
        "schema_name": tenant_code.lower(),
        "name": tenant_name,
        "schema_type": "CENTER",
        "description": f"Test kebele for {tenant_name}",
        "parent_tenant": "subcity_zoble",  # Use the existing subcity
        "address": "123 Test Street",
        "phone": "************",
        "email": f"info@{tenant_code.lower()}.test.com",
        "admin_name": f"Admin {tenant_name}",
        "admin_email": admin_email,
        "admin_phone": "************",
        "admin_username": f"admin_{tenant_code.lower()}",
        "admin_password": "password123",
        "admin_first_name": "Admin",
        "admin_last_name": tenant_name,
        "is_active": True
    }

    print(f"Registering tenant: {tenant_name} (Code: {tenant_code})")

    # Make the API call
    try:
        response = requests.post(
            "http://localhost:8000/api/register-tenant/",
            headers={"Content-Type": "application/json"},
            json=tenant_data
        )

        print(f"Status Code: {response.status_code}")
        if response.status_code == 201:
            data = response.json()
            print(f"Response: {json.dumps(data, indent=2)}")
            print(f"Tenant created successfully: {data['name']} (Schema: {data['schema_name']})")

            # Check if the tenant was created in the database
            tenant = Client.objects.filter(schema_name=tenant_code.lower()).first()
            if tenant:
                print(f"\nTenant found in database: {tenant.name} (Schema: {tenant.schema_name})")

                # Check if the admin user was created in the tenant schema
                try:
                    with tenant_context(tenant):
                        admin_user = User.objects.filter(email=admin_email).first()
                        if admin_user:
                            print(f"Admin user found in tenant schema: {admin_user.email} (Role: {admin_user.role})")
                        else:
                            print(f"Admin user not found in tenant schema")
                except Exception as e:
                    print(f"Error accessing tenant schema: {str(e)}")

                # Check if the admin user was created in the public schema
                with schema_context('public'):
                    public_admin = User.objects.filter(email=admin_email).first()
                    if public_admin:
                        print(f"Admin user found in public schema: {public_admin.email} (Role: {public_admin.role})")
                    else:
                        print(f"Admin user not found in public schema (This is good!)")
            else:
                print(f"Tenant not found in database")
        else:
            print(f"Error: {response.text}")
    except Exception as e:
        print(f"Error making API call: {str(e)}")

if __name__ == "__main__":
    test_tenant_registration()
