"""
Script to clean up blacklisted tokens in the accounts_blacklistedtoken table.
This script can be used to manually clean up blacklisted tokens when they accumulate.
"""

import os
import sys
import django
from datetime import datetime, timedelta

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'neocamelot.settings')
django.setup()

from django.db import connection
from django_tenants.utils import tenant_context
from centers.models import Client
from django.utils import timezone

def cleanup_blacklisted_tokens_in_schema(schema_name, days_old=None):
    """
    Clean up blacklisted tokens in a specific schema.
    
    Args:
        schema_name (str): The schema name of the tenant
        days_old (int, optional): Only delete tokens older than this many days
        
    Returns:
        int: The number of tokens deleted
    """
    print(f"\n=== Cleaning up blacklisted tokens in schema {schema_name} ===")
    
    try:
        # Get the tenant
        tenant = Client.objects.get(schema_name=schema_name)
        print(f"Found tenant: {tenant.name} (Schema: {tenant.schema_name})")
        
        # Switch to the tenant context
        with tenant_context(tenant):
            # Import BlacklistedToken model
            from accounts.models import BlacklistedToken
            
            # Get current count
            total_count = BlacklistedToken.objects.count()
            print(f"Found {total_count} blacklisted tokens in {schema_name}")
            
            # Create query
            query = BlacklistedToken.objects
            
            # Filter by age if specified
            if days_old is not None:
                cutoff_date = timezone.now() - timedelta(days=days_old)
                query = query.filter(blacklisted_at__lt=cutoff_date)
                older_count = query.count()
                print(f"Of these, {older_count} are older than {days_old} days")
            
            # Delete the tokens
            deleted_count = query.delete()[0]
            print(f"Deleted {deleted_count} blacklisted tokens")
            
            return deleted_count
    except Client.DoesNotExist:
        print(f"Tenant with schema {schema_name} does not exist")
        return 0
    except Exception as e:
        print(f"Error cleaning up blacklisted tokens: {str(e)}")
        return 0

def cleanup_all_schemas(days_old=None):
    """
    Clean up blacklisted tokens in all schemas.
    
    Args:
        days_old (int, optional): Only delete tokens older than this many days
        
    Returns:
        int: The total number of tokens deleted
    """
    print(f"\n=== Cleaning up blacklisted tokens in all schemas ===")
    
    try:
        # Get all tenants
        tenants = Client.objects.all()
        total_deleted = 0
        
        for tenant in tenants:
            deleted = cleanup_blacklisted_tokens_in_schema(tenant.schema_name, days_old)
            total_deleted += deleted
        
        print(f"\nTotal deleted across all schemas: {total_deleted}")
        return total_deleted
    except Exception as e:
        print(f"Error cleaning up all schemas: {str(e)}")
        return 0

def list_all_schemas():
    """
    List all available schemas.
    
    Returns:
        list: A list of schema names
    """
    try:
        # Get all tenants
        tenants = Client.objects.all()
        schemas = [tenant.schema_name for tenant in tenants]
        return schemas
    except Exception as e:
        print(f"Error listing schemas: {str(e)}")
        return []

def count_blacklisted_tokens_in_schema(schema_name):
    """
    Count blacklisted tokens in a specific schema.
    
    Args:
        schema_name (str): The schema name of the tenant
        
    Returns:
        dict: A dictionary with token counts by type and age
    """
    print(f"\n=== Counting blacklisted tokens in schema {schema_name} ===")
    
    try:
        # Get the tenant
        tenant = Client.objects.get(schema_name=schema_name)
        print(f"Found tenant: {tenant.name} (Schema: {tenant.schema_name})")
        
        # Switch to the tenant context
        with tenant_context(tenant):
            # Import BlacklistedToken model
            from accounts.models import BlacklistedToken
            
            # Get current count
            total_count = BlacklistedToken.objects.count()
            print(f"Found {total_count} blacklisted tokens in {schema_name}")
            
            # Count by token type
            refresh_count = BlacklistedToken.objects.filter(token_type='refresh').count()
            access_count = BlacklistedToken.objects.filter(token_type='access').count()
            print(f"Refresh tokens: {refresh_count}")
            print(f"Access tokens: {access_count}")
            
            # Count by age
            now = timezone.now()
            last_day = BlacklistedToken.objects.filter(blacklisted_at__gt=now - timedelta(days=1)).count()
            last_week = BlacklistedToken.objects.filter(blacklisted_at__gt=now - timedelta(days=7)).count()
            last_month = BlacklistedToken.objects.filter(blacklisted_at__gt=now - timedelta(days=30)).count()
            
            print(f"Tokens from last 24 hours: {last_day}")
            print(f"Tokens from last 7 days: {last_week}")
            print(f"Tokens from last 30 days: {last_month}")
            
            return {
                'total': total_count,
                'refresh': refresh_count,
                'access': access_count,
                'last_day': last_day,
                'last_week': last_week,
                'last_month': last_month
            }
    except Client.DoesNotExist:
        print(f"Tenant with schema {schema_name} does not exist")
        return None
    except Exception as e:
        print(f"Error counting blacklisted tokens: {str(e)}")
        return None

if __name__ == "__main__":
    # Check if we have command line arguments
    if len(sys.argv) < 2:
        print("Usage:")
        print("  python cleanup_blacklisted_tokens.py list_schemas")
        print("  python cleanup_blacklisted_tokens.py count <schema_name>")
        print("  python cleanup_blacklisted_tokens.py cleanup <schema_name> [days_old]")
        print("  python cleanup_blacklisted_tokens.py cleanup_all [days_old]")
        sys.exit(1)
    
    command = sys.argv[1]
    
    if command == "list_schemas":
        schemas = list_all_schemas()
        print("\nAvailable schemas:")
        for schema in schemas:
            print(f"  {schema}")
    
    elif command == "count":
        if len(sys.argv) < 3:
            print("Usage: python cleanup_blacklisted_tokens.py count <schema_name>")
            sys.exit(1)
        
        schema_name = sys.argv[2]
        count_blacklisted_tokens_in_schema(schema_name)
    
    elif command == "cleanup":
        if len(sys.argv) < 3:
            print("Usage: python cleanup_blacklisted_tokens.py cleanup <schema_name> [days_old]")
            sys.exit(1)
        
        schema_name = sys.argv[2]
        days_old = int(sys.argv[3]) if len(sys.argv) > 3 else None
        
        deleted = cleanup_blacklisted_tokens_in_schema(schema_name, days_old)
        print(f"\nDeleted {deleted} blacklisted tokens from {schema_name}")
    
    elif command == "cleanup_all":
        days_old = int(sys.argv[2]) if len(sys.argv) > 2 else None
        
        deleted = cleanup_all_schemas(days_old)
        print(f"\nDeleted {deleted} blacklisted tokens from all schemas")
    
    else:
        print(f"Unknown command: {command}")
        print("Usage:")
        print("  python cleanup_blacklisted_tokens.py list_schemas")
        print("  python cleanup_blacklisted_tokens.py count <schema_name>")
        print("  python cleanup_blacklisted_tokens.py cleanup <schema_name> [days_old]")
        print("  python cleanup_blacklisted_tokens.py cleanup_all [days_old]")
