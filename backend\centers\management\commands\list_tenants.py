from django.core.management.base import BaseCommand
from centers.models import Client

class Command(BaseCommand):
    help = 'List all tenants in the system'

    def handle(self, *args, **options):
        # Count tenants by type
        city_count = Client.objects.filter(schema_type='CITY').count()
        subcity_count = Client.objects.filter(schema_type='SUBCITY').count()
        kebele_count = Client.objects.filter(schema_type='KEBELE').count()
        
        self.stdout.write(self.style.SUCCESS(f'Cities: {city_count}'))
        self.stdout.write(self.style.SUCCESS(f'Subcities: {subcity_count}'))
        self.stdout.write(self.style.SUCCESS(f'Kebeles: {kebele_count}'))
        
        # List all tenants
        self.stdout.write('\nList of all tenants:')
        for tenant in Client.objects.all():
            parent_name = tenant.parent.name if tenant.parent else None
            self.stdout.write(f'- {tenant.name} (Type: {tenant.schema_type}, Schema: {tenant.schema_name}, Parent: {parent_name})')
