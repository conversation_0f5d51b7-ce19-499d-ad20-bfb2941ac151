# Generated by Django 5.1.7 on 2025-04-14 16:38

import django.db.models.deletion
import django_tenants.postgresql_backend.base
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('centers', '0006_alter_center_options_alter_center_name_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Client',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('schema_name', models.CharField(db_index=True, max_length=63, unique=True, validators=[django_tenants.postgresql_backend.base._check_schema_name])),
                ('name', models.CharField(max_length=100)),
                ('schema_type', models.CharField(choices=[('CITY', 'City'), ('SUBCITY', 'Subcity'), ('KEBELE', 'Kebele')], max_length=10)),
                ('description', models.TextField(blank=True)),
                ('address', models.TextField(blank=True)),
                ('phone', models.CharField(blank=True, max_length=20)),
                ('email', models.EmailField(blank=True, max_length=254)),
                ('website', models.URLField(blank=True)),
                ('logo', models.ImageField(blank=True, null=True, upload_to='tenant_logos/')),
                ('header_color', models.CharField(blank=True, default='#007bff', max_length=7)),
                ('accent_color', models.CharField(blank=True, default='#28a745', max_length=7)),
                ('admin_name', models.CharField(blank=True, max_length=100)),
                ('admin_email', models.EmailField(blank=True, max_length=254)),
                ('admin_phone', models.CharField(blank=True, max_length=20)),
                ('is_active', models.BooleanField(default=True)),
                ('has_printing_facility', models.BooleanField(default=False)),
                ('printing_capacity', models.PositiveIntegerField(default=0, help_text='Number of ID cards that can be printed per day')),
                ('center_type', models.CharField(blank=True, max_length=50)),
                ('is_verified', models.BooleanField(default=False)),
                ('subscription_status', models.CharField(choices=[('trial', 'Trial'), ('basic', 'Basic'), ('premium', 'Premium'), ('suspended', 'Suspended')], default='trial', max_length=20)),
                ('subscription_expiry', models.DateField(blank=True, null=True)),
                ('max_users', models.PositiveIntegerField(default=5)),
                ('max_citizens', models.PositiveIntegerField(default=1000)),
                ('max_id_cards', models.PositiveIntegerField(default=1000)),
                ('settings', models.JSONField(blank=True, default=dict)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_tenants', to=settings.AUTH_USER_MODEL)),
                ('parent', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='children', to='centers.client')),
            ],
            options={
                'verbose_name': 'Tenant',
                'verbose_name_plural': 'Tenants',
            },
        ),
        migrations.CreateModel(
            name='Domain',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('domain', models.CharField(db_index=True, max_length=253, unique=True)),
                ('is_primary', models.BooleanField(db_index=True, default=True)),
                ('tenant', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='domains', to='centers.client')),
            ],
            options={
                'abstract': False,
            },
        ),
    ]
