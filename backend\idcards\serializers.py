from rest_framework import serializers
from .models import IDCard, IDCardTemplate

class IDCardTemplateSerializer(serializers.ModelSerializer):
    """Serializer for the IDCardTemplate model."""
    center_name = serializers.CharField(source='center.name', read_only=True)

    class Meta:
        model = IDCardTemplate
        fields = '__all__'
        read_only_fields = ('created_at', 'updated_at')

class IDCardTemplateListSerializer(serializers.ModelSerializer):
    """Simplified serializer for listing ID card templates."""
    center_name = serializers.CharField(source='center.name', read_only=True)

    class Meta:
        model = IDCardTemplate
        fields = ('id', 'name', 'center', 'center_name', 'is_default')

class IDCardSerializer(serializers.ModelSerializer):
    """Serializer for the IDCard model."""
    citizen_name = serializers.CharField(source='citizen.full_name', read_only=True)
    template_name = serializers.CharField(source='template.name', read_only=True)
    center_name = serializers.CharField(source='citizen.center.name', read_only=True)
    is_expired = serializers.BooleanField(read_only=True)
    days_until_expiry = serializers.IntegerField(read_only=True)
    citizen_id_number = serializers.CharField(source='citizen.id_number', read_only=True)
    citizen_photo = serializers.ImageField(source='citizen.photo', read_only=True)
    document_verified_by_name = serializers.CharField(source='document_verified_by.get_full_name', read_only=True)
    approved_by_name = serializers.CharField(source='approved_by.get_full_name', read_only=True)
    kebele_approved_by_name = serializers.CharField(source='kebele_approved_by.get_full_name', read_only=True, allow_null=True)

    # Security pattern fields
    pattern_status = serializers.SerializerMethodField()
    pattern_data_url = serializers.SerializerMethodField()

    def get_pattern_status(self, obj):
        """Get the status of the security pattern."""
        from .security_patterns import get_pattern_status
        return get_pattern_status(obj)

    def get_pattern_data_url(self, obj):
        """Get the data URL for the security pattern SVG."""
        from .security_patterns import get_pattern_data_url
        return get_pattern_data_url(obj)

    class Meta:
        model = IDCard
        fields = '__all__'
        read_only_fields = ('created_at', 'updated_at', 'is_expired',
                           'days_until_expiry', 'citizen_name', 'template_name', 'center_name',
                           'document_verified_at', 'document_verified_by', 'document_verified_by_name',
                           'approved_by_name', 'kebele_pattern', 'subcity_pattern', 'pattern_seed',
                           'pattern_status', 'pattern_data_url', 'kebele_approved_by', 'kebele_approved_by_name',
                           'kebele_approved_at')

    def create(self, validated_data):
        # If no card_number is provided, generate a temporary one
        if 'card_number' not in validated_data or not validated_data['card_number']:
            import uuid
            import time
            validated_data['card_number'] = f"TEMP-{int(time.time())}-{str(uuid.uuid4())[:8]}"
        return super().create(validated_data)

class IDCardListSerializer(serializers.ModelSerializer):
    """Simplified serializer for listing ID cards."""
    citizen_name = serializers.CharField(source='citizen.full_name', read_only=True)
    citizen_id_number = serializers.CharField(source='citizen.id_number', read_only=True)

    class Meta:
        model = IDCard
        fields = ('id', 'card_number', 'citizen', 'citizen_name', 'citizen_id_number', 'issue_date',
                  'expiry_date', 'status', 'document_verification_status', 'kebele_approval_status')
