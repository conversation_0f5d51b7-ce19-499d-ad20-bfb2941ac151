import React from 'react';
import { Box, Container, Typo<PERSON>, Button } from '@mui/material';
import { useNavigate } from 'react-router-dom';
import PageBanner from '../components/PageBanner';
import PersonIcon from '@mui/icons-material/Person';
import CitizenDetails from './CitizenDetails';

const TestCitizenDetails: React.FC = () => {
  const navigate = useNavigate();

  // Set mock data in localStorage if not already set
  React.useEffect(() => {
    const mockUser = {
      id: 1,
      email: '<EMAIL>',
      first_name: 'Admin',
      last_name: 'User',
      role: 'admin'
    };

    const mockTenant = {
      schema_name: 'kebele_01',
      name: '<PERSON>bel<PERSON> 01',
      type: 'kebele'
    };

    // Set mock data in localStorage
    localStorage.setItem('token', 'mock-token-12345');
    localStorage.setItem('user', JSON.stringify(mockUser));
    localStorage.setItem('tenant', JSON.stringify(mockTenant));
    localStorage.setItem('schema_name', 'kebele_01');

    console.log('Mock login data set in localStorage');
  }, []);

  return (
    <Box sx={{ minHeight: '100vh', bgcolor: '#f5f5f5' }}>
      <PageBanner
        title="Test Citizen Details"
        subtitle="This is a test page to view the citizen details component"
        icon={<PersonIcon sx={{ fontSize: 40, color: 'white' }} />}
      />
      <Container maxWidth="xl" sx={{ py: 4 }}>
        <Box sx={{ mb: 3 }}>
          <Typography variant="h6" gutterBottom>
            Test Environment
          </Typography>
          <Typography variant="body2" color="text.secondary" paragraph>
            This page is for testing the citizen details component with mock data.
          </Typography>
          <Button 
            variant="contained" 
            color="primary" 
            onClick={() => navigate('/dashboard')}
            sx={{ mr: 2 }}
          >
            Go to Dashboard
          </Button>
          <Button 
            variant="outlined" 
            color="primary" 
            onClick={() => window.location.reload()}
          >
            Reload Page
          </Button>
        </Box>
        
        <CitizenDetails />
      </Container>
    </Box>
  );
};

export default TestCitizenDetails;
