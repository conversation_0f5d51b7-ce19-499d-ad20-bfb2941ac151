import os
import django

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'neocamelot.settings')
django.setup()

# Import models
from centers.models import Client
from django.contrib.auth import get_user_model
from django_tenants.utils import tenant_context

User = get_user_model()

def fix_tenant_admin_roles():
    """Fix admin user roles in tenant schemas to match the tenant type."""
    print("\n===== FIXING TENANT ADMIN ROLES =====\n")

    # Get all tenants except public
    tenants = Client.objects.exclude(schema_name='public')
    print(f"Found {tenants.count()} tenants to check")

    for tenant in tenants:
        print(f"\nChecking tenant: {tenant.name} (Schema: {tenant.schema_name})")

        # Determine the correct role based on tenant type
        correct_role = 'KEBELE_ADMIN' if tenant.schema_type == 'KEBELE' else \
                      'SUBCITY_ADMIN' if tenant.schema_type == 'SUBCITY' else \
                      'CITY_ADMIN'

        # Check and update admin users
        with tenant_context(tenant):
            admin_users = User.objects.filter(role__in=['CENTER_ADMIN', 'SUBCITY_ADMIN', 'CITY_ADMIN'])
            if admin_users.exists():
                print(f"Found {admin_users.count()} admin users in {tenant.schema_name} schema")
                for user in admin_users:
                    if user.role != correct_role:
                        old_role = user.role
                        user.role = correct_role
                        user.save()
                        print(f"- Updated {user.email} role from {old_role} to {correct_role}")
                    else:
                        print(f"- {user.email} already has correct role: {user.role}")
            else:
                print(f"No admin users found in {tenant.schema_name} schema")

    print("\n===== TENANT ADMIN ROLES FIX COMPLETE =====\n")

if __name__ == '__main__':
    fix_tenant_admin_roles()
