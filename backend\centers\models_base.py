from django.db import models
from .managers import TenantManager, TenantAwareManager

class TenantModel(models.Model):
    """
    Base model for all tenant-aware models.
    
    This model:
    1. Has a ForeignKey to the Center model
    2. Provides managers for tenant-aware queries
    3. Enforces tenant isolation
    """
    
    # The center (tenant) this object belongs to
    center = models.ForeignKey(
        'centers.Center',
        on_delete=models.CASCADE,
        related_name='%(class)ss',
        verbose_name='Center'
    )
    
    # Managers
    objects = TenantManager()
    tenant_objects = TenantAwareManager()
    
    class Meta:
        abstract = True
    
    def save(self, *args, **kwargs):
        """
        Override save to set the center if not provided.
        
        This ensures that all tenant-aware models have a center.
        """
        if not self.center_id:
            from .middleware import get_current_tenant
            tenant = get_current_tenant()
            if tenant:
                self.center = tenant
        super().save(*args, **kwargs)
