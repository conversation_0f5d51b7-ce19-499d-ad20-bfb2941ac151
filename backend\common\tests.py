from django.test import TestCase
from django.urls import reverse
from rest_framework.test import APIClient
from rest_framework import status
from django.contrib.auth import get_user_model
from .models import Religion, CitizenStatus, MaritalStatus, EmploymentType, RelationshipType

User = get_user_model()

class CommonAPITestCase(TestCase):
    """
    Test case for the common API endpoints.
    """
    def setUp(self):
        self.client = APIClient()
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpassword'
        )
        self.client.force_authenticate(user=self.user)

        # Create test data
        Religion.objects.create(name='Test Religion')
        CitizenStatus.objects.create(name='Test Status')
        MaritalStatus.objects.create(id='TEST', name='Test Marital Status')
        EmploymentType.objects.create(name='Test Employment Type')
        RelationshipType.objects.create(name='Test Relationship Type')

    def test_get_religions(self):
        """
        Test retrieving religions.
        """
        url = reverse('religion-list')
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(len(response.data) > 0)

    def test_get_citizen_statuses(self):
        """
        Test retrieving citizen statuses.
        """
        url = reverse('citizenstatus-list')
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(len(response.data) > 0)

    def test_get_marital_statuses(self):
        """
        Test retrieving marital statuses.
        """
        url = reverse('maritalstatus-list')
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(len(response.data) > 0)

    def test_get_employment_types(self):
        """
        Test retrieving employment types.
        """
        url = reverse('employmenttype-list')
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(len(response.data) > 0)

    def test_get_relationship_types(self):
        """
        Test retrieving relationship types.
        """
        url = reverse('relationshiptype-list')
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(len(response.data) > 0)
