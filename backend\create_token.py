import os
import django
import sys

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'neocamelot.settings')
django.setup()

from django.contrib.auth import get_user_model
from rest_framework.authtoken.models import Token
from django.db import connection
from django_tenants.utils import tenant_context, get_tenant_model
from centers.models import Client

User = get_user_model()

def create_token_for_schema(schema_name):
    """Create a token for a user in a specific schema."""
    print(f"\n=== Creating token for schema {schema_name} ===")
    
    try:
        # Get the tenant
        tenant = Client.objects.get(schema_name=schema_name)
        print(f"Found tenant: {tenant.name} (Schema: {tenant.schema_name})")
        
        # Switch to the tenant context
        with tenant_context(tenant):
            # Get all users
            users = User.objects.all()
            print(f"Found {len(users)} users in {schema_name}")
            
            if not users.exists():
                print(f"No users found in {schema_name}")
                return None
            
            # Create or get token for the first user
            user = users.first()
            token, created = Token.objects.get_or_create(user=user)
            
            print(f"User: {user.email} (ID: {user.id})")
            print(f"Token: {token.key}")
            print(f"Created: {created}")
            
            return token.key
    except Client.DoesNotExist:
        print(f"Tenant with schema {schema_name} does not exist")
        return None
    except Exception as e:
        print(f"Error creating token: {str(e)}")
        return None

if __name__ == "__main__":
    schema_name = "kebele 14"
    if len(sys.argv) > 1:
        schema_name = sys.argv[1]
    
    create_token_for_schema(schema_name)
