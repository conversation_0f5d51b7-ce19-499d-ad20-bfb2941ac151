from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from django_tenants.utils import tenant_context
from centers.models import Client, Center

User = get_user_model()

class Command(BaseCommand):
    help = 'Create test users for different roles in different tenants'

    def add_arguments(self, parser):
        parser.add_argument('--create-all', action='store_true', help='Create all test users')

    def handle(self, *args, **options):
        create_all = options.get('create_all', False)
        
        # Create subcity admin
        if create_all:
            self.create_subcity_admin()
            
        # Create kebele leader
        self.create_kebele_leader()
        
    def create_subcity_admin(self):
        """Create a subcity admin user"""
        try:
            # Find a subcity tenant
            subcity_tenants = Client.objects.filter(schema_type='SUBCITY')
            if not subcity_tenants.exists():
                self.stdout.write(self.style.ERROR('No subcity tenants found'))
                return
                
            subcity = subcity_tenants.first()
            self.stdout.write(self.style.SUCCESS(f'Using subcity tenant: {subcity.name}'))
            
            # Switch to the subcity tenant context
            with tenant_context(subcity):
                # Check if user already exists
                email = '<EMAIL>'
                if User.objects.filter(email=email).exists():
                    self.stdout.write(self.style.WARNING(f'User with email {email} already exists in tenant {subcity.name}'))
                    return
                
                # Get the first center in the tenant
                center = Center.objects.first()
                if not center:
                    self.stdout.write(self.style.ERROR(f'No center found in tenant {subcity.name}'))
                    return
                
                # Create the subcity admin user
                user = User.objects.create_user(
                    email=email,
                    password='password123',
                    first_name='Subcity',
                    last_name='Admin',
                    role='SUBCITY_ADMIN',
                    center=center,
                    is_active=True
                )
                
                self.stdout.write(self.style.SUCCESS(f'Successfully created subcity admin {email} in tenant {subcity.name}'))
                
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'Error creating subcity admin: {str(e)}'))
            
    def create_kebele_leader(self):
        """Create a kebele leader user in a kebele tenant"""
        try:
            # Find a kebele tenant
            kebele_tenants = Client.objects.filter(schema_type='KEBELE')
            if not kebele_tenants.exists():
                self.stdout.write(self.style.ERROR('No kebele tenants found'))
                return
                
            kebele = kebele_tenants.first()
            self.stdout.write(self.style.SUCCESS(f'Using kebele tenant: {kebele.name}'))
            
            # Switch to the kebele tenant context
            with tenant_context(kebele):
                # Check if user already exists
                email = '<EMAIL>'
                if User.objects.filter(email=email).exists():
                    self.stdout.write(self.style.WARNING(f'User with email {email} already exists in tenant {kebele.name}'))
                    return
                
                # Get the first center in the tenant
                center = Center.objects.first()
                if not center:
                    self.stdout.write(self.style.ERROR(f'No center found in tenant {kebele.name}'))
                    return
                
                # Create the kebele leader user
                user = User.objects.create_user(
                    email=email,
                    password='password123',
                    first_name='Kebele',
                    last_name='Leader',
                    role='KEBELE_LEADER',
                    center=center,
                    is_active=True
                )
                
                self.stdout.write(self.style.SUCCESS(f'Successfully created kebele leader {email} in tenant {kebele.name}'))
                self.stdout.write(self.style.SUCCESS(f'Login credentials: Email: {email}, Password: password123'))
                
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'Error creating kebele leader: {str(e)}'))
