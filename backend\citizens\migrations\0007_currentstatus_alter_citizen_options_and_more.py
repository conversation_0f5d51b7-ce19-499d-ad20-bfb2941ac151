# Generated by Django 5.1.7 on 2025-04-17 15:37

import django.db.models.deletion
import uuid
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('centers', '0013_employeetype'),
        ('citizens', '0006_biometric_photo'),
    ]

    operations = [
        migrations.CreateModel(
            name='CurrentStatus',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(help_text='Current status of the citizen', max_length=50, unique=True)),
            ],
            options={
                'verbose_name': 'Current Status',
                'verbose_name_plural': 'Current Statuses',
            },
        ),
        migrations.AlterModelOptions(
            name='citizen',
            options={'ordering': ['-created_at'], 'verbose_name': 'Citizen', 'verbose_name_plural': 'Citizens'},
        ),
        migrations.AlterUniqueTogether(
            name='citizen',
            unique_together=set(),
        ),
        migrations.AddField(
            model_name='citizen',
            name='biometric_data',
            field=models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='citizen_biometric', to='citizens.biometric'),
        ),
        migrations.AddField(
            model_name='citizen',
            name='digital_id',
            field=models.CharField(blank=True, max_length=50, null=True),
        ),
        migrations.AddField(
            model_name='citizen',
            name='emergency_contact',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='citizen_as_emergency', to='citizens.emergencycontact'),
        ),
        migrations.AddField(
            model_name='citizen',
            name='employee_type',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='centers.employeetype'),
        ),
        migrations.AddField(
            model_name='citizen',
            name='employment',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='citizen',
            name='father',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='children_as_father', to='citizens.parent'),
        ),
        migrations.AddField(
            model_name='citizen',
            name='first_name_am',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='citizen',
            name='house_number',
            field=models.CharField(blank=True, max_length=20, null=True),
        ),
        migrations.AddField(
            model_name='citizen',
            name='id_expiry_date',
            field=models.DateField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='citizen',
            name='id_issue_date',
            field=models.DateField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='citizen',
            name='is_resident',
            field=models.BooleanField(default=False, help_text='Indicates whether the citizen is a resident of the city.'),
        ),
        migrations.AddField(
            model_name='citizen',
            name='kebele',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='centers.center'),
        ),
        migrations.AddField(
            model_name='citizen',
            name='ketena',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.PROTECT, to='centers.ketena'),
        ),
        migrations.AddField(
            model_name='citizen',
            name='last_name_am',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='citizen',
            name='middle_name',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='citizen',
            name='middle_name_am',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='citizen',
            name='mother',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='children_as_mother', to='citizens.parent'),
        ),
        migrations.AddField(
            model_name='citizen',
            name='organization_name',
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='citizen',
            name='photo_record',
            field=models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='citizen_photo', to='citizens.photo'),
        ),
        migrations.AddField(
            model_name='citizen',
            name='region',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='centers.region'),
        ),
        migrations.AddField(
            model_name='citizen',
            name='spouse',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='citizen_as_spouse', to='citizens.spouse'),
        ),
        migrations.AddField(
            model_name='citizen',
            name='subcity',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='centers.subcity'),
        ),
        migrations.AlterField(
            model_name='citizen',
            name='address',
            field=models.TextField(blank=True),
        ),
        migrations.AlterField(
            model_name='citizen',
            name='citizen_status',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.PROTECT, to='centers.citizenstatus'),
        ),
        migrations.AlterField(
            model_name='citizen',
            name='email',
            field=models.EmailField(blank=True, max_length=254, null=True),
        ),
        # Removed problematic operation to change ID field type
        # Removed problematic operation to change nationality field
        migrations.AlterField(
            model_name='citizen',
            name='phone',
            field=models.CharField(blank=True, max_length=20, null=True),
        ),
        migrations.AlterField(
            model_name='photo',
            name='citizen',
            field=models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='photo_obj', to='citizens.citizen'),
        ),
        migrations.AddField(
            model_name='citizen',
            name='current_status',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='citizens.currentstatus'),
        ),
        migrations.RemoveField(
            model_name='citizen',
            name='photo',
        ),
        migrations.RemoveField(
            model_name='citizen',
            name='registration_number',
        ),
    ]
