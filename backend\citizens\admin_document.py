from django.contrib import admin
from .models_document import Document

@admin.register(Document)
class DocumentAdmin(admin.ModelAdmin):
    list_display = ('document_type', 'citizen', 'issue_date', 'expiry_date', 'is_active', 'created_at')
    list_filter = ('document_type', 'is_active', 'issue_date', 'expiry_date')
    search_fields = ('citizen__first_name', 'citizen__last_name', 'document_type__name')
    readonly_fields = ('created_at', 'updated_at')
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('document_type', 'citizen', 'document_file')
        }),
        ('Date Information', {
            'fields': ('issue_date', 'expiry_date')
        }),
        ('Status', {
            'fields': ('is_active',)
        }),
        ('System Information', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def save_model(self, request, obj, form, change):
        if not change:  # Only set created_by when creating a new object
            if hasattr(obj, 'created_by') and hasattr(request, 'user'):
                obj.created_by = request.user
        super().save_model(request, obj, form, change)
