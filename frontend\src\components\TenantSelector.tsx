import React, { useState, useEffect } from 'react';
import { Box, Typography, Paper, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Button, CircularProgress, TextField, InputAdornment } from '@mui/material';
import SearchIcon from '@mui/icons-material/Search';
import { useAuth } from '../contexts/AuthContext';

interface Tenant {
  id: number;
  name: string;
  schema_name: string;
  schema_type: string;
  parent: string | null;
}

// No need for a login data interface since we're not logging in directly

const TenantSelector: React.FC = () => {
  const [tenants, setTenants] = useState<Tenant[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [loginLoading, setLoginLoading] = useState<number | null>(null);
  const { setSchema } = useAuth(); // We only need setSchema since we're not setting a token

  useEffect(() => {
    const fetchTenants = async () => {
      setLoading(true);
      setError(null);

      try {
        const response = await fetch('http://127.0.0.1:8000/api/available-tenants/');

        if (!response.ok) {
          throw new Error(`API error: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();
        setTenants(data);
      } catch (err) {
        console.error('Error fetching tenants:', err);
        setError('Failed to fetch tenants. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchTenants();
  }, []);

  const handleSelectTenant = async (tenant: Tenant, index: number) => {
    try {
      setLoginLoading(index);
      console.log(`Selecting tenant: ${tenant.name} (${tenant.schema_name})`);

      // Create a tenant object with the necessary information
      const tenantData = {
        id: tenant.id,
        schema_name: tenant.schema_name,
        name: tenant.name,
        type: tenant.schema_type
      };

      // Store tenant data in localStorage
      localStorage.setItem('schema_name', tenant.schema_name);
      localStorage.setItem('tenant', JSON.stringify(tenantData));

      // Update auth context with the schema
      setSchema(tenant.schema_name);

      // Set a cookie with the schema name for backend compatibility
      // Make sure to set the path to / so it's available for all requests
      document.cookie = `schema_name=${encodeURIComponent(tenant.schema_name)}; path=/`;

      console.log(`Set schema_name to: ${tenant.schema_name}`);
      console.log(`Stored tenant data: ${JSON.stringify(tenantData)}`);

      // Now we need to get a valid token for this tenant
      try {
        // Try to get a token using the login API
        const loginResponse = await fetch('http://127.0.0.1:8000/api/login/', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'X-Schema-Name': tenant.schema_name
          },
          body: JSON.stringify({
            // Use the admin credentials for now
            // In a real app, you would prompt the user for credentials
            email: '<EMAIL>',
            password: 'password123',
            schema_name: tenant.schema_name
          }),
          credentials: 'include'
        });

        if (loginResponse.ok) {
          const loginData = await loginResponse.json();

          if (loginData.token) {
            console.log('Login successful, got token');

            // Store the token in localStorage
            localStorage.setItem('token', loginData.token);

            // Store user data if available
            if (loginData.user) {
              localStorage.setItem('user', JSON.stringify(loginData.user));
            }

            // Show success message
            alert(`Logged in to ${tenant.name} (${tenant.schema_name})`);

            // Redirect to dashboard
            window.location.href = '/dashboard';
            return;
          }
        } else {
          console.log(`Login failed: ${loginResponse.status} ${loginResponse.statusText}`);

          // Try to get more details about the error
          try {
            const errorData = await loginResponse.json();
            console.error('Login error details:', errorData);
          } catch (e) {
            console.error('Could not parse error response');
          }
        }
      } catch (error) {
        console.error('Error logging in:', error);
      }

      // If we get here, login failed or we couldn't get a token
      // Just redirect to the login page with the schema name as a parameter
      console.log('Redirecting to login page with schema parameter');
      alert(`Selected tenant: ${tenant.name} (${tenant.schema_name}). Please log in with your credentials.`);
      window.location.href = `/login?schema=${encodeURIComponent(tenant.schema_name)}`;
    } catch (err) {
      console.error('Error selecting tenant:', err);
      alert(`Failed to select ${tenant.name}. Please try again later.`);
    } finally {
      setLoginLoading(null);
    }
  };

  // Filter tenants based on search query
  const filteredTenants = tenants.filter(tenant =>
    tenant.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    tenant.schema_name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    tenant.schema_type.toLowerCase().includes(searchQuery.toLowerCase())
  );

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="200px">
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{ p: 2 }}>
        <Typography color="error">{error}</Typography>
      </Box>
    );
  }

  return (
    <Box sx={{ width: '100%', p: 2 }}>
      <Typography variant="h5" gutterBottom>
        Tenant Selector
      </Typography>

      <TextField
        fullWidth
        margin="normal"
        variant="outlined"
        placeholder="Search tenants..."
        value={searchQuery}
        onChange={(e) => setSearchQuery(e.target.value)}
        sx={{ mb: 2 }}
        // TODO: InputProps is deprecated but still used throughout the codebase
        // Should be updated in a future refactoring
        InputProps={{
          startAdornment: (
            <InputAdornment position="start">
              <SearchIcon />
            </InputAdornment>
          ),
        }}
      />

      <TableContainer component={Paper} sx={{ mb: 4, boxShadow: '0 4px 12px rgba(0,0,0,0.1)', borderRadius: 2 }}>
        <Table>
          <TableHead sx={{ backgroundColor: '#f5f5f5' }}>
            <TableRow>
              <TableCell><Typography fontWeight="bold">Name</Typography></TableCell>
              <TableCell><Typography fontWeight="bold">Schema Name</Typography></TableCell>
              <TableCell><Typography fontWeight="bold">Type</Typography></TableCell>
              <TableCell><Typography fontWeight="bold">Parent</Typography></TableCell>
              <TableCell><Typography fontWeight="bold">Actions</Typography></TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {filteredTenants.map((tenant, index) => (
              <TableRow key={tenant.id} hover>
                <TableCell>{tenant.name}</TableCell>
                <TableCell>{tenant.schema_name}</TableCell>
                <TableCell>{tenant.schema_type}</TableCell>
                <TableCell>{tenant.parent || 'None'}</TableCell>
                <TableCell>
                  <Button
                    variant="contained"
                    color="primary"
                    size="small"
                    onClick={() => handleSelectTenant(tenant, index)}
                    disabled={loginLoading === index}
                  >
                    {loginLoading === index ? <CircularProgress size={20} /> : 'Select'}
                  </Button>
                </TableCell>
              </TableRow>
            ))}
            {filteredTenants.length === 0 && (
              <TableRow>
                <TableCell colSpan={5} align="center">
                  <Typography variant="body1" sx={{ py: 2 }}>
                    No tenants found matching your search.
                  </Typography>
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </TableContainer>

      <Typography variant="body2" color="textSecondary">
        Select a tenant to log in. This will set the token and schema name for API requests.
      </Typography>
    </Box>
  );
};

export default TenantSelector;
