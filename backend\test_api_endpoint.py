import requests
import json

# Base URL
base_url = 'http://127.0.0.1:8000/api/'

# Token and schema name
token = '9425ec3171e23a01903152c33f7a212106916a4b'
schema_name = 'subcity_zoble'

# Test the statistics endpoint
print("\n=== Testing ID Card Statistics API ===")
try:
    url = base_url + 'idcards/statistics/'
    headers = {
        'Authorization': f'Token {token}',
        'Content-Type': 'application/json',
        'X-Schema-Name': schema_name
    }
    
    print(f"URL: {url}")
    print(f"Headers: {headers}")
    
    response = requests.get(url, headers=headers)
    print(f"Status Code: {response.status_code}")
    
    if response.status_code == 200:
        data = response.json()
        print(f"Response Data: {json.dumps(data, indent=2)}")
    else:
        print(f"Error: {response.text}")
        
except Exception as e:
    print(f"Error testing API: {str(e)}")

# Test the tenant-specific endpoint
print("\n=== Testing Tenant-Specific ID Card API ===")
try:
    url = base_url + f'tenant/{schema_name}/idcards/'
    headers = {
        'Authorization': f'Token {token}',
        'Content-Type': 'application/json',
        'X-Schema-Name': schema_name
    }
    
    print(f"URL: {url}")
    print(f"Headers: {headers}")
    
    response = requests.get(url, headers=headers)
    print(f"Status Code: {response.status_code}")
    
    if response.status_code == 200:
        data = response.json()
        print(f"Response Data: {json.dumps(data, indent=2)}")
    else:
        print(f"Error: {response.text}")
        
except Exception as e:
    print(f"Error testing API: {str(e)}")
