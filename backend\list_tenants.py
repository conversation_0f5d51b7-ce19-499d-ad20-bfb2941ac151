import os
import django

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'neocamelot.settings')
django.setup()

# Import models
from centers.models import Client, Domain

# List all tenants
print("\n=== All Tenants ===")
tenants = Client.objects.all()
for tenant in tenants:
    print(f"ID: {tenant.id}")
    print(f"Name: {tenant.name}")
    print(f"Schema Name: {tenant.schema_name}")
    print(f"Schema Type: {tenant.schema_type}")
    if tenant.parent:
        print(f"Parent: {tenant.parent.name} ({tenant.parent.schema_type})")
    else:
        print("Parent: None")
    print("-" * 30)

# List all domains
print("\n=== All Domains ===")
domains = Domain.objects.all()
for domain in domains:
    print(f"Domain: {domain.domain}")
    print(f"Tenant: {domain.tenant.name}")
    print(f"Is Primary: {domain.is_primary}")
    print("-" * 30)
