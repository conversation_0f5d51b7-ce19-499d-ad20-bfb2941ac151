/**
 * API Service
 *
 * This module provides a consolidated API service for making HTTP requests.
 * It uses Axios for all requests and provides consistent error handling and authentication.
 */

import axios, { AxiosRequestConfig, AxiosResponse, AxiosError, AxiosInstance } from 'axios';
import { getAuthHeaders, getCurrentSchema } from './tokenService';
import { API_BASE_URL } from '../config/apiConfig';

// Create a single Axios instance for all API requests
const axiosInstance = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  withCredentials: true,
});

// Request interceptor to add auth headers
axiosInstance.interceptors.request.use(
  (config) => {
    // Get the current schema from unified token service
    const schema = getCurrentSchema() || '';

    // Get authentication headers
    const headers = getAuthHeaders(schema);

    // Add headers to the request
    Object.assign(config.headers, headers);

    console.log('API Request:', {
      url: config.url,
      method: config.method,
      headers: {
        'Content-Type': config.headers['Content-Type'],
        'Authorization': config.headers['Authorization'] ? 'Token [redacted]' : 'None',
        'X-Schema-Name': config.headers['X-Schema-Name'] || 'None',
      },
    });

    return config;
  },
  (error) => {
    console.error('API Request Error:', error);
    return Promise.reject(error);
  }
);

// Response interceptor to handle errors
axiosInstance.interceptors.response.use(
  (response) => {
    console.log('API Response:', {
      status: response.status,
      url: response.config.url,
      method: response.config.method,
    });
    return response;
  },
  async (error: AxiosError) => {
    console.error('API Response Error:', {
      status: error.response?.status,
      url: error.config?.url,
      method: error.config?.method,
      message: error.message,
    });

    // Handle 401 Unauthorized errors
    if (error.response?.status === 401) {
      console.log('401 Unauthorized error, redirecting to login');

      // Store the error message in localStorage
      localStorage.setItem('auth_error', 'Your session has expired. Please log in again.');

      // Redirect to login page
      window.location.href = '/login';
    }

    return Promise.reject(error);
  }
);

// API Service class
class ApiService {
  private axios: AxiosInstance;

  constructor() {
    this.axios = axiosInstance;
  }

  /**
   * Make a GET request
   * @param url The URL to request
   * @param config The Axios request config
   * @returns A promise with the response data
   */
  async get<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.axios.get<T>(url, config);
    return response.data;
  }

  /**
   * Make a POST request
   * @param url The URL to request
   * @param data The data to send
   * @param config The Axios request config
   * @returns A promise with the response data
   */
  async post<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.axios.post<T>(url, data, config);
    return response.data;
  }

  /**
   * Make a PUT request
   * @param url The URL to request
   * @param data The data to send
   * @param config The Axios request config
   * @returns A promise with the response data
   */
  async put<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.axios.put<T>(url, data, config);
    return response.data;
  }

  /**
   * Make a PATCH request
   * @param url The URL to request
   * @param data The data to send
   * @param config The Axios request config
   * @returns A promise with the response data
   */
  async patch<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.axios.patch<T>(url, data, config);
    return response.data;
  }

  /**
   * Make a DELETE request
   * @param url The URL to request
   * @param config The Axios request config
   * @returns A promise with the response data
   */
  async delete<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.axios.delete<T>(url, config);
    return response.data;
  }

  /**
   * Make a request to a tenant-specific endpoint
   * @param tenantSchema The tenant schema name
   * @param endpoint The endpoint to request
   * @param method The HTTP method
   * @param data The data to send (for POST, PUT, PATCH)
   * @param config The Axios request config
   * @returns A promise with the response data
   */
  async tenantRequest<T>(
    tenantSchema: string,
    endpoint: string,
    method: 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE',
    data?: any,
    config?: AxiosRequestConfig
  ): Promise<T> {
    // Normalize the endpoint
    const normalizedEndpoint = endpoint.startsWith('/') ? endpoint.substring(1) : endpoint;

    // Create the tenant-specific URL
    const url = `/api/tenant/${tenantSchema}/${normalizedEndpoint}`;

    // Create a new config with the schema name in the headers
    const requestConfig: AxiosRequestConfig = {
      ...config,
      headers: {
        ...config?.headers,
        'X-Schema-Name': tenantSchema
      }
    };

    // Make the request based on the method
    switch (method) {
      case 'GET':
        return this.get<T>(url, requestConfig);
      case 'POST':
        return this.post<T>(url, data, requestConfig);
      case 'PUT':
        return this.put<T>(url, data, requestConfig);
      case 'PATCH':
        return this.patch<T>(url, data, requestConfig);
      case 'DELETE':
        return this.delete<T>(url, requestConfig);
    }
  }
}

// Create and export a singleton instance
const apiService = new ApiService();
export default apiService;

// Export tenant-specific API helpers for convenience
export const tenantApi = {
  get: <T>(tenantSchema: string, endpoint: string, config?: AxiosRequestConfig): Promise<T> => {
    return apiService.tenantRequest<T>(tenantSchema, endpoint, 'GET', undefined, config);
  },

  post: <T>(tenantSchema: string, endpoint: string, data?: any, config?: AxiosRequestConfig): Promise<T> => {
    return apiService.tenantRequest<T>(tenantSchema, endpoint, 'POST', data, config);
  },

  put: <T>(tenantSchema: string, endpoint: string, data?: any, config?: AxiosRequestConfig): Promise<T> => {
    return apiService.tenantRequest<T>(tenantSchema, endpoint, 'PUT', data, config);
  },

  patch: <T>(tenantSchema: string, endpoint: string, data?: any, config?: AxiosRequestConfig): Promise<T> => {
    return apiService.tenantRequest<T>(tenantSchema, endpoint, 'PATCH', data, config);
  },

  delete: <T>(tenantSchema: string, endpoint: string, config?: AxiosRequestConfig): Promise<T> => {
    return apiService.tenantRequest<T>(tenantSchema, endpoint, 'DELETE', undefined, config);
  }
};
