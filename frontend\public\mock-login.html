<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Mock Login</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
      line-height: 1.6;
    }
    button {
      background-color: #3f51b5;
      color: white;
      border: none;
      padding: 10px 20px;
      border-radius: 4px;
      cursor: pointer;
      font-size: 16px;
      margin: 10px 0;
    }
    button:hover {
      background-color: #303f9f;
    }
    pre {
      background-color: #f5f5f5;
      padding: 15px;
      border-radius: 4px;
      overflow-x: auto;
    }
    .card {
      border: 1px solid #ddd;
      border-radius: 8px;
      padding: 20px;
      margin: 20px 0;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
  </style>
</head>
<body>
  <h1>Mock Login for Testing</h1>
  
  <div class="card">
    <h2>Step 1: Set Mock Login Data</h2>
    <p>Click the button below to set mock login data in localStorage:</p>
    <button id="setMockData">Set Mock Login Data</button>
    <div id="mockDataStatus"></div>
  </div>

  <div class="card">
    <h2>Step 2: Navigate to Citizen Details</h2>
    <p>After setting the mock data, click the button below to navigate to the citizen details page:</p>
    <button id="goToCitizenDetails">Go to Citizen Details</button>
  </div>

  <div class="card">
    <h2>Mock Data</h2>
    <pre id="mockDataDisplay"></pre>
  </div>

  <script>
    // Mock login data
    const mockUser = {
      id: 1,
      email: '<EMAIL>',
      first_name: 'Admin',
      last_name: 'User',
      role: 'admin'
    };

    const mockTenant = {
      schema_name: 'kebele_01',
      name: 'Kebele 01',
      type: 'kebele'
    };

    document.getElementById('setMockData').addEventListener('click', function() {
      // Set mock data in localStorage
      localStorage.setItem('token', 'mock-token-12345');
      localStorage.setItem('user', JSON.stringify(mockUser));
      localStorage.setItem('tenant', JSON.stringify(mockTenant));
      localStorage.setItem('schema_name', 'kebele_01');

      document.getElementById('mockDataStatus').innerHTML = '<p style="color: green">✓ Mock login data set in localStorage</p>';
      
      // Display the mock data
      document.getElementById('mockDataDisplay').textContent = 
        JSON.stringify({ user: mockUser, tenant: mockTenant, token: 'mock-token-12345' }, null, 2);
    });

    document.getElementById('goToCitizenDetails').addEventListener('click', function() {
      window.location.href = '/citizens/1';
    });
  </script>
</body>
</html>
