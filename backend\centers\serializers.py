from rest_framework import serializers
from .models import City, Subcity, Center, CenterType, Citizen, IDCardTemplate, IDCard, Client, Domain
from django.contrib.auth import get_user_model

User = get_user_model()

class CitySerializer(serializers.ModelSerializer):
    """Serializer for the City model."""
    created_by_name = serializers.CharField(source='created_by.get_full_name', read_only=True)
    subcity_count = serializers.SerializerMethodField()
    center_count = serializers.SerializerMethodField()

    class Meta:
        model = City
        fields = '__all__'
        read_only_fields = ('created_at', 'updated_at', 'created_by')

    def get_subcity_count(self, obj):
        return obj.subcities.count()

    def get_center_count(self, obj):
        return sum(subcity.centers.count() for subcity in obj.subcities.all())

class CityListSerializer(serializers.ModelSerializer):
    """Simplified serializer for listing cities."""
    subcity_count = serializers.SerializerMethodField()

    class Meta:
        model = City
        fields = ('id', 'city_name', 'city_code', 'is_active', 'subcity_count')

    def get_subcity_count(self, obj):
        return obj.subcities.count()

class SubcitySerializer(serializers.ModelSerializer):
    """Serializer for the Subcity model."""
    city_name = serializers.CharField(source='city.city_name', read_only=True)
    created_by_name = serializers.CharField(source='created_by.get_full_name', read_only=True)
    center_count = serializers.SerializerMethodField()

    class Meta:
        model = Subcity
        fields = '__all__'
        read_only_fields = ('slug', 'code', 'created_at', 'updated_at', 'created_by')

    def get_center_count(self, obj):
        return obj.centers.count()

class SubcityListSerializer(serializers.ModelSerializer):
    """Simplified serializer for listing subcities."""
    city_name = serializers.CharField(source='city.city_name', read_only=True)
    center_count = serializers.SerializerMethodField()

    class Meta:
        model = Subcity
        fields = ('id', 'name', 'code', 'slug', 'city', 'city_name', 'is_active', 'has_printing_facility', 'center_count')

    def get_center_count(self, obj):
        return obj.centers.count()

class CenterTypeSerializer(serializers.ModelSerializer):
    """Serializer for the CenterType model."""

    class Meta:
        model = CenterType
        fields = '__all__'
        read_only_fields = ('created_at', 'updated_at')

class CenterSerializer(serializers.ModelSerializer):
    """Serializer for the Center model."""
    type_name = serializers.CharField(source='type.name', read_only=True)
    subcity_name = serializers.CharField(source='subcity.name', read_only=True)
    city_name = serializers.CharField(source='subcity.city.name', read_only=True)
    created_by_name = serializers.CharField(source='created_by.get_full_name', read_only=True)
    user_count = serializers.IntegerField(read_only=True)
    citizen_count = serializers.IntegerField(read_only=True)
    id_card_count = serializers.IntegerField(read_only=True)
    is_at_user_limit = serializers.BooleanField(read_only=True)
    is_at_citizen_limit = serializers.BooleanField(read_only=True)
    is_at_id_card_limit = serializers.BooleanField(read_only=True)

    class Meta:
        model = Center
        fields = '__all__'
        read_only_fields = ('slug', 'code', 'created_at', 'updated_at', 'created_by',
                           'user_count', 'citizen_count', 'id_card_count',
                           'is_at_user_limit', 'is_at_citizen_limit', 'is_at_id_card_limit')

class CenterListSerializer(serializers.ModelSerializer):
    """Simplified serializer for listing centers."""
    type_name = serializers.CharField(source='type.name', read_only=True)
    subcity_name = serializers.CharField(source='subcity.name', read_only=True)
    city_name = serializers.CharField(source='subcity.city.name', read_only=True)
    user_count = serializers.IntegerField(read_only=True)
    citizen_count = serializers.IntegerField(read_only=True)

    class Meta:
        model = Center
        fields = ('id', 'name', 'code', 'slug', 'type_name', 'subcity', 'subcity_name', 'city_name',
                 'is_active', 'is_verified', 'subscription_status', 'user_count', 'citizen_count')

class CenterDetailSerializer(serializers.ModelSerializer):
    """Detailed serializer for center information."""
    type_name = serializers.CharField(source='type.name', read_only=True)
    subcity_name = serializers.CharField(source='subcity.name', read_only=True)
    city_name = serializers.CharField(source='subcity.city.name', read_only=True)
    created_by_name = serializers.CharField(source='created_by.get_full_name', read_only=True)
    user_count = serializers.IntegerField(read_only=True)
    citizen_count = serializers.IntegerField(read_only=True)
    id_card_count = serializers.IntegerField(read_only=True)
    is_at_user_limit = serializers.BooleanField(read_only=True)
    is_at_citizen_limit = serializers.BooleanField(read_only=True)
    is_at_id_card_limit = serializers.BooleanField(read_only=True)
    users = serializers.SerializerMethodField()

    class Meta:
        model = Center
        exclude = ('settings',)  # Exclude sensitive settings
        read_only_fields = ('slug', 'code', 'created_at', 'updated_at', 'created_by')

    def get_users(self, obj):
        from accounts.serializers import UserListSerializer
        return UserListSerializer(obj.users.all()[:5], many=True).data

class CenterSettingsSerializer(serializers.ModelSerializer):
    """Serializer for center settings."""

    class Meta:
        model = Center
        fields = ('id', 'name', 'settings')
        read_only_fields = ('id', 'name')

class CenterStatsSerializer(serializers.Serializer):
    """Serializer for center statistics."""
    total_users = serializers.IntegerField()
    active_users = serializers.IntegerField()
    total_citizens = serializers.IntegerField()
    active_citizens = serializers.IntegerField()
    total_id_cards = serializers.IntegerField()
    id_cards_by_status = serializers.DictField()
    citizens_by_gender = serializers.DictField()
    registration_by_month = serializers.DictField()


class ClientSerializer(serializers.ModelSerializer):
    """Serializer for the Client (tenant) model."""
    parent_name = serializers.CharField(source='parent.name', read_only=True)
    parent_id = serializers.PrimaryKeyRelatedField(source='parent', read_only=True)
    parent_schema_name = serializers.CharField(source='parent.schema_name', read_only=True)

    class Meta:
        model = Client
        fields = '__all__'
        read_only_fields = ('schema_name', 'created_at', 'updated_at')


class DomainSerializer(serializers.ModelSerializer):
    """Serializer for the Domain model."""
    tenant_name = serializers.CharField(source='tenant.name', read_only=True)

    class Meta:
        model = Domain
        fields = '__all__'


class CitizenSerializer(serializers.ModelSerializer):
    """Serializer for the Citizen model."""
    full_name = serializers.SerializerMethodField()
    age = serializers.SerializerMethodField()
    id_card_count = serializers.SerializerMethodField()

    class Meta:
        model = Citizen
        fields = '__all__'
        read_only_fields = ('created_at', 'updated_at')
        ref_name = "CentersCitizen"

    def get_full_name(self, obj):
        if obj.middle_name:
            return f"{obj.first_name} {obj.middle_name} {obj.last_name}"
        return f"{obj.first_name} {obj.last_name}"

    def get_age(self, obj):
        import datetime
        today = datetime.date.today()
        born = obj.date_of_birth
        return today.year - born.year - ((today.month, today.day) < (born.month, born.day))

    def get_id_card_count(self, obj):
        return obj.id_cards.count()


class CitizenListSerializer(serializers.ModelSerializer):
    """Simplified serializer for listing citizens."""
    full_name = serializers.SerializerMethodField()
    age = serializers.SerializerMethodField()

    class Meta:
        model = Citizen
        fields = ('id', 'registration_number', 'first_name', 'last_name', 'full_name',
                 'gender', 'date_of_birth', 'age', 'phone_number')
        ref_name = "CentersCitizenList"

    def get_full_name(self, obj):
        if obj.middle_name:
            return f"{obj.first_name} {obj.middle_name} {obj.last_name}"
        return f"{obj.first_name} {obj.last_name}"

    def get_age(self, obj):
        import datetime
        today = datetime.date.today()
        born = obj.date_of_birth
        return today.year - born.year - ((today.month, today.day) < (born.month, born.day))


class IDCardTemplateSerializer(serializers.ModelSerializer):
    """Serializer for the IDCardTemplate model."""

    class Meta:
        model = IDCardTemplate
        fields = '__all__'
        read_only_fields = ('created_at', 'updated_at')


class IDCardSerializer(serializers.ModelSerializer):
    """Serializer for the IDCard model."""
    citizen_name = serializers.SerializerMethodField()
    template_name = serializers.CharField(source='template.name', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    is_expired = serializers.SerializerMethodField()

    class Meta:
        model = IDCard
        fields = '__all__'
        read_only_fields = ('card_number', 'created_at', 'updated_at')

    def get_citizen_name(self, obj):
        return f"{obj.citizen.first_name} {obj.citizen.last_name}"

    def get_is_expired(self, obj):
        import datetime
        return obj.expiry_date < datetime.date.today()


class IDCardListSerializer(serializers.ModelSerializer):
    """Simplified serializer for listing ID cards."""
    citizen_name = serializers.SerializerMethodField()
    status_display = serializers.CharField(source='get_status_display', read_only=True)

    class Meta:
        model = IDCard
        fields = ('id', 'card_number', 'citizen', 'citizen_name', 'issue_date',
                 'expiry_date', 'status', 'status_display')

    def get_citizen_name(self, obj):
        return f"{obj.citizen.first_name} {obj.citizen.last_name}"
