from django.contrib import admin
from .models_family import Child, Parent, EmergencyContact, Spouse
from common.models import RelationshipType

@admin.register(Child)
class ChildAdmin(admin.ModelAdmin):
    list_display = ('first_name', 'last_name', 'citizen', 'date_of_birth', 'is_active', 'created_at')
    list_filter = ('is_active', 'nationality')
    search_fields = ('first_name', 'last_name', 'citizen__first_name', 'citizen__last_name')
    readonly_fields = ('created_at', 'updated_at')

    fieldsets = (
        ('Basic Information', {
            'fields': ('citizen', 'first_name', 'middle_name', 'last_name', 'date_of_birth', 'nationality')
        }),
        ('Amharic Names', {
            'fields': ('first_name_am', 'middle_name_am', 'last_name_am'),
            'classes': ('collapse',)
        }),
        ('Status', {
            'fields': ('is_active',)
        }),
        ('System Information', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def save_model(self, request, obj, form, change):
        if not change:  # Only set created_by when creating a new object
            if hasattr(obj, 'created_by') and hasattr(request, 'user'):
                obj.created_by = request.user
        super().save_model(request, obj, form, change)


@admin.register(Parent)
class ParentAdmin(admin.ModelAdmin):
    list_display = ('first_name', 'last_name', 'relationship_type', 'citizen', 'is_active', 'created_at')
    list_filter = ('is_active', 'nationality', 'relationship_type')
    search_fields = ('first_name', 'last_name', 'citizen__first_name', 'citizen__last_name')
    readonly_fields = ('created_at', 'updated_at')

    fieldsets = (
        ('Basic Information', {
            'fields': ('citizen', 'relationship_type', 'first_name', 'middle_name', 'last_name', 'nationality')
        }),
        ('Amharic Names', {
            'fields': ('first_name_am', 'middle_name_am', 'last_name_am'),
            'classes': ('collapse',)
        }),
        ('Status', {
            'fields': ('is_active',)
        }),
        ('System Information', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def save_model(self, request, obj, form, change):
        if not change:  # Only set created_by when creating a new object
            if hasattr(obj, 'created_by') and hasattr(request, 'user'):
                obj.created_by = request.user
        super().save_model(request, obj, form, change)


# RelationshipType is already registered in common app
# Do not register it again here


@admin.register(EmergencyContact)
class EmergencyContactAdmin(admin.ModelAdmin):
    list_display = ('first_name', 'last_name', 'citizen', 'relationship', 'phone', 'primary_contact', 'is_active', 'created_at')
    list_filter = ('is_active', 'primary_contact', 'relationship', 'nationality')
    search_fields = ('first_name', 'last_name', 'phone', 'email', 'citizen__first_name', 'citizen__last_name')
    readonly_fields = ('created_at', 'updated_at')

    fieldsets = (
        ('Basic Information', {
            'fields': ('citizen', 'first_name', 'middle_name', 'last_name', 'relationship', 'nationality')
        }),
        ('Contact Information', {
            'fields': ('phone', 'email')
        }),
        ('Status', {
            'fields': ('primary_contact', 'is_active')
        }),
        ('System Information', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def save_model(self, request, obj, form, change):
        if not change:  # Only set created_by when creating a new object
            if hasattr(obj, 'created_by') and hasattr(request, 'user'):
                obj.created_by = request.user
        super().save_model(request, obj, form, change)


@admin.register(Spouse)
class SpouseAdmin(admin.ModelAdmin):
    list_display = ('first_name', 'last_name', 'citizen', 'phone', 'primary_contact', 'is_active', 'created_at')
    list_filter = ('is_active', 'primary_contact', 'nationality')
    search_fields = ('first_name', 'last_name', 'phone', 'email', 'citizen__first_name', 'citizen__last_name')
    readonly_fields = ('created_at', 'updated_at')

    fieldsets = (
        ('Basic Information', {
            'fields': ('citizen', 'first_name', 'middle_name', 'last_name', 'nationality')
        }),
        ('Contact Information', {
            'fields': ('phone', 'email')
        }),
        ('Status', {
            'fields': ('primary_contact', 'is_active')
        }),
        ('System Information', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def save_model(self, request, obj, form, change):
        if not change:  # Only set created_by when creating a new object
            if hasattr(obj, 'created_by') and hasattr(request, 'user'):
                obj.created_by = request.user
        super().save_model(request, obj, form, change)
