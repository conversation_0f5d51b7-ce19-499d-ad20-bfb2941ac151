import React, { useEffect, useState } from 'react';
import { Box, Container, Typography, Paper, Grid, Button, CircularProgress } from '@mui/material';
import { useNavigate } from 'react-router-dom';
import PageBanner from '../components/PageBanner';
import DashboardIcon from '@mui/icons-material/Dashboard';
import { useAuth } from '../contexts/AuthContext';

interface User {
  id: number;
  email: string;
  first_name: string;
  last_name: string;
  role: string;
}

interface Tenant {
  schema_name: string;
  name: string;
  type: string;
}

const Dashboard: React.FC = () => {
  const navigate = useNavigate();
  const auth = useAuth(); // Use the auth context
  const [user, setUser] = useState<User | null>(null);
  const [tenant, setTenant] = useState<Tenant | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Check if user is logged in using auth context
    if (!auth.isAuthenticated) {
      // Redirect to login if not logged in
      navigate('/login');
      return;
    }

    try {
      // Get user and tenant from auth context
      if (auth.user) {
        setUser(auth.user);
      } else {
        // Fall back to localStorage
        const storedUser = localStorage.getItem('user');
        if (storedUser) {
          setUser(JSON.parse(storedUser));
        }
      }

      if (auth.tenant) {
        setTenant(auth.tenant);
      } else {
        // Fall back to localStorage
        const storedTenant = localStorage.getItem('tenant');
        if (storedTenant) {
          setTenant(JSON.parse(storedTenant));
        }
      }
    } catch (error) {
      console.error('Error getting user/tenant data:', error);
      // Clear invalid data and redirect to login
      auth.logout();
    } finally {
      setLoading(false);
    }
  }, [navigate, auth]);

  const handleLogout = () => {
    // Use auth context to logout
    auth.logout();
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ py: 4 }}>
      <PageBanner
        title="Dashboard"
        subtitle={`Welcome, ${user?.first_name || 'User'}`}
        icon={<DashboardIcon sx={{ fontSize: 50, color: 'white' }} />}
        actionContent={
          <Box sx={{ textAlign: 'center', width: '100%' }}>
            <Typography variant="body1" sx={{ color: 'white', fontWeight: 500 }}>
              View your account information and tenant details
            </Typography>
          </Box>
        }
      >
        {/* ID Card Overlapping Elements */}
        <Box
          sx={{
            display: { xs: 'none', md: 'block' },
            position: 'absolute',
            right: '-15%',
            top: '50%',
            transform: 'translateY(-50%)',
            width: '250px',
            height: '180px',
            zIndex: 5
          }}
        >
          <Box
            sx={{
              position: 'absolute',
              top: 0,
              right: 0,
              width: '200px',
              height: '120px',
              borderRadius: '10px',
              background: 'rgba(255,255,255,0.9)',
              boxShadow: '0 10px 20px rgba(0,0,0,0.1)',
              transform: 'rotate(5deg)',
              overflow: 'hidden'
            }}
          >
            <Box
              sx={{
                height: '30px',
                background: '#3f51b5',
                display: 'flex',
                alignItems: 'center',
                px: 2
              }}
            >
              <Typography variant="caption" sx={{ color: 'white', fontWeight: 600 }}>
                GONDAR RESIDENT ID
              </Typography>
            </Box>
            <Box sx={{ p: 1, display: 'flex' }}>
              <Box
                sx={{
                  width: '40px',
                  height: '50px',
                  borderRadius: '4px',
                  background: '#e0e0e0',
                  mr: 1
                }}
              />
              <Box>
                <Typography variant="caption" sx={{ fontWeight: 600, fontSize: '8px', display: 'block' }}>
                  John Doe
                </Typography>
                <Typography variant="caption" sx={{ color: 'text.secondary', fontSize: '6px', display: 'block' }}>
                  Kebele: Azezo 03
                </Typography>
                <Typography variant="caption" sx={{ color: 'text.secondary', fontSize: '6px', display: 'block' }}>
                  ID: GDR-12345678
                </Typography>
              </Box>
            </Box>
          </Box>

          <Box
            sx={{
              position: 'absolute',
              bottom: 0,
              left: 0,
              width: '180px',
              height: '100px',
              borderRadius: '10px',
              background: 'rgba(255,255,255,0.9)',
              boxShadow: '0 10px 20px rgba(0,0,0,0.1)',
              transform: 'rotate(-8deg)',
              overflow: 'hidden'
            }}
          >
            <Box
              sx={{
                height: '30px',
                background: '#5c6bc0',
                display: 'flex',
                alignItems: 'center',
                px: 2
              }}
            >
              <Typography variant="caption" sx={{ color: 'white', fontWeight: 600 }}>
                GONDAR CITY ID
              </Typography>
            </Box>
            <Box sx={{ p: 1, display: 'flex' }}>
              <Box
                sx={{
                  width: '40px',
                  height: '50px',
                  borderRadius: '4px',
                  background: '#e0e0e0',
                  mr: 1
                }}
              />
              <Box>
                <Typography variant="caption" sx={{ fontWeight: 600, fontSize: '8px', display: 'block' }}>
                  Jane Smith
                </Typography>
                <Typography variant="caption" sx={{ color: 'text.secondary', fontSize: '6px', display: 'block' }}>
                  Subcity: Maraki
                </Typography>
                <Typography variant="caption" sx={{ color: 'text.secondary', fontSize: '6px', display: 'block' }}>
                  ID: GDR-87654321
                </Typography>
              </Box>
            </Box>
          </Box>
        </Box>
      </PageBanner>

      <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
        <Paper sx={{ p: 4, borderRadius: 2 }}>

        <Box sx={{ display: 'grid', gridTemplateColumns: { xs: '1fr', md: '1fr 1fr' }, gap: 3 }}>
          {/* User Information */}
          <Paper sx={{ p: 3, borderRadius: 2, bgcolor: '#f5f5f5' }}>
            <Typography variant="h6" gutterBottom>
              User Information
            </Typography>
            {user && (
              <Box>
                <Typography><strong>Name:</strong> {user.first_name} {user.last_name}</Typography>
                <Typography><strong>Email:</strong> {user.email}</Typography>
                <Typography><strong>Role:</strong> {user.role}</Typography>
              </Box>
            )}
          </Paper>

          {/* Tenant Information */}
          <Paper sx={{ p: 3, borderRadius: 2, bgcolor: '#f5f5f5' }}>
            <Typography variant="h6" gutterBottom>
              Tenant Information
            </Typography>
            {tenant ? (
              <Box>
                <Typography><strong>Name:</strong> {tenant.name}</Typography>
                <Typography><strong>Schema:</strong> {tenant.schema_name}</Typography>
                <Typography><strong>Type:</strong> {tenant.type}</Typography>
              </Box>
            ) : (
              <Typography>No tenant information available (Public schema)</Typography>
            )}
          </Paper>
        </Box>

        <Box sx={{ mt: 4, display: 'flex', justifyContent: 'flex-end' }}>
          <Button
            variant="contained"
            color="primary"
            onClick={handleLogout}
            sx={{ borderRadius: 2 }}
          >
            Logout
          </Button>
        </Box>
      </Paper>
    </Container>
    </Box>
  );
};

export default Dashboard;
