import React, { useState, useEffect } from 'react';
import {
  Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper,
  Button, Typography, Box, CircularProgress, Chip, Container, Grid, Card, CardContent
} from '@mui/material';
import { useNavigate } from 'react-router-dom';
import PageBanner from './PageBanner';
import CreditCardIcon from '@mui/icons-material/CreditCard';
import HourglassEmptyIcon from '@mui/icons-material/HourglassEmpty';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import VisibilityIcon from '@mui/icons-material/Visibility';
import AddIcon from '@mui/icons-material/Add';

interface IDCard {
  id: number;
  card_number: string;
  issue_date: string;
  expiry_date: string;
  status: string;
  kebele_approval_status?: string;
  kebele_pattern?: any;
  subcity_pattern?: any;
  citizen: {
    first_name: string;
    last_name: string;
    id_number: string;
  };
  source_schema?: string; // Added to track which schema the card came from
}

const IDCardsList: React.FC = () => {
  // Initialize state with default values
  const [idCards, setIdCards] = useState<IDCard[]>([]);
  const [loading, setLoading] = useState(false); // Start with loading false
  const [error, setError] = useState<string | null>(null);
  const navigate = useNavigate();

  // Log component mount
  console.log('IDCardsList: Component mounted');

  // Force loading state to false after component mount
  useEffect(() => {
    const initialLoadingTimeout = setTimeout(() => {
      console.log('IDCardsList: Initial loading timeout reached, forcing loading state to false');
      setLoading(false);
    }, 1000); // 1 second after mount

    return () => {
      clearTimeout(initialLoadingTimeout);
    };
  }, []);

  // Get authentication info from localStorage and API config
  const schemaString = localStorage.getItem('schema_name');
  const schema = schemaString || '';

  // Get JWT token from localStorage
  const token = localStorage.getItem(`jwt_access_token_${schema}`) ||
                localStorage.getItem('jwt_access_token') ||
                localStorage.getItem('token');

  // Function to get status chip color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'APPROVED':
        return 'success';
      case 'PENDING':
        return 'warning';
      case 'PENDING_SUBCITY':
        return 'info';
      case 'PRINTED':
        return 'secondary';
      case 'ISSUED':
        return 'primary';
      case 'EXPIRED':
        return 'error';
      case 'REVOKED':
        return 'error';
      default:
        return 'default';
    }
  };

  // Initialize with default values
  useEffect(() => {
    // Set initial state
    console.log('IDCardsList: Initializing component');

    // Track if component is mounted
    let isMounted = true;

    // Set a timeout to ensure loading state is reset after a maximum time
    const loadingTimeout = setTimeout(() => {
      if (isMounted) {
        console.log('IDCardsList: Loading timeout reached, forcing loading state to false');
        setLoading(false);
      }
    }, 5000); // 5 seconds maximum loading time

    const fetchIDCards = async () => {
      // Set loading state only if component is still mounted
      if (isMounted) {
        console.log('IDCardsList: Setting loading state to true');
        setLoading(true);
        setError(null);
      }

      try {
        // First, check if we're in a subcity schema
        const isSubcity = schema.startsWith('subcity_');
        const isKebele = schema.startsWith('kebele');

        if (isSubcity) {
          // If this is a subcity tenant, fetch ID cards from all child kebele tenants
          console.log(`Fetching ID cards for subcity schema: ${schema}`);

          try {
            // Use the child-kebele-idcards endpoint to get ID cards from all child kebeles
            console.log('Using JWT token for child kebele fetch');

            const response = await fetch(`http://127.0.0.1:8000/api/child-kebele-idcards/`, {
              headers: {
                'Authorization': token ? `Bearer ${token}` : '',
                'Content-Type': 'application/json',
                'X-Schema-Name': schema // Use the current schema (subcity)
              }
            });

            if (!response.ok) {
              throw new Error(`API error: ${response.status} ${response.statusText}`);
            }

            const data = await response.json();
            console.log(`Found ${data.length} ID cards from child kebeles`);

            // The data already includes source_schema for each card
            let allCards = data;

            // Also try to fetch from the subcity schema itself
            try {
              const encodedSchema = encodeURIComponent(schema);
              console.log('Using JWT token for subcity fetch');

              const subcityResponse = await fetch(`http://127.0.0.1:8000/api/tenant/${encodedSchema}/idcards/`, {
                headers: {
                  'Authorization': token ? `Bearer ${token}` : '',
                  'Content-Type': 'application/json',
                  'X-Schema-Name': schema
                }
              });

              if (subcityResponse.ok) {
                const subcityData = await subcityResponse.json();
                let subcityCards = Array.isArray(subcityData) ? subcityData : subcityData.results || [];

                // Add source schema to each card
                subcityCards = subcityCards.map(card => ({
                  ...card,
                  source_schema: schema
                }));

                allCards = [...allCards, ...subcityCards];
              }
            } catch (subcityError) {
              console.error(`Error fetching ID cards from ${schema}:`, subcityError);
            }

            if (isMounted) {
              console.log(`IDCardsList: Setting ${allCards.length} ID cards from subcity fetch`);
              setIdCards(allCards);
            }
          } catch (error) {
            console.error(`Error fetching ID cards from child kebeles:`, error);
            if (isMounted) {
              setError('Failed to fetch ID cards from child kebeles. Please try again later.');
              setIdCards([]);
            }
          }
        } else if (isKebele || schema.includes('kebele')) {
          // If we're in a kebele schema, fetch ID cards from this kebele
          console.log(`Fetching ID cards for kebele schema: ${schema}`);

          try {
            // For kebele schemas, we need to use the correct format for the API endpoint
            // The schema name in localStorage might have a space, but the API endpoint uses underscores
            const schemaForApi = schema.replace(/ /g, '_');
            const encodedSchema = encodeURIComponent(schema);

            console.log(`Using schema for API: ${schemaForApi}`);
            console.log(`Using encoded schema: ${encodedSchema}`);
            console.log(`Using token: ${token}`);

            // Try first with the original schema name
            console.log('Using JWT token for kebele fetch');

            let response = await fetch(`http://127.0.0.1:8000/api/tenant/${encodedSchema}/idcards/`, {
              headers: {
                'Authorization': token ? `Bearer ${token}` : '',
                'Content-Type': 'application/json',
                'X-Schema-Name': schema
              }
            });

            // If that fails, try with the schema name converted to use underscores
            if (!response.ok && schema !== schemaForApi) {
              console.log(`First attempt failed, trying with schema name using underscores: ${schemaForApi}`);
              const encodedSchemaForApi = encodeURIComponent(schemaForApi);

              response = await fetch(`http://127.0.0.1:8000/api/tenant/${encodedSchemaForApi}/idcards/`, {
                headers: {
                  'Authorization': token ? `Bearer ${token}` : '',
                  'Content-Type': 'application/json',
                  'X-Schema-Name': schemaForApi
                }
              });
            }

            // Handle 401 Unauthorized errors gracefully
            if (response.status === 401) {
              console.log(`Authentication failed for kebele ${schema}. This is normal if there are no ID cards yet.`);
              // Return an empty array instead of throwing an error
              setIdCards([]);
              return;
            }

            if (!response.ok) {
              // If both attempts fail, log the error and throw an exception
              console.error(`Failed to fetch ID cards for kebele ${schema}: ${response.status} ${response.statusText}`);
              throw new Error(`Failed to fetch ID cards for kebele ${schema}`);
            }

            const data = await response.json();
            const cards = Array.isArray(data) ? data : data.results || [];

            // Add source schema to each card
            const cardsWithSchema = cards.map(card => ({
              ...card,
              source_schema: schema
            }));

            console.log(`Found ${cardsWithSchema.length} ID cards in kebele ${schema}`);
            if (isMounted) {
              console.log(`IDCardsList: Setting ${cardsWithSchema.length} ID cards from kebele fetch`);
              setIdCards(cardsWithSchema);
            }
          } catch (error) {
            console.error(`Error fetching ID cards from kebele ${schema}:`, error);
            // Don't re-throw the error, just set an empty array
            if (isMounted) {
              console.log('IDCardsList: Setting empty ID cards array after kebele fetch error');
              setIdCards([]);
            }
          }
        } else {
          // Regular fetch for non-subcity schemas
          const encodedSchema = encodeURIComponent(schema);

          console.log('Using JWT token for regular fetch');

          const response = await fetch(`http://127.0.0.1:8000/api/tenant/${encodedSchema}/idcards/`, {
            headers: {
              'Authorization': token ? `Bearer ${token}` : '',
              'Content-Type': 'application/json',
              'X-Schema-Name': schema
            }
          });

          if (!response.ok) {
            // Handle 404 Not Found as an empty array
            if (response.status === 404) {
              console.log('No ID cards found (404). This is normal for a new tenant.');
              if (isMounted) {
                console.log('IDCardsList: Setting empty ID cards array for 404 response');
                setIdCards([]);
              }
              return;
            }

            // Handle 401 Unauthorized as an empty array
            if (response.status === 401) {
              console.log('Authentication error (401). This is normal if there are no ID cards yet.');
              if (isMounted) {
                console.log('IDCardsList: Setting empty ID cards array for 401 response');
                setIdCards([]);
              }
              return;
            }

            throw new Error(`API error: ${response.status} ${response.statusText}`);
          }

          const data = await response.json();
          const cards = Array.isArray(data) ? data : data.results || [];

          // Add source schema to each card
          const cardsWithSchema = cards.map(card => ({
            ...card,
            source_schema: schema
          }));

          if (isMounted) {
            console.log(`IDCardsList: Setting ${cardsWithSchema.length} ID cards from regular fetch`);
            setIdCards(cardsWithSchema);
          }
        }
      } catch (err) {
        console.error('Error fetching ID cards:', err);

        // Only update state if component is still mounted
        if (isMounted) {
          // Check if the error is a 401 Unauthorized error
          if (err instanceof Error && err.message.includes('401')) {
            console.log('Authentication error (401). This is normal if there are no ID cards yet.');
            setIdCards([]);
          } else if (err instanceof Error && err.message.includes('404')) {
            console.log('No ID cards found (404). This is normal for a new tenant.');
            setIdCards([]);
          } else {
            console.log('IDCardsList: Setting error state and empty ID cards array');
            setError('Failed to fetch ID cards. Please try again later.');
            // Still set empty array to prevent loading state
            setIdCards([]);
          }
        }
      } finally {
        // Only update state if component is still mounted
        if (isMounted) {
          console.log('IDCardsList: Setting loading state to false');
          setLoading(false);
        }
      }
    };

    if (schema && token) {
      fetchIDCards();
    } else {
      // If schema or token is missing, set loading to false
      if (isMounted) {
        console.log('IDCardsList: Schema or token missing, setting loading to false');
        setLoading(false);
      }
    }

    // Cleanup function to prevent state updates after unmounting
    return () => {
      console.log('IDCardsList: Component unmounting, cleaning up');
      clearTimeout(loadingTimeout); // Clear the timeout
      isMounted = false;
    }
  }, [schema, token]);

  const handleViewDetails = (id: number, sourceSchema: string) => {
    navigate(`/id-cards/${id}?schema=${encodeURIComponent(sourceSchema)}`);
  };

  // Navigate to ID card registration page
  const handleRegisterIDCard = () => {
    navigate('/id-cards/new');
  };

  // Log the current state for debugging
  console.log('IDCardsList: Current state', { loading, error, idCardsCount: idCards.length, schema, token });

  // Only show loading state if we're actually loading and don't have any cards yet
  if (loading && idCards.length === 0) {
    console.log('IDCardsList: Rendering loading state');
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="200px">
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{ p: 2 }}>
        <Typography color="error">{error}</Typography>
      </Box>
    );
  }

  return (
    <Box sx={{ width: '100%' }}>
      {/* Banner */}
      <PageBanner
        title="ID Cards Management"
        subtitle={`Manage ID cards for citizens in your center`}
        icon={<CreditCardIcon sx={{ fontSize: 50, color: 'white' }} />}
        actionContent={
          <Box sx={{ textAlign: 'center', width: '100%' }}>
            <Typography variant="body1" sx={{ color: 'white', fontWeight: 500 }}>
              Register, view, and manage ID cards for citizens. Track the status of ID cards from draft to issuance.
            </Typography>
          </Box>
        }
      />

      <Container maxWidth="lg" sx={{ mt: 4 }}>
        {/* Dashboard Cards */}
        <Grid container spacing={2} sx={{ mb: 4, display: 'flex', justifyContent: 'space-between', pb: 2 }}>
          <Grid item xs={12} sm={6} md={4}>
            <Card
              sx={{
                height: '100%',
                minHeight: '180px',
                width: '100%',
                display: 'flex',
                flexDirection: 'column',
                transition: 'transform 0.3s, box-shadow 0.3s',
                '&:hover': {
                  transform: 'translateY(-8px)',
                  boxShadow: '0 12px 20px rgba(0, 0, 0, 0.15)',
                },
                borderRadius: 3,
                overflow: 'hidden',
                border: 'none',
                boxShadow: '0 6px 12px rgba(0, 0, 0, 0.08)',
                position: 'relative',
                '&::before': {
                  content: '""',
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  width: '100%',
                  height: '4px',
                  backgroundColor: 'primary.main',
                  zIndex: 1
                }
              }}
            >
              <Box
                sx={{
                  p: 3,
                  display: 'flex',
                  alignItems: 'center',
                  bgcolor: 'white',
                  color: 'text.primary',
                  borderBottom: '1px solid rgba(0, 0, 0, 0.05)'
                }}
              >
                <Box
                  sx={{
                    mr: 2,
                    bgcolor: 'primary.main',
                    color: 'white',
                    p: 1.5,
                    borderRadius: 2,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}
                >
                  <CreditCardIcon />
                </Box>
                <Typography variant="h6" component="div" fontWeight="500">
                  Total ID Cards
                </Typography>
                <Box
                  sx={{
                    ml: 'auto',
                    bgcolor: 'primary.main',
                    color: 'white',
                    borderRadius: '50%',
                    width: 40,
                    height: 40,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    fontWeight: 'bold'
                  }}
                >
                  <Typography variant="body1" fontWeight="bold">
                    {idCards.length}
                  </Typography>
                </Box>
              </Box>
              <CardContent sx={{ flexGrow: 1, p: 3, minHeight: '80px', display: 'flex', alignItems: 'center', justifyContent: 'center', textAlign: 'center' }}>
                <Typography variant="body1" color="text.secondary" sx={{ lineHeight: 1.6 }}>
                  Total ID cards in the system
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={4}>
            <Card
              sx={{
                height: '100%',
                minHeight: '180px',
                width: '100%',
                display: 'flex',
                flexDirection: 'column',
                transition: 'transform 0.3s, box-shadow 0.3s',
                '&:hover': {
                  transform: 'translateY(-8px)',
                  boxShadow: '0 12px 20px rgba(0, 0, 0, 0.15)',
                },
                borderRadius: 3,
                overflow: 'hidden',
                border: 'none',
                boxShadow: '0 6px 12px rgba(0, 0, 0, 0.08)',
                position: 'relative',
                '&::before': {
                  content: '""',
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  width: '100%',
                  height: '4px',
                  backgroundColor: 'warning.main',
                  zIndex: 1
                }
              }}
            >
              <Box
                sx={{
                  p: 3,
                  display: 'flex',
                  alignItems: 'center',
                  bgcolor: 'white',
                  color: 'text.primary',
                  borderBottom: '1px solid rgba(0, 0, 0, 0.05)'
                }}
              >
                <Box
                  sx={{
                    mr: 2,
                    bgcolor: 'warning.main',
                    color: 'white',
                    p: 1.5,
                    borderRadius: 2,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}
                >
                  <HourglassEmptyIcon />
                </Box>
                <Typography variant="h6" component="div" fontWeight="500">
                  Pending
                </Typography>
                <Box
                  sx={{
                    ml: 'auto',
                    bgcolor: 'warning.main',
                    color: 'white',
                    borderRadius: '50%',
                    width: 40,
                    height: 40,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    fontWeight: 'bold'
                  }}
                >
                  <Typography variant="body1" fontWeight="bold">
                    {idCards.filter(card => card.status === 'PENDING' || card.status === 'PENDING_SUBCITY').length}
                  </Typography>
                </Box>
              </Box>
              <CardContent sx={{ flexGrow: 1, p: 3, minHeight: '80px', display: 'flex', alignItems: 'center', justifyContent: 'center', textAlign: 'center' }}>
                <Typography variant="body1" color="text.secondary" sx={{ lineHeight: 1.6 }}>
                  ID cards pending approval
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={4}>
            <Card
              sx={{
                height: '100%',
                minHeight: '180px',
                width: '100%',
                display: 'flex',
                flexDirection: 'column',
                transition: 'transform 0.3s, box-shadow 0.3s',
                '&:hover': {
                  transform: 'translateY(-8px)',
                  boxShadow: '0 12px 20px rgba(0, 0, 0, 0.15)',
                },
                borderRadius: 3,
                overflow: 'hidden',
                border: 'none',
                boxShadow: '0 6px 12px rgba(0, 0, 0, 0.08)',
                position: 'relative',
                '&::before': {
                  content: '""',
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  width: '100%',
                  height: '4px',
                  backgroundColor: 'success.main',
                  zIndex: 1
                }
              }}
            >
              <Box
                sx={{
                  p: 3,
                  display: 'flex',
                  alignItems: 'center',
                  bgcolor: 'white',
                  color: 'text.primary',
                  borderBottom: '1px solid rgba(0, 0, 0, 0.05)'
                }}
              >
                <Box
                  sx={{
                    mr: 2,
                    bgcolor: 'success.main',
                    color: 'white',
                    p: 1.5,
                    borderRadius: 2,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}
                >
                  <CheckCircleIcon />
                </Box>
                <Typography variant="h6" component="div" fontWeight="500">
                  Approved
                </Typography>
                <Box
                  sx={{
                    ml: 'auto',
                    bgcolor: 'success.main',
                    color: 'white',
                    borderRadius: '50%',
                    width: 40,
                    height: 40,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    fontWeight: 'bold'
                  }}
                >
                  <Typography variant="body1" fontWeight="bold">
                    {idCards.filter(card => card.status === 'APPROVED').length}
                  </Typography>
                </Box>
              </Box>
              <CardContent sx={{ flexGrow: 1, p: 3, minHeight: '80px', display: 'flex', alignItems: 'center', justifyContent: 'center', textAlign: 'center' }}>
                <Typography variant="body1" color="text.secondary" sx={{ lineHeight: 1.6 }}>
                  ID cards approved and ready for printing
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* ID Cards Table */}
        <Paper
          sx={{
            width: '100%',
            overflow: 'hidden',
            borderRadius: 2,
            boxShadow: '0 4px 12px rgba(0,0,0,0.1)',
            mb: 4,
            '& .MuiTableRow-root:hover': {
              backgroundColor: 'rgba(63, 81, 181, 0.04)',
              transition: 'background-color 0.2s ease'
            },
            '& .MuiTableCell-root': {
              padding: '16px',
              borderBottom: '1px solid rgba(224, 224, 224, 0.5)'
            },
            '& .MuiTableCell-head': {
              backgroundColor: 'rgba(63, 81, 181, 0.08)',
              position: 'sticky',
              top: 0,
              zIndex: 11
            }
          }}
        >
          <Box sx={{ p: 3, borderBottom: '1px solid rgba(0, 0, 0, 0.08)', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Box>
              <Typography variant="h5" gutterBottom sx={{ fontWeight: 600, color: 'text.primary' }}>
                ID Cards List
              </Typography>
              <Typography variant="body2" color="text.secondary">
                View and manage ID cards in the system
              </Typography>
            </Box>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <Button
                variant="contained"
                color="primary"
                startIcon={<AddIcon />}
                onClick={handleRegisterIDCard}
                sx={{
                  borderRadius: 2,
                  px: 3,
                  py: 1,
                  mr: 2,
                  boxShadow: '0 4px 10px rgba(63, 81, 181, 0.2)',
                  '&:hover': {
                    boxShadow: '0 6px 15px rgba(63, 81, 181, 0.3)'
                  }
                }}
              >
                Register New ID Card
              </Button>
              {loading && (
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <CircularProgress size={20} sx={{ mr: 1 }} />
                  <Typography variant="body2" color="text.secondary">
                    Refreshing...
                  </Typography>
                </Box>
              )}
            </Box>
          </Box>

          <TableContainer sx={{ maxHeight: 440 }}>
            <Table stickyHeader aria-label="sticky table">
              <TableHead sx={{ position: 'sticky', top: 0, zIndex: 10, backgroundColor: '#fff' }}>
                <TableRow sx={{
                  '& th': {
                    fontWeight: 700,
                    bgcolor: 'rgba(63, 81, 181, 0.08)',
                    color: 'rgba(0, 0, 0, 0.7)',
                    fontSize: '0.875rem',
                    textTransform: 'uppercase',
                    letterSpacing: '0.5px',
                    boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
                    borderBottom: '2px solid rgba(63, 81, 181, 0.2)'
                  }
                }}>
                  <TableCell>Card Number</TableCell>
                  <TableCell>Citizen Name</TableCell>
                  <TableCell>ID Number</TableCell>
                  <TableCell>Issue Date</TableCell>
                  <TableCell>Expiry Date</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell>Source</TableCell>
                  <TableCell align="right">Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {idCards.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={8} align="center" sx={{ py: 5 }}>
                      <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
                        <CreditCardIcon sx={{ fontSize: 60, color: 'text.disabled', mb: 2 }} />
                        <Typography variant="h6" color="text.secondary" gutterBottom>
                          No ID cards found
                        </Typography>
                        <Typography variant="body2" color="text.secondary" sx={{ mb: 2, maxWidth: 400, textAlign: 'center' }}>
                          There are no ID cards registered in the system yet.
                        </Typography>
                      </Box>
                    </TableCell>
                  </TableRow>
                ) : (
                  idCards.map((card) => (
                    <TableRow
                      key={`${card.source_schema}-${card.id}`}
                      hover
                      sx={{
                        cursor: 'pointer',
                        transition: 'all 0.2s',
                        '&:hover': {
                          '& .action-buttons': {
                            opacity: 1
                          },
                          transform: 'translateY(-2px)',
                          boxShadow: '0 4px 8px rgba(0,0,0,0.05)'
                        }
                      }}
                      onClick={() => handleViewDetails(card.id, card.source_schema || schema)}
                    >
                      <TableCell>
                        <Typography variant="body2" sx={{ fontWeight: 600, color: 'primary.main' }}>
                          {card.card_number}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body1" sx={{ fontWeight: 600 }}>
                          {`${card.citizen?.first_name} ${card.citizen?.last_name}`}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {card.citizen?.id_number || 'Not assigned'}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {card.issue_date}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {card.expiry_date}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={card.status}
                          color={getStatusColor(card.status) as any}
                          size="small"
                          sx={{ fontWeight: 600, borderRadius: '16px' }}
                        />
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={card.source_schema}
                          size="small"
                          variant="outlined"
                          sx={{ fontWeight: 500, borderRadius: '16px' }}
                        />
                      </TableCell>
                      <TableCell align="right" onClick={(e) => e.stopPropagation()}>
                        <Box
                          className="action-buttons"
                          sx={{
                            opacity: { xs: 1, md: 0 },
                            transition: 'opacity 0.2s',
                            display: 'flex',
                            justifyContent: 'flex-end'
                          }}
                        >
                          <Button
                            variant="contained"
                            size="small"
                            color="primary"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleViewDetails(card.id, card.source_schema || schema);
                            }}
                            startIcon={<VisibilityIcon />}
                            sx={{
                              borderRadius: 2,
                              textTransform: 'none',
                              boxShadow: 'none',
                              '&:hover': {
                                boxShadow: '0 4px 8px rgba(0, 0, 0, 0.1)'
                              }
                            }}
                          >
                            View Details
                          </Button>
                        </Box>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </TableContainer>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', px: 2, py: 1, borderTop: '1px solid rgba(0, 0, 0, 0.08)' }}>
            <Typography variant="body2" color="text.secondary">
              {idCards.length} {idCards.length === 1 ? 'ID card' : 'ID cards'} found
            </Typography>
            {loading && (
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <CircularProgress size={16} sx={{ mr: 1 }} />
                <Typography variant="body2" color="text.secondary">
                  Updating...
                </Typography>
              </Box>
            )}
          </Box>
        </Paper>
      </Container>
    </Box>
  );
};

export default IDCardsList;
