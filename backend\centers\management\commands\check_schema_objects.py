from django.core.management.base import BaseCommand
from django.db import connection
from django_tenants.utils import tenant_context
from centers.models import Client, Subcity, Center

class Command(BaseCommand):
    help = 'Check objects in each tenant schema'

    def handle(self, *args, **options):
        # Get all tenants
        tenants = Client.objects.all()
        
        for tenant in tenants:
            self.stdout.write(f"\nChecking schema: {tenant.schema_name} (ID: {tenant.id}, Name: {tenant.name})")
            
            # Set the tenant context
            with tenant_context(tenant):
                # Check subcities
                self.stdout.write("  Subcities:")
                subcities = Subcity.objects.all()
                if subcities.exists():
                    for subcity in subcities:
                        self.stdout.write(f"    ID: {subcity.id}, Name: {subcity.name}")
                else:
                    self.stdout.write("    No subcities found")
                
                # Check centers (kebeles)
                self.stdout.write("  Centers (Kebeles):")
                centers = Center.objects.all()
                if centers.exists():
                    for center in centers:
                        self.stdout.write(f"    ID: {center.id}, Name: {center.name}")
                else:
                    self.stdout.write("    No centers found")
