import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Button,
  Chip,
  Divider,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  CircularProgress,
  Card,
  CardContent,
  CardHeader,
  IconButton,
  Snackbar,
  Alert,
  Container,
  Tabs,
  Tab,
  TableContainer,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
} from '@mui/material';
import { useNavigate } from 'react-router-dom';
import { useTenant } from '../contexts/TenantContext';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import CancelIcon from '@mui/icons-material/Cancel';
import VisibilityIcon from '@mui/icons-material/Visibility';
import AssignmentTurnedInIcon from '@mui/icons-material/AssignmentTurnedIn';
import AssignmentLateIcon from '@mui/icons-material/AssignmentLate';
import BadgeIcon from '@mui/icons-material/Badge';

// Status color mapping
const statusColors: Record<string, string> = {
  'DRAFT': 'default',
  'PENDING': 'warning',
  'APPROVED': 'success',
  'PRINTED': 'info',
  'ISSUED': 'primary',
  'EXPIRED': 'error',
  'REVOKED': 'error'
};

// Verification status color mapping
const verificationColors: Record<string, string> = {
  'NOT_VERIFIED': 'warning',
  'VERIFIED': 'success',
  'REJECTED': 'error'
};

interface IDCard {
  id: number;
  card_number: string;
  citizen: number;
  citizen_name: string;
  citizen_id_number: string;
  issue_date: string;
  expiry_date: string;
  status: string;
  document_verification_status: string;
}

const DocumentVerification: React.FC = () => {
  const navigate = useNavigate();
  const { tenant, schemaName } = useTenant();
  const [pendingCards, setPendingCards] = useState<IDCard[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedCard, setSelectedCard] = useState<IDCard | null>(null);
  const [verifyDialogOpen, setVerifyDialogOpen] = useState(false);
  const [rejectDialogOpen, setRejectDialogOpen] = useState(false);
  const [notes, setNotes] = useState('');
  const [reason, setReason] = useState('');
  const [submitting, setSubmitting] = useState(false);
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [snackbarSeverity, setSnackbarSeverity] = useState<'success' | 'error'>('success');
  const [tabValue, setTabValue] = useState(0);

  // Fetch pending ID cards
  const fetchPendingCards = async () => {
    setLoading(true);
    setError(null);

    try {
      // Use the tenant-specific API endpoint
      let schema = schemaName || tenant?.schema_name || '';
      const encodedSchema = encodeURIComponent(schema);
      const url = `http://localhost:8000/api/tenant/${encodedSchema}/idcards/pending_verification/`;

      // Get token from localStorage
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('No authentication token found');
      }

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Authorization': `Token ${token}`,
          'Content-Type': 'application/json'
        },
        credentials: 'include'
      });

      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      setPendingCards(data);
    } catch (error: any) {
      console.error('Error fetching pending ID cards:', error);
      setError(error.message || 'Failed to fetch pending ID cards');
    } finally {
      setLoading(false);
    }
  };

  // Verify documents
  const verifyDocuments = async () => {
    if (!selectedCard) return;

    setSubmitting(true);

    try {
      // Use the tenant-specific API endpoint
      let schema = schemaName || tenant?.schema_name || '';
      const encodedSchema = encodeURIComponent(schema);
      const url = `http://localhost:8000/api/tenant/${encodedSchema}/idcards/${selectedCard.id}/verify_documents/`;

      // Get token from localStorage
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('No authentication token found');
      }

      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Authorization': `Token ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ notes }),
        credentials: 'include'
      });

      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}: ${response.statusText}`);
      }

      // Success
      setSnackbarMessage('Documents verified successfully');
      setSnackbarSeverity('success');
      setSnackbarOpen(true);
      setVerifyDialogOpen(false);

      // Refresh the list
      fetchPendingCards();
    } catch (error: any) {
      console.error('Error verifying documents:', error);
      setSnackbarMessage(error.message || 'Failed to verify documents');
      setSnackbarSeverity('error');
      setSnackbarOpen(true);
    } finally {
      setSubmitting(false);
    }
  };

  // Reject documents
  const rejectDocuments = async () => {
    if (!selectedCard) return;

    setSubmitting(true);

    try {
      // Use the tenant-specific API endpoint
      let schema = schemaName || tenant?.schema_name || '';
      const encodedSchema = encodeURIComponent(schema);
      const url = `http://localhost:8000/api/tenant/${encodedSchema}/idcards/${selectedCard.id}/reject_documents/`;

      // Get token from localStorage
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('No authentication token found');
      }

      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Authorization': `Token ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ reason }),
        credentials: 'include'
      });

      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}: ${response.statusText}`);
      }

      // Success
      setSnackbarMessage('Documents rejected successfully');
      setSnackbarSeverity('success');
      setSnackbarOpen(true);
      setRejectDialogOpen(false);

      // Refresh the list
      fetchPendingCards();
    } catch (error: any) {
      console.error('Error rejecting documents:', error);
      setSnackbarMessage(error.message || 'Failed to reject documents');
      setSnackbarSeverity('error');
      setSnackbarOpen(true);
    } finally {
      setSubmitting(false);
    }
  };

  // Handle tab change
  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  // View ID card details
  const handleViewIDCard = (id: number) => {
    navigate(`/id-cards/${id}`);
  };

  // Open verify dialog
  const handleOpenVerifyDialog = (card: IDCard) => {
    setSelectedCard(card);
    setNotes('');
    setVerifyDialogOpen(true);
  };

  // Open reject dialog
  const handleOpenRejectDialog = (card: IDCard) => {
    setSelectedCard(card);
    setReason('');
    setRejectDialogOpen(true);
  };

  // Close dialogs
  const handleCloseDialogs = () => {
    setVerifyDialogOpen(false);
    setRejectDialogOpen(false);
    setSelectedCard(null);
  };

  // Handle snackbar close
  const handleSnackbarClose = () => {
    setSnackbarOpen(false);
  };

  // Load data on component mount
  useEffect(() => {
    fetchPendingCards();
  }, [schemaName, tenant]);

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      <Paper elevation={3} sx={{ p: 3, borderRadius: 2 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
          <AssignmentTurnedInIcon sx={{ fontSize: 32, mr: 2, color: 'primary.main' }} />
          <Typography variant="h5" component="h1" sx={{ fontWeight: 600 }}>
            Document Verification
          </Typography>
        </Box>

        <Tabs
          value={tabValue}
          onChange={handleTabChange}
          indicatorColor="primary"
          textColor="primary"
          sx={{ mb: 3, borderBottom: 1, borderColor: 'divider' }}
        >
          <Tab
            icon={<AssignmentLateIcon />}
            iconPosition="start"
            label="Pending Verification"
            sx={{ fontWeight: 600 }}
          />
        </Tabs>

        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
            <CircularProgress />
          </Box>
        ) : error ? (
          <Alert severity="error" sx={{ my: 2 }}>
            {error}
          </Alert>
        ) : pendingCards.length === 0 ? (
          <Box sx={{ textAlign: 'center', my: 4 }}>
            <Typography variant="h6" color="text.secondary">
              No ID cards pending verification
            </Typography>
          </Box>
        ) : (
          <TableContainer component={Paper} elevation={0} sx={{ mt: 2 }}>
            <Table>
              <TableHead sx={{ bgcolor: 'rgba(0, 0, 0, 0.03)' }}>
                <TableRow>
                  <TableCell sx={{ fontWeight: 700 }}>ID Number</TableCell>
                  <TableCell sx={{ fontWeight: 700 }}>Citizen Name</TableCell>
                  <TableCell sx={{ fontWeight: 700 }}>Issue Date</TableCell>
                  <TableCell sx={{ fontWeight: 700 }}>Status</TableCell>
                  <TableCell sx={{ fontWeight: 700 }}>Verification Status</TableCell>
                  <TableCell sx={{ fontWeight: 700 }}>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {pendingCards.map((card) => (
                  <TableRow
                    key={card.id}
                    hover
                    sx={{ cursor: 'pointer' }}
                  >
                    <TableCell>
                      <Typography variant="body2" sx={{ fontWeight: 600, color: 'primary.main' }}>
                        {card.card_number}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body1" sx={{ fontWeight: 600 }}>
                        {card.citizen_name}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2" sx={{ fontWeight: 500 }}>
                        {new Date(card.issue_date).toLocaleDateString()}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={card.status}
                        color={statusColors[card.status] as any || 'default'}
                        size="small"
                        sx={{ fontWeight: 600 }}
                      />
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={card.document_verification_status}
                        color={verificationColors[card.document_verification_status] as any || 'default'}
                        size="small"
                        sx={{ fontWeight: 600 }}
                      />
                    </TableCell>
                    <TableCell>
                      <Box sx={{ display: 'flex', gap: 1 }}>
                        <IconButton
                          size="small"
                          color="primary"
                          onClick={() => handleViewIDCard(card.id)}
                          title="View ID Card"
                        >
                          <VisibilityIcon />
                        </IconButton>
                        <IconButton
                          size="small"
                          color="success"
                          onClick={() => handleOpenVerifyDialog(card)}
                          title="Verify Documents"
                        >
                          <CheckCircleIcon />
                        </IconButton>
                        <IconButton
                          size="small"
                          color="error"
                          onClick={() => handleOpenRejectDialog(card)}
                          title="Reject Documents"
                        >
                          <CancelIcon />
                        </IconButton>
                      </Box>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        )}
      </Paper>

      {/* Verify Documents Dialog */}
      <Dialog open={verifyDialogOpen} onClose={handleCloseDialogs} maxWidth="sm" fullWidth>
        <DialogTitle sx={{ bgcolor: 'primary.main', color: 'white', fontWeight: 600 }}>
          Verify Documents
        </DialogTitle>
        <DialogContent sx={{ mt: 2, p: 3 }}>
          {selectedCard && (
            <>
              <Box sx={{ mb: 3 }}>
                <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 1 }}>
                  Citizen Information
                </Typography>
                <Typography variant="body1">
                  <strong>Name:</strong> {selectedCard.citizen_name}
                </Typography>
                <Typography variant="body1">
                  <strong>ID Number:</strong> {selectedCard.card_number}
                </Typography>
              </Box>
              <Divider sx={{ my: 2 }} />
              <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 1 }}>
                Verification Notes
              </Typography>
              <TextField
                fullWidth
                multiline
                rows={4}
                variant="outlined"
                placeholder="Enter any notes about the document verification"
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
                sx={{ mb: 2 }}
              />
            </>
          )}
        </DialogContent>
        <DialogActions sx={{ p: 2 }}>
          <Button onClick={handleCloseDialogs} color="inherit" disabled={submitting}>
            Cancel
          </Button>
          <Button
            onClick={verifyDocuments}
            color="primary"
            variant="contained"
            disabled={submitting}
            startIcon={submitting ? <CircularProgress size={20} /> : <CheckCircleIcon />}
          >
            {submitting ? 'Verifying...' : 'Verify Documents'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Reject Documents Dialog */}
      <Dialog open={rejectDialogOpen} onClose={handleCloseDialogs} maxWidth="sm" fullWidth>
        <DialogTitle sx={{ bgcolor: 'error.main', color: 'white', fontWeight: 600 }}>
          Reject Documents
        </DialogTitle>
        <DialogContent sx={{ mt: 2, p: 3 }}>
          {selectedCard && (
            <>
              <Box sx={{ mb: 3 }}>
                <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 1 }}>
                  Citizen Information
                </Typography>
                <Typography variant="body1">
                  <strong>Name:</strong> {selectedCard.citizen_name}
                </Typography>
                <Typography variant="body1">
                  <strong>ID Number:</strong> {selectedCard.card_number}
                </Typography>
              </Box>
              <Divider sx={{ my: 2 }} />
              <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 1 }}>
                Rejection Reason
              </Typography>
              <TextField
                fullWidth
                multiline
                rows={4}
                variant="outlined"
                placeholder="Enter the reason for rejecting the documents"
                value={reason}
                onChange={(e) => setReason(e.target.value)}
                sx={{ mb: 2 }}
                required
                error={!reason}
                helperText={!reason ? 'Rejection reason is required' : ''}
              />
            </>
          )}
        </DialogContent>
        <DialogActions sx={{ p: 2 }}>
          <Button onClick={handleCloseDialogs} color="inherit" disabled={submitting}>
            Cancel
          </Button>
          <Button
            onClick={rejectDocuments}
            color="error"
            variant="contained"
            disabled={submitting || !reason}
            startIcon={submitting ? <CircularProgress size={20} /> : <CancelIcon />}
          >
            {submitting ? 'Rejecting...' : 'Reject Documents'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbarOpen}
        autoHideDuration={6000}
        onClose={handleSnackbarClose}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert onClose={handleSnackbarClose} severity={snackbarSeverity} sx={{ width: '100%' }}>
          {snackbarMessage}
        </Alert>
      </Snackbar>
    </Container>
  );
};

export default DocumentVerification;
