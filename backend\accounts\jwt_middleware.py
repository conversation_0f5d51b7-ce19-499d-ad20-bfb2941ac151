"""
JWT Middleware for Schema Routing

This middleware extracts JWT tokens from requests, validates them,
and sets the appropriate tenant schema for the request.
"""

import logging
from django.http import JsonResponse
from django.db import connection
from centers.models import Client
from .jwt_utils import (
    validate_jwt_token,
    get_schema_from_token,
    extract_token_from_request,
    get_schema_from_request
)

# Configure logging
logger = logging.getLogger(__name__)

class JWTSchemaMiddleware:
    """
    Middleware that extracts JWT tokens from requests, validates them,
    and sets the appropriate tenant schema for the request.
    """

    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        # Skip middleware for certain paths
        if self._should_skip(request):
            return self.get_response(request)

        # Extract token from request
        token = extract_token_from_request(request)
        if not token:
            # No token, continue with default schema
            return self.get_response(request)

        # Try to get schema from request (header, cookie, or token)
        schema_name = get_schema_from_request(request)
        if not schema_name:
            # No schema found, continue with default schema
            return self.get_response(request)

        # Set schema for request
        try:
            self._set_tenant_schema(request, schema_name)
        except Exception as e:
            logger.error(f"Error setting tenant schema '{schema_name}': {str(e)}")
            # Continue with default schema

        # Process request
        response = self.get_response(request)

        # Reset schema to public after request
        try:
            connection.set_schema_to_public()
        except Exception as e:
            logger.error(f"Error resetting schema to public: {str(e)}")

        return response

    def _should_skip(self, request):
        """
        Check if middleware should be skipped for this request.
        """
        # Skip for login, token refresh, and other public endpoints
        skip_paths = [
            '/api/login/',
            '/api/refresh-token/',
            '/api/validate-token/',
            '/api/jwt/login/',
            '/api/jwt/refresh-token/',
            '/api/jwt/validate-token/',
            '/api/jwt/debug/',
            '/admin/',
            '/static/',
            '/media/',
        ]

        path = request.path_info

        # Check if path starts with any of the skip paths
        for skip_path in skip_paths:
            if path.startswith(skip_path):
                logger.debug(f"Skipping JWT middleware for path: {path}")
                return True

        # Skip for OPTIONS requests (CORS preflight)
        if request.method == 'OPTIONS':
            logger.debug(f"Skipping JWT middleware for OPTIONS request: {path}")
            return True

        # Skip for citizens endpoint with detail=true parameter
        if '/citizens/' in path and 'detail=true' in request.META.get('QUERY_STRING', ''):
            logger.debug(f"Skipping JWT middleware for citizens endpoint with detail=true: {path}")
            return True

        # Check if the request has a 'detail' query parameter set to 'true'
        if request.GET.get('detail') == 'true':
            logger.debug(f"Skipping JWT middleware for endpoint with detail=true parameter: {path}")
            return True

        return False

    # Removed _extract_token and _get_schema_from_token methods
    # Now using the utility functions from jwt_utils.py

    def _set_tenant_schema(self, request, schema_name):
        """
        Set tenant schema for request.
        """
        try:
            # Get tenant by schema name
            tenant = Client.objects.get(schema_name=schema_name)

            # Set tenant for request
            request.tenant = tenant

            # Set schema for database connection
            connection.set_tenant(tenant)

            # Store schema name in request for later use
            request.schema_name = schema_name

            logger.debug(f"Set tenant schema to '{schema_name}'")
            return True
        except Client.DoesNotExist:
            logger.warning(f"Tenant with schema '{schema_name}' not found")
            return False
        except Exception as e:
            logger.error(f"Error setting tenant schema '{schema_name}': {str(e)}")
            return False
