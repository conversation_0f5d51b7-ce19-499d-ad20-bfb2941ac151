from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from django.conf import settings
from .jwt_utils import validate_jwt_token
import logging

logger = logging.getLogger(__name__)

class JWTValidationView(APIView):
    """
    API view to validate JWT tokens.
    """
    
    def get(self, request, *args, **kwargs):
        """
        Validate a JWT token from the Authorization header.
        
        Returns:
            Response: JSON response with validation result
        """
        # Get the Authorization header
        auth_header = request.META.get('HTTP_AUTHORIZATION', '')
        
        # Check if the Authorization header is present and in the correct format
        if not auth_header or not auth_header.startswith('Bearer '):
            logger.warning("Invalid or missing Authorization header")
            return Response({
                'valid': False,
                'error': 'Invalid or missing Authorization header'
            }, status=status.HTTP_401_UNAUTHORIZED)
        
        # Extract the token
        token = auth_header.split(' ')[1]
        
        try:
            # Validate the token
            payload = validate_jwt_token(token)
            
            if payload:
                # Token is valid
                return Response({
                    'valid': True,
                    'user_id': payload.get('sub'),
                    'email': payload.get('email'),
                    'exp': payload.get('exp')
                }, status=status.HTTP_200_OK)
            else:
                # Token is invalid
                logger.warning("Invalid JWT token")
                return Response({
                    'valid': False,
                    'error': 'Invalid JWT token'
                }, status=status.HTTP_401_UNAUTHORIZED)
                
        except Exception as e:
            # Error validating token
            logger.error(f"Error validating JWT token: {str(e)}")
            return Response({
                'valid': False,
                'error': str(e)
            }, status=status.HTTP_401_UNAUTHORIZED)
