import React, { useState, useEffect } from 'react';
import {
  Container,
  Paper,
  Typography,
  Box,
  Grid,
  Card,
  CardContent,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormHelperText,
  Divider,
  TableContainer,
  Table,
  TableHead,
  TableBody,
  TableRow,
  TableCell,
  Chip,
  CircularProgress,
  Alert,
  Snackbar
} from '@mui/material';
import { useNavigate } from 'react-router-dom';
import SupervisorAccountIcon from '@mui/icons-material/SupervisorAccount';
import PersonAddIcon from '@mui/icons-material/PersonAdd';
import RefreshIcon from '@mui/icons-material/Refresh';
import { useTenant } from '../contexts/TenantContext';

interface Tenant {
  id: number;
  name: string;
  schema_name: string;
  schema_type: string;
  domains: string[];
}

interface User {
  id: number;
  email: string;
  first_name: string;
  last_name: string;
  role: string;
  is_active: boolean;
}

const SubcityUserManagement: React.FC = () => {
  const navigate = useNavigate();
  const { tenant, schemaName } = useTenant();
  const [childSubcities, setChildSubcities] = useState<Tenant[]>([]);
  const [selectedSubcity, setSelectedSubcity] = useState<Tenant | null>(null);
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    first_name: '',
    last_name: '',
    role: 'SUBCITY_STAFF',
    tenant_schema: '',
  });
  const [formErrors, setFormErrors] = useState({
    email: '',
    password: '',
    first_name: '',
    last_name: '',
  });
  const [submitting, setSubmitting] = useState(false);
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [snackbarSeverity, setSnackbarSeverity] = useState<'success' | 'error'>('success');

  // Fetch child subcities
  const fetchChildSubcities = async () => {
    setLoading(true);
    setError(null);

    try {
      // Get token from localStorage
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('No authentication token found');
      }

      const response = await fetch('http://localhost:8000/api/child-subcities/', {
        method: 'GET',
        headers: {
          'Authorization': `Token ${token}`,
          'Content-Type': 'application/json'
        },
        credentials: 'include'
      });

      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      setChildSubcities(data);

      // Select the first subcity by default if available
      if (data.length > 0 && !selectedSubcity) {
        setSelectedSubcity(data[0]);
        fetchSubcityUsers(data[0].schema_name);
      } else {
        setLoading(false);
      }
    } catch (error: any) {
      console.error('Error fetching child subcities:', error);
      setError(error.message || 'Failed to fetch child subcities');
      setLoading(false);
    }
  };

  // Fetch users for a specific subcity
  const fetchSubcityUsers = async (subcitySchema: string) => {
    setLoading(true);
    setError(null);

    try {
      // Get token from localStorage
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('No authentication token found');
      }

      const response = await fetch(`http://localhost:8000/api/child-subcities/${subcitySchema}/users/`, {
        method: 'GET',
        headers: {
          'Authorization': `Token ${token}`,
          'Content-Type': 'application/json'
        },
        credentials: 'include'
      });

      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      setUsers(data);
    } catch (error: any) {
      console.error('Error fetching subcity users:', error);
      setError(error.message || 'Failed to fetch subcity users');
    } finally {
      setLoading(false);
    }
  };

  // Edit a user in the selected subcity
  const editUser = async () => {
    if (!selectedSubcity || !selectedUser) return;

    // Validate form
    let hasErrors = false;
    const errors = {
      email: '',
      password: '',
      first_name: '',
      last_name: '',
    };

    if (!formData.email) {
      errors.email = 'Email is required';
      hasErrors = true;
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      errors.email = 'Email is invalid';
      hasErrors = true;
    }

    if (formData.password && formData.password.length < 8) {
      errors.password = 'Password must be at least 8 characters';
      hasErrors = true;
    }

    if (!formData.first_name) {
      errors.first_name = 'First name is required';
      hasErrors = true;
    }

    if (!formData.last_name) {
      errors.last_name = 'Last name is required';
      hasErrors = true;
    }

    // Use the selected subcity from the dropdown or the currently selected subcity
    const targetSubcitySchema = formData.tenant_schema || selectedSubcity.schema_name;
    if (!targetSubcitySchema) {
      setSnackbarMessage('Please select a subcity');
      setSnackbarSeverity('error');
      setSnackbarOpen(true);
      return;
    }

    if (hasErrors) {
      setFormErrors(errors);
      return;
    }

    setSubmitting(true);

    try {
      // Get token from localStorage
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('No authentication token found');
      }

      const response = await fetch(`http://localhost:8000/api/child-subcities/${targetSubcitySchema}/users/${selectedUser.id}/`, {
        method: 'PUT',
        headers: {
          'Authorization': `Token ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(formData),
        credentials: 'include'
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `HTTP error ${response.status}`);
      }

      // Success
      const data = await response.json();
      setSnackbarMessage(data.message || 'User updated successfully');
      setSnackbarSeverity('success');
      setSnackbarOpen(true);
      setEditDialogOpen(false);

      // Reset form
      setFormData({
        email: '',
        password: '',
        first_name: '',
        last_name: '',
        role: 'SUBCITY_STAFF',
        tenant_schema: '',
      });
      setSelectedUser(null);

      // Refresh users
      fetchSubcityUsers(selectedSubcity.schema_name);
    } catch (error: any) {
      console.error('Error updating user:', error);
      setSnackbarMessage(error.message || 'Failed to update user');
      setSnackbarSeverity('error');
      setSnackbarOpen(true);
    } finally {
      setSubmitting(false);
    }
  };

  // Delete a user from the selected subcity
  const deleteUser = async () => {
    if (!selectedSubcity || !selectedUser) return;

    setSubmitting(true);

    try {
      // Get token from localStorage
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('No authentication token found');
      }

      const response = await fetch(`http://localhost:8000/api/child-subcities/${selectedSubcity.schema_name}/users/${selectedUser.id}/delete/`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Token ${token}`,
          'Content-Type': 'application/json'
        },
        credentials: 'include'
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `HTTP error ${response.status}`);
      }

      // Success
      const data = await response.json();
      setSnackbarMessage(data.message || 'User deleted successfully');
      setSnackbarSeverity('success');
      setSnackbarOpen(true);
      setDeleteDialogOpen(false);
      setSelectedUser(null);

      // Refresh users
      fetchSubcityUsers(selectedSubcity.schema_name);
    } catch (error: any) {
      console.error('Error deleting user:', error);
      setSnackbarMessage(error.message || 'Failed to delete user');
      setSnackbarSeverity('error');
      setSnackbarOpen(true);
    } finally {
      setSubmitting(false);
    }
  };

  // Create a new user in the selected subcity
  const createUser = async () => {
    if (!selectedSubcity) return;

    // Validate form
    let hasErrors = false;
    const errors = {
      email: '',
      password: '',
      first_name: '',
      last_name: '',
    };

    if (!formData.email) {
      errors.email = 'Email is required';
      hasErrors = true;
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      errors.email = 'Email is invalid';
      hasErrors = true;
    }

    if (!formData.password) {
      errors.password = 'Password is required';
      hasErrors = true;
    } else if (formData.password.length < 8) {
      errors.password = 'Password must be at least 8 characters';
      hasErrors = true;
    }

    if (!formData.first_name) {
      errors.first_name = 'First name is required';
      hasErrors = true;
    }

    if (!formData.last_name) {
      errors.last_name = 'Last name is required';
      hasErrors = true;
    }

    // Use the selected subcity from the dropdown or the currently selected subcity
    const targetSubcitySchema = formData.tenant_schema || selectedSubcity.schema_name;
    if (!targetSubcitySchema) {
      setSnackbarMessage('Please select a subcity');
      setSnackbarSeverity('error');
      setSnackbarOpen(true);
      return;
    }

    if (hasErrors) {
      setFormErrors(errors);
      return;
    }

    setSubmitting(true);

    try {
      // Get token from localStorage
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('No authentication token found');
      }

      const response = await fetch(`http://localhost:8000/api/child-subcities/${targetSubcitySchema}/users/create/`, {
        method: 'POST',
        headers: {
          'Authorization': `Token ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(formData),
        credentials: 'include'
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `HTTP error ${response.status}`);
      }

      // Success
      const data = await response.json();
      setSnackbarMessage(data.message || 'User created successfully');
      setSnackbarSeverity('success');
      setSnackbarOpen(true);
      setCreateDialogOpen(false);

      // Reset form
      setFormData({
        email: '',
        password: '',
        first_name: '',
        last_name: '',
        role: 'SUBCITY_STAFF',
        tenant_schema: '',
      });

      // Refresh users
      fetchSubcityUsers(selectedSubcity.schema_name);
    } catch (error: any) {
      console.error('Error creating user:', error);
      setSnackbarMessage(error.message || 'Failed to create user');
      setSnackbarSeverity('error');
      setSnackbarOpen(true);
    } finally {
      setSubmitting(false);
    }
  };

  // Handle subcity selection
  const handleSubcityChange = (subcity: Tenant) => {
    setSelectedSubcity(subcity);
    fetchSubcityUsers(subcity.schema_name);
  };

  // Handle form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | { name?: string; value: unknown }>) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name as string]: value,
    });

    // Clear error for this field
    if (name && Object.keys(formErrors).includes(name as string)) {
      setFormErrors({
        ...formErrors,
        [name]: '',
      });
    }
  };

  // Open edit dialog for a user
  const handleEditUser = (user: User) => {
    setSelectedUser(user);
    setFormData({
      email: user.email,
      password: '', // Don't set password for editing
      first_name: user.first_name,
      last_name: user.last_name,
      role: user.role,
      tenant_schema: selectedSubcity?.schema_name || '',
    });
    setEditDialogOpen(true);
  };

  // Open delete dialog for a user
  const handleDeleteUser = (user: User) => {
    setSelectedUser(user);
    setDeleteDialogOpen(true);
  };

  // Handle snackbar close
  const handleSnackbarClose = () => {
    setSnackbarOpen(false);
  };

  // Load data on component mount
  useEffect(() => {
    fetchChildSubcities();
  }, [schemaName, tenant]);

  // Get role display name
  const getRoleDisplayName = (role: string) => {
    switch (role) {
      case 'SUBCITY_STAFF':
        return 'Subcity Staff';
      case 'SUBCITY_ADMIN':
        return 'Subcity Admin';
      case 'SUBCITY_MANAGER':
        return 'Subcity Manager';
      default:
        return role;
    }
  };

  // Get role color
  const getRoleColor = (role: string) => {
    switch (role) {
      case 'SUBCITY_STAFF':
        return 'primary';
      case 'SUBCITY_ADMIN':
        return 'success';
      case 'SUBCITY_MANAGER':
        return 'error';
      default:
        return 'default';
    }
  };

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      <Paper elevation={3} sx={{ p: 3, borderRadius: 2 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
          <SupervisorAccountIcon sx={{ fontSize: 32, mr: 2, color: 'primary.main' }} />
          <Typography variant="h5" component="h1" sx={{ fontWeight: 600 }}>
            Subcity User Management
          </Typography>
        </Box>

        <Typography variant="body1" sx={{ mb: 3 }}>
          Manage users for subcity tenants under your city. You can create subcity admins, managers, and staff for each subcity.
        </Typography>

        {/* Subcity Selection */}
        <Box sx={{ mb: 4 }}>
          <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
            Select Subcity
          </Typography>
          <Grid container spacing={2}>
            {childSubcities.map((childSubcity) => (
              <Grid item xs={12} sm={6} md={4} key={childSubcity.id}>
                <Card
                  sx={{
                    cursor: 'pointer',
                    border: selectedSubcity?.id === childSubcity.id ? '2px solid' : '1px solid',
                    borderColor: selectedSubcity?.id === childSubcity.id ? 'primary.main' : 'divider',
                    boxShadow: selectedSubcity?.id === childSubcity.id ? 3 : 1,
                  }}
                  onClick={() => handleSubcityChange(childSubcity)}
                >
                  <CardContent>
                    <Typography variant="h6" sx={{ fontWeight: 600 }}>
                      {childSubcity.name}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {childSubcity.schema_name}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
            ))}
            {childSubcities.length === 0 && !loading && (
              <Grid item xs={12}>
                <Box sx={{ textAlign: 'center', py: 3 }}>
                  <Typography variant="body1" color="text.secondary">
                    No subcity tenants found under your city.
                  </Typography>
                </Box>
              </Grid>
            )}
          </Grid>
        </Box>

        <Divider sx={{ my: 3 }} />

        {/* Users List */}
        <Box sx={{ mb: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Typography variant="h6" sx={{ fontWeight: 600 }}>
            {selectedSubcity ? `Users in ${selectedSubcity.name}` : 'Select a subcity to view users'}
          </Typography>
          <Box>
            <Button
              variant="outlined"
              startIcon={<RefreshIcon />}
              onClick={() => selectedSubcity && fetchSubcityUsers(selectedSubcity.schema_name)}
              sx={{ mr: 1 }}
              disabled={!selectedSubcity || loading}
            >
              Refresh
            </Button>
            <Button
              variant="contained"
              startIcon={<PersonAddIcon />}
              onClick={() => setCreateDialogOpen(true)}
              disabled={!selectedSubcity || loading}
            >
              Add User
            </Button>
          </Box>
        </Box>

        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
            <CircularProgress />
          </Box>
        ) : error ? (
          <Alert severity="error" sx={{ my: 2 }}>
            {error}
          </Alert>
        ) : selectedSubcity ? (
          users.length > 0 ? (
            <TableContainer component={Paper} elevation={0} sx={{ mt: 2 }}>
              <Table>
                <TableHead sx={{ bgcolor: 'rgba(0, 0, 0, 0.03)' }}>
                  <TableRow>
                    <TableCell sx={{ fontWeight: 700 }}>Name</TableCell>
                    <TableCell sx={{ fontWeight: 700 }}>Email</TableCell>
                    <TableCell sx={{ fontWeight: 700 }}>Role</TableCell>
                    <TableCell sx={{ fontWeight: 700 }}>Status</TableCell>
                    <TableCell sx={{ fontWeight: 700 }}>Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {users.map((user) => (
                    <TableRow
                      key={user.id}
                      hover
                    >
                      <TableCell>
                        <Typography variant="body1" sx={{ fontWeight: 600 }}>
                          {user.first_name} {user.last_name}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {user.email}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={getRoleDisplayName(user.role)}
                          color={getRoleColor(user.role) as any}
                          size="small"
                          sx={{ fontWeight: 600 }}
                        />
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={user.is_active ? 'Active' : 'Inactive'}
                          color={user.is_active ? 'success' : 'default'}
                          size="small"
                          sx={{ fontWeight: 600 }}
                        />
                      </TableCell>
                      <TableCell>
                        <Button
                          size="small"
                          onClick={() => handleEditUser(user)}
                          sx={{ mr: 1 }}
                        >
                          Edit
                        </Button>
                        <Button
                          size="small"
                          color="error"
                          onClick={() => handleDeleteUser(user)}
                        >
                          Delete
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          ) : (
            <Box sx={{ textAlign: 'center', my: 4 }}>
              <Typography variant="h6" color="text.secondary">
                No users found in this subcity
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                Click the "Add User" button to create a new user
              </Typography>
            </Box>
          )
        ) : (
          <Box sx={{ textAlign: 'center', my: 4 }}>
            <Typography variant="h6" color="text.secondary">
              Select a subcity to view and manage users
            </Typography>
          </Box>
        )}
      </Paper>

      {/* Create User Dialog */}
      <Dialog open={createDialogOpen} onClose={() => setCreateDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle sx={{ bgcolor: 'primary.main', color: 'white', fontWeight: 600 }}>
          Create New User
        </DialogTitle>
        <Box sx={{ bgcolor: 'info.light', color: 'info.contrastText', p: 2, mb: 2 }}>
          <Typography variant="body2">
            <strong>Important:</strong> This user will be created in the selected subcity schema and will be able to log in with the selected role.
          </Typography>
        </Box>
        <DialogContent sx={{ mt: 2, p: 3 }}>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="First Name"
                name="first_name"
                value={formData.first_name}
                onChange={handleInputChange}
                error={!!formErrors.first_name}
                helperText={formErrors.first_name}
                required
                margin="normal"
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Last Name"
                name="last_name"
                value={formData.last_name}
                onChange={handleInputChange}
                error={!!formErrors.last_name}
                helperText={formErrors.last_name}
                required
                margin="normal"
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Email"
                name="email"
                type="email"
                value={formData.email}
                onChange={handleInputChange}
                error={!!formErrors.email}
                helperText={formErrors.email}
                required
                margin="normal"
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Password"
                name="password"
                type="password"
                value={formData.password}
                onChange={handleInputChange}
                error={!!formErrors.password}
                helperText={formErrors.password}
                required
                margin="normal"
              />
            </Grid>
            <Grid item xs={12}>
              <FormControl fullWidth margin="normal">
                <InputLabel id="tenant-label">Subcity</InputLabel>
                <Select
                  labelId="tenant-label"
                  name="tenant_schema"
                  value={formData.tenant_schema || (selectedSubcity ? selectedSubcity.schema_name : '')}
                  onChange={handleInputChange}
                  label="Subcity"
                >
                  {childSubcities.map((subcity) => (
                    <MenuItem key={subcity.id} value={subcity.schema_name}>
                      {subcity.name} ({subcity.schema_name})
                    </MenuItem>
                  ))}
                </Select>
                <FormHelperText>
                  Select the subcity where this user will be created
                </FormHelperText>
              </FormControl>
            </Grid>
            <Grid item xs={12}>
              <FormControl fullWidth margin="normal">
                <InputLabel id="role-label">Role</InputLabel>
                <Select
                  labelId="role-label"
                  name="role"
                  value={formData.role}
                  onChange={handleInputChange}
                  label="Role"
                >
                  <MenuItem value="SUBCITY_STAFF">Subcity Staff</MenuItem>
                  <MenuItem value="SUBCITY_ADMIN">Subcity Admin</MenuItem>
                  <MenuItem value="SUBCITY_MANAGER">Subcity Manager</MenuItem>
                </Select>
                <FormHelperText>
                  {formData.role === 'SUBCITY_ADMIN' ?
                    `This user will be created as a Subcity Admin` :
                    `This user will be created as a ${formData.role === 'SUBCITY_STAFF' ? 'Subcity Staff' : 'Subcity Manager'}`
                  }
                </FormHelperText>
              </FormControl>
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions sx={{ p: 2 }}>
          <Button
            onClick={() => setCreateDialogOpen(false)}
            color="inherit"
            disabled={submitting}
          >
            Cancel
          </Button>
          <Button
            onClick={createUser}
            variant="contained"
            color="primary"
            disabled={submitting}
            startIcon={submitting ? <CircularProgress size={20} /> : null}
          >
            {submitting ? 'Creating...' : 'Create User'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Edit User Dialog */}
      <Dialog open={editDialogOpen} onClose={() => setEditDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle sx={{ bgcolor: 'primary.main', color: 'white', fontWeight: 600 }}>
          Edit User: {selectedUser?.first_name} {selectedUser?.last_name}
        </DialogTitle>
        <DialogContent sx={{ mt: 2, p: 3 }}>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="First Name"
                name="first_name"
                value={formData.first_name}
                onChange={handleInputChange}
                error={!!formErrors.first_name}
                helperText={formErrors.first_name}
                required
                margin="normal"
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Last Name"
                name="last_name"
                value={formData.last_name}
                onChange={handleInputChange}
                error={!!formErrors.last_name}
                helperText={formErrors.last_name}
                required
                margin="normal"
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Email"
                name="email"
                type="email"
                value={formData.email}
                onChange={handleInputChange}
                error={!!formErrors.email}
                helperText={formErrors.email}
                required
                margin="normal"
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Password (leave blank to keep current)"
                name="password"
                type="password"
                value={formData.password}
                onChange={handleInputChange}
                error={!!formErrors.password}
                helperText={formErrors.password || 'Leave blank to keep the current password'}
                margin="normal"
              />
            </Grid>
            <Grid item xs={12}>
              <FormControl fullWidth margin="normal">
                <InputLabel id="role-label">Role</InputLabel>
                <Select
                  labelId="role-label"
                  name="role"
                  value={formData.role}
                  onChange={handleInputChange}
                  label="Role"
                >
                  <MenuItem value="SUBCITY_STAFF">Subcity Staff</MenuItem>
                  <MenuItem value="SUBCITY_ADMIN">Subcity Admin</MenuItem>
                  <MenuItem value="SUBCITY_MANAGER">Subcity Manager</MenuItem>
                </Select>
                <FormHelperText>
                  {formData.role === 'SUBCITY_ADMIN' ?
                    `This user will be updated as a Subcity Admin` :
                    `This user will be updated as a ${formData.role === 'SUBCITY_STAFF' ? 'Subcity Staff' : 'Subcity Manager'}`
                  }
                </FormHelperText>
              </FormControl>
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions sx={{ p: 2 }}>
          <Button
            onClick={() => setEditDialogOpen(false)}
            color="inherit"
            disabled={submitting}
          >
            Cancel
          </Button>
          <Button
            onClick={editUser}
            variant="contained"
            color="primary"
            disabled={submitting}
            startIcon={submitting ? <CircularProgress size={20} /> : null}
          >
            {submitting ? 'Updating...' : 'Update User'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete User Dialog */}
      <Dialog open={deleteDialogOpen} onClose={() => setDeleteDialogOpen(false)} maxWidth="sm">
        <DialogTitle sx={{ bgcolor: 'error.main', color: 'white', fontWeight: 600 }}>
          Delete User
        </DialogTitle>
        <DialogContent sx={{ mt: 2, p: 3 }}>
          <Typography variant="body1">
            Are you sure you want to delete the user <strong>{selectedUser?.first_name} {selectedUser?.last_name}</strong>?
          </Typography>
          <Typography variant="body2" color="error" sx={{ mt: 2 }}>
            This action cannot be undone. The user will be permanently removed from the system.
          </Typography>
        </DialogContent>
        <DialogActions sx={{ p: 2 }}>
          <Button
            onClick={() => setDeleteDialogOpen(false)}
            color="inherit"
            disabled={submitting}
          >
            Cancel
          </Button>
          <Button
            onClick={deleteUser}
            variant="contained"
            color="error"
            disabled={submitting}
            startIcon={submitting ? <CircularProgress size={20} /> : null}
          >
            {submitting ? 'Deleting...' : 'Delete User'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbarOpen}
        autoHideDuration={6000}
        onClose={handleSnackbarClose}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert
          onClose={handleSnackbarClose}
          severity={snackbarSeverity}
          variant="filled"
          sx={{ width: '100%' }}
        >
          {snackbarMessage}
        </Alert>
      </Snackbar>
    </Container>
  );
};

export default SubcityUserManagement;
