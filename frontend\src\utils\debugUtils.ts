/**
 * Utility functions for debugging API responses and other issues
 */

/**
 * Log detailed information about an API response
 * @param response The fetch API response object
 * @param source A string identifying the source of the response
 */
export const logResponseDetails = async (response: Response, source: string): Promise<void> => {
  console.group(`API Response Details (${source})`);
  console.log('Status:', response.status);
  console.log('Status Text:', response.statusText);
  console.log('Headers:', Object.fromEntries([...response.headers.entries()]));

  try {
    // Clone the response to avoid consuming it
    const clonedResponse = response.clone();
    const text = await clonedResponse.text();

    console.log('Response Text:', text);

    // Try to parse as JSON if possible
    try {
      const json = JSON.parse(text);
      console.log('Response JSON:', json);
    } catch (e) {
      console.log('Response is not valid JSON');
    }
  } catch (error) {
    console.error('Error reading response body:', error);
  }

  console.groupEnd();
};

/**
 * Log detailed information about the current authentication state
 */
export const logAuthDetails = (): void => {
  console.group('Authentication Details');

  // Get token from localStorage
  const token = localStorage.getItem('token');
  console.log('Token exists:', !!token);
  if (token) {
    console.log('Token length:', token.length);
    console.log('Token preview:', `${token.substring(0, 10)}...`);
  }

  // Get tenant information
  const tenantStr = localStorage.getItem('tenant');
  if (tenantStr) {
    try {
      const tenant = JSON.parse(tenantStr);
      console.log('Tenant:', tenant);
      console.log('Tenant type:', tenant.type);
      console.log('Tenant schema_name:', tenant.schema_name);
    } catch (e) {
      console.error('Error parsing tenant:', e);
    }
  } else {
    console.log('No tenant information found');
  }

  // Get schema name
  const schemaName = localStorage.getItem('schema_name');
  console.log('Schema name:', schemaName);

  console.groupEnd();
};

/**
 * Log detailed information about the current browser environment
 */
export const logEnvironmentDetails = (): void => {
  console.group('Environment Details');
  console.log('User Agent:', navigator.userAgent);
  console.log('Window Location:', window.location.href);
  console.log('Base URL:', window.location.origin);
  console.log('API Base URL:', `${window.location.origin}/api`);
  console.groupEnd();
};

/**
 * Log all available information for debugging
 */
export const logDebugInfo = (): void => {
  console.group('Debug Information');
  logAuthDetails();
  logEnvironmentDetails();
  console.groupEnd();
};

/**
 * Generate multiple schema variations to try
 * @param baseSchema The base schema name to generate variations from
 * @returns An array of schema variations to try
 */
export const generateSchemaVariations = (baseSchema: string): string[] => {
  if (!baseSchema) return [];

  const variations = [
    baseSchema,
    baseSchema.replace(/\s+/g, '_'),  // Replace spaces with underscores
    baseSchema.replace(/\s+/g, ''),   // Remove spaces
    baseSchema.toLowerCase(),         // Lowercase
    baseSchema.toLowerCase().replace(/\s+/g, '_'),  // Lowercase with underscores
    baseSchema.toLowerCase().replace(/\s+/g, ''),   // Lowercase without spaces
  ];

  // If the schema is something like "kebele 16", also try variations with just the number
  const match = baseSchema.match(/kebele\s*(\d+)/i);
  if (match && match[1]) {
    const number = match[1];
    variations.push(`kebele ${number}`);
    variations.push(`kebele_${number}`);
    variations.push(`kebele${number}`);
  }

  // Return unique variations
  return [...new Set(variations)];
};
