"""
Script to generate a JWT token for a specific user.
This can be used to create a valid JWT token for testing.
"""
import os
import sys
import django

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'neocamelot.settings')
django.setup()

from django.contrib.auth import get_user_model
from django.db import connection
from centers.models import Client
from django_tenants.utils import tenant_context, get_public_schema_name
from accounts.jwt_utils import generate_jwt_tokens

User = get_user_model()

def generate_token(email, schema_name=None):
    """Generate a JWT token for a user with the given email."""
    print(f"Generating JWT token for user: {email}")

    # First try to find the user in the public schema
    connection.set_schema_to_public()

    try:
        user = User.objects.get(email=email)
        print(f"Found user in public schema: {user.email} (ID: {user.id})")

        # Generate JWT tokens
        schema = schema_name or 'public'
        access_token, refresh_token = generate_jwt_tokens(user, schema)

        print(f"Generated JWT tokens:")
        print(f"Access token: {access_token[:20]}...")
        print(f"Refresh token: {refresh_token[:20]}...")

        print(f"\nUse these tokens in your API calls:")
        print(f"Authorization: Bearer {access_token}")

        return {
            'access_token': access_token,
            'refresh_token': refresh_token
        }

    except User.DoesNotExist:
        print(f"User not found in public schema, checking tenant schemas...")

    # If user not found in public schema and a schema name is provided, check that schema
    if schema_name:
        try:
            tenant = Client.objects.get(schema_name=schema_name)
            print(f"Found tenant: {tenant.name} (Schema: {tenant.schema_name})")

            with tenant_context(tenant):
                try:
                    user = User.objects.get(email=email)
                    print(f"Found user in tenant {tenant.schema_name}: {user.email} (ID: {user.id})")

                    # Generate JWT tokens
                    access_token, refresh_token = generate_jwt_tokens(user, schema_name)

                    print(f"Generated JWT tokens:")
                    print(f"Access token: {access_token[:20]}...")
                    print(f"Refresh token: {refresh_token[:20]}...")

                    print(f"\nUse these tokens in your API calls:")
                    print(f"Authorization: Bearer {access_token}")

                    return {
                        'access_token': access_token,
                        'refresh_token': refresh_token
                    }

                except User.DoesNotExist:
                    print(f"User not found in tenant {tenant.schema_name}")
        except Client.DoesNotExist:
            print(f"Tenant with schema name '{schema_name}' not found")

    # If still not found, check all tenants
    tenants = Client.objects.exclude(schema_name=get_public_schema_name())
    for tenant in tenants:
        print(f"Checking tenant: {tenant.name} (Schema: {tenant.schema_name})")

        with tenant_context(tenant):
            try:
                user = User.objects.get(email=email)
                print(f"Found user in tenant {tenant.schema_name}: {user.email} (ID: {user.id})")

                # Generate JWT tokens
                access_token, refresh_token = generate_jwt_tokens(user, tenant.schema_name)

                print(f"Generated JWT tokens:")
                print(f"Access token: {access_token[:20]}...")
                print(f"Refresh token: {refresh_token[:20]}...")

                print(f"\nUse these tokens in your API calls:")
                print(f"Authorization: Bearer {access_token}")

                return {
                    'access_token': access_token,
                    'refresh_token': refresh_token
                }

            except User.DoesNotExist:
                print(f"User not found in tenant {tenant.schema_name}")

    print(f"User with email {email} not found in any schema")
    return None

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("Usage: python generate_token_for_user.py <email> [schema_name]")
        sys.exit(1)

    email = sys.argv[1]
    schema_name = sys.argv[2] if len(sys.argv) > 2 else None

    tokens = generate_token(email, schema_name)

    if tokens:
        print("\nTokens generated successfully!")
        print(f"Access Token: {tokens['access_token']}")
        print(f"Refresh Token: {tokens['refresh_token']}")
    else:
        print("\nFailed to generate tokens")
        sys.exit(1)
