import React, { useState, useEffect } from 'react';
import { Box, Button, Typography, CircularProgress, Alert, Paper, TextField, Grid } from '@mui/material';
import ColorLensIcon from '@mui/icons-material/ColorLens';
import apiService from '../../services/apiService';

interface UpdateTenantColorsProps {
  onColorsUpdated: (headerColor: string, accentColor: string) => void;
}

const UpdateTenantColors: React.FC<UpdateTenantColorsProps> = ({ onColorsUpdated }) => {
  const [headerColor, setHeaderColor] = useState<string>('#1976d2');
  const [accentColor, setAccentColor] = useState<string>('#1976d2');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  // Load initial colors from tenant data
  useEffect(() => {
    const tenantStr = localStorage.getItem('tenant');
    if (tenantStr) {
      try {
        const tenant = JSON.parse(tenantStr);
        if (tenant.header_color) {
          setHeaderColor(tenant.header_color);
        }
        if (tenant.accent_color) {
          setAccentColor(tenant.accent_color);
        }
      } catch (error) {
        console.error('Error parsing tenant data:', error);
      }
    }
  }, []);

  // Handle form submission
  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();

    setLoading(true);
    setError(null);
    setSuccess(false);

    // Get tenant data from localStorage
    const tenantStr = localStorage.getItem('tenant');
    let tenant = null;
    if (tenantStr) {
      try {
        tenant = JSON.parse(tenantStr);
      } catch (e) {
        console.error('Error parsing tenant data:', e);
      }
    }

    // Create form data
    const formData = new FormData();
    formData.append('header_color', headerColor);
    formData.append('accent_color', accentColor);

    // Get schema_name from localStorage or tenant object
    let schema_name = localStorage.getItem('schema_name');

    // If schema_name is not in localStorage, try to get it from tenant object
    if (!schema_name && tenant && tenant.schema_name) {
      schema_name = tenant.schema_name;
      // Store it in localStorage for future use
      localStorage.setItem('schema_name', schema_name);
    }

    console.log('Using schema_name:', schema_name);

    // Add schema_name to form data if available
    if (schema_name) {
      formData.append('schema_name', schema_name);
    } else {
      setError('Schema name not found. Please log out and log in again.');
      setLoading(false);
      return;
    }

    try {
      // Log debugging information
      console.log('Schema name:', schema_name);
      console.log('Header color:', headerColor);
      console.log('Accent color:', accentColor);

      // Send request to update colors
      const response = await apiService.post(
        '/api/update-tenant-colors/',
        formData
      );

      // Handle success
      setSuccess(true);

      // Call the callback with the new colors
      if (response.data && response.data.header_color && response.data.accent_color) {
        onColorsUpdated(response.data.header_color, response.data.accent_color);

        // Update the tenant object in localStorage
        if (tenant) {
          tenant.header_color = response.data.header_color;
          tenant.accent_color = response.data.accent_color;
          localStorage.setItem('tenant', JSON.stringify(tenant));

          // Update CSS variables
          document.documentElement.style.setProperty('--header-color', response.data.header_color);
          document.documentElement.style.setProperty('--accent-color', response.data.accent_color);
        }
      }

      // Force reload to apply new colors
      setTimeout(() => {
        window.location.reload();
      }, 1500);
    } catch (err) {
      // Handle error with more details
      console.error('Error updating colors:', err);

      // Get more detailed error information
      if (err.response) {
        // The request was made and the server responded with a status code
        // that falls out of the range of 2xx
        console.error('Error response data:', err.response.data);
        console.error('Error response status:', err.response.status);
        console.error('Error response headers:', err.response.headers);

        // Set a more detailed error message
        if (err.response.data && err.response.data.error) {
          setError(`Error: ${err.response.data.error}`);
        } else {
          setError(`Failed to update colors. Server returned status ${err.response.status}.`);
        }
      } else if (err.request) {
        // The request was made but no response was received
        console.error('Error request:', err.request);
        setError('Failed to update colors. No response received from server.');
      } else {
        // Something happened in setting up the request that triggered an Error
        console.error('Error message:', err.message);
        setError(`Failed to update colors: ${err.message}`);
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <Paper elevation={2} sx={{ p: 3, mb: 3 }}>
      <Typography variant="h6" gutterBottom>
        Update Tenant Colors
      </Typography>

      <Box component="form" onSubmit={handleSubmit} sx={{ mt: 2 }}>
        <Grid container spacing={3}>
          {/* Header Color */}
          <Grid item xs={12} md={6}>
            <Box sx={{ mb: 2 }}>
              <Typography variant="subtitle1" gutterBottom>
                Header Color
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <TextField
                  type="color"
                  value={headerColor}
                  onChange={(e) => setHeaderColor(e.target.value)}
                  sx={{ width: 100 }}
                />
                <Typography variant="body2">
                  {headerColor}
                </Typography>
              </Box>
              <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                This color will be used for the header background in the sidebar.
              </Typography>
            </Box>
          </Grid>

          {/* Accent Color */}
          <Grid item xs={12} md={6}>
            <Box sx={{ mb: 2 }}>
              <Typography variant="subtitle1" gutterBottom>
                Accent Color
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <TextField
                  type="color"
                  value={accentColor}
                  onChange={(e) => setAccentColor(e.target.value)}
                  sx={{ width: 100 }}
                />
                <Typography variant="body2">
                  {accentColor}
                </Typography>
              </Box>
              <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                This color will be used for buttons, links, and other accent elements.
              </Typography>
            </Box>
          </Grid>
        </Grid>

        {/* Preview */}
        <Box sx={{ mb: 3, mt: 2 }}>
          <Typography variant="subtitle1" gutterBottom>
            Preview
          </Typography>
          <Box sx={{ display: 'flex', gap: 3 }}>
            <Box>
              <Typography variant="body2" gutterBottom>
                Header Color
              </Typography>
              <Box
                sx={{
                  width: 100,
                  height: 60,
                  bgcolor: headerColor,
                  borderRadius: 1,
                  boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
                }}
              />
            </Box>
            <Box>
              <Typography variant="body2" gutterBottom>
                Accent Color
              </Typography>
              <Box
                sx={{
                  width: 100,
                  height: 60,
                  bgcolor: accentColor,
                  borderRadius: 1,
                  boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
                }}
              />
            </Box>
          </Box>
        </Box>

        {/* Error message */}
        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        {/* Success message */}
        {success && (
          <Alert severity="success" sx={{ mb: 2 }}>
            Colors updated successfully! The page will reload to apply the changes.
          </Alert>
        )}

        {/* Submit button */}
        <Button
          type="submit"
          variant="contained"
          startIcon={<ColorLensIcon />}
          disabled={loading}
          sx={{ mt: 1 }}
        >
          {loading ? (
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <CircularProgress size={20} sx={{ mr: 1, color: 'white' }} />
              Updating...
            </Box>
          ) : (
            'Update Colors'
          )}
        </Button>
      </Box>
    </Paper>
  );
};

export default UpdateTenantColors;
