import uuid
import datetime
from django.utils.text import slugify

def generate_id_card_number(citizen, center, tenant=None, format_string=None):
    """
    Generate a unique ID card number based on the provided format.

    Args:
        citizen: The Citizen object for whom the ID card is being created
        center: The Center object where the ID card is being issued
        tenant: The Client (tenant) object (optional)
        format_string: A custom format string (optional)

    Format placeholders:
        {CENTER_CODE}: The center's code (e.g., KEB-1234)
        {CENTER_SLUG}: The center's slug (e.g., kebele-14)
        {CENTER_PREFIX}: First 3 characters of center's slug, uppercase (e.g., KEB)
        {TENANT_CODE}: The tenant's schema_name, uppercase (e.g., KEBELE_14)
        {TENANT_TYPE}: The tenant's schema_type (e.g., K<PERSON><PERSON>LE)
        {TENANT_PREFIX}: First 3 characters of tenant's schema_type, uppercase (e.g., KEB)
        {CITIZEN_ID}: The citizen's ID
        {CITIZEN_REG}: The citizen's registration number
        {YEAR}: Current year (e.g., 2025)
        {MONTH}: Current month (e.g., 04)
        {DAY}: Current day (e.g., 16)
        {RANDOM}: Random 8-character UUID
        {RANDOM4}: Random 4-character UUID
        {RANDOM6}: Random 6-character UUID
        {RANDOM8}: Random 8-character UUID
        {RANDOM10}: Random 10-character UUID
        {SEQUENCE}: A sequential number (not implemented yet)
        {CITY_FIRST_LETTER}: First letter of the city name (e.g., G for Gondar)
        {SUBCITY_FIRST_TWO_LETTERS}: First two letters of the subcity name (e.g., ZO for Zoble)
        {KEBELE_NUMBER}: The numeric part of the kebele name (e.g., 14 from Kebele 14)

    Returns:
        str: A unique ID card number
    """
    # Default format if none provided
    if not format_string:
        format_string = "ID-{CENTER_PREFIX}-{RANDOM8}"

    # Get current date components
    now = datetime.datetime.now()
    year = now.year
    month = now.strftime('%m')
    day = now.strftime('%d')

    # Generate random components
    random_uuid = str(uuid.uuid4()).upper().replace('-', '')
    random4 = random_uuid[:4]
    random6 = random_uuid[:6]
    random8 = random_uuid[:8]
    random10 = random_uuid[:10]

    # Prepare center components
    center_code = center.code if center and hasattr(center, 'code') else "CTR"
    center_slug = center.slug if center and hasattr(center, 'slug') else slugify(center.name) if center else "center"
    center_prefix = center_slug[:3].upper() if center_slug else "CTR"

    # Prepare tenant components
    tenant_code = tenant.schema_name.upper() if tenant and hasattr(tenant, 'schema_name') else ""
    tenant_type = tenant.schema_type if tenant and hasattr(tenant, 'schema_type') else ""
    tenant_prefix = tenant_type[:3].upper() if tenant_type else "TEN"

    # Prepare citizen components
    citizen_id = str(citizen.id) if citizen else ""
    citizen_reg = citizen.registration_number if citizen and hasattr(citizen, 'registration_number') else ""

    # Prepare city, subcity, and kebele components
    city_first_letter = ""
    subcity_first_two_letters = ""
    kebele_number = ""

    # Get city name from tenant hierarchy
    if tenant and hasattr(tenant, 'parent') and tenant.parent:
        if tenant.schema_type == 'KEBELE':
            # For kebele, parent is subcity and grandparent is city
            subcity_tenant = tenant.parent
            if subcity_tenant and hasattr(subcity_tenant, 'name'):
                subcity_name = subcity_tenant.name
                subcity_first_two_letters = subcity_name[:2].upper() if subcity_name else "XX"

                # Get city from subcity's parent
                if hasattr(subcity_tenant, 'parent') and subcity_tenant.parent:
                    city_tenant = subcity_tenant.parent
                    if city_tenant and hasattr(city_tenant, 'name'):
                        city_name = city_tenant.name
                        city_first_letter = city_name[0].upper() if city_name else "X"

        elif tenant.schema_type == 'SUBCITY':
            # For subcity, parent is city
            subcity_name = tenant.name
            subcity_first_two_letters = subcity_name[:2].upper() if subcity_name else "XX"

            city_tenant = tenant.parent
            if city_tenant and hasattr(city_tenant, 'name'):
                city_name = city_tenant.name
                city_first_letter = city_name[0].upper() if city_name else "X"

    # Extract kebele number from tenant name or schema_name
    if tenant and tenant.schema_type == 'KEBELE':
        # Try to extract numeric part from the tenant name
        tenant_name = tenant.name if hasattr(tenant, 'name') else ""
        import re
        # Find all digits in the tenant name
        digits = re.findall(r'\d+', tenant_name)
        if digits:
            # Use the last group of digits found (most likely the kebele number)
            kebele_number = digits[-1].zfill(2)[:2]  # Ensure it's 2 digits
        else:
            # Fallback to a default
            kebele_number = "00"

    # Format the ID card number
    id_card_number = format_string.format(
        CENTER_CODE=center_code,
        CENTER_SLUG=center_slug,
        CENTER_PREFIX=center_prefix,
        TENANT_CODE=tenant_code,
        TENANT_TYPE=tenant_type,
        TENANT_PREFIX=tenant_prefix,
        CITIZEN_ID=citizen_id,
        CITIZEN_REG=citizen_reg,
        YEAR=year,
        MONTH=month,
        DAY=day,
        RANDOM=random_uuid,
        RANDOM4=random4,
        RANDOM6=random6,
        RANDOM8=random8,
        RANDOM10=random10,
        SEQUENCE="00001",  # Placeholder for future implementation
        CITY_FIRST_LETTER=city_first_letter,
        SUBCITY_FIRST_TWO_LETTERS=subcity_first_two_letters,
        KEBELE_NUMBER=kebele_number
    )

    return id_card_number

def get_id_card_format(tenant=None, center=None):
    """
    Get the ID card number format based on tenant and center settings.

    Args:
        tenant: The Client (tenant) object
        center: The Center object

    Returns:
        str: The format string to use for ID card numbers
    """
    # Check if center has a custom format in settings
    if center and hasattr(center, 'settings') and center.settings:
        center_settings = center.settings
        if isinstance(center_settings, dict) and 'id_card_format' in center_settings:
            return center_settings['id_card_format']

    # Check if tenant has a custom format in settings
    if tenant and hasattr(tenant, 'settings') and tenant.settings:
        tenant_settings = tenant.settings
        if isinstance(tenant_settings, dict) and 'id_card_format' in tenant_settings:
            return tenant_settings['id_card_format']

    # Default format is now the custom format requested
    # {CITY_FIRST_LETTER}{SUBCITY_FIRST_TWO_LETTERS}{KEBELE_NUMBER}{RANDOM6}
    return "{CITY_FIRST_LETTER}{SUBCITY_FIRST_TWO_LETTERS}{KEBELE_NUMBER}{RANDOM6}"
