{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^7.0.2", "@mui/lab": "^7.0.0-beta.11", "@mui/material": "^7.0.2", "@mui/x-date-pickers": "^7.28.3", "axios": "^1.8.4", "date-fns": "^2.30.0", "framer-motion": "^12.7.2", "js-cookie": "^3.0.5", "jwt-decode": "^4.0.0", "notistack": "^3.0.2", "react": "^19.0.0", "react-dom": "^19.0.0", "react-qr-code": "^2.0.15", "react-router-dom": "^7.5.0", "react-webcam": "^7.2.0"}, "devDependencies": {"@eslint/js": "^9.21.0", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.21.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "typescript": "~5.7.2", "typescript-eslint": "^8.24.1", "vite": "^6.2.0"}, "proxy": "http://127.0.0.1:8000"}