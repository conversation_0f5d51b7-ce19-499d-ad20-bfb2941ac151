from django.contrib import admin
from .models_citizen import Religion, CitizenStatus, MaritalStatus, DocumentType, EmploymentType, EmployeeType
from .models_region import Country, Region

# Unregister models from the default admin site
# These models are now registered in the common app
try:
    admin.site.unregister(Religion)
except admin.sites.NotRegistered:
    pass

try:
    admin.site.unregister(CitizenStatus)
except admin.sites.NotRegistered:
    pass

try:
    admin.site.unregister(MaritalStatus)
except admin.sites.NotRegistered:
    pass

try:
    admin.site.unregister(DocumentType)
except admin.sites.NotRegistered:
    pass

try:
    admin.site.unregister(EmploymentType)
except admin.sites.NotRegistered:
    pass

try:
    admin.site.unregister(EmployeeType)
except admin.sites.NotRegistered:
    pass

try:
    admin.site.unregister(Country)
except admin.sites.NotRegistered:
    pass

try:
    admin.site.unregister(Region)
except admin.sites.NotRegistered:
    pass
