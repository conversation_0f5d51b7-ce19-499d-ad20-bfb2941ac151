<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Test Login</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
    }
    .container {
      border: 1px solid #ccc;
      padding: 20px;
      border-radius: 5px;
      margin-bottom: 20px;
    }
    button {
      padding: 10px 15px;
      background-color: #4CAF50;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      margin-top: 10px;
    }
    input, select {
      padding: 8px;
      margin: 5px 0;
      width: 100%;
      box-sizing: border-box;
    }
    pre {
      background-color: #f5f5f5;
      padding: 10px;
      border-radius: 5px;
      overflow-x: auto;
    }
  </style>
</head>
<body>
  <h1>Test Login</h1>
  
  <div class="container">
    <h2>Login Form</h2>
    <div>
      <label for="email">Email:</label>
      <input type="email" id="email" value="<EMAIL>">
    </div>
    <div>
      <label for="password">Password:</label>
      <input type="password" id="password" value="password123">
    </div>
    <div>
      <label for="schema">Schema:</label>
      <input type="text" id="schema" value="kebele16">
    </div>
    <button id="loginButton">Login</button>
  </div>
  
  <div class="container">
    <h2>Login Result</h2>
    <pre id="loginResult">No login attempt yet</pre>
  </div>
  
  <script>
    document.getElementById('loginButton').addEventListener('click', async () => {
      const email = document.getElementById('email').value;
      const password = document.getElementById('password').value;
      const schema = document.getElementById('schema').value;
      const resultElement = document.getElementById('loginResult');
      
      resultElement.textContent = 'Logging in...';
      
      try {
        // Clear any existing schema cookies
        document.cookie = 'schema_name=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
        
        // Set the schema cookie
        if (schema) {
          document.cookie = `schema_name=${encodeURIComponent(schema)}; path=/; SameSite=Lax`;
          console.log(`Set schema_name cookie to: ${schema}`);
        }
        
        // Create request body
        const body = {
          email,
          password
        };
        
        // Add schema if provided
        if (schema) {
          body.schema_name = schema;
        }
        
        // Create headers
        const headers = {
          'Content-Type': 'application/json'
        };
        
        // Add schema to headers if provided
        if (schema) {
          headers['X-Schema-Name'] = schema;
        }
        
        // Log the request details
        console.log('Login request details:');
        console.log('- URL:', '/api/jwt/login/');
        console.log('- Method: POST');
        console.log('- Headers:', headers);
        console.log('- Body:', JSON.stringify(body));
        console.log('- Cookies:', document.cookie);
        
        // Make login request
        const response = await fetch('/api/jwt/login/', {
          method: 'POST',
          headers,
          body: JSON.stringify(body),
          credentials: 'include' // Include cookies
        });
        
        console.log('Login response status:', response.status);
        console.log('Login response status text:', response.statusText);
        
        // Clone the response for logging
        const responseClone = response.clone();
        
        try {
          const data = await response.json();
          resultElement.textContent = JSON.stringify(data, null, 2);
          
          if (response.ok) {
            console.log('Login successful!');
            
            // Store tokens
            if (data.access_token) {
              localStorage.setItem('token', data.access_token);
              localStorage.setItem('jwt_access_token', data.access_token);
            }
            
            if (data.refresh_token) {
              localStorage.setItem('jwt_refresh_token', data.refresh_token);
            }
            
            // Store schema
            if (data.tenant?.schema_name || schema) {
              const schemaName = data.tenant?.schema_name || schema;
              localStorage.setItem('schema_name', schemaName);
              localStorage.setItem('jwt_schema', schemaName);
            }
            
            // Store user and tenant info
            if (data.user) {
              localStorage.setItem('user', JSON.stringify(data.user));
            }
            
            if (data.tenant) {
              localStorage.setItem('tenant', JSON.stringify(data.tenant));
            }
          } else {
            console.error('Login failed:', data);
          }
        } catch (jsonError) {
          // If the response is not valid JSON, try to get the text
          try {
            const errorText = await responseClone.text();
            resultElement.textContent = errorText;
            console.error('Login error text:', errorText);
          } catch (textError) {
            resultElement.textContent = `Error: ${response.status} ${response.statusText}`;
            console.error('Failed to read error response:', textError);
          }
        }
      } catch (error) {
        resultElement.textContent = `Error: ${error.message}`;
        console.error('Error logging in:', error);
      }
    });
  </script>
</body>
</html>
