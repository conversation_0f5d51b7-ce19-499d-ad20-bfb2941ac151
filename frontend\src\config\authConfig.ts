/**
 * Authentication Configuration
 *
 * This module provides authentication configuration for the application.
 * It replaces the hardcoded token with a dynamic token from localStorage or cookies.
 */

// API base URL
// Use direct URL to backend server
export const API_BASE_URL = 'http://localhost:8000/api';

// Helper function to get the current token
export const getCurrentToken = (): string => {
  // Try to get the token from localStorage
  const token = localStorage.getItem('token');
  if (token) {
    return token;
  }

  // Try to get the adminToken from cookies
  const adminToken = document.cookie
    .split('; ')
    .find(row => row.startsWith('adminToken='))
    ?.split('=')[1];

  if (adminToken) {
    return adminToken;
  }

  // Return an empty string if no token is found
  return '';
};

// Helper function to get the current schema name
export const getCurrentSchema = (): string => {
  // Try to get the schema name from localStorage
  const schemaName = localStorage.getItem('schema_name');
  if (schemaName) {
    return schemaName;
  }

  // Try to get the schema name from the tenant object
  try {
    const tenantStr = localStorage.getItem('tenant');
    if (tenantStr) {
      const tenant = JSON.parse(tenantStr);
      if (tenant && tenant.schema_name) {
        return tenant.schema_name;
      }
    }
  } catch (error) {
    console.error('Error parsing tenant from localStorage:', error);
  }

  // Try to get the schema name from cookies
  const schemaCookie = document.cookie
    .split('; ')
    .find(row => row.startsWith('schema_name='))
    ?.split('=')[1];

  if (schemaCookie) {
    try {
      return decodeURIComponent(schemaCookie);
    } catch (error) {
      console.error('Error decoding schema cookie:', error);
    }
  }

  // Return an empty string if no schema name is found
  return '';
};

// Helper function to create API URLs
export const createApiUrl = (endpoint: string): string => {
  // If the endpoint already starts with http, return it as is
  if (endpoint.startsWith('http')) {
    return endpoint;
  }

  // If the endpoint already includes /api/, don't add it again
  if (endpoint.includes('/api/')) {
    return `${API_BASE_URL.replace(/\/api$/, '')}${endpoint}`;
  }

  // Otherwise, append the endpoint to the base URL
  return `${API_BASE_URL}/${endpoint}`;
};

// Helper function to create tenant-specific API URLs
export const createTenantApiUrl = (schema: string, endpoint: string): string => {
  // Normalize the endpoint (remove leading /api/ if present)
  const normalizedEndpoint = endpoint.replace(/^\/api\//, '');

  // Create the tenant-specific URL
  return `${API_BASE_URL}/tenant/${encodeURIComponent(schema)}/${normalizedEndpoint}`;
};

// Helper function to create common API URLs
export const createCommonApiUrl = (endpoint: string): string => {
  // Normalize the endpoint (remove leading /api/ if present)
  const normalizedEndpoint = endpoint.replace(/^\/api\//, '');

  // Create the common API URL
  return `${API_BASE_URL}/common/${normalizedEndpoint}`;
};

// Default headers for API requests
export const getAuthHeaders = () => {
  const token = getCurrentToken();

  return {
    'Authorization': token ? (token.includes('.') ? `Bearer ${token}` : `Bearer ${token}`) : '',
    'Content-Type': 'application/json'
  };
};

// Set the schema name as a cookie
export const setSchemaNameCookie = (schema: string): void => {
  if (schema) {
    document.cookie = `schema_name=${encodeURIComponent(schema)}; path=/; SameSite=Lax`;
  }
};

// Make an authenticated request
export const authFetch = async (url: string, options: RequestInit = {}): Promise<Response> => {
  // Get the token and schema name
  const token = getCurrentToken();
  const schemaName = getCurrentSchema();

  // Create headers
  const headers = new Headers(options.headers || {});

  // Add the token to the headers
  if (token) {
    // Use Bearer prefix for JWT tokens
    const tokenWithPrefix = token.includes('.') ? `Bearer ${token}` : `Bearer ${token}`;
    headers.set('Authorization', tokenWithPrefix);
    console.log('authFetch: Added Authorization header with Bearer token');
  }

  // Add the schema name to the headers
  if (schemaName) {
    headers.set('X-Schema-Name', schemaName);

    // Also set the schema name as a cookie
    setSchemaNameCookie(schemaName);
  }

  // Make the request
  return fetch(url, {
    ...options,
    headers,
    credentials: 'include'
  });
};
