"""
Utilities for accessing data across tenants.

This module provides utilities for accessing data from child tenants
and parent tenants in a controlled and secure way.
"""

from django.db import connection
from django_tenants.utils import tenant_context, get_public_schema_name
from .models import Client
import logging

logger = logging.getLogger(__name__)

def get_child_tenant_data(parent_tenant, child_schema_name, model_class, filter_kwargs=None, select_related=None, prefetch_related=None):
    """
    Get data from a child tenant.
    
    Args:
        parent_tenant: The parent tenant object
        child_schema_name: The schema name of the child tenant
        model_class: The model class to query
        filter_kwargs: Optional filter kwargs for the query
        select_related: Optional fields to select_related
        prefetch_related: Optional fields to prefetch_related
        
    Returns:
        A queryset of objects from the child tenant, or None if the child tenant doesn't exist
        or is not a child of the parent tenant
    """
    if not parent_tenant or not child_schema_name or not model_class:
        logger.error("Missing required parameters for get_child_tenant_data")
        return None
        
    try:
        # Get the child tenant
        child_tenant = Client.objects.get(schema_name=child_schema_name)
        
        # Verify this is a child of the parent tenant
        if child_tenant.parent != parent_tenant:
            logger.warning(f"Tenant {child_schema_name} is not a child of {parent_tenant.schema_name}")
            return None
            
        # Switch to the child tenant context
        with tenant_context(child_tenant):
            # Build the queryset
            queryset = model_class.objects.all()
            
            # Apply filters if provided
            if filter_kwargs:
                queryset = queryset.filter(**filter_kwargs)
                
            # Apply select_related if provided
            if select_related:
                queryset = queryset.select_related(*select_related)
                
            # Apply prefetch_related if provided
            if prefetch_related:
                queryset = queryset.prefetch_related(*prefetch_related)
                
            return queryset
    except Client.DoesNotExist:
        logger.warning(f"Child tenant {child_schema_name} does not exist")
        return None
    except Exception as e:
        logger.error(f"Error getting data from child tenant {child_schema_name}: {str(e)}")
        return None

def get_all_child_tenant_data(parent_tenant, model_class, filter_kwargs=None, select_related=None, prefetch_related=None):
    """
    Get data from all child tenants of a parent tenant.
    
    Args:
        parent_tenant: The parent tenant object
        model_class: The model class to query
        filter_kwargs: Optional filter kwargs for the query
        select_related: Optional fields to select_related
        prefetch_related: Optional fields to prefetch_related
        
    Returns:
        A list of (tenant, queryset) tuples, where tenant is the child tenant object
        and queryset is a queryset of objects from that tenant
    """
    if not parent_tenant or not model_class:
        logger.error("Missing required parameters for get_all_child_tenant_data")
        return []
        
    try:
        # Get all child tenants
        child_tenants = Client.objects.filter(parent=parent_tenant)
        
        # Initialize the result list
        result = []
        
        # Get data from each child tenant
        for child_tenant in child_tenants:
            try:
                # Switch to the child tenant context
                with tenant_context(child_tenant):
                    # Build the queryset
                    queryset = model_class.objects.all()
                    
                    # Apply filters if provided
                    if filter_kwargs:
                        queryset = queryset.filter(**filter_kwargs)
                        
                    # Apply select_related if provided
                    if select_related:
                        queryset = queryset.select_related(*select_related)
                        
                    # Apply prefetch_related if provided
                    if prefetch_related:
                        queryset = queryset.prefetch_related(*prefetch_related)
                        
                    # Add the tenant and queryset to the result
                    result.append((child_tenant, queryset))
            except Exception as e:
                logger.error(f"Error getting data from child tenant {child_tenant.schema_name}: {str(e)}")
                continue
                
        return result
    except Exception as e:
        logger.error(f"Error getting data from child tenants: {str(e)}")
        return []

def get_parent_tenant_data(child_tenant, model_class, filter_kwargs=None, select_related=None, prefetch_related=None):
    """
    Get data from a parent tenant.
    
    Args:
        child_tenant: The child tenant object
        model_class: The model class to query
        filter_kwargs: Optional filter kwargs for the query
        select_related: Optional fields to select_related
        prefetch_related: Optional fields to prefetch_related
        
    Returns:
        A queryset of objects from the parent tenant, or None if the child tenant doesn't have a parent
    """
    if not child_tenant or not model_class:
        logger.error("Missing required parameters for get_parent_tenant_data")
        return None
        
    try:
        # Get the parent tenant
        parent_tenant = child_tenant.parent
        
        if not parent_tenant:
            logger.warning(f"Tenant {child_tenant.schema_name} does not have a parent")
            return None
            
        # Switch to the parent tenant context
        with tenant_context(parent_tenant):
            # Build the queryset
            queryset = model_class.objects.all()
            
            # Apply filters if provided
            if filter_kwargs:
                queryset = queryset.filter(**filter_kwargs)
                
            # Apply select_related if provided
            if select_related:
                queryset = queryset.select_related(*select_related)
                
            # Apply prefetch_related if provided
            if prefetch_related:
                queryset = queryset.prefetch_related(*prefetch_related)
                
            return queryset
    except Exception as e:
        logger.error(f"Error getting data from parent tenant: {str(e)}")
        return None

def with_tenant_context(tenant, func, *args, **kwargs):
    """
    Execute a function within a tenant context.
    
    Args:
        tenant: The tenant object
        func: The function to execute
        *args: Positional arguments for the function
        **kwargs: Keyword arguments for the function
        
    Returns:
        The result of the function
    """
    if not tenant or not func:
        logger.error("Missing required parameters for with_tenant_context")
        return None
        
    try:
        # Switch to the tenant context
        with tenant_context(tenant):
            # Execute the function
            return func(*args, **kwargs)
    except Exception as e:
        logger.error(f"Error executing function in tenant context: {str(e)}")
        return None
