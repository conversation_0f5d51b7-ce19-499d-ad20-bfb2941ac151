"""
Proxy models for tenant-specific models to make them available in the global schema.
"""
from django.db import models
from django.utils.translation import gettext_lazy as _

class GlobalCitizen(models.Model):
    """Proxy model for Citizen to make it available in the global schema."""
    # Basic information
    registration_number = models.CharField(max_length=20, blank=True)
    first_name = models.CharField(max_length=100)
    last_name = models.CharField(max_length=100)
    date_of_birth = models.DateField()
    gender = models.CharField(max_length=1)

    # Contact information
    address = models.TextField()
    phone = models.CharField(max_length=20, blank=True)
    email = models.EmailField(blank=True)

    # ID card information
    photo = models.ImageField(upload_to='citizen_photos/', blank=True, null=True)
    id_number = models.CharField(max_length=50, blank=True)

    # Additional information
    occupation = models.CharField(max_length=100, blank=True)
    nationality = models.Char<PERSON>ield(max_length=100, default='Ethiopian')

    # System fields
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    # Tenant information
    tenant_name = models.CharField(max_length=100, blank=True)
    tenant_schema = models.CharField(max_length=100, blank=True)
    tenant_type = models.CharField(max_length=10, blank=True)  # CITY, SUBCITY, or KEBELE

    class Meta:
        verbose_name = _('Global Citizen')
        verbose_name_plural = _('Global Citizens')
        managed = False  # This model is not managed by Django ORM
        app_label = 'centers'  # This model belongs to the centers app

    def __str__(self):
        return f"{self.first_name} {self.last_name} ({self.registration_number})"


class GlobalIDCardTemplate(models.Model):
    """Proxy model for IDCardTemplate to make it available in the global schema."""
    name = models.CharField(max_length=100)
    background_image = models.ImageField(upload_to='id_templates/', blank=True, null=True)
    is_default = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    # Tenant information
    tenant_name = models.CharField(max_length=100, blank=True)
    tenant_schema = models.CharField(max_length=100, blank=True)
    tenant_type = models.CharField(max_length=10, blank=True)  # CITY, SUBCITY, or KEBELE

    class Meta:
        verbose_name = _('Global ID Card Template')
        verbose_name_plural = _('Global ID Card Templates')
        managed = False  # This model is not managed by Django ORM
        app_label = 'centers'  # This model belongs to the centers app

    def __str__(self):
        return f"{self.tenant_name} - {self.name}"


class GlobalIDCard(models.Model):
    """Proxy model for IDCard to make it available in the global schema."""
    STATUS_CHOICES = (
        ('DRAFT', 'Draft'),
        ('PENDING', 'Pending Approval'),
        ('APPROVED', 'Approved'),
        ('PRINTED', 'Printed'),
        ('ISSUED', 'Issued'),
        ('EXPIRED', 'Expired'),
        ('REVOKED', 'Revoked'),
    )

    card_number = models.CharField(max_length=50, blank=True)
    citizen_name = models.CharField(max_length=200, blank=True)
    issue_date = models.DateField()
    expiry_date = models.DateField(blank=True, null=True)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='DRAFT')

    # System fields
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    # Tenant information
    tenant_name = models.CharField(max_length=100, blank=True)
    tenant_schema = models.CharField(max_length=100, blank=True)
    tenant_type = models.CharField(max_length=10, blank=True)  # CITY, SUBCITY, or KEBELE

    class Meta:
        verbose_name = _('Global ID Card')
        verbose_name_plural = _('Global ID Cards')
        managed = False  # This model is not managed by Django ORM
        app_label = 'centers'  # This model belongs to the centers app

    def __str__(self):
        return f"ID Card: {self.card_number} - {self.citizen_name}"
