from django.contrib import admin
from .models import IDCard, IDCardTemplate
from centers.models import Client
from django_tenants.utils import tenant_context

# Create tenant-specific admin classes
class IDCardTemplateAdmin(admin.ModelAdmin):
    list_display = ('name', 'is_default', 'created_at')
    list_filter = ('is_default',)
    search_fields = ('name',)
    readonly_fields = ('created_at', 'updated_at')

    fieldsets = (
        (None, {
            'fields': ('name', 'center', 'is_default', 'background_image')
        }),
        ('Layout Configuration', {
            'fields': ('front_layout', 'back_layout'),
            'classes': ('collapse',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

class IDCardAdmin(admin.ModelAdmin):
    list_display = ('card_number', 'citizen', 'template', 'issue_date', 'expiry_date', 'status')
    list_filter = ('status', 'issue_date')
    search_fields = ('card_number', 'citizen__first_name', 'citizen__last_name', 'citizen__registration_number')
    readonly_fields = ('card_number', 'created_at', 'updated_at', 'created_by', 'approved_by')

    fieldsets = (
        ('Card Information', {
            'fields': ('citizen', 'template', 'card_number', 'issue_date', 'expiry_date', 'status')
        }),
        ('Card Data', {
            'fields': ('card_data',),
            'classes': ('collapse',)
        }),
        ('System Information', {
            'fields': ('created_at', 'updated_at', 'created_by', 'approved_by'),
            'classes': ('collapse',)
        }),
    )

    def save_model(self, request, obj, form, change):
        if not change:  # Only set created_by when creating a new object
            obj.created_by = request.user
        super().save_model(request, obj, form, change)

# Create global admin classes
class GlobalIDCardTemplateAdmin(admin.ModelAdmin):
    list_display = ('schema_name', 'name', 'is_default', 'created_at')
    list_filter = ('is_default',)
    search_fields = ('name',)
    readonly_fields = ('schema_name', 'created_at', 'updated_at')

    def get_queryset(self, request):
        # Get all tenants
        tenants = Client.objects.exclude(schema_name='public')

        # Initialize an empty list to store templates from all tenants
        all_templates = []

        # Iterate through each tenant and get their templates
        for tenant in tenants:
            try:
                with tenant_context(tenant):
                    # Get templates for this tenant
                    templates = list(IDCardTemplate.objects.all())

                    # Add schema_name attribute to each template
                    for template in templates:
                        template.schema_name = tenant.schema_name

                    # Add templates to the list
                    all_templates.extend(templates)
            except Exception as e:
                print(f"Error accessing tenant {{tenant.schema_name}}: {{str(e)}}")

        # Return a queryset-like object
        return all_templates

    def schema_name(self, obj):
        return obj.schema_name if hasattr(obj, 'schema_name') else 'Unknown'
    schema_name.short_description = 'Tenant'

    def has_add_permission(self, request):
        return False

    def has_change_permission(self, request, obj=None):
        return False

    def has_delete_permission(self, request, obj=None):
        return False

class GlobalIDCardAdmin(admin.ModelAdmin):
    list_display = ('schema_name', 'card_number', 'citizen_name', 'issue_date', 'expiry_date', 'status')
    list_filter = ('status', 'issue_date')
    search_fields = ('card_number',)
    readonly_fields = ('schema_name', 'card_number', 'created_at', 'updated_at', 'created_by', 'approved_by')

    def get_queryset(self, request):
        # Get all tenants
        tenants = Client.objects.exclude(schema_name='public')

        # Initialize an empty list to store ID cards from all tenants
        all_cards = []

        # Iterate through each tenant and get their ID cards
        for tenant in tenants:
            try:
                with tenant_context(tenant):
                    # Get ID cards for this tenant
                    cards = list(IDCard.objects.all())

                    # Add schema_name attribute to each card
                    for card in cards:
                        card.schema_name = tenant.schema_name

                    # Add cards to the list
                    all_cards.extend(cards)
            except Exception as e:
                print(f"Error accessing tenant {{tenant.schema_name}}: {{str(e)}}")

        # Return a queryset-like object
        return all_cards

    def schema_name(self, obj):
        return obj.schema_name if hasattr(obj, 'schema_name') else 'Unknown'
    schema_name.short_description = 'Tenant'

    def citizen_name(self, obj):
        try:
            return f"{{obj.citizen.first_name}} {{obj.citizen.last_name}}" if obj.citizen else 'Unknown'
        except Exception:
            return f"Unknown (ID: {{obj.citizen_id if hasattr(obj, 'citizen_id') else 'N/A'}})"
    citizen_name.short_description = 'Citizen'

    def has_add_permission(self, request):
        return False

    def has_change_permission(self, request, obj=None):
        return False

    def has_delete_permission(self, request, obj=None):
        return False

# Unregister models if they are already registered
try:
    admin.site.unregister(IDCardTemplate)
except admin.sites.NotRegistered:
    pass

try:
    admin.site.unregister(IDCard)
except admin.sites.NotRegistered:
    pass

# Register with the appropriate admin class based on the current schema
from django.db import connection
if connection.schema_name == 'public':
    # Disable global admin classes to avoid 'distinct' method error
    # admin.site.register(IDCardTemplate, GlobalIDCardTemplateAdmin)
    # admin.site.register(IDCard, GlobalIDCardAdmin)
    pass
else:
    admin.site.register(IDCardTemplate, IDCardTemplateAdmin)
    admin.site.register(IDCard, IDCardAdmin)
