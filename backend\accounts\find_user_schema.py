"""
Find User Schema API

This module provides an API endpoint to find the correct schema for a user
based on their email and password.
"""

import logging
from rest_framework import status
from rest_framework.decorators import api_view, permission_classes, authentication_classes
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from django.contrib.auth import authenticate
from django.views.decorators.csrf import csrf_exempt
from django_tenants.utils import tenant_context
from centers.models import Client
from accounts.tenant_user_utils import find_user_in_all_tenants, authenticate_user_in_tenant

# Configure logging
logger = logging.getLogger(__name__)

@api_view(['POST', 'OPTIONS'])
@permission_classes([AllowAny])
@authentication_classes([])
@csrf_exempt
def find_user_schema(request):
    """
    Find the correct schema for a user based on their email and password.

    This endpoint tries to authenticate the user in all available schemas
    and returns the first schema where authentication succeeds.
    """
    # Handle preflight OPTIONS request
    if request.method == 'OPTIONS':
        response = Response()
        origin = request.headers.get('Origin', '')
        allowed_origins = ['http://localhost:5173', 'http://127.0.0.1:5173', 'http://localhost:5174', 'http://127.0.0.1:5174']

        if origin in allowed_origins:
            response['Access-Control-Allow-Origin'] = origin
            response['Access-Control-Allow-Methods'] = 'POST, OPTIONS'
            response['Access-Control-Allow-Headers'] = 'Content-Type, Authorization, X-Requested-With, X-CSRFToken'
            response['Access-Control-Allow-Credentials'] = 'true'
            response['Access-Control-Max-Age'] = '86400'  # 24 hours
        return response

    # Get email and password from request
    email = request.data.get('email')
    password = request.data.get('password')

    # Log the request data for debugging
    logger.info(f"find_user_schema called with email: {email}")
    logger.info(f"Request data: {request.data}")
    logger.info(f"Request headers: {request.headers}")

    if not email or not password:
        logger.error(f"Email or password missing. Email: {email is not None}, Password: {password is not None}")
        response = Response(
            {'error': 'Email and password are required'},
            status=status.HTTP_400_BAD_REQUEST
        )

        # Add CORS headers
        origin = request.headers.get('Origin', '')
        allowed_origins = ['http://localhost:5173', 'http://127.0.0.1:5173', 'http://localhost:5174', 'http://127.0.0.1:5174']
        if origin in allowed_origins:
            response['Access-Control-Allow-Origin'] = origin
            response['Access-Control-Allow-Credentials'] = 'true'

        return response

    # Initialize response variable to avoid UnboundLocalError
    response = None

    # First, try to find the user in all tenants
    logger.info(f"Searching for user {email} in all tenants")
    found_user, found_tenant = find_user_in_all_tenants(email, include_public=True)

    if found_user:
        logger.info(f"User {email} found in tenant: {found_tenant.schema_name if found_tenant else 'public'}")

        # If found in public schema
        if not found_tenant:
            logger.info(f"User {email} found in public schema")
            user_role = getattr(found_user, 'role', 'N/A')
            logger.info(f"User role in public schema: {user_role}")

            # Try to authenticate
            user = authenticate(request, username=email, password=password)
            if user:
                logger.info(f"User {email} authenticated in public schema. Is superuser: {user.is_superuser}")

                if user.is_superuser:
                    # This is a superadmin in the public schema
                    logger.info(f"User {email} is a superadmin in public schema")
                    response = Response({
                        'schema': 'public',
                        'role': user_role,
                        'tenant_type': 'CITY'  # Public schema is treated as CITY type
                    })
                else:
                    logger.info(f"User {email} is not a superadmin in public schema")
                    # Not a superadmin, but still authenticated in public schema
                    response = Response({
                        'schema': 'public',
                        'role': user_role,
                        'tenant_type': 'CITY'  # Public schema is treated as CITY type
                    })

                # Add CORS headers
                origin = request.headers.get('Origin', '')
                allowed_origins = ['http://localhost:5173', 'http://127.0.0.1:5173', 'http://localhost:5174', 'http://127.0.0.1:5174']
                if origin in allowed_origins:
                    response['Access-Control-Allow-Origin'] = origin
                    response['Access-Control-Allow-Credentials'] = 'true'

                return response
            else:
                logger.warning(f"User {email} found in public schema but authentication failed")

        # If found in a tenant schema
        elif found_tenant:
            logger.info(f"User {email} found in tenant {found_tenant.schema_name} (type: {found_tenant.schema_type})")

            # Try to authenticate in this tenant
            user = authenticate_user_in_tenant(request, email, password, found_tenant)

            if user:
                # Found and authenticated the user in this tenant
                logger.info(f"User {email} authenticated successfully in tenant {found_tenant.schema_name}")
                user_role = getattr(user, 'role', 'N/A')
                logger.info(f"User details: id={user.id}, is_active={user.is_active}, role={user_role}")

                # Return the schema name and user role
                response = Response({
                    'schema': found_tenant.schema_name,
                    'role': user_role,
                    'tenant_type': found_tenant.schema_type
                })

                # Add CORS headers
                origin = request.headers.get('Origin', '')
                allowed_origins = ['http://localhost:5173', 'http://127.0.0.1:5173', 'http://localhost:5174', 'http://127.0.0.1:5174']
                if origin in allowed_origins:
                    response['Access-Control-Allow-Origin'] = origin
                    response['Access-Control-Allow-Credentials'] = 'true'

                return response
            else:
                logger.warning(f"User {email} found in tenant {found_tenant.schema_name} but authentication failed")
    else:
        logger.warning(f"User {email} not found in any tenant")

    # If we get here, try the fallback approach - try all tenants
    try:
        # Try all active tenants
        tenants = Client.objects.filter(is_active=True)
        logger.info(f"Fallback: Found {tenants.count()} active tenants to check")

        # Try each tenant until we find the user
        for tenant in tenants:
            try:
                logger.info(f"Fallback: Trying to authenticate {email} in tenant {tenant.schema_name}")
                user = authenticate_user_in_tenant(request, email, password, tenant)

                if user:
                    # Found the user in this tenant
                    logger.info(f"Fallback: User {email} authenticated successfully in tenant {tenant.schema_name}")
                    user_role = getattr(user, 'role', 'N/A')
                    logger.info(f"Fallback: User details: id={user.id}, is_active={user.is_active}, role={user_role}")

                    # Return the schema name and user role
                    response = Response({
                        'schema': tenant.schema_name,
                        'role': user_role,
                        'tenant_type': tenant.schema_type
                    })

                    # Add CORS headers
                    origin = request.headers.get('Origin', '')
                    allowed_origins = ['http://localhost:5173', 'http://127.0.0.1:5173', 'http://localhost:5174', 'http://127.0.0.1:5174']
                    if origin in allowed_origins:
                        response['Access-Control-Allow-Origin'] = origin
                        response['Access-Control-Allow-Credentials'] = 'true'

                    return response
            except Exception as e:
                # Log the error but continue trying other tenants
                logger.error(f"Fallback: Error checking tenant {tenant.schema_name}: {str(e)}")
                continue
    except Exception as e:
        logger.error(f"Error finding user schema: {str(e)}")
        response = Response(
            {'error': f'Error finding user schema: {str(e)}'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

        # Add CORS headers
        origin = request.headers.get('Origin', '')
        allowed_origins = ['http://localhost:5173', 'http://127.0.0.1:5173', 'http://localhost:5174', 'http://127.0.0.1:5174']
        if origin in allowed_origins:
            response['Access-Control-Allow-Origin'] = origin
            response['Access-Control-Allow-Credentials'] = 'true'

        return response

    # If we get here and response is still None, the user was not found in any tenant
    if response is None:
        logger.warning(f"User {email} not found in any tenant after all attempts")
        response = Response(
            {'error': f'User {email} not found in any tenant'},
            status=status.HTTP_404_NOT_FOUND
        )

        # Add CORS headers
        origin = request.headers.get('Origin', '')
        allowed_origins = ['http://localhost:5173', 'http://127.0.0.1:5173', 'http://localhost:5174', 'http://127.0.0.1:5174']
        if origin in allowed_origins:
            response['Access-Control-Allow-Origin'] = origin
            response['Access-Control-Allow-Credentials'] = 'true'

    return response
