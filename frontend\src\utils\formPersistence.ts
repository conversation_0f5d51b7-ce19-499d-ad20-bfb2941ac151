/**
 * Utility functions for persisting form data to local storage
 * to prevent data loss during session expiration
 */

// Save form data to local storage
export const saveFormData = (formKey: string, data: any): void => {
  try {
    // Convert Date objects to ISO strings for storage
    const processedData = JSON.parse(JSON.stringify(data, (key, value) => {
      // Convert Date objects to ISO strings
      if (value instanceof Date) {
        return value.toISOString();
      }
      return value;
    }));
    
    localStorage.setItem(`form_data_${formKey}`, JSON.stringify(processedData));
    localStorage.setItem(`form_data_${formKey}_timestamp`, Date.now().toString());
    console.log(`Form data saved to local storage with key: form_data_${formKey}`);
  } catch (error) {
    console.error('Error saving form data to local storage:', error);
  }
};

// Load form data from local storage
export const loadFormData = (formKey: string): any | null => {
  try {
    const storedData = localStorage.getItem(`form_data_${formKey}`);
    if (!storedData) return null;
    
    // Parse the data and convert ISO date strings back to Date objects
    const parsedData = JSON.parse(storedData, (key, value) => {
      // Check if the value is a date string (ISO format)
      if (typeof value === 'string' && 
          /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}.\d{3}Z$/.test(value)) {
        return new Date(value);
      }
      return value;
    });
    
    console.log(`Form data loaded from local storage with key: form_data_${formKey}`);
    return parsedData;
  } catch (error) {
    console.error('Error loading form data from local storage:', error);
    return null;
  }
};

// Check if form data exists in local storage
export const hasStoredFormData = (formKey: string): boolean => {
  return !!localStorage.getItem(`form_data_${formKey}`);
};

// Get the timestamp of when the form data was saved
export const getFormDataTimestamp = (formKey: string): number | null => {
  const timestamp = localStorage.getItem(`form_data_${formKey}_timestamp`);
  return timestamp ? parseInt(timestamp, 10) : null;
};

// Clear form data from local storage
export const clearFormData = (formKey: string): void => {
  localStorage.removeItem(`form_data_${formKey}`);
  localStorage.removeItem(`form_data_${formKey}_timestamp`);
  console.log(`Form data cleared from local storage with key: form_data_${formKey}`);
};

// Format a timestamp into a human-readable string
export const formatTimestamp = (timestamp: number): string => {
  const date = new Date(timestamp);
  return date.toLocaleString();
};
