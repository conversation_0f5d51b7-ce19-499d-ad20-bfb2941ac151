/**
 * Utility functions for validating schema and token combinations
 */
import { fetchWithAuth } from './tokenRefresh';
import { formatSchemaForHeader, formatSchemaForUrl, setCurrentSchema } from './schemaUtils';

/**
 * List of valid schemas from the database
 * This is a fallback in case we can't determine the schema from other sources
 */
const VALID_SCHEMAS = [
  'city_gondar',
  'subcity_zoble',
  'kebele 14',
  'kebele 15',
  'kebele16'
];

/**
 * Checks if a token is valid in a specific schema
 * @param schema The schema to check
 * @param token The token to validate
 * @returns Promise<boolean> True if valid, false otherwise
 */
export const isTokenValidInSchema = async (schema: string, token: string): Promise<boolean> => {
  try {
    console.log(`Checking if token is valid in schema: ${schema}`);

    // Format the schema for URL
    const urlFormattedSchema = formatSchemaForUrl(schema);

    // Try to fetch citizen count as a simple validation
    const response = await fetch(`/api/tenant/${urlFormattedSchema}/citizens/count/`, {
      method: 'GET',
      headers: {
        'Authorization': `Token ${token}`,
        'X-Schema-Name': formatSchemaForHeader(schema),
        'Accept': 'application/json'
      },
      credentials: 'include'
    });

    // If we get a 200 response, the token is valid in this schema
    if (response.ok) {
      console.log(`Token is valid in schema: ${schema}`);
      return true;
    }

    // If we get a 401 or 403, the token is not valid in this schema
    if (response.status === 401 || response.status === 403) {
      console.log(`Token is not valid in schema: ${schema}`);
      return false;
    }

    // For other status codes, try another endpoint
    const altResponse = await fetch(`/api/tenant/${urlFormattedSchema}/citizens/?limit=1`, {
      method: 'GET',
      headers: {
        'Authorization': `Token ${token}`,
        'X-Schema-Name': formatSchemaForHeader(schema),
        'Accept': 'application/json'
      },
      credentials: 'include'
    });

    // If we get a 200 response, the token is valid in this schema
    if (altResponse.ok) {
      console.log(`Token is valid in schema: ${schema} (alternative endpoint)`);
      return true;
    }

    console.log(`Token validation failed in schema: ${schema}`);
    return false;
  } catch (error) {
    console.error(`Error checking token in schema ${schema}:`, error);
    return false;
  }
};

/**
 * Finds the schema where a token is valid
 * @param token The token to validate
 * @param currentSchema The current schema to try first
 * @returns Promise<string|null> The schema where the token is valid, or null if not found
 */
export const findValidSchema = async (token: string, currentSchema?: string): Promise<string|null> => {
  // First try the current schema if provided
  if (currentSchema) {
    if (await isTokenValidInSchema(currentSchema, token)) {
      return currentSchema;
    }
  }

  // Try to get the schema from localStorage
  const localStorageSchema = localStorage.getItem('schema_name');
  if (localStorageSchema && localStorageSchema !== currentSchema) {
    if (await isTokenValidInSchema(localStorageSchema, token)) {
      return localStorageSchema;
    }
  }

  // Try to get the schema from the tenant object in localStorage
  try {
    const tenantStr = localStorage.getItem('tenant');
    if (tenantStr) {
      const tenant = JSON.parse(tenantStr);
      if (tenant && tenant.schema_name && tenant.schema_name !== currentSchema && tenant.schema_name !== localStorageSchema) {
        if (await isTokenValidInSchema(tenant.schema_name, token)) {
          return tenant.schema_name;
        }
      }
    }
  } catch (error) {
    console.error('Error parsing tenant from localStorage:', error);
  }

  // Try valid schemas as a last resort
  for (const schema of VALID_SCHEMAS) {
    if (schema !== currentSchema && schema !== localStorageSchema) {
      if (await isTokenValidInSchema(schema, token)) {
        return schema;
      }
    }
  }

  // If we get here, we couldn't find a valid schema
  console.error('Could not find a valid schema for the token');
  return null;
};

/**
 * Validates the current token in the current schema
 * If the token is not valid in the current schema, tries to find a valid schema
 * @returns Promise<boolean> True if a valid schema was found, false otherwise
 */
export const validateTokenSchema = async (): Promise<boolean> => {
  // Get the current token
  const token = localStorage.getItem('token');
  if (!token) {
    console.error('No token found in localStorage');
    return false;
  }

  // Get the current schema
  const currentSchema = localStorage.getItem('schema_name');

  // Check if the token is valid in the current schema
  if (currentSchema && await isTokenValidInSchema(currentSchema, token)) {
    console.log(`Token is valid in current schema: ${currentSchema}`);
    return true;
  }

  // If not, try to find a valid schema
  console.log('Token is not valid in current schema, trying to find a valid schema');
  const validSchema = await findValidSchema(token, currentSchema || undefined);

  if (validSchema) {
    console.log(`Found valid schema: ${validSchema}`);

    // Update the schema in localStorage
    setCurrentSchema(validSchema);

    // Also set the schema name as a cookie for backend compatibility
    document.cookie = `schema_name=${encodeURIComponent(validSchema)}; path=/`;

    return true;
  }

  console.error('Could not find a valid schema for the token');
  return false;
};

export default {
  isTokenValidInSchema,
  findValidSchema,
  validateTokenSchema
};
