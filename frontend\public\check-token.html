<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Check Authentication Token</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
      line-height: 1.6;
    }
    .card {
      border: 1px solid #ddd;
      border-radius: 8px;
      padding: 20px;
      margin-bottom: 20px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    h1 {
      color: #333;
    }
    h2 {
      color: #555;
      margin-top: 0;
    }
    pre {
      background-color: #f5f5f5;
      padding: 10px;
      border-radius: 4px;
      overflow-x: auto;
    }
    button {
      background-color: #4CAF50;
      color: white;
      border: none;
      padding: 10px 15px;
      text-align: center;
      text-decoration: none;
      display: inline-block;
      font-size: 16px;
      margin: 4px 2px;
      cursor: pointer;
      border-radius: 4px;
    }
    button:hover {
      background-color: #45a049;
    }
    .error {
      color: #f44336;
    }
    .success {
      color: #4CAF50;
    }
  </style>
</head>
<body>
  <h1>Authentication Token Checker</h1>
  
  <div class="card">
    <h2>Current Authentication Data</h2>
    <div id="tokenInfo"></div>
    <pre id="tokenData"></pre>
    <button id="checkToken">Check Token</button>
    <button id="clearToken">Clear Token</button>
  </div>

  <div class="card">
    <h2>Set Test Token</h2>
    <p>Use this to set a test token for debugging:</p>
    <input type="text" id="testToken" placeholder="Enter test token" style="width: 300px; padding: 8px; margin-right: 10px;">
    <button id="setTestToken">Set Token</button>
    <div id="setTokenResult"></div>
  </div>

  <div class="card">
    <h2>Test API Request</h2>
    <p>Test an authenticated API request to check if your token works:</p>
    <button id="testRequest">Test API Request</button>
    <div id="requestResult" style="margin-top: 10px;"></div>
    <pre id="responseData"></pre>
  </div>

  <script>
    document.getElementById('checkToken').addEventListener('click', function() {
      const token = localStorage.getItem('token');
      const user = localStorage.getItem('user');
      const tenant = localStorage.getItem('tenant');
      const schemaName = localStorage.getItem('schema_name');
      
      const tokenInfo = document.getElementById('tokenInfo');
      const tokenData = document.getElementById('tokenData');
      
      if (!token) {
        tokenInfo.innerHTML = '<p class="error">No token found in localStorage!</p>';
        tokenData.textContent = '';
        return;
      }
      
      tokenInfo.innerHTML = `
        <p><strong>Token:</strong> <span class="success">Found</span></p>
        <p><strong>Token Length:</strong> ${token.length} characters</p>
        <p><strong>Schema Name:</strong> ${schemaName || 'Not set'}</p>
      `;
      
      const data = {
        token: token,
        user: user ? JSON.parse(user) : null,
        tenant: tenant ? JSON.parse(tenant) : null,
        schema_name: schemaName
      };
      
      tokenData.textContent = JSON.stringify(data, null, 2);
    });
    
    document.getElementById('clearToken').addEventListener('click', function() {
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      localStorage.removeItem('tenant');
      localStorage.removeItem('schema_name');
      
      document.getElementById('tokenInfo').innerHTML = '<p class="error">Token cleared from localStorage!</p>';
      document.getElementById('tokenData').textContent = '';
    });
    
    document.getElementById('setTestToken').addEventListener('click', function() {
      const testToken = document.getElementById('testToken').value.trim();
      const resultElement = document.getElementById('setTokenResult');
      
      if (!testToken) {
        resultElement.innerHTML = '<p class="error">Please enter a token!</p>';
        return;
      }
      
      localStorage.setItem('token', testToken);
      
      // Set a basic user and tenant if they don't exist
      if (!localStorage.getItem('user')) {
        const mockUser = {
          id: 1,
          email: '<EMAIL>',
          first_name: 'Test',
          last_name: 'User'
        };
        localStorage.setItem('user', JSON.stringify(mockUser));
      }
      
      if (!localStorage.getItem('tenant')) {
        const mockTenant = {
          schema_name: 'kebele 14',
          name: 'Kebele 14'
        };
        localStorage.setItem('tenant', JSON.stringify(mockTenant));
        localStorage.setItem('schema_name', 'kebele 14');
      }
      
      resultElement.innerHTML = '<p class="success">Test token set successfully!</p>';
      
      // Refresh token display
      document.getElementById('checkToken').click();
    });
    
    document.getElementById('testRequest').addEventListener('click', function() {
      const token = localStorage.getItem('token');
      const schemaName = localStorage.getItem('schema_name') || '';
      const resultElement = document.getElementById('requestResult');
      const responseElement = document.getElementById('responseData');
      
      if (!token) {
        resultElement.innerHTML = '<p class="error">No token found! Please set a token first.</p>';
        return;
      }
      
      resultElement.innerHTML = '<p>Testing API request...</p>';
      responseElement.textContent = '';
      
      // Encode the schema name for the URL
      const encodedSchema = encodeURIComponent(schemaName);
      const url = `http://localhost:8000/api/tenant/${encodedSchema}/idcards/`;
      
      fetch(url, {
        method: 'GET',
        headers: {
          'Authorization': `Token ${token}`,
          'Content-Type': 'application/json',
          'X-Schema-Name': schemaName
        }
      })
      .then(response => {
        if (!response.ok) {
          throw new Error(`HTTP error ${response.status}: ${response.statusText}`);
        }
        return response.json();
      })
      .then(data => {
        resultElement.innerHTML = '<p class="success">API request successful!</p>';
        responseElement.textContent = JSON.stringify(data, null, 2);
      })
      .catch(error => {
        resultElement.innerHTML = `<p class="error">API request failed: ${error.message}</p>`;
        console.error('API request error:', error);
      });
    });
    
    // Auto-check token on page load
    document.addEventListener('DOMContentLoaded', function() {
      document.getElementById('checkToken').click();
    });
  </script>
</body>
</html>
