from rest_framework import serializers
from .models_document import Document
from common.models import DocumentType

class DocumentTypeSerializer(serializers.ModelSerializer):
    """Serializer for the DocumentType model."""
    class Meta:
        model = DocumentType
        fields = '__all__'
        ref_name = "CitizenDocumentType"

class DocumentSerializer(serializers.ModelSerializer):
    """Serializer for the Document model."""
    document_type_name = serializers.CharField(source='document_type.name', read_only=True)
    citizen_name = serializers.CharField(source='citizen.get_full_name', read_only=True)

    class Meta:
        model = Document
        fields = '__all__'
        read_only_fields = ('created_at', 'updated_at')

    def validate(self, data):
        """Validate the data."""
        # Check if expiry date is after issue date
        if data.get('issue_date') and data.get('expiry_date'):
            if data['expiry_date'] <= data['issue_date']:
                raise serializers.ValidationError("Expiry date must be after issue date.")
        return data
