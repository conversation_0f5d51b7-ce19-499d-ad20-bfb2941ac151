import os
import django

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'neocamelot.settings')
django.setup()

from django.db import connection

with connection.cursor() as cursor:
    cursor.execute("SELECT column_name, data_type FROM information_schema.columns WHERE table_name = 'citizens_citizen' AND column_name = 'marital_status_id'")
    columns = cursor.fetchall()
    for column in columns:
        print(f"Column: {column[0]}, Type: {column[1]}")
