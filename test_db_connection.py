import psycopg2

def test_connection():
    """Test connection to PostgreSQL database."""
    try:
        # Try to connect to PostgreSQL
        conn = psycopg2.connect(
            dbname='neocamelot',
            user='postgres',
            password='postgres',
            host='localhost',
            port='5432'
        )
        
        # Create a cursor
        cursor = conn.cursor()
        
        # Execute a simple query
        cursor.execute("SELECT version();")
        
        # Fetch the result
        version = cursor.fetchone()
        print(f"PostgreSQL version: {version[0]}")
        
        # Close the cursor and connection
        cursor.close()
        conn.close()
        
        print("Database connection successful!")
        return True
    except psycopg2.OperationalError as e:
        print(f"Error connecting to PostgreSQL: {e}")
        return False
    except Exception as e:
        print(f"Unexpected error: {e}")
        return False

if __name__ == "__main__":
    test_connection()
