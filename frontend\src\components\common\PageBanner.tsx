import React, { ReactNode } from 'react';
import { Box, Typography, Container, Paper } from '@mui/material';

interface PageBannerProps {
  title: string;
  subtitle?: string;
  icon?: ReactNode;
  actionContent?: ReactNode;
  color?: string;
}

/**
 * A reusable page banner component with title, subtitle, icon, and action content
 */
const PageBanner: React.FC<PageBannerProps> = ({
  title,
  subtitle,
  icon,
  actionContent,
  color = 'primary.main'
}) => {
  return (
    <Paper
      elevation={0}
      sx={{
        bgcolor: color,
        color: 'white',
        borderRadius: 0,
        mb: 4,
        py: 3,
        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.1)'
      }}
    >
      <Container maxWidth="lg">
        <Box
          sx={{
            display: 'flex',
            flexDirection: { xs: 'column', md: 'row' },
            alignItems: 'center',
            justifyContent: 'space-between',
            gap: 2
          }}
        >
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            {icon && (
              <Box
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  bgcolor: 'rgba(255, 255, 255, 0.2)',
                  borderRadius: '50%',
                  p: 1,
                  width: 70,
                  height: 70
                }}
              >
                {icon}
              </Box>
            )}
            <Box>
              <Typography
                variant="h4"
                component="h1"
                sx={{
                  fontWeight: 700,
                  fontSize: { xs: '1.75rem', md: '2.25rem' },
                  textShadow: '0 2px 4px rgba(0, 0, 0, 0.1)'
                }}
              >
                {title}
              </Typography>
              {subtitle && (
                <Typography
                  variant="subtitle1"
                  sx={{
                    opacity: 0.9,
                    mt: 0.5,
                    fontWeight: 400,
                    textShadow: '0 1px 2px rgba(0, 0, 0, 0.1)'
                  }}
                >
                  {subtitle}
                </Typography>
              )}
            </Box>
          </Box>

          {actionContent && (
            <Box
              sx={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: { xs: 'center', md: 'flex-end' },
                width: { xs: '100%', md: 'auto' },
                mt: { xs: 2, md: 0 }
              }}
            >
              {actionContent}
            </Box>
          )}
        </Box>
      </Container>
    </Paper>
  );
};

export default PageBanner;
