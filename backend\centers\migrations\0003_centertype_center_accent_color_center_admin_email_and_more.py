# Generated by Django 5.1.7 on 2025-04-14 12:37

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('centers', '0002_auto_20250414_generate_center_codes'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='CenterType',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True)),
                ('description', models.TextField(blank=True)),
                ('is_active', models.<PERSON><PERSON>anField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.AddField(
            model_name='center',
            name='accent_color',
            field=models.Char<PERSON>ield(blank=True, default='#2980b9', help_text='Accent color in hex format', max_length=20),
        ),
        migrations.AddField(
            model_name='center',
            name='admin_email',
            field=models.EmailField(blank=True, help_text='Email of the center administrator', max_length=254),
        ),
        migrations.AddField(
            model_name='center',
            name='admin_name',
            field=models.CharField(blank=True, help_text='Name of the center administrator', max_length=100),
        ),
        migrations.AddField(
            model_name='center',
            name='admin_phone',
            field=models.CharField(blank=True, help_text='Phone of the center administrator', max_length=20),
        ),
        migrations.AddField(
            model_name='center',
            name='alternate_phone',
            field=models.CharField(blank=True, max_length=20),
        ),
        migrations.AddField(
            model_name='center',
            name='city',
            field=models.CharField(blank=True, max_length=100),
        ),
        migrations.AddField(
            model_name='center',
            name='code',
            field=models.CharField(blank=True, help_text='Unique code for the center', max_length=10),
        ),
        migrations.AddField(
            model_name='center',
            name='country',
            field=models.CharField(default='Ethiopia', max_length=100),
        ),
        migrations.AddField(
            model_name='center',
            name='created_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_centers', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='center',
            name='description',
            field=models.TextField(blank=True),
        ),
        migrations.AddField(
            model_name='center',
            name='header_color',
            field=models.CharField(blank=True, default='#3498db', help_text='Header color in hex format', max_length=20),
        ),
        migrations.AddField(
            model_name='center',
            name='is_verified',
            field=models.BooleanField(default=False, help_text='Whether the center has been verified'),
        ),
        migrations.AddField(
            model_name='center',
            name='max_citizens',
            field=models.PositiveIntegerField(default=1000, help_text='Maximum number of citizens allowed'),
        ),
        migrations.AddField(
            model_name='center',
            name='max_id_cards',
            field=models.PositiveIntegerField(default=1000, help_text='Maximum number of ID cards allowed'),
        ),
        migrations.AddField(
            model_name='center',
            name='max_users',
            field=models.PositiveIntegerField(default=10, help_text='Maximum number of users allowed'),
        ),
        migrations.AddField(
            model_name='center',
            name='postal_code',
            field=models.CharField(blank=True, max_length=20),
        ),
        migrations.AddField(
            model_name='center',
            name='settings',
            field=models.JSONField(blank=True, default=dict, help_text='Center-specific settings'),
        ),
        migrations.AddField(
            model_name='center',
            name='state',
            field=models.CharField(blank=True, max_length=100),
        ),
        migrations.AddField(
            model_name='center',
            name='subscription_expiry',
            field=models.DateField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='center',
            name='subscription_status',
            field=models.CharField(choices=[('trial', 'Trial'), ('basic', 'Basic'), ('premium', 'Premium'), ('enterprise', 'Enterprise')], default='trial', max_length=20),
        ),
        migrations.AddField(
            model_name='center',
            name='website',
            field=models.URLField(blank=True),
        ),
        migrations.AddField(
            model_name='center',
            name='type',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='centers', to='centers.centertype'),
        ),
    ]
