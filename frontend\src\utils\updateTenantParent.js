/**
 * Utility function to update the parent_id in the tenant object stored in localStorage
 * This can be run in the browser console to fix the tenant parent relationship
 */

function updateTenantParent(parentId) {
  // Get the current tenant from localStorage
  const tenantString = localStorage.getItem('tenant');
  if (!tenantString) {
    console.error('No tenant found in localStorage');
    return false;
  }

  try {
    // Parse the tenant object
    const tenant = JSON.parse(tenantString);
    console.log('Current tenant:', tenant);
    
    // Update the parent_id
    const updatedTenant = {
      ...tenant,
      parent_id: parentId
    };
    
    // Save the updated tenant back to localStorage
    localStorage.setItem('tenant', JSON.stringify(updatedTenant));
    
    console.log('Updated tenant:', updatedTenant);
    console.log('Tenant parent_id updated successfully. Please refresh the page.');
    
    return true;
  } catch (error) {
    console.error('Error updating tenant parent_id:', error);
    return false;
  }
}

// Example usage:
// updateTenantParent('123');  // Replace '123' with the actual parent ID
