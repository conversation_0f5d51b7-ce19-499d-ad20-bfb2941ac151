import React, { useState, useEffect } from 'react';
import { Button, Typography, Box, Paper, CircularProgress, Alert } from '@mui/material';
import { useAuth } from '../contexts/AuthContext';
import { useTenant } from '../contexts/TenantContext';
import { logTokenDebugInfo } from '../utils/tokenDebugger';

/**
 * Component to test the citizens API endpoint
 */
const CitizensApiTest: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [citizens, setCitizens] = useState<any[]>([]);

  // Get authentication and tenant context
  const { isAuthenticated, getAuthHeaders } = useAuth();
  const { tenant } = useTenant();

  // Function to fetch citizens
  const fetchCitizens = async () => {
    setLoading(true);
    setError(null);
    setSuccess(null);

    try {
      // Check if user is authenticated
      if (!isAuthenticated) {
        setError('You must be logged in to access this resource');
        setLoading(false);
        return;
      }

      // Get the current schema
      const schema = tenant?.schema_name || localStorage.getItem('schema_name');
      if (!schema) {
        setError('No tenant selected');
        setLoading(false);
        return;
      }

      // Format schema for API endpoint (replace spaces with underscores)
      const formattedSchema = schema.replace(/\s+/g, '_');

      // Get authentication headers
      const headers = getAuthHeaders();

      console.log('Request headers:', {
        'Authorization': headers['Authorization'] ? 'Present' : 'None',
        'Content-Type': headers['Content-Type'],
        'X-Schema-Name': headers['X-Schema-Name'] || 'None'
      });

      // Make the request
      const response = await fetch(`http://localhost:8000/api/tenant/${formattedSchema}/citizens/`, {
        method: 'GET',
        headers: headers,
        credentials: 'include'
      });

      console.log('Response status:', response.status);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Error response:', errorText);
        throw new Error(`Failed to fetch citizens: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      console.log('Citizens data:', data);

      setCitizens(data);
      setSuccess('Successfully fetched citizens data');
    } catch (error: any) {
      console.error('Error fetching citizens:', error);
      setError(error.message || 'Failed to fetch citizens');
    } finally {
      setLoading(false);
    }
  };

  // Try both token formats
  const tryBothTokenFormats = async () => {
    setLoading(true);
    setError(null);
    setSuccess(null);

    try {
      // Check if user is authenticated
      if (!isAuthenticated) {
        setError('You must be logged in to access this resource');
        setLoading(false);
        return;
      }

      // Get the current schema
      const schema = tenant?.schema_name || localStorage.getItem('schema_name');
      if (!schema) {
        setError('No tenant selected');
        setLoading(false);
        return;
      }

      // Format schema for API endpoint (replace spaces with underscores)
      const formattedSchema = schema.replace(/\s+/g, '_');

      // Get token from localStorage
      const token = localStorage.getItem('access_token');
      if (!token) {
        setError('No access token found');
        setLoading(false);
        return;
      }

      // Try with Bearer prefix
      console.log('Trying with Bearer prefix...');
      let response = await fetch(`http://localhost:8000/api/tenant/${formattedSchema}/citizens/`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
          'X-Schema-Name': schema
        },
        credentials: 'include'
      });

      console.log('Bearer response status:', response.status);

      if (response.ok) {
        const data = await response.json();
        console.log('Citizens data (Bearer):', data);
        setCitizens(data);
        setSuccess('Successfully fetched citizens data with Bearer token');
        setLoading(false);
        return;
      }

      // If Bearer fails, try with Token prefix
      console.log('Trying with Token prefix...');
      response = await fetch(`http://localhost:8000/api/tenant/${formattedSchema}/citizens/`, {
        method: 'GET',
        headers: {
          'Authorization': `Token ${token}`,
          'Content-Type': 'application/json',
          'X-Schema-Name': schema
        },
        credentials: 'include'
      });

      console.log('Token response status:', response.status);

      if (response.ok) {
        const data = await response.json();
        console.log('Citizens data (Token):', data);
        setCitizens(data);
        setSuccess('Successfully fetched citizens data with Token prefix');
        setLoading(false);
        return;
      }

      // If both fail, throw an error
      throw new Error(`Failed to fetch citizens with both Bearer and Token prefixes`);
    } catch (error: any) {
      console.error('Error fetching citizens:', error);
      setError(error.message || 'Failed to fetch citizens');
    } finally {
      setLoading(false);
    }
  };

  // Debug function
  const debugTokenInfo = () => {
    logTokenDebugInfo();
    setSuccess('Token debug info logged to console. Press F12 to view.');
  };

  return (
    <Paper elevation={3} sx={{ p: 3, mt: 3 }}>
      <Typography variant="h5" gutterBottom>
        Citizens API Test
      </Typography>

      <Box sx={{ mb: 2 }}>
        <Button
          variant="contained"
          onClick={fetchCitizens}
          disabled={loading}
          sx={{ mr: 2 }}
        >
          {loading ? <CircularProgress size={24} /> : 'Fetch Citizens'}
        </Button>

        <Button
          variant="outlined"
          onClick={tryBothTokenFormats}
          disabled={loading}
          sx={{ mr: 2 }}
        >
          Try Both Token Formats
        </Button>

        <Button
          variant="outlined"
          color="secondary"
          onClick={debugTokenInfo}
          disabled={loading}
        >
          Debug Token Info
        </Button>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      {success && (
        <Alert severity="success" sx={{ mb: 2 }}>
          {success}
        </Alert>
      )}

      {citizens.length > 0 && (
        <Box>
          <Typography variant="h6" gutterBottom>
            Citizens ({citizens.length})
          </Typography>

          <Box sx={{ maxHeight: 400, overflow: 'auto' }}>
            {citizens.map((citizen: any) => (
              <Box key={citizen.id} sx={{ mb: 1, p: 1, border: '1px solid #eee' }}>
                <Typography>
                  <strong>{citizen.first_name} {citizen.last_name}</strong>
                </Typography>
                <Typography variant="body2">
                  ID: {citizen.id_number || 'N/A'}
                </Typography>
              </Box>
            ))}
          </Box>
        </Box>
      )}
    </Paper>
  );
};

export default CitizensApiTest;
