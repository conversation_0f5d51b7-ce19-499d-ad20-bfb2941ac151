<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Login to Kebele 16</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
      line-height: 1.6;
    }
    h1, h2, h3 {
      color: #3f51b5;
    }
    .card {
      border: 1px solid #ddd;
      border-radius: 8px;
      padding: 20px;
      margin-bottom: 20px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    button {
      background-color: #3f51b5;
      color: white;
      border: none;
      padding: 10px 20px;
      border-radius: 4px;
      cursor: pointer;
      font-size: 16px;
      margin-right: 10px;
      margin-bottom: 10px;
    }
    button:hover {
      background-color: #303f9f;
    }
    pre {
      background-color: #f5f5f5;
      padding: 10px;
      border-radius: 4px;
      overflow-x: auto;
      white-space: pre-wrap;
      word-break: break-all;
    }
    .success { color: green; }
    .error { color: red; }
    .warning { color: orange; }
    input[type="text"],
    input[type="password"] {
      width: 100%;
      padding: 10px;
      margin: 5px 0 15px;
      border: 1px solid #ddd;
      border-radius: 4px;
      box-sizing: border-box;
    }
    label {
      font-weight: bold;
    }
    .form-group {
      margin-bottom: 15px;
    }
    .note {
      background-color: #fff3cd;
      border-left: 4px solid #ffc107;
      padding: 10px;
      margin-bottom: 15px;
    }
    .step {
      background-color: #e8f4fd;
      border-left: 4px solid #2196f3;
      padding: 10px;
      margin-bottom: 15px;
    }
  </style>
</head>
<body>
  <h1>Login to Kebele 16</h1>
  
  <div class="note">
    <p><strong>Important:</strong> This page is specifically designed to log you into the kebele 16 tenant.</p>
    <p>Based on the backend logs, we need to ensure the token is stored in the correct schema.</p>
  </div>
  
  <div class="card">
    <h2>Step 1: Login to Kebele 16</h2>
    <div class="form-group">
      <label for="email">Email:</label>
      <input type="text" id="email" placeholder="Enter your email">
    </div>
    <div class="form-group">
      <label for="password">Password:</label>
      <input type="password" id="password" placeholder="Enter your password">
    </div>
    <button id="login-btn">Login to Kebele 16</button>
    <div id="login-result" style="margin-top: 10px;"></div>
  </div>
  
  <div class="card">
    <h2>Step 2: Test API Request</h2>
    <button id="test-api">Test API Request</button>
    <div id="api-result" style="margin-top: 10px; display: none;"></div>
  </div>
  
  <div class="card">
    <h2>Navigation</h2>
    <button onclick="window.location.href = '/'">Go to Home</button>
    <button onclick="window.location.href = '/citizens'">Go to Citizens</button>
  </div>
  
  <script>
    // Constants
    const SCHEMA_NAME = 'kebele 16';
    const URL_SCHEMA = 'kebele_16';
    
    // Initialize the page
    document.addEventListener('DOMContentLoaded', function() {
      // Add event listeners
      document.getElementById('login-btn').addEventListener('click', handleLogin);
      document.getElementById('test-api').addEventListener('click', testApi);
    });
    
    // Handle login
    async function handleLogin() {
      const email = document.getElementById('email').value.trim();
      const password = document.getElementById('password').value;
      const loginResultDiv = document.getElementById('login-result');
      
      if (!email || !password) {
        loginResultDiv.innerHTML = '<p class="error">Please enter both email and password</p>';
        return;
      }
      
      loginResultDiv.innerHTML = '<p>Logging in...</p>';
      
      try {
        // Clear all existing authentication data
        clearAuthData();
        
        // Create headers
        const headers = new Headers({
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'X-Schema-Name': SCHEMA_NAME
        });
        
        // Set schema cookie
        document.cookie = `schema_name=${encodeURIComponent(SCHEMA_NAME)}; path=/; SameSite=Lax`;
        
        // Make the login request
        const response = await fetch('/api/login/', {
          method: 'POST',
          headers: headers,
          body: JSON.stringify({
            email: email,
            password: password,
            schema_name: SCHEMA_NAME
          }),
          credentials: 'include'
        });
        
        // Get response details
        const status = response.status;
        const statusText = response.statusText;
        
        let responseData;
        try {
          responseData = await response.json();
        } catch (e) {
          responseData = await response.text();
        }
        
        // Display the response
        loginResultDiv.innerHTML = `
          <h3>Login Result</h3>
          <p><strong>Status:</strong> ${status} ${statusText}</p>
          <p><strong>Response:</strong></p>
          <pre>${typeof responseData === 'object' ? JSON.stringify(responseData, null, 2) : responseData}</pre>
        `;
        
        // If login successful, store the token
        if (response.ok && responseData && responseData.token) {
          // Clean the token (remove ALL spaces and commas)
          const cleanToken = responseData.token.replace(/[\s,]+/g, '');
          
          // Store the token in localStorage
          localStorage.setItem('token', cleanToken);
          
          // Store the schema name
          localStorage.setItem('schema_name', SCHEMA_NAME);
          
          // Set the schema cookie
          document.cookie = `schema_name=${encodeURIComponent(SCHEMA_NAME)}; path=/; SameSite=Lax`;
          
          // Store user data if available
          if (responseData.user) {
            localStorage.setItem('user', JSON.stringify(responseData.user));
          }
          
          // Store tenant data if available
          if (responseData.tenant) {
            localStorage.setItem('tenant', JSON.stringify(responseData.tenant));
          } else {
            // Create a basic tenant object
            const tenant = {
              schema_name: SCHEMA_NAME,
              name: SCHEMA_NAME
            };
            localStorage.setItem('tenant', JSON.stringify(tenant));
          }
          
          // Create a tokenStore
          const tokenStore = {};
          tokenStore[SCHEMA_NAME] = cleanToken;
          localStorage.setItem('tokenStore', JSON.stringify(tokenStore));
          
          loginResultDiv.innerHTML += `
            <p class="success">Login successful! Token stored.</p>
            <p><strong>Token:</strong> ${cleanToken}</p>
          `;
        }
      } catch (error) {
        loginResultDiv.innerHTML = `<p class="error">Error logging in: ${error.message}</p>`;
      }
    }
    
    // Test API request
    async function testApi() {
      const apiResultDiv = document.getElementById('api-result');
      apiResultDiv.style.display = 'block';
      apiResultDiv.innerHTML = '<p>Testing API request...</p>';
      
      try {
        // Get the token from localStorage
        const token = localStorage.getItem('token');
        if (!token) {
          apiResultDiv.innerHTML = '<p class="error">No token found in localStorage. Please login first.</p>';
          return;
        }
        
        // Create the URL
        const url = `/api/tenant/${URL_SCHEMA}/citizens/?limit=1`;
        
        // Create headers
        const headers = new Headers({
          'Authorization': `Token ${token}`,
          'Content-Type': 'application/json',
          'X-Schema-Name': SCHEMA_NAME
        });
        
        // Set schema cookie
        document.cookie = `schema_name=${encodeURIComponent(SCHEMA_NAME)}; path=/; SameSite=Lax`;
        
        // Make the request
        const response = await fetch(url, {
          method: 'GET',
          headers: headers,
          credentials: 'include'
        });
        
        // Get response details
        const status = response.status;
        const statusText = response.statusText;
        
        let responseData;
        try {
          responseData = await response.json();
        } catch (e) {
          responseData = await response.text();
        }
        
        // Display the response
        apiResultDiv.innerHTML = `
          <h3>API Request Result</h3>
          <p><strong>URL:</strong> ${url}</p>
          <p><strong>Status:</strong> ${status} ${statusText}</p>
          <p><strong>Headers Sent:</strong></p>
          <pre>${JSON.stringify(Object.fromEntries(headers.entries()), null, 2)}</pre>
          <p><strong>Response:</strong></p>
          <pre>${typeof responseData === 'object' ? JSON.stringify(responseData, null, 2) : responseData}</pre>
        `;
        
        // If successful, show a success message
        if (response.ok) {
          apiResultDiv.innerHTML += '<p class="success">API request successful!</p>';
        }
      } catch (error) {
        apiResultDiv.innerHTML = `<p class="error">Error testing API: ${error.message}</p>`;
      }
    }
    
    // Clear authentication data
    function clearAuthData() {
      // Clear localStorage
      localStorage.removeItem('token');
      localStorage.removeItem('schema_name');
      localStorage.removeItem('tokenStore');
      localStorage.removeItem('tenant');
      localStorage.removeItem('user');
      
      // Clear cookies
      document.cookie.split(';').forEach(function(c) {
        document.cookie = c.trim().split('=')[0] + '=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
      });
      
      console.log('Authentication data cleared');
    }
  </script>
</body>
</html>
