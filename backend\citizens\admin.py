from django.contrib import admin
from .models import Citizen
from .models_document import Document
from .models_family import Child, Parent, EmergencyContact, Spouse
from common.models import RelationshipType
from .models_biometric import Biometric, Photo
from .admin_document import DocumentAdmin
from .admin_family import ChildAdmin, ParentAdmin, EmergencyContactAdmin, SpouseAdmin
from .admin_biometric import BiometricAdmin, PhotoAdmin
from centers.models import Client
from django_tenants.utils import tenant_context

# Create a tenant-specific admin class for Citizen
class CitizenAdmin(admin.ModelAdmin):
    list_display = ('digital_id', 'first_name', 'middle_name', 'last_name', 'gender', 'date_of_birth',
                    'is_active', 'created_at')
    list_filter = ('is_active', 'gender', 'nationality', 'nationality_country', 'religion', 'citizen_status', 'marital_status', 'employment_type',
                  'subcity', 'kebele', 'ketena', 'current_status', 'is_resident', 'employment')
    search_fields = ('first_name', 'middle_name', 'last_name', 'id_number', 'digital_id', 'email', 'phone', 'house_number')
    readonly_fields = ('digital_id', 'id_number', 'uuid', 'created_at', 'updated_at', 'created_by')

    fieldsets = (
        ('Basic Information', {
            'fields': ('center', 'digital_id', 'id_number', 'uuid', 'first_name', 'middle_name', 'last_name',
                      'date_of_birth', 'gender', 'nationality', 'nationality_country', 'citizen_status')
        }),
        ('Amharic Names', {
            'fields': ('first_name_am', 'middle_name_am', 'last_name_am'),
            'classes': ('collapse',)
        }),
        ('Location Information', {
            'fields': ('subcity', 'kebele', 'ketena', 'region', 'is_resident')
        }),
        ('Contact Information', {
            'fields': ('email', 'phone', 'address', 'house_number')
        }),
        ('ID Card Information', {
            'fields': ('id_issue_date', 'id_expiry_date')
        }),
        ('Employment Information', {
            'fields': ('employment', 'employee_type', 'employment_type', 'occupation', 'organization_name')
        }),
        ('Personal Information', {
            'fields': ('religion', 'marital_status')
        }),
        ('Family Information', {
            'fields': ('spouse', 'mother', 'father', 'emergency_contact')
        }),
        ('Related Data', {
            'fields': ('biometric_data', 'photo_record'),
            'classes': ('collapse',)
        }),
        ('Status Information', {
            'fields': ('current_status', 'is_active')
        }),
        ('System Information', {
            'fields': ('created_at', 'updated_at', 'created_by'),
            'classes': ('collapse',)
        }),
    )

    def save_model(self, request, obj, form, change):
        if not change:  # Only set created_by when creating a new object
            obj.created_by = request.user
        super().save_model(request, obj, form, change)

# Create a global admin class for Citizen that works across all tenants
class GlobalCitizenAdmin(admin.ModelAdmin):
    list_display = ('schema_name', 'digital_id', 'first_name', 'middle_name', 'last_name', 'gender', 'date_of_birth', 'is_active')
    list_filter = ('is_active', 'gender', 'nationality', 'religion', 'citizen_status', 'marital_status', 'employment_type')
    search_fields = ('first_name', 'middle_name', 'last_name', 'digital_id', 'id_number', 'email', 'phone')
    readonly_fields = ('schema_name', 'digital_id', 'id_number', 'created_at', 'updated_at', 'created_by')

    def get_queryset(self, request):
        # Get all tenants
        tenants = Client.objects.exclude(schema_name='public')

        # Initialize an empty list to store citizens from all tenants
        all_citizens = []

        # Iterate through each tenant and get their citizens
        for tenant in tenants:
            try:
                with tenant_context(tenant):
                    # Get citizens for this tenant
                    citizens = list(Citizen.objects.all())

                    # Add schema_name attribute to each citizen
                    for citizen in citizens:
                        citizen.schema_name = tenant.schema_name

                    # Add citizens to the list
                    all_citizens.extend(citizens)
            except Exception as e:
                print(f"Error accessing tenant {{tenant.schema_name}}: {{str(e)}}")

        # Return a queryset-like object
        return all_citizens

    def schema_name(self, obj):
        return obj.schema_name if hasattr(obj, 'schema_name') else 'Unknown'
    schema_name.short_description = 'Tenant'

    def has_add_permission(self, request):
        return False

    def has_change_permission(self, request, obj=None):
        return False

    def has_delete_permission(self, request, obj=None):
        return False

# Register the models with the admin site
try:
    admin.site.unregister(Citizen)
except admin.sites.NotRegistered:
    pass

# Register with the appropriate admin class based on the current schema
from django.db import connection
if connection.schema_name == 'public':
    # Disable global admin class to avoid 'distinct' method error
    # admin.site.register(Citizen, GlobalCitizenAdmin)
    pass
else:
    admin.site.register(Citizen, CitizenAdmin)
