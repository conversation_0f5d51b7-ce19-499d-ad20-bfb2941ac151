import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Grid,
  Typography,
  TextField,
  Button,
  Checkbox,
  FormControlLabel,
  Link,
  Paper,
  CircularProgress,
  Alert,
} from '@mui/material';
import { Link as RouterLink, useNavigate, useLocation } from 'react-router-dom';
import PageBanner from '../components/PageBanner';
import LockIcon from '@mui/icons-material/Lock';
// JWT authentication is used instead of token service
import { useAuth } from '../contexts/AuthContext';
// No need for Tenant interface with automatic login

const Login: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const auth = useAuth(); // Use the auth context
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [rememberMe, setRememberMe] = useState(false);
  const [loading, setLoading] = useState(false);
  const [loginError, setLoginError] = useState<string | null>(null);
  const [tenantLogo, setTenantLogo] = useState<string | null>(null);
  const [tenantName, setTenantName] = useState<string>('Gondar ID Card Management System');

  // Get query parameters
  const searchParams = new URLSearchParams(location.search);
  const returnUrl = searchParams.get('returnUrl') || '/dashboard';
  const schemaParam = searchParams.get('schema');

  // State to track if we have a schema from URL
  const [schemaFromUrl, setSchemaFromUrl] = useState<string | null>(null);

  // Check for session expired error and fetch tenant logo and name
  useEffect(() => {
    // Check for session expired error in URL
    const params = new URLSearchParams(window.location.search);
    const error = params.get('error');
    const schemaFromParams = params.get('schema');

    if (error === 'session_expired') {
      setLoginError('Your session has expired. Please log in again.');
    } else if (error === 'no_token') {
      setLoginError('No valid authentication token found. Please log in again.');
    } else if (error === 'invalid_token') {
      setLoginError('Your authentication token is invalid. Please log in again.');
    } else if (error === 'token_expired') {
      setLoginError('Your authentication token has expired. Please log in again.');
    } else if (error) {
      setLoginError(`Authentication error: ${error}. Please log in again.`);
    }

    // Check if we were redirected due to token expiration
    const tokenExpired = localStorage.getItem('token_expired');
    if (tokenExpired === 'true') {
      // Get additional information about the expiration
      const reason = localStorage.getItem('token_expired_reason') || 'unknown';
      const schema = localStorage.getItem('token_expired_schema') || '';
      const timestamp = localStorage.getItem('token_expired_timestamp') || '';

      console.log(`Session expired. Reason: ${reason}, Schema: ${schema}, Time: ${timestamp}`);

      // Clear the flags
      localStorage.removeItem('token_expired');
      localStorage.removeItem('token_expired_reason');
      localStorage.removeItem('token_expired_schema');
      localStorage.removeItem('token_expired_timestamp');

      // Show a message to the user based on the reason
      if (reason === 'invalid_refresh_token') {
        setLoginError('Your session has expired because your authentication token is no longer valid. Please log in again.');
      } else if (reason === 'missing_refresh_token') {
        setLoginError('Your session has expired because your authentication token is missing. Please log in again.');
      } else {
        setLoginError('Your session has expired. Please log in again.');
      }
    }

    // Check if emergency reset was triggered
    const emergencyReset = localStorage.getItem('emergency_reset');
    if (emergencyReset === 'true') {
      // Clear the flag
      localStorage.removeItem('emergency_reset');

      // Show a message to the user
      setLoginError('The application was reset due to a refresh loop. Please log in again.');
    }

    // Clear any existing tokens to ensure a fresh login
    if (error) {
      console.log('Clearing existing tokens due to login error');
      localStorage.removeItem('token');
      localStorage.removeItem('tokenStore');
      sessionStorage.removeItem('token');

      // Clear cookies related to authentication
      document.cookie = 'auth_token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
      document.cookie = 'token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
    }

    // If we have a schema parameter, store it
    if (schemaFromParams) {
      console.log(`Found schema parameter in URL: ${schemaFromParams}`);
      setSchemaFromUrl(schemaFromParams);

      // Store the schema in localStorage for later use
      localStorage.setItem('schema_name', schemaFromParams);

      // Also set as cookie for backend compatibility
      // Make sure to set the path to / so it's available for all requests
      document.cookie = `schema_name=${encodeURIComponent(schemaFromParams)}; path=/`;

      console.log(`Set schema_name cookie to: ${schemaFromParams}`);

      // Log all cookies for debugging
      console.log('All cookies:', document.cookie);
    } else {
      // If no schema parameter, check if we have one in localStorage
      const schemaFromStorage = localStorage.getItem('schema_name');
      if (schemaFromStorage) {
        console.log(`Using schema from localStorage: ${schemaFromStorage}`);
        setSchemaFromUrl(schemaFromStorage);

        // Make sure the cookie is set
        document.cookie = `schema_name=${encodeURIComponent(schemaFromStorage)}; path=/`;

        console.log(`Set schema_name cookie to: ${schemaFromStorage}`);
      }
    }

    const fetchTenantInfo = async () => {
      try {
        // Determine which tenant schema to use
        // Priority: 1. URL parameter, 2. Subdomain, 3. Default
        let tenantSchema = 'public';

        if (schemaFromParams) {
          // Use schema from URL parameter
          tenantSchema = schemaFromParams;
          console.log(`Using schema from URL parameter: ${tenantSchema}`);
        } else {
          // Try to get from hostname/subdomain
          const hostname = window.location.hostname;
          if (hostname.includes('.')) {
            const subdomain = hostname.split('.')[0];
            if (subdomain !== 'www') {
              tenantSchema = subdomain;
              console.log(`Using schema from subdomain: ${tenantSchema}`);
            }
          }
        }

        // Fetch tenant info including logo
        console.log(`Fetching tenant info for schema: ${tenantSchema}`);
        const response = await fetch(`/api/tenant-info/${tenantSchema}/`, {
          // Add timeout to avoid long waits
          signal: AbortSignal.timeout(5000),
          // Add CORS headers
          mode: 'cors',
          headers: {
            'Accept': 'application/json',
          }
        });

        if (response.ok) {
          const data = await response.json();
          console.log('Tenant info response:', data);

          if (data.logo_url) {
            setTenantLogo(data.logo_url);
          }

          if (data.name) {
            setTenantName(data.name);
          }

          // If we got tenant info but didn't have a schema from URL,
          // store the schema from the response
          if (!schemaFromParams && data.schema_name) {
            console.log(`Setting schema from tenant info: ${data.schema_name}`);
            setSchemaFromUrl(data.schema_name);
            localStorage.setItem('schema_name', data.schema_name);
            document.cookie = `schema_name=${encodeURIComponent(data.schema_name)}; path=/`;
          }
        } else {
          console.warn(`Failed to fetch tenant info: ${response.status} ${response.statusText}`);
        }
      } catch (error) {
        console.error('Error fetching tenant info:', error);
        // Keep default logo and name if there's an error
      }
    };

    fetchTenantInfo();
  }, [schemaParam]); // Add schemaParam as a dependency

  // JWT authentication handles API URLs internally

  // No direct login function needed

  // No need to check server status when component mounts

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setLoginError(null);

    // Use only JWT login
    try {
      console.log('Attempting JWT login with email:', email);

      // Get the schema from URL parameter or from URL
      let schemaName = schemaParam || schemaFromUrl;

      console.log(`Initial schema from URL or parameter: ${schemaName || 'undefined'}`);

      // Special case for public schema - don't pass it to loginWithJWT
      if (schemaName === 'public') {
        console.log('Public schema detected, will try login without schema parameter');
        schemaName = undefined;
      }

      // The loginWithJWT function will handle schema variations and detection
      // With domain-based detection, we can let the backend find the tenant based on email domain

      // Clear all auth-related items
      localStorage.removeItem('jwt_access_token');
      localStorage.removeItem('tokenStore');
      localStorage.removeItem('token'); // Remove legacy token

      // Clear any existing schema cookies
      document.cookie = 'schema_name=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';

      // Set the schema cookie for the login request
      if (schemaName) {
        document.cookie = `schema_name=${encodeURIComponent(schemaName)}; path=/; SameSite=Lax`;
        console.log(`Set schema_name cookie to: ${schemaName}`);
      } else {
        console.log('No schema specified, will use domain-based detection if available');
      }

      console.log('Cleared auth tokens before login attempt');

      // Use the auth context to login with JWT
      const success = await auth.loginWithJWT(email, password, schemaName);

      if (success) {
        console.log('JWT login successful');

        // Get the schema name from localStorage (set by the JWT login process)
        const currentSchema = localStorage.getItem('schema_name') ||
                             localStorage.getItem('jwt_schema');

        if (currentSchema) {
          console.log(`Using schema name: ${currentSchema}`);

          try {
            // Import tenant context manager
            const { switchTenantContext } = await import('../utils/tenantContextManager');

            // Switch to the tenant context
            const contextSwitchSuccess = await switchTenantContext(currentSchema);
            if (contextSwitchSuccess) {
              console.log(`Successfully switched to tenant context ${currentSchema}`);

              // Store the user role in localStorage if it's not already there
              const user = JSON.parse(localStorage.getItem('user') || '{}');
              if (user && user.role && !localStorage.getItem('user_role')) {
                localStorage.setItem('user_role', user.role);
                console.log(`Stored user role in localStorage: ${user.role}`);
              }

              // Store the tenant type in localStorage if it's not already there
              const tenant = JSON.parse(localStorage.getItem('tenant') || '{}');
              if (tenant && (tenant.schema_type || tenant.type) && !localStorage.getItem('tenant_type')) {
                const tenantType = tenant.schema_type || tenant.type;
                localStorage.setItem('tenant_type', tenantType);
                console.log(`Stored tenant type in localStorage: ${tenantType}`);
              }
            } else {
              console.warn(`Failed to switch to tenant context ${currentSchema}`);
            }
          } catch (error) {
            console.error('Error switching tenant context:', error);
          }
        }

        // Redirect to the dashboard
        navigate(returnUrl);
        return;
      } else {
        throw new Error('JWT login failed');
      }
    } catch (error) {
      console.error('Login error:', error);

      // Handle different error types
      if (error instanceof Error) {
        // Handle connection errors specifically
        if (error instanceof TypeError && error.message?.includes('Failed to fetch')) {
          setLoginError('Cannot connect to the server. Please make sure the backend server is running and try again.');
        } else if (error.message.includes('401')) {
          setLoginError('Invalid email or password. Please try again.');
        } else if (error.message.includes('403')) {
          setLoginError('You do not have permission to access this tenant. Please contact your administrator.');
        } else if (error.message.includes('500')) {
          setLoginError('Server error. Please try again later or contact your administrator.');
        } else {
          setLoginError(error.message || 'Authentication failed');
        }
      } else if (typeof error === 'string') {
        setLoginError(error);
      } else {
        setLoginError('An unexpected error occurred during login');
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <Box
      sx={{
        minHeight: '100vh',
        display: 'flex',
        flexDirection: 'column',
        bgcolor: '#f8f9fa'
      }}
    >
      {/* Hero Banner with PageBanner Component */}
      <PageBanner
        title={tenantName}
        subtitle="Streamline your resident ID card issuance process with our secure and efficient system"
        icon={<LockIcon sx={{ fontSize: 50, color: 'white' }} />}
        actionContent={
          <Box sx={{ textAlign: 'center', width: '100%' }}>
            <Typography variant="body1" sx={{ color: 'white', fontWeight: 500 }}>
              Register, print, and track resident ID cards with ease. Our platform provides end-to-end solutions for all of Gondar city's identification needs.
            </Typography>
          </Box>
        }
      >
        {/* ID Card Overlapping Elements */}
        <Box
          sx={{
            display: { xs: 'none', md: 'block' },
            position: 'absolute',
            right: '-15%',
            top: '50%',
            transform: 'translateY(-50%)',
            width: '250px',
            height: '180px',
            zIndex: 5
          }}
        >
          <Box
            sx={{
              position: 'absolute',
              top: 0,
              right: 0,
              width: '200px',
              height: '120px',
              borderRadius: '10px',
              background: 'rgba(255,255,255,0.9)',
              boxShadow: '0 10px 20px rgba(0,0,0,0.1)',
              transform: 'rotate(5deg)',
              overflow: 'hidden'
            }}
          >
            <Box
              sx={{
                height: '30px',
                background: '#3f51b5',
                display: 'flex',
                alignItems: 'center',
                px: 2
              }}
            >
              <Typography variant="caption" sx={{ color: 'white', fontWeight: 600 }}>
                GONDAR RESIDENT ID
              </Typography>
            </Box>
            <Box sx={{ p: 1, display: 'flex' }}>
              <Box
                sx={{
                  width: '40px',
                  height: '50px',
                  borderRadius: '4px',
                  background: '#e0e0e0',
                  mr: 1
                }}
              />
              <Box>
                <Typography variant="caption" sx={{ fontWeight: 600, fontSize: '8px', display: 'block' }}>
                  John Doe
                </Typography>
                <Typography variant="caption" sx={{ color: 'text.secondary', fontSize: '6px', display: 'block' }}>
                  Kebele: Azezo 03
                </Typography>
                <Typography variant="caption" sx={{ color: 'text.secondary', fontSize: '6px', display: 'block' }}>
                  ID: GDR-12345678
                </Typography>
              </Box>
            </Box>
          </Box>

          <Box
            sx={{
              position: 'absolute',
              bottom: 0,
              left: 0,
              width: '200px',
              height: '120px',
              borderRadius: '10px',
              background: 'rgba(255,255,255,0.9)',
              boxShadow: '0 10px 20px rgba(0,0,0,0.1)',
              transform: 'rotate(-8deg)',
              overflow: 'hidden'
            }}
          >
            <Box
              sx={{
                height: '30px',
                background: '#5c6bc0',
                display: 'flex',
                alignItems: 'center',
                px: 2
              }}
            >
              <Typography variant="caption" sx={{ color: 'white', fontWeight: 600 }}>
                GONDAR CITY ID
              </Typography>
            </Box>
            <Box sx={{ p: 1, display: 'flex' }}>
              <Box
                sx={{
                  width: '40px',
                  height: '50px',
                  borderRadius: '4px',
                  background: '#e0e0e0',
                  mr: 1
                }}
              />
              <Box>
                <Typography variant="caption" sx={{ fontWeight: 600, fontSize: '8px', display: 'block' }}>
                  Jane Smith
                </Typography>
                <Typography variant="caption" sx={{ color: 'text.secondary', fontSize: '6px', display: 'block' }}>
                  Subcity: Maraki
                </Typography>
                <Typography variant="caption" sx={{ color: 'text.secondary', fontSize: '6px', display: 'block' }}>
                  ID: GDR-87654321
                </Typography>
              </Box>
            </Box>
          </Box>
        </Box>
      </PageBanner>

      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Grid container spacing={0} height="100%">
          {tenantLogo ? (
            <Box
              sx={{
                position: 'absolute',
                left: 20,
                top: 20,
                width: 80,
                height: 80,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              }}
            >
              <img
                src={tenantLogo}
                alt="Tenant Logo"
                style={{ maxWidth: '100%', maxHeight: '100%', objectFit: 'contain' }}
                onError={(e) => {
                  console.error('Error loading logo:', e);
                  // Fallback to text if image fails to load
                  const target = e.target as HTMLImageElement;
                  target.style.display = 'none';
                  // Show fallback text
                  const parent = target.parentElement;
                  if (parent) {
                    parent.innerHTML = `<div style="width: 100%; height: 100%; display: flex; align-items: center; justify-content: center; font-weight: bold; font-size: 1.2rem; color: #3f51b5;">Logo</div>`;
                  }
                }}
              />
            </Box>
          ) : (
            <Box
              sx={{
                position: 'absolute',
                left: 20,
                top: 20,
                color: '#3f51b5',
                fontWeight: 'bold',
                fontSize: '1.2rem'
              }}
            >
              Logo
            </Box>
          )}
          <Paper
            elevation={1}
            sx={{
              width: '100%',
              overflow: 'hidden',
              display: 'flex',
              flexDirection: { xs: 'column', md: 'row' },
              borderRadius: 2,
              bgcolor: 'white'
            }}
          >
            {/* Left Section - Illustration */}
            <Box
              sx={{
                width: { xs: '100%', md: '50%' },
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                p: 4,
                position: 'relative'
              }}
            >
              <Box
                component="img"
                src="/id-card-hero.svg"
                alt="ID Card Illustration"
                sx={{
                  maxWidth: '100%',
                  height: 'auto',
                  maxHeight: '400px'
                }}
              />

              {/* Vertical Divider - Only visible on md and up */}
              <Box
                sx={{
                  position: 'absolute',
                  right: 0,
                  top: '15%',
                  height: '70%',
                  display: { xs: 'none', md: 'flex' },
                  flexDirection: 'column',
                  alignItems: 'center'
                }}
              >
                <Box
                  sx={{
                    width: '1px',
                    height: '100%',
                    background: 'linear-gradient(to bottom, rgba(63, 81, 181, 0.05), rgba(63, 81, 181, 0.2), rgba(63, 81, 181, 0.05))'
                  }}
                />
              </Box>
            </Box>
            {/* Right Section - Login Form */}
            <Box
              sx={{
                width: { xs: '100%', md: '50%' },
                p: 4,
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'center'
              }}
            >
              {/* Beautiful Banner */}
              <Box
                sx={{
                  mb: 4,
                  borderRadius: 2,
                  overflow: 'hidden',
                  position: 'relative',
                  boxShadow: '0 4px 12px rgba(0,0,0,0.05)'
                }}
              >
                <Box
                  sx={{
                    height: '120px',
                    background: 'linear-gradient(135deg, #3f51b5 0%, #5c6bc0 50%, #7986cb 100%)',
                    position: 'relative',
                    overflow: 'hidden'
                  }}
                >
                  {/* Decorative Elements */}
                  <Box
                    sx={{
                      position: 'absolute',
                      top: '-20px',
                      right: '-20px',
                      width: '150px',
                      height: '150px',
                      borderRadius: '50%',
                      background: 'rgba(255,255,255,0.1)'
                    }}
                  />
                  <Box
                    sx={{
                      position: 'absolute',
                      bottom: '-30px',
                      left: '20px',
                      width: '100px',
                      height: '100px',
                      borderRadius: '50%',
                      background: 'rgba(255,255,255,0.1)'
                    }}
                  />
                  <Box
                    sx={{
                      position: 'absolute',
                      top: '20px',
                      left: '40%',
                      width: '15px',
                      height: '15px',
                      borderRadius: '50%',
                      background: 'rgba(255,255,255,0.2)'
                    }}
                  />
                  <Box
                    sx={{
                      position: 'absolute',
                      bottom: '20px',
                      right: '30%',
                      width: '10px',
                      height: '10px',
                      borderRadius: '50%',
                      background: 'rgba(255,255,255,0.15)'
                    }}
                  />
                  {/* ID Card Icon */}
                  <Box
                    sx={{
                      position: 'absolute',
                      top: '50%',
                      right: '30px',
                      transform: 'translateY(-50%)',
                      width: '40px',
                      height: '40px',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      background: 'rgba(255,255,255,0.2)',
                      borderRadius: '50%',
                      color: 'white'
                    }}
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <rect x="3" y="4" width="18" height="16" rx="2" ry="2"></rect>
                      <line x1="8" y1="2" x2="8" y2="6"></line>
                      <line x1="16" y1="2" x2="16" y2="6"></line>
                      <circle cx="9" cy="10" r="2"></circle>
                      <path d="M15 8h2"></path>
                      <path d="M15 12h2"></path>
                      <path d="M7 16h10"></path>
                    </svg>
                  </Box>

                  {/* Banner Content */}
                  <Box
                    sx={{
                      position: 'absolute',
                      top: 0,
                      left: 0,
                      width: '100%',
                      height: '100%',
                      display: 'flex',
                      flexDirection: 'column',
                      justifyContent: 'center',
                      p: 3,
                      zIndex: 1
                    }}
                  >
                    <Typography
                      variant="h5"
                      sx={{
                        fontWeight: 700,
                        color: 'white',
                        textShadow: '0 2px 4px rgba(0,0,0,0.2)'
                      }}
                    >
                      Welcome Back!
                    </Typography>
                    <Typography
                      variant="body2"
                      sx={{
                        color: 'rgba(255,255,255,0.9)',
                        mt: 0.5
                      }}
                    >
                      Sign in to access your ID management dashboard
                    </Typography>
                  </Box>
                </Box>
              </Box>

              <Box
                component="form"
                onSubmit={handleSubmit}
                sx={{ width: '100%' }}
              >
                {/* No server status indicator needed */}

                {/* Error message */}
                {loginError && (
                  <Alert severity="error" sx={{ mb: 2 }}>
                    {loginError}
                  </Alert>
                )}

                {/* No tenant selection dropdown for automatic login */}

                <TextField
                  margin="normal"
                  required
                  fullWidth
                  id="email"
                  label="Email Address"
                  name="email"
                  autoComplete="email"
                  autoFocus
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  sx={{
                    mb: 2,
                    '& .MuiOutlinedInput-root': {
                      borderRadius: 1.5
                    }
                  }}
                  // Use slots instead of InputProps (which is deprecated)
                  slotProps={{
                    input: {
                      startAdornment: (
                        <Box component="span" sx={{ color: '#3f51b5', mr: 1 }}>
                          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                            <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path>
                            <polyline points="22,6 12,13 2,6"></polyline>
                          </svg>
                        </Box>
                      )
                    }
                  }}
                />
                <TextField
                  margin="normal"
                  required
                  fullWidth
                  name="password"
                  label="Password"
                  type="password"
                  id="password"
                  autoComplete="current-password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  sx={{
                    mb: 2,
                    '& .MuiOutlinedInput-root': {
                      borderRadius: 1.5
                    }
                  }}
                  // Use slots instead of InputProps (which is deprecated)
                  slotProps={{
                    input: {
                      startAdornment: (
                        <Box component="span" sx={{ color: '#3f51b5', mr: 1 }}>
                          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                            <rect x="3" y="11" width="18" height="11" rx="2" ry="2"></rect>
                            <path d="M7 11V7a5 5 0 0 1 10 0v4"></path>
                          </svg>
                        </Box>
                      )
                    }
                  }}
                />
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                  <FormControlLabel
                    control={
                      <Checkbox
                        checked={rememberMe}
                        onChange={(e) => setRememberMe(e.target.checked)}
                        color="primary"
                        size="small"
                      />
                    }
                    label={<Typography variant="body2">Remember Me</Typography>}
                  />
                  <Link
                    component={RouterLink}
                    to="/forgot-password"
                    variant="body2"
                    sx={{ color: '#3f51b5' }}
                  >
                    Forgot password?
                  </Link>
                </Box>
                <Button
                  type="submit"
                  fullWidth
                  variant="contained"
                  disabled={loading}
                  sx={{
                    mt: 1,
                    mb: 1,
                    py: 1.5,
                    bgcolor: '#3f51b5',
                    borderRadius: 1.5,
                    textTransform: 'none',
                    fontWeight: 600,
                    '&:hover': {
                      bgcolor: '#303f9f'
                    }
                  }}
                >
                  {loading ? (
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <CircularProgress size={20} sx={{ mr: 1, color: 'white' }} />
                      Signing In...
                    </Box>
                  ) : (
                    'Sign In'
                  )}
                </Button>

                {/* No direct login button needed */}

                <Box sx={{ textAlign: 'center', mt: 2 }}>
                  <Typography variant="body2" color="text.secondary">
                    New User?{' '}
                    <Link
                      component={RouterLink}
                      to="/register"
                      sx={{ fontWeight: 600, color: '#3f51b5' }}
                    >
                      Sign Up
                    </Link>
                  </Typography>

                  <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mt: 1 }}>
                    <Box component="span" sx={{ fontStyle: 'italic' }}>
                      Your email domain will automatically direct you to the correct tenant.
                    </Box>
                  </Typography>

                  <Typography variant="body2" sx={{ mt: 2, color: 'text.secondary' }}>
                    Having trouble logging in? Try our <Link component={RouterLink} to="/direct-login" sx={{ fontWeight: 600, color: '#3f51b5' }}>Direct Login</Link> instead.
                  </Typography>

                  {/* No server status indicator needed */}
                </Box>
              </Box>
            </Box>
          </Paper>
        </Grid>
      </Container>

      {/* Footer - Intentionally left empty */}
    </Box>
  );
};

export default Login;
