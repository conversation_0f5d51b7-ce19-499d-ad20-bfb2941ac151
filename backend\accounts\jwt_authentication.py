"""
JWT Authentication for Django REST Framework

This module provides a JWT authentication class for Django REST Framework.
"""

import logging
from rest_framework import authentication
from rest_framework import exceptions
from django.contrib.auth import get_user_model
from django.db import connection
from centers.models import Client
from .jwt_utils import (
    validate_jwt_token,
    extract_token_from_request,
    get_schema_from_request
)

# Configure logging
logger = logging.getLogger(__name__)

User = get_user_model()

class JWTAuthentication(authentication.BaseAuthentication):
    """
    JWT authentication for Django REST Framework.
    """

    def authenticate(self, request):
        """
        Authenticate the request and return a two-tuple of (user, token).
        """
        # Extract token from request using utility function
        token = extract_token_from_request(request)
        if not token:
            return None

        # Validate token
        payload = validate_jwt_token(token)
        if not payload:
            return None

        # Get user from token
        user = self._get_user_from_payload(payload)
        if not user:
            return None

        # Set tenant context if not already set
        if not hasattr(request, 'tenant') or not request.tenant:
            schema_name = payload.get('schema')
            if schema_name:
                self._set_tenant_context(request, schema_name)

        return (user, token)

    def authenticate_header(self, request):
        """
        Return a string to be used as the value of the `WWW-Authenticate`
        header in a `401 Unauthenticated` response.
        """
        return 'Bearer realm="api"'

    # Removed _get_token method
    # Now using extract_token_from_request from jwt_utils.py

    def _get_user_from_payload(self, payload):
        """
        Get user from JWT payload.
        """
        try:
            user_id = payload.get('sub')
            if not user_id:
                logger.warning("No user ID in token payload")
                return None

            # Get schema from payload
            schema_name = payload.get('schema')
            if not schema_name:
                logger.warning("No schema in token payload")
                return None

            # Get tenant
            try:
                tenant = Client.objects.get(schema_name=schema_name)
            except Client.DoesNotExist:
                logger.warning(f"Tenant with schema '{schema_name}' not found")
                return None

            # Switch to tenant schema
            connection.set_tenant(tenant)

            # Get user from tenant schema
            try:
                user = User.objects.get(id=user_id)
                return user
            except User.DoesNotExist:
                logger.warning(f"User with ID {user_id} not found in schema '{schema_name}'")
                return None
            finally:
                # Switch back to public schema
                connection.set_schema_to_public()
        except Exception as e:
            logger.error(f"Error getting user from token payload: {str(e)}")
            return None

    def _set_tenant_context(self, request, schema_name):
        """
        Set tenant context for request.
        """
        try:
            # Get tenant by schema name
            tenant = Client.objects.get(schema_name=schema_name)

            # Set tenant for request
            request.tenant = tenant

            # Set schema for database connection
            connection.set_tenant(tenant)

            # Store schema name in request for later use
            request.schema_name = schema_name

            logger.debug(f"Set tenant context to '{schema_name}'")
            return True
        except Client.DoesNotExist:
            logger.warning(f"Tenant with schema '{schema_name}' not found")
            return False
        except Exception as e:
            logger.error(f"Error setting tenant context for schema '{schema_name}': {str(e)}")
            return False
