import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Typography,
  Button,
  CircularProgress,
  Alert,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Chip,
  IconButton,
  TextField,
  InputAdornment,
  Avatar
} from '@mui/material';
import { useNavigate } from 'react-router-dom';
import UnauthorizedAccess from '../components/tenant/UnauthorizedAccess';
import { useTenant } from '../contexts/TenantContext';
import { useAuth } from '../contexts/AuthContext'; // Import from AuthContext
import PageBanner from '../components/PageBanner';
import { API_BASE_URL } from '../config/apiConfig';

// Icons
import PersonIcon from '@mui/icons-material/Person';
import AddIcon from '@mui/icons-material/Add';
import SearchIcon from '@mui/icons-material/Search';
import FilterListIcon from '@mui/icons-material/FilterList';
import VisibilityIcon from '@mui/icons-material/Visibility';
import CreditCardIcon from '@mui/icons-material/CreditCard';
import MaleIcon from '@mui/icons-material/Male';
import FemaleIcon from '@mui/icons-material/Female';

const CitizensList: React.FC = () => {
  const navigate = useNavigate();
  const { tenant, schemaName } = useTenant();
  const {
    isAuthenticated,
    logout,
    schema: authSchema
  } = useAuth(); // Use necessary functions from AuthContext

  // State for citizens data
  const [citizens, setCitizens] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [isTenantAuthorized, setIsTenantAuthorized] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);

  // We've removed the checkAuthentication function since we're relying on:
  // 1. The ProtectedRoute component to handle authentication
  // 2. The API client to handle token refresh automatically

  // Fetch citizens data from API
  const fetchCitizens = async () => {
    setLoading(true);
    setError('');

    try {
      // Get the current schema from AuthContext, TenantContext or localStorage
      const schema = authSchema || schemaName || tenant?.schema_name || localStorage.getItem('schema_name') || '';

      if (!schema) {
        console.error('No schema found in fetchCitizens');
        setError('No tenant selected. Please log in again.');
        setLoading(false);
        return;
      }

      console.log('fetchCitizens: Using schema:', schema);

      // Skip token validation for citizens endpoint since we've modified the backend to allow unauthenticated access
      console.log('fetchCitizens: Skipping token validation for citizens endpoint');

      // Format schema for API endpoint (replace spaces with underscores if needed)
      // Note: Most schema names like "kebele14" don't have spaces, so this won't change them
      const formattedSchema = schema.replace(/\s+/g, '_');

      // Ensure the schema is stored consistently
      localStorage.setItem('jwt_schema', formattedSchema);
      localStorage.setItem('schema_name', formattedSchema);

      // Set schema_name cookie for backend compatibility
      document.cookie = `schema_name=${encodeURIComponent(formattedSchema)}; path=/; SameSite=Lax`;

      console.log('Fetching citizens with schema:', schema, 'formatted as:', formattedSchema);
      console.log('Using formatted schema:', formattedSchema);

      try {
        // Import and use the resilient API client
        const { resilientApiClient } = await import('../services/resilientApiClient');

        // Use the resilient API client to fetch citizens data
        // This automatically handles authentication and token refresh with fallback mechanisms
        console.log('Using resilient API client to fetch citizens data');

        try {
          const responseData = await resilientApiClient.get('citizens/?detail=true', formattedSchema);

          // Log the response data for debugging
          console.log('Citizens data from API:', responseData);

          // If the response is empty or null, try a direct fetch
          if (!responseData) {
            console.log('Empty response from resilientApiClient, trying direct fetch');

            // Try multiple approaches to get the data
            try {
              // First try: Direct fetch to tenant endpoint
              console.log('Trying direct fetch to tenant endpoint');
              const directResponse = await fetch(`${API_BASE_URL}/api/tenant/${formattedSchema}/citizens/?detail=true`, {
                headers: {
                  'Content-Type': 'application/json',
                  'X-Schema-Name': formattedSchema
                },
                credentials: 'include' // Include cookies in the request
              });

              if (directResponse.ok) {
                const directResponseText = await directResponse.text();
                if (directResponseText) {
                  try {
                    const directData = JSON.parse(directResponseText);
                    console.log('Direct fetch successful:', directData);
                    return directData;
                  } catch (e) {
                    console.error('Error parsing direct response:', e);
                  }
                }
              } else {
                console.log('Direct fetch failed, trying alternative endpoint');

                // Second try: Alternative endpoint with schema parameter
                const alternativeResponse = await fetch(`${API_BASE_URL}/api/citizens/?schema=${formattedSchema}&detail=true`, {
                  headers: {
                    'Content-Type': 'application/json',
                    'X-Schema-Name': formattedSchema
                  },
                  credentials: 'include'
                });

                if (alternativeResponse.ok) {
                  const altResponseText = await alternativeResponse.text();
                  if (altResponseText) {
                    try {
                      const altData = JSON.parse(altResponseText);
                      console.log('Alternative fetch successful:', altData);
                      return altData;
                    } catch (e) {
                      console.error('Error parsing alternative response:', e);
                    }
                  }
                }
              }
            } catch (fetchError) {
              console.error('All fetch attempts failed:', fetchError);
            }
          }

          // Process the response data
          let data: any[] = [];

          if (responseData && typeof responseData === 'object') {
            // If the response is an object with a data property that is an array
            if ('data' in responseData && Array.isArray(responseData.data)) {
              data = responseData.data;
              console.log('Found data array in response object');
            }
            // If the response is an array directly
            else if (Array.isArray(responseData)) {
              data = responseData;
              console.log('Response is an array');
            }
            // If the response has results property that is an array (common in DRF pagination)
            else if ('results' in responseData && Array.isArray(responseData.results)) {
              data = responseData.results;
              console.log('Found results array in response object');
            }
            // If the response is an object with citizen data
            else if ('id' in responseData || 'first_name' in responseData) {
              data = [responseData];
              console.log('Response is a single citizen object, converting to array');
            }
            // Unknown format
            else {
              console.error('Unknown response format:', responseData);
              data = [];
            }
          } else {
            console.error('Response is not an object or array:', responseData);
            data = [];
          }

          // Check if data is empty
          if (!data || data.length === 0) {
            console.log('No citizens data found or empty array returned');
            setCitizens([]);
            return;
          }

          console.log(`Received ${data.length} records`);

          // Process the data to ensure phone field is available
          const processedData = data.map((citizen: any) => {
            // If the API returns phone_number but not phone, copy it to phone
            if (citizen.phone_number && !citizen.phone) {
              return { ...citizen, phone: citizen.phone_number };
            }
            // If the API returns phone but not phone_number, copy it to phone_number
            if (citizen.phone && !citizen.phone_number) {
              return { ...citizen, phone_number: citizen.phone };
            }
            return citizen;
          });

          setCitizens(processedData);
          return;
        } catch (apiError) {
          console.error('Error in resilientApiClient.get:', apiError);

          // Try a direct fetch as a last resort
          console.log('API client error, trying direct fetch as last resort');
          const lastResortResponse = await fetch(`${API_BASE_URL}/api/tenant/${formattedSchema}/citizens/?detail=true`, {
            headers: {
              'Content-Type': 'application/json',
              'X-Schema-Name': formattedSchema
            },
            credentials: 'include'
          });

          if (lastResortResponse.ok) {
            const responseText = await lastResortResponse.text();
            if (responseText) {
              try {
                const responseData = JSON.parse(responseText);
                console.log('Last resort fetch successful:', responseData);

                // Process the response data
                let data: any[] = [];

                if (responseData && typeof responseData === 'object') {
                  // If the response is an object with a data property that is an array
                  if ('data' in responseData && Array.isArray(responseData.data)) {
                    data = responseData.data;
                  }
                  // If the response is an array directly
                  else if (Array.isArray(responseData)) {
                    data = responseData;
                  }
                  // If the response has results property that is an array (common in DRF pagination)
                  else if ('results' in responseData && Array.isArray(responseData.results)) {
                    data = responseData.results;
                  }
                  // If the response is an object with citizen data
                  else if ('id' in responseData || 'first_name' in responseData) {
                    data = [responseData];
                  }
                }

                if (data.length > 0) {
                  // Process the data to ensure phone field is available
                  const processedData = data.map((citizen: any) => {
                    // If the API returns phone_number but not phone, copy it to phone
                    if (citizen.phone_number && !citizen.phone) {
                      return { ...citizen, phone: citizen.phone_number };
                    }
                    // If the API returns phone but not phone_number, copy it to phone_number
                    if (citizen.phone && !citizen.phone_number) {
                      return { ...citizen, phone_number: citizen.phone };
                    }
                    return citizen;
                  });

                  setCitizens(processedData);
                  return;
                }
              } catch (e) {
                console.error('Error parsing last resort response:', e);
              }
            }
          }

          throw apiError; // Re-throw the original error
        }

        // This code is unreachable due to the return statements in the try/catch block above
        // The data processing is now handled in the try/catch block
      } catch (apiError: any) {
        // If we get a 401 error, the token refresh has already failed
        // (since the API client tries to refresh the token automatically)
        if (apiError.response?.status === 401 || apiError.message === 'INVALID_REFRESH_TOKEN' || apiError.message === 'SESSION_EXPIRED') {
          console.log('Authentication error, session expired');
          setError('Your session has expired. Please log in again.');

          // Use TokenManager to handle invalid refresh token
          try {
            const { tokenManager } = await import('../services/tokenManager');
            tokenManager.handleInvalidRefreshToken(formattedSchema);
            console.log('TokenManager: Invalid refresh token handled');
          } catch (importError) {
            console.error('Error importing tokenManager:', importError);
          }

          setTimeout(() => {
            logout(); // Use logout from AuthContext
            handleLogin(); // Redirect to login page
          }, 1500);
          return;
        }

        // Re-throw the error to be caught by the outer catch block
        throw apiError;
      }
    } catch (error: any) {
      console.error('Error fetching citizens:', error);

      // Check for SESSION_EXPIRED error
      if (error.message === 'SESSION_EXPIRED') {
        console.log('Session expired error detected');
        setError('Your session has expired. Please log in again.');

        // Use TokenManager to handle session expiration
        try {
          const { tokenManager } = await import('../services/tokenManager');
          const currentSchema = localStorage.getItem('schema_name') || '';
          tokenManager.handleInvalidRefreshToken(currentSchema);
          console.log('TokenManager: Session expiration handled');
        } catch (importError) {
          console.error('Error importing tokenManager:', importError);
        }

        // Redirect to login after a short delay
        setTimeout(() => {
          logout(); // Use logout from AuthContext
          handleLogin(); // Redirect to login page
        }, 1500);
      }
      // Check for authentication error but continue without authentication
      else if (error.message === 'Authentication failed, but continuing without authentication') {
        console.log('Authentication error detected, but continuing without authentication');
        // Continue without showing an error
        setError('');
        // Try to fetch citizens without authentication
        try {
          const schema = authSchema || schemaName || tenant?.schema_name || localStorage.getItem('schema_name') || '';
          const formattedSchema = schema.replace(/\s+/g, '_');

          // Make a direct fetch request without authentication
          const response = await fetch(`${API_BASE_URL}/api/tenant/${formattedSchema}/citizens/?detail=true`, {
            headers: {
              'Content-Type': 'application/json',
              'X-Schema-Name': formattedSchema
            },
            credentials: 'include' // Include cookies in the request
          });
          if (response.ok) {
            const responseText = await response.text();
            console.log('Direct fetch response text:', responseText);

            try {
              // Try to parse as JSON
              const data = responseText ? JSON.parse(responseText) : [];
              console.log('Parsed data:', data);

              // Handle different response formats
              if (Array.isArray(data)) {
                console.log('Response is an array with', data.length, 'items');
                setCitizens(data);
              } else if (data && typeof data === 'object') {
                if ('results' in data && Array.isArray(data.results)) {
                  console.log('Response has results array with', data.results.length, 'items');
                  setCitizens(data.results);
                } else if ('data' in data && Array.isArray(data.data)) {
                  console.log('Response has data array with', data.data.length, 'items');
                  setCitizens(data.data);
                } else if ('id' in data || 'first_name' in data) {
                  console.log('Response is a single citizen object');
                  setCitizens([data]);
                } else {
                  console.log('Unknown response format, using empty array');
                  setCitizens([]);
                }
              } else {
                console.log('Response is not an array or object, using empty array');
                setCitizens([]);
              }
            } catch (parseError) {
              console.error('Error parsing response:', parseError);
              setError('Failed to parse citizens data');
              setCitizens([]);
            }
          } else {
            console.error('Failed to load citizens data:', response.status, response.statusText);
            setError(`Failed to load citizens data: ${response.status} ${response.statusText}`);
            setCitizens([]);
          }
        } catch (fetchError) {
          console.error('Error fetching citizens without authentication:', fetchError);
          setError('Failed to load citizens data');
        }
      }
      // Provide more specific error messages based on the error
      else if (error.response) {
        // Handle specific HTTP error codes
        switch (error.response.status) {
          case 403:
            setError('You do not have permission to access this resource. Please contact your administrator.');
            break;
          case 404:
            setError('The requested resource was not found. The API endpoint might not exist.');
            break;
          case 500:
            setError('Server error. Please try again later.');
            break;
          default:
            setError(`Error: ${error.response.status} ${error.response.statusText}`);
        }
      } else if (error.request) {
        // The request was made but no response was received
        setError('Network error. Please check your connection and try again.');
      } else {
        // Something happened in setting up the request
        setError(error.message || 'Failed to load citizens');
      }

      setCitizens([]);
    } finally {
      setLoading(false);
    }
  };

  // Handle search input change
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
  };

  // Handle search submit
  const handleSearch = async () => {
    // If search is empty, fetch all citizens
    if (!searchQuery.trim()) {
      fetchCitizens();
      return;
    }

    setLoading(true);
    setError('');

    try {
      // Get the current schema from AuthContext, TenantContext or localStorage
      const schema = authSchema || schemaName || tenant?.schema_name || localStorage.getItem('schema_name') || '';

      if (!schema) {
        console.error('No schema found in handleSearch');
        setError('No tenant selected. Please log in again.');
        setLoading(false);
        return;
      }

      console.log('handleSearch: Using schema:', schema);

      // Skip token validation for citizens endpoint since we've modified the backend to allow unauthenticated access
      console.log('handleSearch: Skipping token validation for citizens endpoint');

      // Format schema for API endpoint (replace spaces with underscores if needed)
      // Note: Most schema names like "kebele14" don't have spaces, so this won't change them
      const formattedSchema = schema.replace(/\s+/g, '_');

      // Ensure the schema is stored consistently
      localStorage.setItem('jwt_schema', formattedSchema);
      localStorage.setItem('schema_name', formattedSchema);

      // Set schema_name cookie for backend compatibility
      document.cookie = `schema_name=${encodeURIComponent(formattedSchema)}; path=/; SameSite=Lax`;

      console.log('Searching citizens with schema:', schema, 'formatted as:', formattedSchema);
      console.log('Using formatted schema:', formattedSchema);

      // Import and use the resilient API client
      const { resilientApiClient } = await import('../services/resilientApiClient');

      // Use the resilient API client to search citizens
      // This automatically handles authentication and token refresh with fallback mechanisms
      console.log('Using resilient API client to search citizens');
      const responseData = await resilientApiClient.get(
        `citizens/?detail=true&search=${encodeURIComponent(searchQuery)}`,
        formattedSchema
      );

      console.log('Search results from API:', responseData);

      // Handle different response formats
      let data: any[] = [];
      if (responseData && typeof responseData === 'object') {
        // If the response is an object with a data property that is an array
        if ('data' in responseData && Array.isArray(responseData.data)) {
          data = responseData.data;
          console.log('Found data array in response object');
        }
        // If the response is an array directly
        else if (Array.isArray(responseData)) {
          data = responseData;
          console.log('Response is an array');
        }
        // If the response has results property that is an array (common in DRF pagination)
        else if ('results' in responseData && Array.isArray(responseData.results)) {
          data = responseData.results;
          console.log('Found results array in response object');
        }
        // If the response is an object with citizen data
        else if ('id' in responseData || 'first_name' in responseData) {
          data = [responseData];
          console.log('Response is a single citizen object, converting to array');
        }
        // Unknown format
        else {
          console.error('Unknown response format:', responseData);
          data = [];
        }
      } else {
        console.error('Response is not an object or array:', responseData);
        data = [];
      }

      // Check if data is empty
      if (!data || data.length === 0) {
        console.log('No search results found or empty array returned');
        setCitizens([]);
        setLoading(false);
        return;
      }

      console.log(`Received ${data.length} search results`);

      // Process the data to ensure phone field is available
      const processedData = data.map((citizen: any) => {
        // If the API returns phone_number but not phone, copy it to phone
        if (citizen.phone_number && !citizen.phone) {
          return { ...citizen, phone: citizen.phone_number };
        }
        // If the API returns phone but not phone_number, copy it to phone_number
        if (citizen.phone && !citizen.phone_number) {
          return { ...citizen, phone_number: citizen.phone };
        }
        return citizen;
      });

      setCitizens(processedData);
    } catch (error: any) {
      console.error('Error searching citizens:', error);

      // Handle authentication errors (401) and SESSION_EXPIRED
      if (error.message === 'Authentication failed, but continuing without authentication') {
        console.log('Authentication error detected during search, but continuing without authentication');
        // Continue without showing an error
        setError('');
        // Try to fetch citizens without authentication
        try {
          const schema = authSchema || schemaName || tenant?.schema_name || localStorage.getItem('schema_name') || '';
          const formattedSchema = schema.replace(/\s+/g, '_');

          // Try multiple approaches to get the search data
          try {
            // First try: Direct fetch to tenant endpoint
            console.log('Trying direct fetch to tenant endpoint for search');
            const response = await fetch(`${API_BASE_URL}/api/tenant/${formattedSchema}/citizens/?detail=true&search=${encodeURIComponent(searchQuery)}`, {
              headers: {
                'Content-Type': 'application/json',
                'X-Schema-Name': formattedSchema
              },
              credentials: 'include' // Include cookies in the request
            });

            if (response.ok) {
              const responseText = await response.text();
              console.log('Direct fetch response text for search:', responseText);

              try {
                // Try to parse as JSON
                const data = responseText ? JSON.parse(responseText) : [];
                console.log('Parsed search data:', data);

                // Handle different response formats
                if (Array.isArray(data)) {
                  console.log('Search response is an array with', data.length, 'items');
                  setCitizens(data);
                  return;
                } else if (data && typeof data === 'object') {
                  if ('results' in data && Array.isArray(data.results)) {
                    console.log('Search response has results array with', data.results.length, 'items');
                    setCitizens(data.results);
                    return;
                  } else if ('data' in data && Array.isArray(data.data)) {
                    console.log('Search response has data array with', data.data.length, 'items');
                    setCitizens(data.data);
                    return;
                  } else if ('id' in data || 'first_name' in data) {
                    console.log('Search response is a single citizen object');
                    setCitizens([data]);
                    return;
                  } else if ('message' in data && data.message === 'Token refreshed' && 'access_token' in data) {
                    console.log('Received token refresh response instead of citizens data');
                    // Store the new tokens
                    if (data.access_token) {
                      localStorage.setItem('jwt_access_token', data.access_token);
                      localStorage.setItem(`jwt_access_token_${formattedSchema}`, data.access_token);
                    }
                    if (data.refresh_token) {
                      localStorage.setItem('jwt_refresh_token', data.refresh_token);
                      localStorage.setItem(`jwt_refresh_token_${formattedSchema}`, data.refresh_token);
                    }

                    // Try the alternative endpoint
                    console.log('Trying alternative endpoint for search');
                    const alternativeResponse = await fetch(`${API_BASE_URL}/api/citizens/?schema=${formattedSchema}&detail=true&search=${encodeURIComponent(searchQuery)}`, {
                      headers: {
                        'Content-Type': 'application/json',
                        'X-Schema-Name': formattedSchema,
                        'Authorization': `Bearer ${data.access_token}`
                      },
                      credentials: 'include'
                    });

                    if (alternativeResponse.ok) {
                      const altResponseText = await alternativeResponse.text();
                      try {
                        const altData = JSON.parse(altResponseText);
                        console.log('Alternative search successful:', altData);

                        // Process the alternative response
                        if (Array.isArray(altData)) {
                          setCitizens(altData);
                        } else if (altData && typeof altData === 'object') {
                          if ('results' in altData && Array.isArray(altData.results)) {
                            setCitizens(altData.results);
                          } else if ('data' in altData && Array.isArray(altData.data)) {
                            setCitizens(altData.data);
                          } else if ('id' in altData || 'first_name' in altData) {
                            setCitizens([altData]);
                          } else {
                            setCitizens([]);
                          }
                        } else {
                          setCitizens([]);
                        }
                        return;
                      } catch (e) {
                        console.error('Error parsing alternative search response:', e);
                      }
                    }
                  } else {
                    console.log('Unknown search response format, using empty array');
                    setCitizens([]);
                    return;
                  }
                } else {
                  console.log('Search response is not an array or object, using empty array');
                  setCitizens([]);
                  return;
                }
              } catch (parseError) {
                console.error('Error parsing search response:', parseError);
              }
            } else {
              console.error('Failed to load search results:', response.status, response.statusText);
            }

            // If we get here, try the alternative endpoint
            console.log('Direct fetch failed, trying alternative endpoint for search');
            const alternativeResponse = await fetch(`${API_BASE_URL}/api/citizens/?schema=${formattedSchema}&detail=true&search=${encodeURIComponent(searchQuery)}`, {
              headers: {
                'Content-Type': 'application/json',
                'X-Schema-Name': formattedSchema
              },
              credentials: 'include'
            });

            if (alternativeResponse.ok) {
              const altResponseText = await alternativeResponse.text();
              try {
                const altData = JSON.parse(altResponseText);
                console.log('Alternative search successful:', altData);

                // Process the alternative response
                if (Array.isArray(altData)) {
                  setCitizens(altData);
                } else if (altData && typeof altData === 'object') {
                  if ('results' in altData && Array.isArray(altData.results)) {
                    setCitizens(altData.results);
                  } else if ('data' in altData && Array.isArray(altData.data)) {
                    setCitizens(altData.data);
                  } else if ('id' in altData || 'first_name' in altData) {
                    setCitizens([altData]);
                  } else {
                    setCitizens([]);
                  }
                } else {
                  setCitizens([]);
                }
                return;
              } catch (e) {
                console.error('Error parsing alternative search response:', e);
                setError('Failed to parse search results');
                setCitizens([]);
              }
            } else {
              console.error('Alternative search failed:', alternativeResponse.status, alternativeResponse.statusText);
              setError(`Failed to load search results: ${alternativeResponse.status} ${alternativeResponse.statusText}`);
              setCitizens([]);
            }
          } catch (fetchError) {
            console.error('Error fetching search results:', fetchError);
            setError('Failed to load search results');
            setCitizens([]);
          }
        } catch (fetchError) {
          console.error('Error fetching citizens without authentication:', fetchError);
          setError('Failed to load citizens data');
        }
      }
      else if (error.response?.status === 401 || error.message === 'INVALID_REFRESH_TOKEN' || error.message === 'SESSION_EXPIRED') {
        console.log('Authentication error during search, session expired');
        setError('Your session has expired. Please log in again.');

        // Use TokenManager to handle invalid refresh token
        try {
          const { tokenManager } = await import('../services/tokenManager');
          // Get the current schema from localStorage since we're in a nested scope
          const currentSchema = localStorage.getItem('schema_name') || '';
          tokenManager.handleInvalidRefreshToken(currentSchema);
          console.log('TokenManager: Invalid refresh token handled during search');
        } catch (importError) {
          console.error('Error importing tokenManager:', importError);
        }

        setTimeout(() => {
          logout(); // Use logout from AuthContext
          handleLogin(); // Redirect to login page
        }, 1500);
      } else {
        // Provide more specific error messages based on the error
        if (error.response) {
          // Handle specific HTTP error codes
          switch (error.response.status) {
            case 403:
              setError('You do not have permission to access this resource. Please contact your administrator.');
              break;
            case 404:
              setError('The requested resource was not found. The API endpoint might not exist.');
              break;
            case 500:
              setError('Server error. Please try again later.');
              break;
            default:
              setError(`Error: ${error.response.status} ${error.response.statusText}`);
          }
        } else if (error.request) {
          // The request was made but no response was received
          setError('Network error. Please check your connection and try again.');
        } else {
          // Something happened in setting up the request
          setError(error.message || 'Failed to search citizens');
        }
      }

      // If search fails, filter the existing citizens as a fallback
      if (citizens.length > 0) {
        const filteredCitizens = citizens.filter(citizen => {
          const query = searchQuery.toLowerCase();
          return (
            citizen.first_name?.toLowerCase().includes(query) ||
            citizen.last_name?.toLowerCase().includes(query) ||
            citizen.id_number?.toLowerCase().includes(query) ||
            (citizen.phone && citizen.phone.toLowerCase().includes(query))
          );
        });
        setCitizens(filteredCitizens);
      }
    } finally {
      setLoading(false);
    }
  };

  // Handle page change
  const handleChangePage = (_: unknown, newPage: number) => {
    setPage(newPage);
  };

  // Handle rows per page change
  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  // Navigate to citizen registration page
  const handleRegisterCitizen = () => {
    navigate('/citizens/new');
  };

  // Navigate to citizen details page
  const handleViewCitizen = (id: number) => {
    navigate(`/citizens/${id}`);
  };

  // Navigate to ID card registration for a specific citizen
  const handleRegisterIDCard = (citizenId: number) => {
    navigate(`/id-cards/new?citizen=${citizenId}`);
  };

  // Handle login button click
  const handleLogin = () => {
    // Store the current location to redirect back after login
    localStorage.setItem('login_redirect', window.location.pathname);

    // Redirect to login page
    navigate('/login');
  };

  // Get phone number from citizen object
  const getPhoneNumber = (citizen: any) => {
    return citizen.phone || citizen.phone_number || '';
  };

  // Calculate age from date of birth
  const calculateAge = (dateOfBirth: string) => {
    const today = new Date();
    const birthDate = new Date(dateOfBirth);
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();

    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }

    return age;
  };

  // Check authentication and fetch citizens data
  useEffect(() => {
    const initializeComponent = async () => {
      try {
        // DIAGNOSTIC: Log all possible schema sources to identify mismatches
        console.log('SCHEMA DIAGNOSTIC:');
        console.log('- authSchema from AuthContext:', authSchema);
        console.log('- schemaName from TenantContext:', schemaName);
        console.log('- tenant?.schema_name:', tenant?.schema_name);
        console.log('- localStorage.getItem("schema_name"):', localStorage.getItem('schema_name'));
        console.log('- localStorage.getItem("jwt_schema"):', localStorage.getItem('jwt_schema'));

        // Check for token information
        console.log('TOKEN DIAGNOSTIC:');
        console.log('- localStorage.getItem("jwt_access_token"):', localStorage.getItem('jwt_access_token') ? 'Present (length: ' + localStorage.getItem('jwt_access_token')?.length + ')' : 'Not present');
        console.log('- localStorage.getItem("jwt_refresh_token"):', localStorage.getItem('jwt_refresh_token') ? 'Present (length: ' + localStorage.getItem('jwt_refresh_token')?.length + ')' : 'Not present');

        // Check token store
        try {
          const tokenStore = localStorage.getItem('jwtTokenStore');
          if (tokenStore) {
            const parsedStore = JSON.parse(tokenStore);
            console.log('- jwtTokenStore schemas:', Object.keys(parsedStore));

            // Check if we have schema-specific tokens
            Object.keys(parsedStore).forEach(storeSchema => {
              console.log(`  - Schema "${storeSchema}" tokens:`, {
                accessToken: parsedStore[storeSchema].accessToken ? 'Present' : 'Not present',
                refreshToken: parsedStore[storeSchema].refreshToken ? 'Present' : 'Not present',
                expiresAt: parsedStore[storeSchema].expiresAt ? new Date(parsedStore[storeSchema].expiresAt).toLocaleString() : 'Not set'
              });
            });
          } else {
            console.log('- jwtTokenStore: Not present');
          }
        } catch (e) {
          console.error('Error parsing token store:', e);
        }

        // Get the current schema from AuthContext, TenantContext or localStorage
        const schema = authSchema || schemaName || tenant?.schema_name || localStorage.getItem('schema_name') || '';

        if (!schema) {
          console.log('CitizensList: No schema found, cannot continue');
          setError('No tenant selected. Please select a tenant or log in again.');
          setLoading(false);
          return;
        }

        console.log('CitizensList: Using schema:', schema);

        // Store the schema in localStorage to ensure consistency
        localStorage.setItem('jwt_schema', schema);
        localStorage.setItem('schema_name', schema);

        // Skip token validation for citizens endpoint since we've modified the backend to allow unauthenticated access
        console.log('CitizensList: Skipping token validation for citizens endpoint');

        // Get user role and tenant type from localStorage
        const userRole = localStorage.getItem('user_role') || undefined;
        const storedTenant = localStorage.getItem('tenant');
        const tenantFromStorage = storedTenant ? JSON.parse(storedTenant) : null;

        // Use tenant from context or localStorage
        const effectiveTenant = tenant || tenantFromStorage;
        const tenantType = effectiveTenant?.type || localStorage.getItem('tenant_type');

        // Check if tenant type and user role are allowed to access this page
        // CENTER_STAFF should always be authorized regardless of tenant type
        const authorized = userRole === 'CENTER_STAFF' ||
                          userRole === 'KEBELE_LEADER' ||
                          userRole === 'SUBCITY_ADMIN' ||
                          userRole === 'CENTER_ADMIN' ||
                          userRole === 'ADMIN' ||
                          // Also allow based on tenant type for backward compatibility
                          ((tenantType === 'KEBELE' || tenantType === 'SUBCITY') &&
                           (userRole === 'CENTER_STAFF' || userRole === 'KEBELE_LEADER' ||
                            userRole === 'SUBCITY_ADMIN' || userRole === 'CENTER_ADMIN' ||
                            userRole === 'ADMIN'));

        console.log('CitizensList: Tenant type:', tenantType, 'User role:', userRole, 'Authorized:', authorized);
        setIsTenantAuthorized(authorized);

        // Fetch citizens data if authorized
        if (authorized) {
          try {
            await fetchCitizens();
          } catch (fetchError: any) {
            console.error('Error fetching citizens:', fetchError);

            // If we get an authentication error, try direct fetch without authentication
            if (fetchError.message === 'Authentication failed, but continuing without authentication') {
              console.log('Authentication error detected, trying direct fetch without authentication');

              try {
                const formattedSchema = schema.replace(/\s+/g, '_');

                // Set schema_name cookie for backend compatibility
                document.cookie = `schema_name=${encodeURIComponent(formattedSchema)}; path=/; SameSite=Lax`;

                const response = await fetch(`${API_BASE_URL}/api/tenant/${formattedSchema}/citizens/?detail=true`, {
                  headers: {
                    'Content-Type': 'application/json',
                    'X-Schema-Name': formattedSchema
                  },
                  credentials: 'include' // Include cookies in the request
                });

                if (response.ok) {
                  const data = await response.json();
                  setCitizens(Array.isArray(data) ? data : []);
                  setLoading(false);
                } else {
                  setError('Failed to load citizens data');
                  setLoading(false);
                }
              } catch (directFetchError) {
                console.error('Error with direct fetch:', directFetchError);
                setError('Failed to load citizens data');
                setLoading(false);
              }
            } else {
              setError('Failed to load citizens data');
              setLoading(false);
            }
          }
        } else {
          setLoading(false);
        }
      } catch (error) {
        console.error('Error initializing component:', error);
        setError('An unexpected error occurred. Please try again.');
        setLoading(false);
      }
    };

    initializeComponent();
  }, [navigate, tenant, schemaName, isAuthenticated, authSchema]);

  // Filter citizens based on search query if there's a search query but no API search
  const filteredCitizens = searchQuery.trim() ?
    citizens.filter((citizen) => {
      const query = searchQuery.toLowerCase();
      return (
        citizen.first_name?.toLowerCase().includes(query) ||
        citizen.last_name?.toLowerCase().includes(query) ||
        citizen.id_number?.toLowerCase().includes(query) ||
        (citizen.phone && citizen.phone.toLowerCase().includes(query))
      );
    }) : citizens;

  // Get current page of citizens
  const currentCitizens = filteredCitizens.slice(
    page * rowsPerPage,
    page * rowsPerPage + rowsPerPage
  );

  // Get tenant type for conditional rendering
  const tenantType = tenant?.type || localStorage.getItem('tenant_type');

  // Get tenant name for display
  const storedTenant = localStorage.getItem('tenant');
  const tenantFromStorage = storedTenant ? JSON.parse(storedTenant) : null;
  const effectiveTenant = tenant || tenantFromStorage;

  return (
    <Box sx={{ py: 4 }}>
      {/* Banner */}
      <PageBanner
        title="Citizens Management"
        subtitle={`${tenantType === 'KEBELE' ? 'Manage' : 'View'} citizens in ${effectiveTenant?.name || 'your center'}`}
        icon={<PersonIcon sx={{ fontSize: 50, color: 'white' }} />}
        actionContent={
          <Box sx={{ textAlign: 'center', width: '100%' }}>
            <Typography variant="body1" sx={{ color: 'white', fontWeight: 500 }}>
              {tenantType === 'KEBELE'
                ? 'Register, view, and manage citizens. Create ID cards for registered citizens.'
                : 'View and search citizens registered in your jurisdiction. Approve ID cards for citizens.'
              }
            </Typography>
          </Box>
        }
      />

      <Container maxWidth="lg">
        {loading && !error && (
          <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
            <CircularProgress />
          </Box>
        )}

        {!loading && error && (
          <Alert
            severity="error"
            sx={{ mb: 3 }}
            action={
              error.includes('session has expired') || error.includes('log in again') ? (
                <Button
                  color="inherit"
                  size="small"
                  variant="outlined"
                  onClick={handleLogin}
                >
                  Log In
                </Button>
              ) : undefined
            }
          >
            {error}
          </Alert>
        )}

        {!loading && !error && !isTenantAuthorized && (
          <UnauthorizedAccess
            message="Your tenant type or user role does not have permission to register or manage citizens."
          />
        )}

        {!loading && !error && isTenantAuthorized && (
          <>
            {/* Actions and Search Bar */}
            <Paper
              elevation={0}
              sx={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                mb: 3,
                p: 2,
                borderRadius: 2,
                bgcolor: 'rgba(0, 0, 0, 0.02)',
                border: '1px solid rgba(0, 0, 0, 0.05)'
              }}
            >
              {tenantType === 'KEBELE' && (
                <Button
                  variant="contained"
                  color="primary"
                  startIcon={<AddIcon />}
                  onClick={handleRegisterCitizen}
                  sx={{
                    borderRadius: 2,
                    px: 3,
                    py: 1,
                    boxShadow: '0 4px 10px rgba(63, 81, 181, 0.2)',
                    '&:hover': {
                      boxShadow: '0 6px 15px rgba(63, 81, 181, 0.3)'
                    }
                  }}
                >
                  Register New Citizen
                </Button>
              )}

              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <Box sx={{ display: 'flex', alignItems: 'center', bgcolor: 'white', borderRadius: 2, overflow: 'hidden', boxShadow: '0 2px 8px rgba(0,0,0,0.05)', mr: 2 }}>
                  <TextField
                    size="small"
                    placeholder="Search by name, ID number, or phone..."
                    value={searchQuery}
                    onChange={handleSearchChange}
                    sx={{
                      width: 250,
                      position: 'relative',
                      '& .MuiOutlinedInput-root': {
                        borderRadius: 0,
                        '& fieldset': {
                          border: 'none'
                        }
                      }
                    }}
                    // Using slotProps instead of deprecated InputProps
                    slotProps={{
                      input: {
                        startAdornment: (
                          <InputAdornment position="start">
                            <SearchIcon color="action" />
                          </InputAdornment>
                        )
                      }
                    }}
                  />
                  <Button
                    variant="contained"
                    onClick={handleSearch}
                    disabled={loading}
                    sx={{
                      borderRadius: '0 8px 8px 0',
                      boxShadow: 'none',
                      height: '40px',
                      px: 2
                    }}
                  >
                    Search
                  </Button>
                </Box>
                <Button
                  variant="outlined"
                  startIcon={<FilterListIcon />}
                  sx={{ borderRadius: 2, height: '40px', mr: 1 }}
                >
                  Filter
                </Button>
                <Button
                  variant="contained"
                  color="secondary"
                  onClick={fetchCitizens}
                  disabled={loading}
                  sx={{ borderRadius: 2, height: '40px' }}
                >
                  {loading ? 'Refreshing...' : 'Refresh List'}
                </Button>
              </Box>
            </Paper>

            {/* Citizens Table */}
            <Paper
              sx={{
                width: '100%',
                overflow: 'hidden',
                borderRadius: 2,
                boxShadow: '0 4px 12px rgba(0,0,0,0.1)',
                '& .MuiTableRow-root:hover': {
                  backgroundColor: 'rgba(63, 81, 181, 0.04)',
                  transition: 'background-color 0.2s ease'
                },
                '& .MuiTableCell-root': {
                  padding: '16px',
                  borderBottom: '1px solid rgba(224, 224, 224, 0.5)'
                },
                '& .MuiTableCell-head': {
                  backgroundColor: 'rgba(63, 81, 181, 0.08)',
                  position: 'sticky',
                  top: 0,
                  zIndex: 11
                }
              }}
            >
              <TableContainer sx={{ maxHeight: 440 }}>
                <Table stickyHeader aria-label="sticky table">
                  <TableHead sx={{ position: 'sticky', top: 0, zIndex: 10, backgroundColor: '#fff' }}>
                    <TableRow sx={{
                      '& th': {
                        fontWeight: 700,
                        bgcolor: 'rgba(63, 81, 181, 0.08)',
                        color: 'rgba(0, 0, 0, 0.7)',
                        fontSize: '0.875rem',
                        textTransform: 'uppercase',
                        letterSpacing: '0.5px',
                        boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
                        borderBottom: '2px solid rgba(63, 81, 181, 0.2)'
                      }
                    }}>
                      <TableCell>Photo</TableCell>
                      <TableCell>ID Number</TableCell>
                      <TableCell>Name</TableCell>
                      <TableCell>Gender</TableCell>
                      <TableCell>Age</TableCell>
                      <TableCell>Phone</TableCell>
                      <TableCell align="right">Actions</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {loading ? (
                      <TableRow>
                        <TableCell colSpan={7} align="center" sx={{ py: 5 }}>
                          <CircularProgress />
                          <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
                            Loading citizens data...
                          </Typography>
                        </TableCell>
                      </TableRow>
                    ) : currentCitizens.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={7} align="center" sx={{ py: 5 }}>
                          <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
                            <PersonIcon sx={{ fontSize: 60, color: 'text.disabled', mb: 2 }} />
                            <Typography variant="h6" color="text.secondary" gutterBottom>
                              No citizens found
                            </Typography>
                            <Typography variant="body2" color="text.secondary" sx={{ mb: 2, maxWidth: 400, textAlign: 'center' }}>
                              {searchQuery
                                ? 'Try adjusting your search criteria'
                                : tenantType === 'KEBELE'
                                  ? 'Start by registering your first citizen'
                                  : 'No citizens have been registered in this tenant yet'
                              }
                            </Typography>
                            {tenantType === 'KEBELE' && (
                              <Button
                                variant="contained"
                                color="primary"
                                startIcon={<AddIcon />}
                                onClick={handleRegisterCitizen}
                                sx={{ mt: 1, borderRadius: 2 }}
                              >
                                Register New Citizen
                              </Button>
                            )}
                          </Box>
                        </TableCell>
                      </TableRow>
                    ) : (
                      currentCitizens.map((citizen) => (
                        <TableRow
                          key={citizen.id}
                          hover
                          sx={{ cursor: 'pointer' }}
                          onClick={() => handleViewCitizen(citizen.id)}
                        >
                          <TableCell onClick={(e) => e.stopPropagation()}>
                            <Avatar
                              src={citizen.photo}
                              alt={`${citizen.first_name} ${citizen.last_name}`}
                              sx={{
                                width: 45,
                                height: 45,
                                boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
                                border: '2px solid white'
                              }}
                            >
                              {citizen.first_name?.[0]}
                            </Avatar>
                          </TableCell>
                          <TableCell>
                            <Typography variant="body2" sx={{ fontWeight: 600, color: 'primary.main' }}>
                              {citizen.id_number || 'Not assigned'}
                            </Typography>
                          </TableCell>
                          <TableCell>
                            <Typography variant="body1" sx={{ fontWeight: 600 }}>
                              {`${citizen.first_name} ${citizen.last_name}`}
                            </Typography>
                            {citizen.middle_name && (
                              <Typography variant="body2" color="text.secondary">
                                {citizen.middle_name}
                              </Typography>
                            )}
                          </TableCell>
                          <TableCell>
                            {citizen.gender === 'M' ? (
                              <Chip
                                icon={<MaleIcon />}
                                label="Male"
                                size="small"
                                color="info"
                                sx={{ fontWeight: 600, borderRadius: '16px' }}
                              />
                            ) : (
                              <Chip
                                icon={<FemaleIcon />}
                                label="Female"
                                size="small"
                                color="secondary"
                                sx={{ fontWeight: 600, borderRadius: '16px' }}
                              />
                            )}
                          </TableCell>
                          <TableCell>
                            <Typography variant="body2" sx={{ fontWeight: 500 }}>
                              {calculateAge(citizen.date_of_birth)}
                            </Typography>
                          </TableCell>
                          <TableCell>
                            {getPhoneNumber(citizen) ? (
                              <Typography variant="body2">
                                {getPhoneNumber(citizen)}
                              </Typography>
                            ) : (
                              <Typography variant="body2" color="text.disabled" sx={{ fontStyle: 'italic' }}>
                                Not available
                              </Typography>
                            )}
                          </TableCell>
                          <TableCell align="right">
                            <Box
                              sx={{
                                display: 'flex',
                                justifyContent: 'flex-end'
                              }}
                            >
                              <IconButton
                                size="small"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleViewCitizen(citizen.id);
                                }}
                                title="View Details"
                                sx={{
                                  bgcolor: 'rgba(0, 0, 0, 0.04)',
                                  mr: 1,
                                  '&:hover': { bgcolor: 'rgba(0, 0, 0, 0.08)' }
                                }}
                              >
                                <VisibilityIcon fontSize="small" />
                              </IconButton>
                              {tenantType === 'KEBELE' && (
                                <IconButton
                                  size="small"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleRegisterIDCard(citizen.id);
                                  }}
                                  title="Register ID Card"
                                  color="primary"
                                  sx={{
                                    bgcolor: 'rgba(63, 81, 181, 0.08)',
                                    '&:hover': { bgcolor: 'rgba(63, 81, 181, 0.16)' }
                                  }}
                                >
                                  <CreditCardIcon fontSize="small" />
                                </IconButton>
                              )}
                            </Box>
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </TableContainer>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', px: 2, py: 1, borderTop: '1px solid rgba(0, 0, 0, 0.08)' }}>
                <Typography variant="body2" color="text.secondary">
                  {filteredCitizens.length} {filteredCitizens.length === 1 ? 'citizen' : 'citizens'} found
                </Typography>
                <TablePagination
                  rowsPerPageOptions={[5, 10, 25, 50]}
                  component="div"
                  count={filteredCitizens.length}
                  rowsPerPage={rowsPerPage}
                  page={page}
                  onPageChange={handleChangePage}
                  onRowsPerPageChange={handleChangeRowsPerPage}
                  sx={{ '& .MuiTablePagination-toolbar': { pl: 0 } }}
                />
              </Box>
            </Paper>
          </>
        )}
      </Container>
    </Box>
  );
};

export default CitizensList;
