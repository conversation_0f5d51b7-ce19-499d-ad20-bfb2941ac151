import os
import django

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'neocamelot.settings')
django.setup()

# Import models and admin
from django.contrib import admin
from citizens.models import Citizen
from idcards.models import IDCard, IDCardTemplate
from centers.models import Center, Client
from django_tenants.utils import tenant_context, schema_context

# Create a custom admin class for Citizen that doesn't require a center
class CustomCitizenAdmin(admin.ModelAdmin):
    list_display = ('registration_number', 'first_name', 'last_name', 'gender', 'date_of_birth', 'is_active', 'created_at')
    list_filter = ('is_active', 'gender', 'nationality')
    search_fields = ('first_name', 'last_name', 'registration_number', 'id_number', 'email', 'phone')
    readonly_fields = ('registration_number', 'id_number', 'created_at', 'updated_at', 'created_by')

    fieldsets = (
        ('Basic Information', {
            'fields': ('center', 'registration_number', 'id_number', 'first_name', 'middle_name', 'last_name',
                      'date_of_birth', 'gender', 'nationality')
        }),
        ('Contact Information', {
            'fields': ('address', 'phone', 'email')
        }),
        ('Additional Information', {
            'fields': ('occupation', 'photo')
        }),
        ('Status', {
            'fields': ('is_active',)
        }),
        ('System Information', {
            'fields': ('created_at', 'updated_at', 'created_by'),
            'classes': ('collapse',)
        }),
    )

    def save_model(self, request, obj, form, change):
        if not change:  # Only set created_by when creating a new object
            obj.created_by = request.user
        super().save_model(request, obj, form, change)

# Create a custom admin class for IDCardTemplate that doesn't require a center
class CustomIDCardTemplateAdmin(admin.ModelAdmin):
    list_display = ('name', 'center', 'is_default', 'created_at')
    list_filter = ('is_default',)
    search_fields = ('name',)
    readonly_fields = ('created_at', 'updated_at')

    fieldsets = (
        (None, {
            'fields': ('name', 'center', 'is_default', 'background_image')
        }),
        ('Layout Configuration', {
            'fields': ('front_layout', 'back_layout'),
            'classes': ('collapse',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

# Create a custom admin class for IDCard that doesn't require a center
class CustomIDCardAdmin(admin.ModelAdmin):
    list_display = ('card_number', 'citizen', 'template', 'issue_date', 'expiry_date', 'status')
    list_filter = ('status', 'issue_date')
    search_fields = ('card_number', 'citizen__first_name', 'citizen__last_name', 'citizen__registration_number')
    readonly_fields = ('card_number', 'created_at', 'updated_at', 'created_by', 'approved_by')

    fieldsets = (
        ('Card Information', {
            'fields': ('citizen', 'template', 'card_number', 'issue_date', 'expiry_date', 'status')
        }),
        ('Card Data', {
            'fields': ('card_data',),
            'classes': ('collapse',)
        }),
        ('System Information', {
            'fields': ('created_at', 'updated_at', 'created_by', 'approved_by'),
            'classes': ('collapse',)
        }),
    )

    def save_model(self, request, obj, form, change):
        if not change:  # Only set created_by when creating a new object
            obj.created_by = request.user
        super().save_model(request, obj, form, change)

# Unregister models if they are already registered
try:
    admin.site.unregister(Citizen)
    print("Unregistered Citizen model")
except admin.sites.NotRegistered:
    print("Citizen model was not registered")

try:
    admin.site.unregister(IDCardTemplate)
    print("Unregistered IDCardTemplate model")
except admin.sites.NotRegistered:
    print("IDCardTemplate model was not registered")

try:
    admin.site.unregister(IDCard)
    print("Unregistered IDCard model")
except admin.sites.NotRegistered:
    print("IDCard model was not registered")

# Register models with custom admin classes
admin.site.register(Citizen, CustomCitizenAdmin)
print("Registered Citizen model with custom admin class")

admin.site.register(IDCardTemplate, CustomIDCardTemplateAdmin)
print("Registered IDCardTemplate model with custom admin class")

admin.site.register(IDCard, CustomIDCardAdmin)
print("Registered IDCard model with custom admin class")

# Create default ID card templates for each tenant
print("\nCreating default ID card templates for each tenant:")
tenants = Client.objects.exclude(schema_name='public')
for tenant in tenants:
    print(f"\nChecking tenant: {tenant.name} (schema: {tenant.schema_name})")
    try:
        with tenant_context(tenant):
            # Get all centers in this tenant
            centers = Center.objects.all()
            print(f"Found {centers.count()} centers in {tenant.schema_name}")
            
            # Create a default template for each center
            for center in centers:
                # Check if the center already has a template
                templates = IDCardTemplate.objects.filter(center=center)
                if templates.exists():
                    print(f"Center '{center.name}' already has {templates.count()} templates")
                else:
                    # Create a default template
                    template = IDCardTemplate.objects.create(
                        name="Default Template",
                        center=center,
                        is_default=True,
                        front_layout={
                            'title': {'x': 10, 'y': 10, 'font_size': 18},
                            'photo': {'x': 20, 'y': 40, 'width': 100, 'height': 120},
                            'name': {'x': 130, 'y': 50, 'font_size': 14},
                            'id_number': {'x': 130, 'y': 70, 'font_size': 12},
                        },
                        back_layout={
                            'address': {'x': 10, 'y': 10, 'font_size': 12},
                            'issue_date': {'x': 10, 'y': 30, 'font_size': 12},
                            'expiry_date': {'x': 10, 'y': 50, 'font_size': 12},
                        }
                    )
                    print(f"Created default template for center '{center.name}'")
    except Exception as e:
        print(f"Error accessing tenant schema: {str(e)}")

print("\nDone!")
