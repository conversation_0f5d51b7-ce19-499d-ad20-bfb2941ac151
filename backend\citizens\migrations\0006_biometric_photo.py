# Generated by Django 5.1.7 on 2025-04-17 15:27

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('citizens', '0005_relationship_child_parent_emergencycontact_spouse'),
    ]

    operations = [
        migrations.CreateModel(
            name='Biometric',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('left_hand_fingerprint', models.BinaryField(blank=True, null=True)),
                ('right_hand_fingerprint', models.BinaryField(blank=True, null=True)),
                ('left_thumb_fingerprint', models.BinaryField(blank=True, null=True)),
                ('right_thumb_fingerprint', models.BinaryField(blank=True, null=True)),
                ('left_eye_iris_scan', models.BinaryField(blank=True, null=True)),
                ('right_eye_iris_scan', models.BinaryField(blank=True, null=True)),
                ('citizen', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='biometric', to='citizens.citizen')),
            ],
            options={
                'verbose_name': 'Biometric',
                'verbose_name_plural': 'Biometrics',
            },
        ),
        migrations.CreateModel(
            name='Photo',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('photo', models.ImageField(upload_to='citizen_photos/')),
                ('upload_date', models.DateTimeField(auto_now_add=True)),
                ('citizen', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='photo_record', to='citizens.citizen')),
            ],
            options={
                'verbose_name': 'Photo',
                'verbose_name_plural': 'Photos',
            },
        ),
    ]
