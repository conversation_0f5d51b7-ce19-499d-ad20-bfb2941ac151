import React, { useState } from 'react';
import { Box, Button, TextField, Typography, Paper, Alert, CircularProgress } from '@mui/material';
import api, { tenantApi } from '../services/api';
import { API_BASE_URL } from '../config/apiConfig';

/**
 * ApiTest component
 *
 * This component provides a simple interface to test API endpoints.
 * It can be used to diagnose API connection issues.
 */
const ApiTest: React.FC = () => {
  const [url, setUrl] = useState<string>('/api/health/');
  const [schema, setSchema] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [result, setResult] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  const handleDirectTest = async () => {
    setIsLoading(true);
    setResult(null);
    setError(null);

    try {
      console.log(`Testing API endpoint: ${url}`);
      const response = await api.get(url);
      console.log('API test result:', response.status, response.data);
      setResult({
        status: response.status,
        data: response.data
      });
    } catch (error) {
      console.error('API test failed:', error);
      setError(`Error: ${error instanceof Error ? error.message : String(error)}`);
    } finally {
      setIsLoading(false);
    }
  };

  const handleTenantTest = async () => {
    if (!schema) {
      setError('Please enter a schema name');
      return;
    }

    setIsLoading(true);
    setResult(null);
    setError(null);

    try {
      // Extract the endpoint part from the URL (remove /api/ prefix)
      const endpoint = url.replace(/^\/api\//, '');
      console.log(`Testing tenant API endpoint: ${endpoint} with schema: ${schema}`);

      const response = await tenantApi.get(schema, endpoint);
      console.log('Tenant API test result:', response);
      setResult({
        status: 200, // tenantApi.get doesn't return status, only data
        data: response
      });
    } catch (error) {
      console.error('Tenant API test failed:', error);
      setError(`Error: ${error instanceof Error ? error.message : String(error)}`);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Paper elevation={3} sx={{ p: 3, mb: 3 }}>
      <Typography variant="h6" gutterBottom>
        API Test Tool
      </Typography>

      <Box sx={{ mb: 2 }}>
        <Typography variant="subtitle1" gutterBottom>
          Testing API at {API_BASE_URL}
        </Typography>

        <TextField
          label="API Endpoint"
          value={url}
          onChange={(e) => setUrl(e.target.value)}
          fullWidth
          margin="normal"
          helperText="Enter the API endpoint to test (e.g., /api/health/)"
        />

        <TextField
          label="Schema Name (for tenant API)"
          value={schema}
          onChange={(e) => setSchema(e.target.value)}
          fullWidth
          margin="normal"
          helperText="Enter the schema name for tenant-specific endpoints (e.g., subcity_zoble)"
        />

        <Box sx={{ mt: 2, display: 'flex', gap: 2 }}>
          <Button
            variant="contained"
            onClick={handleDirectTest}
            disabled={isLoading}
          >
            Test API
          </Button>

          <Button
            variant="contained"
            onClick={handleTenantTest}
            disabled={isLoading || !schema}
            color="secondary"
          >
            Test Tenant API
          </Button>
        </Box>
      </Box>

      {isLoading && (
        <Box sx={{ display: 'flex', justifyContent: 'center', my: 2 }}>
          <CircularProgress />
        </Box>
      )}

      {error && (
        <Alert severity="error" sx={{ mt: 2 }}>
          {error}
        </Alert>
      )}

      {result && (
        <Box sx={{ mt: 2 }}>
          <Alert severity="success">
            Status: {result.status}
          </Alert>

          <Typography variant="subtitle1" sx={{ mt: 2 }}>
            Response Data:
          </Typography>

          <Paper
            elevation={1}
            sx={{
              p: 2,
              mt: 1,
              bgcolor: 'grey.100',
              maxHeight: '300px',
              overflow: 'auto',
              fontFamily: 'monospace'
            }}
          >
            <pre>{JSON.stringify(result.data, null, 2)}</pre>
          </Paper>
        </Box>
      )}

      <Typography variant="body2" color="text.secondary" sx={{ mt: 3 }}>
        Tips:
        <ul>
          <li>Use <code>/api/health/</code> to test the basic API connection</li>
          <li>Use <code>/api/debug-cors/</code> to test CORS configuration</li>
          <li>For tenant-specific endpoints, enter the schema name and use endpoints like <code>citizens/</code></li>
          <li>All API requests are sent directly to {API_BASE_URL}</li>
        </ul>
      </Typography>
    </Paper>
  );
};

export default ApiTest;
