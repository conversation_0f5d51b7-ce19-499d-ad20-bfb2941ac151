import React, { useState } from 'react';
import { Box, Button, Container, Typography, CircularProgress, Paper } from '@mui/material';
import { API_BASE_URL } from '../config/apiConfig';

const CitizenApiTest = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [response, setResponse] = useState<any>(null);
  const [responseStatus, setResponseStatus] = useState<number | null>(null);
  const [responseHeaders, setResponseHeaders] = useState<any>(null);
  const [responseText, setResponseText] = useState<string | null>(null);

  // Test minimal citizen creation
  const testCreateCitizen = async () => {
    setLoading(true);
    setError(null);
    setResponse(null);
    setResponseStatus(null);
    setResponseHeaders(null);
    setResponseText(null);

    try {
      // Get schema name from localStorage
      const schema = localStorage.getItem('schema_name') || 'kebele15';
      console.log('Using schema:', schema);

      // Get tenant info from localStorage
      const tenantString = localStorage.getItem('tenant');
      let tenant = null;
      if (tenantString) {
        try {
          tenant = JSON.parse(tenantString);
          console.log('Loaded tenant from localStorage:', tenant);
        } catch (error) {
          console.error('Error parsing tenant from localStorage:', error);
        }
      }

      // Create minimal citizen data
      const minimalCitizenData = {
        first_name: 'Test',
        last_name: 'User',
        gender: 'M',
        date_of_birth: '1990-01-01',
        religion: 1,
        citizen_status: 1,
        marital_status: 1,
        nationality: 'Ethiopian',
        nationality_country: 1,
        is_resident: true,
        center: tenant?.id || 1,
        // Add required fields
        address: 'Test Address',
        subcity: tenant?.parent_id || 1,
        kebele: tenant?.id || 1
      };

      console.log('Sending citizen data:', minimalCitizenData);

      // Construct URL
      const url = `${API_BASE_URL}/api/tenant/${encodeURIComponent(schema)}/citizens/`;
      console.log('API URL:', url);

      // Get token from localStorage
      const token = localStorage.getItem('token') || localStorage.getItem('jwt_token') || '';
      console.log('Using token:', token ? 'Token exists' : 'No token');

      // Make API call
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': token.startsWith('eyJ') ? `Bearer ${token}` : `Token ${token}`
        },
        credentials: 'include',
        body: JSON.stringify(minimalCitizenData)
      });

      // Save response status and headers
      setResponseStatus(response.status);
      setResponseHeaders(Object.fromEntries(response.headers.entries()));

      // Try to parse response as JSON
      try {
        const responseData = await response.json();
        setResponse(responseData);
        console.log('Response data:', responseData);
      } catch (jsonError) {
        // If not JSON, get as text
        const responseClone = response.clone();
        const responseText = await responseClone.text();
        setResponseText(responseText);
        console.log('Response text:', responseText);
      }

      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }

    } catch (error) {
      console.error('Error testing API:', error);
      setError(error instanceof Error ? error.message : 'An unknown error occurred');
    } finally {
      setLoading(false);
    }
  };

  // Test GET citizens
  const testGetCitizens = async () => {
    setLoading(true);
    setError(null);
    setResponse(null);
    setResponseStatus(null);
    setResponseHeaders(null);
    setResponseText(null);

    try {
      // Get schema name from localStorage
      const schema = localStorage.getItem('schema_name') || 'kebele15';
      console.log('Using schema:', schema);

      // Construct URL
      const url = `${API_BASE_URL}/api/tenant/${encodeURIComponent(schema)}/citizens/`;
      console.log('API URL:', url);

      // Get token from localStorage
      const token = localStorage.getItem('token') || localStorage.getItem('jwt_token') || '';
      console.log('Using token:', token ? 'Token exists' : 'No token');

      // Make API call
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': token.startsWith('eyJ') ? `Bearer ${token}` : `Token ${token}`
        },
        credentials: 'include'
      });

      // Save response status and headers
      setResponseStatus(response.status);
      setResponseHeaders(Object.fromEntries(response.headers.entries()));

      // Try to parse response as JSON
      try {
        const responseData = await response.json();
        setResponse(responseData);
        console.log('Response data:', responseData);
      } catch (jsonError) {
        // If not JSON, get as text
        const responseClone = response.clone();
        const responseText = await responseClone.text();
        setResponseText(responseText);
        console.log('Response text:', responseText);
      }

      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }

    } catch (error) {
      console.error('Error testing API:', error);
      setError(error instanceof Error ? error.message : 'An unknown error occurred');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Container maxWidth="md">
      <Box sx={{ my: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          Citizen API Test
        </Typography>

        {error && (
          <Box sx={{ mb: 2, p: 2, bgcolor: 'error.light', borderRadius: 1 }}>
            <Typography color="error">{error}</Typography>
          </Box>
        )}

        <Box sx={{ mb: 2 }}>
          <Button
            variant="contained"
            color="primary"
            onClick={testCreateCitizen}
            disabled={loading}
            sx={{ mr: 2 }}
          >
            {loading ? <CircularProgress size={24} /> : 'Test Create Citizen'}
          </Button>

          <Button
            variant="outlined"
            color="primary"
            onClick={testGetCitizens}
            disabled={loading}
          >
            {loading ? <CircularProgress size={24} /> : 'Test Get Citizens'}
          </Button>
        </Box>

        {responseStatus !== null && (
          <Paper sx={{ p: 2, mb: 2 }}>
            <Typography variant="h6">Response Status: {responseStatus}</Typography>
          </Paper>
        )}

        {responseHeaders && (
          <Paper sx={{ p: 2, mb: 2 }}>
            <Typography variant="h6">Response Headers:</Typography>
            <pre>{JSON.stringify(responseHeaders, null, 2)}</pre>
          </Paper>
        )}

        {response && (
          <Paper sx={{ p: 2, mb: 2 }}>
            <Typography variant="h6">Response Data:</Typography>
            <pre>{JSON.stringify(response, null, 2)}</pre>
          </Paper>
        )}

        {responseText && (
          <Paper sx={{ p: 2, mb: 2 }}>
            <Typography variant="h6">Response Text:</Typography>
            <pre style={{ whiteSpace: 'pre-wrap', wordBreak: 'break-word' }}>{responseText}</pre>
          </Paper>
        )}
      </Box>
    </Container>
  );
};

export default CitizenApiTest;
