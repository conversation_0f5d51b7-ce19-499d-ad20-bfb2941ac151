import requests
import json

# Test admin login with different superuser accounts
def test_admin_login():
    superusers = [
        {"email": "<EMAIL>", "password": "admin"},
        {"email": "<EMAIL>", "password": "admin"},
        {"email": "<EMAIL>", "password": "admin"},
        {"email": "<EMAIL>", "password": "admin"},
        {"email": "<EMAIL>", "password": "admin123"},
        {"email": "<EMAIL>", "password": "admin"}
    ]
    
    for user in superusers:
        print(f"\nTrying login with {user['email']}...")
        url = "http://localhost:8000/api/admin-login/"
        data = {
            "email": user["email"],
            "password": user["password"]
        }
        headers = {
            "Content-Type": "application/json"
        }
        
        try:
            response = requests.post(url, json=data, headers=headers)
            print(f"Status code: {response.status_code}")
            print(f"Response: {response.text}")
            
            if response.status_code == 200:
                token = response.json().get('token')
                print(f"Token: {token}")
                return token, user
        except Exception as e:
            print(f"Error: {str(e)}")
    
    return None, None

# Test JWT login with different superuser accounts
def test_jwt_login():
    superusers = [
        {"email": "<EMAIL>", "password": "admin"},
        {"email": "<EMAIL>", "password": "admin"},
        {"email": "<EMAIL>", "password": "admin"},
        {"email": "<EMAIL>", "password": "admin"},
        {"email": "<EMAIL>", "password": "admin123"},
        {"email": "<EMAIL>", "password": "admin"}
    ]
    
    for user in superusers:
        print(f"\nTrying JWT login with {user['email']}...")
        url = "http://localhost:8000/api/jwt/login/"
        data = {
            "email": user["email"],
            "password": user["password"],
            "schema_name": "public"
        }
        headers = {
            "Content-Type": "application/json"
        }
        
        try:
            response = requests.post(url, json=data, headers=headers)
            print(f"Status code: {response.status_code}")
            print(f"Response: {response.text}")
            
            if response.status_code == 200:
                tokens = response.json()
                print(f"Access token: {tokens.get('access_token')}")
                print(f"Refresh token: {tokens.get('refresh_token')}")
                return tokens, user
        except Exception as e:
            print(f"Error: {str(e)}")
    
    return None, None

# Test admin access with token
def test_admin_access(token):
    url = "http://localhost:8000/admin/"
    headers = {
        "Authorization": f"Token {token}"
    }
    
    try:
        response = requests.get(url, headers=headers)
        print(f"Status code: {response.status_code}")
        print(f"Response length: {len(response.text)}")
        print(f"Response snippet: {response.text[:200]}...")
        
        return response.status_code == 200
    except Exception as e:
        print(f"Error: {str(e)}")
        return False

# Test admin access with JWT
def test_admin_access_jwt(tokens):
    url = "http://localhost:8000/admin/"
    headers = {
        "Authorization": f"Bearer {tokens.get('access_token')}"
    }
    
    try:
        response = requests.get(url, headers=headers)
        print(f"Status code: {response.status_code}")
        print(f"Response length: {len(response.text)}")
        print(f"Response snippet: {response.text[:200]}...")
        
        return response.status_code == 200
    except Exception as e:
        print(f"Error: {str(e)}")
        return False

# Test Django admin login directly
def test_django_admin_login(user):
    url = "http://localhost:8000/admin/login/"
    data = {
        "username": user["email"],
        "password": user["password"],
        "next": "/admin/"
    }
    
    try:
        session = requests.Session()
        # Get the login page first to get the CSRF token
        login_page = session.get(url)
        
        # Extract CSRF token from the login page
        csrf_token = None
        if 'csrftoken' in session.cookies:
            csrf_token = session.cookies['csrftoken']
        
        if csrf_token:
            headers = {
                "Referer": url,
                "X-CSRFToken": csrf_token
            }
            
            # Submit the login form
            response = session.post(url, data=data, headers=headers)
            print(f"Status code: {response.status_code}")
            print(f"Response length: {len(response.text)}")
            print(f"Response snippet: {response.text[:200]}...")
            
            # Check if we were redirected to the admin page
            if response.url.endswith('/admin/'):
                print("Successfully logged in to Django admin!")
                return True
            else:
                print(f"Failed to log in. Redirected to: {response.url}")
        else:
            print("Could not find CSRF token")
    except Exception as e:
        print(f"Error: {str(e)}")
    
    return False

if __name__ == "__main__":
    print("=== Testing Admin Login API ===")
    token, user = test_admin_login()
    
    print("\n=== Testing JWT Login API ===")
    tokens, jwt_user = test_jwt_login()
    
    if token:
        print("\n=== Testing Admin Access with Token ===")
        test_admin_access(token)
    
    if tokens and tokens.get('access_token'):
        print("\n=== Testing Admin Access with JWT ===")
        test_admin_access_jwt(tokens)
    
    if user:
        print("\n=== Testing Django Admin Login Directly ===")
        test_django_admin_login(user)
    elif jwt_user:
        print("\n=== Testing Django Admin Login Directly ===")
        test_django_admin_login(jwt_user)
