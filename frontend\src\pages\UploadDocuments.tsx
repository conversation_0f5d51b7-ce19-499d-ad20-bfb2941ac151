import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Box,
  Container,
  Typography,
  TextField,
  Button,
  Paper,
  Grid,
  Alert,
  CircularProgress,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormHelperText
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import SaveIcon from '@mui/icons-material/Save';
import UploadFileIcon from '@mui/icons-material/UploadFile';
import DescriptionIcon from '@mui/icons-material/Description';
import PageBanner from '../components/PageBanner';

interface DocumentType {
  id: number;
  name: string;
}

const UploadDocuments: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  
  // State for form data
  const [formData, setFormData] = useState({
    document_type: '',
    issue_date: null as Date | null,
    expiry_date: null as Date | null,
    document_file: null as File | null
  });
  
  // State for document types
  const [documentTypes, setDocumentTypes] = useState<DocumentType[]>([]);
  
  // State for loading and error
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState(false);
  
  // Get the current tenant and authentication info from localStorage
  const tenantString = localStorage.getItem('tenant');
  const tenant = tenantString ? JSON.parse(tenantString) : null;
  const token = localStorage.getItem('token');
  const schemaName = localStorage.getItem('schema_name');
  
  // Fetch document types
  useEffect(() => {
    const fetchDocumentTypes = async () => {
      try {
        // Use the tenant-specific API endpoint
        let schema = schemaName || tenant?.schema_name || '';
        const encodedSchema = encodeURIComponent(schema);
        const url = `http://localhost:8000/api/common/document-types/`;
        
        // Use the hardcoded token for development purposes
        const hardcodedToken = '01aa7be65fbda335a0b29edd56c967ad6112fa6b';
        
        const response = await fetch(url, {
          method: 'GET',
          headers: {
            'Authorization': `Token ${hardcodedToken}`,
            'Content-Type': 'application/json'
          },
          credentials: 'include'
        });
        
        if (!response.ok) {
          throw new Error('Failed to fetch document types');
        }
        
        const data = await response.json();
        setDocumentTypes(data);
      } catch (error) {
        console.error('Error fetching document types:', error);
        // Set mock document types for testing
        setDocumentTypes([
          { id: 1, name: 'Birth Certificate' },
          { id: 2, name: 'Marriage Certificate' },
          { id: 3, name: 'Driving License' },
          { id: 4, name: 'Passport' },
          { id: 5, name: 'Education Certificate' }
        ]);
      }
    };
    
    fetchDocumentTypes();
  }, []);
  
  // Handle form input changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | { name?: string; value: unknown }>) => {
    const { name, value } = e.target as HTMLInputElement;
    if (name) {
      setFormData(prev => ({
        ...prev,
        [name]: value
      }));
    }
  };
  
  // Handle date changes
  const handleIssueDateChange = (date: Date | null) => {
    setFormData(prev => ({
      ...prev,
      issue_date: date
    }));
  };
  
  const handleExpiryDateChange = (date: Date | null) => {
    setFormData(prev => ({
      ...prev,
      expiry_date: date
    }));
  };
  
  // Handle file upload
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      setFormData(prev => ({
        ...prev,
        document_file: e.target.files![0]
      }));
    }
  };
  
  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');
    
    try {
      if (!formData.document_file) {
        throw new Error('Please select a file to upload');
      }
      
      // Create form data for file upload
      const formDataToSend = new FormData();
      formDataToSend.append('document_type', formData.document_type);
      if (formData.issue_date) {
        formDataToSend.append('issue_date', formData.issue_date.toISOString().split('T')[0]);
      }
      if (formData.expiry_date) {
        formDataToSend.append('expiry_date', formData.expiry_date.toISOString().split('T')[0]);
      }
      formDataToSend.append('document_file', formData.document_file);
      
      // Use the tenant-specific API endpoint
      let schema = schemaName || tenant?.schema_name || '';
      const encodedSchema = encodeURIComponent(schema);
      const url = `http://localhost:8000/api/tenant/${encodedSchema}/citizens/${id}/document/`;
      
      // Use the hardcoded token for development purposes
      const hardcodedToken = '01aa7be65fbda335a0b29edd56c967ad6112fa6b';
      
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Authorization': `Token ${hardcodedToken}`
          // Don't set Content-Type for FormData
        },
        body: formDataToSend,
        credentials: 'include'
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || 'Failed to upload document');
      }
      
      setSuccess(true);
      setTimeout(() => {
        navigate(`/citizens/${id}`);
      }, 2000);
    } catch (error: any) {
      console.error('Error uploading document:', error);
      setError(error.message || 'Failed to upload document');
    } finally {
      setLoading(false);
    }
  };
  
  // Handle cancel
  const handleCancel = () => {
    navigate(`/citizens/${id}`);
  };
  
  return (
    <Box sx={{ py: 4 }}>
      {/* Banner */}
      <PageBanner
        title="Upload Documents"
        subtitle="Upload official documents for the citizen"
        icon={<DescriptionIcon sx={{ fontSize: 50, color: 'white' }} />}
      />
      
      <Container maxWidth="md">
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            mb: 4,
            mt: 1
          }}
        >
          <Button
            startIcon={<ArrowBackIcon />}
            onClick={handleCancel}
            variant="outlined"
            sx={{ borderRadius: 2 }}
          >
            Back to Citizen
          </Button>
        </Box>
        
        {/* Error message */}
        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}
        
        {/* Success message */}
        {success && (
          <Alert severity="success" sx={{ mb: 3 }}>
            Document uploaded successfully! Redirecting...
          </Alert>
        )}
        
        <Paper
          component="form"
          onSubmit={handleSubmit}
          sx={{
            p: 4,
            borderRadius: 3,
            boxShadow: '0 8px 24px rgba(0,0,0,0.12)',
            mb: 4
          }}
        >
          <Typography variant="h5" sx={{ mb: 3, fontWeight: 600 }}>
            Document Information
          </Typography>
          
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth required>
                <InputLabel id="document-type-label">Document Type</InputLabel>
                <Select
                  labelId="document-type-label"
                  name="document_type"
                  value={formData.document_type}
                  onChange={handleChange}
                  label="Document Type"
                >
                  {documentTypes.map(type => (
                    <MenuItem key={type.id} value={type.id}>
                      {type.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            
            <Grid item xs={12} md={6}>
              <Box sx={{ display: 'flex', flexDirection: 'column' }}>
                <Button
                  variant="outlined"
                  component="label"
                  startIcon={<UploadFileIcon />}
                  sx={{ height: '56px', borderRadius: 1 }}
                >
                  {formData.document_file ? 'Change File' : 'Select File'}
                  <input
                    type="file"
                    hidden
                    onChange={handleFileChange}
                    accept=".pdf,.jpg,.jpeg,.png"
                  />
                </Button>
                {formData.document_file && (
                  <FormHelperText>
                    Selected: {formData.document_file.name}
                  </FormHelperText>
                )}
              </Box>
            </Grid>
            
            <Grid item xs={12} md={6}>
              <LocalizationProvider dateAdapter={AdapterDateFns}>
                <DatePicker
                  label="Issue Date"
                  value={formData.issue_date}
                  onChange={handleIssueDateChange}
                  slotProps={{
                    textField: {
                      fullWidth: true,
                      required: true,
                      variant: 'outlined'
                    }
                  }}
                />
              </LocalizationProvider>
            </Grid>
            
            <Grid item xs={12} md={6}>
              <LocalizationProvider dateAdapter={AdapterDateFns}>
                <DatePicker
                  label="Expiry Date (if applicable)"
                  value={formData.expiry_date}
                  onChange={handleExpiryDateChange}
                  slotProps={{
                    textField: {
                      fullWidth: true,
                      variant: 'outlined'
                    }
                  }}
                />
              </LocalizationProvider>
            </Grid>
          </Grid>
          
          <Box sx={{ mt: 4, display: 'flex', justifyContent: 'flex-end', gap: 2 }}>
            <Button
              variant="outlined"
              onClick={handleCancel}
              sx={{ borderRadius: 2, px: 3, py: 1.2 }}
            >
              Cancel
            </Button>
            
            <Button
              type="submit"
              variant="contained"
              color="primary"
              startIcon={loading ? <CircularProgress size={20} color="inherit" /> : <SaveIcon />}
              disabled={loading}
              sx={{ borderRadius: 2, px: 3, py: 1.2 }}
            >
              {loading ? 'Uploading...' : 'Upload Document'}
            </Button>
          </Box>
        </Paper>
      </Container>
    </Box>
  );
};

export default UploadDocuments;
