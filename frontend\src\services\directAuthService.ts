/**
 * Direct Authentication Service
 * 
 * This service provides a direct authentication mechanism that bypasses the complex
 * token refresh logic. It's designed to be simple and reliable.
 */

import { API_BASE_URL } from '../config/apiConfig';

// Simple storage for tokens
let currentAccessToken: string | null = null;
let currentRefreshToken: string | null = null;
let currentSchema: string | null = null;

/**
 * Login directly with email and password
 * @param email User email
 * @param password User password
 * @param schema Schema name
 * @returns Success status
 */
export const directLogin = async (email: string, password: string, schema?: string): Promise<boolean> => {
  try {
    console.log('Direct login attempt with:', { email, schema: schema || 'not provided' });
    
    // Clear any existing tokens
    localStorage.removeItem('direct_access_token');
    localStorage.removeItem('direct_refresh_token');
    localStorage.removeItem('direct_schema');
    
    // Make the login request
    const response = await fetch(`${API_BASE_URL}/api/jwt/login/`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        ...(schema ? { 'X-Schema-Name': schema } : {})
      },
      body: JSON.stringify({ email, password }),
      credentials: 'include',
    });
    
    if (!response.ok) {
      const errorText = await response.text();
      console.error(`Direct login failed: ${response.status} ${response.statusText} - ${errorText}`);
      return false;
    }
    
    const data = await response.json();
    
    if (data.access_token && data.refresh_token) {
      // Store tokens in memory
      currentAccessToken = data.access_token;
      currentRefreshToken = data.refresh_token;
      
      // Get schema from response or parameter
      const schemaName = data.tenant?.schema_name || schema || 'default';
      currentSchema = schemaName;
      
      // Store tokens in localStorage as backup
      localStorage.setItem('direct_access_token', data.access_token);
      localStorage.setItem('direct_refresh_token', data.refresh_token);
      localStorage.setItem('direct_schema', schemaName);
      
      // Store user and tenant info
      if (data.user) {
        localStorage.setItem('direct_user', JSON.stringify(data.user));
      }
      if (data.tenant) {
        localStorage.setItem('direct_tenant', JSON.stringify(data.tenant));
      }
      
      console.log('Direct login successful');
      return true;
    }
    
    console.error('Login response did not contain tokens');
    return false;
  } catch (error) {
    console.error('Error during direct login:', error);
    return false;
  }
};

/**
 * Make an authenticated API request
 * @param endpoint API endpoint
 * @param method HTTP method
 * @param body Request body
 * @returns Response data
 */
export const directApiRequest = async (
  endpoint: string, 
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' = 'GET',
  body?: any
): Promise<any> => {
  try {
    // Get tokens and schema
    const accessToken = currentAccessToken || localStorage.getItem('direct_access_token');
    const schema = currentSchema || localStorage.getItem('direct_schema');
    
    if (!accessToken || !schema) {
      throw new Error('No authentication information available');
    }
    
    // Format endpoint
    const formattedEndpoint = endpoint.startsWith('/') ? endpoint.substring(1) : endpoint;
    
    // Create URL
    const url = `${API_BASE_URL}/api/tenant/${schema}/${formattedEndpoint}`;
    
    console.log(`Making direct API ${method} request to: ${url}`);
    
    // Make the request
    const response = await fetch(url, {
      method,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${accessToken}`,
        'X-Schema-Name': schema
      },
      ...(body ? { body: JSON.stringify(body) } : {}),
      credentials: 'include',
    });
    
    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`API request failed: ${response.status} ${response.statusText} - ${errorText}`);
    }
    
    return await response.json();
  } catch (error) {
    console.error(`Error making direct API request to ${endpoint}:`, error);
    throw error;
  }
};

/**
 * Check if user is authenticated
 * @returns Authentication status
 */
export const isDirectlyAuthenticated = (): boolean => {
  return !!(currentAccessToken || localStorage.getItem('direct_access_token'));
};

/**
 * Get current schema
 * @returns Current schema
 */
export const getDirectSchema = (): string | null => {
  return currentSchema || localStorage.getItem('direct_schema');
};

/**
 * Logout
 */
export const directLogout = (): void => {
  currentAccessToken = null;
  currentRefreshToken = null;
  currentSchema = null;
  
  localStorage.removeItem('direct_access_token');
  localStorage.removeItem('direct_refresh_token');
  localStorage.removeItem('direct_schema');
  localStorage.removeItem('direct_user');
  localStorage.removeItem('direct_tenant');
};

export default {
  directLogin,
  directApiRequest,
  isDirectlyAuthenticated,
  getDirectSchema,
  directLogout
};
