from django.db import migrations

def update_idcard_centers(apps, schema_editor):
    """Update IDCard centers based on their citizen's center."""
    IDCard = apps.get_model('idcards', 'IDCard')
    for idcard in IDCard.objects.all():
        if idcard.citizen and hasattr(idcard.citizen, 'center'):
            idcard.center = idcard.citizen.center
            idcard.save(update_fields=['center'])

class Migration(migrations.Migration):

    dependencies = [
        ('centers', '0004_alter_center_code'),
        ('idcards', '0002_idcard_center'),
        ('citizens', '0001_initial'),
    ]

    operations = [
        migrations.RunPython(update_idcard_centers),
    ]
