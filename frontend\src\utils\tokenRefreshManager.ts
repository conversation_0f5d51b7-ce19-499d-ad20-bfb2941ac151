/**
 * Token Refresh Manager
 *
 * This module provides utilities for refreshing tokens and handling token expiration.
 * It ensures that tokens are refreshed before they expire and handles token expiration gracefully.
 */

import { getAccessTokenForSchema as getTokenForSchema, storeTokensForSchema as saveTokenForSchema, clearTokensForSchema as removeTokenForSchema, getCurrentSchema } from '../services/tokenService';
import { apiClient } from './apiClient';

// Token refresh interval in milliseconds (default: 30 minutes)
const TOKEN_REFRESH_INTERVAL = 30 * 60 * 1000;

// Token expiration time in milliseconds (default: 1 hour)
const TOKEN_EXPIRATION_TIME = 60 * 60 * 1000;

// Flag to prevent multiple refresh attempts
let isRefreshing = false;

// Token refresh timers by schema
const refreshTimers: Record<string, NodeJS.Timeout> = {};

/**
 * Initialize token refresh for a schema
 * @param schema The schema name
 */
export const initializeTokenRefresh = (schema?: string): void => {
  try {
    // If no schema is provided, use the current schema
    const schemaName = schema || getCurrentSchema();

    if (!schemaName) {
      console.error('Cannot initialize token refresh: schema is missing');
      return;
    }

    // Clear any existing timer for this schema
    if (refreshTimers[schemaName]) {
      clearTimeout(refreshTimers[schemaName]);
      delete refreshTimers[schemaName];
    }

    // Set up a timer to refresh the token
    refreshTimers[schemaName] = setTimeout(() => {
      refreshToken(schemaName);
    }, TOKEN_REFRESH_INTERVAL);

    console.log(`Token refresh initialized for schema ${schemaName}`);
  } catch (error) {
    console.error('Error initializing token refresh:', error);
  }
};

/**
 * Refresh a token for a schema
 * @param schema The schema name
 */
export const refreshToken = async (schema: string): Promise<void> => {
  // Prevent multiple refresh attempts
  if (isRefreshing) {
    console.log('Token refresh already in progress, skipping');
    return;
  }

  try {
    isRefreshing = true;

    if (!schema) {
      console.error('Cannot refresh token: schema is missing');
      isRefreshing = false;
      return;
    }

    // Get the current token for this schema
    const token = getTokenForSchema(schema);

    if (!token) {
      console.error(`Cannot refresh token: no token found for schema ${schema}`);
      isRefreshing = false;
      return;
    }

    console.log(`Refreshing token for schema ${schema}`);

    try {
      // Make a request to the token refresh endpoint
      const response = await apiClient.post<{ token: string }>('refresh-token/', {
        token,
        schema_name: schema
      });

      if (response && response.token) {
        // Save the new token
        saveTokenForSchema(schema, response.token, '');
        console.log(`Token refreshed for schema ${schema}`);

        // Set up the next refresh
        initializeTokenRefresh(schema);
      } else {
        console.error(`Failed to refresh token for schema ${schema}`);
        handleTokenExpiration(schema);
      }
    } catch (error) {
      console.error(`Error refreshing token for schema ${schema}:`, error);
      handleTokenExpiration(schema);
    }
  } catch (error) {
    console.error(`Error refreshing token for schema ${schema}:`, error);
    handleTokenExpiration(schema);
  } finally {
    isRefreshing = false;
  }
};

// Flag to prevent multiple redirects to login
let isRedirectingToLogin = false;

/**
 * Handle token expiration
 * @param schema The schema name
 */
export const handleTokenExpiration = (schema: string): void => {
  try {
    if (!schema) {
      console.error('Cannot handle token expiration: schema is missing');
      return;
    }

    console.log(`Handling token expiration for schema ${schema}`);

    // Remove the token for this schema
    removeTokenForSchema(schema);

    // Clear any existing timer for this schema
    if (refreshTimers[schema]) {
      clearTimeout(refreshTimers[schema]);
      delete refreshTimers[schema];
    }

    // Check if this is the current schema
    const currentSchema = getCurrentSchema();

    if (currentSchema === schema && !isRedirectingToLogin) {
      isRedirectingToLogin = true;

      // Store a flag to prevent login loops
      sessionStorage.setItem('token_expired', 'true');

      // Redirect to login page
      console.log('Redirecting to login page due to token expiration');

      // Use setTimeout to allow other operations to complete
      setTimeout(() => {
        window.location.href = '/login';
      }, 100);
    }
  } catch (error) {
    console.error(`Error handling token expiration for schema ${schema}:`, error);
    isRedirectingToLogin = false;
  }
};

/**
 * Initialize token refresh for all schemas
 */
export const initializeAllTokenRefreshes = (): void => {
  try {
    // Get all schemas from localStorage
    const tokenStoreStr = localStorage.getItem('tokenStore');

    if (tokenStoreStr) {
      const tokenStore = JSON.parse(tokenStoreStr);

      // Initialize token refresh for each schema
      Object.keys(tokenStore).forEach(schema => {
        initializeTokenRefresh(schema);
      });

      console.log('Token refresh initialized for all schemas');
    }
  } catch (error) {
    console.error('Error initializing all token refreshes:', error);
  }
};

/**
 * Check if a token is expired
 * @param token The token to check
 * @returns True if the token is expired, false otherwise
 */
export const isTokenExpired = (token: string): boolean => {
  try {
    if (!token) {
      return true;
    }

    // Parse the token to get the expiration time
    const tokenParts = token.split('.');

    if (tokenParts.length !== 3) {
      // Not a JWT token, assume it's not expired
      return false;
    }

    const base64Url = tokenParts[1];
    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
    const jsonPayload = decodeURIComponent(
      window
        .atob(base64)
        .split('')
        .map(c => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2))
        .join('')
    );
    const payload = JSON.parse(jsonPayload);

    if (!payload.exp) {
      // No expiration time, assume it's not expired
      return false;
    }

    // Check if the token is expired
    const expirationTime = payload.exp * 1000; // Convert to milliseconds
    const currentTime = Date.now();

    return currentTime >= expirationTime;
  } catch (error) {
    console.error('Error checking token expiration:', error);
    return true;
  }
};

// Flag to prevent multiple redirects
let isRedirecting = false;

/**
 * Add an interceptor to handle token expiration
 */
export const addTokenExpirationInterceptor = (): void => {
  try {
    // Add a response interceptor to handle token expiration
    const originalFetch = window.fetch;

    window.fetch = async (input: RequestInfo, init?: RequestInit) => {
      try {
        // Skip interception for login and refresh token requests
        const url = typeof input === 'string' ? input : input.url;
        const isLoginRequest = url.includes('/login/') || url.includes('/refresh-token/');

        if (isLoginRequest) {
          return originalFetch(input, init);
        }

        // Make the request
        const response = await originalFetch(input, init);

        // Check if the response status is 401 (Unauthorized)
        if (response.status === 401 && !isRedirecting) {
          // Get the current schema
          const schema = getCurrentSchema();

          if (schema) {
            isRedirecting = true;

            // Try to refresh the token first
            try {
              await refreshToken(schema);
              isRedirecting = false;

              // If refresh was successful, return the original response
              // The next request will use the new token
              return response;
            } catch (refreshError) {
              console.error('Error refreshing token:', refreshError);

              // If refresh failed, handle token expiration
              handleTokenExpiration(schema);
              isRedirecting = false;
            }
          }
        }

        return response;
      } catch (error) {
        console.error('Error in fetch interceptor:', error);
        throw error;
      }
    };

    console.log('Token expiration interceptor added');
  } catch (error) {
    console.error('Error adding token expiration interceptor:', error);
  }
};
