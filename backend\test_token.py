import os
import django
import sys

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'neocamelot.settings')
django.setup()

from django.db import connection
from django_tenants.utils import tenant_context
from centers.models import Client
from django.contrib.auth import get_user_model
from accounts.jwt_utils import validate_jwt_token, generate_jwt_tokens

User = get_user_model()

# Get the token from command line argument
if len(sys.argv) > 1:
    token_key = sys.argv[1]
else:
    print("Please provide a JWT token as a command line argument")
    sys.exit(1)

print(f"\nValidating JWT token: {token_key[:20]}...")

# Validate the JWT token
payload = validate_jwt_token(token_key)

if not payload:
    print(f"Invalid JWT token")
    sys.exit(1)

print(f"JWT token is valid")
print(f"Payload: {payload}")

# Get user ID and schema from payload
user_id = payload.get('sub')
schema_name = payload.get('schema')

if not user_id or not schema_name:
    print(f"Invalid token payload - missing user ID or schema")
    sys.exit(1)

print(f"User ID: {user_id}")
print(f"Schema: {schema_name}")

# Get the tenant by schema name
try:
    tenant = Client.objects.get(schema_name=schema_name)
    print(f"Found tenant: {tenant.name} (Schema: {tenant.schema_name})")

    # Set the tenant for this request
    connection.set_tenant(tenant)

    # Get the user
    with tenant_context(tenant):
        try:
            user = User.objects.get(id=user_id)
            print(f"Found user: {user.email} (ID: {user.id})")
            print(f"User details: {user.get_full_name()}")
            print(f"User is_staff: {user.is_staff}")
            print(f"User is_superuser: {user.is_superuser}")

            # Generate new tokens for the user
            print(f"\nGenerating new tokens for user")
            access_token, refresh_token = generate_jwt_tokens(user, schema_name)

            print(f"New access token: {access_token[:20]}...")
            print(f"New refresh token: {refresh_token[:20]}...")

            print(f"\nJWT token is valid for user {user.email} in tenant {tenant.name}")
            sys.exit(0)
        except User.DoesNotExist:
            print(f"User with ID {user_id} not found in tenant {schema_name}")
            sys.exit(1)
except Client.DoesNotExist:
    print(f"Tenant with schema {schema_name} not found")
    sys.exit(1)
except Exception as e:
    print(f"Error: {str(e)}")
    sys.exit(1)
