"""
JWT Authentication Views

This module provides views for JWT-based authentication, including login,
token refresh, and token validation.
"""

import logging
import jwt
import datetime
from rest_framework import status
from rest_framework.decorators import api_view, permission_classes, authentication_classes
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from django.contrib.auth import authenticate, get_user_model
from django_tenants.utils import tenant_context  # Used by imported functions
from centers.models import Client
from .serializers import LoginSerializer, UserSerializer
from .jwt_utils import (
    generate_jwt_token,
    validate_jwt_token,
    refresh_jwt_token,
    extract_token_from_request,
    JWT_SECRET_KEY,
    JWT_ALGORITHM
)
from .tenant_user_utils import find_user_in_all_tenants, authenticate_user_in_tenant

# Configure logging
logger = logging.getLogger(__name__)

User = get_user_model()

@api_view(['POST', 'OPTIONS'])
@permission_classes([AllowAny])
@authentication_classes([])
def jwt_login_view(request):
    """
    JWT-based login endpoint with automatic tenant selection based on email.
    """
    # Log request details for debugging
    logger.info(f"JWT login request received: {request.method}")
    logger.info(f"Request headers: {dict(request.headers)}")
    logger.info(f"Request data: {request.data}")
    logger.info(f"Request cookies: {request.COOKIES}")

    # Handle preflight OPTIONS request
    if request.method == 'OPTIONS':
        response = Response()
        origin = request.headers.get('Origin', '*')

        # Allow any origin for OPTIONS requests
        response['Access-Control-Allow-Origin'] = origin
        response['Access-Control-Allow-Methods'] = 'POST, OPTIONS'
        response['Access-Control-Allow-Headers'] = 'Content-Type, Authorization, X-Requested-With, X-Schema-Name'
        response['Access-Control-Allow-Credentials'] = 'true'
        response['Access-Control-Max-Age'] = '86400'  # 24 hours

        logger.info(f"Returning OPTIONS response with CORS headers: {dict(response.headers)}")
        return response

    # Validate login data
    serializer = LoginSerializer(data=request.data)
    if not serializer.is_valid():
        logger.error(f"Login serializer validation failed: {serializer.errors}")
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    email = serializer.validated_data['email']
    password = serializer.validated_data['password']

    # Get schema_name from various sources
    schema_name = request.data.get('schema_name')

    # If schema_name is not in request data, try to get it from headers
    if not schema_name:
        schema_name = request.headers.get('X-Schema-Name')
        logger.info(f"Got schema_name from header: {schema_name}")

    # If still no schema_name, try to get it from cookies
    if not schema_name:
        schema_name = request.COOKIES.get('schema_name')
        logger.info(f"Got schema_name from cookie: {schema_name}")

    logger.info(f"Using schema_name: {schema_name}")

    # First try to authenticate in the public schema for superadmin
    user = authenticate(request, username=email, password=password)

    if user and user.is_superuser:
        # This is a superadmin in the public schema
        # Generate JWT tokens
        access_token = generate_jwt_token(user, 'public', is_refresh=False)
        refresh_token = generate_jwt_token(user, 'public', is_refresh=True)

        if not access_token or not refresh_token:
            return Response(
                {'error': 'Failed to generate tokens'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

        # Prepare response
        response_data = {
            'access_token': access_token,
            'refresh_token': refresh_token,
            'user': UserSerializer(user).data,
            'is_superadmin': True,
            'tenant': {
                'id': None,
                'schema_name': 'public',
                'name': 'System Administration',
                'type': 'SYSTEM',
            }
        }

        # Create response with tokens
        response = Response(response_data)

        # Set tokens as cookies (optional, for added security)
        response.set_cookie(
            'jwt_access_token',
            access_token,
            httponly=True,
            secure=True,  # Required when samesite='None'
            samesite='None',  # Use 'None' for cross-origin requests
            max_age=900  # 15 minutes
        )
        response.set_cookie(
            'jwt_refresh_token',
            refresh_token,
            httponly=True,
            secure=True,  # Required when samesite='None'
            samesite='None',  # Use 'None' for cross-origin requests
            max_age=604800  # 7 days
        )

        # Also set a non-httponly cookie for the frontend to access
        response.set_cookie(
            'jwt_access_token_frontend',
            access_token,
            httponly=False,
            secure=True,  # Required when samesite='None'
            samesite='None',  # Use 'None' for cross-origin requests
            max_age=900  # 15 minutes
        )

        # Add CORS headers
        origin = request.headers.get('Origin', '*')
        response['Access-Control-Allow-Origin'] = origin
        response['Access-Control-Allow-Credentials'] = 'true'
        logger.info(f"Added CORS headers for origin: {origin}")

        return response

    # If not a superadmin, try to find the user in tenant schemas
    try:
        # First, try domain-based detection if the email contains a domain
        tenant_from_domain = None
        if '@' in email:
            from .tenant_user_utils import find_tenant_by_email_domain
            tenant_from_domain = find_tenant_by_email_domain(email)
            if tenant_from_domain:
                logger.info(f"Found tenant {tenant_from_domain.schema_name} based on email domain from {email}")

                # Try to authenticate in this tenant first
                user = authenticate_user_in_tenant(request, email, password, tenant_from_domain)

                if user:
                    logger.info(f"Successfully authenticated user {email} in domain-matched tenant {tenant_from_domain.schema_name}")

                    # Generate JWT tokens
                    access_token = generate_jwt_token(user, tenant_from_domain.schema_name, is_refresh=False)
                    refresh_token = generate_jwt_token(user, tenant_from_domain.schema_name, is_refresh=True)

                    if not access_token or not refresh_token:
                        return Response(
                            {'error': 'Failed to generate tokens'},
                            status=status.HTTP_500_INTERNAL_SERVER_ERROR
                        )

                    # Prepare response data
                    response_data = {
                        'access_token': access_token,
                        'refresh_token': refresh_token,
                        'user': UserSerializer(user).data,
                        'tenant': {
                            'id': tenant_from_domain.id,
                            'schema_name': tenant_from_domain.schema_name,
                            'name': tenant_from_domain.name,
                            'type': tenant_from_domain.schema_type,
                        }
                    }

                    # Add parent information if available
                    if tenant_from_domain.parent:
                        response_data['tenant'].update({
                            'parent_id': tenant_from_domain.parent.id,
                            'parent_name': tenant_from_domain.parent.name,
                            'parent_schema_name': tenant_from_domain.parent.schema_name,
                        })

                    # Create response with tokens
                    response = Response(response_data)

                    # Set tokens as cookies
                    response.set_cookie(
                        'jwt_access_token',
                        access_token,
                        httponly=True,
                        secure=True,  # Required when samesite='None'
                        samesite='None',  # Use 'None' for cross-origin requests
                        max_age=900  # 15 minutes
                    )
                    response.set_cookie(
                        'jwt_refresh_token',
                        refresh_token,
                        httponly=True,
                        secure=True,  # Required when samesite='None'
                        samesite='None',  # Use 'None' for cross-origin requests
                        max_age=604800  # 7 days
                    )

                    # Also set a non-httponly cookie for the frontend to access
                    response.set_cookie(
                        'jwt_access_token_frontend',
                        access_token,
                        httponly=False,
                        secure=True,  # Required when samesite='None'
                        samesite='None',  # Use 'None' for cross-origin requests
                        max_age=900  # 15 minutes
                    )

                    # Add CORS headers
                    origin = request.headers.get('Origin', '*')
                    response['Access-Control-Allow-Origin'] = origin
                    response['Access-Control-Allow-Credentials'] = 'true'
                    logger.info(f"Added CORS headers for origin: {origin}")

                    return response

        # If domain-based detection failed, try to find the user in all tenants
        found_user, found_tenant = find_user_in_all_tenants(email)

        if found_user:
            logger.info(f"User {email} found in tenant: {found_tenant.schema_name if found_tenant else 'public'}")
        else:
            logger.warning(f"User {email} not found in any tenant")
            return Response(
                {'error': f'User {email} not found in any schema'},
                status=status.HTTP_401_UNAUTHORIZED
            )

        # If schema_name is provided, try only that schema
        if schema_name:
            try:
                tenant = Client.objects.get(schema_name=schema_name)
                # Authenticate the user in the specified tenant
                user = authenticate_user_in_tenant(request, email, password, tenant)

                if user:
                    # Generate JWT tokens
                    access_token = generate_jwt_token(user, schema_name, is_refresh=False)
                    refresh_token = generate_jwt_token(user, schema_name, is_refresh=True)

                    if not access_token or not refresh_token:
                        return Response(
                            {'error': 'Failed to generate tokens'},
                            status=status.HTTP_500_INTERNAL_SERVER_ERROR
                        )

                    # Prepare response data
                    response_data = {
                        'access_token': access_token,
                        'refresh_token': refresh_token,
                        'user': UserSerializer(user).data,
                        'tenant': {
                            'id': tenant.id,
                            'schema_name': tenant.schema_name,
                            'name': tenant.name,
                            'type': tenant.schema_type,
                        }
                    }

                    # Add parent information if available
                    if tenant.parent:
                        response_data['tenant'].update({
                            'parent_id': tenant.parent.id,
                            'parent_name': tenant.parent.name,
                            'parent_schema_name': tenant.parent.schema_name,
                        })

                    # Create response with tokens
                    response = Response(response_data)

                    # Set tokens as cookies (optional, for added security)
                    response.set_cookie(
                        'jwt_access_token',
                        access_token,
                        httponly=True,
                        secure=True,  # Required when samesite='None'
                        samesite='None',  # Use 'None' for cross-origin requests
                        max_age=900  # 15 minutes
                    )
                    response.set_cookie(
                        'jwt_refresh_token',
                        refresh_token,
                        httponly=True,
                        secure=True,  # Required when samesite='None'
                        samesite='None',  # Use 'None' for cross-origin requests
                        max_age=604800  # 7 days
                    )

                    # Also set a non-httponly cookie for the frontend to access
                    response.set_cookie(
                        'jwt_access_token_frontend',
                        access_token,
                        httponly=False,
                        secure=True,  # Required when samesite='None'
                        samesite='None',  # Use 'None' for cross-origin requests
                        max_age=900  # 15 minutes
                    )

                    # Add CORS headers
                    origin = request.headers.get('Origin', '*')
                    response['Access-Control-Allow-Origin'] = origin
                    response['Access-Control-Allow-Credentials'] = 'true'
                    logger.info(f"Added CORS headers for origin: {origin}")

                    return response
            except Client.DoesNotExist:
                return Response(
                    {'error': f'Tenant with schema "{schema_name}" not found'},
                    status=status.HTTP_404_NOT_FOUND
                )
        else:
            # If we already found the user, try to authenticate in that tenant
            if found_tenant:
                logger.info(f"Attempting to authenticate {email} in found tenant {found_tenant.schema_name}")
                user = authenticate_user_in_tenant(request, email, password, found_tenant)

                if user:
                    # Found and authenticated the user in this tenant
                    logger.info(f"User {email} authenticated successfully in tenant {found_tenant.schema_name}")

                    # Generate JWT tokens
                    access_token = generate_jwt_token(user, found_tenant.schema_name, is_refresh=False)
                    refresh_token = generate_jwt_token(user, found_tenant.schema_name, is_refresh=True)

                    if not access_token or not refresh_token:
                        return Response(
                            {'error': 'Failed to generate tokens'},
                            status=status.HTTP_500_INTERNAL_SERVER_ERROR
                        )
                else:
                    logger.warning(f"User {email} found in tenant {found_tenant.schema_name} but authentication failed")
                    return Response(
                        {'error': 'Invalid credentials'},
                        status=status.HTTP_401_UNAUTHORIZED
                    )
            else:
                # Automatic tenant selection - try all active tenants (fallback)
                tenants = Client.objects.filter(is_active=True)
                logger.info(f"Found {tenants.count()} active tenants to check")

                # Try each tenant until we find the user
                for tenant in tenants:
                    try:
                        logger.info(f"Trying to authenticate {email} in tenant {tenant.schema_name}")
                        user = authenticate_user_in_tenant(request, email, password, tenant)

                        if user:
                            # Generate JWT tokens
                            access_token = generate_jwt_token(user, tenant.schema_name, is_refresh=False)
                            refresh_token = generate_jwt_token(user, tenant.schema_name, is_refresh=True)

                            if not access_token or not refresh_token:
                                continue  # Try next tenant if token generation fails

                            # Prepare response data
                            response_data = {
                                'access_token': access_token,
                                'refresh_token': refresh_token,
                                'user': UserSerializer(user).data,
                                'tenant': {
                                    'id': tenant.id,
                                    'schema_name': tenant.schema_name,
                                    'name': tenant.name,
                                    'type': tenant.schema_type,
                                }
                            }

                            # Add parent information if available
                            if tenant.parent:
                                response_data['tenant'].update({
                                    'parent_id': tenant.parent.id,
                                    'parent_name': tenant.parent.name,
                                    'parent_schema_name': tenant.parent.schema_name,
                                })

                            # Create response with tokens
                            response = Response(response_data)

                            # Set tokens as cookies (optional, for added security)
                            response.set_cookie(
                                'jwt_access_token',
                                access_token,
                                httponly=True,
                                secure=True,  # Required when samesite='None'
                                samesite='None',  # Use 'None' for cross-origin requests
                                max_age=900  # 15 minutes
                            )
                            response.set_cookie(
                                'jwt_refresh_token',
                                refresh_token,
                                httponly=True,
                                secure=True,  # Required when samesite='None'
                                samesite='None',  # Use 'None' for cross-origin requests
                                max_age=604800  # 7 days
                            )

                            # Also set a non-httponly cookie for the frontend to access
                            response.set_cookie(
                                'jwt_access_token_frontend',
                                access_token,
                                httponly=False,
                                secure=True,  # Required when samesite='None'
                                samesite='None',  # Use 'None' for cross-origin requests
                                max_age=900  # 15 minutes
                            )

                            # Add CORS headers
                            origin = request.headers.get('Origin', '*')
                            response['Access-Control-Allow-Origin'] = origin
                            response['Access-Control-Allow-Credentials'] = 'true'
                            logger.info(f"Added CORS headers for origin: {origin}")

                            return response
                    except Exception as e:
                        # Log the error but continue trying other tenants
                        logger.error(f"Error checking tenant {tenant.schema_name}: {str(e)}")
                        continue
    except Exception as e:
        logger.error(f"Error during authentication: {str(e)}")
        return Response(
            {'error': f'Error during authentication: {str(e)}'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

    # If we get here, the user was not found in any tenant
    return Response(
        {'error': 'Invalid credentials. User not found in any tenant.'},
        status=status.HTTP_401_UNAUTHORIZED
    )

@api_view(['POST', 'OPTIONS'])
@permission_classes([AllowAny])
@authentication_classes([])
def jwt_refresh_token_view(request):
    """
    Refresh JWT tokens using a valid refresh token.
    """
    # Log request details for debugging
    logger.info(f"JWT refresh token request received: {request.method}")
    logger.info(f"Request headers: {dict(request.headers)}")
    logger.info(f"Request data: {request.data}")
    logger.info(f"Request cookies: {request.COOKIES}")

    # Handle preflight OPTIONS request
    if request.method == 'OPTIONS':
        response = Response()
        origin = request.headers.get('Origin', '*')

        # Allow any origin for OPTIONS requests
        response['Access-Control-Allow-Origin'] = origin
        response['Access-Control-Allow-Methods'] = 'POST, OPTIONS'
        response['Access-Control-Allow-Headers'] = 'Content-Type, Authorization, X-Requested-With, X-Schema-Name'
        response['Access-Control-Allow-Credentials'] = 'true'
        response['Access-Control-Max-Age'] = '86400'  # 24 hours

        logger.info(f"Returning OPTIONS response with CORS headers: {dict(response.headers)}")
        return response

    # Get refresh token from request body
    refresh_token = request.data.get('refresh_token')
    logger.info(f"Refresh token from request body: {'Present' if refresh_token else 'Not present'}")

    # Try to get refresh token from cookies if not in request body
    if not refresh_token:
        # Check all possible cookie names for refresh token
        refresh_cookie_names = [
            'jwt_refresh_token',
            'refresh_token'
        ]

        for cookie_name in refresh_cookie_names:
            cookie_token = request.COOKIES.get(cookie_name)
            if cookie_token:
                refresh_token = cookie_token
                logger.info(f"Found refresh token in cookie '{cookie_name}'")
                break

        if refresh_token:
            logger.info("Using refresh token from cookies")

    # As a fallback, try to get a token from the request and check if it's a refresh token
    if not refresh_token:
        token = extract_token_from_request(request)
        if token:
            # Validate the token without checking expiry
            try:
                payload = jwt.decode(token, JWT_SECRET_KEY, algorithms=[JWT_ALGORITHM], options={"verify_exp": False})
                if payload.get('token_type') == 'refresh':
                    refresh_token = token
                    logger.info("Using token from request as refresh token")
            except Exception as e:
                logger.warning(f"Error decoding token as refresh token: {str(e)}")

    if not refresh_token:
        return Response(
            {'error': 'Refresh token is required'},
            status=status.HTTP_400_BAD_REQUEST
        )

    # Refresh the tokens
    access_token, new_refresh_token = refresh_jwt_token(refresh_token)

    if not access_token or not new_refresh_token:
        return Response(
            {'error': 'Invalid or expired refresh token'},
            status=status.HTTP_401_UNAUTHORIZED
        )

    # Prepare response
    response_data = {
        'access_token': access_token,
        'refresh_token': new_refresh_token,
    }

    # Create response with tokens
    response = Response(response_data)

    # Set tokens as cookies (optional, for added security)
    response.set_cookie(
        'jwt_access_token',
        access_token,
        httponly=True,
        secure=True,  # Required when samesite='None'
        samesite='None',  # Use 'None' for cross-origin requests
        max_age=900  # 15 minutes
    )
    response.set_cookie(
        'jwt_refresh_token',
        new_refresh_token,
        httponly=True,
        secure=True,  # Required when samesite='None'
        samesite='None',  # Use 'None' for cross-origin requests
        max_age=604800  # 7 days
    )

    # Also set a non-httponly cookie for the frontend to access
    response.set_cookie(
        'jwt_access_token_frontend',
        access_token,
        httponly=False,
        secure=True,  # Required when samesite='None'
        samesite='None',  # Use 'None' for cross-origin requests
        max_age=900  # 15 minutes
    )

    # Add CORS headers
    origin = request.headers.get('Origin', '*')
    response['Access-Control-Allow-Origin'] = origin
    response['Access-Control-Allow-Credentials'] = 'true'
    logger.info(f"Added CORS headers for origin: {origin}")

    return response

@api_view(['POST', 'GET', 'OPTIONS'])
@permission_classes([AllowAny])
@authentication_classes([])
def jwt_validate_token_view(request):
    """
    Validate a JWT token and return user information if valid.
    """
    # Log request details for debugging
    logger.info(f"JWT validate token request received: {request.method}")
    logger.info(f"Request headers: {dict(request.headers)}")
    logger.info(f"Request data: {request.data}")
    logger.info(f"Request cookies: {request.COOKIES}")

    # Handle preflight OPTIONS request
    if request.method == 'OPTIONS':
        response = Response()
        origin = request.headers.get('Origin', '*')

        # Allow any origin for OPTIONS requests
        response['Access-Control-Allow-Origin'] = origin
        response['Access-Control-Allow-Methods'] = 'POST, GET, OPTIONS'
        response['Access-Control-Allow-Headers'] = 'Content-Type, Authorization, X-Requested-With, X-Schema-Name'
        response['Access-Control-Allow-Credentials'] = 'true'
        response['Access-Control-Max-Age'] = '86400'  # 24 hours

        logger.info(f"Returning OPTIONS response with CORS headers: {dict(response.headers)}")
        return response

    # Get token from request using utility function
    token = extract_token_from_request(request)

    if not token:
        return Response(
            {'error': 'Token is required'},
            status=status.HTTP_400_BAD_REQUEST
        )

    # Validate the token
    payload = validate_jwt_token(token)

    if not payload:
        return Response(
            {'valid': False, 'error': 'Invalid or expired token'},
            status=status.HTTP_401_UNAUTHORIZED
        )

    # Token is valid, return user information
    response_data = {
        'valid': True,
        'user_id': payload.get('sub'),
        'email': payload.get('email'),
        'name': payload.get('name'),
        'is_superuser': payload.get('is_superuser'),
        'is_staff': payload.get('is_staff'),
        'role': payload.get('role'),
        'tenant_level': payload.get('tenant_level'),
        'schema': payload.get('schema'),
        'exp': payload.get('exp'),
    }

    # Add CORS headers
    response = Response(response_data)
    origin = request.headers.get('Origin', '*')
    response['Access-Control-Allow-Origin'] = origin
    response['Access-Control-Allow-Credentials'] = 'true'
    logger.info(f"Added CORS headers for origin: {origin}")

    return response


@api_view(['GET', 'POST', 'OPTIONS'])
@permission_classes([AllowAny])
@authentication_classes([])
def jwt_debug_view(request):
    """
    Debug view to help diagnose JWT authentication issues.
    """
    # Log request details for debugging
    logger.info(f"JWT debug request received: {request.method}")
    logger.info(f"Request headers: {dict(request.headers)}")
    logger.info(f"Request data: {request.data}")
    logger.info(f"Request cookies: {request.COOKIES}")

    # Handle preflight OPTIONS request
    if request.method == 'OPTIONS':
        response = Response()
        origin = request.headers.get('Origin', '*')

        # Allow any origin for OPTIONS requests
        response['Access-Control-Allow-Origin'] = origin
        response['Access-Control-Allow-Methods'] = 'GET, POST, OPTIONS'
        response['Access-Control-Allow-Headers'] = 'Content-Type, Authorization, X-Requested-With, X-Schema-Name'
        response['Access-Control-Allow-Credentials'] = 'true'
        response['Access-Control-Max-Age'] = '86400'  # 24 hours

        logger.info(f"Returning OPTIONS response with CORS headers: {dict(response.headers)}")
        return response

    # Prepare response data
    response_data = {
        'message': 'JWT debug endpoint',
        'method': request.method,
        'headers': dict(request.headers),
        'cookies': request.COOKIES,
        'data': request.data,
        'query_params': request.query_params,
        'path': request.path,
        'user_agent': request.META.get('HTTP_USER_AGENT', 'Unknown'),
        'remote_addr': request.META.get('REMOTE_ADDR', 'Unknown'),
        'timestamp': datetime.datetime.now().isoformat(),
    }

    # Extract token if present
    token = extract_token_from_request(request)
    if token:
        response_data['token_present'] = True
        response_data['token_prefix'] = token[:10] + '...'

        # Try to validate the token
        payload = validate_jwt_token(token)
        if payload:
            response_data['token_valid'] = True
            response_data['token_payload'] = payload
        else:
            response_data['token_valid'] = False
    else:
        response_data['token_present'] = False

    # Create response
    response = Response(response_data)

    # Add CORS headers
    origin = request.headers.get('Origin', '*')
    response['Access-Control-Allow-Origin'] = origin
    response['Access-Control-Allow-Credentials'] = 'true'
    logger.info(f"Added CORS headers for origin: {origin}")

    return response