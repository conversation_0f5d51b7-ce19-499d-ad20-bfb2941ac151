import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Container,
  Typography,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Button,
  Alert,
  CircularProgress,
  Snackbar,
} from '@mui/material';
import RefreshIcon from '@mui/icons-material/Refresh';
import PersonIcon from '@mui/icons-material/Person';
import { directApiRequest, isDirectlyAuthenticated, getDirectSchema } from '../services/directAuthService';

// Citizen interface
interface Citizen {
  id: number;
  first_name: string;
  last_name: string;
  gender: string;
  birth_date: string;
  id_number?: string;
  phone?: string;
  photo_url?: string;
}

const DirectCitizensList: React.FC = () => {
  const navigate = useNavigate();
  
  // State
  const [citizens, setCitizens] = useState<Citizen[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  
  // Check authentication
  useEffect(() => {
    if (!isDirectlyAuthenticated()) {
      console.log('Not authenticated, redirecting to direct login');
      navigate('/direct-login');
    }
  }, [navigate]);
  
  // Fetch citizens
  const fetchCitizens = async () => {
    setLoading(true);
    setError(null);
    
    try {
      console.log('Fetching citizens using direct API request');
      
      const responseData = await directApiRequest('citizens/?detail=true');
      console.log('Citizens data received:', responseData);
      
      // Handle different response formats
      let data: Citizen[] = [];
      if (responseData && typeof responseData === 'object') {
        // If the response is an object with a data property that is an array
        if ('data' in responseData && Array.isArray(responseData.data)) {
          data = responseData.data;
        }
        // If the response is an array directly
        else if (Array.isArray(responseData)) {
          data = responseData;
        }
        // If the response has results property that is an array (common in DRF pagination)
        else if ('results' in responseData && Array.isArray(responseData.results)) {
          data = responseData.results;
        }
        // If the response is an object with citizen data
        else if ('id' in responseData || 'first_name' in responseData) {
          data = [responseData];
        }
      }
      
      console.log(`Fetched ${data.length} citizens`);
      setCitizens(data);
      setSnackbarMessage(`Successfully loaded ${data.length} citizens`);
      setSnackbarOpen(true);
    } catch (error) {
      console.error('Error fetching citizens:', error);
      
      if (error instanceof Error) {
        setError(`Error: ${error.message}`);
      } else {
        setError('An unknown error occurred');
      }
    } finally {
      setLoading(false);
    }
  };
  
  // Fetch citizens on component mount
  useEffect(() => {
    fetchCitizens();
  }, []);
  
  // Handle page change
  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };
  
  // Handle rows per page change
  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };
  
  // Handle snackbar close
  const handleSnackbarClose = () => {
    setSnackbarOpen(false);
  };
  
  // Calculate age from birth date
  const calculateAge = (birthDate: string): number => {
    const today = new Date();
    const birthDateObj = new Date(birthDate);
    let age = today.getFullYear() - birthDateObj.getFullYear();
    const monthDiff = today.getMonth() - birthDateObj.getMonth();
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDateObj.getDate())) {
      age--;
    }
    
    return age;
  };
  
  // Get current page of citizens
  const currentCitizens = citizens.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage);
  
  return (
    <Box sx={{ py: 4 }}>
      <Snackbar
        open={snackbarOpen}
        autoHideDuration={6000}
        onClose={handleSnackbarClose}
        message={snackbarMessage}
      />
      
      <Container maxWidth="lg">
        <Paper sx={{ p: 3, mb: 3 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
            <Typography variant="h4" component="h1">
              <PersonIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
              Direct Citizens List
            </Typography>
            <Box>
              <Button
                variant="contained"
                color="primary"
                onClick={fetchCitizens}
                disabled={loading}
                startIcon={<RefreshIcon />}
              >
                {loading ? 'Loading...' : 'Refresh'}
              </Button>
            </Box>
          </Box>
          
          <Typography variant="body1" sx={{ mb: 3 }}>
            Using direct authentication for schema: <strong>{getDirectSchema()}</strong>
          </Typography>
          
          {error && (
            <Alert severity="error" sx={{ mb: 3 }}>
              {error}
            </Alert>
          )}
          
          {loading && !error ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
              <CircularProgress />
            </Box>
          ) : (
            <>
              <TableContainer>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>ID Number</TableCell>
                      <TableCell>Name</TableCell>
                      <TableCell>Gender</TableCell>
                      <TableCell>Age</TableCell>
                      <TableCell>Phone</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {currentCitizens.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={5} align="center">
                          <Typography variant="body1" sx={{ py: 3 }}>
                            No citizens found
                          </Typography>
                        </TableCell>
                      </TableRow>
                    ) : (
                      currentCitizens.map((citizen) => (
                        <TableRow key={citizen.id}>
                          <TableCell>{citizen.id_number || 'N/A'}</TableCell>
                          <TableCell>{`${citizen.first_name} ${citizen.last_name}`}</TableCell>
                          <TableCell>{citizen.gender}</TableCell>
                          <TableCell>{citizen.birth_date ? calculateAge(citizen.birth_date) : 'N/A'}</TableCell>
                          <TableCell>{citizen.phone || 'N/A'}</TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </TableContainer>
              
              <TablePagination
                rowsPerPageOptions={[5, 10, 25]}
                component="div"
                count={citizens.length}
                rowsPerPage={rowsPerPage}
                page={page}
                onPageChange={handleChangePage}
                onRowsPerPageChange={handleChangeRowsPerPage}
              />
            </>
          )}
        </Paper>
      </Container>
    </Box>
  );
};

export default DirectCitizensList;
