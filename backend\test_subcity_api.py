import os
import django
import requests
import json

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'neocamelot.settings')
django.setup()

# Import models
from centers.models import Client

# Print all tenants from the database
print("\n=== All Tenants in Database ===")
tenants = Client.objects.all()
for tenant in tenants:
    print(f"ID: {tenant.id}")
    print(f"Name: {tenant.name}")
    print(f"Schema Name: {tenant.schema_name}")
    print(f"Schema Type: {tenant.schema_type}")
    if tenant.parent:
        print(f"Parent: {tenant.parent.name} ({tenant.parent.schema_type})")
    else:
        print("Parent: None")
    print("-" * 30)

# Test the API endpoint for subcity registration
print("\n=== Testing API Endpoint for Subcity Registration ===")
try:
    response = requests.get("http://localhost:8000/api/available-tenants/?type=SUBCITY")
    print(f"Status Code: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        print(f"Response Data: {json.dumps(data, indent=2)}")
        print(f"Number of tenants returned: {len(data)}")
        for tenant in data:
            print(f"- {tenant['name']} (ID: {tenant['id']}, Type: {tenant['schema_type']}, Schema: {tenant['schema_name']})")
    else:
        print(f"Error: {response.text}")
except Exception as e:
    print(f"Error testing API: {str(e)}")
