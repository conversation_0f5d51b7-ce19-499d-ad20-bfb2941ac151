/**
 * Token Fix Utility
 *
 * This utility provides functions to fix token storage inconsistencies.
 * It can be used to ensure that tokens are stored consistently across
 * all storage locations.
 */

/**
 * Fix token storage inconsistencies
 * @returns {Object} Result of the fix operation
 */
export const fixTokenStorage = () => {
  try {
    console.group('Token Storage Fix');

    // Step 1: Get the current schema
    const schema = localStorage.getItem('jwt_schema') || localStorage.getItem('schema_name');
    if (!schema) {
      console.error('No schema found. Cannot fix tokens.');
      console.groupEnd();
      return { success: false, error: 'No schema found' };
    }
    console.log('Current schema:', schema);

    // Step 2: Get the tokens
    const accessToken = localStorage.getItem(`jwt_access_token_${schema}`) || localStorage.getItem('jwt_access_token');
    const refreshToken = localStorage.getItem(`jwt_refresh_token_${schema}`) || localStorage.getItem('jwt_refresh_token');

    // Check for tokens in cookies - prioritize refresh_token as it's more stable
    let cookieRefreshToken = null;
    let jwtRefreshTokenCookie = null;
    let refreshTokenCookie = null;

    document.cookie.split(';').forEach(cookie => {
      const [name, value] = cookie.trim().split('=');
      if (name === 'jwt_refresh_token') {
        jwtRefreshTokenCookie = decodeURIComponent(value);
      }
      if (name === 'refresh_token') {
        refreshTokenCookie = decodeURIComponent(value);
      }
    });

    // PRIORITIZE refresh_token cookie as it's more stable
    cookieRefreshToken = refreshTokenCookie || jwtRefreshTokenCookie;

    // Log cookie status
    console.log('Cookie status:');
    console.log('- refresh_token cookie (primary):', refreshTokenCookie ? 'Present' : 'Missing');
    console.log('- jwt_refresh_token cookie (secondary):', jwtRefreshTokenCookie ? 'Present' : 'Missing');

    console.log('Token sources:');
    console.log(`- jwt_access_token_${schema}: ${localStorage.getItem(`jwt_access_token_${schema}`) ? 'Present' : 'Missing'}`);
    console.log(`- jwt_access_token: ${localStorage.getItem('jwt_access_token') ? 'Present' : 'Missing'}`);
    console.log(`- jwt_refresh_token_${schema}: ${localStorage.getItem(`jwt_refresh_token_${schema}`) ? 'Present' : 'Missing'}`);
    console.log(`- jwt_refresh_token: ${localStorage.getItem('jwt_refresh_token') ? 'Present' : 'Missing'}`);
    console.log(`- Cookie refresh token: ${cookieRefreshToken ? 'Present' : 'Missing'}`);

    if (!accessToken) {
      console.error('No access token found. Cannot fix.');
      console.groupEnd();
      return { success: false, error: 'No access token found' };
    }

    // Use cookie refresh token if available
    const finalRefreshToken = refreshToken || cookieRefreshToken;

    if (!finalRefreshToken) {
      console.error('No refresh token found. Cannot fix.');
      console.groupEnd();
      return { success: false, error: 'No refresh token found' };
    }
    console.log('Found tokens for schema:', schema);

    // Step 3: Clear all token-related items
    const keysToRemove = [];
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && (key.includes('jwt_access_token') || key.includes('jwt_refresh_token'))) {
        keysToRemove.push(key);
      }
    }
    keysToRemove.forEach(key => {
      console.log('Removing:', key);
      localStorage.removeItem(key);
    });

    // Step 4: Store tokens consistently
    const formattedSchema = schema.replace(/\s+/g, '_');

    // Set schema names consistently
    localStorage.setItem('jwt_schema', formattedSchema);
    localStorage.setItem('schema_name', formattedSchema);
    localStorage.setItem('current_schema', formattedSchema);
    console.log('Set schema name:', formattedSchema);

    // Set access tokens
    localStorage.setItem('jwt_access_token', accessToken);
    localStorage.setItem(`jwt_access_token_${formattedSchema}`, accessToken);
    console.log('Set access tokens');

    // Set refresh tokens
    localStorage.setItem('jwt_refresh_token', finalRefreshToken);
    localStorage.setItem(`jwt_refresh_token_${formattedSchema}`, finalRefreshToken);
    console.log('Set refresh tokens');

    // Step 5: Set cookies for refresh token (both names for compatibility)
    const expirationDate = new Date();
    expirationDate.setDate(expirationDate.getDate() + 30);

    // First, clear any existing cookies to avoid conflicts
    document.cookie = `jwt_refresh_token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT; SameSite=Lax`;
    document.cookie = `refresh_token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT; SameSite=Lax`;

    // Set the jwt_refresh_token cookie with secure settings
    document.cookie = `jwt_refresh_token=${encodeURIComponent(finalRefreshToken)}; path=/; expires=${expirationDate.toUTCString()}; SameSite=Lax`;

    // Set the refresh_token cookie (legacy name) with the same settings
    document.cookie = `refresh_token=${encodeURIComponent(finalRefreshToken)}; path=/; expires=${expirationDate.toUTCString()}; SameSite=Lax`;

    // Clear any schema_name cookie that might be set to 'public'
    document.cookie = `schema_name=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT; SameSite=Lax`;

    // Verify cookies were set
    let jwtCookieSet = false;
    let refreshCookieSet = false;

    document.cookie.split(';').forEach(cookie => {
      const [name, value] = cookie.trim().split('=');
      if (name === 'jwt_refresh_token') jwtCookieSet = true;
      if (name === 'refresh_token') refreshCookieSet = true;
    });

    console.log('Cookie verification:');
    console.log('- jwt_refresh_token cookie set:', jwtCookieSet);
    console.log('- refresh_token cookie set:', refreshCookieSet);
    console.log('Set cookies');

    console.log('Token storage fixed successfully!');
    console.groupEnd();

    // Return success
    return {
      success: true,
      schema: formattedSchema,
      accessTokenLength: accessToken.length,
      refreshTokenLength: finalRefreshToken.length,
      tokenSources: {
        accessTokenFromSchemaSpecific: !!localStorage.getItem(`jwt_access_token_${schema}`),
        accessTokenFromGeneral: !!localStorage.getItem('jwt_access_token'),
        refreshTokenFromSchemaSpecific: !!localStorage.getItem(`jwt_refresh_token_${schema}`),
        refreshTokenFromGeneral: !!localStorage.getItem('jwt_refresh_token'),
        refreshTokenFromCookie: !!cookieRefreshToken
      }
    };
  } catch (error) {
    console.error('Error fixing token storage:', error);
    console.groupEnd();
    return { success: false, error: error.message };
  }
};

/**
 * Preserve cookies when navigating between pages
 * This function should be called before navigation
 * @returns {Object} Result of the preservation operation
 */
export const preserveCookies = () => {
  try {
    console.group('Preserve Cookies');

    // Check for refresh tokens in cookies
    let jwtRefreshTokenCookie = null;
    let refreshTokenCookie = null;

    document.cookie.split(';').forEach(cookie => {
      const [name, value] = cookie.trim().split('=');
      if (name === 'jwt_refresh_token') {
        jwtRefreshTokenCookie = decodeURIComponent(value);
      }
      if (name === 'refresh_token') {
        refreshTokenCookie = decodeURIComponent(value);
      }
    });

    console.log('Current cookie status:');
    console.log('- jwt_refresh_token cookie:', jwtRefreshTokenCookie ? 'Present' : 'Missing');
    console.log('- refresh_token cookie:', refreshTokenCookie ? 'Present' : 'Missing');

    // If either cookie is missing but the other is present, restore it
    if (jwtRefreshTokenCookie && !refreshTokenCookie) {
      console.log('Restoring missing refresh_token cookie from jwt_refresh_token');
      const expirationDate = new Date();
      expirationDate.setDate(expirationDate.getDate() + 30);
      document.cookie = `refresh_token=${encodeURIComponent(jwtRefreshTokenCookie)}; path=/; expires=${expirationDate.toUTCString()}; SameSite=Lax`;
    } else if (!jwtRefreshTokenCookie && refreshTokenCookie) {
      console.log('Restoring missing jwt_refresh_token cookie from refresh_token');
      const expirationDate = new Date();
      expirationDate.setDate(expirationDate.getDate() + 30);
      document.cookie = `jwt_refresh_token=${encodeURIComponent(refreshTokenCookie)}; path=/; expires=${expirationDate.toUTCString()}; SameSite=Lax`;
    }

    // Verify cookies after restoration
    let jwtCookieSet = false;
    let refreshCookieSet = false;

    document.cookie.split(';').forEach(cookie => {
      const [name, value] = cookie.trim().split('=');
      if (name === 'jwt_refresh_token') jwtCookieSet = true;
      if (name === 'refresh_token') refreshCookieSet = true;
    });

    console.log('Cookie verification after preservation:');
    console.log('- jwt_refresh_token cookie set:', jwtCookieSet);
    console.log('- refresh_token cookie set:', refreshCookieSet);

    console.groupEnd();
    return {
      success: true,
      jwtCookieSet,
      refreshCookieSet
    };
  } catch (error) {
    console.error('Error preserving cookies:', error);
    console.groupEnd();
    return { success: false, error: error.message };
  }
};

/**
 * Protect token storage from being cleared
 * @returns {Object} Result of the protection operation
 */
export const protectTokenStorage = () => {
  try {
    console.group('Protect Token Storage');

    // Get current tokens
    const schema = localStorage.getItem('jwt_schema') || localStorage.getItem('schema_name');
    const tokens = {
      jwt_schema: schema,
      schema_name: schema,
      jwt_access_token: localStorage.getItem('jwt_access_token'),
      jwt_refresh_token: localStorage.getItem('jwt_refresh_token')
    };

    if (schema) {
      tokens[`jwt_access_token_${schema}`] = localStorage.getItem(`jwt_access_token_${schema}`);
      tokens[`jwt_refresh_token_${schema}`] = localStorage.getItem(`jwt_refresh_token_${schema}`);
    }

    console.log('Protected tokens:', tokens);

    // Override removeItem to protect token-related items
    const originalRemoveItem = localStorage.removeItem;
    localStorage.removeItem = function(key) {
      if (key && (key.includes('jwt_') || key === 'schema_name')) {
        console.warn(`Prevented removal of ${key}`);
        return;
      }
      originalRemoveItem.apply(this, arguments);
    };

    // Override clear to protect token-related items
    const originalClear = localStorage.clear;
    localStorage.clear = function() {
      console.warn('Prevented localStorage.clear()');

      // Instead of clearing everything, we'll clear non-token items
      const keysToRemove = [];
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key && !key.includes('jwt_') && key !== 'schema_name') {
          keysToRemove.push(key);
        }
      }

      keysToRemove.forEach(key => {
        originalRemoveItem.call(localStorage, key);
      });
    };

    console.log('Token storage protected. Token-related items cannot be removed.');
    console.groupEnd();

    return { success: true, protected: Object.keys(tokens) };
  } catch (error) {
    console.error('Error protecting token storage:', error);
    console.groupEnd();
    return { success: false, error: error.message };
  }
};

/**
 * Show token information
 * @param {string} schema Optional schema name
 * @returns {Object} Token information
 */
export const showTokenInfo = (schema) => {
  try {
    console.group('Token Information');

    // Get the schema to use
    const schemaToUse = schema || localStorage.getItem('jwt_schema') || localStorage.getItem('schema_name');
    if (!schemaToUse) {
      console.error('No schema provided or current schema found');
      console.groupEnd();
      return { success: false, error: 'No schema found' };
    }

    console.log('Schema:', schemaToUse);

    // Check all possible token sources
    const accessTokenSources = {
      schemaSpecific: localStorage.getItem(`jwt_access_token_${schemaToUse}`),
      general: localStorage.getItem('jwt_access_token')
    };

    const refreshTokenSources = {
      schemaSpecific: localStorage.getItem(`jwt_refresh_token_${schemaToUse}`),
      general: localStorage.getItem('jwt_refresh_token'),
      cookie: null
    };

    // Check for tokens in cookies
    document.cookie.split(';').forEach(cookie => {
      const [name, value] = cookie.trim().split('=');
      if (name === 'jwt_refresh_token' || name === 'refresh_token') {
        refreshTokenSources.cookie = decodeURIComponent(value);
      }
    });

    console.log('Access Token Sources:');
    console.log(`- jwt_access_token_${schemaToUse}: ${accessTokenSources.schemaSpecific ? 'Present' : 'Missing'}`);
    console.log(`- jwt_access_token: ${accessTokenSources.general ? 'Present' : 'Missing'}`);

    console.log('Refresh Token Sources:');
    console.log(`- jwt_refresh_token_${schemaToUse}: ${refreshTokenSources.schemaSpecific ? 'Present' : 'Missing'}`);
    console.log(`- jwt_refresh_token: ${refreshTokenSources.general ? 'Present' : 'Missing'}`);
    console.log(`- Cookie refresh token: ${refreshTokenSources.cookie ? 'Present' : 'Missing'}`);

    const accessToken = accessTokenSources.schemaSpecific || accessTokenSources.general;
    if (!accessToken) {
      console.error('No access token found for schema:', schemaToUse);
      console.groupEnd();
      return { success: false, error: 'No access token found' };
    }

    try {
      // JWT tokens are in the format header.payload.signature
      const parts = accessToken.split('.');
      if (parts.length !== 3) {
        console.error('Invalid JWT token format');
        return { success: false, error: 'Invalid token format' };
      }

      // Decode the payload (middle part)
      const payload = JSON.parse(atob(parts[1]));
      console.group('JWT Token Information');
      console.log('Schema:', schemaToUse);
      console.log('Payload:', payload);

      // Check expiration
      if (payload.exp) {
        const expirationDate = new Date(payload.exp * 1000);
        const now = new Date();
        const isExpired = expirationDate < now;
        console.log('Expiration:', expirationDate.toLocaleString());
        console.log('Is Expired:', isExpired);
        console.log('Time Remaining:', isExpired ? 'Expired' : `${Math.floor((expirationDate.getTime() - now.getTime()) / 1000)} seconds`);
      }

      // Check refresh token
      const refreshToken = refreshTokenSources.schemaSpecific || refreshTokenSources.general || refreshTokenSources.cookie;

      // Try to decode refresh token if available
      let refreshPayload = null;
      if (refreshToken) {
        try {
          const refreshParts = refreshToken.split('.');
          if (refreshParts.length === 3) {
            refreshPayload = JSON.parse(atob(refreshParts[1]));
            console.log('Refresh Token Payload:', refreshPayload);

            if (refreshPayload.exp) {
              const refreshExpirationDate = new Date(refreshPayload.exp * 1000);
              const now = new Date();
              const isRefreshExpired = refreshExpirationDate < now;
              console.log('Refresh Token Expiration:', refreshExpirationDate.toLocaleString());
              console.log('Refresh Token Is Expired:', isRefreshExpired);
              console.log('Refresh Token Time Remaining:', isRefreshExpired ? 'Expired' : `${Math.floor((refreshExpirationDate.getTime() - now.getTime()) / 1000)} seconds`);
            }
          }
        } catch (refreshError) {
          console.error('Error decoding refresh token:', refreshError);
        }
      }

      console.groupEnd();

      return {
        success: true,
        schema: schemaToUse,
        accessToken: {
          payload,
          expiration: payload.exp ? new Date(payload.exp * 1000).toLocaleString() : 'Not set',
          isExpired: payload.exp ? new Date(payload.exp * 1000) < new Date() : false,
          sources: {
            schemaSpecific: !!accessTokenSources.schemaSpecific,
            general: !!accessTokenSources.general
          }
        },
        refreshToken: refreshToken ? {
          payload: refreshPayload,
          expiration: refreshPayload?.exp ? new Date(refreshPayload.exp * 1000).toLocaleString() : 'Not set',
          isExpired: refreshPayload?.exp ? new Date(refreshPayload.exp * 1000) < new Date() : false,
          sources: {
            schemaSpecific: !!refreshTokenSources.schemaSpecific,
            general: !!refreshTokenSources.general,
            cookie: !!refreshTokenSources.cookie
          }
        } : null
      };
    } catch (error) {
      console.error('Error decoding JWT token:', error);
      console.groupEnd();
      return { success: false, error: 'Error decoding token' };
    }
  } catch (error) {
    console.error('Error showing token info:', error);
    if (console.groupEnd) console.groupEnd();
    return { success: false, error: error.message };
  }
};

// Add to window object for easy access from console
if (typeof window !== 'undefined') {
  window.tokenFix = {
    fix: fixTokenStorage,
    protect: protectTokenStorage,
    preserve: preserveCookies,
    showInfo: showTokenInfo
  };
}

export default {
  fixTokenStorage,
  protectTokenStorage,
  preserveCookies,
  showTokenInfo
};
