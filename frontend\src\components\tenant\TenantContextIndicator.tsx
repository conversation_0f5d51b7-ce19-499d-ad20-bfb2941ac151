import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Chip,
  Tooltip,
  IconButton,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
  Divider,
  Badge,
  CircularProgress
} from '@mui/material';
import LocationCityIcon from '@mui/icons-material/LocationCity';
import ApartmentIcon from '@mui/icons-material/Apartment';
import HomeIcon from '@mui/icons-material/Home';
import InfoIcon from '@mui/icons-material/Info';
import SyncIcon from '@mui/icons-material/Sync';
import { useNavigate } from 'react-router-dom';
import { getCurrentSchema } from '../../services/tokenService';
import { switchTenantContext, getTenantInfo, TenantInfo } from '../../utils/tenantContextManager';

// Using TenantInfo interface imported from tenantContextManager

const TenantContextIndicator: React.FC = () => {
  const navigate = useNavigate();
  const [tenantInfo, setTenantInfo] = useState<TenantInfo | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [menuAnchorEl, setMenuAnchorEl] = useState<null | HTMLElement>(null);
  const [refreshing, setRefreshing] = useState(false);

  // Load tenant info on mount - with safeguards
  useEffect(() => {
    console.log('TenantContextIndicator: Loading tenant info on mount');

    // Set a flag in sessionStorage to prevent multiple initializations
    const isInitialized = sessionStorage.getItem('tenant_indicator_initialized');

    if (!isInitialized) {
      sessionStorage.setItem('tenant_indicator_initialized', 'true');
      loadTenantInfo();
    }
  }, []);

  // Load tenant info
  const loadTenantInfo = async () => {
    try {
      setLoading(true);
      setError(null);

      // Get the current schema
      const schema = getCurrentSchema();

      if (!schema) {
        setError('No tenant context found');
        setLoading(false);
        return;
      }

      // Get tenant info
      const info = await getTenantInfo(schema);

      if (info) {
        setTenantInfo(info);
      } else {
        setError('Failed to load tenant info');
      }
    } catch (err) {
      console.error('Error loading tenant info:', err);
      setError('Error loading tenant info');
    } finally {
      setLoading(false);
    }
  };

  // Handle menu open
  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setMenuAnchorEl(event.currentTarget);
  };

  // Handle menu close
  const handleMenuClose = () => {
    setMenuAnchorEl(null);
  };

  // Handle tenant selection page
  const handleTenantSelection = () => {
    handleMenuClose();
    navigate('/tenant-selection');
  };

  // Handle refresh tenant context
  const handleRefreshContext = async () => {
    try {
      setRefreshing(true);
      handleMenuClose();

      // Get the current schema
      const schema = getCurrentSchema();

      if (!schema) {
        setError('No tenant context found');
        return;
      }

      // Switch to the tenant context
      const success = await switchTenantContext(schema);

      if (success) {
        // Reload tenant info
        await loadTenantInfo();
      } else {
        setError('Failed to refresh tenant context');
      }
    } catch (err) {
      console.error('Error refreshing tenant context:', err);
      setError('Error refreshing tenant context');
    } finally {
      setRefreshing(false);
    }
  };

  // Get tenant icon based on tenant type
  const getTenantIcon = () => {
    if (!tenantInfo) return <LocationCityIcon fontSize="small" />;

    switch (tenantInfo.schema_type) {
      case 'CITY':
        return <LocationCityIcon fontSize="small" />;
      case 'SUBCITY':
        return <ApartmentIcon fontSize="small" />;
      case 'KEBELE':
        return <HomeIcon fontSize="small" />;
      default:
        return <LocationCityIcon fontSize="small" />;
    }
  };

  // Get tenant color based on tenant type
  const getTenantColor = (): 'default' | 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning' => {
    if (!tenantInfo) return 'default';

    switch (tenantInfo.schema_type) {
      case 'CITY':
        return 'primary';
      case 'SUBCITY':
        return 'secondary';
      case 'KEBELE':
        return 'success';
      default:
        return 'default';
    }
  };

  return (
    <Box sx={{ display: 'flex', alignItems: 'center' }}>
      {loading ? (
        <CircularProgress size={24} sx={{ mx: 1 }} />
      ) : error ? (
        <Tooltip title={error}>
          <Chip
            icon={<InfoIcon />}
            label="Error"
            color="error"
            size="small"
            onClick={handleMenuOpen}
            sx={{ cursor: 'pointer' }}
          />
        </Tooltip>
      ) : (
        <Tooltip title={`Current tenant: ${tenantInfo?.name || 'Unknown'}`}>
          <Chip
            icon={getTenantIcon()}
            label={tenantInfo?.name || 'Unknown'}
            color={getTenantColor()}
            size="small"
            onClick={handleMenuOpen}
            sx={{ cursor: 'pointer' }}
          />
        </Tooltip>
      )}

      <Menu
        anchorEl={menuAnchorEl}
        open={Boolean(menuAnchorEl)}
        onClose={handleMenuClose}
        PaperProps={{
          elevation: 2,
          sx: {
            mt: 1.5,
            minWidth: 200,
            borderRadius: 2,
            overflow: 'hidden'
          }
        }}
      >
        <Box sx={{ px: 2, py: 1.5, bgcolor: 'primary.main', color: 'white' }}>
          <Typography variant="subtitle2" fontWeight="500">
            Tenant Context
          </Typography>
          <Typography variant="caption" sx={{ opacity: 0.8 }}>
            {tenantInfo?.schema_name || 'Unknown'}
          </Typography>
        </Box>

        <MenuItem onClick={handleTenantSelection} sx={{ py: 1.5 }}>
          <ListItemIcon>
            <LocationCityIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText primary="Switch Tenant" />
        </MenuItem>

        <MenuItem onClick={handleRefreshContext} sx={{ py: 1.5 }} disabled={refreshing}>
          <ListItemIcon>
            {refreshing ? (
              <CircularProgress size={20} />
            ) : (
              <SyncIcon fontSize="small" />
            )}
          </ListItemIcon>
          <ListItemText primary="Refresh Context" />
        </MenuItem>

        <Divider />

        <Box sx={{ px: 2, py: 1, bgcolor: 'grey.100' }}>
          <Typography variant="caption" color="text.secondary">
            Current tenant: {tenantInfo?.schema_type || 'Unknown'}
          </Typography>
        </Box>
      </Menu>
    </Box>
  );
};

export default TenantContextIndicator;
