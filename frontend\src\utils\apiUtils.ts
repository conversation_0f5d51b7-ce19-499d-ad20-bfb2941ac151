/**
 * API Utilities
 *
 * This module provides utilities for making API requests with proper authentication.
 * Supports both JWT and legacy token authentication.
 */

import { createTenantApiUrl } from '../config/api';
import { getAuthHeaders, validateJWTToken, refreshJWTTokens } from '../services/tokenService';

/**
 * Make an authenticated request to a tenant-specific endpoint
 * @param schema The tenant schema name
 * @param endpoint The API endpoint
 * @param options The fetch options
 * @returns Promise with the response
 */
export const fetchTenantApi = async (
  schema: string,
  endpoint: string,
  options: RequestInit = {}
): Promise<Response> => {
  try {
    // First try JWT authentication
    try {
      console.log('fetchTenantApi: Trying JWT authentication');

      // Get authentication headers
      const authHeaders = getAuthHeaders(schema);

      // Create the URL - use underscores in the URL
      const urlSchema = schema.replace(/\s+/g, '_');
      const url = createTenantApiUrl(urlSchema, endpoint);

      // Log the schema details
      console.log('fetchTenantApi: Using schema in headers:', schema);
      console.log('fetchTenantApi: Using schema in URL:', urlSchema);

      // Create headers
      const headers = new Headers(options.headers || {});

      // Add authentication headers
      Object.entries(authHeaders).forEach(([key, value]) => {
        headers.set(key, value);
      });

      // Ensure Content-Type is set
      if (!headers.has('Content-Type')) {
        headers.set('Content-Type', 'application/json');
      }

      // Log the headers
      console.log('fetchTenantApi: Headers:', Object.fromEntries([...headers.entries()]));

      // Set the schema name as a cookie
      document.cookie = `schema_name=${encodeURIComponent(schema)}; path=/; SameSite=Lax`;

      return await makeRequestWithJWT(url, schema, headers, options);
    } catch (jwtError) {
      console.error('fetchTenantApi: JWT authentication failed:', jwtError);
      console.log('fetchTenantApi: Falling back to legacy token authentication');
    }

    // Fall back to legacy token authentication
    console.log('fetchTenantApi: Using legacy token authentication');

    // Get the token from localStorage or tokenStore
    let token = localStorage.getItem('token');

    // Try to get token from token service
    try {
      const { getAccessTokenForSchema } = await import('../services/tokenService');
      const schemaToken = getAccessTokenForSchema(schema);
      if (schemaToken) {
        console.log('fetchTenantApi: Using token from token service');
        token = schemaToken;
      }
    } catch (error) {
      console.warn('fetchTenantApi: Could not import tokenService:', error);
    }

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    // Create the URL - use underscores in the URL
    const urlSchema = schema.replace(/\s+/g, '_');
    const url = createTenantApiUrl(urlSchema, endpoint);

    // Log the schema details
    console.log('fetchTenantApi: Using schema in headers:', schema);
    console.log('fetchTenantApi: Using schema in URL:', urlSchema);

    // Create headers
    const headers = new Headers(options.headers || {});

    // Clean the token thoroughly - remove ALL spaces, not just leading/trailing
    const cleanToken = token.replace(/\s+/g, '');

    // Add the token to the headers - use Bearer prefix for JWT tokens
    const tokenWithPrefix = cleanToken.includes('.') ? `Bearer ${cleanToken}` : `Bearer ${cleanToken}`;
    headers.set('Authorization', tokenWithPrefix);
    console.log('fetchTenantApi: Added Authorization header with Bearer token');

    // Add the schema name to the headers
    headers.set('X-Schema-Name', schema);

    // Log the exact token being used (first 10 chars for security)
    console.log(`fetchTenantApi: Using cleaned token: ${cleanToken.substring(0, 10)}...`);

    // Set the schema name as a cookie
    document.cookie = `schema_name=${encodeURIComponent(schema)}; path=/; SameSite=Lax`;

    // Log the request details
    console.log(`fetchTenantApi: Making request to ${url}`);
    console.log('fetchTenantApi: Headers:', Object.fromEntries(headers.entries()));

    // Make the request
    const response = await fetch(url, {
      ...options,
      headers,
      credentials: 'include'
    });

    // Log the response status
    console.log(`fetchTenantApi: Response status: ${response.status}`);

    // If we get a 401 Unauthorized, try to refresh the token
    if (response.status === 401) {
      console.log('fetchTenantApi: Got 401 Unauthorized, attempting to refresh token');

      try {
        // Try to refresh the token
        const { refreshToken } = await import('./tokenRefreshManager');
        await refreshToken(schema);

        // Get the new token
        const { getAccessTokenForSchema } = await import('../services/tokenService');
        const newToken = getAccessTokenForSchema(schema);

        if (newToken) {
          console.log('fetchTenantApi: Token refreshed, retrying request');

          // Create new headers with the new token
          const newHeaders = new Headers(options.headers || {});
          // Use Bearer prefix for JWT tokens
          const tokenWithPrefix = newToken.includes('.') ? `Bearer ${newToken}` : `Bearer ${newToken}`;
          newHeaders.set('Authorization', tokenWithPrefix);
          newHeaders.set('X-Schema-Name', schema);

          // Retry the request with the new token
          const newResponse = await fetch(url, {
            ...options,
            headers: newHeaders,
            credentials: 'include'
          });

          console.log(`fetchTenantApi: Retry response status: ${newResponse.status}`);
          return newResponse;
        }
      } catch (refreshError) {
        console.error('fetchTenantApi: Error refreshing token:', refreshError);
      }
    }

    return response;
  } catch (error) {
    console.error('fetchTenantApi: Error making request:', error);
    throw error;
  }
};

/**
 * Make an authenticated request to a common API endpoint
 * @param endpoint The API endpoint
 * @param options The fetch options
 * @returns Promise with the response
 */
export const fetchCommonApi = async (
  endpoint: string,
  options: RequestInit = {}
): Promise<Response> => {
  try {
    // Get the token from localStorage or tokenStore
    let token = localStorage.getItem('token');

    // Get token from token service
    try {
      const { getAccessTokenForSchema, getCurrentSchema } = await import('../services/tokenService');
      const schema = getCurrentSchema();
      if (schema) {
        const accessToken = getAccessTokenForSchema(schema);
        if (accessToken) {
          console.log('fetchCommonApi: Using token from token service');
          token = accessToken;
        }
      }
    } catch (error) {
      console.warn('fetchCommonApi: Could not get token from token service:', error);
    }

    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    // Create the URL
    const url = `/api/common/${endpoint}`;

    // Create headers
    const headers = new Headers(options.headers || {});

    // Clean the token thoroughly - remove ALL spaces, not just leading/trailing
    const cleanToken = token.replace(/\s+/g, '');

    // Add the token to the headers - use Bearer prefix for JWT tokens
    const tokenWithPrefix = cleanToken.includes('.') ? `Bearer ${cleanToken}` : `Bearer ${cleanToken}`;
    headers.set('Authorization', tokenWithPrefix);
    console.log('fetchCommonApi: Added Authorization header with Bearer token');

    // Log the cleaned token (first 10 chars for security)
    console.log(`fetchCommonApi: Using cleaned token: ${cleanToken.substring(0, 10)}...`);

    // Get the current schema
    let schema = localStorage.getItem('schema_name');
    if (schema) {
      // Add the schema name to the headers
      headers.set('X-Schema-Name', schema);
      console.log(`fetchCommonApi: Using schema: ${schema}`);

      // Set the schema name as a cookie
      document.cookie = `schema_name=${encodeURIComponent(schema)}; path=/; SameSite=Lax`;
    }

    // Log the request details
    console.log(`fetchCommonApi: Making request to ${url}`);
    console.log('fetchCommonApi: Headers:', Object.fromEntries(headers.entries()));

    // Make the request
    const response = await fetch(url, {
      ...options,
      headers,
      credentials: 'include'
    });

    // Log the response status
    console.log(`fetchCommonApi: Response status: ${response.status}`);

    // If we get a 401 Unauthorized, try to refresh the token
    if (response.status === 401) {
      console.log('fetchCommonApi: Got 401 Unauthorized, attempting to refresh token');

      try {
        // Try to refresh the token
        const { refreshToken } = await import('./tokenRefreshManager');
        const currentSchema = schema || 'public';
        await refreshToken(currentSchema);

        // Get the new token
        const { getAccessTokenForSchema } = await import('../services/tokenService');
        const newToken = getAccessTokenForSchema(currentSchema);

        if (newToken) {
          console.log('fetchCommonApi: Token refreshed, retrying request');

          // Create new headers with the new token
          const newHeaders = new Headers(options.headers || {});
          // Use Bearer prefix for JWT tokens
          const tokenWithPrefix = newToken.includes('.') ? `Bearer ${newToken}` : `Bearer ${newToken}`;
          newHeaders.set('Authorization', tokenWithPrefix);

          if (schema) {
            newHeaders.set('X-Schema-Name', schema);
          }

          // Retry the request with the new token
          const newResponse = await fetch(url, {
            ...options,
            headers: newHeaders,
            credentials: 'include'
          });

          console.log(`fetchCommonApi: Retry response status: ${newResponse.status}`);
          return newResponse;
        }
      } catch (refreshError) {
        console.error('fetchCommonApi: Error refreshing token:', refreshError);
      }
    }

    return response;
  } catch (error) {
    console.error('fetchCommonApi: Error making request:', error);
    throw error;
  }
};

/**
 * Ensure the token is properly set in localStorage and unified token manager
 * @returns true if the token is set, false otherwise
 */
export const ensureToken = async (): Promise<boolean> => {
  try {
    // Get token from localStorage
    let token = localStorage.getItem('token');
    let schema = localStorage.getItem('schema_name');
    let tokenFound = false;

    // Try to get token from token service
    try {
      const { getAccessTokenForSchema, getCurrentSchema } = await import('../services/tokenService');

      // If we don't have a schema, try to get it from the token service
      if (!schema) {
        schema = getCurrentSchema();
        console.log('ensureToken: Got schema from token service:', schema);
      }

      // If we have a schema, try to get the token for it
      if (schema) {
        const schemaToken = getAccessTokenForSchema(schema);
        if (schemaToken) {
          console.log('ensureToken: Using token from token service for schema:', schema);
          token = schemaToken;
          tokenFound = true;
        }
      }
    } catch (error) {
      console.warn('ensureToken: Could not import tokenService:', error);
    }

    if (!token) {
      console.error('No token found in localStorage or unified token manager');
      return false;
    }

    // Clean the token thoroughly - remove ALL spaces, not just leading/trailing
    const cleanToken = token.replace(/\s+/g, '');

    // Update the token in localStorage
    localStorage.setItem('token', cleanToken);
    console.log('ensureToken: Cleaned token and saved to localStorage');

    // If we have a schema, try to save the token for it in the token service
    if (schema && !tokenFound) {
      try {
        const { storeTokensForSchema } = await import('../services/tokenService');
        storeTokensForSchema(schema, cleanToken, '');
        console.log('ensureToken: Saved token to token service for schema:', schema);
      } catch (error) {
        console.warn('ensureToken: Could not save token to token service:', error);
      }
    }

    return true;
  } catch (error) {
    console.error('ensureToken: Error ensuring token:', error);
    return false;
  }
};

/**
 * Ensure the schema name is properly set in localStorage, cookies, and unified token manager
 * @param schema The schema name to set
 * @returns true if the schema is set, false otherwise
 */
export const ensureSchema = async (schema: string): Promise<boolean> => {
  try {
    if (!schema) {
      console.error('No schema provided');

      // Try to get the schema from the tenant object
      try {
        const tenantStr = localStorage.getItem('tenant');
        if (tenantStr) {
          const tenant = JSON.parse(tenantStr);
          if (tenant && tenant.schema_name) {
            schema = tenant.schema_name;
            console.log('ensureSchema: Using schema from tenant object:', schema);
          }
        }
      } catch (error) {
        console.error('ensureSchema: Error parsing tenant from localStorage:', error);
      }

      // Try to get the schema from the token service
      if (!schema) {
        try {
          const { getCurrentSchema } = await import('../services/tokenService');
          const currentSchema = getCurrentSchema();
          if (currentSchema) {
            schema = currentSchema;
            console.log('ensureSchema: Using schema from token service:', schema);
          }
        } catch (error) {
          console.warn('ensureSchema: Could not import tokenService:', error);
        }
      }

      // If we still don't have a schema, return false
      if (!schema) {
        return false;
      }
    }

    // Set the schema name in localStorage
    localStorage.setItem('schema_name', schema);
    console.log('ensureSchema: Saved schema to localStorage:', schema);

    // Set the schema name as a cookie
    document.cookie = `schema_name=${encodeURIComponent(schema)}; path=/; SameSite=Lax`;
    console.log('ensureSchema: Set schema cookie:', schema);

    // Try to switch to the tenant context
    try {
      const { switchTenantContext } = await import('./tenantContextManager');
      const success = await switchTenantContext(schema);
      if (success) {
        console.log('ensureSchema: Successfully switched to tenant context:', schema);
      } else {
        console.warn('ensureSchema: Failed to switch to tenant context:', schema);
      }
    } catch (error) {
      console.warn('ensureSchema: Could not import tenantContextManager:', error);
    }

    return true;
  } catch (error) {
    console.error('ensureSchema: Error ensuring schema:', error);
    return false;
  }
};

/**
 * Make a request with JWT authentication
 * @param url The URL to request
 * @param schema The schema name
 * @param headers The request headers
 * @param options The fetch options
 * @returns Promise with the response
 */
export const makeRequestWithJWT = async (
  url: string,
  schema: string,
  headers: Headers,
  options: RequestInit = {}
): Promise<Response> => {
  try {
    // Make the request
    console.log(`makeRequestWithJWT: Making request to ${url}`);
    console.log('makeRequestWithJWT: Headers:', Object.fromEntries([...headers.entries()]));

    let response = await fetch(url, {
      ...options,
      headers,
      credentials: 'include'
    });

    // Log the response status
    console.log(`makeRequestWithJWT: Response status: ${response.status}`);

    // If we get a 401 Unauthorized, try to refresh the token
    if (response.status === 401) {
      console.log('makeRequestWithJWT: Got 401 Unauthorized, attempting to refresh token');

      try {
        // Try to refresh the token
        const refreshResult = await refreshJWTTokens(schema);

        if (refreshResult && refreshResult.access_token) {
          console.log('makeRequestWithJWT: Token refreshed, retrying request');

          // Get fresh authentication headers after token refresh
          const newAuthHeaders = getAuthHeaders(schema);

          // Create new headers with the new token
          const newHeaders = new Headers(options.headers || {});

          // Add authentication headers
          Object.entries(newAuthHeaders).forEach(([key, value]) => {
            newHeaders.set(key, value);
          });

          // Ensure Content-Type is set
          if (!newHeaders.has('Content-Type')) {
            newHeaders.set('Content-Type', 'application/json');
          }

          console.log('makeRequestWithJWT: New headers:', Object.fromEntries([...newHeaders.entries()]));

          // Retry the request with the new token
          const newResponse = await fetch(url, {
            ...options,
            headers: newHeaders,
            credentials: 'include'
          });

          console.log(`makeRequestWithJWT: Retry response status: ${newResponse.status}`);
          return newResponse;
        }
      } catch (refreshError) {
        console.error('makeRequestWithJWT: Error refreshing token:', refreshError);
      }
    }

    return response;
  } catch (error) {
    console.error('makeRequestWithJWT: Error making request:', error);
    throw error;
  }
};

export default {
  fetchTenantApi,
  fetchCommonApi,
  ensureToken,
  ensureSchema,
  makeRequestWithJWT
};
