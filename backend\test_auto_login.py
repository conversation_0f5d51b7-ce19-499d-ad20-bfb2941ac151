import requests
import json

def test_auto_login():
    """Test the automatic tenant selection login functionality."""
    print("\n=== Testing Automatic Tenant Selection Login ===")

    # Test login with a tenant user
    print("\n1. Testing login with a tenant user (kebele admin):")
    login_data = {
        "email": "<EMAIL>",  # This is a kebele admin from our new test
        "password": "password123"
    }

    response = requests.post(
        "http://localhost:8000/api/login/",
        headers={"Content-Type": "application/json"},
        json=login_data
    )

    print(f"Status Code: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        print(f"Response: {json.dumps(data, indent=2)}")

        # Check if tenant info is included
        if "tenant" in data:
            print(f"\nSuccessfully logged in to tenant: {data['tenant']['name']} (Schema: {data['tenant']['schema_name']})")
        else:
            print("\nLogin successful but no tenant information was returned")
    else:
        print(f"Error: {response.text}")

    # Test login with a superadmin
    print("\n2. Testing login with a superadmin:")
    login_data = {
        "email": "<EMAIL>",  # This should be a superadmin in the public schema
        "password": "admin123"
    }

    response = requests.post(
        "http://localhost:8000/api/login/",
        headers={"Content-Type": "application/json"},
        json=login_data
    )

    print(f"Status Code: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        print(f"Response: {json.dumps(data, indent=2)}")

        # Check if superadmin flag is included
        if data.get("is_superadmin"):
            print("\nSuccessfully logged in as superadmin")
        else:
            print("\nLogin successful but not identified as superadmin")
    else:
        print(f"Error: {response.text}")

    # Test login with invalid credentials
    print("\n3. Testing login with invalid credentials:")
    login_data = {
        "email": "<EMAIL>",
        "password": "wrongpassword"
    }

    response = requests.post(
        "http://localhost:8000/api/login/",
        headers={"Content-Type": "application/json"},
        json=login_data
    )

    print(f"Status Code: {response.status_code}")
    print(f"Response: {response.text}")

if __name__ == "__main__":
    test_auto_login()
