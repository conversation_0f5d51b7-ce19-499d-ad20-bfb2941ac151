import os
import django
import sys

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'neocamelot.settings')
django.setup()

from django.db import connection
from django_tenants.utils import tenant_context, get_public_schema_name
from centers.models import Client
from django.contrib.auth import get_user_model
User = get_user_model()
from rest_framework.authtoken.models import Token

# Get the token from command line argument or use the default
token_key = '9425ec3171e23a01903152c33f7a212106916a4b'  # Default token to test
schema_name = 'subcity_zoble'  # Default schema to test

print(f"\nTesting token: {token_key} in schema: {schema_name}")

try:
    # Get the tenant by schema name
    tenant = Client.objects.get(schema_name=schema_name)
    print(f"Found tenant: {tenant.name} (ID: {tenant.id}, Type: {tenant.schema_type})")
    
    # Set the tenant for this request
    connection.set_tenant(tenant)
    print(f"Set tenant context to: {connection.schema_name}")
    
    # Use tenant_context to ensure we're querying the right database
    with tenant_context(tenant):
        # Try to find the token in the tenant's database
        try:
            token = Token.objects.get(key=token_key)
            user = token.user
            print(f"Found token in tenant {tenant.schema_name} for user {user.email} (ID: {user.id})")
            print(f"User is_active: {user.is_active}")
            print(f"User is_staff: {user.is_staff}")
            print(f"User is_superuser: {user.is_superuser}")
            print(f"User role: {user.role}")
        except Token.DoesNotExist:
            print(f"Token not found in tenant {tenant.schema_name}")
            
            # If not found, try the public schema
            print(f"Trying public schema...")
            connection.set_schema_to_public()
            try:
                token = Token.objects.get(key=token_key)
                user = token.user
                print(f"Found token in public schema for user {user.email} (ID: {user.id})")
                print(f"User is_active: {user.is_active}")
                print(f"User is_staff: {user.is_staff}")
                print(f"User is_superuser: {user.is_superuser}")
                print(f"User role: {user.role}")
                
                # Now switch back to the tenant schema
                connection.set_tenant(tenant)
                print(f"Set tenant context back to: {connection.schema_name}")
                
                # Try to find the user in the tenant's database
                try:
                    user = User.objects.get(email=token.user.email)
                    print(f"Found user in tenant {tenant.schema_name}: {user.email} (ID: {user.id})")
                except User.DoesNotExist:
                    print(f"User not found in tenant {tenant.schema_name}")
            except Token.DoesNotExist:
                print(f"Token not found in public schema")
except Client.DoesNotExist:
    print(f"Tenant with schema name '{schema_name}' not found.")
