from rest_framework import viewsets, permissions, status, filters
from rest_framework.decorators import action, api_view, permission_classes, authentication_classes
from rest_framework.response import Response
from django.contrib.auth import authenticate, logout
from django_filters.rest_framework import DjangoFilterBackend
from django.contrib.auth import get_user_model
from .serializers import UserSerializer, UserListSerializer
from django.conf import settings
import os
import logging
from django.shortcuts import render
from .jwt_utils import (
    generate_jwt_tokens,
    validate_jwt_token,
    refresh_jwt_tokens
)

logger = logging.getLogger(__name__)

User = get_user_model()

class IsCenterAdmin(permissions.BasePermission):
    """Permission to allow only center admins to perform certain actions."""

    def has_permission(self, request, view):
        return request.user.is_authenticated and request.user.is_center_admin

class IsAdminOrSuperAdmin(permissions.BasePermission):
    """Permission to allow center admins or super admins."""

    def has_permission(self, request, view):
        return request.user.is_authenticated and (request.user.is_center_admin or request.user.is_super_admin)

class UserViewSet(viewsets.ModelViewSet):
    """ViewSet for viewing and editing User instances."""
    queryset = User.objects.all()
    serializer_class = UserSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['is_active', 'role', 'center']
    search_fields = ['email', 'first_name', 'last_name']
    ordering_fields = ['email', 'first_name', 'last_name']

    def get_permissions(self):
        """Set custom permissions for different actions."""
        if self.action == 'create':
            return [IsAdminOrSuperAdmin()]
        elif self.action in ['update', 'partial_update', 'destroy']:
            return [IsAdminOrSuperAdmin()]
        return [permissions.IsAuthenticated()]

    def get_serializer_class(self):
        """Return appropriate serializer class based on action."""
        if self.action == 'list':
            return UserListSerializer
        return UserSerializer

    def get_queryset(self):
        """Filter users based on user role."""
        # Check if this is a schema generation request from Swagger
        if getattr(self, 'swagger_fake_view', False):
            # Return a simple queryset for Swagger schema generation
            return User.objects.none()

        user = self.request.user
        if user.is_super_admin:
            return User.objects.all()
        elif user.is_center_admin and user.center:
            return User.objects.filter(center=user.center)
        return User.objects.filter(id=user.id)  # Regular users can only see themselves

    def perform_create(self, serializer):
        """Set center based on user role if not provided."""
        user = self.request.user
        data = serializer.validated_data

        # If user is center admin and no center is specified, use admin's center
        if user.is_center_admin and not data.get('center') and user.center:
            data['center'] = user.center

        serializer.save()

    @action(detail=False, methods=['post'])
    def logout(self, request):
        """Logout endpoint for users."""
        logout(request)
        return Response({'success': 'Successfully logged out'}, status=status.HTTP_200_OK)

    @action(detail=False, methods=['get'])
    def me(self, request):
        """Return the authenticated user."""
        serializer = UserSerializer(request.user)
        return Response(serializer.data)

# Separate login view outside of the viewset
@api_view(['POST', 'OPTIONS'])
@permission_classes([permissions.AllowAny])
@authentication_classes([])
def login_view(request):
    """
    Legacy login endpoint - redirects to JWT login endpoint
    """
    # Handle preflight OPTIONS request
    if request.method == 'OPTIONS':
        response = Response()
        origin = request.headers.get('Origin', '')
        allowed_origins = ['http://localhost:5173', 'http://127.0.0.1:5173', 'http://localhost:5174', 'http://127.0.0.1:5174']

        if origin in allowed_origins:
            response['Access-Control-Allow-Origin'] = origin
            response['Access-Control-Allow-Methods'] = 'POST, OPTIONS'
            response['Access-Control-Allow-Headers'] = 'Content-Type, Authorization, X-Requested-With, X-CSRFToken'
            response['Access-Control-Allow-Credentials'] = 'true'
            response['Access-Control-Max-Age'] = '86400'  # 24 hours
        return response

    # Redirect to JWT login endpoint
    return jwt_login_view(request)


@api_view(['POST', 'OPTIONS'])
@permission_classes([permissions.AllowAny])
@authentication_classes([])
def admin_login_view(request):
    """
    Special login endpoint for admin users (superusers and staff).
    This endpoint is specifically designed for the Django admin interface.
    Uses JWT authentication.
    """
    # Handle preflight OPTIONS request
    if request.method == 'OPTIONS':
        response = Response()
        origin = request.headers.get('Origin', '')
        allowed_origins = ['http://localhost:5173', 'http://127.0.0.1:5173', 'http://localhost:5174', 'http://127.0.0.1:5174', 'http://localhost:8000', 'http://127.0.0.1:8000']

        if origin in allowed_origins:
            response['Access-Control-Allow-Origin'] = origin
            response['Access-Control-Allow-Methods'] = 'POST, OPTIONS'
            response['Access-Control-Allow-Headers'] = 'Content-Type, Authorization, X-Requested-With, X-CSRFToken'
            response['Access-Control-Allow-Credentials'] = 'true'
            response['Access-Control-Max-Age'] = '86400'  # 24 hours
        return response

    # Get credentials from request
    email = request.data.get('email')
    password = request.data.get('password')

    if not email or not password:
        return Response({'error': 'Email and password are required'}, status=status.HTTP_400_BAD_REQUEST)

    # Authenticate in the public schema
    user = authenticate(request, username=email, password=password)

    # Check if user is a superuser or staff
    if user and (user.is_superuser or user.is_staff):
        # Generate JWT tokens
        access_token, refresh_token = generate_jwt_tokens(user, 'public')

        # Log successful login
        logger.info(f"Admin login successful for user: {email}")

        # Return token and user info
        response = Response({
            'access_token': access_token,
            'refresh_token': refresh_token,
            'user': UserSerializer(user).data,
            'is_admin': True,
            'message': 'Admin login successful'
        })

        # Set refresh token cookie
        response.set_cookie(
            'jwt_refresh_token',
            refresh_token,
            httponly=True,
            samesite='Lax',
            path='/',
            max_age=7 * 24 * 60 * 60  # 7 days
        )

        # Add CORS headers
        origin = request.headers.get('Origin', '')
        allowed_origins = ['http://localhost:5173', 'http://127.0.0.1:5173', 'http://localhost:5174', 'http://127.0.0.1:5174', 'http://localhost:8000', 'http://127.0.0.1:8000']

        if origin in allowed_origins:
            response['Access-Control-Allow-Origin'] = origin
            response['Access-Control-Allow-Credentials'] = 'true'

        return response
    else:
        # Log failed login attempt
        logger.warning(f"Admin login failed for user: {email}")

        # Return error
        response = Response({'error': 'Invalid credentials or not an admin user'}, status=status.HTTP_401_UNAUTHORIZED)

        # Add CORS headers
        origin = request.headers.get('Origin', '')
        allowed_origins = ['http://localhost:5173', 'http://127.0.0.1:5173', 'http://localhost:5174', 'http://127.0.0.1:5174', 'http://localhost:8000', 'http://127.0.0.1:8000']

        if origin in allowed_origins:
            response['Access-Control-Allow-Origin'] = origin
            response['Access-Control-Allow-Credentials'] = 'true'

        return response

def admin_login_page(request):
    """
    View to render the admin login page.
    """
    return render(request, 'admin_login.html')

def swagger_auth_page(request):
    """
    View to render the Swagger authentication helper page.
    """
    return render(request, 'swagger_auth.html')


@api_view(['POST', 'OPTIONS'])
@permission_classes([permissions.AllowAny])
@authentication_classes([])
def refresh_token_view(request):
    """
    Legacy refresh token endpoint - redirects to JWT refresh endpoint
    """
    # Handle preflight OPTIONS request
    if request.method == 'OPTIONS':
        response = Response()
        origin = request.headers.get('Origin', '')
        allowed_origins = ['http://localhost:5173', 'http://127.0.0.1:5173', 'http://localhost:5174', 'http://127.0.0.1:5174']

        if origin in allowed_origins:
            response['Access-Control-Allow-Origin'] = origin
            response['Access-Control-Allow-Methods'] = 'POST, OPTIONS'
            response['Access-Control-Allow-Headers'] = 'Content-Type, Authorization, X-Requested-With, X-CSRFToken'
            response['Access-Control-Allow-Credentials'] = 'true'
            response['Access-Control-Max-Age'] = '86400'  # 24 hours
        return response

    # Redirect to JWT refresh endpoint
    return jwt_refresh_view(request)

@api_view(['POST'])
@permission_classes([permissions.AllowAny])
@authentication_classes([])
def swagger_token_auth(request):
    """
    Get a JWT token for testing with Swagger UI.
    This endpoint is specifically designed for Swagger testing.
    """
    email = request.data.get('email')
    password = request.data.get('password')
    schema_name = request.data.get('schema_name')

    if not email or not password:
        return Response({'error': 'Email and password are required'}, status=status.HTTP_400_BAD_REQUEST)

    # First try to authenticate in the public schema
    user = authenticate(request, username=email, password=password)

    if user:
        # User found in public schema
        schema = schema_name or 'public'
        access_token, refresh_token = generate_jwt_tokens(user, schema)
        return Response({
            'access_token': access_token,
            'refresh_token': refresh_token,
            'schema_name': schema,
            'message': 'Use this token in the Authorize dialog with format: Bearer YOUR_ACCESS_TOKEN'
        })

    # If not found in public schema, try tenant schemas if schema_name is provided
    if schema_name:
        from centers.models import Client
        from django_tenants.utils import tenant_context

        try:
            tenant = Client.objects.get(schema_name=schema_name)

            with tenant_context(tenant):
                user = authenticate(request, username=email, password=password)

                if user:
                    access_token, refresh_token = generate_jwt_tokens(user, tenant)
                    return Response({
                        'access_token': access_token,
                        'refresh_token': refresh_token,
                        'schema_name': schema_name,
                        'message': 'Use this token in the Authorize dialog with format: Bearer YOUR_ACCESS_TOKEN'
                    })
                else:
                    return Response({'error': 'Invalid credentials for this tenant'}, status=status.HTTP_401_UNAUTHORIZED)
        except Client.DoesNotExist:
            return Response({'error': 'Tenant not found'}, status=status.HTTP_404_NOT_FOUND)

    return Response({'error': 'Invalid credentials'}, status=status.HTTP_401_UNAUTHORIZED)

@api_view(['GET', 'OPTIONS'])
@permission_classes([permissions.AllowAny])
@authentication_classes([])
def tenant_info_view(request, schema_name):
    """Get tenant information including logo for the login page."""
    # Handle preflight OPTIONS request
    if request.method == 'OPTIONS':
        response = Response()
        origin = request.headers.get('Origin', '')
        allowed_origins = ['http://localhost:5173', 'http://127.0.0.1:5173', 'http://localhost:5174', 'http://127.0.0.1:5174']

        if origin in allowed_origins:
            response['Access-Control-Allow-Origin'] = origin
            response['Access-Control-Allow-Methods'] = 'GET, OPTIONS'
            response['Access-Control-Allow-Headers'] = 'Content-Type, Authorization, X-Requested-With, X-CSRFToken'
            response['Access-Control-Allow-Credentials'] = 'true'
            response['Access-Control-Max-Age'] = '86400'  # 24 hours
        return response

    try:
        # Import necessary models
        try:
            from centers.models import Client
        except ImportError as e:
            return Response({'error': f'Error importing Client model: {str(e)}'},
                      status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        # Get tenant information
        if schema_name == 'public':
            # Return default information for public schema
            response_data = {
                'name': 'Gondar ID Card Management System',
                'schema_name': 'public',
                'schema_type': 'PUBLIC'
            }
        else:
            try:
                # Try to find the tenant with exact schema name
                try:
                    tenant = Client.objects.get(schema_name=schema_name)
                except Client.DoesNotExist:
                    # Try with schema name variations
                    try:
                        # Try with underscores instead of spaces
                        alt_schema = schema_name.replace(' ', '_')
                        tenant = Client.objects.get(schema_name=alt_schema)
                        schema_name = alt_schema
                    except Client.DoesNotExist:
                        # Try with spaces instead of underscores
                        alt_schema = schema_name.replace('_', ' ')
                        try:
                            tenant = Client.objects.get(schema_name=alt_schema)
                            schema_name = alt_schema
                        except Client.DoesNotExist:
                            return Response({'error': f'Tenant with schema {schema_name} not found'},
                                      status=status.HTTP_404_NOT_FOUND)

                # Basic tenant info
                response_data = {
                    'id': tenant.id,
                    'name': tenant.name,
                    'schema_name': tenant.schema_name,
                    'schema_type': tenant.schema_type
                }

                # Add parent information if available
                if tenant.parent:
                    try:
                        response_data['parent_id'] = tenant.parent.id
                        response_data['parent_name'] = tenant.parent.name
                        response_data['parent_schema_name'] = tenant.parent.schema_name
                    except Exception as e:
                        # Log the error but continue
                        print(f"Error getting parent info: {str(e)}")

                # Check if tenant has a logo
                try:
                    logo_path = os.path.join(settings.MEDIA_ROOT, f'tenant_logos/{tenant.schema_name}_logo.png')
                    if os.path.exists(logo_path):
                        # Add timestamp to prevent caching
                        timestamp = int(os.path.getmtime(logo_path))
                        response_data['logo_url'] = f'http://localhost:8000/media/tenant_logos/{tenant.schema_name}_logo.png?t={timestamp}'
                except Exception as e:
                    # Log the error but continue
                    print(f"Error checking logo: {str(e)}")

                # Add additional fields for all tenant types
                try:
                    if hasattr(tenant, 'website'):
                        response_data['website'] = tenant.website
                    if hasattr(tenant, 'header_color'):
                        response_data['header_color'] = tenant.header_color
                    if hasattr(tenant, 'accent_color'):
                        response_data['accent_color'] = tenant.accent_color
                except Exception as e:
                    # Log the error but continue
                    print(f"Error getting additional fields: {str(e)}")

                # Add city-specific fields if available
                if tenant.schema_type == 'CITY':
                    pass  # Add any future city-specific fields here
            except Client.DoesNotExist:
                return Response({'error': 'Tenant not found'}, status=status.HTTP_404_NOT_FOUND)
            except Exception as e:
                return Response({'error': f'Error retrieving tenant: {str(e)}'},
                          status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        response = Response(response_data)

        # Add CORS headers
        origin = request.headers.get('Origin', '')
        allowed_origins = ['http://localhost:5173', 'http://127.0.0.1:5173', 'http://localhost:5174', 'http://127.0.0.1:5174']
        if origin in allowed_origins:
            response['Access-Control-Allow-Origin'] = origin
            response['Access-Control-Allow-Credentials'] = 'true'

        return response

    except Exception as e:
        response = Response({'error': f'Error retrieving tenant info: {str(e)}'},
                      status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        # Add CORS headers
        origin = request.headers.get('Origin', '')
        allowed_origins = ['http://localhost:5173', 'http://127.0.0.1:5173', 'http://localhost:5174', 'http://127.0.0.1:5174']
        if origin in allowed_origins:
            response['Access-Control-Allow-Origin'] = origin
            response['Access-Control-Allow-Credentials'] = 'true'

        return response


@api_view(['POST', 'OPTIONS'])
@permission_classes([permissions.AllowAny])
@authentication_classes([])
def jwt_login_view(request):
    """
    Login view for authenticating users with JWT.
    """
    # Import connection at the function level to ensure it's available in finally block
    from django.db import connection

    # Handle preflight OPTIONS request
    if request.method == 'OPTIONS':
        response = Response()
        origin = request.headers.get('Origin', '')
        allowed_origins = ['http://localhost:5173', 'http://127.0.0.1:5173', 'http://localhost:5174', 'http://127.0.0.1:5174',
                          'http://localhost:5175', 'http://127.0.0.1:5175', 'http://localhost:5176', 'http://127.0.0.1:5176']

        if origin in allowed_origins:
            response['Access-Control-Allow-Origin'] = origin
            response['Access-Control-Allow-Methods'] = 'POST, OPTIONS'
            response['Access-Control-Allow-Headers'] = 'Content-Type, Authorization, X-Requested-With, X-CSRFToken, X-Schema-Name'
            response['Access-Control-Allow-Credentials'] = 'true'
            response['Access-Control-Max-Age'] = '86400'  # 24 hours
        return response

    try:
        # Log the request for debugging
        logger.info(f"JWT login request received: {request.data}")
        logger.info(f"Headers: {request.headers}")

        email = request.data.get('email')
        password = request.data.get('password')
        schema_name = request.data.get('schema_name')

        # Get schema from request headers if not in request body
        if not schema_name:
            schema_name = request.headers.get('X-Schema-Name')

        logger.info(f"Login attempt for email: {email}, schema: {schema_name}")

        if not email or not password:
            logger.warning("Email or password missing in request")
            return Response({'error': 'Email and password are required'}, status=status.HTTP_400_BAD_REQUEST)

        # Import Client model
        from centers.models import Client

        # Make schema_name optional
        if not schema_name:
            # First, try to find tenant by email domain
            from .tenant_user_utils import find_tenant_by_email_domain
            domain_tenant = find_tenant_by_email_domain(email)

            if domain_tenant:
                logger.info(f"Found tenant {domain_tenant.schema_name} by email domain for {email}")
                # Try to authenticate in this tenant
                connection.set_tenant(domain_tenant)
                user = authenticate(request, username=email, password=password)

                if user is not None:
                    # User found in domain tenant
                    logger.info(f"User {email} authenticated in domain tenant {domain_tenant.schema_name}")
                    tenant = domain_tenant
                else:
                    # User not found in domain tenant, fall back to searching all tenants
                    logger.info(f"User {email} not found in domain tenant {domain_tenant.schema_name}, trying all schemas")
                    connection.set_schema_to_public()

            # If no domain tenant or user not found in domain tenant, try all schemas
            if not domain_tenant or user is None:
                # Try to find the user in all available schemas
                logger.info(f"No schema provided or domain tenant auth failed, trying to find user {email} in all schemas")

                # First try the public schema
                connection.set_schema_to_public()
                user = authenticate(request, username=email, password=password)

                if user is not None:
                    # User found in public schema
                    logger.info(f"User {email} found in public schema")
                    tenant = Client.objects.get(schema_name='public')
                else:
                    # If not found in public schema, try all other schemas
                    logger.info(f"User {email} not found in public schema, trying other schemas")

                    # Get all available tenants
                    connection.set_schema_to_public()
                    all_tenants = Client.objects.exclude(schema_name='public').order_by('id')
                    logger.info(f"Found {len(all_tenants)} tenants to check")

                    # Try each tenant schema
                    found_tenant = None
                    found_user = None

                    for tenant_to_try in all_tenants:
                        try:
                            # Skip the domain tenant if we already tried it
                            if domain_tenant and tenant_to_try.id == domain_tenant.id:
                                logger.info(f"Skipping already tried domain tenant {tenant_to_try.schema_name}")
                                continue

                            logger.info(f"Trying schema {tenant_to_try.schema_name} for user {email}")
                            connection.set_tenant(tenant_to_try)

                            # Try to authenticate in this schema
                            test_user = authenticate(request, username=email, password=password)

                            if test_user is not None:
                                # User found in this schema
                                logger.info(f"User {email} found in schema {tenant_to_try.schema_name}")
                                found_tenant = tenant_to_try
                                found_user = test_user
                                break
                        except Exception as e:
                            logger.error(f"Error checking schema {tenant_to_try.schema_name}: {str(e)}")
                            continue

                    if found_tenant and found_user:
                        # User found in a tenant schema
                        tenant = found_tenant
                        user = found_user
                        connection.set_tenant(tenant)
                        logger.info(f"Using found tenant: {tenant.schema_name}")
                    else:
                        # User not found in any schema
                        logger.warning(f"User {email} not found in any schema")
                        return Response({'error': 'Invalid credentials or user not found in any tenant'}, status=status.HTTP_401_UNAUTHORIZED)
        else:
            # Get tenant by schema name
            try:
                # Try with exact schema name
                try:
                    tenant = Client.objects.get(schema_name=schema_name)
                    logger.info(f"Found tenant with exact schema name: {schema_name}")
                except Client.DoesNotExist:
                    # Try with schema name variations
                    logger.info(f"Tenant with exact schema name {schema_name} not found, trying variations")

                    # Try with underscores instead of spaces
                    alt_schema = schema_name.replace(' ', '_')
                    try:
                        tenant = Client.objects.get(schema_name=alt_schema)
                        schema_name = alt_schema
                        logger.info(f"Found tenant with underscore schema: {schema_name}")
                    except Client.DoesNotExist:
                        # Try with spaces instead of underscores
                        alt_schema = schema_name.replace('_', ' ')
                        try:
                            tenant = Client.objects.get(schema_name=alt_schema)
                            schema_name = alt_schema
                            logger.info(f"Found tenant with space schema: {schema_name}")
                        except Client.DoesNotExist:
                            logger.error(f"Tenant with schema {schema_name} or variations not found")
                            return Response({'error': f'Tenant with schema "{schema_name}" not found'}, status=status.HTTP_404_NOT_FOUND)

                logger.info(f"Using provided schema {schema_name}")
            except Exception as e:
                logger.exception(f"Error finding tenant with schema {schema_name}: {str(e)}")
                return Response({'error': f'Error finding tenant: {str(e)}'}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

            # Switch to tenant schema
            try:
                connection.set_tenant(tenant)
                logger.info(f"Switched to tenant schema: {tenant.schema_name}")
            except Exception as e:
                logger.exception(f"Error switching to tenant schema {tenant.schema_name}: {str(e)}")
                return Response({'error': f'Error switching to tenant schema: {str(e)}'}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

            # Authenticate user in this schema
            try:
                user = authenticate(request, username=email, password=password)

                if user is None:
                    # User not found in the specified schema
                    logger.warning(f"User {email} not found in specified schema {schema_name}")
                    return Response({'error': 'Invalid credentials for the specified tenant'}, status=status.HTTP_401_UNAUTHORIZED)

                logger.info(f"User {email} authenticated successfully in schema {schema_name}")
            except Exception as e:
                logger.exception(f"Error authenticating user {email} in schema {schema_name}: {str(e)}")
                return Response({'error': f'Error authenticating user: {str(e)}'}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        # User is already authenticated by this point

        if user is not None:
            # Generate JWT tokens
            try:
                logger.info(f"Generating JWT tokens for user {user.id} in tenant {tenant.schema_name}")
                tokens = generate_jwt_tokens(user, tenant)
                if not tokens:
                    logger.error(f"Failed to generate JWT tokens for user {user.id} in tenant {tenant.schema_name}")
                    return Response({'error': 'Failed to generate JWT tokens'}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
                logger.info(f"JWT tokens generated successfully for user {user.id}")
            except Exception as e:
                logger.exception(f"Error generating JWT tokens: {str(e)}")
                return Response({'error': f'Error generating JWT tokens: {str(e)}'}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

            # Get user role
            role = user.role if hasattr(user, 'role') else None
            logger.info(f"User role: {role}")

            # Get parent tenant if available
            parent_tenant = None
            if tenant.parent:
                try:
                    parent_tenant = Client.objects.get(id=tenant.parent.id)
                    logger.info(f"Found parent tenant: {parent_tenant.name} (ID: {parent_tenant.id})")
                except Client.DoesNotExist:
                    logger.warning(f'Parent tenant with ID {tenant.parent.id} not found')

            # Return tokens and user info
            response_data = {
                'access_token': tokens['access_token'],
                'refresh_token': tokens['refresh_token'],
                'user': {
                    'id': user.id,
                    'email': user.email,
                    'first_name': user.first_name,
                    'last_name': user.last_name,
                    'role': role
                },
                'tenant': {
                    'id': tenant.id,
                    'name': tenant.name,
                    'schema_name': tenant.schema_name,
                    'domain_url': tenant.domain_url,
                    'type': tenant.schema_type,
                    'parent_id': tenant.parent_id if tenant.parent else None
                }
            }

            # Add parent tenant info if available
            if parent_tenant:
                response_data['tenant']['parent'] = {
                    'id': parent_tenant.id,
                    'name': parent_tenant.name,
                    'schema_name': parent_tenant.schema_name,
                    'type': parent_tenant.schema_type
                }

            # Create response
            response = Response(response_data)

            # Set cookies for tokens
            if tokens['refresh_token']:
                # Set refresh token as a cookie
                response.set_cookie(
                    'refresh_token',
                    tokens['refresh_token'],
                    httponly=True,  # HTTP-only cookie for security
                    samesite='Lax',
                    path='/',
                    max_age=7 * 24 * 60 * 60  # 7 days
                )

                # Set schema name as a cookie
                response.set_cookie(
                    'schema_name',
                    tenant.schema_name,
                    samesite='Lax',
                    path='/',
                    max_age=7 * 24 * 60 * 60  # 7 days
                )

                logger.info(f"Set cookies for refresh_token and schema_name: {tenant.schema_name}")

            # Add CORS headers
            origin = request.headers.get('Origin', '')
            allowed_origins = ['http://localhost:5173', 'http://127.0.0.1:5173', 'http://localhost:5174', 'http://127.0.0.1:5174',
                              'http://localhost:5175', 'http://127.0.0.1:5175', 'http://localhost:5176', 'http://127.0.0.1:5176']
            if origin in allowed_origins:
                response['Access-Control-Allow-Origin'] = origin
                response['Access-Control-Allow-Credentials'] = 'true'
                logger.info(f"Added CORS headers for origin: {origin}")

            logger.info("Login successful, returning response")
            return response
        else:
            # Switch back to public schema
            connection.set_schema_to_public()
            logger.warning("User is None after authentication")

            response = Response({'error': 'Invalid credentials'}, status=status.HTTP_401_UNAUTHORIZED)

            # Add CORS headers
            origin = request.headers.get('Origin', '')
            allowed_origins = ['http://localhost:5173', 'http://127.0.0.1:5173', 'http://localhost:5174', 'http://127.0.0.1:5174',
                              'http://localhost:5175', 'http://127.0.0.1:5175', 'http://localhost:5176', 'http://127.0.0.1:5176']
            if origin in allowed_origins:
                response['Access-Control-Allow-Origin'] = origin
                response['Access-Control-Allow-Credentials'] = 'true'

            return response
    except Exception as e:
        logger.exception('Error in jwt_login_view: %s', str(e))

        response = Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        # Add CORS headers
        origin = request.headers.get('Origin', '')
        allowed_origins = ['http://localhost:5173', 'http://127.0.0.1:5173', 'http://localhost:5174', 'http://127.0.0.1:5174',
                          'http://localhost:5175', 'http://127.0.0.1:5175', 'http://localhost:5176', 'http://127.0.0.1:5176']
        if origin in allowed_origins:
            response['Access-Control-Allow-Origin'] = origin
            response['Access-Control-Allow-Credentials'] = 'true'

        return response
    finally:
        # Switch back to public schema
        try:
            connection.set_schema_to_public()
            logger.info("Switched back to public schema")
        except Exception as e:
            logger.exception(f"Error switching back to public schema: {str(e)}")

@api_view(['POST', 'OPTIONS'])
@permission_classes([permissions.AllowAny])
@authentication_classes([])
def jwt_refresh_view(request):
    """
    Refresh view for refreshing JWT tokens.
    """
    # Import connection at the function level to ensure it's available in finally block
    from django.db import connection

    # Handle preflight OPTIONS request
    if request.method == 'OPTIONS':
        response = Response()
        origin = request.headers.get('Origin', '')
        allowed_origins = ['http://localhost:5173', 'http://127.0.0.1:5173', 'http://localhost:5174', 'http://127.0.0.1:5174']

        if origin in allowed_origins:
            response['Access-Control-Allow-Origin'] = origin
            response['Access-Control-Allow-Methods'] = 'POST, OPTIONS'
            response['Access-Control-Allow-Headers'] = 'Content-Type, Authorization, X-Requested-With, X-CSRFToken, X-Schema-Name'
            response['Access-Control-Allow-Credentials'] = 'true'
            response['Access-Control-Max-Age'] = '86400'  # 24 hours
        return response

    try:
        # Get refresh token from request data, Authorization header, or cookies
        refresh_token = request.data.get('refresh_token')

        # If no refresh token in request data, try to get it from Authorization header
        if not refresh_token:
            auth_header = request.headers.get('Authorization', '')
            if auth_header.startswith('Bearer '):
                refresh_token = auth_header.split(' ')[1].strip()

        # If still no refresh token, try to get it from cookies
        if not refresh_token:
            refresh_token = request.COOKIES.get('refresh_token')
            if refresh_token:
                logger.info('Using refresh token from cookie')

        if not refresh_token:
            return Response({'error': 'Refresh token is required'}, status=status.HTTP_400_BAD_REQUEST)

        # Get schema from request
        schema_name = request.data.get('schema_name')

        # If no schema in request data, try to get it from X-Schema-Name header
        if not schema_name:
            schema_name = request.headers.get('X-Schema-Name')

        # If still no schema, try to extract it from the token
        if not schema_name:
            try:
                from .jwt_utils import get_schema_from_token
                schema_name = get_schema_from_token(refresh_token)
                logger.info(f"Extracted schema from token: {schema_name}")
            except Exception as e:
                logger.error(f"Error extracting schema from token: {str(e)}")

        # Log the refresh token for debugging (only in development)
        logger.info(f"Refresh token: {refresh_token[:20]}...")
        logger.info(f"Schema name: {schema_name}")

        # If we have a schema, switch to that schema's tenant
        if schema_name:
            try:
                from centers.models import Client
                tenant = Client.objects.get(schema_name=schema_name)
                connection.set_tenant(tenant)
                logger.info(f"Switched to tenant schema: {schema_name}")
            except Client.DoesNotExist:
                logger.warning(f"Tenant with schema {schema_name} not found")
                # Try with schema name variations
                try:
                    # Try with underscores instead of spaces
                    alt_schema = schema_name.replace(' ', '_')
                    tenant = Client.objects.get(schema_name=alt_schema)
                    connection.set_tenant(tenant)
                    schema_name = alt_schema
                    logger.info(f"Switched to tenant schema with underscore: {schema_name}")
                except Client.DoesNotExist:
                    # Try with spaces instead of underscores
                    alt_schema = schema_name.replace('_', ' ')
                    try:
                        tenant = Client.objects.get(schema_name=alt_schema)
                        connection.set_tenant(tenant)
                        schema_name = alt_schema
                        logger.info(f"Switched to tenant schema with spaces: {schema_name}")
                    except Client.DoesNotExist:
                        logger.warning(f"Tenant with schema {schema_name} or variations not found")
                        # Continue with public schema

        # Refresh tokens
        tokens = refresh_jwt_tokens(refresh_token)

        if tokens:
            response = Response({
                'access_token': tokens['access_token'],
                'refresh_token': tokens['refresh_token']
            })

            # Set cookies for tokens
            if tokens['refresh_token']:
                # Set refresh token as a cookie
                response.set_cookie(
                    'refresh_token',
                    tokens['refresh_token'],
                    httponly=True,  # HTTP-only cookie for security
                    samesite='Lax',
                    path='/',
                    max_age=7 * 24 * 60 * 60  # 7 days
                )

                # Set schema name as a cookie if we have it
                if schema_name:
                    response.set_cookie(
                        'schema_name',
                        schema_name,
                        samesite='Lax',
                        path='/',
                        max_age=7 * 24 * 60 * 60  # 7 days
                    )

                logger.info(f"Set cookies for refresh_token and schema_name: {schema_name}")

            # Add CORS headers
            origin = request.headers.get('Origin', '')
            allowed_origins = ['http://localhost:5173', 'http://127.0.0.1:5173', 'http://localhost:5174', 'http://127.0.0.1:5174']
            if origin in allowed_origins:
                response['Access-Control-Allow-Origin'] = origin
                response['Access-Control-Allow-Credentials'] = 'true'

            return response
        else:
            response = Response({'error': 'Invalid refresh token'}, status=status.HTTP_401_UNAUTHORIZED)

            # Add CORS headers
            origin = request.headers.get('Origin', '')
            allowed_origins = ['http://localhost:5173', 'http://127.0.0.1:5173', 'http://localhost:5174', 'http://127.0.0.1:5174']
            if origin in allowed_origins:
                response['Access-Control-Allow-Origin'] = origin
                response['Access-Control-Allow-Credentials'] = 'true'

            return response
    except Exception as e:
        logger.exception('Error in jwt_refresh_view: %s', str(e))

        response = Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        # Add CORS headers
        origin = request.headers.get('Origin', '')
        allowed_origins = ['http://localhost:5173', 'http://127.0.0.1:5173', 'http://localhost:5174', 'http://127.0.0.1:5174']
        if origin in allowed_origins:
            response['Access-Control-Allow-Origin'] = origin
            response['Access-Control-Allow-Credentials'] = 'true'

        return response
    finally:
        # Switch back to public schema
        connection.set_schema_to_public()

@api_view(['GET', 'POST', 'OPTIONS'])
@permission_classes([permissions.AllowAny])
@authentication_classes([])
def jwt_validate_view(request):
    """
    Validate view for validating JWT tokens.
    Supports both GET and POST methods for flexibility.
    """
    # Import connection at the function level to ensure it's available in finally block
    from django.db import connection

    # Handle preflight OPTIONS request
    if request.method == 'OPTIONS':
        response = Response()
        origin = request.headers.get('Origin', '')
        allowed_origins = ['http://localhost:5173', 'http://127.0.0.1:5173', 'http://localhost:5174', 'http://127.0.0.1:5174',
                          'http://localhost:5175', 'http://127.0.0.1:5175', 'http://localhost:5176', 'http://127.0.0.1:5176']

        if origin in allowed_origins:
            response['Access-Control-Allow-Origin'] = origin
            response['Access-Control-Allow-Methods'] = 'GET, POST, OPTIONS'
            response['Access-Control-Allow-Headers'] = 'Content-Type, Authorization, X-Requested-With, X-CSRFToken, X-Schema-Name'
            response['Access-Control-Allow-Credentials'] = 'true'
            response['Access-Control-Max-Age'] = '86400'  # 24 hours
        return response

    try:
        # For GET requests, token should be in Authorization header
        # For POST requests, token can be in request body or Authorization header
        token = None

        # First check Authorization header (works for both GET and POST)
        auth_header = request.headers.get('Authorization', '')
        if auth_header.startswith('Bearer '):
            token = auth_header.split(' ')[1].strip()
            logger.info('Using token from Authorization header')

        # If no token in Authorization header and this is a POST request, check request body
        if not token and request.method == 'POST':
            token = request.data.get('token')
            if token:
                logger.info('Using token from request body')

        # If still no token, try to get it from cookies
        if not token:
            token = request.COOKIES.get('jwt_access_token') or request.COOKIES.get('jwt_token') or request.COOKIES.get('refresh_token')
            if token:
                logger.info('Using token from cookie')

        if not token:
            return Response({
                'valid': False,
                'error': 'Token is required. Please provide a Bearer token in the Authorization header.'
            }, status=status.HTTP_401_UNAUTHORIZED)

        # Get schema from request
        schema_name = request.data.get('schema_name')

        # If no schema in request data, try to get it from X-Schema-Name header
        if not schema_name:
            schema_name = request.headers.get('X-Schema-Name')

        # If still no schema, try to extract it from the token
        if not schema_name:
            try:
                from .jwt_utils import get_schema_from_token
                schema_name = get_schema_from_token(token)
                logger.info(f"Extracted schema from token: {schema_name}")
            except Exception as e:
                logger.error(f"Error extracting schema from token: {str(e)}")

        # If we have a schema, switch to that schema's tenant
        if schema_name:
            try:
                from centers.models import Client
                tenant = Client.objects.get(schema_name=schema_name)
                connection.set_tenant(tenant)
                logger.info(f"Switched to tenant schema: {schema_name}")
            except Client.DoesNotExist:
                logger.warning(f"Tenant with schema {schema_name} not found")
                # Continue with public schema

        # Validate token
        payload = validate_jwt_token(token)

        if payload:
            # Extract user information from payload
            user_id = payload.get('sub')
            email = payload.get('email')
            exp = payload.get('exp')

            # Format expiration time for readability
            import datetime
            exp_datetime = datetime.datetime.fromtimestamp(exp) if exp else None
            exp_formatted = exp_datetime.strftime('%Y-%m-%d %H:%M:%S') if exp_datetime else None

            response_data = {
                'valid': True,
                'user_id': user_id,
                'email': email,
                'exp': exp,
                'exp_formatted': exp_formatted,
                'schema_name': schema_name
            }

            # Include tenant information if available
            if schema_name:
                try:
                    from centers.models import Client
                    tenant = Client.objects.get(schema_name=schema_name)
                    response_data['tenant'] = {
                        'id': tenant.id,
                        'name': tenant.name,
                        'schema_name': tenant.schema_name,
                        'schema_type': tenant.schema_type
                    }
                except Exception as e:
                    logger.error(f"Error getting tenant info: {str(e)}")

            response = Response(response_data)

            # Add CORS headers
            origin = request.headers.get('Origin', '')
            allowed_origins = ['http://localhost:5173', 'http://127.0.0.1:5173', 'http://localhost:5174', 'http://127.0.0.1:5174',
                              'http://localhost:5175', 'http://127.0.0.1:5175', 'http://localhost:5176', 'http://127.0.0.1:5176']
            if origin in allowed_origins:
                response['Access-Control-Allow-Origin'] = origin
                response['Access-Control-Allow-Credentials'] = 'true'

            return response
        else:
            response = Response({
                'valid': False,
                'error': 'Invalid token'
            }, status=status.HTTP_401_UNAUTHORIZED)

            # Add CORS headers
            origin = request.headers.get('Origin', '')
            allowed_origins = ['http://localhost:5173', 'http://127.0.0.1:5173', 'http://localhost:5174', 'http://127.0.0.1:5174',
                              'http://localhost:5175', 'http://127.0.0.1:5175', 'http://localhost:5176', 'http://127.0.0.1:5176']
            if origin in allowed_origins:
                response['Access-Control-Allow-Origin'] = origin
                response['Access-Control-Allow-Credentials'] = 'true'

            return response
    except Exception as e:
        logger.exception('Error in jwt_validate_view: %s', str(e))

        response = Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        # Add CORS headers
        origin = request.headers.get('Origin', '')
        allowed_origins = ['http://localhost:5173', 'http://127.0.0.1:5173', 'http://localhost:5174', 'http://127.0.0.1:5174']
        if origin in allowed_origins:
            response['Access-Control-Allow-Origin'] = origin
            response['Access-Control-Allow-Credentials'] = 'true'

        return response
    finally:
        # Switch back to public schema
        connection.set_schema_to_public()

@api_view(['POST', 'OPTIONS'])
@permission_classes([permissions.IsAuthenticated])
def jwt_exchange_token_view(request):
    """
    Exchange view for exchanging legacy tokens for JWT tokens.
    """
    # Handle preflight OPTIONS request
    if request.method == 'OPTIONS':
        response = Response()
        origin = request.headers.get('Origin', '')
        allowed_origins = ['http://localhost:5173', 'http://127.0.0.1:5173', 'http://localhost:5174', 'http://127.0.0.1:5174']

        if origin in allowed_origins:
            response['Access-Control-Allow-Origin'] = origin
            response['Access-Control-Allow-Methods'] = 'POST, OPTIONS'
            response['Access-Control-Allow-Headers'] = 'Content-Type, Authorization, X-Requested-With, X-CSRFToken, X-Schema-Name'
            response['Access-Control-Allow-Credentials'] = 'true'
            response['Access-Control-Max-Age'] = '86400'  # 24 hours
        return response

    try:
        schema_name = request.data.get('schema')

        # Get schema from request headers if not in request body
        if not schema_name:
            schema_name = request.headers.get('X-Schema-Name')

        if not schema_name:
            return Response({'error': 'Schema name is required'}, status=status.HTTP_400_BAD_REQUEST)

        # Get tenant by schema name
        try:
            from centers.models import Client
            from django.db import connection
            tenant = Client.objects.get(schema_name=schema_name)
        except Client.DoesNotExist:
            return Response({'error': f'Tenant with schema "{schema_name}" not found'}, status=status.HTTP_404_NOT_FOUND)

        # Switch to tenant schema
        connection.set_tenant(tenant)

        # Get user from request
        user = request.user

        if user is not None:
            # Generate JWT tokens
            tokens = generate_jwt_tokens(user, tenant)

            response = Response({
                'access_token': tokens['access_token'],
                'refresh_token': tokens['refresh_token']
            })

            # Add CORS headers
            origin = request.headers.get('Origin', '')
            allowed_origins = ['http://localhost:5173', 'http://127.0.0.1:5173', 'http://localhost:5174', 'http://127.0.0.1:5174']
            if origin in allowed_origins:
                response['Access-Control-Allow-Origin'] = origin
                response['Access-Control-Allow-Credentials'] = 'true'

            return response
        else:
            response = Response({'error': 'User not found'}, status=status.HTTP_401_UNAUTHORIZED)

            # Add CORS headers
            origin = request.headers.get('Origin', '')
            allowed_origins = ['http://localhost:5173', 'http://127.0.0.1:5173', 'http://localhost:5174', 'http://127.0.0.1:5174']
            if origin in allowed_origins:
                response['Access-Control-Allow-Origin'] = origin
                response['Access-Control-Allow-Credentials'] = 'true'

            return response
    except Exception as e:
        logger.exception('Error in jwt_exchange_token_view: %s', str(e))

        response = Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        # Add CORS headers
        origin = request.headers.get('Origin', '')
        allowed_origins = ['http://localhost:5173', 'http://127.0.0.1:5173', 'http://localhost:5174', 'http://127.0.0.1:5174']
        if origin in allowed_origins:
            response['Access-Control-Allow-Origin'] = origin
            response['Access-Control-Allow-Credentials'] = 'true'

        return response
    finally:
        # Switch back to public schema
        connection.set_schema_to_public()