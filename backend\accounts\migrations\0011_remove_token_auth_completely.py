from django.db import migrations

class Migration(migrations.Migration):
    """
    Migration to completely remove the Token model and related tables.
    """

    dependencies = [
        ('accounts', '0010_remove_token_auth'),
    ]

    operations = [
        migrations.RunSQL(
            # SQL to run when applying the migration
            """
            -- Drop the authtoken_token table if it exists
            DROP TABLE IF EXISTS authtoken_token CASCADE;
            
            -- Drop any related tables
            DROP TABLE IF EXISTS accounts_expiringtoken CASCADE;
            """,
            # SQL to run when unapplying the migration (no-op)
            """
            -- No reverse migration - we're fully committing to JWT
            """
        ),
    ]
