/**
 * Token Utilities
 *
 * This module provides utilities for managing tokens across different schemas.
 */

// Store tokens by schema
interface TokenStore {
  [schema: string]: string;
}

// Initialize token store from localStorage
const initializeTokenStore = (): TokenStore => {
  try {
    // Try to get the token store from localStorage
    const storedTokens = localStorage.getItem('tokenStore');
    if (storedTokens) {
      return JSON.parse(storedTokens);
    }
  } catch (error) {
    console.error('Error initializing token store:', error);
  }

  // If no token store exists, create a new one
  return {};
};

// Global token store
let tokenStore = initializeTokenStore();

/**
 * Save a token for a specific schema
 * @param schema The schema name
 * @param token The token
 */
export const saveTokenForSchema = (schema: string, token: string): void => {
  if (!schema || !token) {
    console.error('Cannot save token: schema or token is missing');
    return;
  }

  // Save the token in the token store
  tokenStore[schema] = token;

  // Save the token store to localStorage
  try {
    localStorage.setItem('tokenStore', JSON.stringify(tokenStore));
    console.log(`Saved token for schema ${schema}`);
  } catch (error) {
    console.error('Error saving token store to localStorage:', error);
  }
};

/**
 * Get the token for a specific schema
 * @param schema The schema name
 * @returns The token for the schema, or null if not found
 */
export const getTokenForSchema = (schema: string): string | null => {
  if (!schema) {
    console.error('Cannot get token: schema is missing');
    return null;
  }

  // Get the token from the token store
  return tokenStore[schema] || null;
};

/**
 * Get the current schema name
 * @returns The current schema name, or null if not found
 */
export const getCurrentSchema = (): string | null => {
  // Try to get the schema name from localStorage
  const schema = localStorage.getItem('schema_name');
  if (schema) {
    return schema;
  }

  // Try to get the schema name from the tenant object
  try {
    const tenantStr = localStorage.getItem('tenant');
    if (tenantStr) {
      const tenant = JSON.parse(tenantStr);
      if (tenant && tenant.schema_name) {
        return tenant.schema_name;
      }
    }
  } catch (error) {
    console.error('Error parsing tenant from localStorage:', error);
  }

  // Try to get the schema name from cookies
  const schemaCookie = document.cookie
    .split('; ')
    .find(row => row.startsWith('schema_name='))
    ?.split('=')[1];

  if (schemaCookie) {
    try {
      return decodeURIComponent(schemaCookie);
    } catch (error) {
      console.error('Error decoding schema cookie:', error);
    }
  }

  return null;
};

/**
 * Get the token for the current schema
 * @returns The token for the current schema, or null if not found
 */
export const getTokenForCurrentSchema = (): string | null => {
  const schema = getCurrentSchema();
  if (!schema) {
    console.error('Cannot get token: current schema is missing');
    return null;
  }

  return getTokenForSchema(schema);
};

/**
 * Initialize the token store with the current token
 */
export const initTokenStore = (): void => {
  // Get the current schema and token
  const schema = getCurrentSchema();
  const token = localStorage.getItem('token');

  if (schema && token) {
    // Save the token for the current schema
    saveTokenForSchema(schema, token);
    console.log(`Initialized token store with token for schema ${schema}`);
  }
};

// Initialize the token store when this module is imported
initTokenStore();
