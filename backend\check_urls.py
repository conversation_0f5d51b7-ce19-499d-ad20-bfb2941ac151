import os
import django

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'neocamelot.settings')
django.setup()

# Import URL patterns
from django.urls import get_resolver
from neocamelot.urls import urlpatterns as public_urlpatterns
from neocamelot.urls_tenants import urlpatterns as tenant_urlpatterns

# Check URL patterns
print("Public URL patterns:")
resolver = get_resolver(None)
for pattern in resolver.url_patterns:
    if hasattr(pattern, 'pattern'):
        print(f"- {pattern.pattern}")
    else:
        print(f"- {pattern}")

print("\nTenant URL patterns:")
for pattern in tenant_urlpatterns:
    if hasattr(pattern, 'pattern'):
        print(f"- {pattern.pattern}")
    else:
        print(f"- {pattern}")

# Check API endpoints
from rest_framework.routers import DefaultRouter
from neocamelot.urls import router as public_router
from neocamelot.urls_tenants import router as tenant_router

print("\nPublic API endpoints:")
for prefix, viewset, basename in public_router.registry:
    print(f"- {prefix}: {viewset.__name__}")

print("\nTenant API endpoints:")
for prefix, viewset, basename in tenant_router.registry:
    print(f"- {prefix}: {viewset.__name__}")
