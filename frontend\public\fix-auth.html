<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Authentication Fix</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
      line-height: 1.6;
    }
    h1 {
      color: #3f51b5;
      border-bottom: 2px solid #3f51b5;
      padding-bottom: 10px;
    }
    .card {
      border: 1px solid #ddd;
      border-radius: 8px;
      padding: 20px;
      margin-bottom: 20px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    button {
      background-color: #3f51b5;
      color: white;
      border: none;
      padding: 10px 20px;
      border-radius: 4px;
      cursor: pointer;
      font-size: 16px;
      margin-right: 10px;
    }
    button:hover {
      background-color: #303f9f;
    }
    button.warning {
      background-color: #f44336;
    }
    button.warning:hover {
      background-color: #d32f2f;
    }
    button.success {
      background-color: #4caf50;
    }
    button.success:hover {
      background-color: #388e3c;
    }
    pre {
      background-color: #f5f5f5;
      padding: 10px;
      border-radius: 4px;
      overflow-x: auto;
    }
    .result {
      margin-top: 20px;
      display: none;
    }
    .error {
      color: #f44336;
      font-weight: bold;
    }
    .success {
      color: #4caf50;
      font-weight: bold;
    }
  </style>
</head>
<body>
  <h1>Authentication Fix Tool</h1>
  
  <div class="card">
    <h2>Current Authentication State</h2>
    <div id="auth-state">Loading...</div>
    <button onclick="diagnoseAuth()">Refresh</button>
  </div>
  
  <div class="card">
    <h2>Fix Authentication</h2>
    <p>This will clean your authentication token and update all related storage. Use this if you're experiencing 401 Unauthorized errors.</p>
    <button onclick="fixAuth()" class="success">Fix Authentication</button>
    <button onclick="fixAuthAggressive()" class="warning">Aggressive Fix (Last Resort)</button>
    <div id="fix-result" class="result"></div>
  </div>
  
  <div class="card">
    <h2>Manual Token Entry</h2>
    <p>If you have a valid token, you can enter it manually here:</p>
    <input type="text" id="manual-token" placeholder="Enter token" style="width: 100%; padding: 8px; margin-bottom: 10px;">
    <input type="text" id="manual-schema" placeholder="Enter schema name (e.g., gondar_city)" style="width: 100%; padding: 8px; margin-bottom: 10px;">
    <button onclick="setManualToken()">Set Token</button>
    <div id="manual-result" class="result"></div>
  </div>
  
  <div class="card">
    <h2>Navigation</h2>
    <button onclick="window.location.href = '/'">Go to Home</button>
    <button onclick="window.location.href = '/token-test'">Go to Token Test</button>
    <button onclick="window.location.href = '/login'">Go to Login</button>
  </div>
  
  <script>
    // Function to diagnose authentication state
    function diagnoseAuth() {
      const authState = document.getElementById('auth-state');
      
      try {
        // Get token from localStorage
        const token = localStorage.getItem('token');
        const schema = localStorage.getItem('schema_name');
        const tenant = localStorage.getItem('tenant');
        const tokenStore = localStorage.getItem('tokenStore');
        
        let html = '<h3>Authentication Details</h3>';
        
        // Token
        if (token) {
          const hasSpaces = /\s/.test(token);
          html += `<p><strong>Token:</strong> ${token.substring(0, 10)}...${token.substring(token.length - 5)} (${token.length} characters)</p>`;
          html += `<p><strong>Token has spaces:</strong> <span class="${hasSpaces ? 'error' : 'success'}">${hasSpaces ? 'Yes (Problem!)' : 'No'}</span></p>`;
        } else {
          html += '<p><strong>Token:</strong> <span class="error">Not found</span></p>';
        }
        
        // Schema
        if (schema) {
          html += `<p><strong>Schema:</strong> ${schema}</p>`;
        } else {
          html += '<p><strong>Schema:</strong> <span class="error">Not found</span></p>';
        }
        
        // Tenant
        if (tenant) {
          try {
            const tenantObj = JSON.parse(tenant);
            html += `<p><strong>Tenant:</strong> ${tenantObj.name || 'Unknown'}</p>`;
            html += `<p><strong>Tenant Schema:</strong> ${tenantObj.schema_name || 'Unknown'}</p>`;
          } catch (e) {
            html += '<p><strong>Tenant:</strong> <span class="error">Invalid format</span></p>';
          }
        } else {
          html += '<p><strong>Tenant:</strong> <span class="error">Not found</span></p>';
        }
        
        // TokenStore
        if (tokenStore) {
          try {
            const tokenStoreObj = JSON.parse(tokenStore);
            const schemas = Object.keys(tokenStoreObj);
            html += `<p><strong>TokenStore:</strong> Contains ${schemas.length} schemas</p>`;
            html += '<ul>';
            schemas.forEach(s => {
              const schemaToken = tokenStoreObj[s];
              const hasSpaces = /\s/.test(schemaToken);
              html += `<li>${s}: ${schemaToken.substring(0, 5)}...${schemaToken.substring(schemaToken.length - 5)} (${schemaToken.length} characters) - Has spaces: <span class="${hasSpaces ? 'error' : 'success'}">${hasSpaces ? 'Yes (Problem!)' : 'No'}</span></li>`;
            });
            html += '</ul>';
          } catch (e) {
            html += '<p><strong>TokenStore:</strong> <span class="error">Invalid format</span></p>';
          }
        } else {
          html += '<p><strong>TokenStore:</strong> <span class="error">Not found</span></p>';
        }
        
        // Cookies
        html += `<p><strong>Cookies:</strong> ${document.cookie || 'None'}</p>`;
        
        authState.innerHTML = html;
      } catch (error) {
        authState.innerHTML = `<p class="error">Error diagnosing authentication: ${error.message}</p>`;
      }
    }
    
    // Function to fix authentication
    function fixAuth() {
      const fixResult = document.getElementById('fix-result');
      fixResult.style.display = 'block';
      
      try {
        // Get token from localStorage
        const token = localStorage.getItem('token');
        if (!token) {
          fixResult.innerHTML = '<p class="error">No token found in localStorage. Please log in again.</p>';
          return;
        }
        
        // Clean token (remove all spaces)
        const cleanToken = token.replace(/\s+/g, '');
        
        // Update token in localStorage
        localStorage.setItem('token', cleanToken);
        
        // Get schema
        const schema = localStorage.getItem('schema_name');
        if (schema) {
          // Update tokenStore
          try {
            const tokenStoreStr = localStorage.getItem('tokenStore');
            let tokenStore = tokenStoreStr ? JSON.parse(tokenStoreStr) : {};
            tokenStore[schema] = cleanToken;
            localStorage.setItem('tokenStore', JSON.stringify(tokenStore));
          } catch (e) {
            console.error('Error updating tokenStore:', e);
          }
          
          // Set cookies
          document.cookie = `auth_token=${encodeURIComponent(cleanToken)}; path=/; SameSite=Lax`;
          document.cookie = `schema_name=${encodeURIComponent(schema)}; path=/; SameSite=Lax`;
        }
        
        // Update tenant if available
        try {
          const tenantStr = localStorage.getItem('tenant');
          if (tenantStr) {
            const tenant = JSON.parse(tenantStr);
            if (tenant && tenant.schema_name) {
              // Update tokenStore for tenant schema
              const tokenStoreStr = localStorage.getItem('tokenStore');
              let tokenStore = tokenStoreStr ? JSON.parse(tokenStoreStr) : {};
              tokenStore[tenant.schema_name] = cleanToken;
              localStorage.setItem('tokenStore', JSON.stringify(tokenStore));
              
              // Set schema if not already set
              if (!schema) {
                localStorage.setItem('schema_name', tenant.schema_name);
                document.cookie = `schema_name=${encodeURIComponent(tenant.schema_name)}; path=/; SameSite=Lax`;
              }
            }
          }
        } catch (e) {
          console.error('Error processing tenant:', e);
        }
        
        fixResult.innerHTML = `
          <p class="success">Authentication fixed successfully!</p>
          <p>Original token: ${token.substring(0, 5)}...${token.substring(token.length - 5)} (${token.length} characters)</p>
          <p>Cleaned token: ${cleanToken.substring(0, 5)}...${cleanToken.substring(cleanToken.length - 5)} (${cleanToken.length} characters)</p>
          <p>Characters removed: ${token.length - cleanToken.length}</p>
          <button onclick="window.location.reload()">Reload Page</button>
        `;
        
        // Refresh the auth state
        diagnoseAuth();
      } catch (error) {
        fixResult.innerHTML = `<p class="error">Error fixing authentication: ${error.message}</p>`;
      }
    }
    
    // Function for aggressive authentication fix
    function fixAuthAggressive() {
      const fixResult = document.getElementById('fix-result');
      fixResult.style.display = 'block';
      
      try {
        // Get token from localStorage
        const token = localStorage.getItem('token');
        if (!token) {
          fixResult.innerHTML = '<p class="error">No token found in localStorage. Please log in again.</p>';
          return;
        }
        
        // Clean token aggressively (remove all non-alphanumeric characters except some special chars)
        const cleanToken = token.replace(/[^a-zA-Z0-9_\-\.]/g, '');
        
        // Update token in localStorage
        localStorage.setItem('token', cleanToken);
        
        // Get schema
        const schema = localStorage.getItem('schema_name');
        if (schema) {
          // Update tokenStore
          try {
            const tokenStoreStr = localStorage.getItem('tokenStore');
            let tokenStore = tokenStoreStr ? JSON.parse(tokenStoreStr) : {};
            tokenStore[schema] = cleanToken;
            localStorage.setItem('tokenStore', JSON.stringify(tokenStore));
          } catch (e) {
            console.error('Error updating tokenStore:', e);
          }
          
          // Set cookies
          document.cookie = `auth_token=${encodeURIComponent(cleanToken)}; path=/; SameSite=Lax`;
          document.cookie = `schema_name=${encodeURIComponent(schema)}; path=/; SameSite=Lax`;
        }
        
        // Update tenant if available
        try {
          const tenantStr = localStorage.getItem('tenant');
          if (tenantStr) {
            const tenant = JSON.parse(tenantStr);
            if (tenant && tenant.schema_name) {
              // Update tokenStore for tenant schema
              const tokenStoreStr = localStorage.getItem('tokenStore');
              let tokenStore = tokenStoreStr ? JSON.parse(tokenStoreStr) : {};
              tokenStore[tenant.schema_name] = cleanToken;
              localStorage.setItem('tokenStore', JSON.stringify(tokenStore));
              
              // Set schema if not already set
              if (!schema) {
                localStorage.setItem('schema_name', tenant.schema_name);
                document.cookie = `schema_name=${encodeURIComponent(tenant.schema_name)}; path=/; SameSite=Lax`;
              }
            }
          }
        } catch (e) {
          console.error('Error processing tenant:', e);
        }
        
        fixResult.innerHTML = `
          <p class="success">Authentication fixed aggressively!</p>
          <p>Original token: ${token.substring(0, 5)}...${token.substring(token.length - 5)} (${token.length} characters)</p>
          <p>Cleaned token: ${cleanToken.substring(0, 5)}...${cleanToken.substring(cleanToken.length - 5)} (${cleanToken.length} characters)</p>
          <p>Characters removed: ${token.length - cleanToken.length}</p>
          <button onclick="window.location.reload()">Reload Page</button>
        `;
        
        // Refresh the auth state
        diagnoseAuth();
      } catch (error) {
        fixResult.innerHTML = `<p class="error">Error fixing authentication: ${error.message}</p>`;
      }
    }
    
    // Function to set manual token
    function setManualToken() {
      const manualResult = document.getElementById('manual-result');
      manualResult.style.display = 'block';
      
      try {
        const token = document.getElementById('manual-token').value.trim();
        const schema = document.getElementById('manual-schema').value.trim();
        
        if (!token) {
          manualResult.innerHTML = '<p class="error">Please enter a token.</p>';
          return;
        }
        
        if (!schema) {
          manualResult.innerHTML = '<p class="error">Please enter a schema name.</p>';
          return;
        }
        
        // Clean token (remove all spaces)
        const cleanToken = token.replace(/\s+/g, '');
        
        // Update token in localStorage
        localStorage.setItem('token', cleanToken);
        
        // Update schema in localStorage
        localStorage.setItem('schema_name', schema);
        
        // Update tokenStore
        try {
          const tokenStoreStr = localStorage.getItem('tokenStore');
          let tokenStore = tokenStoreStr ? JSON.parse(tokenStoreStr) : {};
          tokenStore[schema] = cleanToken;
          localStorage.setItem('tokenStore', JSON.stringify(tokenStore));
        } catch (e) {
          console.error('Error updating tokenStore:', e);
        }
        
        // Set cookies
        document.cookie = `auth_token=${encodeURIComponent(cleanToken)}; path=/; SameSite=Lax`;
        document.cookie = `schema_name=${encodeURIComponent(schema)}; path=/; SameSite=Lax`;
        
        manualResult.innerHTML = `
          <p class="success">Token set successfully!</p>
          <p>Token: ${cleanToken.substring(0, 5)}...${cleanToken.substring(cleanToken.length - 5)} (${cleanToken.length} characters)</p>
          <p>Schema: ${schema}</p>
          <button onclick="window.location.reload()">Reload Page</button>
        `;
        
        // Refresh the auth state
        diagnoseAuth();
      } catch (error) {
        manualResult.innerHTML = `<p class="error">Error setting token: ${error.message}</p>`;
      }
    }
    
    // Initialize the page
    document.addEventListener('DOMContentLoaded', function() {
      diagnoseAuth();
    });
  </script>
</body>
</html>
