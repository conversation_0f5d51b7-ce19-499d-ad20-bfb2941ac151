# Admin Panel Guide for Citizen and ID Card Models

This guide explains how to access and use the Citizen and ID Card models in the Django admin panel based on the hierarchical structure of the system:

1. **Kebele/Center Level**: Citizen registration and ID Card registration
2. **Subcity Level**: ID Card printing
3. **City Level**: Reports and oversight

## Accessing the Admin Panel

1. Start the Django development server:
   ```
   python manage.py runserver
   ```
   Or run the batch file:
   ```
   run_server.bat
   ```

2. Open your browser and go to:
   ```
   http://localhost:8000/admin/
   ```

3. Log in with your admin credentials.

## Available Models

In the admin panel, you'll now see the following sections:

1. **Centers**
   - Center types
   - Centers
   - Cities
   - Domains
   - Global Citizens
   - Global ID Cards
   - Global ID Card Templates
   - Subcities
   - Tenants

2. **Accounts**
   - Users

3. **Auth Token**
   - Tokens

4. **Authentication and Authorization**
   - Groups

## Working with Global Models

The "Global" models provide a read-only view of data from all tenants in a single interface, respecting the hierarchical structure of the system:

### Global Citizens
- Shows all citizens from Kebele/Center level tenants only (where registration happens)
- Includes tenant information for each citizen (name, type, schema)
- Read-only access (cannot add, edit, or delete)
- Can filter by tenant, gender, nationality, etc.
- Can search by name, registration number, etc.

### Global ID Card Templates
- Shows all ID card templates from Kebele/Center level tenants only
- Includes tenant information for each template
- Read-only access (cannot add, edit, or delete)
- Can filter by tenant, default status, etc.
- Can search by name

### Global ID Cards
- Shows all ID cards from all tenants
- Includes tenant information for each ID card
- Read-only access (cannot add, edit, or delete)
- Can filter by tenant, status, issue date, etc.
- Can search by card number, citizen name, etc.
- Useful for tracking ID cards through the entire process (registration at Kebele level, printing at Subcity level)

## How It Works

These global models are proxy models that:
1. Fetch data from tenant schemas based on the hierarchical structure
2. For Citizens and ID Card Templates: Only fetch from Kebele/Center level tenants
3. For ID Cards: Fetch from all tenants to track the entire process
4. Combine the data into a single view
5. Add tenant information to each record (name, type, schema)
6. Present the data in a read-only format

This allows administrators to view data across all tenants without needing to switch between tenant schemas, while respecting the hierarchical structure of the system.

## Tenant-Specific Admin

To access tenant-specific admin interfaces (where you can add, edit, and delete records):

1. Use the tenant-specific URL:
   ```
   http://{tenant-domain}.localhost:8000/admin/
   ```

   For example:
   ```
   http://kebele-test.localhost:8000/admin/
   ```

2. Log in with your tenant-specific admin credentials.

3. You'll have full access to the tenant's models, including the ability to add, edit, and delete records.
