from django.utils.deprecation import MiddlewareMixin
from django_tenants.utils import get_tenant_model, get_public_schema_name
from django.db import connection
from django.contrib.auth import get_user_model
from rest_framework.authtoken.models import Token
from django.http import HttpResponseForbidden
import logging

logger = logging.getLogger(__name__)

class SchemaHeaderMiddleware(MiddlewareMixin):
    """
    Middleware to handle schema selection based on X-Schema-Name header.
    This middleware should be placed BEFORE django-tenants TenantMainMiddleware.
    """
    def process_request(self, request):
        # Print all headers for debugging
        print("\n\n===== REQUEST HEADERS =====")
        for header, value in request.headers.items():
            print(f"{header}: {value}")
        print("===========================\n\n")

        # Print all cookies for debugging
        print("\n\n===== REQUEST COOKIES =====")
        for cookie, value in request.COOKIES.items():
            print(f"{cookie}: {value}")
        print("===========================\n\n")

        # Print auth info
        print(f"Is authenticated: {request.user.is_authenticated if hasattr(request, 'user') else 'No user'}")
        print(f"User: {request.user if hasattr(request, 'user') else 'No user'}")
        print(f"Auth: {request.auth if hasattr(request, 'auth') else 'No auth'}")

        # Check if X-Schema-Name header is present
        schema_name = request.headers.get('X-Schema-Name')
        print(f"X-Schema-Name header: {schema_name}")

        # Also check for schema name in cookies
        schema_name_cookie = request.COOKIES.get('schema_name')
        print(f"Schema name from cookie: {schema_name_cookie}")

        # Use header if present, otherwise use cookie
        effective_schema_name = schema_name or schema_name_cookie
        print(f"Effective schema name: {effective_schema_name}")

        # Get the authorization token
        auth_header = request.headers.get('Authorization', '')
        print(f"Authorization header: {auth_header}")

        if auth_header and auth_header.startswith('Token '):
            token_key = auth_header.split(' ')[1]
            print(f"Token key: {token_key}")

            # Try to get the token from the public schema
            connection.set_schema_to_public()
            try:
                token = Token.objects.get(key=token_key)
                user = token.user
                print(f"Found token in public schema for user: {user.username} (ID: {user.id})")

                # If we have a schema name, try to set the tenant context
                if effective_schema_name:
                    print(f"Setting tenant context for schema: {effective_schema_name}")

                    # Skip for public schema
                    if effective_schema_name == get_public_schema_name():
                        print("Public schema requested, skipping tenant context")
                        return None

                    # Try to get the tenant by schema name
                    try:
                        Client = get_tenant_model()
                        tenant = Client.objects.get(schema_name=effective_schema_name)

                        # Set the tenant for this request
                        request.tenant = tenant
                        connection.set_tenant(tenant)
                        print(f"Set tenant to {tenant.name} (schema: {tenant.schema_name})")

                        # Try to find the user in the tenant's database
                        User = get_user_model()
                        try:
                            tenant_user = User.objects.get(email=user.email)
                            request.user = tenant_user
                            print(f"Found user in tenant schema: {tenant_user.username} (ID: {tenant_user.id})")
                        except User.DoesNotExist:
                            # Create the user in the tenant schema
                            print(f"Creating user in tenant schema: {user.username} (ID: {user.id})")
                            tenant_user = User.objects.create(
                                email=user.email,
                                first_name=user.first_name,
                                last_name=user.last_name,
                                is_staff=user.is_staff,
                                is_superuser=user.is_superuser,
                                is_active=user.is_active
                            )
                            tenant_user.password = user.password
                            tenant_user.save()
                            request.user = tenant_user

                        # Set tenant attributes based on type
                        if tenant.schema_type == 'CITY':
                            request.city = tenant
                            request.subcity = None
                            request.center = None
                        elif tenant.schema_type == 'SUBCITY':
                            request.subcity = tenant
                            request.city = tenant.parent if tenant.parent and tenant.parent.schema_type == 'CITY' else None
                            request.center = None
                        elif tenant.schema_type == 'KEBELE':
                            request.center = tenant
                            request.subcity = tenant.parent if tenant.parent and tenant.parent.schema_type == 'SUBCITY' else None
                            request.city = request.subcity.parent if request.subcity and request.subcity.parent and request.subcity.parent.schema_type == 'CITY' else None

                        logger.debug(f"Set tenant to {tenant.name} (schema: {tenant.schema_name})")

                    except Client.DoesNotExist:
                        logger.warning(f"Tenant with schema {effective_schema_name} does not exist")
                        return HttpResponseForbidden(f"Tenant with schema {effective_schema_name} does not exist")
                    except Exception as e:
                        logger.error(f"Error setting tenant: {str(e)}")
                        return HttpResponseForbidden(f"Error setting tenant: {str(e)}")
            except Token.DoesNotExist:
                print(f"Token {token_key} not found in public schema")
            except Exception as e:
                print(f"Error authenticating user: {str(e)}")

        return None
