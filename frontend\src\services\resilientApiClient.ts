import { API_BASE_URL } from '../config/apiConfig';
import { tokenManager } from './tokenManager';
import { refreshJWTTokens } from './tokenService';

/**
 * Resilient API Client
 * Handles authentication, token refresh, and fallbacks gracefully
 */
export const resilientApiClient = {
  /**
   * Make an authenticated API request with fallback to unauthenticated
   */
  async fetch(endpoint: string, options: RequestInit = {}, schema?: string): Promise<any> {
    // Get the schema to use
    const schemaToUse = schema || localStorage.getItem('schema_name') || localStorage.getItem('jwt_schema');
    if (!schemaToUse) {
      console.error('No schema provided or found');
      throw new Error('No schema found');
    }

    const formattedSchema = schemaToUse.replace(/\s+/g, '_');
    console.log(`ResilientApiClient: Making request to ${endpoint} with schema ${formattedSchema}`);

    // Create URL
    const url = endpoint.startsWith('http')
      ? endpoint
      : `${API_BASE_URL}/api/tenant/${formattedSchema}/${endpoint.replace(/^\//, '')}`;

    // Create headers
    const headers = new Headers(options.headers || {});
    headers.set('Content-Type', 'application/json');
    headers.set('X-Schema-Name', formattedSchema);

    // Skip authentication for GET requests to citizens endpoints
    const isCitizensEndpoint = endpoint.includes('citizens');
    const isGetRequest = !options.method || options.method === 'GET';

    if (isCitizensEndpoint && isGetRequest) {
      console.log('ResilientApiClient: Skipping authentication for GET request to citizens endpoint');
    } else {
      // Get access token for other requests
      const accessToken = tokenManager.getAccessToken(formattedSchema);
      if (accessToken) {
        // Ensure the token is using the Bearer prefix
        const tokenWithPrefix = accessToken.startsWith('Bearer ') ? accessToken : `Bearer ${accessToken}`;
        headers.set('Authorization', tokenWithPrefix);
        console.log('ResilientApiClient: Added Authorization header with Bearer token');
      }
    }

    // Create request options
    const requestOptions: RequestInit = {
      ...options,
      headers,
      credentials: 'include',
    };

    try {
      // Make the authenticated request
      console.log(`ResilientApiClient: Making authenticated request to ${url}`);
      let response = await fetch(url, requestOptions);

      // If unauthorized, try to refresh token and retry (except for citizens endpoints)
      if (response.status === 401 || response.status === 500) {
        // Skip token refresh for GET requests to citizens endpoints
        if (isCitizensEndpoint && isGetRequest) {
          console.log(`ResilientApiClient: Received ${response.status} for citizens endpoint, but skipping token refresh`);

          // Try a direct fetch without authentication
          console.log('ResilientApiClient: Attempting direct fetch without authentication');
          try {
            const directResponse = await fetch(url, {
              method: 'GET',
              headers: {
                'Content-Type': 'application/json',
                'X-Schema-Name': formattedSchema
              },
              credentials: 'include' // Include cookies for CSRF protection
            });

            if (directResponse.ok) {
              console.log('ResilientApiClient: Direct fetch successful');

              // Parse the response
              const responseText = await directResponse.text();
              if (!responseText) {
                console.log('ResilientApiClient: Empty response from direct fetch');
                return null;
              }

              try {
                const parsedData = JSON.parse(responseText);
                console.log('ResilientApiClient: Successfully parsed response:', parsedData);
                return parsedData;
              } catch (e) {
                console.log('ResilientApiClient: Response is not JSON, returning as text');
                return responseText;
              }
            } else {
              console.log(`ResilientApiClient: Direct fetch failed with status ${directResponse.status}`);

              // Try one more time with a different URL format
              const alternativeUrl = `${API_BASE_URL}/api/citizens/?schema=${formattedSchema}&detail=true`;
              console.log(`ResilientApiClient: Trying alternative URL: ${alternativeUrl}`);

              const alternativeResponse = await fetch(alternativeUrl, {
                method: 'GET',
                headers: {
                  'Content-Type': 'application/json',
                  'X-Schema-Name': formattedSchema
                },
                credentials: 'include'
              });

              if (alternativeResponse.ok) {
                console.log('ResilientApiClient: Alternative fetch successful');
                const altResponseText = await alternativeResponse.text();
                if (!altResponseText) {
                  return null;
                }

                try {
                  return JSON.parse(altResponseText);
                } catch (e) {
                  return altResponseText;
                }
              } else {
                console.log('ResilientApiClient: All fetch attempts failed, continuing with error');
                throw new Error('Authentication failed, but continuing without authentication');
              }
            }
          } catch (fetchError) {
            console.error('ResilientApiClient: Error during direct fetch:', fetchError);
            throw new Error('Authentication failed, but continuing without authentication');
          }
        }

        console.log('ResilientApiClient: Received 401, attempting token refresh');

        try {
          // Try to get the current access token first
          const { tokenManager } = await import('./tokenManager');
          const currentAccessToken = tokenManager.getAccessToken(formattedSchema);

          // If we have a current access token, try using it directly first
          if (currentAccessToken) {
            console.log('ResilientApiClient: Using existing access token before attempting refresh');

            // Update Authorization header with current token
            const tokenWithPrefix = currentAccessToken.startsWith('Bearer ') ? currentAccessToken : `Bearer ${currentAccessToken}`;
            headers.set('Authorization', tokenWithPrefix);
            console.log('ResilientApiClient: Added Authorization header with Bearer token for retry');

            // Retry the request with current token
            const retryResponse = await fetch(url, {
              ...options,
              headers,
              credentials: 'include',
            });

            // If this works, use this response
            if (retryResponse.ok) {
              console.log('ResilientApiClient: Request succeeded with existing token');
              response = retryResponse;
              // Skip token refresh
              return response;
            }
          }

          // Try to refresh the token
          try {
            const refreshResult = await refreshJWTTokens(formattedSchema);

            if (refreshResult && refreshResult.access_token) {
              console.log('ResilientApiClient: Token refresh successful, retrying request');

              // Update Authorization header with new token
              const tokenWithPrefix = refreshResult.access_token.startsWith('Bearer ') ? refreshResult.access_token : `Bearer ${refreshResult.access_token}`;
              headers.set('Authorization', tokenWithPrefix);
              console.log('ResilientApiClient: Added Authorization header with new Bearer token after refresh');

              // Retry the request
              response = await fetch(url, {
                ...options,
                headers,
                credentials: 'include',
              });
            }
          } catch (refreshRateLimitError: any) {
            // Check if this is a rate limit error
            if (refreshRateLimitError.message && refreshRateLimitError.message.includes('refreshed too recently')) {
              console.log('ResilientApiClient: Token refresh rate limited, trying to redirect to login page');

              // Set token expired flag
              localStorage.setItem('token_expired', 'true');
              localStorage.setItem('token_expired_reason', 'refresh_rate_limited');
              localStorage.setItem('token_expired_timestamp', Date.now().toString());

              // Throw a specific error that will be caught by the component
              throw new Error('SESSION_EXPIRED');
            } else {
              // Re-throw other refresh errors
              throw refreshRateLimitError;
            }
          }
        } catch (refreshError: any) {
          console.error('ResilientApiClient: Token refresh failed:', refreshError);

          // If it's an invalid refresh token, we need to redirect to login
          if (refreshError.message === 'INVALID_REFRESH_TOKEN') {
            console.log('ResilientApiClient: Invalid refresh token, redirecting to login');

            // Set token expired flag
            localStorage.setItem('token_expired', 'true');
            localStorage.setItem('token_expired_reason', 'invalid_refresh_token');
            localStorage.setItem('token_expired_timestamp', Date.now().toString());

            // Throw a specific error that will be caught by the component
            throw new Error('SESSION_EXPIRED');
          } else {
            // For other refresh errors, we should also redirect to login
            console.log('ResilientApiClient: Token refresh failed, redirecting to login');

            // Set token expired flag
            localStorage.setItem('token_expired', 'true');
            localStorage.setItem('token_expired_reason', 'refresh_failed');
            localStorage.setItem('token_expired_timestamp', Date.now().toString());

            // Throw a specific error that will be caught by the component
            throw new Error('SESSION_EXPIRED');
          }
        }
      }

      // If still not OK, throw error
      if (!response.ok) {
        const errorText = await response.text();
        let errorData;

        try {
          errorData = JSON.parse(errorText);
        } catch (e) {
          errorData = { error: errorText };
        }

        throw new Error(errorData.detail || errorData.error || `Request failed with status ${response.status}`);
      }

      // Parse response
      const responseText = await response.text();
      if (!responseText) {
        return null;
      }

      try {
        return JSON.parse(responseText);
      } catch (e) {
        return responseText;
      }
    } catch (error) {
      console.error('ResilientApiClient: Request failed:', error);
      throw error;
    }
  },

  /**
   * Make a GET request
   */
  async get(endpoint: string, schema?: string, options: RequestInit = {}): Promise<any> {
    return this.fetch(endpoint, { ...options, method: 'GET' }, schema);
  },

  /**
   * Make a POST request
   */
  async post(endpoint: string, data: any, schema?: string, options: RequestInit = {}): Promise<any> {
    return this.fetch(
      endpoint,
      {
        ...options,
        method: 'POST',
        body: JSON.stringify(data)
      },
      schema
    );
  },

  /**
   * Make a PUT request
   */
  async put(endpoint: string, data: any, schema?: string, options: RequestInit = {}): Promise<any> {
    return this.fetch(
      endpoint,
      {
        ...options,
        method: 'PUT',
        body: JSON.stringify(data)
      },
      schema
    );
  },

  /**
   * Make a DELETE request
   */
  async delete(endpoint: string, schema?: string, options: RequestInit = {}): Promise<any> {
    return this.fetch(endpoint, { ...options, method: 'DELETE' }, schema);
  }
};
