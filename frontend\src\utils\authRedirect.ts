/**
 * Authentication Redirect Utility
 *
 * This utility provides functions to handle authentication redirects
 * when tokens are missing, invalid, or expired.
 */

/**
 * Redirect to login page with appropriate error message
 * @param errorType The type of error (no_token, invalid_token, token_expired, etc.)
 * @param schema Optional schema name to include in the redirect URL
 */
export const redirectToLogin = (errorType: string, schema?: string): void => {
  // Build the redirect URL
  let redirectUrl = `/login?error=${encodeURIComponent(errorType)}`;

  // Add the return URL to redirect back after login
  redirectUrl += `&returnUrl=${encodeURIComponent(window.location.pathname)}`;

  // Add the schema if provided
  if (schema) {
    redirectUrl += `&schema=${encodeURIComponent(schema)}`;
  }

  // Log the redirect
  console.log(`Redirecting to login due to auth error: ${errorType}`);

  // Set a flag in sessionStorage to indicate why we're redirecting
  sessionStorage.setItem('auth_redirect_reason', errorType);

  // Check if we're in a React component with access to useNavigate
  // If not, use window.location.href as a fallback
  try {
    // Try to get the navigate function from a global context
    // This is a workaround since we can't use hooks outside of components
    const navigateGlobal = (window as any).__navigateFunction;

    if (navigateGlobal && typeof navigateGlobal === 'function') {
      console.log('Using React Router navigate for redirection');
      navigateGlobal(redirectUrl);
      return;
    }
  } catch (e) {
    console.error('Error using React Router navigate:', e);
  }

  // If we can't use React Router's navigate, use history.pushState instead of location.href
  // This prevents a full page reload
  try {
    console.log('Using history.pushState for redirection');
    window.history.pushState({}, '', redirectUrl);

    // Dispatch a popstate event to trigger route change
    const popStateEvent = new PopStateEvent('popstate', { state: {} });
    window.dispatchEvent(popStateEvent);

    return;
  } catch (e) {
    console.error('Error using history.pushState:', e);
  }

  // As a last resort, use window.location.href
  console.log('Using window.location.href for redirection (will cause page reload)');
  window.location.href = redirectUrl;
};

/**
 * Check if a token exists and redirect to login if not
 * @param schema The schema to check the token for
 * @returns True if a token exists, false otherwise
 */
export const checkTokenExists = (schema: string): boolean => {
  // Import functions dynamically to avoid circular dependencies
  const { getAccessTokenForSchema } = require('../services/tokenService');

  // Get the token for the schema
  const token = getAccessTokenForSchema(schema);

  // If no token, redirect to login
  if (!token) {
    redirectToLogin('no_token', schema);
    return false;
  }

  return true;
};

/**
 * Handle authentication errors by redirecting to login with appropriate error
 * @param error The error object
 * @param schema The schema that was being used
 */
export const handleAuthError = (error: any, schema?: string): void => {
  // Determine the error type
  let errorType = 'unknown_error';

  if (error.message) {
    const message = error.message.toLowerCase();

    if (message.includes('no token') || message.includes('token not found') || message.includes('missing token')) {
      errorType = 'no_token';
    } else if (message.includes('invalid token') || message.includes('token invalid')) {
      errorType = 'invalid_token';
    } else if (message.includes('expired') || message.includes('token expired')) {
      errorType = 'token_expired';
    } else if (message.includes('unauthorized') || message.includes('not authorized')) {
      errorType = 'unauthorized';
    }
  }

  // Redirect to login with the error type
  redirectToLogin(errorType, schema);
};

export default {
  redirectToLogin,
  checkTokenExists,
  handleAuthError
};
