import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Typography,
  Button,
  Chip,
  CircularProgress,
  Alert,
  Card,
  CardContent,
  Grid,
  Divider,
  Paper
} from '@mui/material';
import { useNavigate, useParams } from 'react-router-dom';
import UnauthorizedAccess from '../components/tenant/UnauthorizedAccess';
import IDCardLayout from '../components/IDCardLayout';

// Icons
import CreditCardIcon from '@mui/icons-material/CreditCard';
import PersonIcon from '@mui/icons-material/Person';
import CalendarTodayIcon from '@mui/icons-material/CalendarToday';
import BadgeIcon from '@mui/icons-material/Badge';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import PrintIcon from '@mui/icons-material/Print';
import EditIcon from '@mui/icons-material/Edit';

// Status color mapping
const statusColors: Record<string, string> = {
  'DRAFT': 'default',
  'PENDING': 'warning',
  'APPROVED': 'success',
  'PRINTED': 'info',
  'ISSUED': 'primary',
  'EXPIRED': 'error',
  'REVOKED': 'error'
};

const IDCardDetails: React.FC = () => {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();

  // State for ID card data
  const [idCard, setIdCard] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [generatingId, setGeneratingId] = useState(false);
  const [idGenerationSuccess, setIdGenerationSuccess] = useState(false);
  const [idGenerationError, setIdGenerationError] = useState('');
  const [cardSide, setCardSide] = useState<'front' | 'back'>('front');

  // Get the current tenant and authentication info from localStorage
  const tenantString = localStorage.getItem('tenant');
  const tenant = tenantString ? JSON.parse(tenantString) : null;
  const token = localStorage.getItem('token');
  const schemaName = localStorage.getItem('schema_name');

  // Redirect to login if not authenticated
  useEffect(() => {
    if (!token) {
      navigate('/login');
    }
  }, [token, navigate]);

  // Check if tenant type is allowed to access this page
  const isTenantAuthorized = tenant?.type === 'CENTER' || tenant?.type === 'KEBELE';

  // Fetch ID card data on component mount
  useEffect(() => {
    if (token && id) {
      fetchIDCard();
    }
  }, [token, id]);

  // Fetch ID card from the API
  const fetchIDCard = async () => {
    if (!token) {
      setError('Authentication token not found. Please log in again.');
      setLoading(false);
      return;
    }

    setLoading(true);
    try {
      // Use the tenant-specific API endpoint
      let schema = schemaName || tenant?.schema_name || '';
      // Make sure to properly encode the schema name to handle spaces
      const encodedSchema = encodeURIComponent(schema);
      const url = `http://localhost:8000/api/tenant/${encodedSchema}/idcards/${id}/`;
      console.log('Fetching ID card with URL:', url);

      // Use the hardcoded token for development purposes
      const hardcodedToken = '01aa7be65fbda335a0b29edd56c967ad6112fa6b';
      console.log('Using hardcoded token for development:', hardcodedToken);
      // Log the actual token from localStorage for debugging
      console.log('Actual token from localStorage:', token || 'No token');

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Authorization': `Token ${hardcodedToken}`,
          'Content-Type': 'application/json'
        },
        credentials: 'include'
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch ID card: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      setIdCard(data);
    } catch (error: any) {
      console.error('Error fetching ID card:', error);
      setError(error.message || 'Failed to load ID card details');
    } finally {
      setLoading(false);
    }
  };

  // Generate ID for the citizen
  const handleGenerateID = async () => {
    if (!token) {
      setError('Authentication token not found. Please log in again.');
      return;
    }

    if (!idCard?.citizen) {
      setError('Citizen information not available');
      return;
    }

    setGeneratingId(true);
    setIdGenerationError('');

    try {
      // Use the tenant-specific API endpoint
      let schema = schemaName || tenant?.schema_name || '';
      // Make sure to properly encode the schema name to handle spaces
      const encodedSchema = encodeURIComponent(schema);
      const url = `http://localhost:8000/api/tenant/${encodedSchema}/citizens/${idCard.citizen}/generate-id/`;
      console.log('Generating ID with URL:', url);

      // Use the hardcoded token for development purposes
      const hardcodedToken = '01aa7be65fbda335a0b29edd56c967ad6112fa6b';
      console.log('Using hardcoded token for development:', hardcodedToken);
      // Log the actual token from localStorage for debugging
      console.log('Actual token from localStorage:', token || 'No token');

      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Authorization': `Token ${hardcodedToken}`,
          'Content-Type': 'application/json'
        },
        credentials: 'include'
      });

      if (!response.ok) {
        // If API call fails, throw an error with the response data
        try {
          const errorData = await response.json();
          throw new Error(errorData.detail || errorData.error || JSON.stringify(errorData) || 'Failed to generate ID');
        } catch (jsonError) {
          throw new Error(`HTTP error ${response.status}: ${response.statusText}`);
        }
      }

      const data = await response.json();
      console.log('ID generated successfully:', data);

      // Update the ID card with the new ID number
      if (idCard && data.id_number) {
        setIdCard({
          ...idCard,
          citizen_id_number: data.id_number
        });
      }

      // Show success message
      setIdGenerationSuccess(true);

      // Refresh the ID card details to show the new ID
      fetchIDCard();
    } catch (error: any) {
      console.error('Error generating ID:', error);
      setIdGenerationError(error.message || 'Failed to generate ID');
    } finally {
      setGeneratingId(false);
    }
  };

  // Navigate back to ID cards list
  const handleBackToList = () => {
    navigate('/id-cards');
  };

  // Navigate to edit ID card
  const handleEditIDCard = () => {
    // This would navigate to an edit page if we had one
    console.log(`Edit ID card ${id}`);
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    const options: Intl.DateTimeFormatOptions = { year: 'numeric', month: 'long', day: 'numeric' };
    return new Date(dateString).toLocaleDateString(undefined, options);
  };

  // If tenant is not authorized, show unauthorized access component
  if (!isTenantAuthorized) {
    return (
      <UnauthorizedAccess
        message="Your tenant type does not have permission to view ID card details."
        tenantType={tenant?.type}
      />
    );
  }

  return (
    <Box sx={{ py: 4 }}>
      {/* Banner */}
      <Box
        sx={{
          width: '100%',
          background: 'linear-gradient(135deg, #3f51b5 0%, #5c6bc0 70%, #7986cb 100%)',
          color: 'white',
          position: 'relative',
          overflow: 'hidden',
          pt: 6,
          pb: 8,
          mb: 4,
          boxShadow: '0 4px 20px rgba(0,0,0,0.15)',
          borderRadius: '0 0 20px 20px',
          '&::after': {
            content: '""',
            position: 'absolute',
            bottom: 0,
            left: 0,
            width: '100%',
            height: '70px',
            background: '#f8f9fa',
            borderTopLeftRadius: '50% 60%',
            borderTopRightRadius: '50% 60%',
            transform: 'scaleX(1.5)'
          },
          '&::before': {
            content: '""',
            position: 'absolute',
            top: 0,
            left: 0,
            width: '100%',
            height: '100%',
            backgroundImage: 'url("/id-card-banner.svg")',
            backgroundSize: 'cover',
            backgroundPosition: 'center',
            opacity: 0.1,
            zIndex: 0
          }
        }}
      >
        <Container maxWidth="lg">
          <Box sx={{ textAlign: 'center', position: 'relative', zIndex: 2 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', mb: 2 }}>
              <CreditCardIcon sx={{ fontSize: 40, color: 'white', mr: 2 }} />
              <Typography variant="h4" sx={{ color: 'white', fontWeight: 700 }}>
                ID Card Details
              </Typography>
            </Box>
            <Typography variant="h6" sx={{ color: 'rgba(255,255,255,0.9)', mb: 1, maxWidth: '800px', mx: 'auto' }}>
              View and manage ID card information
            </Typography>
            <Typography variant="body1" sx={{ color: 'rgba(255,255,255,0.8)', maxWidth: '700px', mx: 'auto' }}>
              Generate ID numbers, print cards, and update card status.
            </Typography>
          </Box>
        </Container>
      </Box>

      <Container maxWidth="lg">
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Button
            startIcon={<ArrowBackIcon />}
            onClick={handleBackToList}
            variant="outlined"
            sx={{
              borderRadius: 2,
              px: 3,
              py: 1,
              boxShadow: '0 2px 8px rgba(0,0,0,0.05)',
              '&:hover': {
                boxShadow: '0 4px 12px rgba(0,0,0,0.1)',
                backgroundColor: 'rgba(63, 81, 181, 0.04)'
              }
            }}
          >
            Back to ID Cards
          </Button>

          {idCard && idCard.status === 'APPROVED' && (
            <Button
              startIcon={<PrintIcon />}
              variant="contained"
              color="primary"
              disabled={!idCard.citizen_id_number}
              sx={{
                borderRadius: 2,
                px: 3,
                py: 1,
                boxShadow: '0 4px 10px rgba(63, 81, 181, 0.2)',
                '&:hover': {
                  boxShadow: '0 6px 15px rgba(63, 81, 181, 0.3)'
                }
              }}
            >
              Print ID Card
            </Button>
          )}
        </Box>

        {/* Error message */}
        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}

        {/* Loading state */}
        {loading ? (
          <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', py: 8 }}>
            <CircularProgress size={60} />
            <Typography variant="h6" color="text.secondary" sx={{ mt: 3 }}>
              Loading ID card details...
            </Typography>
          </Box>
        ) : idCard ? (
          <Grid container spacing={4}>
            {/* Left column - ID Card Preview */}
            <Grid item xs={12} md={5}>
              <Card
                sx={{
                  height: '100%',
                  borderRadius: 3,
                  overflow: 'hidden',
                  boxShadow: '0 6px 18px rgba(0,0,0,0.1)',
                  border: '1px solid rgba(0, 0, 0, 0.05)',
                  position: 'relative',
                  '&::before': {
                    content: '""',
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    width: '100%',
                    height: '4px',
                    backgroundColor: 'primary.main',
                    zIndex: 1
                  }
                }}
              >
                <Box sx={{
                  p: 3,
                  display: 'flex',
                  alignItems: 'center',
                  bgcolor: 'white',
                  color: 'text.primary',
                  borderBottom: '1px solid rgba(0, 0, 0, 0.05)'
                }}>
                  <Box
                    sx={{
                      mr: 2,
                      bgcolor: 'primary.main',
                      color: 'white',
                      p: 1.5,
                      borderRadius: 2,
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center'
                    }}
                  >
                    <CreditCardIcon />
                  </Box>
                  <Typography variant="h6" sx={{ fontWeight: 600 }}>
                    ID Card Preview
                  </Typography>
                  <Box sx={{
                    display: 'flex',
                    gap: 1,
                    ml: 'auto',
                    p: 1,
                    bgcolor: 'rgba(0, 0, 0, 0.02)',
                    borderRadius: 2
                  }}>
                    <Chip
                      label="Front Side"
                      color="primary"
                      size="small"
                      variant={cardSide === 'front' ? 'filled' : 'outlined'}
                      onClick={() => setCardSide('front')}
                      sx={{ fontWeight: 500 }}
                    />
                    <Chip
                      label="Back Side"
                      color="primary"
                      size="small"
                      variant={cardSide === 'back' ? 'filled' : 'outlined'}
                      onClick={() => setCardSide('back')}
                      sx={{ fontWeight: 500 }}
                    />
                  </Box>
                </Box>
                <CardContent sx={{ p: 3 }}>
                  <IDCardLayout
                    idCard={idCard}
                    showFront={cardSide === 'front'}
                    formatDate={formatDate}
                  />
                </CardContent>
              </Card>
            </Grid>

            {/* Right column - ID Card Details */}
            <Grid item xs={12} md={7}>
              <Card sx={{ height: '100%', borderRadius: 3 }}>
                <Box sx={{ p: 3, borderBottom: '1px solid rgba(0,0,0,0.1)' }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <Box
                        sx={{
                          mr: 2,
                          bgcolor: 'secondary.main',
                          color: 'white',
                          p: 1.5,
                          borderRadius: 2,
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center'
                        }}
                      >
                        <PersonIcon />
                      </Box>
                      <Box>
                        <Typography variant="h6" sx={{ fontWeight: 600 }}>
                          ID Card Information
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          Details about the ID card and citizen
                        </Typography>
                      </Box>
                    </Box>
                    <Chip
                      label={idCard.status}
                      color={statusColors[idCard.status] as any || 'default'}
                      size="small"
                      sx={{ fontWeight: 500 }}
                    />
                  </Box>
                </Box>
                <CardContent sx={{ p: 3 }}>
                  <Box sx={{ mb: 3 }}>
                    <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 2 }}>
                      Citizen Information
                    </Typography>
                    <Paper elevation={0} sx={{ p: 2, bgcolor: 'rgba(0,0,0,0.02)', borderRadius: 2 }}>
                      <Grid container spacing={2}>
                        <Grid item xs={12} sm={6}>
                          <Typography variant="body2" color="text.secondary">
                            Full Name
                          </Typography>
                          <Typography variant="body1" sx={{ fontWeight: 600 }}>
                            {idCard.citizen_name}
                          </Typography>
                        </Grid>
                        <Grid item xs={12} sm={6}>
                          <Typography variant="body2" color="text.secondary">
                            ID Number
                          </Typography>
                          <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            <Typography variant="body1" sx={{ fontWeight: 600 }}>
                              {idCard.citizen_id_number || 'Not Assigned'}
                            </Typography>
                            {!idCard.citizen_id_number && (
                              <Button
                                variant="contained"
                                color="primary"
                                size="small"
                                onClick={handleGenerateID}
                                disabled={generatingId}
                                sx={{ ml: 2, borderRadius: 1 }}
                              >
                                {generatingId ? 'Generating...' : 'Generate ID'}
                              </Button>
                            )}
                          </Box>
                        </Grid>
                        <Grid item xs={12} sm={6}>
                          <Typography variant="body2" color="text.secondary">
                            Center
                          </Typography>
                          <Typography variant="body1" sx={{ fontWeight: 600 }}>
                            {idCard.center_name}
                          </Typography>
                        </Grid>
                        <Grid item xs={12} sm={6}>
                          <Typography variant="body2" color="text.secondary">
                            Status
                          </Typography>
                          <Chip
                            label={idCard.status}
                            color={statusColors[idCard.status] as any || 'default'}
                            size="small"
                            sx={{ fontWeight: 600 }}
                          />
                        </Grid>
                      </Grid>
                    </Paper>
                  </Box>

                  <Divider sx={{ my: 3 }} />

                  <Box sx={{ mb: 3 }}>
                    <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 2 }}>
                      Card Details
                    </Typography>
                    <Paper elevation={0} sx={{ p: 2, bgcolor: 'rgba(0,0,0,0.02)', borderRadius: 2 }}>
                      <Grid container spacing={2}>
                        <Grid item xs={12} sm={6}>
                          <Typography variant="body2" color="text.secondary">
                            Issue Date
                          </Typography>
                          <Typography variant="body1" sx={{ fontWeight: 600 }}>
                            {formatDate(idCard.issue_date)}
                          </Typography>
                        </Grid>
                        <Grid item xs={12} sm={6}>
                          <Typography variant="body2" color="text.secondary">
                            Expiry Date
                          </Typography>
                          <Typography variant="body1" sx={{ fontWeight: 600 }}>
                            {formatDate(idCard.expiry_date)}
                          </Typography>
                        </Grid>
                        <Grid item xs={12} sm={6}>
                          <Typography variant="body2" color="text.secondary">
                            Card Number
                          </Typography>
                          <Typography variant="body1" sx={{ fontWeight: 600 }}>
                            {idCard.card_number || 'Not Available'}
                          </Typography>
                        </Grid>
                        <Grid item xs={12} sm={6}>
                          <Typography variant="body2" color="text.secondary">
                            Created Date
                          </Typography>
                          <Typography variant="body1" sx={{ fontWeight: 600 }}>
                            {formatDate(idCard.created_at)}
                          </Typography>
                        </Grid>
                      </Grid>
                    </Paper>
                  </Box>

                  {/* ID Generation Success/Error Messages */}
                  {idGenerationSuccess && (
                    <Alert severity="success" sx={{ mb: 3 }}>
                      ID number generated successfully!
                    </Alert>
                  )}

                  {idGenerationError && (
                    <Alert severity="error" sx={{ mb: 3 }}>
                      {idGenerationError}
                    </Alert>
                  )}

                  <Box sx={{ display: 'flex', justifyContent: 'center', gap: 2, mt: 4 }}>
                    {idCard.status === 'APPROVED' && (
                      <Button
                        variant="contained"
                        color="primary"
                        startIcon={<PrintIcon />}
                        disabled={!idCard.citizen_id_number}
                        sx={{
                          borderRadius: 2,
                          px: 3,
                          py: 1,
                          boxShadow: '0 4px 10px rgba(63, 81, 181, 0.2)',
                          '&:hover': {
                            boxShadow: '0 6px 15px rgba(63, 81, 181, 0.3)'
                          }
                        }}
                      >
                        Print ID Card
                      </Button>
                    )}
                    <Button
                      variant="outlined"
                      startIcon={<EditIcon />}
                      onClick={handleEditIDCard}
                      sx={{
                        borderRadius: 2,
                        px: 3,
                        py: 1,
                        borderColor: '#0066b2',
                        color: '#0066b2',
                        '&:hover': {
                          borderColor: '#0077cc',
                          backgroundColor: 'rgba(0,102,178,0.05)'
                        }
                      }}
                    >
                      Edit Card
                    </Button>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        ) : (
          <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', py: 8 }}>
            <Typography variant="h6" color="text.secondary">
              ID card not found
            </Typography>
            <Button
              startIcon={<ArrowBackIcon />}
              onClick={handleBackToList}
              variant="outlined"
              sx={{ mt: 3 }}
            >
              Back to ID Cards
            </Button>
          </Box>
        )}
      </Container>
    </Box>
  );
};

export default IDCardDetails;
