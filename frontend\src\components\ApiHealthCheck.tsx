import React, { useState, useEffect } from 'react';
import { Box, Typography, Button, Alert, CircularProgress, Paper } from '@mui/material';
import { checkApiConnection } from '../services/api';
import { API_BASE_URL } from '../config/apiConfig';

/**
 * ApiHealthCheck component
 *
 * This component checks the API connection and displays the status.
 * It can be used to diagnose API connection issues.
 */
const ApiHealthCheck: React.FC = () => {
  const [isChecking, setIsChecking] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState<'unknown' | 'success' | 'error'>('unknown');
  const [errorMessage, setErrorMessage] = useState<string | null>(null);

  const checkConnection = async () => {
    setIsChecking(true);
    setErrorMessage(null);

    try {
      console.log(`Checking API connection to ${API_BASE_URL}...`);

      const isConnected = await checkApiConnection();
      setConnectionStatus(isConnected ? 'success' : 'error');
      if (!isConnected) {
        setErrorMessage(`Could not connect to the API at ${API_BASE_URL}. Please check your network connection and server status.`);
      }
    } catch (error) {
      setConnectionStatus('error');
      setErrorMessage(`Error checking API connection: ${error instanceof Error ? error.message : String(error)}`);
    } finally {
      setIsChecking(false);
    }
  };

  useEffect(() => {
    // Check connection when component mounts
    checkConnection();
  }, []);

  return (
    <Paper elevation={3} sx={{ p: 3, mb: 3 }}>
      <Typography variant="h6" gutterBottom>
        API Connection Status
      </Typography>

      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
        <Box sx={{ mr: 2, flexGrow: 1 }}>
          {isChecking ? (
            <Alert severity="info" icon={<CircularProgress size={20} />}>
              Checking API connection...
            </Alert>
          ) : connectionStatus === 'success' ? (
            <Alert severity="success">
              Connected to API at {API_BASE_URL}
            </Alert>
          ) : connectionStatus === 'error' ? (
            <Alert severity="error">
              API Connection Error
              {errorMessage && <Typography variant="body2">{errorMessage}</Typography>}
            </Alert>
          ) : (
            <Alert severity="info">API connection status unknown</Alert>
          )}
        </Box>

        <Button
          variant="outlined"
          onClick={checkConnection}
          disabled={isChecking}
        >
          {isChecking ? 'Checking...' : 'Check Connection'}
        </Button>
      </Box>

      <Typography variant="body2" color="text.secondary">
        If you're experiencing API connection issues, please ensure:
        <ul>
          <li>The backend server is running at {API_BASE_URL}</li>
          <li>There are no CORS or authentication issues</li>
          <li>Your network connection is stable</li>
        </ul>
      </Typography>
    </Paper>
  );
};

export default ApiHealthCheck;
