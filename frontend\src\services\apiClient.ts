/**
 * API Client
 *
 * This module provides a unified API client that uses JWT authentication.
 * It gradually migrates from legacy token authentication to JWT authentication.
 */

import { getCurrentApiService } from './jwtApiService';
import { getAuthHeaders, getCurrentSchema } from './tokenService';

// API base URL
const API_BASE_URL = '/api';

/**
 * Make an API request with JWT authentication
 * @param endpoint The API endpoint
 * @param options Fetch options
 * @returns A promise that resolves to the response
 */
export const apiRequest = async <T>(
  endpoint: string,
  options: RequestInit = {}
): Promise<T> => {
  try {
    // Get the current schema from unified token service
    const schema = getCurrentSchema();

    if (!schema) {
      throw new Error('No schema name found for API request');
    }

    // Get JWT API service
    const apiService = getCurrentApiService();

    // Make the request using JWT authentication
    return await apiService.get<T>(endpoint, options);
  } catch (error) {
    console.error('Error making API request:', error);
    throw error;
  }
};

/**
 * Get data from an API endpoint
 * @param endpoint The API endpoint
 * @returns A promise that resolves to the response
 */
export const getData = async <T>(endpoint: string): Promise<T> => {
  return apiRequest<T>(endpoint, { method: 'GET' });
};

/**
 * Post data to an API endpoint
 * @param endpoint The API endpoint
 * @param data The request body
 * @returns A promise that resolves to the response
 */
export const postData = async <T>(endpoint: string, data: any): Promise<T> => {
  return apiRequest<T>(endpoint, {
    method: 'POST',
    body: JSON.stringify(data),
  });
};

/**
 * Put data to an API endpoint
 * @param endpoint The API endpoint
 * @param data The request body
 * @returns A promise that resolves to the response
 */
export const putData = async <T>(endpoint: string, data: any): Promise<T> => {
  return apiRequest<T>(endpoint, {
    method: 'PUT',
    body: JSON.stringify(data),
  });
};

/**
 * Patch data to an API endpoint
 * @param endpoint The API endpoint
 * @param data The request body
 * @returns A promise that resolves to the response
 */
export const patchData = async <T>(endpoint: string, data: any): Promise<T> => {
  return apiRequest<T>(endpoint, {
    method: 'PATCH',
    body: JSON.stringify(data),
  });
};

/**
 * Delete data from an API endpoint
 * @param endpoint The API endpoint
 * @returns A promise that resolves to the response
 */
export const deleteData = async <T>(endpoint: string): Promise<T> => {
  return apiRequest<T>(endpoint, { method: 'DELETE' });
};

/**
 * Get authentication headers for API requests
 * @returns Headers object with Authorization header
 */
export const getApiHeaders = (): HeadersInit => {
  // Get the current schema from unified token service
  const schema = getCurrentSchema() || '';

  // Get JWT authentication headers
  return getAuthHeaders(schema);
};

export default {
  apiRequest,
  getData,
  postData,
  putData,
  patchData,
  deleteData,
  getApiHeaders,
};
