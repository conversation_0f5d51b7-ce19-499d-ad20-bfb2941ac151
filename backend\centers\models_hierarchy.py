from django.db import models
from django.utils.text import slugify
from .models_region import Timestamp, Country, Region
from .models import City as CityAdministration

class SubCity(Timestamp):
    """SubCity model to represent a sub-city area"""
    city = models.ForeignKey(CityAdministration, on_delete=models.CASCADE, related_name='subcities')
    name = models.CharField(max_length=100, unique=True)
    code = models.CharField(max_length=10, unique=True, blank=True, null=True)
    is_active = models.BooleanField(default=True)  # Boolean field for activity status
    
    # Auto-generated code logic, can be done via override of save method if needed
    def save(self, *args, **kwargs):
        if not self.code:
            # Save first to get an ID if this is a new object
            if not self.id:
                super().save(*args, **kwargs)
                self.code = f"GD-SC{self.id:02d}"  # Generates code like GD-SC01, GD-SC02, etc.
                return super().save(*args, **kwargs)
            else:
                self.code = f"GD-SC{self.id:02d}"
        return super().save(*args, **kwargs)
    
    def __str__(self):
        return self.name
    
    class Meta:
        verbose_name = "SubCity"
        verbose_name_plural = "SubCities"


class Kebele(Timestamp):
    """Kebele model to represent a neighborhood within a sub-city"""
    sub_city = models.ForeignKey(SubCity, on_delete=models.CASCADE, related_name="kebeles")
    name = models.CharField(max_length=100)
    code = models.CharField(max_length=10, unique=True, blank=True, null=True)
    is_active = models.BooleanField(default=True)  # Boolean field for activity status
    
    # Auto-generated code logic for kebele
    def save(self, *args, **kwargs):
        if not self.code:
            # Save first to get an ID if this is a new object
            if not self.id:
                super().save(*args, **kwargs)
                self.code = f"GD-KB{self.id:02d}"  # Generates code like GD-KB01, GD-KB02, etc.
                return super().save(*args, **kwargs)
            else:
                self.code = f"GD-KB{self.id:02d}"
        return super().save(*args, **kwargs)
    
    def __str__(self):
        return self.name
    
    class Meta:
        verbose_name = "Kebele"
        verbose_name_plural = "Kebeles"


class Ketena(Timestamp):
    """Ketena model to represent a smaller administrative division within a kebele"""
    kebele = models.ForeignKey(Kebele, on_delete=models.CASCADE, related_name="ketenes")
    name = models.CharField(max_length=100)
    code = models.CharField(max_length=10, unique=True, blank=True, null=True)
    is_active = models.BooleanField(default=True)  # Boolean field for activity status
    
    # Auto-generated code logic for ketena
    def save(self, *args, **kwargs):
        if not self.code:
            # Save first to get an ID if this is a new object
            if not self.id:
                super().save(*args, **kwargs)
                self.code = f"GD-KT{self.id:02d}"  # Generates code like GD-KT01, GD-KT02, etc.
                return super().save(*args, **kwargs)
            else:
                self.code = f"GD-KT{self.id:02d}"
        return super().save(*args, **kwargs)
    
    def __str__(self):
        return self.name
    
    class Meta:
        verbose_name = "Ketena"
        verbose_name_plural = "Ketenes"
