import logging
from django.utils.deprecation import MiddlewareMixin
from django.shortcuts import redirect
from django.contrib.auth import get_user_model
from .jwt_utils import validate_jwt_token, extract_token_from_request

logger = logging.getLogger(__name__)
User = get_user_model()

class AdminAuthMiddleware(MiddlewareMixin):
    """
    Middleware to handle admin authentication using JWT.

    This middleware checks for the presence of an 'Authorization' header with a JWT token
    for requests to the admin site. If the token is valid and belongs to a superuser or
    staff member, the request is allowed to proceed. Otherwise, the user is redirected
    to the admin login page.
    """

    def process_request(self, request):
        # Check if this is a request to the admin site
        if request.path.startswith('/admin/') and not request.path.startswith('/admin-login/'):
            # Skip authentication for the admin login API endpoint
            if request.path == '/api/admin-login/':
                return None

            # Log all headers for debugging
            logger.debug("Admin request headers:")
            for header, value in request.headers.items():
                logger.debug(f"{header}: {value}")

            # Log all cookies for debugging
            logger.debug("Admin request cookies:")
            for cookie, value in request.COOKIES.items():
                logger.debug(f"{cookie}: {value}")

            # Try multiple authentication methods

            # 1. Try JWT token from Authorization header or cookies
            jwt_token = extract_token_from_request(request)
            if jwt_token:
                logger.debug(f"Found JWT token in request")
                try:
                    # Validate token
                    payload = validate_jwt_token(jwt_token)
                    if payload:
                        logger.debug(f"JWT token is valid: {payload}")

                        # Get user from token payload
                        try:
                            user_id = payload.get('sub')
                            if user_id:
                                user = User.objects.get(id=user_id)

                                # Check if user is a superuser or staff
                                if user.is_superuser or user.is_staff:
                                    logger.info(f"Admin access granted to {user.email} using JWT token")
                                    request.user = user
                                    return None
                                else:
                                    logger.warning(f"User {user.email} is not a superuser or staff member")
                            else:
                                logger.warning("No user ID in JWT token payload")
                        except User.DoesNotExist:
                            logger.warning(f"User with ID {payload.get('sub')} not found")
                        except Exception as e:
                            logger.warning(f"Error processing JWT token: {str(e)}")
                except Exception as e:
                    logger.warning(f"Error validating JWT token: {str(e)}")

            # Legacy token authentication has been removed
            # JWT is now the only authentication method for admin

            # 3. Try Django's session authentication (already handled by Django's auth middleware)
            if hasattr(request, 'user') and request.user.is_authenticated:
                if request.user.is_superuser or request.user.is_staff:
                    logger.info(f"Admin access granted to {request.user.email} using session authentication")
                    return None
                else:
                    logger.warning(f"User {request.user.email} is not a superuser or staff member")

            # If all authentication methods fail, redirect to admin login page
            logger.warning(f"No valid authentication found for admin request: {request.path}")
            return redirect('admin-login-page')

        return None
