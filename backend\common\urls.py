from django.urls import path, include
from rest_framework.routers import Default<PERSON>out<PERSON>
from .views import (
    ReligionViewSet, CitizenStatusViewSet, MaritalStatusViewSet,
    EmploymentTypeViewSet, RelationshipTypeViewSet, EmployeeTypeViewSet,
    CountryViewSet, RegionViewSet, KetenaViewSet, DocumentTypeViewSet,
    SubcityViewSet, CenterViewSet
)

# Create a router and register our viewsets with it
router = DefaultRouter()
router.register(r'religions', ReligionViewSet)
router.register(r'citizen-statuses', CitizenStatusViewSet)
router.register(r'marital-statuses', MaritalStatusViewSet)
router.register(r'employment-types', EmploymentTypeViewSet)
router.register(r'relationship-types', RelationshipTypeViewSet)
router.register(r'employee-types', EmployeeTypeViewSet)
router.register(r'countries', CountryViewSet)
router.register(r'regions', RegionViewSet)
router.register(r'ketenas', KetenaViewSet)
router.register(r'document-types', DocumentTypeViewSet)
router.register(r'subcities', SubcityViewSet)
router.register(r'kebeles', CenterViewSet)

# The API URLs are determined automatically by the router
urlpatterns = [
    path('', include(router.urls)),
]
