import React, { useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Box,
  Container,
  Typography,
  Button,
  Paper,
  Alert,
  CircularProgress
} from '@mui/material';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import SaveIcon from '@mui/icons-material/Save';
import FamilyRestroomIcon from '@mui/icons-material/FamilyRestroom';
import PageBanner from '../components/PageBanner';
import RegisterCitizen from './RegisterCitizen';

const AddSpouseWrapper: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();

  // State for loading and error
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState(false);

  // Get the current tenant and authentication info from localStorage
  const tenantString = localStorage.getItem('tenant');
  const tenant = tenantString ? JSON.parse(tenantString) : null;
  const jwtToken = localStorage.getItem('jwt_access_token');
  const schemaName = localStorage.getItem('schema_name');

  // Handle form submission
  const handleSubmit = async (formData: any) => {
    setLoading(true);
    setError('');

    try {
      // Extract spouse data from the form data
      const spouseData = {
        first_name: formData.spouse_first_name,
        middle_name: formData.spouse_middle_name || '',
        last_name: formData.spouse_last_name,
        phone: formData.spouse_phone || '',
        email: formData.spouse_email || '',
        is_resident: formData.spouse_is_resident || false,
        linked_citizen_id: formData.spouse_linked_citizen || null
      };

      // Use the tenant-specific API endpoint
      let schema = schemaName || tenant?.schema_name || '';
      const encodedSchema = encodeURIComponent(schema);
      const url = `http://localhost:8000/api/tenant/${encodedSchema}/citizens/${id}/spouse/`;

      // Use the JWT token from localStorage
      if (!jwtToken) {
        throw new Error('No authentication token found. Please log in again.');
      }

      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${jwtToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(spouseData),
        credentials: 'include'
      });

      if (!response.ok) {
        try {
          const errorData = await response.json();
          throw new Error(errorData.detail || 'Failed to add spouse');
        } catch (jsonError) {
          // If the response is not valid JSON, use the status text
          const errorMessage = `${response.status} ${response.statusText}`;
          throw new Error(`Failed to add spouse: ${errorMessage}`);
        }
      }

      setSuccess(true);
      setTimeout(() => {
        navigate(`/citizens/${id}`);
      }, 2000);
    } catch (error: any) {
      console.error('Error adding spouse:', error);

      // Provide more helpful error messages
      if (error.message.includes('No authentication token found')) {
        setError('Authentication error: Please log in again to continue.');
      } else if (error.message.includes('401')) {
        setError('Authentication error: Your session may have expired. Please log in again.');
      } else if (error.message.includes('500')) {
        setError('Server error: The server encountered an issue. Please try again later or contact support.');
      } else {
        setError(error.message || 'Failed to add spouse');
      }
    } finally {
      setLoading(false);
    }
  };

  // Handle cancel
  const handleCancel = () => {
    navigate(`/citizens/${id}`);
  };

  return (
    <Box sx={{ py: 4 }}>
      {/* Banner */}
      <PageBanner
        title="Add Spouse Information"
        subtitle="Add spouse details for the citizen"
        icon={<FamilyRestroomIcon sx={{ fontSize: 50, color: 'white' }} />}
      />

      <Container maxWidth="md">
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            mb: 4,
            mt: 1
          }}
        >
          <Button
            startIcon={<ArrowBackIcon />}
            onClick={handleCancel}
            variant="outlined"
            sx={{ borderRadius: 2 }}
          >
            Back to Citizen
          </Button>
        </Box>

        {/* Error message */}
        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}

        {/* Success message */}
        {success && (
          <Alert severity="success" sx={{ mb: 3 }}>
            Spouse information added successfully! Redirecting...
          </Alert>
        )}

        <Paper
          sx={{
            p: 4,
            borderRadius: 3,
            boxShadow: '0 8px 24px rgba(0,0,0,0.12)',
            mb: 4
          }}
        >
          <Typography variant="h5" sx={{ mb: 3, fontWeight: 600 }}>
            Spouse Information
          </Typography>

          {/* Use the RegisterCitizen component with only the spouse form visible */}
          <RegisterCitizen
            mode="spouse-only"
            citizenId={id}
            onSubmit={handleSubmit}
          />

          <Box sx={{ mt: 4, display: 'flex', justifyContent: 'flex-end', gap: 2 }}>
            <Button
              variant="outlined"
              onClick={handleCancel}
              sx={{ borderRadius: 2, px: 3, py: 1.2 }}
            >
              Cancel
            </Button>

            <Button
              type="submit"
              variant="contained"
              color="primary"
              startIcon={loading ? <CircularProgress size={20} color="inherit" /> : <SaveIcon />}
              disabled={loading}
              sx={{ borderRadius: 2, px: 3, py: 1.2 }}
              onClick={() => {
                // Trigger form submission in the RegisterCitizen component
                const submitEvent = new CustomEvent('submit-spouse-form');
                document.dispatchEvent(submitEvent);
              }}
            >
              {loading ? 'Saving...' : 'Save Spouse Information'}
            </Button>
          </Box>
        </Paper>
      </Container>
    </Box>
  );
};

export default AddSpouseWrapper;
