import requests
import json

# Base URL
base_url = 'http://127.0.0.1:8000/api/'

# Test login
def test_login():
    print("Testing login...")
    url = 'http://127.0.0.1:8000/api/login/'
    data = {
        'email': '<EMAIL>',
        'password': 'admin123'
    }
    response = requests.post(url, json=data)
    print(f"Status code: {response.status_code}")
    print(f"Response: {response.text}")

    if response.status_code == 200:
        return response.json().get('token')
    return None

# Test getting centers
def test_get_centers(token):
    print("\nTesting get centers...")
    url = 'http://127.0.0.1:8000/api/centers-simple/'
    headers = {'Authorization': f'Token {token}'}
    response = requests.get(url, headers=headers)
    print(f"Status code: {response.status_code}")
    print(f"Response: {json.dumps(response.json(), indent=2)}")

# Test getting citizens
def test_get_citizens(token):
    print("\nTesting get citizens...")
    url = base_url + 'citizens/'
    headers = {'Authorization': f'Token {token}'}
    response = requests.get(url, headers=headers)
    print(f"Status code: {response.status_code}")
    print(f"Response: {json.dumps(response.json(), indent=2)}")

# Test getting ID cards
def test_get_idcards(token):
    print("\nTesting get ID cards...")
    url = base_url + 'idcards/'
    headers = {'Authorization': f'Token {token}'}
    response = requests.get(url, headers=headers)
    print(f"Status code: {response.status_code}")
    print(f"Response: {json.dumps(response.json(), indent=2)}")

# Run tests
if __name__ == '__main__':
    token = test_login()
    if token:
        test_get_centers(token)
        test_get_citizens(token)
        test_get_idcards(token)
    else:
        print("Login failed. Cannot continue with other tests.")
