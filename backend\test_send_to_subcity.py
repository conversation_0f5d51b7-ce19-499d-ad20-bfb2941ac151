import requests
import json

# Base URL
base_url = 'http://127.0.0.1:8000/api/'

# Token and schema name
token = '01aa7be65fbda335a0b29edd56c967ad6112fa6b'  # This is the hardcoded token from IDCardDetails.tsx
schema_name = 'kebele 14'  # Use a kebele schema

# First, let's get a list of ID cards to find one that can be sent to subcity
print("\n=== Getting ID Cards from Kebele ===")
try:
    url = base_url + f'tenant/{schema_name}/idcards/'
    headers = {
        'Authorization': f'Token {token}',
        'Content-Type': 'application/json',
        'X-Schema-Name': schema_name
    }
    
    print(f"URL: {url}")
    print(f"Headers: {headers}")
    
    response = requests.get(url, headers=headers)
    print(f"Status Code: {response.status_code}")
    
    if response.status_code == 200:
        data = response.json()
        if isinstance(data, dict) and 'results' in data:
            id_cards = data['results']
        else:
            id_cards = data
            
        print(f"Found {len(id_cards)} ID cards")
        
        # Find an ID card that is approved by kebele leader and has kebele pattern
        suitable_cards = []
        for card in id_cards:
            print(f"\nID Card {card.get('id')}:")
            print(f"  Status: {card.get('status')}")
            print(f"  Kebele Approval Status: {card.get('kebele_approval_status')}")
            print(f"  Kebele Pattern: {card.get('kebele_pattern')}")
            
            if (card.get('kebele_approval_status') == 'APPROVED' and 
                card.get('kebele_pattern') == True and
                card.get('status') != 'PENDING_SUBCITY'):
                suitable_cards.append(card)
                print("  ✓ This card can be sent to subcity")
            else:
                print("  ✗ This card cannot be sent to subcity")
        
        if suitable_cards:
            # Test sending the first suitable ID card to subcity
            test_card = suitable_cards[0]
            card_id = test_card['id']
            
            print(f"\n=== Testing Send to Subcity API for ID Card {card_id} ===")
            send_url = base_url + f'tenant/{schema_name}/idcards/{card_id}/send_to_subcity/'
            
            print(f"URL: {send_url}")
            print(f"Headers: {headers}")
            
            send_response = requests.post(
                send_url, 
                headers=headers,
                json={"notes": "Test note for sending to subcity"}
            )
            
            print(f"Status Code: {send_response.status_code}")
            
            if send_response.status_code == 200:
                send_data = send_response.json()
                print(f"Response Data: {json.dumps(send_data, indent=2)}")
                print(f"New Status: {send_data.get('status')}")
            else:
                print(f"Error: {send_response.text}")
        else:
            print("\nNo suitable ID cards found that can be sent to subcity")
            print("To send an ID card to subcity, it must:")
            print("1. Have kebele_approval_status = 'APPROVED'")
            print("2. Have kebele_pattern = True")
            print("3. Not already have status = 'PENDING_SUBCITY'")
    else:
        print(f"Error: {response.text}")
        
except Exception as e:
    print(f"Error testing API: {str(e)}")
