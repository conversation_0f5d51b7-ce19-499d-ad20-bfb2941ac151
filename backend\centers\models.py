from django.db import models
from django.utils.text import slugify
from django.conf import settings as django_settings
from django.core.exceptions import ValidationError
from django.core.validators import MinValueValidator
import uuid
from django_tenants.models import TenantMixin, DomainMixin
from .models_region import Region, Country, Timestamp

class City(Timestamp):
    """Model representing a city administration with detailed information."""
    city_code = models.CharField(max_length=10, unique=True)
    country = models.ForeignKey(Country, on_delete=models.SET_NULL, null=True, blank=True)
    region = models.ForeignKey(Region, on_delete=models.SET_NULL, null=True, blank=True, related_name='cities')
    city_name = models.CharField(max_length=100)
    logo = models.ImageField(upload_to='logos/', blank=True, null=True)
    motto_slogan = models.TextField(blank=True, null=True)
    city_intro = models.TextField(blank=True, null=True)
    mayor_name = models.CharField(max_length=100, blank=True, null=True)
    deputy_mayor = models.CharField(max_length=100, blank=True, null=True)
    contact_email = models.EmailField(blank=True, null=True)  # renamed to contact_email
    contact_phone = models.CharField(max_length=20, blank=True, null=True)  # renamed to contact_phone
    google_maps_url = models.URLField(blank=True, null=True)
    area_sq_km = models.FloatField(blank=True, null=True, validators=[MinValueValidator(0.0)])
    elevation_meters = models.IntegerField(blank=True, null=True)
    timezone = models.CharField(max_length=50, default="EAT")
    website = models.URLField(blank=True, null=True)
    headquarter_address = models.TextField(blank=True, null=True)
    postal_code = models.CharField(max_length=20, blank=True, null=True)
    established_date = models.DateField(blank=True, null=True)
    is_resident = models.BooleanField(default=False)  # Boolean field to check if the city is a resident
    is_active = models.BooleanField(default=True)  # Boolean field for activity status
    created_by = models.ForeignKey(django_settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True,
                                 related_name='created_cities')

    def __str__(self):
        return f"{self.city_name} ({self.city_code})"

    class Meta:
        verbose_name = "City Administration"
        verbose_name_plural = "City Administrations"
        ordering = ['city_name', 'city_code']

    def save(self, *args, **kwargs):
        # Generate unique code if not provided
        if not self.city_code:
            name_prefix = ''.join(c for c in self.city_name[:3] if c.isalnum()).upper()
            if not name_prefix:
                name_prefix = 'CTY'
            random_suffix = str(uuid.uuid4())[:4].upper()
            self.city_code = f"{name_prefix}-{random_suffix}"

        super().save(*args, **kwargs)

class Subcity(Timestamp):
    """Model representing a subcity (middle level in hierarchy)."""
    name = models.CharField(max_length=100, unique=True)
    slug = models.SlugField(max_length=120, blank=True)
    code = models.CharField(max_length=10, unique=True, blank=True, null=True, help_text="Unique code for the subcity")
    description = models.TextField(blank=True)

    # Hierarchy
    city = models.ForeignKey(City, on_delete=models.CASCADE, related_name='subcities')

    # Contact information
    address = models.TextField(blank=True)
    phone = models.CharField(max_length=20, blank=True)
    email = models.EmailField(blank=True)
    website = models.URLField(blank=True)

    # Visual identity
    logo = models.ImageField(upload_to='subcity_logos/', blank=True, null=True)
    header_color = models.CharField(max_length=20, blank=True, default='#3498db')
    accent_color = models.CharField(max_length=20, blank=True, default='#2980b9')

    # Administrative information
    admin_name = models.CharField(max_length=100, blank=True)
    admin_email = models.EmailField(blank=True)
    admin_phone = models.CharField(max_length=20, blank=True)

    # Printing capabilities
    has_printing_facility = models.BooleanField(default=True, help_text="Whether this subcity can print ID cards")
    printing_capacity = models.PositiveIntegerField(default=100, help_text="Number of ID cards that can be printed per day")

    # Status and timestamps
    is_active = models.BooleanField(default=True)
    created_by = models.ForeignKey(django_settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True,
                                 related_name='created_subcities')

    class Meta:
        verbose_name = "SubCity"
        verbose_name_plural = "SubCities"
        ordering = ['city', 'name']

    def __str__(self):
        return f"{self.name} ({self.city.city_name})"

    def save(self, *args, **kwargs):
        # Generate slug if not provided
        if not self.slug:
            self.slug = slugify(self.name)

        # Generate unique code if not provided
        if not self.code:
            # Save first to get an ID if this is a new object
            if not self.id:
                super().save(*args, **kwargs)
                self.code = f"GD-SC{self.id:02d}"  # Generates code like GD-SC01, GD-SC02, etc.
                return super().save(*args, **kwargs)
            else:
                self.code = f"GD-SC{self.id:02d}"

        super().save(*args, **kwargs)

class CenterType(models.Model):
    """Model representing different types of centers."""
    name = models.CharField(max_length=100, unique=True)
    description = models.TextField(blank=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.name

class Kebele(Timestamp):
    """Model representing a center/kebele (lowest level in hierarchy)."""
    # Basic information
    name = models.CharField(max_length=100)
    slug = models.SlugField(max_length=120, blank=True)
    code = models.CharField(max_length=10, unique=True, blank=True, null=True, help_text="Unique code for the center")
    type = models.ForeignKey(CenterType, on_delete=models.SET_NULL, null=True, blank=True, related_name='centers')
    description = models.TextField(blank=True)

    # Hierarchy
    subcity = models.ForeignKey(Subcity, on_delete=models.CASCADE, related_name='centers', null=True, blank=True)

    # Contact information
    address = models.TextField()
    city = models.CharField(max_length=100, blank=True)
    state = models.CharField(max_length=100, blank=True)
    postal_code = models.CharField(max_length=20, blank=True)
    country = models.CharField(max_length=100, default='Ethiopia')
    phone = models.CharField(max_length=20, blank=True)
    alternate_phone = models.CharField(max_length=20, blank=True)
    email = models.EmailField(blank=True)
    website = models.URLField(blank=True)

    # Visual identity
    logo = models.ImageField(upload_to='center_logos/', blank=True, null=True)
    header_color = models.CharField(max_length=20, blank=True, default='#3498db', help_text="Header color in hex format")
    accent_color = models.CharField(max_length=20, blank=True, default='#2980b9', help_text="Accent color in hex format")

    # Administrative information
    admin_name = models.CharField(max_length=100, blank=True, help_text="Name of the center administrator")
    admin_email = models.EmailField(blank=True, help_text="Email of the center administrator")
    admin_phone = models.CharField(max_length=20, blank=True, help_text="Phone of the center administrator")

    # System settings
    max_users = models.PositiveIntegerField(default=10, help_text="Maximum number of users allowed")
    max_citizens = models.PositiveIntegerField(default=1000, help_text="Maximum number of citizens allowed")
    max_id_cards = models.PositiveIntegerField(default=1000, help_text="Maximum number of ID cards allowed")

    # Status and timestamps
    is_active = models.BooleanField(default=True)
    is_verified = models.BooleanField(default=False, help_text="Whether the center has been verified")
    subscription_status = models.CharField(max_length=20, default='trial',
                                        choices=(
                                            ('trial', 'Trial'),
                                            ('basic', 'Basic'),
                                            ('premium', 'Premium'),
                                            ('enterprise', 'Enterprise'),
                                        ))
    subscription_expiry = models.DateField(null=True, blank=True)
    created_by = models.ForeignKey(django_settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True,
                                 related_name='created_centers')

    # Settings and configuration (stored as JSON)
    settings = models.JSONField(default=dict, blank=True, help_text="Center-specific settings")

    class Meta:
        verbose_name = "Kebele"
        verbose_name_plural = "Kebeles"
        ordering = ['subcity', 'name']

    def __str__(self):
        return self.name

    def save(self, *args, **kwargs):
        # Generate slug if not provided
        if not self.slug:
            self.slug = slugify(self.name)

        # Generate unique code if not provided
        if not self.code:
            # Save first to get an ID if this is a new object
            if not self.id:
                super().save(*args, **kwargs)
                self.code = f"GD-KB{self.id:02d}"  # Generates code like GD-KB01, GD-KB02, etc.
                return super().save(*args, **kwargs)
            else:
                self.code = f"GD-KB{self.id:02d}"

        super().save(*args, **kwargs)

    @property
    def user_count(self):
        """Return the number of users associated with this center."""
        return self.users.count()

    @property
    def citizen_count(self):
        """Return the number of citizens associated with this center."""
        return self.citizens.count()

    @property
    def id_card_count(self):
        """Return the number of ID cards associated with this center."""
        return sum(citizen.id_cards.count() for citizen in self.citizens.all())

    @property
    def is_at_user_limit(self):
        """Check if the center has reached its user limit."""
        return self.user_count >= self.max_users

    @property
    def is_at_citizen_limit(self):
        """Check if the center has reached its citizen limit."""
        return self.citizen_count >= self.max_citizens

    @property
    def is_at_id_card_limit(self):
        """Check if the center has reached its ID card limit."""
        return self.id_card_count >= self.max_id_cards

    @property
    def city(self):
        """Return the city this center belongs to."""
        return self.subcity.city if self.subcity else None

    @property
    def city_name(self):
        """Return the city name this center belongs to."""
        return self.subcity.city.city_name if self.subcity and self.subcity.city else None

    def clean(self):
        """Validate that the center belongs to a subcity."""
        # Temporarily commented out for migration
        # if not self.subcity:
        #     raise ValidationError({'subcity': 'A center must belong to a subcity.'})
        pass


class Ketena(Timestamp):
    """Ketena model to represent a smaller administrative division within a kebele"""
    center = models.ForeignKey(Center, on_delete=models.CASCADE, related_name="ketenes")
    name = models.CharField(max_length=100)
    code = models.CharField(max_length=10, unique=True, blank=True, null=True)
    description = models.TextField(blank=True)
    is_active = models.BooleanField(default=True)  # Boolean field for activity status

    # Auto-generated code logic for ketena
    def save(self, *args, **kwargs):
        if not self.code:
            # Save first to get an ID if this is a new object
            if not self.id:
                super().save(*args, **kwargs)
                self.code = f"GD-KT{self.id:02d}"  # Generates code like GD-KT01, GD-KT02, etc.
                return super().save(*args, **kwargs)
            else:
                self.code = f"GD-KT{self.id:02d}"
        return super().save(*args, **kwargs)

    def __str__(self):
        return self.name

    class Meta:
        verbose_name = "Ketena"
        verbose_name_plural = "Ketenes"
        ordering = ['center', 'name']


# Django-tenants models
class Client(TenantMixin):
    """Model representing a tenant in the system.
    This can be a city, subcity, or center depending on the schema_type.
    """
    SCHEMA_TYPE_CHOICES = (
        ('CITY', 'City'),
        ('SUBCITY', 'Subcity'),
        ('KEBELE', 'Kebele'),
    )

    # Basic information
    name = models.CharField(max_length=100)
    schema_type = models.CharField(max_length=10, choices=SCHEMA_TYPE_CHOICES)
    description = models.TextField(blank=True)
    custom_domain = models.CharField(max_length=255, blank=True, null=True, unique=True,
                                   help_text="Custom domain for this tenant (e.g., 'gondar.gov.et')")

    # Hierarchy relationships
    parent = models.ForeignKey('self', on_delete=models.CASCADE, null=True, blank=True, related_name='children')

    # Contact information
    address = models.TextField(blank=True)
    phone = models.CharField(max_length=20, blank=True)
    email = models.EmailField(blank=True)
    email_domain = models.CharField(max_length=255, blank=True, null=True)
    website = models.URLField(blank=True)

    # Visual identity
    logo = models.ImageField(upload_to='tenant_logos/', blank=True, null=True)
    header_color = models.CharField(max_length=7, blank=True, default='#007bff')
    accent_color = models.CharField(max_length=7, blank=True, default='#28a745')

    # Administrative information
    admin_name = models.CharField(max_length=100, blank=True)
    admin_email = models.EmailField(blank=True)
    admin_phone = models.CharField(max_length=20, blank=True)

    # Status
    is_active = models.BooleanField(default=True)

    # City-specific fields
    city_code = models.CharField(max_length=20, blank=True, null=True)
    mayor_name = models.CharField(max_length=100, blank=True, null=True)
    deputy_mayor = models.CharField(max_length=100, blank=True, null=True)
    motto_slogan = models.TextField(blank=True, null=True)
    city_intro = models.TextField(blank=True, null=True)
    established_date = models.DateField(blank=True, null=True)
    area_sq_km = models.FloatField(blank=True, null=True)
    elevation_meters = models.FloatField(blank=True, null=True)
    population = models.IntegerField(blank=True, null=True)
    google_maps_url = models.URLField(blank=True, null=True)

    # For subcities only
    has_printing_facility = models.BooleanField(default=False)
    printing_capacity = models.PositiveIntegerField(default=0, help_text="Number of ID cards that can be printed per day")

    # For centers only
    center_type = models.CharField(max_length=50, blank=True)
    is_verified = models.BooleanField(default=False)
    subscription_status = models.CharField(max_length=20, default='trial',
                                        choices=(
                                            ('trial', 'Trial'),
                                            ('basic', 'Basic'),
                                            ('premium', 'Premium'),
                                            ('suspended', 'Suspended'),
                                        ))
    subscription_expiry = models.DateField(null=True, blank=True)
    max_users = models.PositiveIntegerField(default=5)
    max_citizens = models.PositiveIntegerField(default=1000)
    max_id_cards = models.PositiveIntegerField(default=1000)
    settings = models.JSONField(default=dict, blank=True)

    # System information
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(django_settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, blank=True, related_name='created_tenants')

    class Meta:
        verbose_name = 'Tenant'
        verbose_name_plural = 'Tenants'

    def __str__(self):
        return f"{self.name} ({self.schema_name})"

    def save(self, *args, **kwargs):
        # Generate schema_name if not provided
        if not self.schema_name:
            # Use a combination of schema_type prefix and slugified name
            prefix = self.schema_type.lower()
            slug = slugify(self.name)
            self.schema_name = f"{prefix}_{slug}"

        # Make sure auto_create_schema is True
        self.auto_create_schema = True

        super().save(*args, **kwargs)

class Domain(DomainMixin):
    """Model representing a domain for a tenant."""

    def __str__(self):
        return self.domain


# Citizen Model
class Citizen(models.Model):
    GENDER_CHOICES = [
        ('M', 'Male'),
        ('F', 'Female'),
        ('O', 'Other'),
    ]

    MARITAL_STATUS_CHOICES = [
        ('SINGLE', 'Single'),
        ('MARRIED', 'Married'),
        ('DIVORCED', 'Divorced'),
        ('WIDOWED', 'Widowed'),
    ]

    # Basic Information
    registration_number = models.CharField(max_length=20, unique=True)
    first_name = models.CharField(max_length=100)
    middle_name = models.CharField(max_length=100, blank=True, null=True)
    last_name = models.CharField(max_length=100)
    date_of_birth = models.DateField()
    gender = models.CharField(max_length=1, choices=GENDER_CHOICES)
    marital_status = models.CharField(max_length=10, choices=MARITAL_STATUS_CHOICES)

    # Contact Information
    phone_number = models.CharField(max_length=20, blank=True, null=True)
    email = models.EmailField(blank=True, null=True)

    # Address Information
    address = models.TextField(blank=True, null=True)

    # Additional Information
    nationality = models.CharField(max_length=100, default='Ethiopian')
    occupation = models.CharField(max_length=100, blank=True, null=True)

    # Photo
    photo = models.ImageField(upload_to='citizen_photos/', blank=True, null=True)

    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.first_name} {self.last_name} ({self.registration_number})"


# ID Card Template Model
class IDCardTemplate(models.Model):
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True, null=True)
    is_default = models.BooleanField(default=False)
    front_design = models.JSONField(default=dict)
    back_design = models.JSONField(default=dict)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.name

    def save(self, *args, **kwargs):
        # If this template is set as default, unset default for all other templates
        if self.is_default:
            IDCardTemplate.objects.filter(is_default=True).update(is_default=False)
        super().save(*args, **kwargs)


# ID Card Model
class IDCard(models.Model):
    STATUS_CHOICES = [
        ('DRAFT', 'Draft'),
        ('PENDING', 'Pending Approval'),
        ('APPROVED', 'Approved'),
        ('PRINTED', 'Printed'),
        ('ISSUED', 'Issued'),
        ('EXPIRED', 'Expired'),
        ('REVOKED', 'Revoked'),
    ]

    # Card Information
    card_number = models.CharField(max_length=50, unique=True, blank=True, null=True)
    citizen = models.ForeignKey(Citizen, on_delete=models.CASCADE, related_name='id_cards')
    template = models.ForeignKey(IDCardTemplate, on_delete=models.PROTECT)
    issue_date = models.DateField()
    expiry_date = models.DateField()
    status = models.CharField(max_length=10, choices=STATUS_CHOICES, default='DRAFT')

    # Card Data (can store additional fields specific to the card)
    card_data = models.JSONField(default=dict, blank=True)

    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"ID Card {self.card_number} - {self.citizen.first_name} {self.citizen.last_name}"

    def save(self, *args, **kwargs):
        # Generate card number if not provided
        if not self.card_number:
            # Get tenant code (assuming we're in a tenant context)
            from django_tenants.utils import get_tenant
            tenant = get_tenant()
            tenant_code = tenant.schema_name.upper()

            # Generate a unique ID
            unique_id = str(uuid.uuid4().int)[:8]

            # Format: TENANT-YEAR-UNIQUEID
            import datetime
            year = datetime.datetime.now().year
            self.card_number = f"{tenant_code}-{year}-{unique_id}"

        super().save(*args, **kwargs)
