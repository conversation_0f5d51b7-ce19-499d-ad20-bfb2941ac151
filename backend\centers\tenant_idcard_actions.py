"""
Tenant-specific API endpoints for ID card actions like approval, rejection, etc.
"""
from rest_framework.decorators import api_view, authentication_classes, permission_classes
from rest_framework.authentication import TokenAuthentication
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from rest_framework import status
from django.db import connection
from centers.models import Client
from idcards.models import IDCard
from idcards.serializers import IDCardSerializer
from django.utils import timezone

@api_view(['POST'])
@authentication_classes([TokenAuthentication])
@permission_classes([AllowAny])  # We'll handle authentication manually
def tenant_idcard_kebele_approve(request, schema_name, id):
    """
    Approve an ID card by the kebele leader.
    """
    # Manual authentication
    auth_header = request.headers.get('Authorization', '')
    print(f"\n\nAUTH HEADER for kebele_approve: {auth_header}\n\n")

    if not auth_header or not auth_header.startswith('Token '):
        print(f"\n\nINVALID AUTH HEADER for kebele_approve: {auth_header}\n\n")
        return Response({"error": "Authentication credentials were not provided."}, status=status.HTTP_401_UNAUTHORIZED)

    token_key = auth_header.split(' ')[1]

    try:
        # Get the tenant by schema name
        tenant = Client.objects.get(schema_name=schema_name)

        # Set the tenant for this request
        connection.set_tenant(tenant)

        try:
            # First try to find the token in the tenant's database
            print(f"\n\nLooking for token {token_key} in tenant {tenant.schema_name}\n\n")
            from rest_framework.authtoken.models import Token
            from django.contrib.auth import get_user_model
            User = get_user_model()

            token = Token.objects.get(key=token_key)
            user = token.user
            request.user = user
            print(f"\n\nAuthenticated user: {user.username} (ID: {user.id})\n\n")
        except Token.DoesNotExist:
            # If not found, try the public schema
            print(f"\n\nToken not found in tenant {tenant.schema_name}, trying public schema\n\n")
            connection.set_schema_to_public()
            try:
                token = Token.objects.get(key=token_key)
                print(f"\n\nFound token in public schema for user {token.user.username} (ID: {token.user.id})\n\n")
                # Now switch back to the tenant schema
                connection.set_tenant(tenant)
                # Try to find the user in the tenant's database
                try:
                    user = User.objects.get(email=token.user.email)
                    request.user = user
                    print(f"\n\nFound matching user in tenant {tenant.schema_name}: {user.username} (ID: {user.id})\n\n")
                except User.DoesNotExist:
                    # If the user doesn't exist in the tenant's database, use the public schema user
                    request.user = token.user
                    print(f"\n\nUsing public schema user: {token.user.username} (ID: {token.user.id})\n\n")
            except Token.DoesNotExist:
                print(f"\n\nToken not found in public schema\n\n")
                return Response({"error": "Invalid token."}, status=status.HTTP_401_UNAUTHORIZED)
            except Exception as e:
                print(f"\n\nError authenticating user: {str(e)}\n\n")
                return Response({"error": f"Error authenticating user: {str(e)}"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        # Now we have an authenticated user and the correct tenant context

        # Get the ID card
        try:
            id_card = IDCard.objects.get(id=id)
        except IDCard.DoesNotExist:
            return Response({"error": "ID card not found."}, status=status.HTTP_404_NOT_FOUND)

        # Only check document verification if documents were explicitly rejected
        if id_card.document_verification_status == 'REJECTED':
            return Response(
                {'error': 'Cannot approve ID card with rejected documents'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Get approval notes from request data
        notes = request.data.get('notes', '')

        # Apply the kebele pattern automatically if it hasn't been applied yet
        if not id_card.kebele_pattern:
            try:
                # Import the pattern application function
                from idcards.security_patterns import apply_kebele_pattern

                # Apply the pattern
                print(f"\n\nAutomatically applying kebele pattern during approval\n\n")
                apply_kebele_pattern(id_card)

                # Refresh the ID card to get the updated pattern
                id_card.refresh_from_db()
                print(f"\n\nKebele pattern applied successfully\n\n")
            except Exception as e:
                print(f"\n\nError applying kebele pattern: {str(e)}\n\n")
                # Continue with approval even if pattern application fails
                pass

        # Update the approval status
        id_card.kebele_approval_status = 'APPROVED'
        id_card.kebele_approval_notes = notes
        id_card.kebele_approved_at = timezone.now()
        id_card.kebele_approved_by = request.user

        # Update the ID card status to PENDING for center admin approval
        id_card.status = 'PENDING'

        id_card.save()

        serializer = IDCardSerializer(id_card)
        return Response(serializer.data)

    except Client.DoesNotExist:
        return Response({"error": f"Tenant with schema name '{schema_name}' not found."}, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['POST'])
@authentication_classes([TokenAuthentication])
@permission_classes([AllowAny])  # We'll handle authentication manually
def tenant_idcard_kebele_reject(request, schema_name, id):
    """
    Reject an ID card by the kebele leader.
    """
    # Manual authentication
    auth_header = request.headers.get('Authorization', '')
    print(f"\n\nAUTH HEADER for kebele_reject: {auth_header}\n\n")

    if not auth_header or not auth_header.startswith('Token '):
        print(f"\n\nINVALID AUTH HEADER for kebele_reject: {auth_header}\n\n")
        return Response({"error": "Authentication credentials were not provided."}, status=status.HTTP_401_UNAUTHORIZED)

    token_key = auth_header.split(' ')[1]

    try:
        # Get the tenant by schema name
        tenant = Client.objects.get(schema_name=schema_name)

        # Set the tenant for this request
        connection.set_tenant(tenant)

        try:
            # First try to find the token in the tenant's database
            print(f"\n\nLooking for token {token_key} in tenant {tenant.schema_name}\n\n")
            from rest_framework.authtoken.models import Token
            from django.contrib.auth import get_user_model
            User = get_user_model()

            token = Token.objects.get(key=token_key)
            user = token.user
            request.user = user
            print(f"\n\nAuthenticated user: {user.username} (ID: {user.id})\n\n")
        except Token.DoesNotExist:
            # If not found, try the public schema
            print(f"\n\nToken not found in tenant {tenant.schema_name}, trying public schema\n\n")
            connection.set_schema_to_public()
            try:
                token = Token.objects.get(key=token_key)
                print(f"\n\nFound token in public schema for user {token.user.username} (ID: {token.user.id})\n\n")
                # Now switch back to the tenant schema
                connection.set_tenant(tenant)
                # Try to find the user in the tenant's database
                try:
                    user = User.objects.get(email=token.user.email)
                    request.user = user
                    print(f"\n\nFound matching user in tenant {tenant.schema_name}: {user.username} (ID: {user.id})\n\n")
                except User.DoesNotExist:
                    # If the user doesn't exist in the tenant's database, use the public schema user
                    request.user = token.user
                    print(f"\n\nUsing public schema user: {token.user.username} (ID: {token.user.id})\n\n")
            except Token.DoesNotExist:
                print(f"\n\nToken not found in public schema\n\n")
                return Response({"error": "Invalid token."}, status=status.HTTP_401_UNAUTHORIZED)
            except Exception as e:
                print(f"\n\nError authenticating user: {str(e)}\n\n")
                return Response({"error": f"Error authenticating user: {str(e)}"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        # Now we have an authenticated user and the correct tenant context

        # Get the ID card
        try:
            id_card = IDCard.objects.get(id=id)
        except IDCard.DoesNotExist:
            return Response({"error": "ID card not found."}, status=status.HTTP_404_NOT_FOUND)

        # Get rejection reason from request data
        reason = request.data.get('reason', '')
        if not reason:
            return Response({'error': 'Rejection reason is required'}, status=status.HTTP_400_BAD_REQUEST)

        # Update the approval status
        id_card.kebele_approval_status = 'REJECTED'
        id_card.kebele_approval_notes = reason
        id_card.kebele_approved_at = timezone.now()
        id_card.kebele_approved_by = request.user
        id_card.save()

        serializer = IDCardSerializer(id_card)
        return Response(serializer.data)

    except Client.DoesNotExist:
        return Response({"error": f"Tenant with schema name '{schema_name}' not found."}, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['POST'])
@authentication_classes([TokenAuthentication])
@permission_classes([AllowAny])  # We'll handle authentication manually
def tenant_idcard_apply_kebele_pattern(request, schema_name, id):
    """
    Apply the kebele half of the security pattern to an ID card.
    """
    # Manual authentication
    auth_header = request.headers.get('Authorization', '')
    print(f"\n\nAUTH HEADER for apply_kebele_pattern: {auth_header}\n\n")

    if not auth_header or not auth_header.startswith('Token '):
        print(f"\n\nINVALID AUTH HEADER for apply_kebele_pattern: {auth_header}\n\n")
        return Response({"error": "Authentication credentials were not provided."}, status=status.HTTP_401_UNAUTHORIZED)

    token_key = auth_header.split(' ')[1]

    try:
        # Get the tenant by schema name
        tenant = Client.objects.get(schema_name=schema_name)

        # Set the tenant for this request
        connection.set_tenant(tenant)

        try:
            # First try to find the token in the tenant's database
            print(f"\n\nLooking for token {token_key} in tenant {tenant.schema_name}\n\n")
            from rest_framework.authtoken.models import Token
            from django.contrib.auth import get_user_model
            User = get_user_model()

            token = Token.objects.get(key=token_key)
            user = token.user
            request.user = user
            print(f"\n\nAuthenticated user: {user.username} (ID: {user.id})\n\n")
        except Token.DoesNotExist:
            # If not found, try the public schema
            print(f"\n\nToken not found in tenant {tenant.schema_name}, trying public schema\n\n")
            connection.set_schema_to_public()
            try:
                token = Token.objects.get(key=token_key)
                print(f"\n\nFound token in public schema for user {token.user.username} (ID: {token.user.id})\n\n")
                # Now switch back to the tenant schema
                connection.set_tenant(tenant)
                # Try to find the user in the tenant's database
                try:
                    user = User.objects.get(email=token.user.email)
                    request.user = user
                    print(f"\n\nFound matching user in tenant {tenant.schema_name}: {user.username} (ID: {user.id})\n\n")
                except User.DoesNotExist:
                    # If the user doesn't exist in the tenant's database, use the public schema user
                    request.user = token.user
                    print(f"\n\nUsing public schema user: {token.user.username} (ID: {token.user.id})\n\n")
            except Token.DoesNotExist:
                print(f"\n\nToken not found in public schema\n\n")
                return Response({"error": "Invalid token."}, status=status.HTTP_401_UNAUTHORIZED)
            except Exception as e:
                print(f"\n\nError authenticating user: {str(e)}\n\n")
                return Response({"error": f"Error authenticating user: {str(e)}"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        # Now we have an authenticated user and the correct tenant context

        # Get the ID card
        try:
            id_card = IDCard.objects.get(id=id)
        except IDCard.DoesNotExist:
            return Response({"error": "ID card not found."}, status=status.HTTP_404_NOT_FOUND)

        # Only check document verification if documents were explicitly rejected
        if id_card.document_verification_status == 'REJECTED':
            return Response(
                {'error': 'Cannot apply kebele pattern to ID card with rejected documents'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Check if the kebele pattern has already been applied
        if id_card.kebele_pattern:
            return Response(
                {'error': 'Kebele pattern has already been applied to this ID card'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Apply the kebele pattern
        from idcards.security_patterns import apply_kebele_pattern
        apply_kebele_pattern(id_card)
        id_card.refresh_from_db()

        serializer = IDCardSerializer(id_card)
        return Response(serializer.data)

    except Client.DoesNotExist:
        return Response({"error": f"Tenant with schema name '{schema_name}' not found."}, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['POST'])
@authentication_classes([TokenAuthentication])
@permission_classes([AllowAny])  # We'll handle authentication manually
def tenant_idcard_send_to_subcity(request, schema_name, id):
    """
    Send an approved ID card to the parent subcity for further processing.
    """
    # Manual authentication
    auth_header = request.headers.get('Authorization', '')
    print(f"\n\nAUTH HEADER for send_to_subcity: {auth_header}\n\n")

    if not auth_header or not auth_header.startswith('Token '):
        print(f"\n\nINVALID AUTH HEADER for send_to_subcity: {auth_header}\n\n")
        return Response({"error": "Authentication credentials were not provided."}, status=status.HTTP_401_UNAUTHORIZED)

    token_key = auth_header.split(' ')[1]

    try:
        # Get the tenant by schema name
        tenant = Client.objects.get(schema_name=schema_name)

        # Set the tenant for this request
        connection.set_tenant(tenant)

        try:
            # First try to find the token in the tenant's database
            print(f"\n\nLooking for token {token_key} in tenant {tenant.schema_name}\n\n")
            from rest_framework.authtoken.models import Token
            from django.contrib.auth import get_user_model
            User = get_user_model()

            token = Token.objects.get(key=token_key)
            user = token.user
            request.user = user
            print(f"\n\nAuthenticated user: {user.username} (ID: {user.id})\n\n")
        except Token.DoesNotExist:
            # If not found, try the public schema
            print(f"\n\nToken not found in tenant {tenant.schema_name}, trying public schema\n\n")
            connection.set_schema_to_public()
            try:
                token = Token.objects.get(key=token_key)
                print(f"\n\nFound token in public schema for user {token.user.username} (ID: {token.user.id})\n\n")
                # Now switch back to the tenant schema
                connection.set_tenant(tenant)
                # Try to find the user in the tenant's database
                try:
                    user = User.objects.get(email=token.user.email)
                    request.user = user
                    print(f"\n\nFound matching user in tenant {tenant.schema_name}: {user.username} (ID: {user.id})\n\n")
                except User.DoesNotExist:
                    # If the user doesn't exist in the tenant's database, use the public schema user
                    request.user = token.user
                    print(f"\n\nUsing public schema user: {token.user.username} (ID: {token.user.id})\n\n")
            except Token.DoesNotExist:
                print(f"\n\nToken not found in public schema\n\n")
                return Response({"error": "Invalid token."}, status=status.HTTP_401_UNAUTHORIZED)
            except Exception as e:
                print(f"\n\nError authenticating user: {str(e)}\n\n")
                return Response({"error": f"Error authenticating user: {str(e)}"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        # Now we have an authenticated user and the correct tenant context

        # Get the ID card
        try:
            id_card = IDCard.objects.get(id=id)
        except IDCard.DoesNotExist:
            return Response({"error": "ID card not found."}, status=status.HTTP_404_NOT_FOUND)

        # Check if the ID card is in the correct status
        if id_card.kebele_approval_status != 'APPROVED':
            return Response(
                {'error': 'ID card must be approved by kebele leader before sending to subcity'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Check if kebele pattern has been applied
        if not id_card.kebele_pattern:
            return Response(
                {'error': 'Kebele security pattern must be applied before sending to subcity'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Update the ID card status to indicate it's ready for subcity processing
        id_card.status = 'PENDING_SUBCITY'
        id_card.save()

        # Get any notes from the request
        notes = request.data.get('notes', '')

        # TODO: In a real implementation, we would notify the subcity here
        # This could be done via email, push notification, or by creating a task in a queue

        serializer = IDCardSerializer(id_card)
        return Response(serializer.data)

    except Client.DoesNotExist:
        return Response({"error": f"Tenant with schema name '{schema_name}' not found."}, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['POST'])
@authentication_classes([TokenAuthentication])
@permission_classes([AllowAny])  # We'll handle authentication manually
def tenant_idcard_apply_subcity_pattern(request, schema_name, id):
    """
    Apply the subcity half of the security pattern to an ID card.
    """
    # Manual authentication
    auth_header = request.headers.get('Authorization', '')
    print(f"\n\nAUTH HEADER for apply_subcity_pattern: {auth_header}\n\n")

    if not auth_header or not auth_header.startswith('Token '):
        print(f"\n\nINVALID AUTH HEADER for apply_subcity_pattern: {auth_header}\n\n")
        return Response({"error": "Authentication credentials were not provided."}, status=status.HTTP_401_UNAUTHORIZED)

    token_key = auth_header.split(' ')[1]

    try:
        # Get the tenant by schema name
        tenant = Client.objects.get(schema_name=schema_name)

        # Set the tenant for this request
        connection.set_tenant(tenant)

        try:
            # First try to find the token in the tenant's database
            print(f"\n\nLooking for token {token_key} in tenant {tenant.schema_name}\n\n")
            from rest_framework.authtoken.models import Token
            from django.contrib.auth import get_user_model
            User = get_user_model()

            token = Token.objects.get(key=token_key)
            user = token.user
            request.user = user
            print(f"\n\nAuthenticated user: {user.username} (ID: {user.id})\n\n")
        except Token.DoesNotExist:
            # If not found, try the public schema
            print(f"\n\nToken not found in tenant {tenant.schema_name}, trying public schema\n\n")
            connection.set_schema_to_public()
            try:
                token = Token.objects.get(key=token_key)
                print(f"\n\nFound token in public schema for user {token.user.username} (ID: {token.user.id})\n\n")
                # Now switch back to the tenant schema
                connection.set_tenant(tenant)
                # Try to find the user in the tenant's database
                try:
                    user = User.objects.get(email=token.user.email)
                    request.user = user
                    print(f"\n\nFound matching user in tenant {tenant.schema_name}: {user.username} (ID: {user.id})\n\n")
                except User.DoesNotExist:
                    # If the user doesn't exist in the tenant's database, use the public schema user
                    request.user = token.user
                    print(f"\n\nUsing public schema user: {token.user.username} (ID: {token.user.id})\n\n")
            except Token.DoesNotExist:
                print(f"\n\nToken not found in public schema\n\n")
                return Response({"error": "Invalid token."}, status=status.HTTP_401_UNAUTHORIZED)
            except Exception as e:
                print(f"\n\nError authenticating user: {str(e)}\n\n")
                return Response({"error": f"Error authenticating user: {str(e)}"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        # Now we have an authenticated user and the correct tenant context

        # Get the ID card
        try:
            id_card = IDCard.objects.get(id=id)
        except IDCard.DoesNotExist:
            return Response({"error": "ID card not found."}, status=status.HTTP_404_NOT_FOUND)

        # Check if the kebele pattern has been applied
        if not id_card.kebele_pattern:
            return Response(
                {'error': 'Kebele pattern must be applied before subcity pattern'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Check if the subcity pattern has already been applied
        if id_card.subcity_pattern:
            return Response(
                {'error': 'Subcity pattern has already been applied to this ID card'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Apply the subcity pattern
        from idcards.security_patterns import apply_subcity_pattern
        apply_subcity_pattern(id_card)
        id_card.refresh_from_db()

        serializer = IDCardSerializer(id_card)
        return Response(serializer.data)

    except Client.DoesNotExist:
        return Response({"error": f"Tenant with schema name '{schema_name}' not found."}, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['POST'])
@authentication_classes([TokenAuthentication])
@permission_classes([AllowAny])  # We'll handle authentication manually
def tenant_idcard_subcity_approve(request, schema_name, id):
    """
    Approve an ID card by the subcity admin that was sent from a kebele.
    """
    # Manual authentication
    auth_header = request.headers.get('Authorization', '')
    print(f"\n\nAUTH HEADER for subcity_approve: {auth_header}\n\n")

    if not auth_header or not auth_header.startswith('Token '):
        print(f"\n\nINVALID AUTH HEADER for subcity_approve: {auth_header}\n\n")
        return Response({"error": "Authentication credentials were not provided."}, status=status.HTTP_401_UNAUTHORIZED)

    token_key = auth_header.split(' ')[1]

    try:
        # Get the tenant by schema name
        tenant = Client.objects.get(schema_name=schema_name)

        # Set the tenant for this request
        connection.set_tenant(tenant)

        try:
            # First try to find the token in the tenant's database
            print(f"\n\nLooking for token {token_key} in tenant {tenant.schema_name}\n\n")
            from rest_framework.authtoken.models import Token
            from django.contrib.auth import get_user_model
            User = get_user_model()

            token = Token.objects.get(key=token_key)
            user = token.user
            request.user = user
            print(f"\n\nAuthenticated user: {user.username} (ID: {user.id})\n\n")
        except Token.DoesNotExist:
            # If not found, try the public schema
            print(f"\n\nToken not found in tenant {tenant.schema_name}, trying public schema\n\n")
            connection.set_schema_to_public()
            try:
                token = Token.objects.get(key=token_key)
                print(f"\n\nFound token in public schema for user {token.user.username} (ID: {token.user.id})\n\n")
                # Now switch back to the tenant schema
                connection.set_tenant(tenant)
                # Try to find the user in the tenant's database
                try:
                    user = User.objects.get(email=token.user.email)
                    request.user = user
                    print(f"\n\nFound matching user in tenant {tenant.schema_name}: {user.username} (ID: {user.id})\n\n")
                except User.DoesNotExist:
                    # If the user doesn't exist in the tenant's database, use the public schema user
                    request.user = token.user
                    print(f"\n\nUsing public schema user: {token.user.username} (ID: {token.user.id})\n\n")
            except Token.DoesNotExist:
                print(f"\n\nToken not found in public schema\n\n")
                return Response({"error": "Invalid token."}, status=status.HTTP_401_UNAUTHORIZED)
            except Exception as e:
                print(f"\n\nError authenticating user: {str(e)}\n\n")
                return Response({"error": f"Error authenticating user: {str(e)}"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        # Now we have an authenticated user and the correct tenant context

        # Get the ID card
        try:
            id_card = IDCard.objects.get(id=id)
        except IDCard.DoesNotExist:
            return Response({"error": "ID card not found."}, status=status.HTTP_404_NOT_FOUND)

        # Check if the ID card is in the correct status
        if id_card.status != 'PENDING_SUBCITY':
            return Response(
                {'error': 'Only ID cards with PENDING_SUBCITY status can be approved by subcity admin'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Check if kebele leader has approved
        if id_card.kebele_approval_status != 'APPROVED':
            return Response(
                {'error': 'ID card must be approved by kebele leader before subcity approval'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Check if kebele pattern has been applied
        if not id_card.kebele_pattern:
            return Response(
                {'error': 'Kebele security pattern must be applied before subcity approval'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Get approval notes from request data
        notes = request.data.get('notes', '')

        # Apply the subcity pattern automatically if it hasn't been applied yet
        if not id_card.subcity_pattern:
            try:
                # Import the pattern application function
                from idcards.security_patterns import apply_subcity_pattern

                # Apply the pattern
                print(f"\n\nAutomatically applying subcity pattern during approval\n\n")
                apply_subcity_pattern(id_card)

                # Refresh the ID card to get the updated pattern
                id_card.refresh_from_db()
                print(f"\n\nSubcity pattern applied successfully\n\n")
            except Exception as e:
                print(f"\n\nError applying subcity pattern: {str(e)}\n\n")
                # Continue with approval even if pattern application fails
                pass

        # Update the ID card status to APPROVED
        id_card.status = 'APPROVED'
        id_card.approved_by = request.user
        id_card.save()

        serializer = IDCardSerializer(id_card)
        return Response(serializer.data)

    except Client.DoesNotExist:
        return Response({"error": f"Tenant with schema name '{schema_name}' not found."}, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
