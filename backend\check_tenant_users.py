import os
import django

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'neocamelot.settings')
django.setup()

# Import models
from centers.models import Client
from django.contrib.auth import get_user_model
from django_tenants.utils import tenant_context, schema_context

User = get_user_model()

def check_tenant_users(schema_name):
    """Check users in a specific tenant schema."""
    try:
        tenant = Client.objects.get(schema_name=schema_name)
        print(f"\nChecking users in tenant: {tenant.name} (Schema: {tenant.schema_name})")

        # Check users in tenant schema
        with tenant_context(tenant):
            users = User.objects.all()
            print(f"Users in {tenant.schema_name} schema: {users.count()}")
            for user in users:
                print(f"- {user.email} (Role: {user.role})")

        # Check users in public schema with the same email
        with schema_context('public'):
            for tenant_user in users:
                public_users = User.objects.filter(email=tenant_user.email)
                if public_users.exists():
                    print(f"User {tenant_user.email} also exists in public schema")
                    for user in public_users:
                        print(f"  - {user.email} (Role: {user.role})")
    except Client.DoesNotExist:
        print(f"Tenant with schema name {schema_name} does not exist")
    except Exception as e:
        print(f"Error: {str(e)}")

if __name__ == "__main__":
    # Check all tenants
    tenants = Client.objects.exclude(schema_name='public').order_by('id')
    for tenant in tenants:
        check_tenant_users(tenant.schema_name)
