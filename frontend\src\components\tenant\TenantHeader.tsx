import React, { useState } from 'react';
import {
  A<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>po<PERSON>,
  Button,
  IconButton,
  Box,
  Menu,
  MenuItem,
  Container,
  useMediaQuery,
  Drawer,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Divider,
  Avatar,
  Tooltip
} from '@mui/material';
import { useTheme } from '@mui/material/styles';
import { useNavigate } from 'react-router-dom';
import { hasRoutePermission } from '../../utils/rolePermissions';
import MenuIcon from '@mui/icons-material/Menu';
import DashboardIcon from '@mui/icons-material/Dashboard';
import PeopleIcon from '@mui/icons-material/People';
import CardMembershipIcon from '@mui/icons-material/CardMembership';
import SettingsIcon from '@mui/icons-material/Settings';
import AssessmentIcon from '@mui/icons-material/Assessment';
import PersonAddIcon from '@mui/icons-material/PersonAdd';
import PrintIcon from '@mui/icons-material/Print';
import SearchIcon from '@mui/icons-material/Search';
import AssignmentTurnedInIcon from '@mui/icons-material/AssignmentTurnedIn';
import SupervisorAccountIcon from '@mui/icons-material/SupervisorAccount';
import LogoutIcon from '@mui/icons-material/Logout';
import AccountCircleIcon from '@mui/icons-material/AccountCircle';
import NotificationsIcon from '@mui/icons-material/Notifications';
import HelpIcon from '@mui/icons-material/Help';
import TenantContextIndicator from './TenantContextIndicator';

interface User {
  id: number;
  email: string;
  first_name: string;
  last_name: string;
  role: string;
}

interface Tenant {
  schema_name: string;
  name: string;
  type?: string;
  schema_type: string;
  logo_url?: string;
  header_color?: string;
  accent_color?: string;
}

interface TenantHeaderProps {
  user: User | null;
  tenant: Tenant | null;
}

const TenantHeader: React.FC<TenantHeaderProps> = ({ user, tenant }) => {
  const theme = useTheme();
  const navigate = useNavigate();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const [mobileOpen, setMobileOpen] = useState(false);
  const [userMenuAnchorEl, setUserMenuAnchorEl] = useState<null | HTMLElement>(null);
  const [notificationsAnchorEl, setNotificationsAnchorEl] = useState<null | HTMLElement>(null);

  const handleDrawerToggle = () => {
    setMobileOpen(!mobileOpen);
  };

  const handleUserMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setUserMenuAnchorEl(event.currentTarget);
  };

  const handleUserMenuClose = () => {
    setUserMenuAnchorEl(null);
  };

  const handleNotificationsOpen = (event: React.MouseEvent<HTMLElement>) => {
    setNotificationsAnchorEl(event.currentTarget);
  };

  const handleNotificationsClose = () => {
    setNotificationsAnchorEl(null);
  };

  const handleLogout = () => {
    console.log('Logging out...');

    // Clear all localStorage items
    localStorage.removeItem('jwt_access_token');
    localStorage.removeItem('jwt_refresh_token');
    localStorage.removeItem('jwt_schema');
    localStorage.removeItem('user');
    localStorage.removeItem('tenant');
    localStorage.removeItem('schema_name');
    localStorage.removeItem('tokenStore');
    localStorage.removeItem('permissions_initialized');
    localStorage.removeItem('tenant_indicator_initialized');
    localStorage.removeItem('tenant_layout_initialized');
    localStorage.removeItem('last_theme_path');
    localStorage.removeItem('token_expired');

    // Also clear sessionStorage for completeness
    sessionStorage.clear();

    // Clear cookies
    document.cookie = 'refresh_token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT';
    document.cookie = 'schema_name=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT';

    // Redirect to login
    navigate('/login');
  };

  // Navigation items based on tenant type, user role, and permissions
  const getNavigationItems = () => {
    // Define all possible navigation items
    const allItems = [
      { text: 'Dashboard', path: '/dashboard', icon: <DashboardIcon /> },
      { text: 'Citizens', path: '/citizens', icon: <PeopleIcon /> },
      { text: 'ID Cards', path: '/id-cards', icon: <CardMembershipIcon /> },
      { text: 'Print ID Cards', path: '/print-cards', icon: <PrintIcon /> },
      { text: 'Document Verification', path: '/document-verification', icon: <AssignmentTurnedInIcon /> },
      { text: 'Kebele Users', path: '/kebele-users', icon: <SupervisorAccountIcon /> },
      { text: 'Subcity Users', path: '/subcity-users', icon: <SupervisorAccountIcon /> },
      { text: 'Reports', path: '/reports', icon: <AssessmentIcon /> },
      { text: 'Settings', path: '/settings', icon: <SettingsIcon /> },
    ];

    // Get user role from localStorage if available (set by find-user-schema endpoint)
    const storedRole = localStorage.getItem('user_role');

    // Get tenant type from localStorage if available
    const storedTenantType = localStorage.getItem('tenant_type');

    console.log('Determining navigation items for:');
    console.log('- User role from props:', user?.role);
    console.log('- User role from localStorage:', storedRole);
    console.log('- Tenant type from props:', tenant?.schema_type || tenant?.type);
    console.log('- Tenant type from localStorage:', storedTenantType);

    // Determine the effective role and tenant type
    // Prioritize localStorage values over props
    const effectiveRole = storedRole || user?.role || 'CENTER_STAFF';
    const effectiveTenantType = storedTenantType || tenant?.schema_type || tenant?.type || 'KEBELE';

    console.log('Using effective role:', effectiveRole);
    console.log('Using effective tenant type:', effectiveTenantType);

    // Define a limited set of navigation items based on tenant type and role
    let filteredItems = [];

    // Dashboard is available to all users
    filteredItems.push({ text: 'Dashboard', path: '/dashboard', icon: <DashboardIcon /> });

    // Citizens management is available to all users except city admins
    if (effectiveTenantType !== 'CITY' || effectiveRole === 'ADMIN' ||
        effectiveRole === 'CENTER_STAFF' || effectiveRole === 'CENTER_ADMIN') {
      filteredItems.push({ text: 'Citizens', path: '/citizens', icon: <PeopleIcon /> });
      filteredItems.push({ text: 'Citizens (New)', path: '/citizens-new', icon: <PeopleIcon /> });
    }

    // ID Cards management is available to all users
    filteredItems.push({ text: 'ID Cards', path: '/id-cards', icon: <CardMembershipIcon /> });

    // Print ID Cards is only available to subcity users
    if (effectiveTenantType === 'SUBCITY' || effectiveRole === 'SUBCITY_ADMIN' || effectiveRole === 'ADMIN') {
      filteredItems.push({ text: 'Print ID Cards', path: '/print-cards', icon: <PrintIcon /> });
    }

    // Document Verification is available only to kebele leaders
    if (effectiveTenantType === 'KEBELE' && (effectiveRole === 'KEBELE_LEADER' || effectiveRole === 'ADMIN')) {
      filteredItems.push({ text: 'Document Verification', path: '/document-verification', icon: <AssignmentTurnedInIcon /> });
    }

    // Kebele Users management is only available to subcity admins
    if (effectiveTenantType === 'SUBCITY' || effectiveRole === 'SUBCITY_ADMIN' || effectiveRole === 'ADMIN') {
      filteredItems.push({ text: 'Kebele Users', path: '/kebele-users', icon: <SupervisorAccountIcon /> });
    }

    // Subcity Users management is only available to city admins
    if (effectiveTenantType === 'CITY' || effectiveRole === 'CITY_ADMIN' || effectiveRole === 'ADMIN') {
      filteredItems.push({ text: 'Subcity Users', path: '/subcity-users', icon: <SupervisorAccountIcon /> });
    }

    // Reports are available to admins and leaders only
    if (effectiveRole === 'CITY_ADMIN' || effectiveRole === 'SUBCITY_ADMIN' ||
        effectiveRole === 'KEBELE_LEADER' || effectiveRole === 'ADMIN') {
      filteredItems.push({ text: 'Reports', path: '/reports', icon: <AssessmentIcon /> });
    }

    // Settings are available to admin users
    if (effectiveRole === 'ADMIN' || effectiveRole === 'CITY_ADMIN' || effectiveRole === 'SUBCITY_ADMIN') {
      filteredItems.push({ text: 'Settings', path: '/settings', icon: <SettingsIcon /> });
    }

    // Sort items in a logical order
    const orderedPaths = [
      '/dashboard',
      '/citizens',
      '/id-cards',
      '/print-cards',
      '/document-verification',
      '/kebele-users',
      '/subcity-users',
      '/reports',
      '/settings',
    ];

    filteredItems.sort((a, b) => {
      return orderedPaths.indexOf(a.path) - orderedPaths.indexOf(b.path);
    });

    console.log('Filtered navigation items:', filteredItems.map(item => item.text).join(', '));
    return filteredItems;
  };

  const navigationItems = getNavigationItems();

  const drawer = (
    <Box sx={{ textAlign: 'center', height: '100%', display: 'flex', flexDirection: 'column' }}>
      <Box
        sx={{
          p: 3,
          bgcolor: tenant?.header_color || theme.palette.primary.main,
          color: 'white',
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center'
        }}
      >
        {tenant?.logo_url ? (
          <Box
            sx={{
              width: 80,
              height: 80,
              bgcolor: 'rgba(255, 255, 255, 0.2)',
              borderRadius: 2,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              mb: 2,
              overflow: 'hidden'
            }}
          >
            <img
              src={tenant.logo_url}
              alt="Tenant Logo"
              style={{ maxWidth: '100%', maxHeight: '100%', objectFit: 'contain' }}
              onError={(e) => {
                console.error('Error loading logo:', e);
                // Fallback to initial if image fails to load
                const target = e.target as HTMLImageElement;
                target.style.display = 'none';
                // Show fallback initial
                const parent = target.parentElement;
                if (parent) {
                  parent.innerHTML = `<div style="width: 100%; height: 100%; display: flex; align-items: center; justify-content: center; font-weight: bold; font-size: 2.2rem; color: white;">${tenant?.name?.charAt(0) || 'N'}</div>`;
                }
              }}
            />
          </Box>
        ) : (
          <Box
            sx={{
              width: 80,
              height: 80,
              bgcolor: 'rgba(255, 255, 255, 0.2)',
              color: 'white',
              borderRadius: 2,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              mb: 2,
              fontWeight: 'bold',
              fontSize: '2.2rem'
            }}
          >
            {tenant?.name?.charAt(0) || 'N'}
          </Box>
        )}
        <Typography variant="h6" sx={{ fontWeight: 500 }}>
          {tenant?.name || 'NeoCamelot'}
        </Typography>
        <Typography variant="body2" sx={{ opacity: 0.8, mt: 0.5 }}>
          {tenant?.schema_type || tenant?.type || 'ID Card System'}
        </Typography>
      </Box>
      <Divider />
      <Box sx={{ flexGrow: 1, overflow: 'auto' }}>
        <List sx={{ py: 2 }}>
          {navigationItems.map((item) => (
            <ListItem
              key={item.text}
              disablePadding
              onClick={() => {
                // For all pages, just navigate directly
                // We'll handle token validation in the page component
                navigate(item.path);
                handleDrawerToggle();
              }}
            >
              <Box
                component="a"
                sx={{
                  display: 'flex',
                  width: '100%',
                  py: 1.5,
                  px: 3,
                  alignItems: 'center',
                  textDecoration: 'none',
                  color: theme.palette.text.primary,
                  '&:hover': {
                    bgcolor: 'rgba(0, 0, 0, 0.04)',
                  }
                }}
              >
                <Box
                  sx={{
                    mr: 3,
                    color: theme.palette.primary.main,
                    display: 'flex'
                  }}
                >
                  {item.icon}
                </Box>
                <Typography variant="body1">{item.text}</Typography>
              </Box>
            </ListItem>
          ))}
        </List>
      </Box>
      <Divider />
      <Box sx={{ p: 2 }}>
        <Button
          variant="outlined"
          color="error"
          startIcon={<LogoutIcon />}
          onClick={handleLogout}
          fullWidth
          sx={{ py: 1 }}
        >
          Logout
        </Button>
      </Box>
    </Box>
  );

  return (
    <>
      <AppBar
        position="static"
        elevation={0}
        sx={{
          bgcolor: 'white',
          color: theme.palette.text.primary,
          borderBottom: '1px solid rgba(0, 0, 0, 0.08)',
          boxShadow: '0 2px 10px rgba(0, 0, 0, 0.05)',
          '& .MuiToolbar-root': {
            borderTop: `3px solid ${tenant?.accent_color || theme.palette.primary.main}`
          }
        }}
      >
        <Container maxWidth="xl">
          <Toolbar disableGutters sx={{ py: 1 }}>
            {isMobile ? (
              <>
                <IconButton
                  color="inherit"
                  aria-label="open drawer"
                  edge="start"
                  onClick={handleDrawerToggle}
                  sx={{ mr: 2 }}
                >
                  <MenuIcon />
                </IconButton>
                <Box
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    flexGrow: 1
                  }}
                >
                  {tenant?.logo_url ? (
                    <Box
                      sx={{
                        width: 48,
                        height: 48,
                        bgcolor: 'white',
                        borderRadius: 1,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        mr: 1.5,
                        overflow: 'hidden'
                      }}
                    >
                      <img
                        src={tenant.logo_url}
                        alt="Tenant Logo"
                        style={{ maxWidth: '100%', maxHeight: '100%', objectFit: 'contain' }}
                        onError={(e) => {
                          console.error('Error loading logo:', e);
                          // Fallback to initial if image fails to load
                          const target = e.target as HTMLImageElement;
                          target.style.display = 'none';
                          // Show fallback initial
                          const parent = target.parentElement;
                          if (parent) {
                            parent.innerHTML = `<div style="width: 100%; height: 100%; display: flex; align-items: center; justify-content: center; font-weight: bold; font-size: 1.5rem; color: white;">${tenant?.name?.charAt(0) || 'N'}</div>`;
                          }
                        }}
                      />
                    </Box>
                  ) : (
                    <Box
                      sx={{
                        width: 48,
                        height: 48,
                        bgcolor: theme.palette.primary.main,
                        color: 'white',
                        borderRadius: 1,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        mr: 1.5,
                        fontWeight: 'bold',
                        fontSize: '1.5rem'
                      }}
                    >
                      {tenant?.name?.charAt(0) || 'N'}
                    </Box>
                  )}
                  <Box>
                    <Typography
                      variant="h6"
                      noWrap
                      component="div"
                      sx={{ fontWeight: 500 }}
                    >
                      {tenant?.name || 'NeoCamelot'}
                    </Typography>
                    {user && (
                      <Typography variant="caption" sx={{ display: 'block', mt: -0.5 }}>
                        {!user.role ? 'Clerk' : // Default role if undefined
                         user.role === 'KEBELE_LEADER' ? 'Kebele Leader (Liqe Menber)' :
                         user.role === 'CENTER_STAFF' ? 'Clerk' :
                         user.role === 'CENTER_ADMIN' ? 'Center Admin' :
                         user.role === 'SUBCITY_ADMIN' ? 'Subcity Admin' :
                         user.role === 'CITY_ADMIN' ? 'City Admin' :
                         user.role === 'ADMIN' ? 'Admin' :
                         typeof user.role === 'string' && user.role.toUpperCase() === 'KEBELE_LEADER' ? 'Kebele Leader (Liqe Menber)' :
                         typeof user.role === 'string' && user.role.toUpperCase() === 'CENTER_STAFF' ? 'Clerk' :
                         typeof user.role === 'string' && user.role.toUpperCase() === 'CENTER_ADMIN' ? 'Center Admin' :
                         typeof user.role === 'string' && user.role.toUpperCase() === 'SUBCITY_ADMIN' ? 'Subcity Admin' :
                         typeof user.role === 'string' && user.role.toUpperCase() === 'CITY_ADMIN' ? 'City Admin' :
                         typeof user.role === 'string' && user.role.toUpperCase() === 'ADMIN' ? 'Admin' :
                         user.role || 'Clerk'}
                      </Typography>
                    )}
                  </Box>
                </Box>
              </>
            ) : (
              <>
                <Box
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    mr: 4
                  }}
                >
                  {tenant?.logo_url ? (
                    <Box
                      sx={{
                        width: 48,
                        height: 48,
                        bgcolor: 'white',
                        borderRadius: 1,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        mr: 1.5,
                        overflow: 'hidden'
                      }}
                    >
                      <img
                        src={tenant.logo_url}
                        alt="Tenant Logo"
                        style={{ maxWidth: '100%', maxHeight: '100%', objectFit: 'contain' }}
                        onError={(e) => {
                          console.error('Error loading logo:', e);
                          // Fallback to initial if image fails to load
                          const target = e.target as HTMLImageElement;
                          target.style.display = 'none';
                          // Show fallback initial
                          const parent = target.parentElement;
                          if (parent) {
                            parent.innerHTML = `<div style="width: 100%; height: 100%; display: flex; align-items: center; justify-content: center; font-weight: bold; font-size: 1.5rem; color: white;">${tenant?.name?.charAt(0) || 'N'}</div>`;
                          }
                        }}
                      />
                    </Box>
                  ) : (
                    <Box
                      sx={{
                        width: 48,
                        height: 48,
                        bgcolor: theme.palette.primary.main,
                        color: 'white',
                        borderRadius: 1,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        mr: 1.5,
                        fontWeight: 'bold',
                        fontSize: '1.5rem'
                      }}
                    >
                      {tenant?.name?.charAt(0) || 'N'}
                    </Box>
                  )}
                  <Box>
                    <Typography
                      variant="h6"
                      noWrap
                      component="div"
                      sx={{ fontWeight: 500 }}
                    >
                      {tenant?.name || 'NeoCamelot'}
                    </Typography>
                    {user && (
                      <Typography variant="caption" sx={{ display: 'block', mt: -0.5 }}>
                        {!user.role ? 'Clerk' : // Default role if undefined
                         user.role === 'KEBELE_LEADER' ? 'Kebele Leader (Liqe Menber)' :
                         user.role === 'CENTER_STAFF' ? 'Clerk' :
                         user.role === 'CENTER_ADMIN' ? 'Center Admin' :
                         user.role === 'SUBCITY_ADMIN' ? 'Subcity Admin' :
                         user.role === 'CITY_ADMIN' ? 'City Admin' :
                         user.role === 'ADMIN' ? 'Admin' :
                         typeof user.role === 'string' && user.role.toUpperCase() === 'KEBELE_LEADER' ? 'Kebele Leader (Liqe Menber)' :
                         typeof user.role === 'string' && user.role.toUpperCase() === 'CENTER_STAFF' ? 'Clerk' :
                         typeof user.role === 'string' && user.role.toUpperCase() === 'CENTER_ADMIN' ? 'Center Admin' :
                         typeof user.role === 'string' && user.role.toUpperCase() === 'SUBCITY_ADMIN' ? 'Subcity Admin' :
                         typeof user.role === 'string' && user.role.toUpperCase() === 'CITY_ADMIN' ? 'City Admin' :
                         typeof user.role === 'string' && user.role.toUpperCase() === 'ADMIN' ? 'Admin' :
                         user.role || 'Clerk'}
                      </Typography>
                    )}
                  </Box>
                </Box>
                <Box sx={{ flexGrow: 1, display: 'flex' }}>
                  {navigationItems.map((item) => (
                    <Button
                      key={item.text}
                      onClick={() => {
                        // For all pages, just navigate directly
                        // We'll handle token validation in the page component
                        navigate(item.path);
                      }}
                      sx={{
                        color: theme.palette.text.primary,
                        mx: 0.5,
                        px: 2,
                        py: 1,
                        borderRadius: 1,
                        '&:hover': {
                          bgcolor: 'rgba(0, 0, 0, 0.04)',
                        },
                        '&.active': {
                          bgcolor: `${theme.palette.primary.main}10`,
                          color: theme.palette.primary.main,
                        }
                      }}
                      startIcon={item.icon}
                    >
                      {item.text}
                    </Button>
                  ))}
                </Box>
              </>
            )}

            {/* Tenant Context Indicator */}
            <Box sx={{ mr: 2 }}>
              <TenantContextIndicator />
            </Box>

            {/* Notifications */}
            <Tooltip title="Notifications">
              <IconButton
                onClick={handleNotificationsOpen}
                sx={{
                  ml: 1,
                  bgcolor: 'rgba(0, 0, 0, 0.04)',
                  '&:hover': {
                    bgcolor: 'rgba(0, 0, 0, 0.08)',
                  }
                }}
              >
                <NotificationsIcon sx={{ color: theme.palette.text.secondary }} />
              </IconButton>
            </Tooltip>
            <Menu
              anchorEl={notificationsAnchorEl}
              open={Boolean(notificationsAnchorEl)}
              onClose={handleNotificationsClose}
              PaperProps={{
                elevation: 2,
                sx: {
                  width: 320,
                  maxHeight: 400,
                  mt: 1.5,
                  borderRadius: 2,
                  overflow: 'hidden'
                }
              }}
            >
              <Box sx={{ p: 2, borderBottom: '1px solid rgba(0, 0, 0, 0.08)' }}>
                <Typography variant="subtitle1" fontWeight="500">Notifications</Typography>
              </Box>
              <MenuItem sx={{ py: 2 }}>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <Box
                    sx={{
                      mr: 2,
                      bgcolor: 'rgba(0, 0, 0, 0.04)',
                      borderRadius: '50%',
                      width: 40,
                      height: 40,
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center'
                    }}
                  >
                    <NotificationsIcon color="disabled" />
                  </Box>
                  <Box>
                    <Typography variant="body1">No new notifications</Typography>
                    <Typography variant="caption" color="text.secondary">
                      You're all caught up!
                    </Typography>
                  </Box>
                </Box>
              </MenuItem>
            </Menu>

            {/* Help */}
            <Tooltip title="Help">
              <IconButton
                onClick={() => navigate('/help')}
                sx={{
                  ml: 1,
                  bgcolor: 'rgba(0, 0, 0, 0.04)',
                  '&:hover': {
                    bgcolor: 'rgba(0, 0, 0, 0.08)',
                  }
                }}
              >
                <HelpIcon sx={{ color: theme.palette.text.secondary }} />
              </IconButton>
            </Tooltip>

            {/* User Menu */}
            <Tooltip title="Account">
              <IconButton
                onClick={handleUserMenuOpen}
                sx={{
                  ml: 1,
                  border: '2px solid rgba(0, 0, 0, 0.08)',
                  p: 0.5
                }}
              >
                <Avatar
                  sx={{
                    width: 32,
                    height: 32,
                    bgcolor: theme.palette.primary.main,
                    fontWeight: 'bold'
                  }}
                >
                  {user?.first_name?.charAt(0) || 'U'}
                </Avatar>
              </IconButton>
            </Tooltip>
            <Menu
              anchorEl={userMenuAnchorEl}
              open={Boolean(userMenuAnchorEl)}
              onClose={handleUserMenuClose}
              PaperProps={{
                elevation: 2,
                sx: {
                  mt: 1.5,
                  borderRadius: 2,
                  minWidth: 220,
                  overflow: 'hidden'
                }
              }}
            >
              <Box sx={{ px: 3, py: 2, bgcolor: theme.palette.primary.main, color: 'white' }}>
                <Typography variant="subtitle1" fontWeight="500">
                  {user ? `${user.first_name} ${user.last_name}` : 'User'}
                </Typography>
                <Typography variant="body2" sx={{ opacity: 0.8 }}>
                  {!user?.role ? 'Clerk' : // Default role if undefined
                   user?.role === 'KEBELE_LEADER' ? 'Kebele Leader (Liqe Menber)' :
                   user?.role === 'CENTER_STAFF' ? 'Clerk' :
                   user?.role === 'CENTER_ADMIN' ? 'Center Admin' :
                   user?.role === 'SUBCITY_ADMIN' ? 'Subcity Admin' :
                   user?.role === 'CITY_ADMIN' ? 'City Admin' :
                   user?.role === 'ADMIN' ? 'Admin' :
                   typeof user?.role === 'string' && user?.role.toUpperCase() === 'KEBELE_LEADER' ? 'Kebele Leader (Liqe Menber)' :
                   typeof user?.role === 'string' && user?.role.toUpperCase() === 'CENTER_STAFF' ? 'Clerk' :
                   typeof user?.role === 'string' && user?.role.toUpperCase() === 'CENTER_ADMIN' ? 'Center Admin' :
                   typeof user?.role === 'string' && user?.role.toUpperCase() === 'SUBCITY_ADMIN' ? 'Subcity Admin' :
                   typeof user?.role === 'string' && user?.role.toUpperCase() === 'CITY_ADMIN' ? 'City Admin' :
                   typeof user?.role === 'string' && user?.role.toUpperCase() === 'ADMIN' ? 'Admin' :
                   user?.role || 'Clerk'}
                </Typography>
              </Box>
              <Box sx={{ mt: 1 }}>
                <MenuItem onClick={() => navigate('/profile')} sx={{ py: 1.5, px: 2 }}>
                  <ListItemIcon>
                    <AccountCircleIcon fontSize="small" sx={{ color: theme.palette.primary.main }} />
                  </ListItemIcon>
                  <ListItemText primary="Profile" />
                </MenuItem>
                <MenuItem onClick={() => navigate('/settings')} sx={{ py: 1.5, px: 2 }}>
                  <ListItemIcon>
                    <SettingsIcon fontSize="small" sx={{ color: theme.palette.primary.main }} />
                  </ListItemIcon>
                  <ListItemText primary="Settings" />
                </MenuItem>
                <Divider sx={{ my: 1 }} />
                <MenuItem onClick={handleLogout} sx={{ py: 1.5, px: 2 }}>
                  <ListItemIcon>
                    <LogoutIcon fontSize="small" sx={{ color: theme.palette.error.main }} />
                  </ListItemIcon>
                  <ListItemText primary="Logout" sx={{ color: theme.palette.error.main }} />
                </MenuItem>
              </Box>
            </Menu>
          </Toolbar>
        </Container>
      </AppBar>

      {/* Mobile Drawer */}
      <Drawer
        variant="temporary"
        open={mobileOpen}
        onClose={handleDrawerToggle}
        ModalProps={{
          keepMounted: true, // Better open performance on mobile
        }}
        sx={{
          display: { xs: 'block', md: 'none' },
          '& .MuiDrawer-paper': {
            boxSizing: 'border-box',
            width: 280,
            borderRadius: '0 16px 16px 0',
            boxShadow: '0 8px 24px rgba(0, 0, 0, 0.15)'
          },
        }}
      >
        {drawer}
      </Drawer>
    </>
  );
};

export default TenantHeader;
