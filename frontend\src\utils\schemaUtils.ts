/**
 * Utility functions for handling schema names
 */

/**
 * Formats a schema name for use in API URLs
 * Converts spaces to underscores for URL paths
 * @param schema The schema name to format
 * @returns The formatted schema name
 */
export const formatSchemaForUrl = (schema: string): string => {
  if (!schema) return '';
  
  // Convert spaces to underscores for URL paths
  return schema.replace(/\s+/g, '_');
};

/**
 * Formats a schema name for use in API headers
 * Preserves spaces as they are expected in the X-Schema-Name header
 * @param schema The schema name to format
 * @returns The formatted schema name
 */
export const formatSchemaForHeader = (schema: string): string => {
  if (!schema) return '';
  
  // For headers, we use the schema name as is (with spaces)
  // If the schema has underscores, convert them back to spaces
  return schema.replace(/_/g, ' ');
};

/**
 * Gets the current schema name from localStorage
 * @returns The current schema name or empty string
 */
export const getCurrentSchema = (): string => {
  return localStorage.getItem('schema_name') || '';
};

/**
 * Sets the current schema name in localStorage
 * @param schema The schema name to set
 */
export const setCurrentSchema = (schema: string): void => {
  if (schema) {
    localStorage.setItem('schema_name', schema);
    console.log(`Schema name set in localStorage: ${schema}`);
  }
};

/**
 * Extracts schema name from a URL path
 * @param url The URL to extract from
 * @returns The extracted schema name or empty string
 */
export const extractSchemaFromUrl = (url: string): string => {
  if (!url) return '';
  
  // Try to extract schema from tenant URL pattern
  if (url.includes('/tenant/')) {
    const match = url.match(/\/tenant\/([^\/]+)\//);
    if (match && match[1]) {
      // Convert underscores back to spaces for the schema name
      const schema = match[1].replace(/_/g, ' ');
      console.log(`Extracted schema from URL: ${schema}`);
      return schema;
    }
  }
  
  return '';
};

/**
 * Validates if a schema name is properly formatted
 * @param schema The schema name to validate
 * @returns True if valid, false otherwise
 */
export const isValidSchema = (schema: string): boolean => {
  if (!schema) return false;
  
  // Schema should not be 'public'
  if (schema.toLowerCase() === 'public') return false;
  
  // Schema should not contain special characters except spaces and underscores
  const validPattern = /^[a-zA-Z0-9\s_]+$/;
  return validPattern.test(schema);
};

export default {
  formatSchemaForUrl,
  formatSchemaForHeader,
  getCurrentSchema,
  setCurrentSchema,
  extractSchemaFromUrl,
  isValidSchema
};
