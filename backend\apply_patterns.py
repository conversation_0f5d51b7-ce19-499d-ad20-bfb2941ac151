import os
import django
import json
import random

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'neocamelot.settings')
django.setup()

from django.db import connection

# Function to generate a random pattern seed
def generate_pattern_seed():
    """Generate a random seed for security pattern generation."""
    return ''.join(random.choice('0123456789abcdef') for _ in range(16))

# Function to generate pattern halves
def generate_pattern_halves(seed):
    """
    Generate both halves of the security pattern from a seed.

    Args:
        seed: A string seed to use for random number generation

    Returns:
        Tuple[str, str]: The kebele and subcity pattern halves as JSON strings
    """
    # Use the seed to initialize the random number generator
    random.seed(seed)

    # Create simplified pattern data structure with both halves
    pattern_data = {
        # Kebele pattern parameters (first half)
        "kebele": {
            "color": f"rgba(0, 0, 150, 0.3)",
            "lines": [
                {
                    "x1": 0.1,
                    "y1": 0.1,
                    "x2": 0.4,
                    "y2": 0.9,
                    "width": 1,
                    "opacity": 0.2
                }
            ],
            "dots": [
                {
                    "x": 0.2,
                    "y": 0.5,
                    "radius": 1,
                    "opacity": 0.2
                }
            ]
        },

        # Subcity pattern parameters (second half)
        "subcity": {
            "color": f"rgba(150, 0, 0, 0.3)",
            "lines": [
                {
                    "x1": 0.6,
                    "y1": 0.1,
                    "x2": 0.9,
                    "y2": 0.9,
                    "width": 1,
                    "opacity": 0.2
                }
            ],
            "dots": [
                {
                    "x": 0.8,
                    "y": 0.5,
                    "radius": 1,
                    "opacity": 0.2
                }
            ]
        }
    }

    # Convert to JSON strings
    kebele_pattern = json.dumps(pattern_data["kebele"])
    subcity_pattern = json.dumps(pattern_data["subcity"])

    return kebele_pattern, subcity_pattern

def main():
    print("=== Applying Patterns to ID Cards ===")

    # Get all schemas in the database
    with connection.cursor() as cursor:
        cursor.execute("""
            SELECT schema_name
            FROM information_schema.schemata
            WHERE schema_name NOT IN ('information_schema', 'pg_catalog', 'pg_toast', 'pg_temp_1', 'pg_toast_temp_1')
            ORDER BY schema_name
        """)
        schemas = cursor.fetchall()

        print(f"Found {len(schemas)} schemas in the database")

        # Process each schema
        for schema in schemas:
            schema_name = schema[0]
            print(f"\nProcessing schema: {schema_name}")

            # Check if the idcards_idcard table exists in this schema
            cursor.execute(f"""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables
                    WHERE table_schema = '{schema_name}'
                    AND table_name = 'idcards_idcard'
                )
            """)
            table_exists = cursor.fetchone()[0]

            if not table_exists:
                print(f"  idcards_idcard table does not exist in schema {schema_name}")
                continue

            # Get all ID cards with status 'PENDING_SUBCITY' in this schema
            cursor.execute(f"""
                SELECT id, status
                FROM "{schema_name}".idcards_idcard
                WHERE status = 'PENDING_SUBCITY'
                ORDER BY id
            """)
            id_cards = cursor.fetchall()

            print(f"  Found {len(id_cards)} ID cards with status 'PENDING_SUBCITY' in schema {schema_name}")

            # Apply patterns to each ID card
            for id_card in id_cards:
                card_id, status = id_card
                print(f"  Processing ID Card {card_id} with status {status}")

                try:
                    # Generate a pattern seed
                    pattern_seed = generate_pattern_seed()

                    # Generate both pattern halves
                    kebele_pattern, subcity_pattern = generate_pattern_halves(pattern_seed)

                    # Update the ID card with the pattern
                    cursor.execute(f"""
                        UPDATE "{schema_name}".idcards_idcard
                        SET pattern_seed = '{pattern_seed}',
                            kebele_pattern = '{kebele_pattern}'
                        WHERE id = {card_id}
                    """)

                    print(f"  Applied kebele pattern to ID Card {card_id}")
                except Exception as e:
                    print(f"  Error applying pattern to ID Card {card_id}: {str(e)}")

    print("\n=== Pattern Application Complete ===")

if __name__ == "__main__":
    main()
