import os
import django
import json
import requests

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'neocamelot.settings')
django.setup()

# Base URL for API
BASE_URL = 'http://localhost:8000'

def test_login_with_schema_cookie(email, password, schema_name):
    """Test login with schema cookie."""
    print(f"\nTesting login with schema cookie for email: {email}, schema: {schema_name}")
    
    # Prepare request data
    data = {
        'email': email,
        'password': password,
        'schema_name': schema_name
    }
    
    # Prepare headers
    headers = {
        'Content-Type': 'application/json',
        'X-Schema-Name': schema_name
    }
    
    # Prepare cookies
    cookies = {
        'schema_name': schema_name
    }
    
    # Make the request
    try:
        response = requests.post(
            f'{BASE_URL}/api/jwt/login/',
            json=data,
            headers=headers,
            cookies=cookies
        )
        
        # Print response details
        print(f"Status code: {response.status_code}")
        print(f"Response headers: {response.headers}")
        
        try:
            response_data = response.json()
            print(f"Response data: {json.dumps(response_data, indent=2)}")
        except json.JSONDecodeError:
            print(f"Response text: {response.text}")
        
        return response.status_code == 200
    except Exception as e:
        print(f"Error: {str(e)}")
        return False

# Test with users from different tenants
test_cases = [
    {'email': '<EMAIL>', 'password': 'password123', 'schema': 'kebele16'},
]

# Run the tests
for test_case in test_cases:
    success = test_login_with_schema_cookie(test_case['email'], test_case['password'], test_case['schema'])
    print(f"Login {'successful' if success else 'failed'}")
    print("-" * 50)
