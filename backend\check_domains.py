import os
import django

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'neocamelot.settings')
django.setup()

from centers.models import Client, Domain
from django_tenants.utils import schema_context

def check_domains():
    """Check all domains for all tenants."""
    print("Checking domains for all tenants...")
    
    # Get all tenants
    tenants = Client.objects.all()
    
    for tenant in tenants:
        print(f"\nTenant: {tenant.name} (schema: {tenant.schema_name})")
        
        # Get domains for this tenant
        with schema_context('public'):
            domains = Domain.objects.filter(tenant=tenant)
            if domains.exists():
                print(f"  Domains:")
                for domain in domains:
                    print(f"    - {domain.domain} (primary: {domain.is_primary})")
            else:
                print("  No domains found for this tenant")

if __name__ == '__main__':
    check_domains()
