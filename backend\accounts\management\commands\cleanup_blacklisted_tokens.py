"""
Management command to clean up expired blacklisted tokens.

This command removes blacklisted tokens that have expired from the database.
It should be run periodically (e.g., daily) to keep the database clean.
"""

from django.core.management.base import BaseCommand
from django.utils import timezone
from accounts.models import BlacklistedToken
import logging

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Clean up expired blacklisted tokens'

    def handle(self, *args, **options):
        """
        Handle the command execution.
        """
        try:
            # Get current time
            now = timezone.now()
            
            # Find expired tokens
            expired_tokens = BlacklistedToken.objects.filter(expires_at__lt=now)
            count = expired_tokens.count()
            
            # Delete expired tokens
            expired_tokens.delete()
            
            self.stdout.write(
                self.style.SUCCESS(f'Successfully deleted {count} expired blacklisted tokens')
            )
            logger.info(f'Successfully deleted {count} expired blacklisted tokens')
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Error cleaning up blacklisted tokens: {str(e)}')
            )
            logger.error(f'Error cleaning up blacklisted tokens: {str(e)}')
