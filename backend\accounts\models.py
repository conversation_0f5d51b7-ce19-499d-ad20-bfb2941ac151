from django.db import models
from django.contrib.auth.models import AbstractUser, BaseUserManager
from django.utils.translation import gettext_lazy as _
from django.utils import timezone

class UserManager(BaseUserManager):
    """Custom user manager for the User model."""

    def create_user(self, email, password=None, **extra_fields):
        """Create and save a regular user with the given email and password."""
        if not email:
            raise ValueError('The Email field must be set')
        email = self.normalize_email(email)
        user = self.model(email=email, **extra_fields)
        user.set_password(password)

        # Check if we're in a tenant context
        from django.db import connection
        current_schema = connection.schema_name

        # Only save the user if we're not in the public schema
        # or if this is a superuser (which should be in public schema)
        if current_schema != 'public' or extra_fields.get('is_superuser', False):
            user.save(using=self._db)
        else:
            print(f"Warning: Attempted to create user {email} in public schema. Skipping save.")

        return user

    def create_superuser(self, email, password=None, **extra_fields):
        """Create and save a superuser with the given email and password."""
        extra_fields.setdefault('is_staff', True)
        extra_fields.setdefault('is_superuser', True)
        extra_fields.setdefault('is_active', True)
        extra_fields.setdefault('role', 'SUPER_ADMIN')

        if extra_fields.get('is_staff') is not True:
            raise ValueError('Superuser must have is_staff=True.')
        if extra_fields.get('is_superuser') is not True:
            raise ValueError('Superuser must have is_superuser=True.')

        return self.create_user(email, password, **extra_fields)

class User(AbstractUser):
    """Custom user model for the NeoCamelot system."""

    ROLE_CHOICES = (
        ('SUPER_ADMIN', 'Super Admin'),
        ('CITY_ADMIN', 'City Admin'),
        ('SUBCITY_ADMIN', 'Subcity Admin'),
        ('CENTER_ADMIN', 'Center Admin'),
        ('CENTER_STAFF', 'Center Staff'),
        ('KEBELE_LEADER', 'Kebele Leader (Liqe Menber)'),
    )

    # Keep username field but make it optional
    username = models.CharField(_('username'), max_length=150, blank=True, null=True, unique=True,
                               help_text=_('Optional. Domain-specific username for login.'))
    email = models.EmailField(_('email address'), unique=True)
    role = models.CharField(max_length=20, choices=ROLE_CHOICES, default='CENTER_STAFF')
    center = models.ForeignKey('centers.Kebele', on_delete=models.SET_NULL, null=True, blank=True, related_name='users')
    city = models.ForeignKey('centers.City', on_delete=models.SET_NULL, null=True, blank=True, related_name='users')
    subcity = models.ForeignKey('centers.Subcity', on_delete=models.SET_NULL, null=True, blank=True, related_name='users')

    USERNAME_FIELD = 'email'
    REQUIRED_FIELDS = []

    objects = UserManager()

    def __str__(self):
        return self.email

    def save(self, *args, **kwargs):
        """Override save method to prevent saving in public schema unless it's a superuser."""
        from django.db import connection
        current_schema = connection.schema_name

        # Only save the user if we're not in the public schema
        # or if this is a superuser (which should be in public schema)
        if current_schema != 'public' or self.is_superuser:
            super().save(*args, **kwargs)
        else:
            print(f"Warning: Attempted to save user {self.email} in public schema. Skipping save.")

    @property
    def is_center_admin(self):
        return self.role == 'CENTER_ADMIN'

    @property
    def is_center_staff(self):
        return self.role == 'CENTER_STAFF'

    @property
    def is_super_admin(self):
        return self.role == 'SUPER_ADMIN'

    @property
    def is_city_admin(self):
        return self.role == 'CITY_ADMIN'

    @property
    def is_subcity_admin(self):
        return self.role == 'SUBCITY_ADMIN'

    @property
    def is_kebele_leader(self):
        return self.role == 'KEBELE_LEADER'


class BlacklistedToken(models.Model):
    """
    Model for storing blacklisted JWT tokens.

    This is used for token rotation to prevent refresh token reuse.
    """
    token = models.TextField(unique=True)
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='blacklisted_tokens')
    blacklisted_at = models.DateTimeField(default=timezone.now)
    expires_at = models.DateTimeField()
    token_type = models.CharField(max_length=10, default='refresh')  # 'refresh' or 'access'

    class Meta:
        verbose_name = 'Blacklisted Token'
        verbose_name_plural = 'Blacklisted Tokens'
        indexes = [
            models.Index(fields=['token']),
            models.Index(fields=['user']),
            models.Index(fields=['expires_at']),
        ]

    def __str__(self):
        return f"Token for {self.user.email} (blacklisted at {self.blacklisted_at})"
