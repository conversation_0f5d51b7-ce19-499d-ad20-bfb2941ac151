/**
 * Direct Fix for Headers Issue
 *
 * This script directly fixes the issues with duplicated headers in fetch requests.
 */

(function() {
  console.log('Running direct headers fix...');

  // This script should not use hardcoded schemas
  // Instead, it should get the schema from localStorage or cookies
  const SCHEMA_NAME = localStorage.getItem('schema_name') || '';
  const URL_SCHEMA = SCHEMA_NAME.replace(/ /g, '_');

  // Override the fetch function to fix headers
  const originalFetch = window.fetch;
  window.fetch = function(url, options = {}) {
    // Only modify requests to API endpoints
    if (typeof url === 'string' && url.includes('/api/')) {
      console.log('Original fetch request:', url, options);

      // Create new options with fixed headers
      options = options || {};

      // Create new headers object to avoid modifying existing headers
      const newHeaders = new Headers();

      // Get the token from localStorage
      const token = localStorage.getItem('token');
      if (token) {
        // Clean the token (remove ALL spaces and commas)
        const cleanToken = token.replace(/[\s,]+/g, '');

        // Set the Authorization header with the clean token
        newHeaders.set('Authorization', `Token ${cleanToken}`);
      }

      // Set the X-Schema-Name header
      newHeaders.set('X-Schema-Name', SCHEMA_NAME);

      // Set the Content-Type header if not already set
      if (!newHeaders.has('Content-Type')) {
        newHeaders.set('Content-Type', 'application/json');
      }

      // Copy any other headers from the original options
      if (options.headers) {
        const originalHeaders = new Headers(options.headers);
        originalHeaders.forEach((value, key) => {
          // Skip Authorization and X-Schema-Name headers
          if (key.toLowerCase() !== 'authorization' && key.toLowerCase() !== 'x-schema-name') {
            newHeaders.set(key, value);
          }
        });
      }

      // Replace the headers in the options
      options.headers = newHeaders;

      // Fix the URL if it's a tenant-specific endpoint
      if (url.includes('/api/tenant/')) {
        // Extract the schema from the URL
        const urlParts = url.split('/');
        const tenantIndex = urlParts.indexOf('tenant');
        if (tenantIndex >= 0 && tenantIndex < urlParts.length - 1) {
          // Replace the schema in the URL with the correct format
          urlParts[tenantIndex + 1] = URL_SCHEMA;
          url = urlParts.join('/');
        }
      }

      // Set the schema cookie
      document.cookie = `schema_name=${encodeURIComponent(SCHEMA_NAME)}; path=/; SameSite=Lax`;

      console.log('Modified fetch request:', url, options);
      console.log('Headers:', Object.fromEntries(newHeaders.entries()));
    }

    // Call the original fetch function with the modified arguments
    return originalFetch.call(this, url, options);
  };

  // Fix the current authentication data
  function fixAuthData() {
    // Get the token from localStorage
    const token = localStorage.getItem('token');
    if (token) {
      // Clean the token (remove ALL spaces and commas)
      const cleanToken = token.replace(/[\s,]+/g, '');

      // Store the clean token in localStorage
      localStorage.setItem('token', cleanToken);

      console.log('Cleaned token in localStorage:', cleanToken);
    }

    // Set the schema name in localStorage
    localStorage.setItem('schema_name', SCHEMA_NAME);

    // Set the schema cookie
    document.cookie = `schema_name=${encodeURIComponent(SCHEMA_NAME)}; path=/; SameSite=Lax`;

    // Update the tokenStore
    try {
      const tokenStoreStr = localStorage.getItem('tokenStore');
      let tokenStore = tokenStoreStr ? JSON.parse(tokenStoreStr) : {};

      // Get the clean token
      const cleanToken = localStorage.getItem('token');
      if (cleanToken) {
        // Set the token for the schema
        tokenStore[SCHEMA_NAME] = cleanToken;

        // Remove any entries with problematic schema names
        Object.keys(tokenStore).forEach(key => {
          if (key !== SCHEMA_NAME) {
            delete tokenStore[key];
          }
        });

        // Save the tokenStore
        localStorage.setItem('tokenStore', JSON.stringify(tokenStore));

        console.log('Updated tokenStore:', tokenStore);
      }
    } catch (error) {
      console.error('Error updating tokenStore:', error);
    }

    // Update the tenant object
    try {
      const tenantStr = localStorage.getItem('tenant');
      let tenant = tenantStr ? JSON.parse(tenantStr) : null;

      if (!tenant) {
        // Create a basic tenant object
        tenant = {
          schema_name: SCHEMA_NAME,
          name: SCHEMA_NAME
        };
      } else {
        // Update the schema_name in the tenant object
        tenant.schema_name = SCHEMA_NAME;
      }

      // Save the tenant object
      localStorage.setItem('tenant', JSON.stringify(tenant));

      console.log('Updated tenant object:', tenant);
    } catch (error) {
      console.error('Error updating tenant object:', error);
    }
  }

  // Run the fix
  fixAuthData();

  console.log('Direct headers fix completed');
})();
