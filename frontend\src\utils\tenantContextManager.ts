/**
 * Tenant Context Manager
 *
 * This module provides utilities for managing tenant contexts in the application.
 * It ensures that the correct tenant context is used for each operation and
 * handles tenant switching in a consistent way.
 */

import { getAccessTokenForSchema, getCurrentSchema } from '../services/tokenService';
import { setCacheItem, getCacheItem } from './cacheManager';

// Tenant information interface
export interface TenantInfo {
  id: number;
  name: string;
  schema_name: string;
  schema_type: string;
  parent_id?: number | null;
  logo_url?: string;
  header_color?: string;
  accent_color?: string;
}

// Cache expiration time for tenant information (30 minutes)
const TENANT_CACHE_EXPIRATION = 30 * 60 * 1000;

/**
 * Get tenant information from the server
 * @param schemaName The schema name
 * @returns The tenant information, or null if not found
 */
export const getTenantInfo = async (schemaName: string): Promise<TenantInfo | null> => {
  if (!schemaName) {
    console.error('Cannot get tenant info: schema name is missing');
    return null;
  }

  // Create a cache key for this tenant
  const cacheKey = `tenant_info_${schemaName}`;

  // Check if we have the tenant info in the cache
  const cachedInfo = getCacheItem<TenantInfo>(cacheKey);
  if (cachedInfo) {
    console.log(`getTenantInfo: Using cached tenant info for schema ${schemaName}`);
    return cachedInfo;
  }

  try {
    console.log(`getTenantInfo: Fetching tenant info for schema ${schemaName}`);

    // Get the token for this schema
    const token = getAccessTokenForSchema(schemaName);

    // Create headers
    const headers: HeadersInit = {
      'Accept': 'application/json',
    };

    // Add the token if available
    if (token) {
      // Use Bearer prefix for JWT tokens
      const tokenWithPrefix = token.startsWith('Bearer ') ? token : `Bearer ${token}`;
      headers['Authorization'] = tokenWithPrefix;
      console.log('getTenantInfo: Added Authorization header with Bearer token');
    }

    // Add the schema name
    headers['X-Schema-Name'] = schemaName;

    // Make the request with timeout
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 second timeout

    try {
      // Make the request
      const response = await fetch(`/api/tenant-info/${schemaName}/`, {
        method: 'GET',
        headers,
        credentials: 'include',
        signal: controller.signal
      });

      clearTimeout(timeoutId); // Clear the timeout

      if (response.ok) {
        const data = await response.json();
        console.log(`getTenantInfo: Tenant info for schema ${schemaName}:`, data);

        // Cache the tenant info
        setCacheItem<TenantInfo>(cacheKey, data, TENANT_CACHE_EXPIRATION);

        return data;
      } else {
        console.error(`getTenantInfo: Failed to fetch tenant info for schema ${schemaName}: ${response.status} ${response.statusText}`);

        // Try alternative approach without authentication
        console.log('getTenantInfo: Trying alternative approach without authentication');
        const altResponse = await fetch(`/api/tenant-info/${schemaName}/`, {
          method: 'GET',
          headers: {
            'Accept': 'application/json',
            'X-Schema-Name': schemaName
          },
          credentials: 'include'
        });

        if (altResponse.ok) {
          const altData = await altResponse.json();
          console.log(`getTenantInfo: Successfully fetched tenant info using alternative approach:`, altData);

          // Cache the tenant info
          setCacheItem<TenantInfo>(cacheKey, altData, TENANT_CACHE_EXPIRATION);

          return altData;
        } else {
          console.error(`getTenantInfo: Alternative approach also failed: ${altResponse.status} ${altResponse.statusText}`);
          return null;
        }
      }
    } catch (fetchError) {
      clearTimeout(timeoutId); // Clear the timeout

      if (fetchError.name === 'AbortError') {
        console.error(`getTenantInfo: Request timed out for schema ${schemaName}`);
      } else {
        console.error(`getTenantInfo: Error during fetch for schema ${schemaName}:`, fetchError);
      }

      // Try one more time with a simpler approach
      try {
        console.log('getTenantInfo: Trying simple fetch as last resort');
        const lastResortResponse = await fetch(`/api/tenant-info/${schemaName}/`);

        if (lastResortResponse.ok) {
          const lastResortData = await lastResortResponse.json();
          console.log(`getTenantInfo: Last resort fetch successful:`, lastResortData);

          // Cache the tenant info
          setCacheItem<TenantInfo>(cacheKey, lastResortData, TENANT_CACHE_EXPIRATION);

          return lastResortData;
        } else {
          console.error(`getTenantInfo: Last resort fetch also failed: ${lastResortResponse.status} ${lastResortResponse.statusText}`);
        }
      } catch (lastError) {
        console.error(`getTenantInfo: Last resort fetch also failed:`, lastError);
      }

      return null;
    }
  } catch (error) {
    console.error(`getTenantInfo: Error fetching tenant info for schema ${schemaName}:`, error);
    return null;
  }
};

/**
 * Get the correct schema name for a tenant
 * @param schemaName The schema name to check
 * @returns The correct schema name, or the original if not found
 */
export const getCorrectSchemaName = async (schemaName: string): Promise<string> => {
  if (!schemaName) {
    console.error('Cannot get correct schema name: schema name is missing');
    return '';
  }

  try {
    console.log(`getCorrectSchemaName: Checking schema name ${schemaName}`);

    // Get the tenant info
    const tenantInfo = await getTenantInfo(schemaName);

    if (tenantInfo && tenantInfo.schema_name) {
      console.log(`getCorrectSchemaName: Correct schema name for ${schemaName} is ${tenantInfo.schema_name}`);
      return tenantInfo.schema_name;
    } else {
      console.log(`getCorrectSchemaName: No tenant info found for schema ${schemaName}, using original`);
      return schemaName;
    }
  } catch (error) {
    console.error(`getCorrectSchemaName: Error getting correct schema name for ${schemaName}:`, error);
    return schemaName;
  }
};

/**
 * Update the current schema name in localStorage, sessionStorage, and cookies
 * @param schemaName The schema name to set
 */
export const updateCurrentSchema = (schemaName: string): void => {
  if (!schemaName) {
    console.error('Cannot update current schema: schema name is missing');
    return;
  }

  console.log(`updateCurrentSchema: Updating current schema to ${schemaName}`);

  // Update localStorage and sessionStorage
  localStorage.setItem('schema_name', schemaName);
  sessionStorage.setItem('schema_name', schemaName);
  sessionStorage.setItem('jwt_schema', schemaName);

  // Update cookie
  document.cookie = `schema_name=${encodeURIComponent(schemaName)}; path=/; SameSite=Lax`;

  // Update the tenant object in localStorage if available
  try {
    const tenantStr = localStorage.getItem('tenant');
    if (tenantStr) {
      const tenant = JSON.parse(tenantStr);
      if (tenant) {
        tenant.schema_name = schemaName;
        localStorage.setItem('tenant', JSON.stringify(tenant));
      }
    }
  } catch (error) {
    console.error('Error updating schema name in localStorage tenant object:', error);
  }

  // Update the tenant object in sessionStorage if available
  try {
    const tenantStr = sessionStorage.getItem('tenant');
    if (tenantStr) {
      const tenant = JSON.parse(tenantStr);
      if (tenant) {
        tenant.schema_name = schemaName;
        sessionStorage.setItem('tenant', JSON.stringify(tenant));
      }
    }
  } catch (error) {
    console.error('Error updating schema name in sessionStorage tenant object:', error);
  }
};

/**
 * Get the parent tenant for a given tenant
 * @param schemaName The schema name of the tenant
 * @returns The parent tenant info, or null if not found
 */
export const getParentTenant = async (schemaName: string): Promise<TenantInfo | null> => {
  if (!schemaName) {
    console.error('Cannot get parent tenant: schema name is missing');
    return null;
  }

  try {
    console.log(`getParentTenant: Getting parent tenant for schema ${schemaName}`);

    // Get the tenant info
    const tenantInfo = await getTenantInfo(schemaName);

    if (tenantInfo && tenantInfo.parent_id) {
      // Get the parent tenant info
      const token = getAccessTokenForSchema(schemaName);

      // Create headers
      const headers: HeadersInit = {
        'Accept': 'application/json',
      };

      // Add the token if available
      if (token) {
        // Use Bearer prefix for JWT tokens
        const tokenWithPrefix = token.startsWith('Bearer ') ? token : `Bearer ${token}`;
        headers['Authorization'] = tokenWithPrefix;
        console.log('getParentTenant: Added Authorization header with Bearer token');
      }

      // Make the request
      const response = await fetch(`/api/tenants/${tenantInfo.parent_id}/`, {
        method: 'GET',
        headers,
        credentials: 'include'
      });

      if (response.ok) {
        const data = await response.json();
        console.log(`getParentTenant: Parent tenant for schema ${schemaName}:`, data);
        return data;
      } else {
        console.error(`getParentTenant: Failed to fetch parent tenant for schema ${schemaName}: ${response.status} ${response.statusText}`);
        return null;
      }
    } else {
      console.log(`getParentTenant: No parent tenant found for schema ${schemaName}`);
      return null;
    }
  } catch (error) {
    console.error(`getParentTenant: Error getting parent tenant for schema ${schemaName}:`, error);
    return null;
  }
};

/**
 * Get child tenants for a given tenant
 * @param schemaName The schema name of the parent tenant
 * @returns Array of child tenant info, or empty array if none found
 */
export const getChildTenants = async (schemaName: string): Promise<TenantInfo[]> => {
  if (!schemaName) {
    console.error('Cannot get child tenants: schema name is missing');
    return [];
  }

  // Create a cache key for child tenants
  const cacheKey = `child_tenants_${schemaName}`;

  // Check if we have the child tenants in the cache
  const cachedTenants = getCacheItem<TenantInfo[]>(cacheKey);
  if (cachedTenants) {
    console.log(`getChildTenants: Using cached child tenants for schema ${schemaName}`);
    return cachedTenants;
  }

  try {
    console.log(`getChildTenants: Getting child tenants for schema ${schemaName}`);

    // Get the tenant info
    const tenantInfo = await getTenantInfo(schemaName);

    if (!tenantInfo) {
      console.error(`getChildTenants: No tenant info found for schema ${schemaName}`);
      return [];
    }

    // Determine the endpoint based on tenant type
    let endpoint = '';
    if (tenantInfo.schema_type === 'CITY') {
      endpoint = '/api/child-subcities/';
    } else if (tenantInfo.schema_type === 'SUBCITY') {
      endpoint = '/api/child-kebeles/';
    } else {
      console.log(`getChildTenants: Tenant type ${tenantInfo.schema_type} does not have child tenants`);
      return [];
    }

    // Get the token for this schema
    const token = getAccessTokenForSchema(schemaName);

    // Create headers
    const headers: HeadersInit = {
      'Accept': 'application/json',
      'X-Schema-Name': schemaName
    };

    // Add the token if available
    if (token) {
      // Use Bearer prefix for JWT tokens
      const tokenWithPrefix = token.startsWith('Bearer ') ? token : `Bearer ${token}`;
      headers['Authorization'] = tokenWithPrefix;
      console.log('getChildTenants: Added Authorization header with Bearer token');
    }

    // Make the request
    const response = await fetch(endpoint, {
      method: 'GET',
      headers,
      credentials: 'include'
    });

    if (response.ok) {
      const data = await response.json();
      console.log(`getChildTenants: Child tenants for schema ${schemaName}:`, data);

      // Cache the child tenants
      setCacheItem<TenantInfo[]>(cacheKey, data, TENANT_CACHE_EXPIRATION);

      return data;
    } else {
      console.error(`getChildTenants: Failed to fetch child tenants for schema ${schemaName}: ${response.status} ${response.statusText}`);
      return [];
    }
  } catch (error) {
    console.error(`getChildTenants: Error getting child tenants for schema ${schemaName}:`, error);
    return [];
  }
};

/**
 * Switch to a different tenant context
 * @param schemaName The schema name to switch to
 * @returns True if the switch was successful, false otherwise
 */
export const switchTenantContext = async (schemaName: string): Promise<boolean> => {
  if (!schemaName) {
    console.error('Cannot switch tenant context: schema name is missing');
    return false;
  }

  try {
    console.log(`switchTenantContext: Switching to tenant context ${schemaName}`);

    // Get the correct schema name
    const correctSchemaName = await getCorrectSchemaName(schemaName);

    // Get the tenant info
    const tenantInfo = await getTenantInfo(correctSchemaName);

    if (!tenantInfo) {
      console.error(`switchTenantContext: No tenant info found for schema ${correctSchemaName}`);
      return false;
    }

    // Update the current schema in both localStorage and sessionStorage
    updateCurrentSchema(correctSchemaName);

    // Update the tenant object in both localStorage and sessionStorage
    localStorage.setItem('tenant', JSON.stringify(tenantInfo));
    sessionStorage.setItem('tenant', JSON.stringify(tenantInfo));

    // Store the tenant type in sessionStorage
    if (tenantInfo.schema_type || tenantInfo.type) {
      const tenantType = tenantInfo.schema_type || tenantInfo.type;
      sessionStorage.setItem('tenant_type', tenantType);
      console.log(`switchTenantContext: Stored tenant type in sessionStorage: ${tenantType}`);
    }

    // Get the user from sessionStorage
    const userStr = sessionStorage.getItem('user');
    if (userStr) {
      try {
        const user = JSON.parse(userStr);
        if (user && user.role) {
          // Store the user role in sessionStorage
          sessionStorage.setItem('user_role', user.role);
          console.log(`switchTenantContext: Stored user role in sessionStorage: ${user.role}`);
        }
      } catch (parseError) {
        console.error('switchTenantContext: Error parsing user data:', parseError);
      }
    }

    console.log(`switchTenantContext: Successfully switched to tenant context ${correctSchemaName}`);
    return true;
  } catch (error) {
    console.error(`switchTenantContext: Error switching to tenant context ${schemaName}:`, error);
    return false;
  }
};
