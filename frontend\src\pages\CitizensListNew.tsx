import React, { useState, useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Container,
  Typography,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Button,
  IconButton,
  TextField,
  InputAdornment,
  CircularProgress,
  Alert,
  Chip,
  Avatar,
  Snackbar
} from '@mui/material';
import {
  Add as AddIcon,
  Search as SearchIcon,
  Visibility as VisibilityIcon,
  FilterList as FilterListIcon,
  Male as MaleIcon,
  Female as FemaleIcon,
  Refresh as RefreshIcon,
  CardMembership as CardMembershipIcon
} from '@mui/icons-material';

// Import services
import { tenantApi } from '../services/api';
import { API_BASE_URL } from '../config/apiConfig';
// @ts-ignore
import { fixTokenStorage, preserveCookies } from '../utils/tokenFix';

// Import components
import PageBanner from '../components/common/PageBanner';
import UnauthorizedAccess from '../components/common/UnauthorizedAccess';
import PersonIcon from '@mui/icons-material/Person';

// Define citizen interface
interface Citizen {
  id: number;
  first_name: string;
  last_name: string;
  middle_name?: string;
  gender: string;
  date_of_birth: string;
  phone?: string;
  phone_number?: string;
  id_number?: string;
  photo?: string;
}

const CitizensListNew: React.FC = () => {
  // Navigation
  const navigate = useNavigate();

  // State
  const [citizens, setCitizens] = useState<Citizen[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [page, setPage] = useState<number>(0);
  const [rowsPerPage, setRowsPerPage] = useState<number>(10);
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [isTenantAuthorized, setIsTenantAuthorized] = useState<boolean>(true);
  const [snackbarOpen, setSnackbarOpen] = useState<boolean>(false);
  const [snackbarMessage, setSnackbarMessage] = useState<string>('');
  const [sessionExpired, setSessionExpired] = useState<boolean>(false);
  const [invalidRefreshToken, setInvalidRefreshToken] = useState<boolean>(false);

  // Get authentication info from localStorage
  const schemaString = localStorage.getItem('schema_name');
  const schema = schemaString || '';

  // Get JWT token from localStorage
  const token = localStorage.getItem('jwt_access_token');

  // Function to show a message in the snackbar
  const showMessage = (message: string) => {
    setSnackbarMessage(message);
    setSnackbarOpen(true);
  };

  // Handle snackbar close
  const handleSnackbarClose = () => {
    setSnackbarOpen(false);
  };

  // Handle login
  const handleLogin = () => {
    // Clear any expired token flags
    localStorage.removeItem('token_expired');
    localStorage.removeItem('token_expired_reason');
    localStorage.removeItem('token_expired_schema');
    localStorage.removeItem('token_expired_timestamp');

    // Preserve any valid cookies before navigation
    preserveCookies();

    // Navigate to login page
    navigate('/login');
  };

  // Calculate age from date of birth
  const calculateAge = (dateOfBirth: string) => {
    const today = new Date();
    const birthDate = new Date(dateOfBirth);
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();

    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }

    return age;
  };

  // Get phone number from citizen object
  const getPhoneNumber = (citizen: Citizen) => {
    return citizen.phone || citizen.phone_number || '';
  };

  // Handle search input change
  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(event.target.value);
  };

  // Handle page change
  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };

  // Handle rows per page change
  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  // Navigate to citizen registration page
  const handleRegisterCitizen = () => {
    // Preserve cookies before navigation
    preserveCookies();
    navigate('/citizens/new');
  };

  // Navigate to citizen details page
  const handleViewCitizen = (id: number) => {
    // Preserve cookies before navigation
    preserveCookies();
    navigate(`/citizens/${id}`);
  };

  // Navigate to ID card registration for a specific citizen
  const handleRegisterIDCard = (citizenId: number) => {
    // Preserve cookies before navigation
    preserveCookies();
    navigate(`/id-cards/new?citizen=${citizenId}`);
  };

  // Fetch citizens data from API
  const fetchCitizens = async () => {
    setLoading(true);
    setError(null);

    try {
      // Get the latest schema from localStorage
      const currentSchema = localStorage.getItem('schema_name');

      if (!currentSchema) {
        console.error('No schema found in fetchCitizens');
        setError('No tenant selected. Please log in again.');
        setLoading(false);
        return;
      }

      console.log(`Fetching citizens with schema: ${currentSchema}`);

      // Format schema for API endpoint (replace spaces with underscores if needed)
      const formattedSchema = currentSchema.replace(/\s+/g, '_');

      // Ensure the schema is stored consistently
      localStorage.setItem('jwt_schema', formattedSchema);
      localStorage.setItem('schema_name', formattedSchema);

      console.log(`Using formatted schema: ${formattedSchema}`);

      // Import and use the resilient API client
      const { resilientApiClient } = await import('../services/resilientApiClient');

      // Use the resilient API client to fetch citizens data
      console.log('Making API call to fetch citizens data using resilient API client');
      const responseData = await resilientApiClient.get('citizens/?detail=true', formattedSchema);
      console.log('API call successful');

      // Handle different response formats
      let data: Citizen[] = [];
      if (responseData && typeof responseData === 'object') {
        // If the response is an object with a data property that is an array
        if ('data' in responseData && Array.isArray(responseData.data)) {
          data = responseData.data;
          console.log('Found data array in response object');
        }
        // If the response is an array directly
        else if (Array.isArray(responseData)) {
          data = responseData;
          console.log('Response is an array');
        }
        // If the response has results property that is an array (common in DRF pagination)
        else if ('results' in responseData && Array.isArray(responseData.results)) {
          data = responseData.results;
          console.log('Found results array in response object');
        }
        // If the response is an object with citizen data
        else if ('id' in responseData || 'first_name' in responseData) {
          data = [responseData];
          console.log('Response is a single citizen object, converting to array');
        }
      }

      console.log(`Fetched ${data.length} citizens`);
      setCitizens(data);
      setLoading(false);
      setError(null);
    } catch (error: any) {
      console.error('Error fetching citizens:', error);

      // Check if this is an authentication error
      if (error.response && error.response.status === 401) {
        console.warn('Authentication error (401). You may need to log in again.');
        setSessionExpired(true);
        setError('Your session has expired. Please log in again.');

        // Store the expired token info
        localStorage.setItem('token_expired', 'true');
        localStorage.setItem('token_expired_reason', 'api_401_error');
        localStorage.setItem('token_expired_schema', schema || '');
        localStorage.setItem('token_expired_timestamp', Date.now().toString());
      } else if (error.message === 'INVALID_REFRESH_TOKEN') {
        // This is a specific error for invalid refresh tokens from the token service
        console.warn('Invalid refresh token error from token service.');
        setInvalidRefreshToken(true);
        setSessionExpired(true);
        setError('Your refresh token is invalid or has expired. Please log in again.');

        // Use TokenManager to handle invalid refresh token
        try {
          const { tokenManager } = await import('../services/tokenManager');
          tokenManager.handleInvalidRefreshToken(schema);
        } catch (importError) {
          console.error('Error importing tokenManager:', importError);

          // Fallback to traditional token clearing if TokenManager fails
          localStorage.removeItem('jwt_refresh_token');
          localStorage.removeItem(`jwt_refresh_token_${schema}`);

          // Clear cookies
          document.cookie = 'jwt_refresh_token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT; SameSite=Lax';
          document.cookie = 'refresh_token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT; SameSite=Lax';
        }
      } else if (error.message === 'SESSION_EXPIRED' || error.message.includes('Authentication failed')) {
        // This error comes from the token service, which has already set the token_expired flag
        console.warn('Session expired error from token service.');
        setSessionExpired(true);
        setError('Your authentication session has expired. Please log in again.');
      } else if (error.response?.data?.error === 'Invalid refresh token' || error.message.includes('Invalid refresh token')) {
        // This is a specific error for invalid refresh tokens
        console.warn('Invalid refresh token error detected.');
        setInvalidRefreshToken(true);
        setSessionExpired(true);
        setError('Your refresh token is invalid or has expired. Please log in again.');

        // Clear any invalid tokens
        localStorage.removeItem('jwt_refresh_token');
        localStorage.removeItem(`jwt_refresh_token_${schema}`);

        // Clear cookies
        document.cookie = 'jwt_refresh_token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT; SameSite=Lax';
        document.cookie = 'refresh_token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT; SameSite=Lax';

        // Set the token_expired flag
        localStorage.setItem('token_expired', 'true');
        localStorage.setItem('token_expired_reason', 'invalid_refresh_token');
        localStorage.setItem('token_expired_schema', schema || '');
        localStorage.setItem('token_expired_timestamp', Date.now().toString());
      } else {
        const errorMessage = error.message || 'Unknown error';
        console.error(`Error details: ${errorMessage}`);
        setError(`Error fetching citizens: ${errorMessage}`);
      }

      setLoading(false);
    }
  };

  // Direct API call for testing
  const handleDirectApiCall = async () => {
    setLoading(true);
    setError(null);

    try {
      // Get the latest schema from localStorage
      const currentSchema = localStorage.getItem('schema_name');

      if (!currentSchema) {
        console.error('No schema found for direct API call');
        setError('No tenant selected. Please log in again.');
        setLoading(false);
        return;
      }

      // Import the direct API call utility
      const { directApiGet } = await import('../utils/directApiCall');

      // Format schema for API endpoint
      const formattedSchema = currentSchema.replace(/\s+/g, '_');

      console.log(`Making direct API call with schema: ${formattedSchema}`);

      // Make the direct API call
      const responseData = await directApiGet('citizens/?detail=true', formattedSchema);

      console.log('Direct API call successful:', responseData);

      // Handle different response formats
      let data: Citizen[] = [];
      if (responseData && typeof responseData === 'object') {
        // If the response is an object with a data property that is an array
        if ('data' in responseData && Array.isArray(responseData.data)) {
          data = responseData.data;
        }
        // If the response is an array directly
        else if (Array.isArray(responseData)) {
          data = responseData;
        }
        // If the response has results property that is an array (common in DRF pagination)
        else if ('results' in responseData && Array.isArray(responseData.results)) {
          data = responseData.results;
        }
        // If the response is an object with citizen data
        else if ('id' in responseData || 'first_name' in responseData) {
          data = [responseData];
        }
      }

      console.log(`Direct API call fetched ${data.length} citizens`);
      setCitizens(data);
      setSnackbarMessage('Direct API call successful!');
      setSnackbarOpen(true);
    } catch (error: any) {
      console.error('Error making direct API call:', error);
      setError(`Direct API call failed: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  // Handle search submit
  const handleSearch = async () => {
    // If search is empty, fetch all citizens
    if (!searchQuery.trim()) {
      fetchCitizens();
      return;
    }

    setLoading(true);
    setError(null);

    try {
      // Get the latest schema from localStorage
      const currentSchema = localStorage.getItem('schema_name');

      if (!currentSchema) {
        console.error('No schema found in handleSearch');
        setError('No tenant selected. Please log in again.');
        setLoading(false);
        return;
      }

      console.log(`Searching citizens with schema: ${currentSchema}`);

      // Format schema for API endpoint (replace spaces with underscores if needed)
      const formattedSchema = currentSchema.replace(/\s+/g, '_');

      // Ensure the schema is stored consistently
      localStorage.setItem('jwt_schema', formattedSchema);
      localStorage.setItem('schema_name', formattedSchema);

      console.log(`Using formatted schema: ${formattedSchema}`);

      // Import and use the resilient API client
      const { resilientApiClient } = await import('../services/resilientApiClient');

      // Use the resilient API client to search citizens
      console.log(`Making API call to search citizens with query: "${searchQuery}"`);
      const responseData = await resilientApiClient.get(
        `citizens/?detail=true&search=${encodeURIComponent(searchQuery)}`,
        formattedSchema
      );
      console.log('Search API call successful');

      // Handle different response formats
      let data: Citizen[] = [];
      if (responseData && typeof responseData === 'object') {
        // If the response is an object with a data property that is an array
        if ('data' in responseData && Array.isArray(responseData.data)) {
          data = responseData.data;
          console.log('Found data array in response object');
        }
        // If the response is an array directly
        else if (Array.isArray(responseData)) {
          data = responseData;
          console.log('Response is an array');
        }
        // If the response has results property that is an array (common in DRF pagination)
        else if ('results' in responseData && Array.isArray(responseData.results)) {
          data = responseData.results;
          console.log('Found results array in response object');
        }
        // If the response is an object with citizen data
        else if ('id' in responseData || 'first_name' in responseData) {
          data = [responseData];
          console.log('Response is a single citizen object, converting to array');
        }
      }

      console.log(`Found ${data.length} citizens matching search query`);
      setCitizens(data);
      setLoading(false);
      setError(null);
    } catch (error: any) {
      console.error('Error searching citizens:', error);

      // Check if this is an authentication error
      if (error.response && error.response.status === 401) {
        console.warn('Authentication error (401) during search. You may need to log in again.');
        setSessionExpired(true);
        setError('Your session has expired. Please log in again.');

        // Store the expired token info
        localStorage.setItem('token_expired', 'true');
        localStorage.setItem('token_expired_reason', 'api_401_error_search');
        localStorage.setItem('token_expired_schema', schema || '');
        localStorage.setItem('token_expired_timestamp', Date.now().toString());
      } else if (error.message === 'INVALID_REFRESH_TOKEN') {
        // This is a specific error for invalid refresh tokens from the token service
        console.warn('Invalid refresh token error from token service during search.');
        setInvalidRefreshToken(true);
        setSessionExpired(true);
        setError('Your refresh token is invalid or has expired. Please log in again.');

        // Clear any invalid tokens
        localStorage.removeItem('jwt_refresh_token');
        localStorage.removeItem(`jwt_refresh_token_${schema}`);

        // Clear cookies
        document.cookie = 'jwt_refresh_token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT; SameSite=Lax';
        document.cookie = 'refresh_token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT; SameSite=Lax';
      } else if (error.message === 'SESSION_EXPIRED' || error.message.includes('Authentication failed')) {
        // This error comes from the token service, which has already set the token_expired flag
        console.warn('Session expired error from token service during search.');
        setSessionExpired(true);
        setError('Your authentication session has expired. Please log in again.');
      } else if (error.response?.data?.error === 'Invalid refresh token' || error.message.includes('Invalid refresh token')) {
        // This is a specific error for invalid refresh tokens
        console.warn('Invalid refresh token error detected during search.');
        setInvalidRefreshToken(true);
        setSessionExpired(true);
        setError('Your refresh token is invalid or has expired. Please log in again.');

        // Clear any invalid tokens
        localStorage.removeItem('jwt_refresh_token');
        localStorage.removeItem(`jwt_refresh_token_${schema}`);

        // Clear cookies
        document.cookie = 'jwt_refresh_token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT; SameSite=Lax';
        document.cookie = 'refresh_token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT; SameSite=Lax';

        // Set the token_expired flag
        localStorage.setItem('token_expired', 'true');
        localStorage.setItem('token_expired_reason', 'invalid_refresh_token_search');
        localStorage.setItem('token_expired_schema', schema || '');
        localStorage.setItem('token_expired_timestamp', Date.now().toString());
      } else {
        const errorMessage = error.message || 'Unknown error';
        console.error(`Error details: ${errorMessage}`);
        setError(`Error searching citizens: ${errorMessage}`);
      }

      setLoading(false);
    }
  };



  // Fetch citizens on component mount
  useEffect(() => {
    if (schema && token) {
      console.log('Component mounted, fetching citizens data');
      fetchCitizens();
    } else {
      console.warn('Missing schema or token, cannot fetch citizens');
      setError('Missing authentication information. Please log in again.');
      setLoading(false);
    }
  }, [schema, token]);

  // Filter citizens based on search query if there's a search query but no API search
  const filteredCitizens = searchQuery.trim() ?
    citizens.filter((citizen) => {
      const query = searchQuery.toLowerCase();
      return (
        citizen.first_name?.toLowerCase().includes(query) ||
        citizen.last_name?.toLowerCase().includes(query) ||
        citizen.id_number?.toLowerCase().includes(query) ||
        (citizen.phone && citizen.phone.toLowerCase().includes(query))
      );
    }) : citizens;

  // Get current page of citizens
  const currentCitizens = filteredCitizens.slice(
    page * rowsPerPage,
    page * rowsPerPage + rowsPerPage
  );

  // Get tenant type for conditional rendering
  const storedTenant = localStorage.getItem('tenant');
  const tenantFromStorage = storedTenant ? JSON.parse(storedTenant) : null;
  const tenantType = tenantFromStorage?.type || localStorage.getItem('tenant_type');
  const effectiveTenant = tenantFromStorage;











  // Fetch citizens on component mount
  useEffect(() => {
    // First, try to fix token storage issues
    const fixResult = fixTokenStorage();
    console.log('Token storage fix result:', fixResult);

    // Get the latest token after fixing
    const latestToken = localStorage.getItem('jwt_access_token');
    const latestSchema = localStorage.getItem('schema_name');

    if (latestSchema && latestToken) {
      console.log('Component mounted with fixed tokens, fetching citizens data');

      // Get user role and tenant type from localStorage
      const userRole = localStorage.getItem('user_role');

      // Check if user role is allowed to access this page
      // CENTER_STAFF should always be authorized regardless of tenant type
      const authorized = userRole === 'CENTER_STAFF' ||
                        userRole === 'KEBELE_LEADER' ||
                        userRole === 'SUBCITY_ADMIN' ||
                        userRole === 'CENTER_ADMIN' ||
                        userRole === 'ADMIN';

      console.log('CitizensListNew: User role:', userRole, 'Authorized:', authorized);
      setIsTenantAuthorized(authorized);

      // Fetch citizens data if authorized
      if (authorized) {
        fetchCitizens();
      } else {
        setLoading(false);
      }
    } else {
      console.warn('Missing schema or token even after fixing, cannot fetch citizens');
      setError('Missing authentication information. Please log in again.');
      setLoading(false);
    }
  }, []);



  return (
    <Box sx={{ py: 4 }}>
      {/* Session refresh snackbar */}
      <Snackbar
        open={snackbarOpen}
        autoHideDuration={6000}
        onClose={handleSnackbarClose}
        message={snackbarMessage}
      />

      {/* Banner */}
      <PageBanner
        title="Citizens Management"
        subtitle={`${tenantType === 'KEBELE' ? 'Manage' : 'View'} citizens in ${effectiveTenant?.name || 'your center'}`}
        icon={<PersonIcon sx={{ fontSize: 50, color: 'white' }} />}
        actionContent={
          <Box sx={{ textAlign: 'center', width: '100%' }}>
            <Typography variant="body1" sx={{ color: 'white', fontWeight: 500 }}>
              {tenantType === 'KEBELE'
                ? 'Register, view, and manage citizens. Create ID cards for registered citizens.'
                : 'View and search citizens registered in your jurisdiction. Approve ID cards for citizens.'
              }
            </Typography>
          </Box>
        }
      />

      <Container maxWidth="lg">
        {loading && !error && (
          <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
            <CircularProgress />
          </Box>
        )}

        {!loading && error && (
          <Alert
            severity="error"
            sx={{ mb: 3 }}
            action={
              sessionExpired && (
                <Button
                  color="inherit"
                  size="small"
                  variant="outlined"
                  onClick={handleLogin}
                >
                  Log In
                </Button>
              )
            }
          >
            {invalidRefreshToken ? (
              <>
                <Typography variant="body1" fontWeight="bold">
                  Your session has expired
                </Typography>
                <Typography variant="body2">
                  Your authentication token has expired. Please log in again to continue.
                </Typography>
              </>
            ) : (
              error
            )}
          </Alert>
        )}

        {!loading && !error && !isTenantAuthorized && (
          <UnauthorizedAccess
            message="Your tenant type or user role does not have permission to register or manage citizens."
          />
        )}

        {!loading && !error && isTenantAuthorized && (
          <>
            {/* Actions and Search Bar */}
            <Paper
              elevation={0}
              sx={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                mb: 3,
                p: 2,
                borderRadius: 2,
                bgcolor: 'rgba(0, 0, 0, 0.02)',
                border: '1px solid rgba(0, 0, 0, 0.05)'
              }}
            >
              {tenantType === 'KEBELE' && (
                <Button
                  variant="contained"
                  color="primary"
                  startIcon={<AddIcon />}
                  onClick={handleRegisterCitizen}
                  sx={{
                    borderRadius: 2,
                    px: 3,
                    py: 1,
                    boxShadow: '0 4px 10px rgba(63, 81, 181, 0.2)',
                    '&:hover': {
                      boxShadow: '0 6px 15px rgba(63, 81, 181, 0.3)'
                    }
                  }}
                >
                  Register New Citizen
                </Button>
              )}

              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <Box sx={{ display: 'flex', alignItems: 'center', bgcolor: 'white', borderRadius: 2, overflow: 'hidden', boxShadow: '0 2px 8px rgba(0,0,0,0.05)', mr: 2 }}>
                  <TextField
                    size="small"
                    placeholder="Search by name, ID number, or phone..."
                    value={searchQuery}
                    onChange={handleSearchChange}
                    sx={{
                      width: 250,
                      position: 'relative',
                      '& .MuiOutlinedInput-root': {
                        borderRadius: 0,
                        '& fieldset': {
                          border: 'none'
                        }
                      }
                    }}
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <SearchIcon color="action" />
                        </InputAdornment>
                      )
                    }}
                  />
                  <Button
                    variant="contained"
                    onClick={handleSearch}
                    disabled={loading}
                    sx={{
                      borderRadius: '0 8px 8px 0',
                      boxShadow: 'none',
                      height: '40px',
                      px: 2
                    }}
                  >
                    Search
                  </Button>
                </Box>
                <Button
                  variant="outlined"
                  startIcon={<FilterListIcon />}
                  sx={{ borderRadius: 2, height: '40px', mr: 1 }}
                >
                  Filter
                </Button>
                <Button
                  variant="contained"
                  color="secondary"
                  onClick={fetchCitizens}
                  disabled={loading}
                  startIcon={<RefreshIcon />}
                  sx={{ borderRadius: 2, height: '40px', mr: 1 }}
                >
                  {loading ? 'Refreshing...' : 'Refresh List'}
                </Button>
                <Button
                  variant="contained"
                  color="info"
                  onClick={handleDirectApiCall}
                  disabled={loading}
                  sx={{ borderRadius: 2, height: '40px' }}
                >
                  Direct API Call
                </Button>
              </Box>
            </Paper>

            {/* Citizens Table */}
            <Paper
              sx={{
                width: '100%',
                overflow: 'hidden',
                borderRadius: 2,
                boxShadow: '0 4px 12px rgba(0,0,0,0.1)',
                '& .MuiTableRow-root:hover': {
                  backgroundColor: 'rgba(63, 81, 181, 0.04)',
                  transition: 'background-color 0.2s ease'
                },
                '& .MuiTableCell-root': {
                  padding: '16px',
                  borderBottom: '1px solid rgba(224, 224, 224, 0.5)'
                },
                '& .MuiTableCell-head': {
                  backgroundColor: 'rgba(63, 81, 181, 0.08)',
                  position: 'sticky',
                  top: 0,
                  zIndex: 11
                }
              }}
            >
              <TableContainer sx={{ maxHeight: 440 }}>
                <Table stickyHeader aria-label="sticky table">
                  <TableHead sx={{ position: 'sticky', top: 0, zIndex: 10, backgroundColor: '#fff' }}>
                    <TableRow sx={{
                      '& th': {
                        fontWeight: 700,
                        bgcolor: 'rgba(63, 81, 181, 0.08)',
                        color: 'rgba(0, 0, 0, 0.7)',
                        fontSize: '0.875rem',
                        textTransform: 'uppercase',
                        letterSpacing: '0.5px',
                        boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
                        borderBottom: '2px solid rgba(63, 81, 181, 0.2)'
                      }
                    }}>
                      <TableCell>Photo</TableCell>
                      <TableCell>ID Number</TableCell>
                      <TableCell>Name</TableCell>
                      <TableCell>Gender</TableCell>
                      <TableCell>Age</TableCell>
                      <TableCell>Phone</TableCell>
                      <TableCell align="right">Actions</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {loading ? (
                      <TableRow>
                        <TableCell colSpan={7} align="center" sx={{ py: 5 }}>
                          <CircularProgress />
                          <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
                            Loading citizens data...
                          </Typography>
                        </TableCell>
                      </TableRow>
                    ) : currentCitizens.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={7} align="center" sx={{ py: 5 }}>
                          <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
                            <PersonIcon sx={{ fontSize: 60, color: 'text.disabled', mb: 2 }} />
                            <Typography variant="h6" color="text.secondary" gutterBottom>
                              No citizens found
                            </Typography>
                            <Typography variant="body2" color="text.secondary" sx={{ mb: 2, maxWidth: 400, textAlign: 'center' }}>
                              {searchQuery
                                ? 'Try adjusting your search criteria'
                                : tenantType === 'KEBELE'
                                  ? 'Start by registering your first citizen'
                                  : 'No citizens have been registered in this tenant yet'
                              }
                            </Typography>
                            {tenantType === 'KEBELE' && (
                              <Button
                                variant="contained"
                                color="primary"
                                startIcon={<AddIcon />}
                                onClick={handleRegisterCitizen}
                                sx={{ mt: 1, borderRadius: 2 }}
                              >
                                Register New Citizen
                              </Button>
                            )}
                          </Box>
                        </TableCell>
                      </TableRow>
                    ) : (
                      currentCitizens.map((citizen) => (
                        <TableRow
                          key={citizen.id}
                          hover
                          sx={{ cursor: 'pointer' }}
                          onClick={() => handleViewCitizen(citizen.id)}
                        >
                          <TableCell onClick={(e) => e.stopPropagation()}>
                            <Avatar
                              src={citizen.photo}
                              alt={`${citizen.first_name} ${citizen.last_name}`}
                              sx={{
                                width: 45,
                                height: 45,
                                boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
                                border: '2px solid white'
                              }}
                            >
                              {citizen.first_name?.[0]}
                            </Avatar>
                          </TableCell>
                          <TableCell>
                            <Typography variant="body2" sx={{ fontWeight: 600, color: 'primary.main' }}>
                              {citizen.id_number || 'Not assigned'}
                            </Typography>
                          </TableCell>
                          <TableCell>
                            <Typography variant="body1" sx={{ fontWeight: 600 }}>
                              {`${citizen.first_name} ${citizen.last_name}`}
                            </Typography>
                            {citizen.middle_name && (
                              <Typography variant="body2" color="text.secondary">
                                {citizen.middle_name}
                              </Typography>
                            )}
                          </TableCell>
                          <TableCell>
                            {citizen.gender === 'M' ? (
                              <Chip
                                icon={<MaleIcon />}
                                label="Male"
                                size="small"
                                color="info"
                                sx={{ fontWeight: 600, borderRadius: '16px' }}
                              />
                            ) : (
                              <Chip
                                icon={<FemaleIcon />}
                                label="Female"
                                size="small"
                                color="secondary"
                                sx={{ fontWeight: 600, borderRadius: '16px' }}
                              />
                            )}
                          </TableCell>
                          <TableCell>
                            <Typography variant="body2" sx={{ fontWeight: 500 }}>
                              {calculateAge(citizen.date_of_birth)}
                            </Typography>
                          </TableCell>
                          <TableCell>
                            {getPhoneNumber(citizen) ? (
                              <Typography variant="body2">
                                {getPhoneNumber(citizen)}
                              </Typography>
                            ) : (
                              <Typography variant="body2" color="text.disabled" sx={{ fontStyle: 'italic' }}>
                                Not available
                              </Typography>
                            )}
                          </TableCell>
                          <TableCell align="right">
                            <Box
                              sx={{
                                display: 'flex',
                                justifyContent: 'flex-end'
                              }}
                            >
                              <IconButton
                                size="small"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleViewCitizen(citizen.id);
                                }}
                                title="View Details"
                                sx={{
                                  bgcolor: 'rgba(0, 0, 0, 0.04)',
                                  mr: 1,
                                  '&:hover': { bgcolor: 'rgba(0, 0, 0, 0.08)' }
                                }}
                              >
                                <VisibilityIcon fontSize="small" />
                              </IconButton>
                              {tenantType === 'KEBELE' && (
                                <IconButton
                                  size="small"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleRegisterIDCard(citizen.id);
                                  }}
                                  title="Register ID Card"
                                  sx={{
                                    bgcolor: 'rgba(0, 0, 0, 0.04)',
                                    '&:hover': { bgcolor: 'rgba(0, 0, 0, 0.08)' }
                                  }}
                                >
                                  <CardMembershipIcon fontSize="small" />
                                </IconButton>
                              )}
                            </Box>
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </TableContainer>
              <TablePagination
                rowsPerPageOptions={[5, 10, 25, 50]}
                component="div"
                count={filteredCitizens.length}
                rowsPerPage={rowsPerPage}
                page={page}
                onPageChange={handleChangePage}
                onRowsPerPageChange={handleChangeRowsPerPage}
              />
            </Paper>
          </>
        )}
      </Container>
    </Box>
  );
};

export default CitizensListNew;