"""
Decorators for API views

This module provides decorators for API views.
"""

from functools import wraps
from django.views.decorators.csrf import csrf_exempt
from rest_framework.decorators import api_view as drf_api_view


def csrf_exempt_api_view(http_methods):
    """
    Decorator that combines @csrf_exempt and @api_view.

    This decorator should be used for all API views that need to be exempt from CSRF protection.

    Args:
        http_methods: A list of HTTP methods that the view accepts.

    Returns:
        A decorator that combines @csrf_exempt and @api_view.
    """
    def decorator(view_func):
        @wraps(view_func)
        def wrapped_view(*args, **kwargs):
            return view_func(*args, **kwargs)

        # Apply @csrf_exempt first, then @api_view
        return csrf_exempt(drf_api_view(http_methods)(wrapped_view))

    return decorator
