from django.db import migrations

def create_initial_document_types(apps, schema_editor):
    """
    Create initial data for DocumentType model.
    """
    DocumentType = apps.get_model('common', 'DocumentType')
    
    # Create document types
    document_types = [
        {'name': 'National ID Card', 'description': 'Official national identification card'},
        {'name': 'Passport', 'description': 'International travel document'},
        {'name': 'Birth Certificate', 'description': 'Official document certifying birth details'},
        {'name': 'Marriage Certificate', 'description': 'Official document certifying marriage'},
        {'name': 'Driving License', 'description': 'Official document authorizing holder to operate motor vehicles'},
        {'name': 'Residence Permit', 'description': 'Document authorizing residence in a specific area'},
        {'name': 'Work Permit', 'description': 'Document authorizing employment'},
        {'name': 'Voter ID', 'description': 'Document for voting in elections'},
        {'name': 'Tax ID', 'description': 'Document for tax identification'},
        {'name': 'Social Security Card', 'description': 'Document for social security benefits'},
        {'name': 'Health Insurance Card', 'description': 'Document for health insurance coverage'},
        {'name': 'Student ID', 'description': 'Document identifying student status'},
        {'name': 'Military ID', 'description': 'Document identifying military personnel'},
        {'name': 'Refugee ID', 'description': 'Document identifying refugee status'},
        {'name': 'Other', 'description': 'Other types of identification documents'}
    ]
    
    for doc_type in document_types:
        DocumentType.objects.create(
            name=doc_type['name'],
            description=doc_type['description']
        )

class Migration(migrations.Migration):
    dependencies = [
        ('common', '0007_documenttype'),
    ]

    operations = [
        migrations.RunPython(create_initial_document_types),
    ]
