from django.contrib import admin
from .models_biometric import Biometric, Photo

@admin.register(Biometric)
class BiometricAdmin(admin.ModelAdmin):
    list_display = ('citizen', 'has_fingerprints', 'has_iris_scans', 'created_at')
    list_filter = ('created_at',)
    search_fields = ('citizen__first_name', 'citizen__last_name', 'citizen__id_number')
    readonly_fields = ('created_at', 'updated_at')
    
    fieldsets = (
        ('Citizen Information', {
            'fields': ('citizen',)
        }),
        ('Fingerprints', {
            'fields': ('left_hand_fingerprint', 'right_hand_fingerprint', 'left_thumb_fingerprint', 'right_thumb_fingerprint')
        }),
        ('Iris Scans', {
            'fields': ('left_eye_iris_scan', 'right_eye_iris_scan')
        }),
        ('System Information', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def has_fingerprints(self, obj):
        """Check if the citizen has any fingerprints recorded."""
        return bool(obj.left_hand_fingerprint or obj.right_hand_fingerprint or 
                   obj.left_thumb_fingerprint or obj.right_thumb_fingerprint)
    has_fingerprints.boolean = True
    has_fingerprints.short_description = 'Has Fingerprints'
    
    def has_iris_scans(self, obj):
        """Check if the citizen has any iris scans recorded."""
        return bool(obj.left_eye_iris_scan or obj.right_eye_iris_scan)
    has_iris_scans.boolean = True
    has_iris_scans.short_description = 'Has Iris Scans'
    
    def save_model(self, request, obj, form, change):
        if not change:  # Only set created_by when creating a new object
            if hasattr(obj, 'created_by') and hasattr(request, 'user'):
                obj.created_by = request.user
        super().save_model(request, obj, form, change)


@admin.register(Photo)
class PhotoAdmin(admin.ModelAdmin):
    list_display = ('citizen', 'photo', 'upload_date', 'created_at')
    list_filter = ('upload_date', 'created_at')
    search_fields = ('citizen__first_name', 'citizen__last_name', 'citizen__id_number')
    readonly_fields = ('upload_date', 'created_at', 'updated_at')
    
    fieldsets = (
        ('Citizen Information', {
            'fields': ('citizen',)
        }),
        ('Photo', {
            'fields': ('photo', 'upload_date')
        }),
        ('System Information', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def save_model(self, request, obj, form, change):
        if not change:  # Only set created_by when creating a new object
            if hasattr(obj, 'created_by') and hasattr(request, 'user'):
                obj.created_by = request.user
        super().save_model(request, obj, form, change)
