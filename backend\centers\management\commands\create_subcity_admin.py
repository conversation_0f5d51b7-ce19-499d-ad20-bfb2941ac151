from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from django_tenants.utils import tenant_context
from centers.models import Client, Center

User = get_user_model()

class Command(BaseCommand):
    help = 'Create a subcity admin user for testing'

    def add_arguments(self, parser):
        parser.add_argument('--schema', type=str, help='Schema name of the subcity tenant')
        parser.add_argument('--email', type=str, help='Email for the subcity admin')
        parser.add_argument('--password', type=str, help='Password for the subcity admin')

    def handle(self, *args, **options):
        schema_name = options.get('schema')
        email = options.get('email', '<EMAIL>')
        password = options.get('password', 'password123')

        # If no schema is provided, list available subcity tenants
        if not schema_name:
            subcity_tenants = Client.objects.filter(schema_type='SUBCITY')
            if not subcity_tenants.exists():
                self.stdout.write(self.style.ERROR('No subcity tenants found'))
                return

            self.stdout.write(self.style.SUCCESS('Available subcity tenants:'))
            for tenant in subcity_tenants:
                self.stdout.write(f'- {tenant.name} (Schema: {tenant.schema_name}, Parent: {tenant.parent.name if tenant.parent else None})')

            # Use the first subcity tenant
            schema_name = subcity_tenants.first().schema_name
            self.stdout.write(self.style.SUCCESS(f'Using subcity tenant: {schema_name}'))

        try:
            # Get the tenant
            tenant = Client.objects.get(schema_name=schema_name)

            # Check if the tenant is a subcity
            if tenant.schema_type != 'SUBCITY':
                self.stdout.write(self.style.ERROR(f'Tenant {schema_name} is not a subcity tenant'))
                return

            # Switch to the tenant context
            with tenant_context(tenant):
                # Check if user already exists
                if User.objects.filter(email=email).exists():
                    self.stdout.write(self.style.WARNING(f'User with email {email} already exists in tenant {schema_name}'))
                    return

                # Get the first center in the tenant
                center = Center.objects.first()
                if not center:
                    self.stdout.write(self.style.ERROR(f'No center found in tenant {schema_name}'))
                    return

                # Create the subcity admin user
                user = User.objects.create_user(
                    email=email,
                    password=password,
                    first_name='Subcity',
                    last_name='Admin',
                    role='SUBCITY_ADMIN',
                    center=center,
                    is_active=True
                )

                # Print the created user details
                self.stdout.write(self.style.SUCCESS(f'User created: {user.email}, ID: {user.id}'))

                self.stdout.write(self.style.SUCCESS(f'Successfully created subcity admin {email} in tenant {tenant.name}'))
                self.stdout.write(self.style.SUCCESS(f'Login credentials: Email: {email}, Password: {password}'))

        except Client.DoesNotExist:
            self.stdout.write(self.style.ERROR(f'Tenant with schema {schema_name} does not exist'))
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'Error creating subcity admin: {str(e)}'))
