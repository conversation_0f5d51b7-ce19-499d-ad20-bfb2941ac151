from rest_framework.decorators import api_view, permission_classes, authentication_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework import status
from django_tenants.utils import tenant_context
from django.contrib.auth import get_user_model
from django.shortcuts import get_object_or_404
from .models import Client, Kebele
from accounts.authentication import MultiTenantTokenAuthentication
import logging

logger = logging.getLogger(__name__)

@api_view(['GET'])
@authentication_classes([MultiTenantTokenAuthentication])
@permission_classes([IsAuthenticated])
def get_child_kebele_idcards(request):
    """
    Get ID cards from all child kebele tenants for the current subcity tenant.
    Only returns ID cards with kebele_approval_status='APPROVED'.
    """
    print("\n\n===== GET CHILD KEBELE ID CARDS API CALLED =====\n")
    print(f"Request method: {request.method}")
    print(f"Request headers: {request.headers}")

    try:
        # Check if X-Schema-Name header is provided
        schema_header = request.headers.get('X-Schema-Name')
        if schema_header and schema_header.startswith('subcity_'):
            subcity_schema = schema_header
            print(f"Using schema from X-Schema-Name header: {subcity_schema}")
        else:
            # No valid schema provided
            return Response(
                {"error": "No valid subcity schema provided in X-Schema-Name header"},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Get the subcity tenant
        try:
            subcity_tenant = Client.objects.get(schema_name=subcity_schema)
            print(f"Found subcity tenant: {subcity_tenant.name}")
        except Client.DoesNotExist:
            print(f"Subcity tenant {subcity_schema} not found")
            return Response(
                {"error": f"Subcity tenant {subcity_schema} not found"},
                status=status.HTTP_404_NOT_FOUND
            )

        # Get all kebele tenants that have this subcity as parent
        child_tenants = Client.objects.filter(parent=subcity_tenant, schema_type='KEBELE')
        print(f"Found {len(child_tenants)} child kebele tenants")

        # Initialize an empty list to store all ID cards
        all_idcards = []

        # For each child kebele tenant, get ID cards with kebele_approval_status='APPROVED'
        for child_tenant in child_tenants:
            print(f"Fetching ID cards from kebele tenant: {child_tenant.name}, Schema: {child_tenant.schema_name}")

            try:
                # Switch to the child tenant context
                with tenant_context(child_tenant):
                    from idcards.models import IDCard

                    # Get ID cards with kebele_approval_status='APPROVED'
                    try:
                        idcards = IDCard.objects.filter(kebele_approval_status='APPROVED')
                        print(f"Found {len(idcards)} ID cards with kebele_approval_status='APPROVED' in {child_tenant.name}")

                        # Serialize the ID cards
                        for idcard in idcards:
                            try:
                                # Get the citizen data
                                citizen_data = {
                                    'id': idcard.citizen.id,
                                    'first_name': idcard.citizen.first_name,
                                    'last_name': idcard.citizen.last_name,
                                    'id_number': idcard.citizen.id_number if hasattr(idcard.citizen, 'id_number') else None
                                }

                                # Create a dictionary with the ID card data
                                idcard_data = {
                                    'id': idcard.id,
                                    'card_number': idcard.card_number,
                                    'issue_date': idcard.issue_date,
                                    'expiry_date': idcard.expiry_date,
                                    'status': idcard.status,
                                    'kebele_approval_status': idcard.kebele_approval_status,
                                    'kebele_pattern': idcard.kebele_pattern is not None,
                                    'subcity_pattern': idcard.subcity_pattern is not None if hasattr(idcard, 'subcity_pattern') else None,
                                    'citizen': citizen_data,
                                    'source_schema': child_tenant.schema_name
                                }

                                all_idcards.append(idcard_data)
                            except Exception as e:
                                print(f"Error processing ID card {idcard.id} from {child_tenant.name}: {str(e)}")
                    except Exception as e:
                        print(f"Error querying ID cards from {child_tenant.name}: {str(e)}")
            except Exception as e:
                print(f"Error switching to tenant context for {child_tenant.name}: {str(e)}")

        print(f"Total ID cards found: {len(all_idcards)}")
        return Response(all_idcards)
    except Exception as e:
        logger.error(f"Error getting child kebele ID cards: {str(e)}")
        return Response(
            {"error": f"An error occurred: {str(e)}"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['POST'])
@authentication_classes([MultiTenantTokenAuthentication])
@permission_classes([IsAuthenticated])
def subcity_approve_idcard(request, schema_name, idcard_id):
    """
    Approve an ID card in a specific child kebele tenant.
    """
    try:
        # For debugging, log the user information
        user = request.user
        logger.info(f"User: {user.email if hasattr(user, 'email') else 'No email'}, ID: {user.id if hasattr(user, 'id') else 'No ID'}")

        # Get the tenant from the request context
        if hasattr(request, 'tenant'):
            # If the request has a tenant attribute, use it
            current_tenant = request.tenant
            logger.info(f"Using tenant from request context: {current_tenant.name}, Schema: {current_tenant.schema_name}")

            # Check if this is a subcity tenant
            if current_tenant.schema_type != 'SUBCITY':
                # If this is not a subcity tenant, try to find its parent subcity
                if current_tenant.schema_type == 'KEBELE' and current_tenant.parent and current_tenant.parent.schema_type == 'SUBCITY':
                    current_tenant = current_tenant.parent
                    logger.info(f"Using parent subcity tenant: {current_tenant.name}, Schema: {current_tenant.schema_name}")
                else:
                    # If we can't find a parent subcity, use any subcity tenant
                    subcity_tenants = Client.objects.filter(schema_type='SUBCITY')
                    if subcity_tenants.exists():
                        current_tenant = subcity_tenants.first()
                        logger.info(f"Using fallback subcity tenant: {current_tenant.name}, Schema: {current_tenant.schema_name}")
                    else:
                        return Response(
                            {"error": "No subcity tenants found"},
                            status=status.HTTP_400_BAD_REQUEST
                        )
        else:
            # If the request doesn't have a tenant attribute, use any subcity tenant
            subcity_tenants = Client.objects.filter(schema_type='SUBCITY')
            if not subcity_tenants.exists():
                return Response(
                    {"error": "No subcity tenants found"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            current_tenant = subcity_tenants.first()
            logger.info(f"Using fallback subcity tenant: {current_tenant.name}, Schema: {current_tenant.schema_name}")

        # Get the child tenant (kebele)
        try:
            child_tenant = Client.objects.get(schema_name=schema_name)

            # Verify this is a kebele tenant
            if child_tenant.schema_type != 'KEBELE':
                return Response(
                    {"error": f"Tenant {schema_name} is not a kebele tenant"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Verify this kebele is a child of the current subcity
            if child_tenant.parent != current_tenant and child_tenant.parent is not None:
                return Response(
                    {"error": f"Kebele tenant {schema_name} is not a child of this subcity"},
                    status=status.HTTP_403_FORBIDDEN
                )

        except Client.DoesNotExist:
            return Response(
                {"error": f"Tenant with schema {schema_name} does not exist"},
                status=status.HTTP_404_NOT_FOUND
            )

        # Switch to the child tenant context (the specific kebele)
        with tenant_context(child_tenant):
            from idcards.models import IDCard

            # Get the ID card to approve
            try:
                idcard = get_object_or_404(IDCard, id=idcard_id)
            except Exception as e:
                return Response(
                    {"error": f"ID card with ID {idcard_id} not found in tenant {child_tenant.name}: {str(e)}"},
                    status=status.HTTP_404_NOT_FOUND
                )

            # Check if the ID card is already approved
            if idcard.status == 'APPROVED':
                return Response(
                    {"error": f"ID card {idcard_id} is already approved"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Check if the ID card has kebele approval
            if idcard.kebele_approval_status != 'APPROVED':
                return Response(
                    {"error": f"ID card {idcard_id} does not have kebele approval"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Apply subcity pattern if it doesn't exist
            if not hasattr(idcard, 'subcity_pattern') or idcard.subcity_pattern is None:
                try:
                    # Import the pattern application function
                    from idcards.security_patterns import apply_subcity_pattern

                    # Apply the pattern
                    apply_subcity_pattern(idcard)

                    # Refresh the ID card to get the updated pattern
                    idcard.refresh_from_db()
                except Exception as e:
                    logger.error(f"Error applying subcity pattern to ID card {idcard_id}: {str(e)}")
                    # Continue with approval even if pattern application fails

            # Update the ID card status to APPROVED
            idcard.status = 'APPROVED'
            idcard.approved_by = request.user
            idcard.save()

            # Return success response
            return Response({
                'id': idcard.id,
                'card_number': idcard.card_number,
                'status': idcard.status,
                'message': f"ID card {idcard.card_number} approved successfully"
            }, status=status.HTTP_200_OK)
    except Exception as e:
        logger.error(f"Error approving ID card: {str(e)}")
        return Response(
            {"error": f"An error occurred: {str(e)}"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )
