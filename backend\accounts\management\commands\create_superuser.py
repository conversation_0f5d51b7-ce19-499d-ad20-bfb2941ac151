from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from django.db import IntegrityError

User = get_user_model()

class Command(BaseCommand):
    help = 'Create a superuser with the specified email and password'

    def add_arguments(self, parser):
        parser.add_argument('--email', required=True, help='Email for the superuser')
        parser.add_argument('--password', required=True, help='Password for the superuser')
        parser.add_argument('--first_name', default='Super', help='First name for the superuser')
        parser.add_argument('--last_name', default='Admin', help='Last name for the superuser')

    def handle(self, *args, **options):
        email = options['email']
        password = options['password']
        first_name = options['first_name']
        last_name = options['last_name']
        
        try:
            user = User.objects.create_superuser(
                email=email,
                password=password,
                first_name=first_name,
                last_name=last_name
            )
            self.stdout.write(self.style.SUCCESS(f'Successfully created superuser with email: {email}'))
        except IntegrityError:
            self.stdout.write(self.style.WARNING(f'Superuser with email {email} already exists'))
            user = User.objects.get(email=email)
            user.set_password(password)
            user.is_staff = True
            user.is_superuser = True
            user.role = 'SUPER_ADMIN'
            user.save()
            self.stdout.write(self.style.SUCCESS(f'Updated password for existing superuser: {email}'))
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'Failed to create superuser: {str(e)}'))
