# Generated by Django 5.1.7 on 2025-04-24 13:08

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('idcards', '0005_idcard_kebele_pattern_idcard_pattern_seed_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='idcard',
            name='kebele_approval_notes',
            field=models.TextField(blank=True, help_text='Notes from the kebele leader regarding approval or rejection', null=True),
        ),
        migrations.AddField(
            model_name='idcard',
            name='kebele_approval_status',
            field=models.CharField(choices=[('PENDING', 'Pending'), ('APPROVED', 'Approved'), ('REJECTED', 'Rejected')], default='PENDING', help_text='Approval status by the kebele leader', max_length=20),
        ),
        migrations.AddField(
            model_name='idcard',
            name='kebele_approved_at',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='idcard',
            name='kebele_approved_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='kebele_approved_cards', to=settings.AUTH_USER_MODEL),
        ),
    ]
