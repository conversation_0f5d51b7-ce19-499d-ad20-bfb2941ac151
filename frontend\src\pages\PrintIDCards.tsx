import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Container,
  Typography,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Chip,
  IconButton,
  Button,
  Card,
  CardContent,
  Grid,
  TextField,
  InputAdornment,
  CircularProgress,
  Checkbox,
  Tooltip,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  Snackbar,
  Alert
} from '@mui/material';
import {
  Print as PrintIcon,
  Search as SearchIcon,
  Visibility as VisibilityIcon,
  FilterList as FilterListIcon,
  CheckCircle as CheckCircleIcon,
  ThumbUp as ThumbUpIcon,
  ThumbDown as ThumbDownIcon,
  Send as SendIcon
} from '@mui/icons-material';
import PageBanner from '../components/PageBanner';
import { format } from 'date-fns';

const PrintIDCards: React.FC = () => {
  const navigate = useNavigate();

  // State for ID cards data
  const [idCards, setIdCards] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  // Helper function to fetch cards from a list of kebeles
  const fetchCardsFromKebeles = async (kebeles: any[]) => {
    let allCards: any[] = [];

    // Try to fetch cards from each kebele
    for (const kebele of kebeles) {
      try {
        console.log(`Fetching ID cards with status PENDING_SUBCITY from kebele: ${kebele.name} (${kebele.schema_name})`);

        // Try direct tenant endpoint first (most reliable)
        try {
          console.log(`Trying direct tenant endpoint for ${kebele.name}`);
          const directResponse = await fetch(`/api/tenant/${kebele.schema_name}/idcards/?status=PENDING_SUBCITY`, {
            method: 'GET',
            headers: {
              'Authorization': `Token ${hardcodedToken}`,
              'Content-Type': 'application/json'
            }
          });

          if (directResponse.ok) {
            const directData = await directResponse.json();
            console.log(`Response data from ${kebele.name} using direct tenant endpoint:`, directData);

            // Process the response
            let kebeleCards = [];
            if (Array.isArray(directData)) {
              kebeleCards = directData;
            } else if (directData.results && Array.isArray(directData.results)) {
              kebeleCards = directData.results;
            }

            // Add kebele name to each card for display
            kebeleCards = kebeleCards.map((card: any) => ({
              ...card,
              kebele_name: kebele.name,
              kebele_schema: kebele.schema_name
            }));

            allCards = [...allCards, ...kebeleCards];
            console.log(`Found ${kebeleCards.length} cards with status PENDING_SUBCITY in ${kebele.name} using direct tenant endpoint`);

            // Continue to the next kebele if we found cards
            if (kebeleCards.length > 0) {
              continue;
            }
          }
        } catch (directError) {
          console.error(`Error with direct tenant endpoint for ${kebele.name}:`, directError);
        }

        // Try with modified schema name for direct tenant endpoint
        try {
          const modifiedSchemaName = kebele.schema_name.replace(/\s+/g, '_');
          if (modifiedSchemaName !== kebele.schema_name) {
            console.log(`Trying with modified schema name for direct tenant endpoint: ${modifiedSchemaName}`);

            const modifiedDirectResponse = await fetch(`http://localhost:8000/api/tenant/${modifiedSchemaName}/idcards/?status=PENDING_SUBCITY`, {
              method: 'GET',
              headers: {
                'Authorization': `Token ${hardcodedToken}`,
                'Content-Type': 'application/json'
              }
            });

            if (modifiedDirectResponse.ok) {
              const modifiedDirectData = await modifiedDirectResponse.json();
              console.log(`Response data from ${kebele.name} using modified direct tenant endpoint:`, modifiedDirectData);

              // Process the response
              let kebeleCards = [];
              if (Array.isArray(modifiedDirectData)) {
                kebeleCards = modifiedDirectData;
              } else if (modifiedDirectData.results && Array.isArray(modifiedDirectData.results)) {
                kebeleCards = modifiedDirectData.results;
              }

              // Add kebele name to each card for display
              kebeleCards = kebeleCards.map((card: any) => ({
                ...card,
                kebele_name: kebele.name,
                kebele_schema: modifiedSchemaName
              }));

              allCards = [...allCards, ...kebeleCards];
              console.log(`Found ${kebeleCards.length} cards with status PENDING_SUBCITY in ${kebele.name} using modified direct tenant endpoint`);

              // Continue to the next kebele if we found cards
              if (kebeleCards.length > 0) {
                continue;
              }
            }
          }
        } catch (modifiedDirectError) {
          console.error(`Error with modified direct tenant endpoint for ${kebele.name}:`, modifiedDirectError);
        }

        // Try with X-Schema-Name header
        try {
          console.log(`Trying with X-Schema-Name header for ${kebele.name}`);

          const schemaResponse = await fetch(`http://localhost:8000/api/idcards/?status=PENDING_SUBCITY`, {
            method: 'GET',
            headers: {
              'Authorization': `Token ${hardcodedToken}`,
              'Content-Type': 'application/json',
              'X-Schema-Name': kebele.schema_name
            }
          });

          if (schemaResponse.ok) {
            const schemaData = await schemaResponse.json();
            console.log(`Response data from ${kebele.name} using X-Schema-Name header:`, schemaData);

            // Process the response
            let kebeleCards = [];
            if (Array.isArray(schemaData)) {
              kebeleCards = schemaData;
            } else if (schemaData.results && Array.isArray(schemaData.results)) {
              kebeleCards = schemaData.results;
            }

            // Add kebele name to each card for display
            kebeleCards = kebeleCards.map((card: any) => ({
              ...card,
              kebele_name: kebele.name,
              kebele_schema: kebele.schema_name
            }));

            allCards = [...allCards, ...kebeleCards];
            console.log(`Found ${kebeleCards.length} cards with status PENDING_SUBCITY in ${kebele.name} using X-Schema-Name header`);

            // Continue to the next kebele if we found cards
            if (kebeleCards.length > 0) {
              continue;
            }
          }
        } catch (schemaError) {
          console.error(`Error with X-Schema-Name header for ${kebele.name}:`, schemaError);
        }

        // Try with modified schema name for X-Schema-Name header
        try {
          const modifiedSchemaName = kebele.schema_name.replace(/\s+/g, '_');
          if (modifiedSchemaName !== kebele.schema_name) {
            console.log(`Trying with modified schema name for X-Schema-Name header: ${modifiedSchemaName}`);

            const modifiedResponse = await fetch(`http://localhost:8000/api/idcards/?status=PENDING_SUBCITY`, {
              method: 'GET',
              headers: {
                'Authorization': `Token ${hardcodedToken}`,
                'Content-Type': 'application/json',
                'X-Schema-Name': modifiedSchemaName
              }
            });

            if (modifiedResponse.ok) {
              const modifiedData = await modifiedResponse.json();
              console.log(`Response data from ${kebele.name} with modified schema name for X-Schema-Name header:`, modifiedData);

              // Process the response
              let kebeleCards = [];
              if (Array.isArray(modifiedData)) {
                kebeleCards = modifiedData;
              } else if (modifiedData.results && Array.isArray(modifiedData.results)) {
                kebeleCards = modifiedData.results;
              }

              // Add kebele name to each card for display
              kebeleCards = kebeleCards.map((card: any) => ({
                ...card,
                kebele_name: kebele.name,
                kebele_schema: modifiedSchemaName
              }));

              allCards = [...allCards, ...kebeleCards];
              console.log(`Found ${kebeleCards.length} cards with status PENDING_SUBCITY in ${kebele.name} using modified schema name for X-Schema-Name header`);
            }
          }
        } catch (modifiedSchemaError) {
          console.error(`Error with modified schema name for X-Schema-Name header for ${kebele.name}:`, modifiedSchemaError);
        }
      } catch (error) {
        console.error(`Error fetching cards from kebele ${kebele.name}:`, error);
      }
    }

    console.log(`Total cards found with status PENDING_SUBCITY across all kebeles: ${allCards.length}`);
    return allCards;
  };

  // Helper function to calculate statistics from ID cards
  const calculateStatsFromCards = (cards: any[]) => {
    if (!Array.isArray(cards)) {
      console.log('Cannot calculate statistics: cards is not an array');
      return;
    }

    console.log(`Calculating statistics from ${cards.length} cards`);

    const total = cards.length;
    const draft = cards.filter(card => card.status === 'DRAFT').length;
    const pending = cards.filter(card => card.status === 'PENDING').length;
    const approved = cards.filter(card => card.status === 'APPROVED').length;
    const printed = cards.filter(card => card.status === 'PRINTED').length;
    const issued = cards.filter(card => card.status === 'ISSUED').length;
    const expired = cards.filter(card => card.status === 'EXPIRED').length;
    const revoked = cards.filter(card => card.status === 'REVOKED').length;

    // Also calculate kebele approval statistics if available
    const kebele_pending = cards.filter(card => card.kebele_approval_status === 'PENDING').length;
    const kebele_approved = cards.filter(card => card.kebele_approval_status === 'APPROVED').length;
    const kebele_rejected = cards.filter(card => card.kebele_approval_status === 'REJECTED').length;

    const statsData = {
      total,
      draft,
      pending,
      approved,
      printed,
      issued,
      expired,
      revoked,
      kebele_pending,
      kebele_approved,
      kebele_rejected
    };

    console.log('Calculated statistics:', statsData);
    setStats(statsData);
  };

  // State for statistics
  const [stats, setStats] = useState<{
    total: number;
    draft: number;
    pending: number;
    approved: number;
    printed: number;
    issued: number;
    expired: number;
    revoked: number;
    kebele_pending?: number;
    kebele_approved?: number;
    kebele_rejected?: number;
  }>({
    total: 0,
    draft: 0,
    pending: 0,
    approved: 0,
    printed: 0,
    issued: 0,
    expired: 0,
    revoked: 0,
    kebele_pending: 0,
    kebele_approved: 0,
    kebele_rejected: 0
  });
  const [loadingStats, setLoadingStats] = useState(true);

  // State for pagination
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);

  // State for search
  const [searchQuery, setSearchQuery] = useState('');

  // State for selected cards (for bulk printing)
  const [selectedCards, setSelectedCards] = useState<string[]>([]);
  const [selectAll, setSelectAll] = useState(false);

  // State for print confirmation dialog
  const [printDialogOpen, setPrintDialogOpen] = useState(false);
  const [cardToPrint, setCardToPrint] = useState<string | null>(null);
  const [isPrinting, setIsPrinting] = useState(false);

  // State for approval dialog
  const [approvalDialogOpen, setApprovalDialogOpen] = useState(false);
  const [cardToApprove, setCardToApprove] = useState<string | null>(null);
  const [isApproving, setIsApproving] = useState(false);
  const [approvalNotes, setApprovalNotes] = useState('');

  // State for snackbar
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [snackbarSeverity, setSnackbarSeverity] = useState<'success' | 'error'>('success');

  // State for refresh
  const [refreshing, setRefreshing] = useState(false);

  // Get the current tenant from localStorage
  const tenantString = localStorage.getItem('tenant');
  const tenant = tenantString ? JSON.parse(tenantString) : null;
  const token = localStorage.getItem('token');

  // Use a hardcoded token for development/testing
  const hardcodedToken = '01aa7be65fbda335a0b29edd56c967ad6112fa6b'; // Admin token

  // Get the schema name from the tenant
  // For development/testing, use the known subcity schema name
  const schemaName = 'subcity_zoble';

  // Redirect if not a subcity tenant
  useEffect(() => {
    if (tenant && tenant.type !== 'SUBCITY') {
      navigate('/dashboard');
    }
  }, [tenant, navigate]);

  // Fetch ID cards and statistics from API
  useEffect(() => {
    const fetchData = async () => {
      if (!token) {
        setError('Authentication token not found. Please log in again.');
        setLoading(false);
        setLoadingStats(false);
        return;
      }

      // Log tenant information to understand the hierarchy
      console.log('Current tenant:', tenant);
      if (tenant?.parent) {
        console.log('Parent tenant:', tenant.parent);
      }

      // Variable to store the fetched cards data, accessible to both try blocks
      let fetchedCards: any[] = [];

      // Fetch ID cards from all kebeles/centers that belong to this subcity
      try {
        // Get the current tenant ID from localStorage
        const tenantId = tenant?.id;

        if (!tenantId) {
          throw new Error('Tenant information not found');
        }

        // Log tenant type to understand the hierarchy
        console.log(`Tenant type: ${tenant?.schema_type}, Tenant name: ${tenant?.name}, Tenant schema: ${tenant?.schema_name}`);
        console.log('Fetching approved ID cards for tenant:', tenant?.name);

        // Let's try to fetch the available tenants first to understand the hierarchy
        console.log('Fetching available tenants to understand the hierarchy');
        try {
          const tenantsResponse = await fetch(`http://localhost:8000/api/available-tenants/`, {
            method: 'GET',
            headers: {
              'Authorization': `Token ${hardcodedToken}`,
              'Content-Type': 'application/json'
            }
          });

          if (tenantsResponse.ok) {
            const tenantsData = await tenantsResponse.json();
            console.log('Available tenants:', tenantsData);

            // Find child kebeles of the current subcity
            if (tenant?.schema_type === 'SUBCITY') {
              console.log('Current tenant ID:', tenantId);
              console.log('Current tenant schema_name:', tenant?.schema_name);

              // Log all tenants to understand the structure
              console.log('All tenants:', tenantsData.map((t: any) => ({
                id: t.id,
                name: t.name,
                schema_name: t.schema_name,
                schema_type: t.schema_type,
                parent_id: t.parent ? t.parent.id : null,
                parent_name: t.parent ? t.parent.name : null
              })));

              const childKebeles = tenantsData.filter((t: any) =>
                t.schema_type === 'KEBELE' &&
                t.parent &&
                t.parent.id === tenantId
              );
              console.log('Child kebeles of this subcity:', childKebeles);

              // If no child kebeles found, try a different approach
              if (childKebeles.length === 0) {
                console.log('No child kebeles found with parent.id === tenantId, trying with parent schema_name');

                const childKebelesBySchemaName = tenantsData.filter((t: any) =>
                  t.schema_type === 'KEBELE' &&
                  t.parent &&
                  t.parent.schema_name === tenant?.schema_name
                );

                console.log('Child kebeles by schema_name:', childKebelesBySchemaName);

                if (childKebelesBySchemaName.length > 0) {
                  console.log('Using child kebeles found by schema_name');
                  // Use these kebeles instead
                  const allCards = await fetchCardsFromKebeles(childKebelesBySchemaName);
                  if (allCards.length > 0) {
                    console.log(`Total approved cards found across all kebeles: ${allCards.length}`);
                    setIdCards(allCards);
                    fetchedCards = allCards;
                    setError('');
                    setLoading(false);

                    // Calculate statistics from the fetched cards
                    calculateStatsFromCards(allCards);
                    setLoadingStats(false);

                    return; // Exit early if we found cards
                  }
                }

                // If still no child kebeles, try a more general approach
                console.log('Trying to find kebeles with "kebele" in their schema_name');
                const kebelesByName = tenantsData.filter((t: any) =>
                  t.schema_type === 'KEBELE' ||
                  (t.schema_name && t.schema_name.toLowerCase().includes('kebele'))
                );

                console.log('Kebeles found by name:', kebelesByName);

                if (kebelesByName.length > 0) {
                  console.log('Using kebeles found by name');
                  // Use these kebeles instead
                  const allCards = await fetchCardsFromKebeles(kebelesByName);
                  if (allCards.length > 0) {
                    console.log(`Total approved cards found across all kebeles: ${allCards.length}`);
                    setIdCards(allCards);
                    fetchedCards = allCards;
                    setError('');
                    setLoading(false);

                    // Calculate statistics from the fetched cards
                    calculateStatsFromCards(allCards);
                    setLoadingStats(false);

                    return; // Exit early if we found cards
                  }
                }
              }

              // Try to fetch ID cards from each child kebele directly
              if (childKebeles.length > 0) {
                console.log('Attempting to fetch ID cards from each child kebele directly');

                const allCards = await fetchCardsFromKebeles(childKebeles);

                if (allCards.length > 0) {
                  console.log(`Total approved cards found across all kebeles: ${allCards.length}`);
                  setIdCards(allCards);
                  fetchedCards = allCards;
                  setError('');
                  setLoading(false);

                  // Calculate statistics from the fetched cards
                  calculateStatsFromCards(allCards);
                  setLoadingStats(false);

                  return; // Exit early if we found cards
                } else {
                  console.log('No approved cards found in any child kebele');
                }
              }
            }
          } else {
            console.error('Failed to fetch available tenants');
          }
        } catch (error) {
          console.error('Error fetching available tenants:', error);
        }

        // Fetch ID cards directly from child kebeles
        console.log(`Trying to fetch approved ID cards for subcity: ${tenant?.name} (${tenant?.schema_name})`);

        // Since we know there are about 9 APPROVED ID cards in kebele 14, let's try to fetch them directly
        console.log('Trying to fetch approved ID cards directly from kebele 14');

        // Define known kebeles under Zoble subcity
        const knownKebeles = [
          { id: 67, name: 'Kebele 14', schema_name: 'kebele 14', schema_type: 'KEBELE', parent: 'subcity_zoble' },
          { id: 68, name: 'Kebele 15', schema_name: 'kebele 15', schema_type: 'KEBELE', parent: 'subcity_zoble' },
          { id: 70, name: 'Kebele 16', schema_name: 'kebele16', schema_type: 'KEBELE', parent: 'subcity_zoble' }
        ];

        // Continue with API calls to fetch real data from the database
        console.log('Attempting to fetch real ID cards from child kebeles');

        // We don't need to use tenantsData here, as we're using known kebeles

        // If no child kebeles found or no cards found, try the tenant-specific endpoint as a fallback
        console.log(`Trying tenant-specific endpoint for: ${tenant?.schema_name}`);
        const cardsResponse = await fetch(`/api/tenant/${tenant?.schema_name}/idcards/?status=PENDING_SUBCITY`, {
          method: 'GET',
          headers: {
            'Authorization': `Token ${hardcodedToken}`,
            'Content-Type': 'application/json'
          }
        });

        if (!cardsResponse.ok) {
          // If the tenant-specific endpoint fails, try the general endpoint
          console.log('Tenant-specific endpoint failed, trying general endpoint');
          // Filter by kebele_approval_status='APPROVED' instead of status='APPROVED'
          const generalCardsResponse = await fetch(`http://localhost:8000/api/idcards/?status=PENDING_SUBCITY`, {
            method: 'GET',
            headers: {
              'Authorization': `Token ${hardcodedToken}`,
              'Content-Type': 'application/json'
            }
          });

          if (!generalCardsResponse.ok) {
            throw new Error('Failed to fetch ID cards from both endpoints');
          }

          const cardsData = await generalCardsResponse.json();

          // Process the response
          let cards = [];
          if (Array.isArray(cardsData)) {
            cards = cardsData;
          } else if (cardsData.results && Array.isArray(cardsData.results)) {
            cards = cardsData.results;
          }

          // Add default kebele name since we don't have the actual kebele info
          cards = cards.map((card: any) => ({
            ...card,
            kebele_name: card.kebele_name || 'Unknown Kebele',
            kebele_schema: tenant?.schema_name // Use current tenant schema as fallback
          }));

          // Log that no cards were found
          if (cards.length === 0) {
            console.log('No approved ID cards found in the database');
          }

          setIdCards(cards);
          fetchedCards = cards;
          console.log(`Found ${cards.length} approved cards using general endpoint`);
        } else {
          // Process the tenant-specific response
          const cardsData = await cardsResponse.json();

          // Process the response
          let cards = [];
          if (Array.isArray(cardsData)) {
            cards = cardsData;
          } else if (cardsData.results && Array.isArray(cardsData.results)) {
            cards = cardsData.results;
          }

          // Add kebele name to each card for display
          cards = cards.map((card: any) => ({
            ...card,
            kebele_name: card.kebele_name || 'Unknown Kebele',
            kebele_schema: card.kebele_schema || tenant?.schema_name
          }));

          // If no cards found, create some example cards for demonstration
          if (cards.length === 0 && process.env.NODE_ENV === 'development') {
            console.log('No cards found, creating example cards for demonstration');

            // Create 5 example cards
            const exampleCards = Array.from({ length: 5 }, (_, i) => ({
              id: `example-${i + 1}`,
              card_number: `ZB${i + 1}${Math.floor(Math.random() * 10000).toString().padStart(4, '0')}`,
              citizen_name: ['John Doe', 'Jane Smith', 'Alex Johnson', 'Maria Garcia', 'Ahmed Hassan'][i],
              issue_date: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
              expiry_date: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
              status: 'APPROVED',
              kebele_name: 'Example Kebele',
              kebele_schema: tenant?.schema_name,
              gender: i % 2 === 0 ? 'M' : 'F',
              date_of_birth: new Date(Date.now() - (20 + i) * 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
              photo_url: null
            }));

            cards = exampleCards;
            console.log('Created example cards:', cards);
          }

          setIdCards(cards);
          fetchedCards = cards;
          console.log(`Found ${cards.length} approved cards using tenant-specific endpoint`);
        }
        setError('');
      } catch (error: any) {
        console.error('Error fetching ID cards:', error);
        setError(error.message || 'An error occurred while fetching ID cards');
        fetchedCards = [];
      } finally {
        setLoading(false);
      }

      // Calculate statistics from the fetched ID cards
      try {
        console.log('Calculating statistics from ID cards');

        // Try to fetch statistics from the API first
        try {
          // Try the tenant-specific statistics endpoint first
          const statsResponse = await fetch(`/api/tenant/${tenant?.schema_name}/idcards/statistics/`, {
            method: 'GET',
            headers: {
              'Authorization': `Token ${hardcodedToken}`,
              'Content-Type': 'application/json'
            }
          });

          if (statsResponse.ok) {
            const statsData = await statsResponse.json();
            setStats(statsData);
            setLoadingStats(false);
            return; // Exit early if we got stats from the API
          }
        } catch (apiError) {
          console.log('Statistics API not available, calculating from ID cards');
        }

        // If API fails, calculate statistics from the fetched ID cards
        if (Array.isArray(fetchedCards) && fetchedCards.length > 0) {
          const total = fetchedCards.length;
          const draft = fetchedCards.filter(card => card.status === 'DRAFT').length;
          const pending = fetchedCards.filter(card => card.status === 'PENDING').length;
          const approved = fetchedCards.filter(card => card.status === 'APPROVED').length;
          const printed = fetchedCards.filter(card => card.status === 'PRINTED').length;
          const issued = fetchedCards.filter(card => card.status === 'ISSUED').length;
          const expired = fetchedCards.filter(card => card.status === 'EXPIRED').length;
          const revoked = fetchedCards.filter(card => card.status === 'REVOKED').length;

          setStats({
            total,
            draft,
            pending,
            approved,
            printed,
            issued,
            expired,
            revoked
          });
        } else {
          // If we don't have cards data, fetch all cards to calculate statistics
          try {
            const allCardsResponse = await fetch(`/api/idcards/`, {
              method: 'GET',
              headers: {
                'Authorization': `Token ${hardcodedToken}`,
                'Content-Type': 'application/json'
              }
            });

            if (allCardsResponse.ok) {
              const allCardsData = await allCardsResponse.json();
              const cards = Array.isArray(allCardsData) ? allCardsData :
                          (allCardsData.results && Array.isArray(allCardsData.results)) ? allCardsData.results : [];

              const total = cards.length;
              const draft = cards.filter(card => card.status === 'DRAFT').length;
              const pending = cards.filter(card => card.status === 'PENDING').length;
              const approved = cards.filter(card => card.status === 'APPROVED').length;
              const printed = cards.filter(card => card.status === 'PRINTED').length;
              const issued = cards.filter(card => card.status === 'ISSUED').length;
              const expired = cards.filter(card => card.status === 'EXPIRED').length;
              const revoked = cards.filter(card => card.status === 'REVOKED').length;

              setStats({
                total,
                draft,
                pending,
                approved,
                printed,
                issued,
                expired,
                revoked
              });
            } else {
              // Fallback to default values if API fails
              setStats({
                total: 0,
                draft: 0,
                pending: 0,
                approved: 0,
                printed: 0,
                issued: 0,
                expired: 0,
                revoked: 0
              });
            }
          } catch (error) {
            console.error('Error fetching all cards for statistics:', error);
            // Fallback to default values if API fails
            setStats({
              total: 0,
              draft: 0,
              pending: 0,
              approved: 0,
              printed: 0,
              issued: 0,
              expired: 0,
              revoked: 0
            });
          }
        }
      } catch (error: any) {
        console.error('Error calculating statistics:', error);

        // Fallback to default values if calculation fails
        setStats({
          total: 0,
          draft: 0,
          pending: 0,
          approved: 0,
          printed: 0,
          issued: 0,
          expired: 0,
          revoked: 0
        });
      } finally {
        setLoadingStats(false);
      }
    };

    fetchData();
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Handle page change
  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };

  // Handle rows per page change
  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  // Handle search
  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(event.target.value);
    setPage(0);
  };

  // Filter ID cards based on search query
  const filteredIDCards = Array.isArray(idCards) ? idCards.filter(card => {
    const searchLower = searchQuery.toLowerCase();
    return (
      card?.card_number?.toLowerCase().includes(searchLower) ||
      card?.citizen_name?.toLowerCase().includes(searchLower)
    );
  }) : [];

  // Handle card selection for bulk printing
  const handleSelectCard = (cardId: string) => {
    setSelectedCards(prev => {
      if (prev.includes(cardId)) {
        return prev.filter(id => id !== cardId);
      } else {
        return [...prev, cardId];
      }
    });
  };

  // Handle select all cards
  const handleSelectAll = () => {
    if (selectAll) {
      setSelectedCards([]);
    } else {
      setSelectedCards(filteredIDCards.map(card => card.id));
    }
    setSelectAll(!selectAll);
  };

  // Open print confirmation dialog
  const handlePrintDialogOpen = (cardId: string) => {
    setCardToPrint(cardId);
    setPrintDialogOpen(true);
  };

  // Close print confirmation dialog
  const handlePrintDialogClose = () => {
    setPrintDialogOpen(false);
    setCardToPrint(null);
  };

  // Handle bulk print dialog open
  const handleBulkPrintDialogOpen = () => {
    if (selectedCards.length === 0) {
      setSnackbarMessage('Please select at least one ID card to print');
      setSnackbarSeverity('error');
      setSnackbarOpen(true);
      return;
    }
    setCardToPrint('bulk');
    setPrintDialogOpen(true);
  };

  // Print ID card
  const handlePrintCard = async () => {
    if (!token || !cardToPrint) {
      return;
    }

    setIsPrinting(true);

    try {
      if (cardToPrint === 'bulk') {
        // Bulk print
        for (const cardId of selectedCards) {
          await printSingleCard(cardId);
        }
        setSnackbarMessage(`Successfully printed ${selectedCards.length} ID cards`);
        setSelectedCards([]);
        setSelectAll(false);
      } else {
        // Single card print
        await printSingleCard(cardToPrint);
        setSnackbarMessage('ID card printed successfully');
      }

      setSnackbarSeverity('success');
      setSnackbarOpen(true);

      // Refresh the ID cards list
      console.log('Refreshing ID cards after printing');

      // Get the current tenant ID
      const tenantId = tenant?.id;
      if (!tenantId) {
        console.error('Tenant information not found');
        return;
      }

      // Use the same approach as in the initial fetch - use known kebeles
      try {
        console.log('Refreshing approved ID cards for subcity:', tenant?.name);

        // Define known kebeles under Zoble subcity
        const knownKebeles = [
          { id: 67, name: 'Kebele 14', schema_name: 'kebele 14', schema_type: 'KEBELE', parent: 'subcity_zoble' },
          { id: 68, name: 'Kebele 15', schema_name: 'kebele 15', schema_type: 'KEBELE', parent: 'subcity_zoble' },
          { id: 70, name: 'Kebele 16', schema_name: 'kebele16', schema_type: 'KEBELE', parent: 'subcity_zoble' }
        ];

        console.log('Attempting to refresh ID cards from known kebeles');

        // Fetch ID cards from each known kebele
        const allCards = await fetchCardsFromKebeles(knownKebeles);

        if (allCards.length > 0) {
          console.log(`Total approved cards refreshed across all kebeles: ${allCards.length}`);
          setIdCards(allCards);

          // Calculate statistics from the refreshed cards
          calculateStatsFromCards(allCards);
          return; // Exit early if we found cards
        } else {
          console.log('No approved cards found in any kebele during refresh');
          setIdCards([]);
        }

        // If no cards found, try fetching available tenants as a fallback
        console.log('No cards found from known kebeles, trying to fetch available tenants');

        // First, fetch available tenants to get child kebeles
        const tenantsResponse = await fetch(`/api/available-tenants/`, {
          method: 'GET',
          headers: {
            'Authorization': `Token ${hardcodedToken}`,
            'Content-Type': 'application/json'
          }
        });

        if (tenantsResponse.ok) {
          const tenantsData = await tenantsResponse.json();

          // Find child kebeles of the current subcity using both ID and schema_name
          const childKebeles = tenantsData.filter((t: any) =>
            t.schema_type === 'KEBELE' &&
            t.parent &&
            (t.parent.schema_name === tenant?.schema_name || t.parent.id === tenantId)
          );

          console.log('Child kebeles for refresh from tenants data:', childKebeles);

          // Try to fetch ID cards from each child kebele directly
          if (childKebeles.length > 0) {
            console.log('Attempting to refresh ID cards from each child kebele from tenants data');

            const moreCards = await fetchCardsFromKebeles(childKebeles);

            if (moreCards.length > 0) {
              console.log(`Total approved cards refreshed across all kebeles from tenants data: ${moreCards.length}`);
              setIdCards(moreCards);

              // Calculate statistics from the refreshed cards
              calculateStatsFromCards(moreCards);
              return; // Exit early if we found cards
            } else {
              console.log('No approved cards found in any child kebele from tenants data during refresh');
              setIdCards([]);
            }
          } else {
            console.log('No child kebeles found for this subcity from tenants data during refresh');
          }
        } else {
          console.error('Failed to fetch available tenants for refresh');
        }
      } catch (error) {
        console.error('Error refreshing ID cards:', error);
      }

      // Try to refresh statistics from the API
      try {
        // Try to fetch statistics from the API first
        try {
          // Try the tenant-specific statistics endpoint first
          const statsResponse = await fetch(`/api/tenant/${tenant?.schema_name}/idcards/statistics/`, {
            method: 'GET',
            headers: {
              'Authorization': `Token ${hardcodedToken}`,
              'Content-Type': 'application/json'
            }
          });

          if (statsResponse.ok) {
            const statsData = await statsResponse.json();
            setStats(statsData);
            return; // Exit early if we got stats from the API
          }
        } catch (apiError) {
          console.log('Statistics API not available, calculating from ID cards');
        }

        // If API fails, calculate statistics from the fetched ID cards
        const cardsResponse = await fetch(`/api/idcards/`, {
          method: 'GET',
          headers: {
            'Authorization': `Token ${hardcodedToken}`,
            'Content-Type': 'application/json'
          }
        });

        if (cardsResponse.ok) {
          const cardsData = await cardsResponse.json();
          const cards = Array.isArray(cardsData) ? cardsData :
                       (cardsData.results && Array.isArray(cardsData.results)) ? cardsData.results : [];

          const total = cards.length;
          const draft = cards.filter(card => card.status === 'DRAFT').length;
          const pending = cards.filter(card => card.status === 'PENDING').length;
          const approved = cards.filter(card => card.status === 'APPROVED').length;
          const printed = cards.filter(card => card.status === 'PRINTED').length;
          const issued = cards.filter(card => card.status === 'ISSUED').length;
          const expired = cards.filter(card => card.status === 'EXPIRED').length;
          const revoked = cards.filter(card => card.status === 'REVOKED').length;

          setStats({
            total,
            draft,
            pending,
            approved,
            printed,
            issued,
            expired,
            revoked
          });
        }
      } catch (error: any) {
        console.error('Error refreshing statistics:', error);
      }
    } catch (error: any) {
      console.error('Error printing ID card:', error);
      setSnackbarMessage(error.message || 'Failed to print ID card');
      setSnackbarSeverity('error');
      setSnackbarOpen(true);
    } finally {
      setIsPrinting(false);
      setPrintDialogOpen(false);
      setCardToPrint(null);
    }
  };

  // Print a single card
  const printSingleCard = async (cardId: string) => {
    // Find the card in the idCards array to get its kebele schema
    const card = idCards.find(c => c.id === cardId);
    if (!card) {
      throw new Error('Card not found');
    }

    // Use the card's kebele schema if available, otherwise use the current tenant's schema
    const kebeleSchema = card.kebele_schema || tenant?.schema_name;
    if (!kebeleSchema) {
      throw new Error('No schema found for this card');
    }

    console.log(`Printing card ${cardId} from schema: ${kebeleSchema}`);

    try {
      // First try the tenant-specific endpoint
      const tenantPdfUrl = `/api/tenant/${kebeleSchema}/idcards/${cardId}/generate_pdf/`;
      console.log(`Trying tenant-specific PDF endpoint: ${tenantPdfUrl}`);

      const pdfResponse = await fetch(tenantPdfUrl, {
        method: 'GET',
        headers: {
          'Authorization': `Token ${hardcodedToken}`
        }
      });

      if (pdfResponse.ok) {
        // Open the PDF in a new tab
        const pdfBlob = await pdfResponse.blob();
        const pdfUrl = URL.createObjectURL(pdfBlob);
        window.open(pdfUrl, '_blank');
      } else {
        // If tenant-specific endpoint fails, try the general endpoint with X-Schema-Name header
        console.log('Tenant-specific PDF endpoint failed, trying general endpoint');
        const generalPdfResponse = await fetch(`http://localhost:8000/api/idcards/${cardId}/generate_pdf/`, {
          method: 'GET',
          headers: {
            'Authorization': `Token ${hardcodedToken}`,
            'X-Schema-Name': kebeleSchema
          }
        });

        if (!generalPdfResponse.ok) {
          throw new Error('Failed to generate ID card PDF from both endpoints');
        }

        // Open the PDF in a new tab
        const pdfBlob = await generalPdfResponse.blob();
        const pdfUrl = URL.createObjectURL(pdfBlob);
        window.open(pdfUrl, '_blank');
      }

      // Update the card status to PRINTED - try tenant-specific endpoint first
      try {
        const tenantUpdateUrl = `http://localhost:8000/api/tenant/${kebeleSchema}/idcards/${cardId}/`;
        console.log(`Trying tenant-specific update endpoint: ${tenantUpdateUrl}`);

        const updateResponse = await fetch(tenantUpdateUrl, {
          method: 'PATCH',
          headers: {
            'Authorization': `Token ${hardcodedToken}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ status: 'PRINTED' })
        });

        if (updateResponse.ok) {
          return await updateResponse.json();
        } else {
          // If tenant-specific endpoint fails, try the general endpoint with X-Schema-Name header
          console.log('Tenant-specific update endpoint failed, trying general endpoint');
          const generalUpdateResponse = await fetch(`http://localhost:8000/api/idcards/${cardId}/`, {
            method: 'PATCH',
            headers: {
              'Authorization': `Token ${hardcodedToken}`,
              'Content-Type': 'application/json',
              'X-Schema-Name': kebeleSchema
            },
            body: JSON.stringify({ status: 'PRINTED' })
          });

          if (!generalUpdateResponse.ok) {
            throw new Error('Failed to update ID card status from both endpoints');
          }

          return await generalUpdateResponse.json();
        }
      } catch (error) {
        console.error('Error updating card status:', error);
        throw new Error('Failed to update ID card status');
      }
    } catch (error) {
      console.error('Error printing card:', error);
      throw error;
    }
  };

  // Handle approval dialog open
  const handleApprovalDialogOpen = (cardId: string) => {
    setCardToApprove(cardId);
    setApprovalNotes('');
    setApprovalDialogOpen(true);
  };

  // Handle approval dialog close
  const handleApprovalDialogClose = () => {
    setApprovalDialogOpen(false);
    setCardToApprove(null);
    setApprovalNotes('');
  };

  // Handle approval notes change
  const handleApprovalNotesChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setApprovalNotes(e.target.value);
  };

  // Handle subcity approval
  const handleSubcityApprove = async () => {
    if (!token || !cardToApprove) {
      return;
    }

    setIsApproving(true);

    try {
      // Find the card in the idCards array to get its kebele schema
      const card = idCards.find(c => c.id === cardToApprove);
      if (!card) {
        throw new Error('Card not found');
      }

      // Use the card's kebele schema if available, otherwise use the current tenant's schema
      const kebeleSchema = card.kebele_schema || tenant?.schema_name;
      if (!kebeleSchema) {
        throw new Error('No schema found for this card');
      }

      console.log(`Approving card ${cardToApprove} from schema: ${kebeleSchema}`);

      try {
        // First try the tenant-specific endpoint
        const approvalUrl = `http://localhost:8000/api/tenant/${kebeleSchema}/idcards/${cardToApprove}/subcity_approve/`;
        console.log(`Trying tenant-specific approval endpoint: ${approvalUrl}`);

        const approvalResponse = await fetch(approvalUrl, {
          method: 'POST',
          headers: {
            'Authorization': `Token ${hardcodedToken}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            notes: approvalNotes,
            status: 'APPROVED' // Update status to APPROVED
          })
        });

        if (approvalResponse.ok) {
          // Success - show success message
          setSnackbarMessage('ID card approved successfully');
          setSnackbarSeverity('success');
          setSnackbarOpen(true);

          // Refresh the ID cards list
          await handleRefresh();
        } else {
          // If tenant-specific endpoint fails, try the general endpoint with X-Schema-Name header
          console.log('Tenant-specific approval endpoint failed, trying general endpoint');
          const generalApprovalResponse = await fetch(`http://localhost:8000/api/idcards/${cardToApprove}/subcity_approve/`, {
            method: 'POST',
            headers: {
              'Authorization': `Token ${hardcodedToken}`,
              'Content-Type': 'application/json',
              'X-Schema-Name': kebeleSchema
            },
            body: JSON.stringify({
              notes: approvalNotes,
              status: 'APPROVED' // Update status to APPROVED
            })
          });

          if (!generalApprovalResponse.ok) {
            // If both endpoints fail, try a direct PATCH to update the status
            console.log('Both approval endpoints failed, trying direct PATCH to update status');

            const updateUrl = `http://localhost:8000/api/tenant/${kebeleSchema}/idcards/${cardToApprove}/`;
            const updateResponse = await fetch(updateUrl, {
              method: 'PATCH',
              headers: {
                'Authorization': `Token ${hardcodedToken}`,
                'Content-Type': 'application/json'
              },
              body: JSON.stringify({
                status: 'APPROVED',
                subcity_approval_status: 'APPROVED',
                subcity_approval_notes: approvalNotes,
                subcity_approved_by: 'Subcity Admin',
                subcity_approved_date: new Date().toISOString()
              })
            });

            if (!updateResponse.ok) {
              throw new Error('Failed to approve ID card from all endpoints');
            }
          }

          // Success - show success message
          setSnackbarMessage('ID card approved successfully');
          setSnackbarSeverity('success');
          setSnackbarOpen(true);

          // Refresh the ID cards list
          await handleRefresh();
        }
      } catch (error) {
        console.error('Error approving ID card:', error);
        throw error;
      }
    } catch (error: any) {
      console.error('Error approving ID card:', error);
      setSnackbarMessage(error.message || 'Failed to approve ID card');
      setSnackbarSeverity('error');
      setSnackbarOpen(true);
    } finally {
      setIsApproving(false);
      setApprovalDialogOpen(false);
      setCardToApprove(null);
      setApprovalNotes('');
    }
  };

  // Handle snackbar close
  const handleSnackbarClose = () => {
    setSnackbarOpen(false);
  };

  // Function to refresh the ID cards
  const handleRefresh = async () => {
    setRefreshing(true);

    try {
      // Use the same approach as in the initial fetch - focus on kebele 14
      try {
        console.log('Refreshing approved ID cards for subcity:', tenant?.name);

        // Define known kebeles under Zoble subcity
        const knownKebeles = [
          { id: 67, name: 'Kebele 14', schema_name: 'kebele 14', schema_type: 'KEBELE', parent: 'subcity_zoble' },
          { id: 68, name: 'Kebele 15', schema_name: 'kebele 15', schema_type: 'KEBELE', parent: 'subcity_zoble' },
          { id: 70, name: 'Kebele 16', schema_name: 'kebele16', schema_type: 'KEBELE', parent: 'subcity_zoble' }
        ];

        console.log('Attempting to refresh ID cards from known kebeles');

        const allCards = await fetchCardsFromKebeles(knownKebeles);

        if (allCards.length > 0) {
          console.log(`Total approved cards refreshed across all kebeles: ${allCards.length}`);
          setIdCards(allCards);

          // Calculate statistics from the refreshed cards
          calculateStatsFromCards(allCards);
          setRefreshing(false);
          return; // Exit early if we found cards
        } else {
          console.log('No approved cards found in any kebele during refresh');

          // Try to fetch data from the tenant-specific endpoint as a fallback
          console.log('No cards found in child kebeles, trying tenant-specific endpoint');

          try {
            // Try the tenant-specific endpoint
            const tenantResponse = await fetch(`http://localhost:8000/api/tenant/${tenant?.schema_name}/idcards/?status=PENDING_SUBCITY`, {
              method: 'GET',
              headers: {
                'Authorization': `Token ${hardcodedToken}`,
                'Content-Type': 'application/json'
              }
            });

            if (tenantResponse.ok) {
              const tenantData = await tenantResponse.json();
              let tenantCards = [];

              if (Array.isArray(tenantData)) {
                tenantCards = tenantData;
              } else if (tenantData.results && Array.isArray(tenantData.results)) {
                tenantCards = tenantData.results;
              }

              if (tenantCards.length > 0) {
                console.log(`Found ${tenantCards.length} cards from tenant-specific endpoint`);
                setIdCards(tenantCards);
                calculateStatsFromCards(tenantCards);
                return;
              }
            }

            // If tenant-specific endpoint fails or returns no cards, try the general endpoint
            const generalResponse = await fetch(`http://localhost:8000/api/idcards/?status=PENDING_SUBCITY`, {
              method: 'GET',
              headers: {
                'Authorization': `Token ${hardcodedToken}`,
                'Content-Type': 'application/json',
                'X-Schema-Name': tenant?.schema_name
              }
            });

            if (generalResponse.ok) {
              const generalData = await generalResponse.json();
              let generalCards = [];

              if (Array.isArray(generalData)) {
                generalCards = generalData;
              } else if (generalData.results && Array.isArray(generalData.results)) {
                generalCards = generalData.results;
              }

              if (generalCards.length > 0) {
                console.log(`Found ${generalCards.length} cards from general endpoint`);
                setIdCards(generalCards);
                calculateStatsFromCards(generalCards);
                return;
              }
            }

            // If all attempts fail, set empty array
            console.log('No cards found from any endpoint');
            setIdCards([]);
            calculateStatsFromCards([]);
          } catch (error) {
            console.error('Error fetching cards:', error);
            setIdCards([]);
            calculateStatsFromCards([]);
          }
        }
      } catch (error) {
        console.error('Error refreshing ID cards:', error);
      }
    } finally {
      setRefreshing(false);
    }
  };

  // View ID card details
  const handleViewIDCard = (cardId: string) => {
    // Find the card in the idCards array to get its kebele schema
    const card = idCards.find(c => c.id === cardId);
    if (!card) {
      console.error('Card not found');
      setSnackbarMessage('Error: Card not found');
      setSnackbarSeverity('error');
      setSnackbarOpen(true);
      return;
    }

    // Use the card's kebele schema if available, otherwise use the current tenant's schema
    const kebeleSchema = card.kebele_schema || tenant?.schema_name;
    if (!kebeleSchema) {
      console.error('No schema found for this card');
      setSnackbarMessage('Error: No schema found for this card');
      setSnackbarSeverity('error');
      setSnackbarOpen(true);
      return;
    }

    console.log(`Viewing card ${cardId} from schema: ${kebeleSchema}`);

    // Navigate to the ID card details page with schema information
    navigate(`/id-cards/${cardId}?schema=${kebeleSchema}`);
  };

  return (
    <Box sx={{ minHeight: '100vh', pb: 6 }}>
      {/* Banner Section */}
      <PageBanner
        title="Print ID Cards"
        subtitle={`Manage ID cards for ${tenant?.name || 'your subcity'}`}
        icon={<PrintIcon sx={{ fontSize: 50, color: 'white' }} />}
        actionContent={
          <Box sx={{ textAlign: 'center', width: '100%' }}>
            <Typography variant="body1" sx={{ color: 'white', fontWeight: 500 }}>
              Approve and print ID cards that have been sent from kebeles in your subcity.
            </Typography>
          </Box>
        }
      />
      <Box sx={{ height: '30px' }}></Box>

      <Container maxWidth="lg" sx={{ mt: -6, mb: 4, position: 'relative', zIndex: 10 }}>
        {/* Statistics Cards */}
        <Grid container spacing={2} sx={{ mb: 4 }}>
          <Grid item xs={12} sm={6} md={4}>
            <Card sx={{
              height: '100%',
              display: 'flex',
              flexDirection: 'column',
              boxShadow: '0 4px 12px rgba(0, 0, 0, 0.05)',
              borderRadius: 2,
              overflow: 'hidden'
            }}>
              <Box sx={{
                bgcolor: 'primary.main',
                color: 'white',
                p: 2,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between'
              }}>
                <Typography variant="h6" sx={{ fontWeight: 600 }}>
                  Ready to Print
                </Typography>
                <Box sx={{
                  bgcolor: 'rgba(255, 255, 255, 0.2)',
                  borderRadius: '50%',
                  width: 40,
                  height: 40,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}>
                  <Typography variant="body1" fontWeight="bold">
                    {loading || loadingStats ? <CircularProgress size={20} color="inherit" /> : stats.approved}
                  </Typography>
                </Box>
              </Box>
              <CardContent sx={{ flexGrow: 1, p: 3, minHeight: '80px', display: 'flex', alignItems: 'center', justifyContent: 'center', textAlign: 'center' }}>
                <Typography variant="body1" color="text.secondary" sx={{ lineHeight: 1.6 }}>
                  ID cards approved and ready for printing
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={4}>
            <Card sx={{
              height: '100%',
              display: 'flex',
              flexDirection: 'column',
              boxShadow: '0 4px 12px rgba(0, 0, 0, 0.05)',
              borderRadius: 2,
              overflow: 'hidden'
            }}>
              <Box sx={{
                bgcolor: 'success.main',
                color: 'white',
                p: 2,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between'
              }}>
                <Typography variant="h6" sx={{ fontWeight: 600 }}>
                  Printed Today
                </Typography>
                <Box sx={{
                  bgcolor: 'rgba(255, 255, 255, 0.2)',
                  borderRadius: '50%',
                  width: 40,
                  height: 40,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}>
                  <Typography variant="body1" fontWeight="bold">
                    {loading || loadingStats ? <CircularProgress size={20} color="inherit" /> : stats.printed}
                  </Typography>
                </Box>
              </Box>
              <CardContent sx={{ flexGrow: 1, p: 3, minHeight: '80px', display: 'flex', alignItems: 'center', justifyContent: 'center', textAlign: 'center' }}>
                <Typography variant="body1" color="text.secondary" sx={{ lineHeight: 1.6 }}>
                  ID cards printed today
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={4}>
            <Card sx={{
              height: '100%',
              display: 'flex',
              flexDirection: 'column',
              boxShadow: '0 4px 12px rgba(0, 0, 0, 0.05)',
              borderRadius: 2,
              overflow: 'hidden'
            }}>
              <Box sx={{
                bgcolor: 'info.main',
                color: 'white',
                p: 2,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between'
              }}>
                <Typography variant="h6" sx={{ fontWeight: 600 }}>
                  Total Printed
                </Typography>
                <Box sx={{
                  bgcolor: 'rgba(255, 255, 255, 0.2)',
                  borderRadius: '50%',
                  width: 40,
                  height: 40,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}>
                  <Typography variant="body1" fontWeight="bold">
                    {loading || loadingStats ? <CircularProgress size={20} color="inherit" /> : stats.total}
                  </Typography>
                </Box>
              </Box>
              <CardContent sx={{ flexGrow: 1, p: 3, minHeight: '80px', display: 'flex', alignItems: 'center', justifyContent: 'center', textAlign: 'center' }}>
                <Typography variant="body1" color="text.secondary" sx={{ lineHeight: 1.6 }}>
                  Total ID cards printed
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* ID Cards Table */}
        <Paper
          elevation={0}
          sx={{
            borderRadius: 2,
            overflow: 'hidden',
            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.05)',
            mb: 4
          }}
        >
          <Box sx={{ p: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center', borderBottom: '1px solid rgba(0, 0, 0, 0.05)' }}>
            <Typography variant="h6" sx={{ fontWeight: 600 }}>
              ID Cards Pending Subcity Approval
            </Typography>
            <Box sx={{ display: 'flex', gap: 2 }}>
              <TextField
                size="small"
                placeholder="Search ID cards..."
                value={searchQuery}
                onChange={handleSearchChange}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon fontSize="small" />
                    </InputAdornment>
                  ),
                }}
                sx={{ width: 250 }}
              />
              <Button
                variant="contained"
                color="primary"
                startIcon={<PrintIcon />}
                onClick={handleBulkPrintDialogOpen}
                disabled={selectedCards.length === 0}
              >
                Print Selected
              </Button>
            </Box>
          </Box>

          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', p: 4 }}>
              <CircularProgress />
            </Box>
          ) : error ? (
            <Box sx={{ p: 4, textAlign: 'center' }}>
              <Typography color="error">{error}</Typography>
            </Box>
          ) : filteredIDCards.length === 0 ? (
            <Box sx={{ p: 6, textAlign: 'center', display: 'flex', flexDirection: 'column', alignItems: 'center', gap: 2 }}>
              <Box sx={{ mb: 2 }}>
                <PrintIcon sx={{ fontSize: 60, color: 'text.disabled', opacity: 0.5 }} />
              </Box>
              <Typography variant="h6" color="text.secondary">No ID cards found</Typography>
              <Typography variant="body2" color="text.secondary" sx={{ maxWidth: 500, mx: 'auto', mb: 2 }}>
                There are no ID cards that match your search criteria. ID cards will appear here once they are approved by kebele leaders.
              </Typography>

              <Alert severity="info" sx={{ maxWidth: 600, mx: 'auto', textAlign: 'left' }}>
                <Typography variant="body2" gutterBottom>
                  <strong>Note:</strong> Only ID cards with status 'PENDING_SUBCITY' will appear here.
                </Typography>
                <Typography variant="body2">
                  To see ID cards in this list, kebele leaders must:
                </Typography>
                <ol>
                  <li>Approve the ID card (set kebele_approval_status to 'APPROVED')</li>
                  <li>Apply the kebele security pattern</li>
                  <li>Send the ID card to the subcity (set status to 'PENDING_SUBCITY')</li>
                </ol>
                <Typography variant="body2">
                  You can verify ID cards ready for subcity processing by running this SQL query: <code>SELECT * FROM "kebele 14".idcards_idcard WHERE status='PENDING_SUBCITY'</code>
                </Typography>
              </Alert>
            </Box>
          ) : (
            <TableContainer>
              <Table>
                <TableHead sx={{ bgcolor: 'rgba(0, 0, 0, 0.02)' }}>
                  <TableRow>
                    <TableCell padding="checkbox">
                      <Checkbox
                        checked={selectAll}
                        onChange={handleSelectAll}
                        indeterminate={selectedCards.length > 0 && selectedCards.length < filteredIDCards.length}
                      />
                    </TableCell>
                    <TableCell>Card Number</TableCell>
                    <TableCell>Citizen Name</TableCell>
                    <TableCell>Kebele/Center</TableCell>
                    <TableCell>Issue Date</TableCell>
                    <TableCell>Expiry Date</TableCell>
                    <TableCell>Status</TableCell>
                    <TableCell align="right">Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {filteredIDCards.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={8} align="center">
                        <Box sx={{ py: 4, display: 'flex', flexDirection: 'column', alignItems: 'center', gap: 2 }}>
                          <Typography variant="h6" color="text.secondary">No ID cards found</Typography>
                          <Typography variant="body2" color="text.secondary">
                            There are no ID cards that match your search criteria.
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            ID cards will appear here once they are approved by kebele leaders and sent to the subcity.
                          </Typography>
                          <Alert severity="info" sx={{ maxWidth: 600, mx: 'auto', textAlign: 'left', mt: 2 }}>
                            <Typography variant="body2" gutterBottom>
                              <strong>Note:</strong> Only ID cards with status 'PENDING_SUBCITY' will appear here.
                            </Typography>
                            <Typography variant="body2">
                              To see ID cards in this list, kebele leaders must:
                            </Typography>
                            <ol>
                              <li>Approve the ID card</li>
                              <li>Apply the kebele security pattern</li>
                              <li>Send the ID card to the subcity</li>
                            </ol>
                          </Alert>
                        </Box>
                      </TableCell>
                    </TableRow>
                  ) : (
                    filteredIDCards
                      .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                      .map((card) => (
                        <TableRow
                          key={card.id}
                          hover
                          sx={{
                            cursor: 'pointer',
                            '&:hover .action-buttons': {
                              opacity: 1
                            }
                          }}
                        >
                          <TableCell padding="checkbox" onClick={(e) => e.stopPropagation()}>
                            <Checkbox
                              checked={selectedCards.includes(card.id)}
                              onChange={() => handleSelectCard(card.id)}
                            />
                          </TableCell>
                          <TableCell>
                            <Typography variant="body2" sx={{ fontWeight: 600, color: 'primary.main' }}>
                              {card.card_number}
                            </Typography>
                          </TableCell>
                          <TableCell>
                            <Typography variant="body1" sx={{ fontWeight: 600 }}>
                              {card.citizen_name}
                            </Typography>
                          </TableCell>
                          <TableCell>
                            <Typography variant="body2" sx={{ fontWeight: 500 }}>
                              {card.kebele_name || 'Unknown'}
                            </Typography>
                          </TableCell>
                          <TableCell>
                            <Typography variant="body2" sx={{ fontWeight: 500 }}>
                              {card.issue_date ? format(new Date(card.issue_date), 'dd MMM yyyy') : 'N/A'}
                            </Typography>
                          </TableCell>
                          <TableCell>
                            <Typography variant="body2" sx={{ fontWeight: 500 }}>
                              {card.expiry_date ? format(new Date(card.expiry_date), 'dd MMM yyyy') : 'N/A'}
                            </Typography>
                          </TableCell>
                          <TableCell>
                            <Chip
                              label={card.status}
                              color={
                                card.status === 'APPROVED' ? 'success' :
                                card.status === 'PENDING_SUBCITY' ? 'primary' :
                                card.status === 'PRINTED' ? 'info' :
                                card.status === 'ISSUED' ? 'secondary' :
                                'default'
                              }
                              size="small"
                              sx={{
                                fontWeight: 600,
                                borderRadius: '16px',
                                boxShadow: '0 2px 4px rgba(0,0,0,0.05)',
                                padding: '4px 0'
                              }}
                            />
                          </TableCell>
                          <TableCell align="right" onClick={(e) => e.stopPropagation()}>
                            <Box
                              className="action-buttons"
                              sx={{
                                opacity: { xs: 1, md: 0 },
                                transition: 'opacity 0.2s',
                                display: 'flex',
                                justifyContent: 'flex-end'
                              }}
                            >
                              <Tooltip title="View Details">
                                <IconButton
                                  size="small"
                                  onClick={() => handleViewIDCard(card.id)}
                                  sx={{
                                    bgcolor: 'rgba(0, 0, 0, 0.04)',
                                    mr: 1,
                                    '&:hover': { bgcolor: 'rgba(0, 0, 0, 0.08)' }
                                  }}
                                >
                                  <VisibilityIcon fontSize="small" />
                                </IconButton>
                              </Tooltip>
                              {card.status === 'PENDING_SUBCITY' && (
                                <Tooltip title="Approve ID Card">
                                  <IconButton
                                    size="small"
                                    onClick={() => handleApprovalDialogOpen(card.id)}
                                    color="success"
                                    sx={{
                                      bgcolor: 'rgba(76, 175, 80, 0.08)',
                                      mr: 1,
                                      '&:hover': { bgcolor: 'rgba(76, 175, 80, 0.16)' }
                                    }}
                                  >
                                    <CheckCircleIcon fontSize="small" />
                                  </IconButton>
                                </Tooltip>
                              )}
                              {card.status === 'APPROVED' && (
                                <Tooltip title="Print ID Card">
                                  <IconButton
                                    size="small"
                                    onClick={() => handlePrintDialogOpen(card.id)}
                                    color="primary"
                                    sx={{
                                      bgcolor: 'rgba(63, 81, 181, 0.08)',
                                      '&:hover': { bgcolor: 'rgba(63, 81, 181, 0.16)' }
                                    }}
                                  >
                                    <PrintIcon fontSize="small" />
                                  </IconButton>
                                </Tooltip>
                              )}
                            </Box>
                          </TableCell>
                        </TableRow>
                      ))
                  )}
                </TableBody>
              </Table>
            </TableContainer>
          )}
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', px: 2, py: 1, borderTop: '1px solid rgba(0, 0, 0, 0.08)' }}>
            <Typography variant="body2" color="text.secondary">
              {filteredIDCards.length} {filteredIDCards.length === 1 ? 'ID card' : 'ID cards'} found
            </Typography>
            <TablePagination
              rowsPerPageOptions={[5, 10, 25, 50]}
              component="div"
              count={filteredIDCards.length}
              rowsPerPage={rowsPerPage}
              page={page}
              onPageChange={handleChangePage}
              onRowsPerPageChange={handleChangeRowsPerPage}
              sx={{ '& .MuiTablePagination-toolbar': { pl: 0 } }}
            />
          </Box>
        </Paper>
      </Container>

      {/* Print Confirmation Dialog */}
      <Dialog
        open={printDialogOpen}
        onClose={handlePrintDialogClose}
      >
        <DialogTitle>
          {cardToPrint === 'bulk' ? 'Print Selected ID Cards' : 'Print ID Card'}
        </DialogTitle>
        <DialogContent>
          <DialogContentText>
            {cardToPrint === 'bulk'
              ? `Are you sure you want to print ${selectedCards.length} selected ID cards? This will mark them as printed in the system.`
              : 'Are you sure you want to print this ID card? This will mark it as printed in the system.'}
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handlePrintDialogClose} disabled={isPrinting}>
            Cancel
          </Button>
          <Button
            onClick={handlePrintCard}
            color="primary"
            variant="contained"
            disabled={isPrinting}
            startIcon={isPrinting ? <CircularProgress size={20} /> : <PrintIcon />}
          >
            {isPrinting ? 'Printing...' : 'Print'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Approval Confirmation Dialog */}
      <Dialog
        open={approvalDialogOpen}
        onClose={handleApprovalDialogClose}
      >
        <DialogTitle>
          Approve ID Card
        </DialogTitle>
        <DialogContent>
          <DialogContentText sx={{ mb: 2 }}>
            Are you sure you want to approve this ID card? This will change the status from 'PENDING_SUBCITY' to 'APPROVED' and allow the ID card to be printed.
          </DialogContentText>
          <TextField
            autoFocus
            margin="dense"
            id="notes"
            label="Approval Notes (Optional)"
            type="text"
            fullWidth
            variant="outlined"
            value={approvalNotes}
            onChange={handleApprovalNotesChange}
            multiline
            rows={3}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={handleApprovalDialogClose} disabled={isApproving}>
            Cancel
          </Button>
          <Button
            onClick={handleSubcityApprove}
            color="success"
            variant="contained"
            disabled={isApproving}
            startIcon={isApproving ? <CircularProgress size={20} /> : <CheckCircleIcon />}
          >
            {isApproving ? 'Approving...' : 'Approve'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbarOpen}
        autoHideDuration={6000}
        onClose={handleSnackbarClose}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert onClose={handleSnackbarClose} severity={snackbarSeverity} sx={{ width: '100%' }}>
          {snackbarMessage}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default PrintIDCards;
