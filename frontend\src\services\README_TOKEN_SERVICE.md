# Token Service Consolidation

This document describes the consolidation of token-related utilities in the application.

## Overview

The application previously had multiple token-related utilities:
- `unifiedTokenService.ts`: JWT token management
- `tokenManager.ts`: Legacy token management
- `unifiedTokenManager.ts`: Bridge between JWT and legacy token systems

These have been consolidated into a single `tokenService.ts` that provides all necessary token management functions.

## Migration Guide

### For Components Using unifiedTokenService

Change imports from:
```typescript
import { 
  loginWithJWT, 
  refreshJWTTokens, 
  validateJWTToken, 
  getAuthHeaders 
} from '../services/unifiedTokenService';
```

To:
```typescript
import { 
  loginWithJWT, 
  refreshJWTTokens, 
  validateJWTToken, 
  getAuthHeaders 
} from '../services/tokenService';
```

### For Components Using tokenManager

Change imports from:
```typescript
import { 
  authenticatedFetch, 
  setTokenForCurrentSchema 
} from '../utils/tokenManager';
```

To:
```typescript
import { 
  authenticatedFetch, 
  storeTokensForSchema, 
  getCurrentSchema 
} from '../services/tokenService';
```

And replace:
```typescript
setTokenForCurrentSchema(token);
```

With:
```typescript
const schema = getCurrentSchema();
if (schema) {
  storeTokensForSchema(schema, token, '');
}
```

### For Components Using unifiedTokenManager

Change imports from:
```typescript
import { 
  getTokenForSchema, 
  saveTokenForSchema, 
  getCurrentSchema 
} from './unifiedTokenManager';
```

To:
```typescript
import { 
  getAccessTokenForSchema, 
  storeTokensForSchema, 
  getCurrentSchema 
} from '../services/tokenService';
```

And replace:
```typescript
const token = getTokenForSchema(schema);
```

With:
```typescript
const token = getAccessTokenForSchema(schema);
```

And replace:
```typescript
saveTokenForSchema(schema, token);
```

With:
```typescript
storeTokensForSchema(schema, token, '');
```

## Key Features of the Consolidated Token Service

### Token Storage
- In-memory `tokenStore` object
- localStorage (both general and schema-specific keys)
- HTTP-only cookies for refresh tokens

### Token Management Functions
- `storeTokensForSchema`: Stores access and refresh tokens for a specific schema
- `getAccessTokenForSchema`: Gets the access token for a specific schema
- `getRefreshTokenForSchema`: Gets the refresh token for a specific schema
- `clearTokensForSchema`: Clears tokens for a specific schema
- `clearAllTokens`: Clears all tokens
- `getAuthHeaders`: Gets authentication headers for API requests
- `loginWithJWT`: Logs in with JWT
- `refreshJWTTokens`: Refreshes JWT tokens
- `validateJWTToken`: Validates a JWT token
- `createAuthenticatedFetch`: Creates an authenticated fetch function for a specific schema
- `authenticatedFetch`: Legacy compatibility function for authenticated fetch

### Schema Management Functions
- `getCurrentSchema`: Gets the current schema
- `storeCurrentSchema`: Stores the current schema

### Legacy Compatibility
- The service includes a migration function that automatically migrates tokens from legacy storage
- It provides compatibility functions for components that use the old token utilities

## Best Practices

1. Always use the `tokenService.ts` for token management
2. Always include the `X-Schema-Name` header in API requests
3. Always use the Bearer prefix for JWT tokens
4. Use HTTP-only cookies for refresh tokens
5. Implement token refresh before expiration
6. Use the `getAuthHeaders` function to get authentication headers for API requests

## Security Considerations

1. JWT tokens are short-lived (15 minutes) to minimize the impact of token theft
2. Refresh tokens are longer-lived (7 days) but are stored in HTTP-only cookies
3. Token rotation is implemented for refresh tokens to prevent replay attacks
4. Tokens are validated on every request
5. Tokens include the schema name to prevent cross-tenant access
