# Fixing Citizen and IDCard Models in Admin Panel

This guide explains how to fix the issue where Citizen and IDCard models are not appearing in the Django admin panel.

## The Issue

The Citizen and IDCard models are defined in tenant-specific apps, but they need to be registered in the global admin site to be accessible in the public schema's admin panel.

## Solution

We've created a script that properly registers these models in the admin panel using global admin classes that can access data across all tenants.

## How to Fix

### Option 1: Run the Batch File (Windows)

1. Make sure your Django development server is running
2. Open a command prompt in the `backend` directory
3. Run the batch file:
   ```
   run_fix_admin.bat
   ```
4. The script will register the models in the admin panel
5. Visit http://localhost:8000/admin/ to access the admin panel

### Option 2: Run the Python Script Directly

1. Make sure your Django development server is running
2. Open a command prompt in the `backend` directory
3. Run the Python script:
   ```
   python fix_admin_registration.py
   ```
4. The script will register the models in the admin panel
5. Visit http://localhost:8000/admin/ to access the admin panel

## Admin Sites

The project has two admin sites:

1. **Default Admin Site**: http://localhost:8000/admin/
2. **Custom Admin Site**: http://localhost:8000/neocamelot-admin/

Both admin sites should now display the Citizen and IDCard models.

## How It Works

The fix works by:

1. Unregistering the models if they are already registered
2. Importing the global admin classes from `global_admin.py`
3. Registering the models with these global admin classes
4. The global admin classes use tenant contexts to fetch data from all tenants

## Permanent Fix

To make this fix permanent, you can:

1. Add the registration code to `neocamelot/admin.py`
2. Add the registration code to `neocamelot/urls.py`
3. Update the `INSTALLED_APPS` setting to include the citizen and idcard apps in the shared apps

## Troubleshooting

If you encounter any issues:

1. Make sure the Django development server is running
2. Check that the `global_admin.py` file exists in the backend directory
3. Check that the models are defined correctly in their respective apps
4. Check the Django error logs for any specific errors
