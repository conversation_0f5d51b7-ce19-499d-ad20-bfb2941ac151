# Generated by Django 5.1.7 on 2025-04-17 16:02

from django.db import migrations
import uuid


def generate_uuid_values(apps, schema_editor):
    Citizen = apps.get_model('citizens', 'Citizen')
    for citizen in Citizen.objects.all():
        if not citizen.uuid:
            citizen.uuid = uuid.uuid4()
            citizen.save(update_fields=['uuid'])


class Migration(migrations.Migration):

    dependencies = [
        ('citizens', '0018_alter_citizen_uuid'),
    ]

    operations = [
        migrations.RunPython(generate_uuid_values, migrations.RunPython.noop),
    ]
