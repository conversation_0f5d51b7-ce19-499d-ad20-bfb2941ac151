import requests
import json

# Base URL
base_url = 'http://127.0.0.1:8000'

# Login to get token
def login():
    print("Logging in...")
    url = base_url + '/api/login/'
    data = {
        'email': '<EMAIL>',
        'password': 'admin123'
    }
    response = requests.post(url, json=data)
    print(f"Status code: {response.status_code}")

    if response.status_code == 200:
        return response.json().get('token')
    return None

# Create a new center
def create_center(token):
    print("\nCreating a new center...")
    url = base_url + '/api/centers-create-simple/'
    headers = {'Authorization': f'Token {token}'}
    data = {
        'name': 'Test Center',
        'address': '123 Test Street, Test City',
        'description': 'This is a test center',
        'city': 'Test City',
        'state': 'Test State',
        'postal_code': '12345',
        'country': 'Ethiopia',
        'phone': '+251-987654321',
        'email': '<EMAIL>',
        'website': 'https://www.testcenter.com',
        'header_color': '#ff5733',
        'accent_color': '#c70039',
        'admin_name': 'Test Admin',
        'admin_email': '<EMAIL>',
        'admin_phone': '+251-123456789',
        'max_users': 15,
        'max_citizens': 1500,
        'max_id_cards': 1500,
        'type_id': 1  # Government Office
    }
    response = requests.post(url, json=data, headers=headers)
    print(f"Status code: {response.status_code}")
    print(f"Response: {json.dumps(response.json(), indent=2)}")

    return response.json().get('id') if response.status_code == 201 else None

# Run the test
if __name__ == '__main__':
    token = login()
    if token:
        center_id = create_center(token)
        if center_id:
            print(f"\nSuccessfully created center with ID: {center_id}")
        else:
            print("\nFailed to create center")
    else:
        print("Login failed. Cannot continue with the test.")
