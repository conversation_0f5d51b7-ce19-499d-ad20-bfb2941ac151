import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Typo<PERSON>,
  Button,
  Paper,
  TextField,
  CircularProgress,
  Alert,
  Grid,
  Card,
  CardContent,
  Divider
} from '@mui/material';
import { authenticatedFetch } from '../services/tokenService';
import { fetchTenantApi, ensureToken, ensureSchema } from '../utils/apiUtils';
import { fixAuthentication, diagnoseAuthentication } from '../utils/authFix';

const TokenTest: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [result, setResult] = useState<string | null>(null);
  const [schema, setSchema] = useState<string>('gondar_city');
  const [token, setToken] = useState<string>('');
  const [url, setUrl] = useState<string>('/api/tenant/gondar_city/citizens/?limit=1');
  const [diagnosticResult, setDiagnosticResult] = useState<string>('');

  // Load token from localStorage on component mount
  useEffect(() => {
    const storedToken = localStorage.getItem('token');
    if (storedToken) {
      setToken(storedToken);
    }
  }, []);

  const handleTestRequest = async () => {
    setLoading(true);
    setError(null);
    setResult(null);

    try {
      // Log current localStorage state
      console.log('Current localStorage state:');
      console.log('token:', localStorage.getItem('token'));
      console.log('schema_name:', localStorage.getItem('schema_name'));
      console.log('tenant:', localStorage.getItem('tenant'));
      console.log('tokenStore:', localStorage.getItem('tokenStore'));

      // Log all cookies
      console.log('All cookies:', document.cookie);

      // Create headers with the token
      const headers = new Headers({
        'Authorization': `Token ${token}`,
        'Content-Type': 'application/json'
      });

      // Make the request
      console.log(`Making request to ${url} with token: ${token}`);
      const response = await fetch(url, {
        method: 'GET',
        headers,
        credentials: 'include'
      });

      console.log('Response status:', response.status);

      if (response.ok) {
        const data = await response.json();
        setResult(JSON.stringify(data, null, 2));
      } else {
        const errorText = await response.text();
        setError(`Request failed with status ${response.status}: ${errorText}`);
      }
    } catch (error) {
      console.error('Error making request:', error);
      setError(`Error making request: ${error instanceof Error ? error.message : String(error)}`);
    } finally {
      setLoading(false);
    }
  };

  const handleTestWithAuthenticatedFetch = async () => {
    setLoading(true);
    setError(null);
    setResult(null);

    try {
      // Set schema in localStorage
      localStorage.setItem('schema_name', schema);

      // Log current localStorage state
      console.log('Current localStorage state before authenticatedFetch:');
      console.log('token:', localStorage.getItem('token'));
      console.log('schema_name:', localStorage.getItem('schema_name'));
      console.log('tenant:', localStorage.getItem('tenant'));
      console.log('tokenStore:', localStorage.getItem('tokenStore'));

      // Make the request using authenticatedFetch
      console.log(`Making request to ${url} using authenticatedFetch`);
      const response = await authenticatedFetch(url);

      console.log('Response status:', response.status);

      if (response.ok) {
        const data = await response.json();
        setResult(JSON.stringify(data, null, 2));
      } else {
        const errorText = await response.text();
        setError(`Request failed with status ${response.status}: ${errorText}`);
      }
    } catch (error) {
      console.error('Error making request:', error);
      setError(`Error making request: ${error instanceof Error ? error.message : String(error)}`);
    } finally {
      setLoading(false);
    }
  };

  const handleTestWithApiUtils = async () => {
    setLoading(true);
    setError(null);
    setResult(null);

    try {
      // Ensure token and schema are properly set
      ensureToken();
      ensureSchema(schema);

      // Log current localStorage state
      console.log('Current localStorage state before fetchTenantApi:');
      console.log('token:', localStorage.getItem('token'));
      console.log('schema_name:', localStorage.getItem('schema_name'));

      // Extract the endpoint from the URL
      // The URL format is /api/tenant/{schema}/{endpoint}
      const urlParts = url.split('/');
      const endpointIndex = urlParts.indexOf(schema) + 1;
      const endpoint = urlParts.slice(endpointIndex).join('/');

      console.log(`Making request to endpoint ${endpoint} for schema ${schema} using fetchTenantApi`);

      // Make the request using fetchTenantApi
      const response = await fetchTenantApi(schema, endpoint);

      console.log('Response status:', response.status);

      if (response.ok) {
        const data = await response.json();
        setResult(JSON.stringify(data, null, 2));
      } else {
        const errorText = await response.text();
        setError(`Request failed with status ${response.status}: ${errorText}`);
      }
    } catch (error) {
      console.error('Error making request:', error);
      setError(`Error making request: ${error instanceof Error ? error.message : String(error)}`);
    } finally {
      setLoading(false);
    }
  };

  const handleSaveToken = () => {
    localStorage.setItem('token', token);
    localStorage.setItem('schema_name', schema);
    alert('Token and schema saved to localStorage');
  };

  const handleDiagnose = () => {
    const diagnosis = diagnoseAuthentication();
    setDiagnosticResult(diagnosis.message);
  };

  const handleFix = () => {
    const result = fixAuthentication(schema);
    if (result.success) {
      setDiagnosticResult(`Fix applied successfully: ${result.message}`);
    } else {
      setError(result.message);
    }
  };

  const handleFixTokenFormat = () => {
    // Get the current token
    const currentToken = localStorage.getItem('token');
    if (!currentToken) {
      setError('No token found in localStorage');
      return;
    }

    // Clean the token thoroughly - remove ALL spaces
    const cleanToken = currentToken.replace(/\s+/g, '');

    // Update the token in localStorage
    localStorage.setItem('token', cleanToken);

    // Update the token in the tokenStore
    try {
      const tokenStoreStr = localStorage.getItem('tokenStore');
      let tokenStore = tokenStoreStr ? JSON.parse(tokenStoreStr) : {};

      // Update the token for all schemas
      Object.keys(tokenStore).forEach(schemaKey => {
        tokenStore[schemaKey] = cleanToken;
      });

      // Also set it for the current schema
      tokenStore[schema] = cleanToken;

      // Save the tokenStore back to localStorage
      localStorage.setItem('tokenStore', JSON.stringify(tokenStore));

      setDiagnosticResult(`Token format fixed. Original token had ${currentToken.length} characters, cleaned token has ${cleanToken.length} characters.\n\nAll spaces have been removed from the token.`);

      // Update the token in the form
      setToken(cleanToken);
    } catch (error) {
      console.error('Error updating tokenStore:', error);
      setError('Error updating tokenStore');
    }
  };

  return (
    <Container maxWidth="md" sx={{ py: 4 }}>
      <Typography variant="h4" gutterBottom>
        Token Test Page
      </Typography>
      <Typography variant="body1" paragraph>
        Use this page to test API requests with different tokens and schemas.
      </Typography>

      <Paper sx={{ p: 3, mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          Current State
        </Typography>
        <Grid container spacing={2}>
          <Grid item xs={12} md={6}>
            <Card variant="outlined">
              <CardContent>
                <Typography variant="subtitle1">LocalStorage</Typography>
                <Typography variant="body2">
                  token: {localStorage.getItem('token') || 'not set'}
                </Typography>
                <Typography variant="body2">
                  schema_name: {localStorage.getItem('schema_name') || 'not set'}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} md={6}>
            <Card variant="outlined">
              <CardContent>
                <Typography variant="subtitle1">Cookies</Typography>
                <Typography variant="body2" sx={{ wordBreak: 'break-all' }}>
                  {document.cookie || 'No cookies set'}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </Paper>

      <Paper sx={{ p: 3, mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          Test Configuration
        </Typography>
        <Grid container spacing={2}>
          <Grid item xs={12}>
            <TextField
              fullWidth
              label="API URL"
              value={url}
              onChange={(e) => setUrl(e.target.value)}
              margin="normal"
              variant="outlined"
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label="Schema Name"
              value={schema}
              onChange={(e) => setSchema(e.target.value)}
              margin="normal"
              variant="outlined"
              helperText="The schema name to use for the request"
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label="Token"
              value={token}
              onChange={(e) => setToken(e.target.value)}
              margin="normal"
              variant="outlined"
              helperText="The authentication token to use for the request"
            />
          </Grid>
          <Grid item xs={12}>
            <Button
              variant="contained"
              color="secondary"
              onClick={handleSaveToken}
              sx={{ mr: 2 }}
            >
              Save Token & Schema to LocalStorage
            </Button>
            <Button
              variant="contained"
              color="info"
              onClick={handleDiagnose}
              sx={{ mr: 2 }}
            >
              Diagnose Authentication
            </Button>
            <Button
              variant="contained"
              color="warning"
              onClick={handleFix}
              sx={{ mr: 2 }}
            >
              Fix Authentication
            </Button>
            <Button
              variant="contained"
              color="error"
              onClick={handleFixTokenFormat}
            >
              Fix Token Format
            </Button>
          </Grid>

          {diagnosticResult && (
            <Grid item xs={12} sx={{ mt: 2 }}>
              <Paper variant="outlined" sx={{ p: 2 }}>
                <Typography variant="subtitle1" gutterBottom>
                  Diagnostic Result:
                </Typography>
                <Typography
                  variant="body2"
                  component="pre"
                  sx={{
                    whiteSpace: 'pre-wrap',
                    fontFamily: 'monospace',
                    fontSize: '0.875rem'
                  }}
                >
                  {diagnosticResult}
                </Typography>
              </Paper>
            </Grid>
          )}
        </Grid>
      </Paper>

      <Paper sx={{ p: 3, mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          Test API Request
        </Typography>
        <Grid container spacing={2}>
          <Grid item xs={12} md={6}>
            <Button
              fullWidth
              variant="contained"
              color="primary"
              onClick={handleTestRequest}
              disabled={loading}
            >
              {loading ? <CircularProgress size={24} /> : 'Test Direct Request'}
            </Button>
          </Grid>
          <Grid item xs={12} md={6}>
            <Button
              fullWidth
              variant="contained"
              color="primary"
              onClick={handleTestWithAuthenticatedFetch}
              disabled={loading}
            >
              {loading ? <CircularProgress size={24} /> : 'Test with authenticatedFetch'}
            </Button>
          </Grid>
          <Grid item xs={12}>
            <Button
              fullWidth
              variant="contained"
              color="secondary"
              onClick={handleTestWithApiUtils}
              disabled={loading}
            >
              {loading ? <CircularProgress size={24} /> : 'Test with apiUtils'}
            </Button>
          </Grid>
        </Grid>

        {error && (
          <Alert severity="error" sx={{ mt: 2 }}>
            {error}
          </Alert>
        )}

        {result && (
          <Box sx={{ mt: 2 }}>
            <Typography variant="subtitle1" gutterBottom>
              Response:
            </Typography>
            <Paper
              variant="outlined"
              sx={{
                p: 2,
                maxHeight: '300px',
                overflow: 'auto',
                fontFamily: 'monospace',
                fontSize: '0.875rem',
                whiteSpace: 'pre-wrap',
                wordBreak: 'break-word'
              }}
            >
              {result}
            </Paper>
          </Box>
        )}
      </Paper>
    </Container>
  );
};

export default TokenTest;
