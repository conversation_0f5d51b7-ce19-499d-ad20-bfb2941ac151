# Generated by Django 5.1.7 on 2025-04-18 15:00

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('common', '0006_populate_ketenas'),
    ]

    operations = [
        migrations.CreateModel(
            name='DocumentType',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True)),
                ('description', models.TextField(blank=True, null=True)),
            ],
            options={
                'verbose_name': 'Document Type',
                'verbose_name_plural': 'Document Types',
                'ordering': ['name'],
            },
        ),
    ]
