import { useAuth } from '../contexts/AuthContext';
import { 
  hasRoutePermission, 
  hasFeaturePermission, 
  Feature, 
  UserRole 
} from '../utils/rolePermissions';
import { useLocation } from 'react-router-dom';

/**
 * Custom hook for checking user permissions
 */
export const usePermissions = () => {
  const { user } = useAuth();
  const location = useLocation();
  
  // Get user role from user object or sessionStorage
  const userRole = user?.role || sessionStorage.getItem('user_role') as UserRole;
  
  // Check if user has permission to access current route
  const canAccessCurrentRoute = (): boolean => {
    return hasRoutePermission(userRole, location.pathname);
  };
  
  // Check if user has permission to access a specific route
  const canAccessRoute = (route: string): boolean => {
    return hasRoutePermission(userRole, route);
  };
  
  // Check if user has permission to use a specific feature
  const canUseFeature = (feature: Feature): boolean => {
    return hasFeaturePermission(userRole, feature);
  };
  
  return {
    userRole,
    canAccessCurrentRoute,
    canAccessRoute,
    canUseFeature
  };
};

export default usePermissions;
