import os
import django

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'neocamelot.settings')
django.setup()

from django.contrib.auth import get_user_model
from rest_framework.authtoken.models import Token
from django.db import connection

User = get_user_model()

def list_all_users():
    """List all users in the public schema."""
    print("=== Users in Public Schema ===")
    users = User.objects.all()

    print(f"Found {users.count()} users in public schema")

    for user in users:
        print(f"User ID: {user.id}, Email: {user.email}, Name: {user.first_name} {user.last_name}")

        # Print role if it exists
        if hasattr(user, 'role'):
            print(f"  Role: {user.role}")

        # Check if the user has a token
        try:
            token = Token.objects.get(user=user)
            print(f"  Token: {token.key}")
        except Token.DoesNotExist:
            print("  No token found")

def create_token_for_user(email):
    """Create a token for a user."""
    print(f"\n=== Creating Token for User {email} ===")

    try:
        user = User.objects.get(email=email)
        print(f"User {user.email} found in public schema")

        # Create or get token
        token, created = Token.objects.get_or_create(user=user)

        if created:
            print(f"Token created for user {user.email}: {token.key}")
        else:
            print(f"Token already exists for user {user.email}: {token.key}")

        return token.key
    except User.DoesNotExist:
        print(f"User {email} not found in public schema")
        return None

def create_subcity_admin_user():
    """Create a subcity admin user in the public schema."""
    print("\n=== Creating Subcity Admin User ===")

    email = "<EMAIL>"
    password = "password123"
    first_name = "Subcity"
    last_name = "Admin"
    role = "SUBCITY_ADMIN"

    # Check if the user already exists
    try:
        user = User.objects.get(email=email)
        print(f"User {email} already exists")
    except User.DoesNotExist:
        # Create the user
        user = User.objects.create_user(
            email=email,
            password=password,
            first_name=first_name,
            last_name=last_name
        )
        # Set the role if the field exists
        if hasattr(user, 'role'):
            user.role = role
            user.save()
        print(f"User {email} created")

    # Create or get token
    token, created = Token.objects.get_or_create(user=user)

    if created:
        print(f"Token created for user {email}: {token.key}")
    else:
        print(f"Token already exists for user {email}: {token.key}")

    return token.key

def main():
    print("=== Token Generator ===")

    # List all users
    list_all_users()

    # Create a subcity admin user
    subcity_admin_token = create_subcity_admin_user()

    print("\n=== Token Generation Complete ===")
    print(f"Subcity Admin Token: {subcity_admin_token}")
    print("Use this token in the frontend by setting localStorage.token = 'YOUR_TOKEN'")

if __name__ == "__main__":
    main()
