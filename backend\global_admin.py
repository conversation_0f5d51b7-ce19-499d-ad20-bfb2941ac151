from django.contrib import admin
from citizens.models import Citizen
from idcards.models import IDCard, IDCardTemplate
from centers.models import Client
from django_tenants.utils import tenant_context, schema_context
from django.db.models import Char<PERSON><PERSON>, Value
from django.db.models.functions import Concat
from itertools import chain

# Create a global admin class for Citizen that works across all tenants
class GlobalCitizenAdmin(admin.ModelAdmin):
    list_display = ('schema_name', 'registration_number', 'first_name', 'last_name', 'gender', 'date_of_birth', 'is_active')
    list_filter = ('is_active', 'gender', 'nationality')
    search_fields = ('first_name', 'last_name', 'registration_number', 'id_number', 'email', 'phone')
    readonly_fields = ('schema_name', 'registration_number', 'id_number', 'created_at', 'updated_at', 'created_by')

    def get_queryset(self, request):
        # Get all tenants
        tenants = Client.objects.exclude(schema_name='public')
        
        # Initialize an empty list to store citizens from all tenants
        all_citizens = []
        
        # Iterate through each tenant and get their citizens
        for tenant in tenants:
            try:
                with tenant_context(tenant):
                    # Get citizens for this tenant
                    citizens = list(Citizen.objects.all())
                    
                    # Add schema_name attribute to each citizen
                    for citizen in citizens:
                        citizen.schema_name = tenant.schema_name
                    
                    # Add citizens to the list
                    all_citizens.extend(citizens)
            except Exception as e:
                print(f"Error accessing tenant {tenant.schema_name}: {str(e)}")
        
        # Return a queryset-like object
        return all_citizens
    
    def schema_name(self, obj):
        return obj.schema_name if hasattr(obj, 'schema_name') else 'Unknown'
    schema_name.short_description = 'Tenant'
    
    def has_add_permission(self, request):
        return False
    
    def has_change_permission(self, request, obj=None):
        return False
    
    def has_delete_permission(self, request, obj=None):
        return False

# Create a global admin class for IDCardTemplate that works across all tenants
class GlobalIDCardTemplateAdmin(admin.ModelAdmin):
    list_display = ('schema_name', 'name', 'is_default', 'created_at')
    list_filter = ('is_default',)
    search_fields = ('name',)
    readonly_fields = ('schema_name', 'created_at', 'updated_at')

    def get_queryset(self, request):
        # Get all tenants
        tenants = Client.objects.exclude(schema_name='public')
        
        # Initialize an empty list to store templates from all tenants
        all_templates = []
        
        # Iterate through each tenant and get their templates
        for tenant in tenants:
            try:
                with tenant_context(tenant):
                    # Get templates for this tenant
                    templates = list(IDCardTemplate.objects.all())
                    
                    # Add schema_name attribute to each template
                    for template in templates:
                        template.schema_name = tenant.schema_name
                    
                    # Add templates to the list
                    all_templates.extend(templates)
            except Exception as e:
                print(f"Error accessing tenant {tenant.schema_name}: {str(e)}")
        
        # Return a queryset-like object
        return all_templates
    
    def schema_name(self, obj):
        return obj.schema_name if hasattr(obj, 'schema_name') else 'Unknown'
    schema_name.short_description = 'Tenant'
    
    def has_add_permission(self, request):
        return False
    
    def has_change_permission(self, request, obj=None):
        return False
    
    def has_delete_permission(self, request, obj=None):
        return False

# Create a global admin class for IDCard that works across all tenants
class GlobalIDCardAdmin(admin.ModelAdmin):
    list_display = ('schema_name', 'card_number', 'citizen_name', 'issue_date', 'expiry_date', 'status')
    list_filter = ('status', 'issue_date')
    search_fields = ('card_number',)
    readonly_fields = ('schema_name', 'card_number', 'created_at', 'updated_at', 'created_by', 'approved_by')

    def get_queryset(self, request):
        # Get all tenants
        tenants = Client.objects.exclude(schema_name='public')
        
        # Initialize an empty list to store ID cards from all tenants
        all_cards = []
        
        # Iterate through each tenant and get their ID cards
        for tenant in tenants:
            try:
                with tenant_context(tenant):
                    # Get ID cards for this tenant
                    cards = list(IDCard.objects.all())
                    
                    # Add schema_name attribute to each card
                    for card in cards:
                        card.schema_name = tenant.schema_name
                    
                    # Add cards to the list
                    all_cards.extend(cards)
            except Exception as e:
                print(f"Error accessing tenant {tenant.schema_name}: {str(e)}")
        
        # Return a queryset-like object
        return all_cards
    
    def schema_name(self, obj):
        return obj.schema_name if hasattr(obj, 'schema_name') else 'Unknown'
    schema_name.short_description = 'Tenant'
    
    def citizen_name(self, obj):
        return f"{obj.citizen.first_name} {obj.citizen.last_name}" if obj.citizen else 'Unknown'
    citizen_name.short_description = 'Citizen'
    
    def has_add_permission(self, request):
        return False
    
    def has_change_permission(self, request, obj=None):
        return False
    
    def has_delete_permission(self, request, obj=None):
        return False

# Register the models with the admin site
admin.site.register(Citizen, GlobalCitizenAdmin)
admin.site.register(IDCardTemplate, GlobalIDCardTemplateAdmin)
admin.site.register(IDCard, GlobalIDCardAdmin)
