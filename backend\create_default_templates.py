import os
import django

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'neocamelot.settings')
django.setup()

# Import models
from idcards.models import IDCardTemplate
from centers.models import Center, Client
from django_tenants.utils import tenant_context, schema_context

# Create default ID card templates for each tenant
print("Creating default ID card templates for each tenant:")
tenants = Client.objects.exclude(schema_name='public')
for tenant in tenants:
    print(f"\nChecking tenant: {tenant.name} (schema: {tenant.schema_name})")
    try:
        with tenant_context(tenant):
            # Get all centers in this tenant
            centers = Center.objects.all()
            print(f"Found {centers.count()} centers in {tenant.schema_name}")
            
            # Create a default template for each center
            for center in centers:
                # Check if the center already has a template
                templates = IDCardTemplate.objects.filter(center=center)
                if templates.exists():
                    print(f"Center '{center.name}' already has {templates.count()} templates")
                else:
                    # Create a default template
                    template = IDCardTemplate.objects.create(
                        name="Default Template",
                        center=center,
                        is_default=True,
                        front_layout={
                            'title': {'x': 10, 'y': 10, 'font_size': 18},
                            'photo': {'x': 20, 'y': 40, 'width': 100, 'height': 120},
                            'name': {'x': 130, 'y': 50, 'font_size': 14},
                            'id_number': {'x': 130, 'y': 70, 'font_size': 12},
                        },
                        back_layout={
                            'address': {'x': 10, 'y': 10, 'font_size': 12},
                            'issue_date': {'x': 10, 'y': 30, 'font_size': 12},
                            'expiry_date': {'x': 10, 'y': 50, 'font_size': 12},
                        }
                    )
                    print(f"Created default template for center '{center.name}'")
    except Exception as e:
        print(f"Error accessing tenant schema: {str(e)}")

print("\nDone!")
