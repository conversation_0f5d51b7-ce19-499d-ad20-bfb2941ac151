import os
import django

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'neocamelot.settings')
django.setup()

# Import models and admin
from django.contrib import admin
from centers.models import Client
from django_tenants.utils import tenant_context, schema_context

# Check admin site configuration
print("Admin site configuration:")
print(f"Admin site name: {admin.site.name}")
print(f"Admin site site_header: {admin.site.site_header}")
print(f"Admin site site_title: {admin.site.site_title}")
print(f"Admin site index_title: {admin.site.index_title}")

# Check if the admin site is using the correct registry
print("\nAdmin site registry:")
print(f"Number of registered models: {len(admin.site._registry)}")
print(f"Registered models: {[model.__name__ for model, _ in admin.site._registry.items()]}")

# Check if the models are registered in the public schema
with schema_context('public'):
    print("\nModels registered in public schema:")
    print(f"Number of registered models: {len(admin.site._registry)}")
    print(f"Registered models: {[model.__name__ for model, _ in admin.site._registry.items()]}")

# Check if the models are registered in tenant schemas
tenants = Client.objects.exclude(schema_name='public')
for tenant in tenants:
    print(f"\nChecking tenant: {tenant.name} (schema: {tenant.schema_name})")
    try:
        with tenant_context(tenant):
            print(f"Models registered in {tenant.schema_name} schema:")
            print(f"Number of registered models: {len(admin.site._registry)}")
            print(f"Registered models: {[model.__name__ for model, _ in admin.site._registry.items()]}")
    except Exception as e:
        print(f"Error accessing tenant schema: {str(e)}")
