from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from .models import Kebele, CenterType
import json

@csrf_exempt
def centers_json(request):
    """Return all centers as JSON."""
    centers = Kebele.objects.all()
    data = [
        {
            'id': center.id,
            'name': center.name,
            'code': center.code,
            'slug': center.slug,
            'type': center.type.name if center.type else None,
            'subcity': center.subcity.name if center.subcity else None,
            'subcity_id': center.subcity.id if center.subcity else None,
            'city': center.subcity.city.name if center.subcity and center.subcity.city else None,
            'city_id': center.subcity.city.id if center.subcity and center.subcity.city else None,
            'description': center.description,
            'address': center.address,
            'state': center.state,
            'email': center.email,
            'phone': center.phone,
            'website': center.website,
            'is_active': center.is_active,
            'is_verified': center.is_verified,
        }
        for center in centers
    ]
    return JsonResponse(data, safe=False)

@csrf_exempt
def center_types_json(request):
    """Return all center types as JSON."""
    types = CenterType.objects.all()
    data = [
        {
            'id': type_obj.id,
            'name': type_obj.name,
            'description': type_obj.description,
            'is_active': type_obj.is_active,
        }
        for type_obj in types
    ]
    return JsonResponse(data, safe=False)

@csrf_exempt
def create_center(request):
    """Create a new center."""
    if request.method != 'POST':
        return JsonResponse({'error': 'Only POST method is allowed'}, status=405)

    try:
        data = json.loads(request.body)
    except json.JSONDecodeError:
        return JsonResponse({'error': 'Invalid JSON'}, status=400)

    # Validate required fields
    required_fields = ['name', 'address']
    for field in required_fields:
        if field not in data:
            return JsonResponse({'error': f'{field} is required'}, status=400)

    # Create the center
    center = Kebele(
        name=data['name'],
        address=data['address']
    )

    # Set optional fields if provided
    optional_fields = [
        'description', 'city', 'state', 'postal_code', 'country',
        'phone', 'alternate_phone', 'email', 'website',
        'header_color', 'accent_color',
        'admin_name', 'admin_email', 'admin_phone',
        'max_users', 'max_citizens', 'max_id_cards',
        'is_active', 'is_verified', 'subscription_status'
    ]

    for field in optional_fields:
        if field in data:
            setattr(center, field, data[field])

    # Set center type if provided
    if 'type_id' in data:
        try:
            center_type = CenterType.objects.get(id=data['type_id'])
            center.type = center_type
        except CenterType.DoesNotExist:
            return JsonResponse({'error': f'Center type with ID {data["type_id"]} does not exist'}, status=400)

    # Save the center
    center.save()

    # Return the created center
    return JsonResponse({
        'id': center.id,
        'name': center.name,
        'code': center.code,
        'slug': center.slug,
        'type': center.type.name if center.type else None,
        'description': center.description,
        'address': center.address,
        'city': center.city,
        'state': center.state,
        'email': center.email,
        'phone': center.phone,
        'website': center.website,
        'is_active': center.is_active,
        'is_verified': center.is_verified,
    }, status=201)
