from django.db import models
import uuid
from datetime import date, timedelta
from centers.models_base import TenantModel

class IDCardTemplate(TenantModel):
    """Model for ID card templates that can be used by centers."""
    name = models.CharField(max_length=100)
    background_image = models.ImageField(upload_to='id_templates/', blank=True, null=True)
    is_default = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    # Template configuration (stored as JSON)
    front_layout = models.JSONField(default=dict)
    back_layout = models.JSONField(default=dict)

    class Meta:
        unique_together = ['center', 'name']

    def __str__(self):
        return f"{self.center.name} - {self.name}"

    def save(self, *args, **kwargs):
        # First ensure we have a center set (from TenantModel)
        super().save(*args, **kwargs)

        # If this template is set as default, unset default for other templates
        if self.is_default:
            IDCardTemplate.objects.filter(
                center=self.center,
                is_default=True
            ).exclude(pk=self.pk).update(is_default=False)

class IDCard(TenantModel):
    """Model representing an ID card issued to a citizen."""
    STATUS_CHOICES = (
        ('DRAFT', 'Draft'),
        ('PENDING', 'Pending Approval'),
        ('APPROVED', 'Approved'),
        ('PENDING_SUBCITY', 'Pending Subcity Processing'),
        ('PRINTED', 'Printed'),
        ('ISSUED', 'Issued'),
        ('EXPIRED', 'Expired'),
        ('REVOKED', 'Revoked'),
    )

    DOCUMENT_VERIFICATION_CHOICES = (
        ('NOT_VERIFIED', 'Not Verified'),
        ('VERIFIED', 'Verified'),
        ('REJECTED', 'Rejected'),
    )

    citizen = models.ForeignKey('citizens.Citizen', on_delete=models.CASCADE, related_name='id_cards')
    template = models.ForeignKey(IDCardTemplate, on_delete=models.SET_NULL, null=True, related_name='id_cards')
    card_number = models.CharField(max_length=50, unique=True, blank=True)
    issue_date = models.DateField(default=date.today)
    expiry_date = models.DateField(blank=True, null=True)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='DRAFT')

    # Document verification fields
    document_verification_required = models.BooleanField(
        default=False,  # Changed to False to make document verification optional by default
        help_text='Whether document verification is required for this ID card'
    )
    document_verification_status = models.CharField(
        max_length=20,
        choices=DOCUMENT_VERIFICATION_CHOICES,
        default='NOT_VERIFIED',
        help_text='Verification status of the citizen\'s documents'
    )
    document_verification_notes = models.TextField(
        blank=True,
        null=True,
        help_text='Notes about document verification'
    )
    document_verified_at = models.DateTimeField(blank=True, null=True)
    document_verified_by = models.ForeignKey(
        'accounts.User',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='verified_documents'
    )

    # Kebele leader approval fields
    APPROVAL_CHOICES = (
        ('PENDING', 'Pending'),
        ('APPROVED', 'Approved'),
        ('REJECTED', 'Rejected'),
    )
    kebele_approval_status = models.CharField(
        max_length=20,
        choices=APPROVAL_CHOICES,
        default='PENDING',
        help_text='Approval status by the kebele leader'
    )
    kebele_approval_notes = models.TextField(
        blank=True,
        null=True,
        help_text='Notes from the kebele leader regarding approval or rejection'
    )
    kebele_approved_at = models.DateTimeField(blank=True, null=True)
    kebele_approved_by = models.ForeignKey(
        'accounts.User',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='kebele_approved_cards'
    )

    # Security pattern fields
    kebele_pattern = models.CharField(
        max_length=255,
        blank=True,
        null=True,
        help_text='First half of the security pattern applied by kebele leader'
    )
    subcity_pattern = models.CharField(
        max_length=255,
        blank=True,
        null=True,
        help_text='Second half of the security pattern applied by subcity leader'
    )
    pattern_seed = models.CharField(
        max_length=64,
        blank=True,
        null=True,
        help_text='Seed used to generate the security pattern'
    )


    # Card data (can store custom fields)
    card_data = models.JSONField(default=dict)

    # System fields
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey('accounts.User', on_delete=models.SET_NULL, null=True, related_name='created_cards')
    approved_by = models.ForeignKey('accounts.User', on_delete=models.SET_NULL, null=True, blank=True, related_name='approved_cards')

    class Meta:
        ordering = ['-created_at']

    def __str__(self):
        return f"ID Card: {self.card_number} - {self.citizen.full_name}"

    def save(self, *args, **kwargs):
        # First ensure we have a center set (from TenantModel)
        # For IDCard, we get the center from the citizen
        if self.citizen and self.citizen.center:
            self.center = self.citizen.center

        # Ensure we have a card_number before saving to avoid unique constraint violations
        if not self.card_number:
            # Generate a temporary card number if none exists
            import uuid
            import time
            self.card_number = f"TEMP-{int(time.time())}-{str(uuid.uuid4())[:8]}"

        super().save(*args, **kwargs)

        needs_save = False

        # If the card number is temporary, generate a proper one
        if self.card_number and self.card_number.startswith('TEMP-'):
            # Import here to avoid circular imports
            from .utils import generate_id_card_number, get_id_card_format

            # We can't use get_tenant() here as it requires a request object
            # Instead, use a simple format for now
            id_format = "{RANDOM8}"

            # Generate a unique card number
            self.card_number = generate_id_card_number(
                citizen=self.citizen,
                center=self.center,
                tenant=None,  # No tenant needed for this format
                format_string=id_format
            )
            needs_save = True

        if not self.expiry_date:
            # Default expiry date is 5 years from issue date
            self.expiry_date = self.issue_date + timedelta(days=5*365)
            needs_save = True

        if needs_save:
            # Use update to avoid infinite recursion
            IDCard.objects.filter(pk=self.pk).update(
                card_number=self.card_number,
                expiry_date=self.expiry_date
            )

    @property
    def is_expired(self):
        return date.today() > self.expiry_date if self.expiry_date else False

    @property
    def days_until_expiry(self):
        if not self.expiry_date:
            return None
        delta = self.expiry_date - date.today()
        return delta.days if delta.days > 0 else 0
