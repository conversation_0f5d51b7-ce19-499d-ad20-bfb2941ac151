from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from django_tenants.utils import tenant_context
from centers.models import Client, Center

User = get_user_model()

class Command(BaseCommand):
    help = 'Create a kebele leader (liqe menber) user for a specific kebele tenant'

    def add_arguments(self, parser):
        parser.add_argument('--schema', type=str, required=True, help='Schema name of the kebele tenant')
        parser.add_argument('--email', type=str, required=True, help='Email for the kebele leader')
        parser.add_argument('--password', type=str, required=True, help='Password for the kebele leader')
        parser.add_argument('--first_name', type=str, required=True, help='First name of the kebele leader')
        parser.add_argument('--last_name', type=str, required=True, help='Last name of the kebele leader')

    def handle(self, *args, **options):
        schema_name = options['schema']
        email = options['email']
        password = options['password']
        first_name = options['first_name']
        last_name = options['last_name']

        try:
            # Get the tenant
            tenant = Client.objects.get(schema_name=schema_name)
            
            # Check if the tenant is a kebele
            if tenant.schema_type != 'KEBELE':
                self.stdout.write(self.style.ERROR(f'Tenant {schema_name} is not a kebele tenant'))
                return
                
            # Switch to the tenant context
            with tenant_context(tenant):
                # Check if user already exists
                if User.objects.filter(email=email).exists():
                    self.stdout.write(self.style.WARNING(f'User with email {email} already exists in tenant {schema_name}'))
                    return
                
                # Get the first center in the tenant
                center = Center.objects.first()
                if not center:
                    self.stdout.write(self.style.ERROR(f'No center found in tenant {schema_name}'))
                    return
                
                # Create the kebele leader user
                user = User.objects.create_user(
                    email=email,
                    password=password,
                    first_name=first_name,
                    last_name=last_name,
                    role='KEBELE_LEADER',
                    center=center,
                    is_active=True
                )
                
                self.stdout.write(self.style.SUCCESS(f'Successfully created kebele leader {email} in tenant {schema_name}'))
                
        except Client.DoesNotExist:
            self.stdout.write(self.style.ERROR(f'Tenant with schema {schema_name} does not exist'))
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'Error creating kebele leader: {str(e)}'))
