/**
 * Emergency Token Fix Script
 * 
 * This script provides a direct fix for token-related authentication issues.
 * It will:
 * 1. Clean the token in localStorage
 * 2. Update the tokenStore
 * 3. Set the token in cookies
 * 4. Reload the page
 */

(function() {
  console.log('Running emergency token fix...');
  
  // Get the current token
  const token = localStorage.getItem('token');
  if (!token) {
    console.error('No token found in localStorage. Please log in again.');
    return;
  }
  
  // Clean the token by removing ALL characters that aren't alphanumeric or specific symbols
  // This is more aggressive than just removing spaces
  const cleanToken = token.replace(/[^a-zA-Z0-9_\-\.]/g, '');
  console.log('Original token:', token);
  console.log('Cleaned token:', cleanToken);
  console.log('Original length:', token.length);
  console.log('Cleaned length:', cleanToken.length);
  console.log('Difference:', token.length - cleanToken.length);
  
  // Update the token in localStorage
  localStorage.setItem('token', cleanToken);
  
  // Get the schema name
  const schema = localStorage.getItem('schema_name');
  if (schema) {
    console.log('Schema name:', schema);
    
    // Update the tokenStore
    try {
      const tokenStoreStr = localStorage.getItem('tokenStore');
      let tokenStore = tokenStoreStr ? JSON.parse(tokenStoreStr) : {};
      
      // Update the token for the schema
      tokenStore[schema] = cleanToken;
      
      // Save the tokenStore
      localStorage.setItem('tokenStore', JSON.stringify(tokenStore));
      console.log('Updated tokenStore');
    } catch (error) {
      console.error('Error updating tokenStore:', error);
    }
    
    // Set the token in cookies
    document.cookie = `auth_token=${encodeURIComponent(cleanToken)}; path=/; SameSite=Lax`;
    document.cookie = `schema_name=${encodeURIComponent(schema)}; path=/; SameSite=Lax`;
    console.log('Updated cookies');
  } else {
    console.warn('No schema name found in localStorage');
  }
  
  // Get the tenant
  try {
    const tenantStr = localStorage.getItem('tenant');
    if (tenantStr) {
      const tenant = JSON.parse(tenantStr);
      if (tenant && tenant.schema_name) {
        console.log('Tenant schema name:', tenant.schema_name);
        
        // Update the tokenStore for the tenant schema
        const tokenStoreStr = localStorage.getItem('tokenStore');
        let tokenStore = tokenStoreStr ? JSON.parse(tokenStoreStr) : {};
        
        // Update the token for the tenant schema
        tokenStore[tenant.schema_name] = cleanToken;
        
        // Save the tokenStore
        localStorage.setItem('tokenStore', JSON.stringify(tokenStore));
        console.log('Updated tokenStore for tenant schema');
        
        // Set the schema name in localStorage if not already set
        if (!schema) {
          localStorage.setItem('schema_name', tenant.schema_name);
          console.log('Set schema_name in localStorage');
          
          // Set the schema name in cookies
          document.cookie = `schema_name=${encodeURIComponent(tenant.schema_name)}; path=/; SameSite=Lax`;
          console.log('Set schema_name cookie');
        }
      }
    }
  } catch (error) {
    console.error('Error processing tenant:', error);
  }
  
  console.log('Emergency token fix complete. Reloading page in 2 seconds...');
  
  // Reload the page after a short delay
  setTimeout(() => {
    window.location.reload();
  }, 2000);
})();
