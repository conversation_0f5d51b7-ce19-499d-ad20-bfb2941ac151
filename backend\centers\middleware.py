from django.utils.deprecation import MiddlewareMixin
from django.http import HttpResponseForbidden
from threading import local
from django.db import connection
from .models import City, Subcity, Center

# Thread local storage for tenant context
_tenant_context = local()

# Thread local storage for hierarchy context
_hierarchy_context = local()

def get_current_tenant():
    """Get the current tenant from thread local storage."""
    return getattr(_tenant_context, 'tenant', None)

def set_current_tenant(tenant):
    """Set the current tenant in thread local storage."""
    _tenant_context.tenant = tenant

def get_current_city():
    """Get the current city from thread local storage."""
    return getattr(_hierarchy_context, 'city', None)

def set_current_city(city):
    """Set the current city in thread local storage."""
    _hierarchy_context.city = city

def get_current_subcity():
    """Get the current subcity from thread local storage."""
    return getattr(_hierarchy_context, 'subcity', None)

def set_current_subcity(subcity):
    """Set the current subcity in thread local storage."""
    _hierarchy_context.subcity = subcity

class TenantMiddleware(MiddlewareMixin):
    """
    Middleware to handle multi-tenancy in the application.

    This middleware:
    1. Extracts the center (tenant) from the request headers or URL
    2. Sets the current center on the request object and thread-local storage
    3. Restricts access based on the user's center
    """

    def process_request(self, request):
        # Clear context at the beginning of request
        set_current_tenant(None)
        set_current_city(None)
        set_current_subcity(None)

        # Skip for admin, API authentication URLs, and login endpoint
        if request.path.startswith('/admin/') or request.path.startswith('/api-auth/') or request.path == '/api/login/':
            return None

        # Get hierarchy parameters from headers or query parameters
        city_slug = request.headers.get('X-City') or request.GET.get('city')
        subcity_slug = request.headers.get('X-Subcity') or request.GET.get('subcity')
        center_slug = request.headers.get('X-Center') or request.GET.get('center')

        # Initialize request attributes
        request.city = None
        request.subcity = None
        request.center = None

        # Try to get city from slug
        if city_slug:
            try:
                request.city = City.objects.get(slug=city_slug, is_active=True)
                set_current_city(request.city)
            except City.DoesNotExist:
                pass

        # Try to get subcity from slug
        if subcity_slug:
            try:
                subcity_query = Subcity.objects.filter(slug=subcity_slug, is_active=True)
                if request.city:
                    subcity_query = subcity_query.filter(city=request.city)
                request.subcity = subcity_query.first()
                set_current_subcity(request.subcity)
            except Subcity.DoesNotExist:
                pass

        # Try to get center from slug
        if center_slug:
            try:
                center_query = Center.objects.filter(slug=center_slug, is_active=True)
                if request.subcity:
                    center_query = center_query.filter(subcity=request.subcity)
                elif request.city:
                    center_query = center_query.filter(subcity__city=request.city)
                request.center = center_query.first()
            except Center.DoesNotExist:
                pass

        # If no hierarchy is specified in the request but user has a role, use that
        if hasattr(request, 'user') and request.user.is_authenticated:
            # For super admins, don't set any tenant to allow access to all data
            if hasattr(request.user, 'is_super_admin') and request.user.is_super_admin:
                pass  # Super admins can access everything
            # For city admins, set their city
            elif hasattr(request.user, 'is_city_admin') and request.user.is_city_admin and request.user.city:
                if not request.city:
                    request.city = request.user.city
                    set_current_city(request.city)
            # For subcity admins, set their subcity
            elif hasattr(request.user, 'is_subcity_admin') and request.user.is_subcity_admin and request.user.subcity:
                if not request.subcity:
                    request.subcity = request.user.subcity
                    set_current_subcity(request.subcity)
                if not request.city and request.subcity:
                    request.city = request.subcity.city
                    set_current_city(request.city)
            # For center admins and staff, set their center
            elif hasattr(request.user, 'center') and request.user.center:
                if not request.center:
                    request.center = request.user.center
                if not request.subcity and request.center and request.center.subcity:
                    request.subcity = request.center.subcity
                    set_current_subcity(request.subcity)
                if not request.city and request.subcity and request.subcity.city:
                    request.city = request.subcity.city
                    set_current_city(request.city)

        # Set the current tenant in thread-local storage
        set_current_tenant(request.center)

        # For authenticated users, enforce hierarchy-based access control
        if request.path.startswith('/api/') and request.user.is_authenticated:
            # Super admins can access everything
            if hasattr(request.user, 'is_super_admin') and request.user.is_super_admin:
                return None

            # City admins can access their city and everything under it
            if hasattr(request.user, 'is_city_admin') and request.user.is_city_admin:
                if request.city and request.user.city != request.city:
                    return HttpResponseForbidden("You don't have access to this city")
                return None

            # Subcity admins can access their subcity and everything under it
            if hasattr(request.user, 'is_subcity_admin') and request.user.is_subcity_admin:
                if request.subcity and request.user.subcity != request.subcity:
                    return HttpResponseForbidden("You don't have access to this subcity")
                return None

            # Center admins and staff can only access their center
            if request.center and request.user.center != request.center:
                return HttpResponseForbidden("You don't have access to this center")

        return None

    def process_response(self, request, response):
        # Clear context at the end of request
        set_current_tenant(None)
        set_current_city(None)
        set_current_subcity(None)
        return response
