import os
import django
import json
import requests

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'neocamelot.settings')
django.setup()

# Base URL
base_url = 'http://localhost:8000/api/'

# Login to get token
def login():
    print("Logging in to get token...")
    url = base_url + 'login/'
    data = {
        'email': '<EMAIL>',
        'password': 'password123'
    }
    response = requests.post(url, data=data)
    print(f"Status code: {response.status_code}")

    if response.status_code == 200:
        token = response.json().get('token')
        print(f"Token: {token}")
        return token
    else:
        print(f"Error: {response.text}")
        return None

# Test API endpoints
def test_endpoint(token, endpoint):
    print(f"\nTesting {endpoint} endpoint...")
    url = base_url + endpoint + '/'
    headers = {'Authorization': f'Token {token}'}
    response = requests.get(url, headers=headers)
    print(f"Status code: {response.status_code}")

    if response.status_code == 200:
        data = response.json()
        print(f"Number of items: {len(data)}")
        if len(data) > 0:
            print(f"First item: {json.dumps(data[0], indent=2)}")
    else:
        print(f"Error: {response.text}")

# Main function
if __name__ == '__main__':
    token = login()
    if token:
        test_endpoint(token, 'citizens')
        test_endpoint(token, 'idcards')
        test_endpoint(token, 'idcard-templates')
    else:
        print("Login failed. Cannot test endpoints.")
