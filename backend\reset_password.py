import os
import django
import sys

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'neocamelot.settings')
django.setup()

# Import models
from centers.models import Client
from django.contrib.auth import get_user_model
from django_tenants.utils import tenant_context

User = get_user_model()

def reset_password(email, new_password, schema_name=None):
    """Reset password for a user in the specified schema."""
    try:
        if schema_name and schema_name != 'public':
            # Get the tenant
            try:
                tenant = Client.objects.get(schema_name=schema_name)
                print(f"Found tenant: {tenant.name} (Schema: {tenant.schema_name})")
                
                # Switch to tenant context
                with tenant_context(tenant):
                    # Find the user
                    try:
                        user = User.objects.get(email=email)
                        print(f"Found user: {user.email} (Role: {getattr(user, 'role', 'N/A')})")
                        
                        # Set new password
                        user.set_password(new_password)
                        user.save()
                        print(f"Password reset successful for {email} in {schema_name}")
                        return True
                    except User.DoesNotExist:
                        print(f"User {email} not found in {schema_name}")
                        return False
            except Client.DoesNotExist:
                print(f"Tenant with schema name {schema_name} does not exist")
                return False
        else:
            # Public schema
            try:
                user = User.objects.get(email=email)
                print(f"Found user: {user.email} (Role: {getattr(user, 'role', 'N/A')})")
                
                # Set new password
                user.set_password(new_password)
                user.save()
                print(f"Password reset successful for {email} in public schema")
                return True
            except User.DoesNotExist:
                print(f"User {email} not found in public schema")
                return False
    except Exception as e:
        print(f"Error: {str(e)}")
        return False

if __name__ == "__main__":
    # Check command line arguments
    if len(sys.argv) < 3:
        print("Usage: python reset_password.py <email> <new_password> [schema_name]")
        sys.exit(1)
    
    email = sys.argv[1]
    new_password = sys.argv[2]
    schema_name = sys.argv[3] if len(sys.argv) > 3 else None
    
    reset_password(email, new_password, schema_name)
