import React, { useEffect, useState } from 'react';
import { Box, Container, Typography } from '@mui/material';

interface PageBannerProps {
  title: string;
  subtitle?: string;
  icon?: React.ReactNode;
  actionContent?: React.ReactNode;
  color?: 'primary' | 'secondary' | 'custom';
  customColor?: string;
  children?: React.ReactNode;
}

const PageBanner: React.FC<PageBannerProps> = ({
  title,
  subtitle,
  icon,
  actionContent,
  color = 'primary',
  customColor,
  children
}) => {
  // State to store tenant colors
  const [tenantHeaderColor, setTenantHeaderColor] = useState<string | null>(null);
  const [tenantAccentColor, setTenantAccentColor] = useState<string | null>(null);

  // Get tenant colors from localStorage
  useEffect(() => {
    const tenantStr = localStorage.getItem('tenant');
    if (tenantStr) {
      try {
        const tenant = JSON.parse(tenantStr);
        if (tenant.header_color) {
          setTenantHeaderColor(tenant.header_color);
        }
        if (tenant.accent_color) {
          setTenantAccentColor(tenant.accent_color);
        }
      } catch (error) {
        console.error('Error parsing tenant data:', error);
      }
    }
  }, []);

  // Determine gradient colors based on the color prop and tenant colors
  let gradientColors = '';

  if (color === 'primary' && tenantHeaderColor) {
    // Use tenant header color if available
    const lighterColor = tenantHeaderColor;
    const darkerColor = adjustColor(tenantHeaderColor, -20); // Slightly darker version
    gradientColors = `linear-gradient(135deg, ${darkerColor} 0%, ${tenantHeaderColor} 50%, ${lighterColor} 100%)`;
  } else if (color === 'secondary' && tenantAccentColor) {
    // Use tenant accent color if available
    const lighterColor = tenantAccentColor;
    const darkerColor = adjustColor(tenantAccentColor, -20); // Slightly darker version
    gradientColors = `linear-gradient(135deg, ${darkerColor} 0%, ${tenantAccentColor} 50%, ${lighterColor} 100%)`;
  } else if (color === 'primary') {
    gradientColors = 'linear-gradient(135deg, #0066b2 0%, #0077cc 50%, #0088dd 100%)';
  } else if (color === 'secondary') {
    gradientColors = 'linear-gradient(135deg, #9c27b0 0%, #ab47bc 50%, #ba68c8 100%)';
  } else if (color === 'custom' && customColor) {
    gradientColors = `linear-gradient(135deg, ${customColor} 0%, ${customColor} 50%, ${customColor} 100%)`;
  } else {
    gradientColors = 'linear-gradient(135deg, #0066b2 0%, #0077cc 50%, #0088dd 100%)';
  }

  // Helper function to adjust color brightness
  function adjustColor(color: string, amount: number): string {
    // Remove the # if it exists
    color = color.replace('#', '');

    // Parse the color
    let r = parseInt(color.substring(0, 2), 16);
    let g = parseInt(color.substring(2, 4), 16);
    let b = parseInt(color.substring(4, 6), 16);

    // Adjust the color
    r = Math.max(0, Math.min(255, r + amount));
    g = Math.max(0, Math.min(255, g + amount));
    b = Math.max(0, Math.min(255, b + amount));

    // Convert back to hex
    return `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`;
  }

  return (
    <Box
      sx={{
        width: '100%',
        background: gradientColors,
        color: 'white',
        position: 'relative',
        overflow: 'hidden',
        pt: 7,
        pb: 9,
        mb: 5,
        boxShadow: '0 6px 30px rgba(0,0,0,0.2)',
        borderRadius: '0 0 30px 30px',
        '&::after': {
          content: '""',
          position: 'absolute',
          bottom: 0,
          left: 0,
          width: '100%',
          height: '80px',
          background: '#f8f9fa',
          borderTopLeftRadius: '50% 70%',
          borderTopRightRadius: '50% 70%',
          transform: 'scaleX(1.6)'
        },
        '&::before': {
          content: '""',
          position: 'absolute',
          top: 0,
          left: 0,
          width: '100%',
          height: '100%',
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%23ffffff' fill-opacity='0.1' fill-rule='evenodd'/%3E%3C/svg%3E")`,
          backgroundSize: '180px 180px',
          backgroundPosition: 'center',
          opacity: 0.2,
          zIndex: 0
        }
      }}
    >
      <Container maxWidth="lg">
        <Box sx={{ textAlign: 'center', position: 'relative', zIndex: 2 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', mb: 3 }}>
            {icon && (
              <Box
                sx={{
                  bgcolor: 'rgba(255,255,255,0.15)',
                  p: 2,
                  borderRadius: '50%',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  boxShadow: '0 4px 15px rgba(0,0,0,0.1)',
                  mr: 3
                }}
              >
                {icon}
              </Box>
            )}
            <Box sx={{ textAlign: icon ? 'left' : 'center' }}>
              <Typography variant="h3" sx={{ color: 'white', fontWeight: 800, textShadow: '0 2px 4px rgba(0,0,0,0.2)' }}>
                {title}
              </Typography>
              {subtitle && (
                <Typography
                  variant="h6"
                  sx={{
                    color: 'rgba(255,255,255,0.9)',
                    mt: 1,
                    fontWeight: 400,
                    textShadow: '0 1px 2px rgba(0,0,0,0.1)',
                    borderLeft: icon ? '3px solid rgba(255,255,255,0.4)' : 'none',
                    pl: icon ? 2 : 0
                  }}
                >
                  {subtitle}
                </Typography>
              )}
            </Box>
          </Box>

          {actionContent && (
            <Box
              sx={{
                display: 'flex',
                justifyContent: 'center',
                gap: 4,
                mt: 4,
                backgroundColor: 'rgba(255,255,255,0.1)',
                borderRadius: 3,
                py: 2,
                px: 4,
                maxWidth: '800px',
                mx: 'auto',
                backdropFilter: 'blur(5px)',
                boxShadow: 'inset 0 1px 1px rgba(255,255,255,0.2), 0 4px 15px rgba(0,0,0,0.1)'
              }}
            >
              {actionContent}
            </Box>
          )}

          {/* Render children if provided */}
          {children}
        </Box>
      </Container>
    </Box>
  );
};

export default PageBanner;
