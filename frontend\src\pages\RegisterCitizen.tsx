import React, { useState, useEffect, useRef, useCallback } from 'react';
import Webcam from 'react-webcam';
import { getValidToken, handleAuthError, fetchWithAuth } from '../utils/tokenRefresh';
import { saveFormData, loadFormData, hasStoredFormData, clearFormData } from '../utils/formPersistence';
import { authenticatedFetch, storeTokensForSchema, getCurrentSchema, getAccessTokenForSchema, refreshJWTTokens } from '../services/tokenService';
import { resilientApiClient } from '../utils/resilientApiClient';
import { API_BASE_URL } from '../config/apiConfig';
import {
  Box,
  Container,
  Paper,
  Typography,
  TextField,
  Button,
  Grid,
  MenuItem,
  Divider,
  Alert,
  AlertTitle,
  CircularProgress,
  Card,
  CardContent,
  FormControl,
  InputLabel,
  Select,
  FormHelperText,
  Snackbar,
  IconButton,
  InputAdornment,
  Switch,
  FormControlLabel,
  Stepper,
  Step,
  StepLabel,
  StepContent,
  StepButton,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Avatar,
  ListSubheader,
  Autocomplete,
  Chip
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { useNavigate } from 'react-router-dom';
import { format } from 'date-fns';
import { createTheme, ThemeProvider } from '@mui/material/styles';
import UnauthorizedAccess from '../components/tenant/UnauthorizedAccess';
import PageBanner from '../components/PageBanner';

// Icons
import PersonAddIcon from '@mui/icons-material/PersonAdd';
import PersonIcon from '@mui/icons-material/Person';
import CalendarTodayIcon from '@mui/icons-material/CalendarToday';
import SaveIcon from '@mui/icons-material/Save';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import HomeIcon from '@mui/icons-material/Home';
import PhoneIcon from '@mui/icons-material/Phone';
import EmailIcon from '@mui/icons-material/Email';
import WorkIcon from '@mui/icons-material/Work';
import FlagIcon from '@mui/icons-material/Flag';
import PhotoCameraIcon from '@mui/icons-material/PhotoCamera';
import WcIcon from '@mui/icons-material/Wc';
import FamilyRestroomIcon from '@mui/icons-material/FamilyRestroom';
import ContactEmergencyIcon from '@mui/icons-material/ContactEmergency';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import NavigateNextIcon from '@mui/icons-material/NavigateNext';
import NavigateBeforeIcon from '@mui/icons-material/NavigateBefore';
import ChildCareIcon from '@mui/icons-material/ChildCare';
import DeleteIcon from '@mui/icons-material/Delete';
import AddIcon from '@mui/icons-material/Add';
import CalendarMonthIcon from '@mui/icons-material/CalendarMonth';
import SearchIcon from '@mui/icons-material/Search';

// Create a custom theme for compact form fields
const compactFormTheme = createTheme({
  components: {
    MuiOutlinedInput: {
      styleOverrides: {
        root: {
          '& .MuiOutlinedInput-input': {
            padding: '10px 14px', // Reduce input padding
          },
          '& .MuiInputAdornment-root': {
            marginRight: '-4px', // Reduce space between icon and input
          },
        },
      },
    },
    MuiInputLabel: {
      styleOverrides: {
        root: {
          transform: 'translate(14px, 12px) scale(1)', // Adjust label position
          '&.MuiInputLabel-shrink': {
            transform: 'translate(14px, -6px) scale(0.75)', // Adjust shrunk label position
          },
        },
      },
    },
    MuiFormHelperText: {
      styleOverrides: {
        root: {
          marginTop: '2px', // Reduce space between input and helper text
          fontSize: '0.7rem', // Make helper text smaller
        },
      },
    },
    MuiSelect: {
      styleOverrides: {
        select: {
          padding: '10px 14px', // Reduce select padding
        },
      },
    },
    MuiMenuItem: {
      styleOverrides: {
        root: {
          minHeight: '32px', // Reduce menu item height
        },
      },
    },
  },
});

interface RegisterCitizenProps {
  mode?: 'full' | 'spouse-only' | 'parent-only' | 'child-only' | 'emergency-contact-only';
  citizenId?: string;
  onSubmit?: (formData: any) => void;
}

const RegisterCitizen: React.FC<RegisterCitizenProps> = ({
  mode = 'full',
  citizenId,
  onSubmit
}) => {
  const navigate = useNavigate();

  // We'll use the token from localStorage - avoid hardcoded tokens

  // Get the current tenant from localStorage first
  const tenantString = localStorage.getItem('tenant');
  console.log('Raw tenant string from localStorage:', tenantString);

  // Parse the tenant object and log all its properties
  const tenant = tenantString ? JSON.parse(tenantString) : null;
  console.log('Parsed tenant object:', tenant);
  if (tenant) {
    console.log('Tenant properties:');
    Object.keys(tenant).forEach(key => {
      console.log(`  ${key}: ${JSON.stringify(tenant[key])}`);
    });
  }

  const token = localStorage.getItem('token');
  const schemaName = localStorage.getItem('schema_name');

  // Check if tenant type is allowed to access this page
  // Get user role from localStorage
  const userRole = localStorage.getItem('user_role');

  // CENTER_STAFF should always be authorized regardless of tenant type
  const isTenantAuthorized =
    userRole === 'CENTER_STAFF' ||
    tenant?.type === 'KEBELE';

  // Function to determine the parent subcity ID for a kebele tenant
  const determineParentSubcity = () => {
    // If tenant has parent_id, use it - this should be the primary approach
    if (tenant?.parent_id) {
      console.log('Using tenant.parent_id:', tenant.parent_id);
      return tenant.parent_id;
    }

    // If tenant has parent object, use its ID
    if (tenant?.parent && tenant.parent.id) {
      console.log('Using tenant.parent.id:', tenant.parent.id);
      return tenant.parent.id;
    }

    // If no parent_id or parent object, try to fetch it from the API
    console.log('No parent information found in tenant object, will try to fetch from API');

    // Schedule an API fetch to get the parent information
    setTimeout(() => {
      fetchTenantFromAPI();
    }, 100);

    // Return empty string for now, it will be updated when API response comes back
    return '';
  };



  // Function to get initial form data
  const getInitialFormData = () => {
    console.log('Initializing form data with tenant:', tenant);

    // Get tenant information directly from localStorage
    const tenantString = localStorage.getItem('tenant');
    let localTenant = tenant; // Default to the tenant from context

    if (tenantString) {
      try {
        const parsedTenant = JSON.parse(tenantString);
        console.log('Parsed tenant from localStorage:', parsedTenant);

        // Use the tenant from localStorage if it has more information
        if (parsedTenant && parsedTenant.id) {
          localTenant = parsedTenant;
          console.log('Using tenant from localStorage:', localTenant);
        }
      } catch (error) {
        console.error('Error parsing tenant from localStorage:', error);
      }
    } else {
      console.log('No tenant found in localStorage');
    }

    // Automatically determine subcity and kebele values
    let initialSubcity = '';
    let initialKebele = '';

    // For kebele tenants, set values automatically
    if (localTenant?.type === 'KEBELE') {
      // Get subcity from parent tenant
      initialSubcity = localTenant.parent_id || '';

      // Get kebele from current tenant
      initialKebele = localTenant.id || '';

      console.log('Auto-setting location values from localStorage tenant');
      console.log('  subcity (from parent tenant):', initialSubcity, localTenant.parent_name ? `(${localTenant.parent_name})` : '');
      console.log('  kebele (from current tenant):', initialKebele, `(${localTenant.name})`);

      // If we don't have a valid subcity ID yet, try to get it from other sources
      if (!initialSubcity) {
        console.log('No valid subcity ID found in localStorage tenant');

        // Try to get parent_id from tenant context
        if (tenant?.parent_id) {
          initialSubcity = tenant.parent_id;
          console.log('Using parent_id from tenant context:', initialSubcity);
        } else {
          // Use determineParentSubcity as a last resort
          initialSubcity = determineParentSubcity();
          console.log('Using determined parent subcity:', initialSubcity);
        }
      }

      // Also populate the subcities and kebeles arrays for the dropdowns
      if (initialSubcity && localTenant.parent_name) {
        console.log('Setting subcities array with parent tenant');
        setSubcities([{
          id: initialSubcity,
          name: localTenant.parent_name,
          city: '1' // Assuming city ID 1
        }]);
      }

      if (initialKebele && localTenant.name) {
        console.log('Setting kebeles array with current tenant');
        setKebeles([{
          id: initialKebele,
          name: localTenant.name,
          subcity: initialSubcity,
          code: initialKebele.toString().padStart(2, '0')
        }]);
      }
    }

    return {

    // Basic Information
    registration_number: '',
    first_name: '',
    middle_name: '',
    last_name: '',
    first_name_am: '',
    middle_name_am: '',
    last_name_am: '',
    date_of_birth: new Date(),
    gender: '',
    religion: '',
    citizen_status: '',

    // Contact Information
    phone: '',
    email: '',
    address: '',
    house_number: '',
    subcity: initialSubcity, // Use hardcoded value from workaround
    kebele: initialKebele, // Use hardcoded value from workaround
    ketena: '',

    // Nationality Information
    nationality: 'Ethiopian',
    nationality_country: '',
    region: '',

    // Employment Information
    employment: false,
    employee_type: '',
    organization_name: '',
    occupation: '',
    employment_type: '',

    // Family Information
    marital_status: '',

    // Spouse Information (if married)
    spouse_first_name: '',
    spouse_middle_name: '',
    spouse_last_name: '',
    spouse_phone: '',
    spouse_email: '',
    spouse_is_resident: false,
    spouse_linked_citizen: '',

    // Children Information
    has_children: false,
    children: [] as any[],

    // Parent Information
    mother_first_name: '',
    mother_middle_name: '',
    mother_last_name: '',
    mother_is_resident: false,
    mother_linked_citizen: '',

    father_first_name: '',
    father_middle_name: '',
    father_last_name: '',
    father_is_resident: false,
    father_linked_citizen: '',

    // Emergency Contact Information
    emergency_contact_first_name: '',
    emergency_contact_middle_name: '',
    emergency_contact_last_name: '',
    emergency_contact_phone: '',
    emergency_contact_email: '',
    emergency_contact_relationship: '',
    emergency_contact_is_resident: false,
    emergency_contact_linked_citizen: '',

    // Other Information
    is_resident: true,

    // Photo
    photo: null as File | null
  };
  };

  // For debugging: Get the initial values that should be set
  const initialSubcity = tenant?.type === 'KEBELE' ? tenant.parent_id : '';
  const initialKebele = tenant?.type === 'KEBELE' ? tenant.id : '';
  console.log('Initial subcity should be:', initialSubcity);
  console.log('Initial kebele should be:', initialKebele);

  // State for form data - directly set the location values in the initial state
  const [formData, setFormData] = useState({
    ...getInitialFormData(),
    subcity: initialSubcity,
    kebele: initialKebele
  });

  // Force set location values whenever tenant changes
  useEffect(() => {
    // Skip if tenant is null or undefined
    if (!tenant) return;

    // Only run this effect if tenant has a parent_id
    if (tenant?.type === 'KEBELE' && tenant.parent_id) {
      console.log('TENANT CHANGE EFFECT: Setting location values');
      console.log('  subcity:', tenant.parent_id, tenant.parent_name ? `(${tenant.parent_name})` : '');
      console.log('  kebele:', tenant.id, `(${tenant.name})`);

      // Set the location values directly
      setFormData(prev => {
        // Only update if values are different to prevent infinite loops
        if (prev.subcity !== tenant.parent_id || prev.kebele !== tenant.id) {
          return {
            ...prev,
            subcity: tenant.parent_id,
            kebele: tenant.id
          };
        }
        return prev;
      });

      // Also fetch kebeles for the parent subcity (fetchKebeles has its own loop prevention)
      fetchKebeles(tenant.parent_id);

      // EXTREME MEASURE: Use direct DOM manipulation as a last resort
      // This will run after the component has rendered
      setTimeout(() => {
        try {
          // Try to directly set the select values using DOM manipulation
          const subcitySelect = document.getElementById('subcity-select');
          const kebeleSelect = document.getElementById('kebele-select');

          if (subcitySelect) {
            // @ts-ignore - TypeScript doesn't know about the value property
            subcitySelect.value = tenant.parent_id;
            console.log('DOM MANIPULATION: Set subcity select value to', tenant.parent_id);
          }

          if (kebeleSelect) {
            // @ts-ignore - TypeScript doesn't know about the value property
            kebeleSelect.value = tenant.id;
            console.log('DOM MANIPULATION: Set kebele select value to', tenant.id);
          }
        } catch (error) {
          console.error('Error with DOM manipulation:', error);
        }
      }, 1000); // Wait 1 second for the component to render

      // Try again after 2 seconds as a final attempt, but only once
      const finalAttemptTimer = setTimeout(() => {
        console.log('FINAL ATTEMPT: Setting location values after 2 seconds');
        setFormData(prev => {
          // Only update if values are different to prevent infinite loops
          if (prev.subcity !== tenant.parent_id || prev.kebele !== tenant.id) {
            return {
              ...prev,
              subcity: tenant.parent_id,
              kebele: tenant.id
            };
          }
          return prev;
        });
      }, 2000);

      // Clean up the timer when the component unmounts or when tenant changes
      return () => clearTimeout(finalAttemptTimer);
    }
  }, [tenant?.id, tenant?.parent_id]); // Only re-run when tenant.id or tenant.parent_id changes

  // State for form submission
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitSuccess, setSubmitSuccess] = useState(false);
  const [submitError, setSubmitError] = useState('');
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [tokenRefreshCountdownInterval, setTokenRefreshCountdownInterval] = useState<NodeJS.Timeout | null>(null);

  // State for citizens loading error
  const [citizensError, setCitizensError] = useState('');

  // State for photo preview
  const [photoPreview, setPhotoPreview] = useState<string | null>(null);

  // State for webcam capture
  const [isCapturing, setIsCapturing] = useState(false);
  const webcamRef = useRef<Webcam>(null);

  // State for citizen search
  const [motherSearchQuery, setMotherSearchQuery] = useState('');
  const [fatherSearchQuery, setFatherSearchQuery] = useState('');
  const [spouseSearchQuery, setSpouseSearchQuery] = useState('');
  const [emergencyContactSearchQuery, setEmergencyContactSearchQuery] = useState('');

  const [searchingMother, setSearchingMother] = useState(false);
  const [searchingFather, setSearchingFather] = useState(false);
  const [searchingSpouse, setSearchingSpouse] = useState(false);
  const [searchingEmergencyContact, setSearchingEmergencyContact] = useState(false);
  const [motherSearchResults, setMotherSearchResults] = useState<any[]>([]);
  const [fatherSearchResults, setFatherSearchResults] = useState<any[]>([]);
  const [spouseSearchResults, setSpouseSearchResults] = useState<any[]>([]);
  const [emergencyContactSearchResults, setEmergencyContactSearchResults] = useState<any[]>([]);
  const [filteredFatherCitizens, setFilteredFatherCitizens] = useState<any[]>([]);
  const [filteredSpouseCitizens, setFilteredSpouseCitizens] = useState<any[]>([]);
  const [filteredEmergencyContactCitizens, setFilteredEmergencyContactCitizens] = useState<any[]>([]);

  // Add the timeout property to the Window interface
  declare global {
    interface Window {
      motherSearchTimeout?: NodeJS.Timeout;
      fatherSearchTimeout?: NodeJS.Timeout;
      spouseSearchTimeout?: NodeJS.Timeout;
      emergencyContactSearchTimeout?: NodeJS.Timeout;
    }
  }

  // State for child form
  const [childForm, setChildForm] = useState({
    first_name: '',
    middle_name: '',
    last_name: '',
    gender: '',
    date_of_birth: null as Date | null
  });

  // State for stepper
  const [activeStep, setActiveStep] = useState(mode === 'full' ? 0 :
    mode === 'spouse-only' ? 3 :
    mode === 'parent-only' ? 3 :
    mode === 'child-only' ? 3 :
    mode === 'emergency-contact-only' ? 4 : 0
  );
  const [completed, setCompleted] = useState<{ [k: number]: boolean }>({});

  // Add event listeners for custom submit events
  useEffect(() => {
    const handleSubmitSpouseForm = () => {
      if (mode === 'spouse-only' && onSubmit) {
        onSubmit(formData);
      }
    };

    const handleSubmitParentForm = () => {
      if (mode === 'parent-only' && onSubmit) {
        onSubmit(formData);
      }
    };

    const handleSubmitChildForm = () => {
      if (mode === 'child-only' && onSubmit) {
        onSubmit(formData);
      }
    };

    const handleSubmitEmergencyContactForm = () => {
      if (mode === 'emergency-contact-only' && onSubmit) {
        onSubmit(formData);
      }
    };

    // Add event listeners
    document.addEventListener('submit-spouse-form', handleSubmitSpouseForm);
    document.addEventListener('submit-parent-form', handleSubmitParentForm);
    document.addEventListener('submit-child-form', handleSubmitChildForm);
    document.addEventListener('submit-emergency-contact-form', handleSubmitEmergencyContactForm);

    // Remove event listeners on cleanup
    return () => {
      document.removeEventListener('submit-spouse-form', handleSubmitSpouseForm);
      document.removeEventListener('submit-parent-form', handleSubmitParentForm);
      document.removeEventListener('submit-child-form', handleSubmitChildForm);
      document.removeEventListener('submit-emergency-contact-form', handleSubmitEmergencyContactForm);
    };
  }, [mode, onSubmit, formData]);

  // Cleanup effect for token refresh countdown interval
  useEffect(() => {
    return () => {
      if (tokenRefreshCountdownInterval) {
        clearInterval(tokenRefreshCountdownInterval);
      }
    };
  }, [tokenRefreshCountdownInterval]);

  // State for dropdown options
  const [religions, setReligions] = useState<any[]>([]);
  const [citizenStatuses, setCitizenStatuses] = useState<any[]>([]);
  const [subcities, setSubcities] = useState<any[]>([]);
  const [kebeles, setKebeles] = useState<any[]>([]);
  const [ketenas, setKetenas] = useState<any[]>([]);
  const [countries, setCountries] = useState<any[]>([]);
  const [regions, setRegions] = useState<any[]>([]);
  const [employeeTypes, setEmployeeTypes] = useState<any[]>([]);
  const [employmentTypes, setEmploymentTypes] = useState<any[]>([]);
  const [maritalStatuses, setMaritalStatuses] = useState<any[]>([]);
  const [citizens, setCitizens] = useState<any[]>([]);
  const [relationshipTypes, setRelationshipTypes] = useState<any[]>([]);

  // Loading states
  const [loadingReligions, setLoadingReligions] = useState(false);
  const [loadingCitizenStatuses, setLoadingCitizenStatuses] = useState(false);
  const [loadingSubcities, setLoadingSubcities] = useState(false);
  const [loadingKebeles, setLoadingKebeles] = useState(false);
  const [loadingKetenas, setLoadingKetenas] = useState(false);
  const [loadingCountries, setLoadingCountries] = useState(false);
  const [loadingRegions, setLoadingRegions] = useState(false);
  const [loadingEmployeeTypes, setLoadingEmployeeTypes] = useState(false);
  const [loadingEmploymentTypes, setLoadingEmploymentTypes] = useState(false);
  const [loadingMaritalStatuses, setLoadingMaritalStatuses] = useState(false);
  const [loadingCitizens, setLoadingCitizens] = useState(false);
  const [loadingRelationshipTypes, setLoadingRelationshipTypes] = useState(false);

  // Store tenant's parent information
  const [tenantParent, setTenantParent] = useState<any>(null);

  // Function to set location values based on tenant
  const setLocationValues = (force = false) => {
    if (tenant?.type === 'KEBELE') {
      console.log('Setting location values from setLocationValues function');

      if (tenant.parent_id) {
        // If tenant has a parent_id, use it
        console.log('Setting subcity to:', tenant.parent_id, tenant.parent_name ? `(${tenant.parent_name})` : '');
        console.log('Setting kebele to:', tenant.id, `(${tenant.name})`);

        setFormData(prev => ({
          ...prev,
          subcity: tenant.parent_id,
          kebele: tenant.id
        }));

        // Also fetch kebeles for the parent subcity
        fetchKebeles(tenant.parent_id);

        // Try direct DOM manipulation as well
        setTimeout(() => {
          try {
            const subcitySelect = document.getElementById('subcity-select');
            const kebeleSelect = document.getElementById('kebele-select');

            if (subcitySelect) {
              // @ts-ignore
              subcitySelect.value = tenant.parent_id;
              console.log('DOM: Set subcity select value to', tenant.parent_id);
            }

            if (kebeleSelect) {
              // @ts-ignore
              kebeleSelect.value = tenant.id;
              console.log('DOM: Set kebele select value to', tenant.id);
            }
          } catch (error) {
            console.error('Error with DOM manipulation:', error);
          }
        }, 500);

        return true;
      } else {
        // If tenant doesn't have a parent_id, try to determine it
        const parentSubcityId = determineParentSubcity();

        if (parentSubcityId) {
          console.log('Using determined parent subcity ID:', parentSubcityId);

          setFormData(prev => ({
            ...prev,
            subcity: parentSubcityId,
            kebele: tenant.id
          }));

          // Also fetch kebeles for the parent subcity
          fetchKebeles(parentSubcityId);

          return true;
        } else {
          // If tenant doesn't have a parent_id, just set the kebele if it's not already set
          console.log('Tenant has no parent_id, only setting kebele if not already set');

          if (!formData.kebele || force) {
            setFormData(prev => ({
              ...prev,
              kebele: tenant.id
            }));
          }

          return false;
        }
      }
    }

    return false;
  };



  // Handle form field changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | { name?: string; value: unknown }>) => {
    const { name, value } = e.target;
    if (name) {
      setFormData(prev => ({ ...prev, [name]: value }));
    }
  };

  // Handle date changes
  const handleDateChange = (date: Date | null) => {
    if (date) {
      setFormData(prev => ({ ...prev, date_of_birth: date }));
    }
  };

  // Handle photo upload
  const handlePhotoChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      setFormData(prev => ({ ...prev, photo: file }));

      // Create a preview URL
      const reader = new FileReader();
      reader.onloadend = () => {
        setPhotoPreview(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  // Toggle webcam capture mode
  const toggleCapture = () => {
    setIsCapturing(!isCapturing);
  };

  // Capture photo from webcam
  const capturePhoto = useCallback(() => {
    if (webcamRef.current) {
      const imageSrc = webcamRef.current.getScreenshot();
      if (imageSrc) {
        console.log('Photo captured from webcam');
        setPhotoPreview(imageSrc);

        // Convert base64 to file
        try {
          const byteString = atob(imageSrc.split(',')[1]);
          const mimeString = imageSrc.split(',')[0].split(':')[1].split(';')[0];
          const ab = new ArrayBuffer(byteString.length);
          const ia = new Uint8Array(ab);

          for (let i = 0; i < byteString.length; i++) {
            ia[i] = byteString.charCodeAt(i);
          }

          const blob = new Blob([ab], { type: mimeString });
          const file = new File([blob], `webcam-capture-${Date.now()}.jpg`, { type: 'image/jpeg' });

          console.log('Converted webcam capture to File object:', file);
          console.log('File size:', file.size, 'bytes');
          console.log('File type:', file.type);

          // Update form data with the captured photo
          setFormData(prev => {
            console.log('Updating form data with captured photo');
            return { ...prev, photo: file };
          });

          setIsCapturing(false); // Exit capture mode after taking photo
        } catch (error) {
          console.error('Error converting webcam capture to file:', error);
          alert('There was an error processing the captured image. Please try again.');
        }
      }
    }
  }, [webcamRef]);

  // Generate a unique registration number
  const generateRegistrationNumber = () => {
    const prefix = tenant?.schema_name ? tenant.schema_name.substring(0, 3).toUpperCase() : 'CIT';
    const timestamp = new Date().getTime().toString().substring(7);
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
    return `${prefix}-${timestamp}-${random}`;
  };

  // Fetch tenant parent information
  const fetchAndSetTenantParent = async () => {
    try {
      console.log('Fetching tenant parent information...');
      console.log('Current tenant:', tenant);

      if (tenant?.type === 'KEBELE') {
        console.log('This is a KEBELE tenant');
        console.log('Current tenant ID:', tenant.id);

        // Use parent_id directly from the tenant object
        if (tenant.parent_id) {
          console.log('Using parent_id from tenant:', tenant.parent_id);
          console.log('Parent name:', tenant.parent_name);

          // Set the location values immediately
          console.log('Setting location values...');
          setFormData(prevData => {
            const newData = {
              ...prevData,
              subcity: tenant.parent_id,
              kebele: tenant.id
            };
            console.log('New form data with location values:', newData);
            return newData;
          });

          // Fetch kebeles for the parent subcity
          fetchKebeles(tenant.parent_id);

          // Force a delay to ensure the values are set after the component is fully rendered
          setTimeout(() => {
            console.log('DELAYED EFFECT: Forcing location values again');
            setFormData(prev => ({
              ...prev,
              subcity: tenant.parent_id,
              kebele: tenant.id
            }));
          }, 500);
        } else {
          console.log('Warning: Tenant has no parent_id');
          console.log('Trying to determine parent subcity using fallback method...');

          // Try to determine parent subcity using our fallback function
          const parentSubcityId = determineParentSubcity();

          if (parentSubcityId) {
            console.log('Determined parent subcity ID using fallback:', parentSubcityId);

            // Set the location values
            setFormData(prevData => ({
              ...prevData,
              subcity: parentSubcityId,
              kebele: tenant.id
            }));

            // Fetch kebeles for the parent subcity
            fetchKebeles(parentSubcityId);

            // Force a delay to ensure the values are set after the component is fully rendered
            setTimeout(() => {
              console.log('DELAYED EFFECT: Forcing location values again with fallback');
              setFormData(prev => ({
                ...prev,
                subcity: parentSubcityId,
                kebele: tenant.id
              }));
            }, 500);
          } else {
            console.log('Warning: Could not determine parent subcity');
            console.log('User will need to select subcity and kebele manually');

            // Just set the kebele value
            setFormData(prevData => ({
              ...prevData,
              kebele: tenant.id
            }));

            // Fetch all subcities to allow manual selection
            fetchSubcities();
          }
        }
      } else {
        console.log('Not a KEBELE tenant');
      }
    } catch (error) {
      console.error('Error fetching tenant parent:', error);
    }
  };

  // Fetch dropdown data
  const fetchReligions = async () => {
    setLoadingReligions(true);
    try {
      // Fetch religions from common API endpoint
      const url = '/api/common/religions/';
      console.log('Fetching religions from common API:', url);

      // For testing, don't use authentication
      const response = await fetch(url);

      console.log('API response status:', response.status);
      console.log('API response status text:', response.statusText);

      if (!response.ok) {
        throw new Error(`Failed to fetch religions: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      console.log('Parsed religion data:', data);

      // Extract the results array from the response
      const results = data.results || data;
      console.log('Religion results:', results);

      if (!Array.isArray(results)) {
        console.error('Results is not an array:', results);
        throw new Error('Religion results is not an array');
      }

      console.log('Setting religions state with:', results);
      setReligions(results);
    } catch (error) {
      console.error('Error fetching religions:', error);
      // Don't set fallback values, just show an empty array
      setReligions([]);
    } finally {
      setLoadingReligions(false);
    }
  };

  const fetchCitizenStatuses = async () => {
    setLoadingCitizenStatuses(true);
    try {
      // Fetch citizen statuses from common API endpoint
      const url = '/api/common/citizen-statuses/';
      console.log('Fetching citizen statuses from common API:', url);

      // For testing, don't use authentication
      const response = await fetch(url);

      if (!response.ok) {
        throw new Error(`Failed to fetch citizen statuses: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      console.log('Fetched citizen statuses from API:', data);

      // Extract the results array from the response
      const results = data.results || data;
      console.log('Citizen status results:', results);

      setCitizenStatuses(results);
    } catch (error) {
      console.error('Error fetching citizen statuses:', error);
      // Don't set fallback values, just show an empty array
      setCitizenStatuses([]);
    } finally {
      setLoadingCitizenStatuses(false);
    }
  };

  const fetchSubcities = async () => {
    setLoadingSubcities(true);
    try {
      // First try to get tenant information from localStorage
      const tenantString = localStorage.getItem('tenant');
      let localTenant = null;

      if (tenantString) {
        try {
          localTenant = JSON.parse(tenantString);
          console.log('Parsed tenant from localStorage for subcities:', localTenant);
        } catch (error) {
          console.error('Error parsing tenant from localStorage:', error);
        }
      }

      // If we're in a kebele tenant, use parent tenant information
      if ((localTenant?.type === 'KEBELE' || tenant?.type === 'KEBELE')) {
        console.log('Fetching subcities for kebele tenant');

        // First try to get parent information from localStorage tenant
        if (localTenant?.parent_id && localTenant?.parent_name) {
          console.log('Using parent tenant information from localStorage');
          const parentSubcity = [
            {
              id: localTenant.parent_id,
              name: localTenant.parent_name,
              city: '1' // Assuming city ID 1
            }
          ];
          console.log('Setting subcities from localStorage parent tenant:', parentSubcity);
          setSubcities(parentSubcity);

          // Also set the subcity value in the form data
          setFormData(prev => ({
            ...prev,
            subcity: localTenant.parent_id
          }));

          return;
        }

        // Then try to get parent information from tenant context
        if (tenant?.parent_id && tenant?.parent_name) {
          console.log('Using parent tenant information from context');
          const parentSubcity = [
            {
              id: tenant.parent_id,
              name: tenant.parent_name,
              city: '1' // Assuming city ID 1
            }
          ];
          console.log('Setting subcities from context parent tenant:', parentSubcity);
          setSubcities(parentSubcity);

          // Also set the subcity value in the form data
          setFormData(prev => ({
            ...prev,
            subcity: tenant.parent_id
          }));

          return;
        }

        // If we don't have parent information, try to fetch it
        console.log('No parent information available in localStorage or context, trying to fetch from API');
        await fetchTenantFromAPI();

        // Check if we have parent information after the fetch
        if (tenant?.parent_id && tenant?.parent_name) {
          console.log('Parent information fetched successfully from API');
          const parentSubcity = [
            {
              id: tenant.parent_id,
              name: tenant.parent_name,
              city: '1' // Assuming city ID 1
            }
          ];
          console.log('Setting subcities from API-fetched parent tenant:', parentSubcity);
          setSubcities(parentSubcity);

          // Also set the subcity value in the form data
          setFormData(prev => ({
            ...prev,
            subcity: tenant.parent_id
          }));

          return;
        }
      }

      // For other tenant types or if we couldn't get parent information, try to fetch from API
      console.log('Fetching subcities from API');
      let schema = schemaName || tenant?.schema_name || '';
      // Do NOT replace spaces with underscores - use URL encoding instead
      const url = `/api/common/subcities/`;
      console.log('Fetching subcities from URL:', url);

      // Use direct fetch without authentication for common endpoints
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        console.error('Failed to fetch subcities:', response.status, response.statusText);
        throw new Error('Failed to fetch subcities');
      }

      const data = await response.json();
      console.log('Fetched subcities from API:', data);

      // Extract the results array from the response
      const results = data.results || data;
      console.log('Subcity results:', results);

      setSubcities(results);

      // If we're in a kebele tenant and we have a parent_id, set the subcity value
      if (tenant?.type === 'KEBELE' && tenant?.parent_id) {
        setFormData(prev => ({
          ...prev,
          subcity: tenant.parent_id
        }));
      }
    } catch (error) {
      console.error('Error fetching subcities:', error);

      // If we have tenant information, use that to create a fallback subcity
      if (tenant?.type === 'KEBELE' && tenant?.parent_id && tenant?.parent_name) {
        const fallbackSubcities = [
          {
            id: tenant.parent_id,
            name: tenant.parent_name,
            city: '1' // Assuming city ID 1
          }
        ];
        console.log('Using fallback subcities from tenant parent:', fallbackSubcities);
        setSubcities(fallbackSubcities);

        // Also set the subcity value in the form data
        setFormData(prev => ({
          ...prev,
          subcity: tenant.parent_id
        }));
      } else {
        // Otherwise, create some default subcities
        const defaultSubcities = [
          { id: '1', name: 'Zoble', city: '1' },
          { id: '2', name: 'Arada', city: '1' },
          { id: '3', name: 'Bole', city: '1' }
        ];
        console.log('Using default subcities:', defaultSubcities);
        setSubcities(defaultSubcities);
      }
    } finally {
      setLoadingSubcities(false);
    }
  };

  const fetchKebeles = async (subcityId: string) => {
    if (!subcityId) {
      console.log('No subcity ID provided, cannot fetch kebeles');
      setKebeles([]);
      return;
    }

    console.log('Fetching kebeles for subcity ID:', subcityId);

    // Check if we've already fetched kebeles for this subcity to prevent infinite loops
    if (kebeles.length > 0 && kebeles[0].subcity === subcityId) {
      console.log('Kebeles already fetched for this subcity, skipping fetch');

      // Even though we're skipping the fetch, make sure the kebele value is set in the form data
      if (tenant?.type === 'KEBELE' && tenant?.id) {
        console.log('Setting kebele value from tenant:', tenant.id);
        setFormData(prev => ({
          ...prev,
          kebele: tenant.id
        }));
      }

      return;
    }

    setLoadingKebeles(true);
    try {
      // First try to get tenant information from localStorage
      const tenantString = localStorage.getItem('tenant');
      let localTenant = null;

      if (tenantString) {
        try {
          localTenant = JSON.parse(tenantString);
          console.log('Parsed tenant from localStorage for kebeles:', localTenant);
        } catch (error) {
          console.error('Error parsing tenant from localStorage:', error);
        }
      }

      // If we're in a kebele tenant, create a kebele entry from the tenant data
      if (localTenant?.type === 'KEBELE' || tenant?.type === 'KEBELE') {
        console.log('Creating kebele entry for kebele tenant');

        // First try to use localStorage tenant
        if (localTenant?.id && localTenant?.name) {
          console.log('Using tenant information from localStorage');
          const currentKebele = [
            {
              id: localTenant.id,
              name: localTenant.name,
              subcity: subcityId,
              code: localTenant.id.toString().padStart(2, '0')
            }
          ];
          console.log('Setting kebeles from localStorage tenant:', currentKebele);
          setKebeles(currentKebele);

          // Also set the kebele value in the form data
          setFormData(prev => ({
            ...prev,
            kebele: localTenant.id
          }));

          return;
        }

        // Then try to use tenant from context
        if (tenant?.id && tenant?.name) {
          console.log('Using tenant information from context');
          const currentKebele = [
            {
              id: tenant.id,
              name: tenant.name,
              subcity: subcityId,
              code: tenant.id.toString().padStart(2, '0')
            }
          ];
          console.log('Setting kebeles from context tenant:', currentKebele);
          setKebeles(currentKebele);

          // Also set the kebele value in the form data
          setFormData(prev => ({
            ...prev,
            kebele: tenant.id
          }));

          return;
        } else {
          console.warn('Tenant data is incomplete in both localStorage and context, cannot create kebele entry');
        }
      }

      // For other tenant types or if tenant data is incomplete, fetch from API
      console.log('Fetching kebeles from API for subcity:', subcityId);
      const url = `/api/common/kebeles/?subcity=${subcityId}`;
      console.log('Fetching kebeles from URL:', url);

      // Use direct fetch without authentication for common endpoints
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        console.warn(`API returned ${response.status} for kebeles.`);
        throw new Error(`Failed to fetch kebeles: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      console.log('Fetched kebeles from API:', data);

      // Extract the results array from the response
      const results = data.results || data;
      console.log('Kebele results:', results);

      setKebeles(results);

      // If we're in a kebele tenant, set the kebele value in the form data
      if (tenant?.type === 'KEBELE' && tenant?.id) {
        console.log('Setting kebele value from tenant after API fetch:', tenant.id);
        setFormData(prev => ({
          ...prev,
          kebele: tenant.id
        }));
      }
    } catch (error) {
      console.error('Error in fetchKebeles:', error);

      // If we have tenant information, use that to create a fallback kebele
      if (tenant?.type === 'KEBELE' && tenant?.id && tenant?.name) {
        const fallbackKebeles = [
          {
            id: tenant.id,
            name: tenant.name,
            subcity: subcityId,
            code: tenant.id.toString().padStart(2, '0')
          }
        ];
        console.log('Using fallback kebeles from tenant:', fallbackKebeles);
        setKebeles(fallbackKebeles);

        // Also set the kebele value in the form data
        setFormData(prev => ({
          ...prev,
          kebele: tenant.id
        }));
      } else {
        // Otherwise, create some default kebeles for the given subcity
        const defaultKebeles = [
          { id: '1', name: 'Kebele 01', subcity: subcityId, code: '01' },
          { id: '2', name: 'Kebele 02', subcity: subcityId, code: '02' },
          { id: '3', name: 'Kebele 03', subcity: subcityId, code: '03' }
        ];
        console.log('Using default kebeles:', defaultKebeles);
        setKebeles(defaultKebeles);
      }
    } finally {
      setLoadingKebeles(false);
    }
  };

  const fetchKetenas = async (kebeleId?: string) => {
    console.log('Fetching all ketenas');
    console.log('Current ketenas state:', ketenas);

    // We're now forcing a fresh fetch every time, so we don't need to check if we've already fetched
    // This ensures we always get the latest data from the API
    console.log('Proceeding with ketena fetch');

    setLoadingKetenas(true);
    try {
      // Fetch all ketenas from common API endpoint without kebele filter
      const url = `/api/common/ketenas/`;
      console.log('Fetching ketenas from common API:', url);

      // For testing, don't use authentication
      console.log('Sending request to fetch ketenas...');
      const response = await fetch(url);
      console.log('Response received:', response.status, response.statusText);

      if (!response.ok) {
        throw new Error(`Failed to fetch ketenas: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      console.log('Fetched ketenas from API:', data);

      // Extract the results array from the response
      const results = data.results || data;
      console.log('Ketena results:', results);
      console.log('Ketena results type:', typeof results);
      console.log('Ketena results length:', results.length);

      if (results.length === 0) {
        console.warn(`No ketenas found.`);
        // Just set an empty array for ketenas
        setKetenas([]);
        return;
      }

      // Map the results to match the expected format
      const mappedResults = results.map((ketena: any) => ({
        id: ketena.id.toString(),
        name: ketena.name,
        code: ketena.code,
        kebele: ketena.kebele_id
      }));

      // Use all ketenas without filtering by kebele
      console.log('Setting all ketenas without filtering by kebele:', mappedResults);
      setKetenas(mappedResults);
    } catch (error) {
      console.error('Error fetching ketenas:', error);
      // Just set an empty array for ketenas
      setKetenas([]);
    } finally {
      setLoadingKetenas(false);
    }
  };

  const fetchCountries = async () => {
    setLoadingCountries(true);
    try {
      // Fetch countries from common API endpoint
      const url = '/api/common/countries/';
      console.log('Fetching countries from common API:', url);

      // For testing, don't use authentication
      const response = await fetch(url);

      if (!response.ok) {
        throw new Error(`Failed to fetch countries: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      console.log('Fetched countries from API:', data);

      // Extract the results array from the response
      const results = data.results || data;
      console.log('Country results:', results);

      setCountries(results);
    } catch (error) {
      console.error('Error fetching countries:', error);
      // Don't set fallback values, just show an empty array
      setCountries([]);
    } finally {
      setLoadingCountries(false);
    }
  };

  const fetchRegions = async (countryId: string) => {
    if (!countryId) {
      setRegions([]);
      return;
    }
    setLoadingRegions(true);
    try {
      // Fetch regions from common API endpoint with country filter
      const url = `/api/common/regions/?country=${countryId}`;
      console.log('Fetching regions from common API:', url);

      // For testing, don't use authentication
      const response = await fetch(url);

      if (!response.ok) {
        throw new Error(`Failed to fetch regions: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      console.log('Fetched regions from API:', data);

      // Extract the results array from the response
      const results = data.results || data;
      console.log('Region results:', results);

      setRegions(results);
    } catch (error) {
      console.error('Error fetching regions:', error);
      // Don't set fallback values, just show an empty array
      setRegions([]);
    } finally {
      setLoadingRegions(false);
    }
  };

  const fetchEmployeeTypes = async () => {
    setLoadingEmployeeTypes(true);
    try {
      // Fetch employee types from common API endpoint
      const url = '/api/common/employee-types/';
      console.log('Fetching employee types from common API:', url);

      // For testing, don't use authentication
      const response = await fetch(url);

      if (!response.ok) {
        throw new Error(`Failed to fetch employee types: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      console.log('Fetched employee types from API:', data);

      // Extract the results array from the response
      const results = data.results || data;
      console.log('Employee type results:', results);

      setEmployeeTypes(results);
    } catch (error) {
      console.error('Error fetching employee types:', error);
      // Don't set fallback values, just show an empty array
      setEmployeeTypes([]);
    } finally {
      setLoadingEmployeeTypes(false);
    }
  };

  const fetchEmploymentTypes = async () => {
    setLoadingEmploymentTypes(true);
    try {
      // Fetch employment types from common API endpoint
      const url = '/api/common/employment-types/';
      console.log('Fetching employment types from common API:', url);

      // For testing, don't use authentication
      const response = await fetch(url);

      if (!response.ok) {
        throw new Error(`Failed to fetch employment types: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      console.log('Fetched employment types from API:', data);

      // Extract the results array from the response
      const results = data.results || data;
      console.log('Employment type results:', results);

      setEmploymentTypes(results);
    } catch (error) {
      console.error('Error fetching employment types:', error);
      // Don't set fallback values, just show an empty array
      setEmploymentTypes([]);
    } finally {
      setLoadingEmploymentTypes(false);
    }
  };

  const fetchMaritalStatuses = async () => {
    setLoadingMaritalStatuses(true);
    try {
      // Fetch marital statuses from common API endpoint
      const url = '/api/common/marital-statuses/';
      console.log('Fetching marital statuses from common API:', url);

      // For testing, don't use authentication
      const response = await fetch(url);

      if (!response.ok) {
        throw new Error(`Failed to fetch marital statuses: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      console.log('Fetched marital statuses from API:', data);

      // Extract the results array from the response
      const results = data.results || data;
      console.log('Marital status results:', results);

      setMaritalStatuses(results);
    } catch (error) {
      console.error('Error fetching marital statuses:', error);
      // Don't set fallback values, just show an empty array
      setMaritalStatuses([]);
    } finally {
      setLoadingMaritalStatuses(false);
    }
  };

  // This function is now imported from tokenRefresh.ts

  // State for authentication error
  const [authError, setAuthError] = useState(false);

  // Form persistence key
  const FORM_STORAGE_KEY = 'citizen_registration';

  // Function to handle login redirection
  const handleLoginRedirect = () => {
    // Save form data before redirecting
    saveFormData(FORM_STORAGE_KEY, formData);

    // Clear authentication data
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    localStorage.removeItem('tenant');
    localStorage.removeItem('schema_name');

    // Redirect to login page with return URL
    window.location.href = `/login?returnUrl=${encodeURIComponent(window.location.pathname)}`;
  };

  // Function to handle session expiration
  const handleSessionExpiration = () => {
    console.log('Handling session expiration');

    // Save form data
    saveFormData(FORM_STORAGE_KEY, formData);

    // Try to create a new token first
    createNewToken().then(newToken => {
      if (newToken) {
        console.log('Successfully created new token, no need to redirect');
        // Refresh the page to use the new token
        window.location.reload();
        return;
      }

      console.log('Could not create new token, redirecting to login');
      // Save current URL and redirect to login
      handleLoginRedirect();
    }).catch(error => {
      console.error('Error creating new token:', error);
      // Save current URL and redirect to login
      handleLoginRedirect();
    });
  };

  const fetchCitizens = async () => {
    setLoadingCitizens(true);
    try {
      // Use the getApiUrl function to get the correct URL with schema name
      const url = await getApiUrl('citizens', { limit: '1000' });
      console.log('Fetching all citizens from URL:', url);

      // Get a valid token using our enhanced function
      const validToken = await getEnhancedValidToken();
      if (!validToken) {
        console.error('Could not get a valid token for citizens fetch');
        throw new Error('Authentication failed. Please log in again.');
      }

      console.log('Using token for citizens fetch:', validToken ? 'Token exists' : 'No token');

      // Make the API call with direct fetch
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${validToken}`
        },
        credentials: 'include'
      });

      if (!response.ok) {
        console.error('Failed to fetch citizens:', response.status, response.statusText);
        // Try to get more details about the error
        let errorText = '';
        try {
          errorText = await response.text();
          console.error('Error details:', errorText);
        } catch (e) {
          console.error('Could not parse error response');
        }

        console.error('Request URL:', url);

        // Get the schema name
        const schema = localStorage.getItem('schema_name') || tenant?.schema_name;

        console.error('Expected request headers:', {
          'X-Schema-Name': schema || 'NOT_FOUND',
          'Content-Type': 'application/json'
        });

        // Handle specific error cases
        if (response.status === 401) {
          console.warn('Received 401 for citizens endpoint, trying to create a new token');

          // Try to create a new token
          const newToken = await createNewToken();
          if (newToken) {
            console.log('Successfully created new token, retrying citizens fetch');

            // Get the formatted schema name
            const { getCurrentSchema, formatSchemaForUrl } = await import('../utils/schemaUtils');
            const schema = getCurrentSchema() || schemaName || tenant?.schema_name || '';
            const formattedSchema = formatSchemaForUrl(schema);

            // Try a direct fetch with the new token
            const directUrl = `/api/tenant/${encodeURIComponent(formattedSchema)}/citizens/?limit=1000`;
            console.log('Retrying with direct URL:', directUrl);

            const directResponse = await fetch(directUrl, {
              method: 'GET',
              headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${newToken}`
              },
              credentials: 'include'
            });

            if (directResponse.ok) {
              const data = await directResponse.json();
              console.log(`Fetched ${data.length} citizens with authenticated fetch`);
              setCitizens(data);
              setFilteredFatherCitizens([]);
              setFilteredEmergencyContactCitizens([]);
              setFilteredSpouseCitizens([]);
              setLoadingCitizens(false);
              return;
            } else {
              console.error('Authenticated fetch also failed:', directResponse.status, directResponse.statusText);
            }
          } else {
            console.error('Failed to get a fresh token');
          }
        }

        throw new Error(`Failed to fetch citizens: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      console.log(`Fetched ${data.length} citizens:`, data);

      // Log the first citizen to see its structure
      if (data.length > 0) {
        console.log('First citizen data structure:', JSON.stringify(data[0], null, 2));
        console.log('Registration number field:', data[0].registration_number);
        console.log('ID number field:', data[0].id_number);
      }

      setCitizens(data);

      // Don't initialize filtered citizens with all citizens
      // We'll fetch them on demand when the user searches
      setFilteredFatherCitizens([]);
      setFilteredEmergencyContactCitizens([]);
      setFilteredSpouseCitizens([]);

      console.log('Citizens loaded, but filtered lists are empty until search');
    } catch (error: any) {
      console.error('Error fetching citizens:', error);
      // Set error message
      setCitizensError(error.message || 'Failed to fetch citizens. Please try again later.');
      // Set empty arrays if we can't fetch citizens
      setCitizens([]);
      setFilteredFatherCitizens([]);
      setFilteredEmergencyContactCitizens([]);
      setFilteredSpouseCitizens([]);
    } finally {
      setLoadingCitizens(false);
    }
  };



  // Old search function removed to avoid duplicate declaration



  // Search function for mother citizens
  const searchMotherCitizens = async (query: string) => {
    console.log('Searching for mother citizens with query:', query);
    console.log('Total citizens in memory:', citizens.length);

    // If citizens array is empty, try to fetch them first
    if (citizens.length === 0) {
      console.log('No citizens in memory, fetching them first...');
      await fetchCitizens();
    }

    // Log the gender distribution
    const maleCount = citizens.filter(c => c.gender === 'M').length;
    const femaleCount = citizens.filter(c => c.gender === 'F').length;
    console.log(`Gender distribution - Male: ${maleCount}, Female: ${femaleCount}, Unknown: ${citizens.length - maleCount - femaleCount}`);

    if (!query || query.length < 2) {
      setMotherSearchResults([]);
      setSearchingMother(false);
      return;
    }

    try {
      // First check if we have any female citizens at all
      if (femaleCount === 0) {
        console.log('No female citizens found in the database');
        setMotherSearchResults([]);
        return;
      }

      // Log a sample of citizens to check their structure
      if (citizens.length > 0) {
        console.log('Sample citizen data:', citizens[0]);
        console.log('Sample citizen gender:', citizens[0].gender);
      }

      // Try a more lenient search first - just filter by gender
      const allFemales = citizens.filter(citizen => citizen.gender === 'F');
      console.log(`Found ${allFemales.length} total female citizens`);

      // Now apply the search filter
      const filteredResults = allFemales.filter(citizen => {
        const searchQuery = query.toLowerCase();
        return (
          citizen.first_name?.toLowerCase().includes(searchQuery) ||
          citizen.middle_name?.toLowerCase().includes(searchQuery) ||
          citizen.last_name?.toLowerCase().includes(searchQuery) ||
          // Also search for full name matches
          `${citizen.first_name} ${citizen.middle_name || ''} ${citizen.last_name}`.toLowerCase().includes(searchQuery) ||
          // Search by ID and phone
          citizen.id_number?.toLowerCase().includes(searchQuery) ||
          citizen.registration_number?.toLowerCase().includes(searchQuery) ||
          citizen.phone?.toLowerCase().includes(searchQuery)
        );
      });

      console.log(`Found ${filteredResults.length} matching female citizens`);

      // If no results with strict filtering, show all females
      if (filteredResults.length === 0 && query.length <= 3) {
        console.log('No matches with strict filtering, showing all females');
        setMotherSearchResults(allFemales);
      } else {
        setMotherSearchResults(filteredResults);
      }
    } catch (error) {
      console.error('Error searching for mother citizens:', error);
      setMotherSearchResults([]);
    } finally {
      setSearchingMother(false);
    }
  };

  // Search function for father citizens
  const searchFatherCitizens = async (query: string) => {
    console.log('Searching for father citizens with query:', query);
    console.log('Total citizens in memory:', citizens.length);

    // If citizens array is empty, try to fetch them first
    if (citizens.length === 0) {
      console.log('No citizens in memory, fetching them first...');
      await fetchCitizens();
    }

    // Check all possible gender values in the database
    const genderValues = new Set();
    citizens.forEach(c => genderValues.add(c.gender));
    console.log('All gender values in database:', Array.from(genderValues));

    // Log the gender distribution
    const maleCount = citizens.filter(c => c.gender === 'M').length;
    const femaleCount = citizens.filter(c => c.gender === 'F').length;
    console.log(`Gender distribution - Male: ${maleCount}, Female: ${femaleCount}, Unknown: ${citizens.length - maleCount - femaleCount}`);

    // Don't clear results if the query is empty or too short
    // This allows the dropdown to stay open with previous results
    if (!query || query.length < 2) {
      console.log('Query too short, keeping existing results');
      setSearchingFather(false);
      return;
    }

    try {
      // First check if we have any male citizens at all
      if (maleCount === 0) {
        console.log('No male citizens found in the database');
        // Try a more flexible approach - maybe the gender is stored differently
        const possibleMales = citizens.filter(c =>
          c.gender === 'M' ||
          c.gender === 'm' ||
          c.gender === 'Male' ||
          c.gender === 'male'
        );

        if (possibleMales.length > 0) {
          console.log(`Found ${possibleMales.length} possible male citizens with flexible gender matching`);

          // Apply the search filter
          const filteredResults = possibleMales.filter(citizen =>
            citizen.first_name?.toLowerCase().includes(query.toLowerCase()) ||
            citizen.last_name?.toLowerCase().includes(query.toLowerCase()) ||
            citizen.id_number?.toLowerCase().includes(query.toLowerCase()) ||
            citizen.registration_number?.toLowerCase().includes(query.toLowerCase()) ||
            citizen.phone?.toLowerCase().includes(query.toLowerCase())
          );

          console.log(`Found ${filteredResults.length} matching possible male citizens`);
          setFatherSearchResults(filteredResults.length > 0 ? filteredResults : possibleMales);
        } else {
          // As a last resort, just show all citizens
          console.log('No male citizens found with any gender matching, showing all citizens');
          setFatherSearchResults(citizens);
        }
        return;
      }

      // Log a sample of citizens to check their structure
      if (citizens.length > 0) {
        console.log('Sample citizen data:', citizens[0]);
        console.log('Sample citizen gender:', citizens[0].gender);
      }

      // Try a more lenient search first - just filter by gender
      const allMales = citizens.filter(citizen => citizen.gender === 'M');
      console.log(`Found ${allMales.length} total male citizens`);

      // Now apply the search filter
      const filteredResults = allMales.filter(citizen => {
        const searchQuery = query.toLowerCase();
        return (
          citizen.first_name?.toLowerCase().includes(searchQuery) ||
          citizen.middle_name?.toLowerCase().includes(searchQuery) ||
          citizen.last_name?.toLowerCase().includes(searchQuery) ||
          // Also search for full name matches
          `${citizen.first_name} ${citizen.middle_name || ''} ${citizen.last_name}`.toLowerCase().includes(searchQuery) ||
          // Search by ID and phone
          citizen.id_number?.toLowerCase().includes(searchQuery) ||
          citizen.registration_number?.toLowerCase().includes(searchQuery) ||
          citizen.phone?.toLowerCase().includes(searchQuery)
        );
      });

      console.log(`Found ${filteredResults.length} matching male citizens`);

      // If no results with strict filtering, show all males
      if (filteredResults.length === 0) {
        console.log('No matches with strict filtering, showing all males');
        setFatherSearchResults(allMales);

        // Log the results we're setting
        console.log('Setting father search results to all males:', allMales);
      } else {
        setFatherSearchResults(filteredResults);

        // Log the results we're setting
        console.log('Setting father search results to filtered males:', filteredResults);
      }

      // Double-check that we have results after setting
      setTimeout(() => {
        console.log('Father search results after setting:', fatherSearchResults);
      }, 100);
    } catch (error) {
      console.error('Error searching for father citizens:', error);
      setFatherSearchResults([]);
    } finally {
      setSearchingFather(false);
    }
  };

  // No longer needed

  // This function is no longer needed as we're handling search in the Autocomplete component

  // Search function for emergency contact citizens
  const searchEmergencyContactCitizens = async (query: string) => {
    console.log('Searching for emergency contact citizens with query:', query);
    console.log('Total citizens in memory:', citizens.length);

    // If citizens array is empty, try to fetch them first
    if (citizens.length === 0) {
      console.log('No citizens in memory, fetching them first...');
      await fetchCitizens();
    }

    // Check all possible gender values in the database
    const genderValues = new Set();
    citizens.forEach(c => genderValues.add(c.gender));
    console.log('All gender values in database:', Array.from(genderValues));

    if (!query || query.length < 2) {
      console.log('Query too short, keeping existing results');
      setSearchingEmergencyContact(false);
      return;
    }

    try {
      // Apply the search filter to all citizens
      const filteredResults = citizens.filter(citizen => {
        const searchQuery = query.toLowerCase();
        return (
          citizen.first_name?.toLowerCase().includes(searchQuery) ||
          citizen.middle_name?.toLowerCase().includes(searchQuery) ||
          citizen.last_name?.toLowerCase().includes(searchQuery) ||
          // Also search for full name matches
          `${citizen.first_name} ${citizen.middle_name || ''} ${citizen.last_name}`.toLowerCase().includes(searchQuery) ||
          // Search by ID and phone
          citizen.id_number?.toLowerCase().includes(searchQuery) ||
          citizen.registration_number?.toLowerCase().includes(searchQuery) ||
          citizen.phone?.toLowerCase().includes(searchQuery)
        );
      });

      console.log(`Found ${filteredResults.length} matching citizens for emergency contact`);

      // If no results with strict filtering, show all citizens
      if (filteredResults.length === 0) {
        console.log('No matches with strict filtering, showing all citizens');
        setEmergencyContactSearchResults(citizens);

        // Log the results we're setting
        console.log('Setting emergency contact search results to all citizens:', citizens);
      } else {
        setEmergencyContactSearchResults(filteredResults);

        // Log the results we're setting
        console.log('Setting emergency contact search results to filtered citizens:', filteredResults);
      }
    } catch (error) {
      console.error('Error searching for emergency contact citizens:', error);
      setEmergencyContactSearchResults([]);
    } finally {
      setSearchingEmergencyContact(false);
    }
  };

  // This function is no longer needed as we're handling search in the Autocomplete component

  // Search function for spouse citizens
  const searchSpouseCitizens = async (query: string) => {
    console.log('Searching for spouse citizens with query:', query);
    console.log('Total citizens in memory:', citizens.length);
    console.log('Current citizen gender:', formData.gender);

    // If citizens array is empty, try to fetch them first
    if (citizens.length === 0) {
      console.log('No citizens in memory, fetching them first...');
      await fetchCitizens();
    }

    // Check all possible gender values in the database
    const genderValues = new Set();
    citizens.forEach(c => genderValues.add(c.gender));
    console.log('All gender values in database:', Array.from(genderValues));

    if (!query || query.length < 2) {
      console.log('Query too short, keeping existing results');
      setSearchingSpouse(false);
      return;
    }

    try {
      // Determine which gender to filter for based on the current citizen's gender
      let targetGender = '';
      if (formData.gender === 'M') {
        // If citizen is male, show only female citizens as potential spouses
        targetGender = 'F';
      } else if (formData.gender === 'F') {
        // If citizen is female, show only male citizens as potential spouses
        targetGender = 'M';
      }

      console.log('Target gender for spouse search:', targetGender);

      // If no gender is specified, we can't filter properly
      if (!targetGender) {
        console.log('No gender specified for current citizen, showing all citizens');
        const filteredResults = citizens.filter(citizen => {
          const searchQuery = query.toLowerCase();
          return (
            citizen.first_name?.toLowerCase().includes(searchQuery) ||
            citizen.middle_name?.toLowerCase().includes(searchQuery) ||
            citizen.last_name?.toLowerCase().includes(searchQuery) ||
            // Also search for full name matches
            `${citizen.first_name} ${citizen.middle_name || ''} ${citizen.last_name}`.toLowerCase().includes(searchQuery) ||
            // Search by ID and phone
            citizen.id_number?.toLowerCase().includes(searchQuery) ||
            citizen.registration_number?.toLowerCase().includes(searchQuery) ||
            citizen.phone?.toLowerCase().includes(searchQuery)
          );
        });

        console.log(`Found ${filteredResults.length} matching citizens`);
        setSpouseSearchResults(filteredResults);
        return;
      }

      // Filter citizens by the target gender
      const potentialSpouses = citizens.filter(citizen => citizen.gender === targetGender);
      console.log(`Found ${potentialSpouses.length} citizens with gender ${targetGender}`);

      // Now apply the search filter
      const filteredResults = potentialSpouses.filter(citizen => {
        const searchQuery = query.toLowerCase();
        return (
          citizen.first_name?.toLowerCase().includes(searchQuery) ||
          citizen.middle_name?.toLowerCase().includes(searchQuery) ||
          citizen.last_name?.toLowerCase().includes(searchQuery) ||
          // Also search for full name matches
          `${citizen.first_name} ${citizen.middle_name || ''} ${citizen.last_name}`.toLowerCase().includes(searchQuery) ||
          // Search by ID and phone
          citizen.id_number?.toLowerCase().includes(searchQuery) ||
          citizen.registration_number?.toLowerCase().includes(searchQuery) ||
          citizen.phone?.toLowerCase().includes(searchQuery)
        );
      });

      console.log(`Found ${filteredResults.length} matching potential spouses`);

      // If no results with strict filtering, show all potential spouses
      if (filteredResults.length === 0) {
        console.log('No matches with strict filtering, showing all potential spouses');
        setSpouseSearchResults(potentialSpouses);

        // Log the results we're setting
        console.log('Setting spouse search results to all potential spouses:', potentialSpouses);
      } else {
        setSpouseSearchResults(filteredResults);

        // Log the results we're setting
        console.log('Setting spouse search results to filtered potential spouses:', filteredResults);
      }
    } catch (error) {
      console.error('Error searching for spouse citizens:', error);
      setSpouseSearchResults([]);
    } finally {
      setSearchingSpouse(false);
    }
  };

  // This function is no longer needed as we're handling search in the Autocomplete component

  const fetchRelationshipTypes = async () => {
    setLoadingRelationshipTypes(true);
    try {
      // Fetch relationship types from common API endpoint
      const url = '/api/common/relationship-types/';
      console.log('Fetching relationship types from common API:', url);

      // For testing, don't use authentication
      const response = await fetch(url);

      if (!response.ok) {
        throw new Error(`Failed to fetch relationship types: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      console.log('Fetched relationship types from API:', data);

      // Extract the results array from the response
      const results = data.results || data;
      console.log('Relationship type results:', results);

      setRelationshipTypes(results);
    } catch (error) {
      console.error('Error fetching relationship types:', error);
      // Don't set fallback values, just show an empty array
      setRelationshipTypes([]);
    } finally {
      setLoadingRelationshipTypes(false);
    }
  };

  // Utility function to format API URLs consistently for tenant-specific endpoints
  const getApiUrl = async (endpoint: string, queryParams: Record<string, string> = {}) => {
    // Import schema utilities
    const { getCurrentSchema, formatSchemaForUrl, setCurrentSchema, extractSchemaFromUrl } = await import('../utils/schemaUtils');
    // Import API config
    const { API_BASE_URL } = await import('../config/apiConfig');

    // First try to get schema name using the utility function
    let schema = getCurrentSchema();

    // If not found in localStorage, fall back to component state or tenant object
    if (!schema) {
      schema = schemaName || tenant?.schema_name || '';

      // If we found a schema from component state or tenant, save it
      if (schema) {
        setCurrentSchema(schema);
      }
    }

    // Always add detail=true parameter for citizens endpoint to allow unauthenticated access
    if (endpoint === 'citizens' || endpoint.startsWith('citizens/')) {
      queryParams.detail = 'true';
      console.log('Adding detail=true parameter to citizens endpoint for unauthenticated access');
    }

    console.log('Using schema name for API call:', schema);

    // If still no schema name, try to extract it from the URL
    if (!schema) {
      const currentPath = window.location.pathname;
      const extractedSchema = extractSchemaFromUrl(currentPath);

      if (extractedSchema) {
        schema = extractedSchema;
        console.log('Extracted schema name from URL:', schema);

        // Save it to localStorage for future use
        setCurrentSchema(schema);
      }
    }

    // If still no schema, try to get it from cookies
    if (!schema) {
      const schemaCookie = document.cookie
        .split('; ')
        .find(row => row.startsWith('schema_name='))
        ?.split('=')[1];

      if (schemaCookie) {
        try {
          schema = decodeURIComponent(schemaCookie);
          console.log('Using schema name from cookie:', schema);
          setCurrentSchema(schema);
        } catch (error) {
          console.error('Error decoding schema cookie:', error);
        }
      }
    }

    // If still no schema, show an error
    if (!schema) {
      console.error('No schema found, cannot proceed with registration');
      setError('No tenant schema found. Please log in again or contact your administrator.');
      setLoading(false);
      return;
    }

    // Format the schema name for URL (converting spaces to underscores)
    const urlFormattedSchema = formatSchemaForUrl(schema);

    // Use the correct API endpoint format based on the backend URL patterns
    // Use absolute URL with the correct API base URL
    let url = `${API_BASE_URL}/api/tenant/${encodeURIComponent(urlFormattedSchema)}/${endpoint}/`;
    console.log('FINAL URL for API call:', url);

    // IMPORTANT: Always set the schema name as a cookie for backend compatibility
    // Make sure to set the path to / so it's available for all requests
    document.cookie = `schema_name=${encodeURIComponent(schema)}; path=/; SameSite=Lax`;
    console.log('Set schema_name cookie to:', schema);

    // Log all cookies for debugging
    console.log('All cookies:', document.cookie);

    // Check if we have a token in localStorage
    const currentToken = localStorage.getItem('token');
    if (!currentToken) {
      console.warn('No token found in localStorage');

      // Try to get the adminToken from cookies as a fallback
      const adminToken = document.cookie
        .split('; ')
        .find(row => row.startsWith('adminToken='))
        ?.split('=')[1];

      if (adminToken) {
        console.log('Using adminToken from cookie as fallback');
        // Store it in localStorage for future use
        localStorage.setItem('token', adminToken);
      } else {
        console.error('No token available. Authentication will likely fail.');
      }
    } else {
      console.log('Using token from localStorage');
    }

    // Skip tenant info check for citizens endpoint since we've modified the backend to allow unauthenticated access
    if (endpoint !== 'citizens') {
      // For debugging, also try to fetch the tenant info to verify it exists
      // Include the schema name in the headers
      fetch(`${API_BASE_URL}/api/tenant-info/${encodeURIComponent(urlFormattedSchema)}/`, {
        headers: {
          'X-Schema-Name': schema,
          'Authorization': `Token ${localStorage.getItem('token') || document.cookie
            .split('; ')
            .find(row => row.startsWith('adminToken='))
            ?.split('=')[1] || ''}`
        }
      })
        .then(response => response.json())
        .then(data => console.log('Tenant info check:', data))
        .catch(error => console.error('Error checking tenant info:', error));
    }

    // Add query parameters if any
    const queryString = Object.entries(queryParams)
      .map(([key, value]) => `${encodeURIComponent(key)}=${encodeURIComponent(value)}`)
      .join('&');

    if (queryString) {
      url += `?${queryString}`;
    }

    return url;
  };

  // Utility function to get URLs for common data (not tenant-specific)
  const getCommonApiUrl = async (endpoint: string) => {
    // Import API config
    const { API_BASE_URL } = await import('../config/apiConfig');
    return `${API_BASE_URL}/api/common/${endpoint}/`;
  };

  // Custom fetchWithAuth function specifically for this component
  const customFetchWithAuth = async (url: string, options: RequestInit = {}) => {
    console.log('Using customFetchWithAuth for request to:', url);

    // Get token from any available source
    const token = localStorage.getItem('token') ||
                 document.cookie.split('; ').find(row => row.startsWith('adminToken='))?.split('=')[1];

    if (!token) {
      console.error('No token available from any source!');
    } else {
      console.log('Using token for request:', token ? 'Token exists' : 'No token');
    }

    // Get schema name from any available source
    const schema = localStorage.getItem('schema_name') ||
                  tenant?.schema_name ||
                  document.cookie.split('; ').find(row => row.startsWith('schema_name='))?.split('=')[1] ||
                  'kebele16';

    console.log('Using schema for request:', schema);

    // Always set the schema name cookie
    document.cookie = `schema_name=${encodeURIComponent(schema)}; path=/; SameSite=Lax`;

    // Prepare headers
    const headers = new Headers(options.headers || {});

    // Always include the Authorization header
    if (token) {
      headers.set('Authorization', `Token ${token}`);
    }

    // Always include the X-Schema-Name header
    headers.set('X-Schema-Name', schema);

    // Log the request details
    console.log('Request URL:', url);
    console.log('Request headers:', Object.fromEntries(headers.entries()));

    // Make the request
    return fetch(url, {
      ...options,
      headers,
      credentials: 'include'
    });
  };

  // Function to fetch a specific citizen by ID
  const fetchCitizenById = async (citizenId: string | number) => {
    try {
      console.log('Fetching specific citizen with ID:', citizenId);

      // Use the getApiUrl function to get the correct URL with schema name
      const url = await getApiUrl(`citizens/${citizenId}`);
      console.log('Fetching citizen from URL:', url);

      // Get a valid token using our enhanced function
      const validToken = await getEnhancedValidToken();
      if (!validToken) {
        console.error('Could not get a valid token for citizen fetch');
        throw new Error('Authentication failed. Please log in again.');
      }

      console.log('Using token for citizen fetch:', validToken ? 'Token exists' : 'No token');

      // Make the API call with direct fetch
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${validToken}`
        },
        credentials: 'include'
      });

      if (!response.ok) {
        console.error('Failed to fetch citizen:', response.status, response.statusText);

        // If we get a 401, try to create a new token and retry
        if (response.status === 401) {
          console.warn('Received 401 for citizen endpoint, trying to create a new token');

          // Try to create a new token
          const newToken = await createNewToken();
          if (newToken) {
            console.log('Successfully created new token, retrying citizen fetch');

            // Get the formatted schema name
            const { getCurrentSchema, formatSchemaForUrl } = await import('../utils/schemaUtils');
            const schema = getCurrentSchema() || schemaName || tenant?.schema_name || '';
            const formattedSchema = formatSchemaForUrl(schema);

            // Try a direct fetch with the new token
            const directUrl = `/api/tenant/${encodeURIComponent(formattedSchema)}/citizens/${citizenId}/`;
            console.log('Retrying with direct URL:', directUrl);

            const retryResponse = await fetch(directUrl, {
              method: 'GET',
              headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${newToken}`
              },
              credentials: 'include'
            });

            if (retryResponse.ok) {
              const data = await retryResponse.json();
              console.log('Successfully fetched citizen data after token refresh:', data);
              return data;
            } else {
              console.error('Retry with new token also failed:', retryResponse.status, retryResponse.statusText);
            }
          } else {
            console.error('Failed to create a new token');
          }
        }

        return null;
      }

      const data = await response.json();
      console.log('Fetched citizen data:', data);
      return data;
    } catch (error) {
      console.error('Error fetching citizen by ID:', error);
      return null;
    }
  };

  // Function to fetch fresh tenant data from the API
  const fetchTenantFromAPI = async () => {
    try {
      if (!tenant || !tenant.id) {
        console.log('Cannot fetch tenant from API: No tenant ID available');
        return;
      }

      console.log('Fetching tenant data directly from API for ID:', tenant.id);
      // Use the tenant-info endpoint instead of tenants endpoint
      const url = `/api/tenant-info/${tenant.id}/`;
      console.log('Using tenant-info endpoint:', url);

      // Get the current schema name
      const currentSchema = localStorage.getItem('schema_name') || tenant?.schema_name || '';
      if (!currentSchema) {
        console.error('No schema name available for API request');
        return;
      }

      // Use direct fetch with schema name in headers
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'X-Schema-Name': currentSchema
        },
        credentials: 'include'
      });

      if (!response.ok) {
        console.error(`Failed to fetch tenant from API: ${response.status} ${response.statusText}`);
        return;
      }

      const freshTenantData = await response.json();
      console.log('Fresh tenant data from API:', freshTenantData);
      console.log('Parent ID from API:', freshTenantData.parent);
      console.log('Parent ID from API (parent_id):', freshTenantData.parent_id);

      // Extract parent information from the API response
      const parentId = freshTenantData.parent_id || freshTenantData.parent || '';
      const parentName = freshTenantData.parent_name || '';
      const parentSchemaName = freshTenantData.parent_schema_name || '';

      console.log('API response parent information:');
      console.log('  parent_id:', parentId);
      console.log('  parent_name:', parentName);
      console.log('  parent_schema_name:', parentSchemaName);

      if (parentId) {
        console.log('Found parent ID in API response:', parentId);

        // Get the current tenant from localStorage
        const tenantString = localStorage.getItem('tenant');
        if (tenantString) {
          // Parse the tenant object
          const storedTenant = JSON.parse(tenantString);

          // Update with parent information
          const updatedTenant = {
            ...storedTenant,
            parent_id: parentId,
            parent_name: parentName,
            parent_schema_name: parentSchemaName
          };

          // Save back to localStorage
          localStorage.setItem('tenant', JSON.stringify(updatedTenant));
          console.log('Updated tenant in localStorage with parent information:', updatedTenant);

          // Update the tenant state
          setTenant(updatedTenant);

          // Set the subcity and kebele values immediately
          setFormData(prev => ({
            ...prev,
            subcity: parentId,
            kebele: tenant.id || ''
          }));

          // Fetch kebeles for this subcity
          fetchKebeles(parentId);

          // Force a delay to ensure the values are set after the component is fully rendered
          setTimeout(() => {
            console.log('DELAYED EFFECT: Forcing location values after API fetch');
            setFormData(prev => ({
              ...prev,
              subcity: parentId,
              kebele: tenant.id || ''
            }));
          }, 500);

          console.log('Successfully updated tenant with parent information');
          return;
        }
      } else {
        console.log('No parent information found in API response');

        // Try to fetch parent information from a different endpoint
        try {
          console.log('Attempting to fetch parent information from tenant hierarchy endpoint');
          const hierarchyUrl = `/api/tenant-hierarchy/${tenant.id}/`;

          const hierarchyResponse = await fetch(hierarchyUrl, {
            method: 'GET',
            headers: {
              'Content-Type': 'application/json',
              'X-Schema-Name': tenant.schema_name || ''
            },
            credentials: 'include'
          });

          if (hierarchyResponse.ok) {
            const hierarchyData = await hierarchyResponse.json();
            console.log('Hierarchy data:', hierarchyData);

            if (hierarchyData.parent) {
              const parentFromHierarchy = hierarchyData.parent;
              console.log('Found parent in hierarchy:', parentFromHierarchy);

              // Update tenant with parent information
              const updatedTenant = {
                ...tenant,
                parent_id: parentFromHierarchy.id,
                parent_name: parentFromHierarchy.name,
                parent_schema_name: parentFromHierarchy.schema_name
              };

              // Save to localStorage
              localStorage.setItem('tenant', JSON.stringify(updatedTenant));
              console.log('Updated tenant with parent from hierarchy:', updatedTenant);

              // Update the tenant state
              setTenant(updatedTenant);

              // Set the subcity and kebele values
              setFormData(prev => ({
                ...prev,
                subcity: parentFromHierarchy.id,
                kebele: tenant.id || ''
              }));

              // Fetch kebeles for this subcity
              fetchKebeles(parentFromHierarchy.id);

              return;
            }
          }
        } catch (hierarchyError) {
          console.error('Error fetching tenant hierarchy:', hierarchyError);
        }

        console.log('Could not find parent information from any source');
        // Don't show an alert, just log the issue
      }
    } catch (error) {
      console.error('Error fetching tenant from API:', error);
    }
  };

  // Debug function to log tenant information
  const debugTenantInfo = () => {
    console.log('DEBUG: Checking tenant information');

    // Check tenant from context
    console.log('Tenant from context:', tenant);

    // Check tenant from localStorage
    const tenantString = localStorage.getItem('tenant');
    if (tenantString) {
      try {
        const localTenant = JSON.parse(tenantString);
        console.log('Tenant from localStorage:', localTenant);
        console.log('  ID:', localTenant.id);
        console.log('  Name:', localTenant.name);
        console.log('  Type:', localTenant.type);
        console.log('  Parent ID:', localTenant.parent_id);
        console.log('  Parent Name:', localTenant.parent_name);
        console.log('  Schema Name:', localTenant.schema_name);
      } catch (error) {
        console.error('Error parsing tenant from localStorage:', error);
      }
    } else {
      console.log('No tenant found in localStorage');
    }

    // Check all localStorage keys
    console.log('All localStorage keys:');
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      console.log(`  ${key}`);
    }
  };

  // Function to store user credentials for automatic token creation
  const storeUserCredentials = () => {
    try {
      // Check if we already have credentials stored
      if (localStorage.getItem('username') && localStorage.getItem('password')) {
        console.log('User credentials already stored in localStorage');
        return;
      }

      // Try to get credentials from the login form if they were saved
      const savedLoginData = localStorage.getItem('loginData');
      if (savedLoginData) {
        try {
          const loginData = JSON.parse(savedLoginData);
          if (loginData.username && loginData.password) {
            console.log('Storing user credentials from saved login data');
            localStorage.setItem('username', loginData.username);
            localStorage.setItem('password', loginData.password);
            return;
          }
        } catch (error) {
          console.error('Error parsing saved login data:', error);
        }
      }

      // If we don't have saved credentials, use default admin credentials
      // This is a fallback for development/testing only
      console.log('Using default admin credentials as fallback');
      localStorage.setItem('username', 'admin');
      localStorage.setItem('password', 'admin');
    } catch (error) {
      console.error('Error storing user credentials:', error);
    }
  };

  // Auto-generate registration number if empty and fetch dropdown data
  useEffect(() => {
    console.log('Initial useEffect running...');
    console.log('Current tenant:', JSON.stringify(tenant, null, 2));

    // Store user credentials for automatic token creation
    storeUserCredentials();

    // Run the debug function
    debugTenantInfo();

    // First try to get tenant information directly from localStorage
    const tenantString = localStorage.getItem('tenant');
    let localTenant = null;

    if (tenantString) {
      try {
        localTenant = JSON.parse(tenantString);
        console.log('Parsed tenant from localStorage in initial useEffect:', localTenant);
      } catch (error) {
        console.error('Error parsing tenant from localStorage:', error);
      }
    }

    // Check if we have saved form data in local storage
    if (hasStoredFormData(FORM_STORAGE_KEY)) {
      const savedData = loadFormData(FORM_STORAGE_KEY);
      if (savedData) {
        console.log('Loaded saved form data from local storage');
        setFormData(prevData => ({
          ...prevData,
          ...savedData
        }));
      }
    } else {
      // Set initial values for the form
      const initialValues = getInitialFormData();

      // Auto-fill subcity and kebele from localStorage tenant
      if (localTenant) {
        console.log('Auto-filling subcity and kebele from localStorage tenant');

        // Get subcity from parent tenant
        const parentSubcityId = localTenant.parent_id || '';
        const parentSubcityName = localTenant.parent_name || '';

        // Get kebele from current tenant
        const kebeleId = localTenant.id || '';
        const kebeleName = localTenant.name || '';

        console.log('Setting location values from localStorage:');
        console.log('  subcity (from parent tenant):', parentSubcityId, `(${parentSubcityName})`);
        console.log('  kebele (from current tenant):', kebeleId, `(${kebeleName})`);

        // Update the initial values
        initialValues.subcity = parentSubcityId;
        initialValues.kebele = kebeleId;

        // Also populate the subcities and kebeles arrays for the dropdowns
        if (parentSubcityId && parentSubcityName) {
          console.log('Setting subcities array with parent tenant');
          setSubcities([{
            id: parentSubcityId,
            name: parentSubcityName,
            city: '1' // Assuming city ID 1
          }]);
        }

        if (kebeleId && kebeleName) {
          console.log('Setting kebeles array with current tenant');
          setKebeles([{
            id: kebeleId,
            name: kebeleName,
            subcity: parentSubcityId,
            code: kebeleId.toString().padStart(2, '0')
          }]);
        }
      }

      setFormData(initialValues);
    }

    // If we're in edit mode, load data from the API
    if (mode !== 'full' && citizenId) {
      fetchCitizenById(citizenId).then(citizenData => {
        if (citizenData) {
          // Pre-fill form with citizen data
          setFormData(prevData => ({
            ...prevData,
            ...citizenData
          }));
        }
      });
    }

    // Note: We're not using authentication for testing the common API endpoints
    console.log('Fetching common data without authentication for testing');

    // Fetch common dropdown data from API endpoints
    console.log('Fetching common data (Religion, CitizenStatus, MaritalStatus, etc.) from API...');
    fetchReligions();
    fetchCitizenStatuses();
    fetchMaritalStatuses();
    fetchRelationshipTypes();
    fetchEmploymentTypes();

    // Fetch tenant-specific data
    fetchCountries();
    fetchRegions();

    // Fetch tenant parent information
    fetchAndSetTenantParent();

    // Log all tenant properties to understand its structure
    if (tenant) {
      console.log('Tenant ID:', tenant.id);
      console.log('Tenant type:', tenant.type);
      console.log('Tenant parent_id:', tenant.parent_id);
      console.log('Tenant schema_name:', tenant.schema_name);
      console.log('All tenant keys:', Object.keys(tenant));
    }

    // Auto-generate registration number
    if (!formData.registration_number) {
      const regNumber = generateRegistrationNumber();
      console.log('Generated registration number:', regNumber);
      setFormData(prev => ({ ...prev, registration_number: regNumber }));
    }

    // For kebele tenants, set up location information
    if (tenant?.type === 'KEBELE') {
      console.log('Setting up location information for kebele tenant');

      // First try to get tenant information directly from localStorage
      const tenantString = localStorage.getItem('tenant');
      let localTenant = null;

      if (tenantString) {
        try {
          localTenant = JSON.parse(tenantString);
          console.log('Parsed tenant from localStorage for location setup:', localTenant);

          // Get subcity from parent tenant
          const parentSubcityId = localTenant.parent_id || '';
          const parentSubcityName = localTenant.parent_name || '';

          // Get kebele from current tenant
          const kebeleId = localTenant.id || '';
          const kebeleName = localTenant.name || '';

          if (parentSubcityId && kebeleId) {
            console.log('Setting location values from localStorage:');
            console.log('  subcity (from parent tenant):', parentSubcityId, `(${parentSubcityName})`);
            console.log('  kebele (from current tenant):', kebeleId, `(${kebeleName})`);

            // Update the form data
            setFormData(prev => ({
              ...prev,
              subcity: parentSubcityId,
              kebele: kebeleId
            }));

            // Also populate the subcities and kebeles arrays for the dropdowns
            if (parentSubcityId && parentSubcityName) {
              console.log('Setting subcities array with parent tenant');
              setSubcities([{
                id: parentSubcityId,
                name: parentSubcityName,
                city: '1' // Assuming city ID 1
              }]);
            }

            if (kebeleId && kebeleName) {
              console.log('Setting kebeles array with current tenant');
              setKebeles([{
                id: kebeleId,
                name: kebeleName,
                subcity: parentSubcityId,
                code: kebeleId.toString().padStart(2, '0')
              }]);
            }

            return; // Skip the API calls if we have the data from localStorage
          }
        } catch (error) {
          console.error('Error parsing tenant from localStorage:', error);
        }
      }

      // If we couldn't get the data from localStorage, fall back to API calls
      console.log('Falling back to API calls for location information');

      // First fetch subcities - this will set the subcity value in the form data
      fetchSubcities();

      // Then fetch kebeles for the parent subcity
      const parentSubcityId = tenant?.parent_id || determineParentSubcity();
      if (parentSubcityId) {
        console.log('Fetching kebeles for parent subcity:', parentSubcityId);
        fetchKebeles(parentSubcityId);
      } else {
        console.warn('No parent subcity ID available, will try again after tenant API fetch');

        // Schedule another attempt after a delay
        setTimeout(() => {
          const updatedParentId = tenant?.parent_id || determineParentSubcity();
          if (updatedParentId) {
            console.log('Retrying kebele fetch with parent subcity:', updatedParentId);
            fetchKebeles(updatedParentId);
          }
        }, 2000);
      }
    } else {
      // For non-kebele tenants, fetch all subcities
      console.log('Fetching all subcities for non-kebele tenant');
      fetchSubcities();
    }

    // Fetch ketenas
    fetchKetenas();

    // Then fetch other tenant-specific data with a slight delay
    setTimeout(() => {
      console.log('Fetching additional tenant-specific data after delay...');
      fetchEmployeeTypes();
      fetchCitizens();
      // No need to re-fetch common data as it's already being fetched above
    }, 1000);

    // Force a refresh of the location values after a longer delay
    // This ensures the dropdowns are populated even if the initial fetch fails
    setTimeout(() => {
      console.log('Forcing refresh of location values');
      if (tenant?.type === 'KEBELE') {
        // Get tenant information from localStorage
        const tenantString = localStorage.getItem('tenant');
        let localTenant = null;

        if (tenantString) {
          try {
            localTenant = JSON.parse(tenantString);
            console.log('Parsed tenant from localStorage for delayed refresh:', localTenant);
          } catch (error) {
            console.error('Error parsing tenant from localStorage:', error);
          }
        }

        // Get subcity from parent tenant
        const parentSubcityId = localTenant?.parent_id || tenant?.parent_id || determineParentSubcity();
        const parentSubcityName = localTenant?.parent_name || tenant?.parent_name || 'Parent Subcity';

        // Get kebele from current tenant
        const kebeleId = localTenant?.id || tenant?.id || '';
        const kebeleName = localTenant?.name || tenant?.name || 'Current Kebele';

        // If we still don't have a kebele ID, try to get it from the schema_name
        let finalKebeleId = kebeleId;
        let finalKebeleName = kebeleName;

        if (!finalKebeleId || !finalKebeleName) {
          console.log('No kebele ID or name found, trying to extract from schema_name');
          const schemaName = localStorage.getItem('schema_name');
          if (schemaName && schemaName.startsWith('kebele')) {
            // Extract the kebele ID from the schema name (e.g., 'kebele14' -> '14')
            const extractedId = schemaName.replace('kebele', '');
            if (extractedId) {
              console.log('Extracted kebele ID from schema_name:', extractedId);
              finalKebeleId = extractedId;
              finalKebeleName = `Kebele ${extractedId}`;
            }
          }
        }

        // If we still don't have a kebele ID, use a hardcoded fallback
        if (!finalKebeleId || !finalKebeleName) {
          console.log('No kebele ID or name found, using hardcoded fallback');
          finalKebeleId = '14'; // Fallback to a default kebele ID
          finalKebeleName = 'Kebele 14'; // Fallback to a default kebele name
        }

        if (parentSubcityId) {
          console.log('Setting location values in delayed refresh:');
          console.log('  subcity:', parentSubcityId, `(${parentSubcityName})`);
          console.log('  kebele:', finalKebeleId, `(${finalKebeleName})`);

          // Update the form data
          setFormData(prev => ({
            ...prev,
            subcity: parentSubcityId,
            kebele: finalKebeleId
          }));

          // Directly update the subcities and kebeles arrays
          if (subcities.length === 0) {
            console.log('Setting subcities array in delayed refresh');
            setSubcities([{
              id: parentSubcityId,
              name: parentSubcityName,
              city: '1' // Assuming city ID 1
            }]);
          }

          if (kebeles.length === 0) {
            console.log('Setting kebeles array in delayed refresh');
            setKebeles([{
              id: finalKebeleId,
              name: finalKebeleName,
              subcity: parentSubcityId,
              code: finalKebeleId.toString().padStart(2, '0')
            }]);
          }
        }
      }
    }, 1000);
  }, []);

  // Fetch kebeles when subcity changes
  useEffect(() => {
    // Only fetch if subcity has a value and is different from the previous value
    if (formData.subcity) {
      // Store the current subcity ID to prevent unnecessary fetches
      const currentSubcityId = formData.subcity;
      console.log('Subcity changed to:', currentSubcityId);

      // Check if we already have kebeles for this subcity
      const alreadyFetched = kebeles.length > 0 && kebeles[0].subcity === currentSubcityId;

      if (!alreadyFetched) {
        console.log('Fetching kebeles for subcity:', currentSubcityId);
        fetchKebeles(currentSubcityId);
      } else {
        console.log('Already have kebeles for subcity:', currentSubcityId);
      }
    }
  }, [formData.subcity]);

  // Fetch ketenas when component mounts
  useEffect(() => {
    // Fetch all ketenas regardless of kebele
    console.log('Component mounted, fetching all ketenas');
    setKetenas([]); // Clear existing ketenas to force a fresh fetch
    fetchKetenas();
  }, []);

  // Fetch regions when country changes
  useEffect(() => {
    if (formData.nationality_country) {
      fetchRegions(formData.nationality_country);
    }
  }, [formData.nationality_country]);

  // Debug: Log form data changes and save to local storage
  useEffect(() => {
    console.log('Form data updated:', formData);
    console.log('Current subcity value:', formData.subcity);
    console.log('Current kebele value:', formData.kebele);

    // Save form data to local storage when it changes
    // Don't save empty form data
    if (formData.first_name || formData.last_name) {
      saveFormData(FORM_STORAGE_KEY, formData);
    }
  }, [formData]);

  // Debug: Log citizens state changes
  useEffect(() => {
    console.log('Citizens state changed, now has', citizens.length, 'citizens');
    // Log the first few citizens if available
    if (citizens.length > 0) {
      console.log('Sample citizens:', citizens.slice(0, 3));
    }
  }, [citizens]);



  // Effect to search citizens when father search query changes
  useEffect(() => {
    // Debounce the search to avoid too many API calls
    const timer = setTimeout(() => {
      if (formData.father_is_resident) {
        searchFatherCitizens();
      }
    }, 500);

    return () => clearTimeout(timer);
  }, [fatherSearchQuery]);

  // Effect to search citizens when emergency contact search query changes
  useEffect(() => {
    // Debounce the search to avoid too many API calls
    const timer = setTimeout(() => {
      if (formData.emergency_contact_is_resident) {
        searchEmergencyContactCitizens();
      }
    }, 500);

    return () => clearTimeout(timer);
  }, [emergencyContactSearchQuery]);

  // Effect to search citizens when spouse search query changes
  useEffect(() => {
    // Debounce the search to avoid too many API calls
    const timer = setTimeout(() => {
      if (formData.spouse_is_resident) {
        searchSpouseCitizens();
      }
    }, 500);

    return () => clearTimeout(timer);
  }, [spouseSearchQuery]);

  // Create a ref to track if we've already fetched citizens to prevent infinite loops
  const hasFetchedCitizensRef = useRef(false);

  // Effect to fetch citizens when component mounts
  useEffect(() => {
    if (!hasFetchedCitizensRef.current && tenant && tenant.schema_name) {
      console.log('Component mounted, fetching citizens...');
      // Fetch citizens with a slight delay to ensure other initialization is complete
      setTimeout(() => {
        console.log('Tenant information available, fetching citizens for:', tenant.schema_name);
        fetchCitizens();
        // Mark as fetched
        hasFetchedCitizensRef.current = true;
      }, 1000);
    } else if (!tenant || !tenant.schema_name) {
      console.warn('No tenant information available yet, cannot fetch citizens');
    }
  }, [tenant]);

  // Additional effect to ensure citizens are loaded
  useEffect(() => {
    // Force a fetch of citizens when the component mounts
    console.log('Additional effect to ensure citizens are loaded');
    if (tenant && tenant.schema_name) {
      console.log('Forcing citizen fetch on component mount');
      fetchCitizens();
    }
  }, []);

  // Force citizens to load when the component mounts
  useEffect(() => {
    // If we haven't fetched citizens yet, do it now
    if (citizens.length === 0 && tenant && tenant.schema_name) {
      console.log('No citizens loaded yet, forcing fetch...');
      fetchCitizens();
    }
  }, []);

  // We no longer initialize father search results when citizens are loaded
  // This ensures results are only loaded when the user starts typing

  // Effect to fetch reference data when component mounts
  useEffect(() => {
    console.log('Fetching reference data...');
    // Fetch common reference data
    fetchReligions();
    fetchCitizenStatuses();
    fetchMaritalStatuses();
    fetchKetenas();
    fetchEmployeeTypes();
    fetchEmploymentTypes();
  }, []);

  // Effect to update filtered citizens when citizens state changes
  useEffect(() => {
    console.log(`Citizens state changed, now has ${citizens.length} citizens`);

    // We don't need to update filtered citizens here anymore
    // We'll fetch them on demand when the user searches
    // This prevents loading all citizens at once
    console.log('Not pre-filtering citizens - will search on demand');

    // Only update filtered spouse citizens based on gender
    if (citizens.length > 0 && formData.gender) {
      console.log('Updating filtered spouse citizens based on gender');
      if (formData.gender === 'M') {
        // If citizen is male, show only female citizens as potential spouses
        const femaleCitizens = citizens.filter(citizen => citizen.gender === 'F');
        setFilteredSpouseCitizens(femaleCitizens);
        console.log(`Updated filtered spouse citizens to ${femaleCitizens.length} females`);
      } else if (formData.gender === 'F') {
        // If citizen is female, show only male citizens as potential spouses
        const maleCitizens = citizens.filter(citizen => citizen.gender === 'M');
        setFilteredSpouseCitizens(maleCitizens);
        console.log(`Updated filtered spouse citizens to ${maleCitizens.length} males`);
      }
    }
  }, [citizens, formData.gender]);

  // We've combined this effect with the citizens effect above
  // No need for a separate effect for gender changes

  // Function to get a valid JWT token
  const getJWTToken = async (): Promise<string | null> => {
    try {
      // Get the current schema
      const schema = getCurrentSchema() || schemaName || tenant?.schema_name || '';
      if (!schema) {
        console.error('No schema found for JWT token');
        return null;
      }

      console.log('Getting JWT token for schema:', schema);

      // Try to get the access token
      let accessToken = getAccessTokenForSchema(schema);

      // If no access token, try to refresh
      if (!accessToken) {
        console.log('No access token found, attempting to refresh');
        try {
          const refreshResult = await refreshJWTTokens(schema);
          if (refreshResult && refreshResult.access_token) {
            accessToken = refreshResult.access_token;
            console.log('Successfully refreshed JWT token');
          } else {
            console.error('Failed to refresh JWT token');

            // If refresh fails, try to create a new token via login
            console.log('Attempting to create a new token via login');
            const newToken = await createNewToken();
            if (newToken) {
              console.log('Successfully created new token via login');
              return newToken;
            }

            return null;
          }
        } catch (error) {
          console.error('Error refreshing JWT token:', error);

          // If refresh fails with an error, try to create a new token via login
          console.log('Attempting to create a new token via login after refresh error');
          const newToken = await createNewToken();
          if (newToken) {
            console.log('Successfully created new token via login after refresh error');
            return newToken;
          }

          return null;
        }
      }

      return accessToken;
    } catch (error) {
      console.error('Error getting JWT token:', error);
      return null;
    }
  };

  // Function to create a new token via login
  const createNewToken = async (): Promise<string | null> => {
    try {
      console.log('Attempting to create a new token via login API');

      // Get username and password from localStorage if available
      const username = localStorage.getItem('username');
      const password = localStorage.getItem('password');

      if (!username || !password) {
        console.error('No stored credentials found for automatic login');
        return null;
      }

      // Get the current schema
      const schema = getCurrentSchema() || schemaName || tenant?.schema_name || '';
      if (!schema) {
        console.error('No schema found for login');
        return null;
      }

      console.log('Creating new token for schema:', schema);

      // Try JWT login first
      try {
        console.log('Attempting JWT login');
        const jwtLoginUrl = `/api/jwt/login/`;
        const jwtResponse = await fetch(jwtLoginUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'X-Schema-Name': schema
          },
          body: JSON.stringify({
            email: username,
            password,
            schema_name: schema
          }),
          credentials: 'include'
        });

        if (jwtResponse.ok) {
          const jwtData = await jwtResponse.json();
          if (jwtData.access_token) {
            console.log('Successfully created new JWT token via login');

            // Store the new tokens
            if (schema) {
              storeTokensForSchema(schema, jwtData.access_token, jwtData.refresh_token || '');
            }

            return jwtData.access_token;
          }
        } else {
          console.warn('JWT login failed, falling back to token login');
        }
      } catch (jwtError) {
        console.error('Error during JWT login:', jwtError);
        console.warn('Falling back to token login');
      }

      // Fall back to regular token login
      console.log('Attempting regular token login');
      const loginUrl = `/api/login/`;
      const response = await fetch(loginUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Schema-Name': schema
        },
        body: JSON.stringify({
          email: username,
          password,
          schema_name: schema
        }),
        credentials: 'include'
      });

      if (!response.ok) {
        console.error('Login failed:', response.status, response.statusText);

        // If login fails, try the direct token creation endpoint
        console.log('Attempting to create token directly');
        try {
          const directTokenUrl = `/api/direct-token-create/`;
          const directTokenResponse = await fetch(directTokenUrl, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'X-Schema-Name': schema
            },
            body: JSON.stringify({
              email: username,
              schema_name: schema
            }),
            credentials: 'include'
          });

          if (directTokenResponse.ok) {
            const directTokenData = await directTokenResponse.json();
            if (directTokenData.token) {
              console.log('Successfully created token directly');

              // Store the new token
              localStorage.setItem('token', directTokenData.token);

              // Also store as JWT token if possible
              if (schema) {
                storeTokensForSchema(schema, directTokenData.token, '');
              }

              return directTokenData.token;
            }
          } else {
            console.error('Direct token creation failed:', directTokenResponse.status, directTokenResponse.statusText);
          }
        } catch (directTokenError) {
          console.error('Error creating token directly:', directTokenError);
        }

        // If all else fails, use a hardcoded token as a last resort
        console.log('All token creation methods failed, using hardcoded token as last resort');

        // This is a temporary solution for development/testing only
        // In production, you should never use hardcoded tokens
        const hardcodedToken = localStorage.getItem('hardcoded_token');
        if (hardcodedToken) {
          console.log('Using hardcoded token from localStorage');
          return hardcodedToken;
        }

        return null;
      }

      const data = await response.json();
      if (data.token) {
        console.log('Successfully created new token via login');

        // Store the new token
        localStorage.setItem('token', data.token);

        // Also store as JWT token if possible
        if (schema) {
          storeTokensForSchema(schema, data.token, '');
        }

        return data.token;
      } else {
        console.error('Login response did not contain a token');
        return null;
      }
    } catch (error) {
      console.error('Error creating new token via login:', error);
      return null;
    }
  };

  // Enhanced function to get a valid token from any source
  const getEnhancedValidToken = async (): Promise<string | null> => {
    // Use our guaranteed valid token function
    return getGuaranteedValidToken();
  };

  // Helper function to add related data (mother, father, spouse, emergency contact, etc.)
  const addRelatedData = async (
    citizenId: number,
    formData: FormValues,
    tenant: TenantInfo | null,
    csrftoken: string,
    customFetch: (url: string, options: RequestInit) => Promise<Response>
  ) => {
    console.log('Adding related data for citizen ID:', citizenId);

    // Add spouse information if applicable
    if (formData.marital_status !== 'Single' &&
        (formData.spouse_first_name || formData.spouse_linked_citizen)) {
      try {
        console.log('Adding spouse information...');

        // If spouse is a resident, fetch the complete citizen data
        let spouseCitizen = null;
        if (formData.spouse_is_resident && formData.spouse_linked_citizen) {
          console.log('Fetching complete spouse citizen data...');

          // Get a guaranteed valid token for the API call
          const spouseToken = await getGuaranteedValidToken();
          if (!spouseToken) {
            console.error('Could not get a valid token for spouse citizen fetch');
            throw new Error('Authentication failed. Please log in again.');
          }

          // Get the formatted schema name and API base URL
          const { formatSchemaForUrl } = await import('../utils/schemaUtils');
          const { API_BASE_URL } = await import('../config/apiConfig');
          const formattedSchema = formatSchemaForUrl(tenant?.schema_name || '');

          // Make a direct API call with our guaranteed token
          const spouseUrl = `${API_BASE_URL}/api/tenant/${encodeURIComponent(formattedSchema)}/citizens/${formData.spouse_linked_citizen}/`;
          console.log('Fetching spouse from URL:', spouseUrl);

          const spouseResponse = await fetch(spouseUrl, {
            method: 'GET',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${spouseToken}`,
              'X-Schema-Name': formattedSchema
            },
            credentials: 'include'
          });

          if (spouseResponse.ok) {
            spouseCitizen = await spouseResponse.json();
            console.log('Fetched spouse citizen data:', spouseCitizen);
          } else {
            console.error('Failed to fetch spouse citizen data:', spouseResponse.status, spouseResponse.statusText);
            // Try to find in the existing citizens array as fallback
            spouseCitizen = citizens.find(c => c.id === parseInt(formData.spouse_linked_citizen));
          }
        } else {
          // Try to find in the existing citizens array
          spouseCitizen = formData.spouse_linked_citizen ?
            citizens.find(c => c.id === parseInt(formData.spouse_linked_citizen)) : null;
        }

        const spouseData = {
          first_name: formData.spouse_is_resident && spouseCitizen ? spouseCitizen.first_name : (formData.spouse_first_name || ''),
          middle_name: formData.spouse_is_resident && spouseCitizen ? spouseCitizen.middle_name || '' : (formData.spouse_middle_name || ''),
          last_name: formData.spouse_is_resident && spouseCitizen ? spouseCitizen.last_name : (formData.spouse_last_name || ''),
          phone: formData.spouse_is_resident && spouseCitizen ? spouseCitizen.phone || '' : (formData.spouse_phone || ''),
          email: formData.spouse_is_resident && spouseCitizen ? spouseCitizen.email || '' : (formData.spouse_email || ''),
          is_resident: formData.spouse_is_resident || false,
          nationality: 1, // Assuming Ethiopia has ID 1
          primary_contact: true,
          linked_citizen: formData.spouse_linked_citizen || null,
          citizen: citizenId,  // Add the citizen ID
          center: tenant?.id  // Use the tenant ID for the center
        };

        console.log('Spouse data being sent:', spouseData);
        console.log('Spouse linked_citizen ID:', formData.spouse_linked_citizen,
          'converted to:', formData.spouse_linked_citizen ? parseInt(formData.spouse_linked_citizen) : null);

        // Use the tenant-specific family endpoint
        // Get the formatted schema name and API base URL
        const { formatSchemaForUrl } = await import('../utils/schemaUtils');
        const { API_BASE_URL } = await import('../config/apiConfig');
        const formattedSchema = formatSchemaForUrl(tenant?.schema_name || '');

        // Make sure citizenId is defined
        if (!citizenId) {
          console.error('Citizen ID is undefined, cannot add spouse information');
          return;
        }

        const spouseUrl = `${API_BASE_URL}/api/tenant/${encodeURIComponent(formattedSchema)}/citizens/${citizenId}/spouses/`;
        console.log('Spouse API URL:', spouseUrl);
        console.log('Spouse data:', spouseData);

        // Get a guaranteed valid token for the API call
        const spouseApiToken = await getGuaranteedValidToken();
        if (!spouseApiToken) {
          console.error('Could not get a valid token for spouse API call');
          throw new Error('Authentication failed. Please log in again.');
        }

        console.log('Using guaranteed valid token for spouse API call');

        // Make a direct API call with our guaranteed token
        const spouseResponse = await fetch(spouseUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${spouseApiToken}`,
            'X-Schema-Name': formattedSchema
          },
          body: JSON.stringify(spouseData),
          credentials: 'include'
        });

        if (!spouseResponse.ok) {
          // Handle 401 Unauthorized errors first
          if (spouseResponse.status === 401) {
            console.error('Authentication failed for spouse API call (401 Unauthorized)');
            console.warn('Continuing with registration process despite spouse API authentication failure');
            // Don't throw an error, just log it and continue
          } else {
            // For other errors, try to get more details
            let errorText = '';
            try {
              errorText = await spouseResponse.text();
              try {
                const errorJson = JSON.parse(errorText);
                console.error('Failed to add spouse information:', errorJson);
                console.error('Spouse data sent:', spouseData);
                console.error('Response status:', spouseResponse.status);
                console.error('Response status text:', spouseResponse.statusText);
                console.error('Error details:', errorJson);
              } catch (e) {
                console.error('Failed to add spouse information:', errorText);
                console.error('Spouse data sent:', spouseData);
                console.error('Response status:', spouseResponse.status);
                console.error('Response status text:', spouseResponse.statusText);
              }
            } catch (e) {
              console.error('Could not read spouse response text');
            }
          }
        } else {
          const responseData = await spouseResponse.json();
          console.log('Spouse information added successfully:', responseData);
        }
      } catch (spouseError) {
        console.error('Error adding spouse information:', spouseError);
      }
    }

    // Add mother information if applicable
    if (formData.mother_first_name || formData.mother_linked_citizen) {
      try {
        console.log('Adding mother information...');

        // If mother is a resident, fetch the complete citizen data
        let motherCitizen = null;
        if (formData.mother_is_resident && formData.mother_linked_citizen) {
          console.log('Fetching complete mother citizen data...');

          // Get a guaranteed valid token for the API call
          const motherToken = await getGuaranteedValidToken();
          if (!motherToken) {
            console.error('Could not get a valid token for mother citizen fetch');
            throw new Error('Authentication failed. Please log in again.');
          }

          // Get the formatted schema name and API base URL
          const { formatSchemaForUrl } = await import('../utils/schemaUtils');
          const { API_BASE_URL } = await import('../config/apiConfig');
          const formattedSchema = formatSchemaForUrl(tenant?.schema_name || '');

          // Make a direct API call with our guaranteed token
          const motherUrl = `${API_BASE_URL}/api/tenant/${encodeURIComponent(formattedSchema)}/citizens/${formData.mother_linked_citizen}/`;
          console.log('Fetching mother from URL:', motherUrl);

          const motherResponse = await fetch(motherUrl, {
            method: 'GET',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${motherToken}`,
              'X-Schema-Name': formattedSchema
            },
            credentials: 'include'
          });

          if (motherResponse.ok) {
            motherCitizen = await motherResponse.json();
            console.log('Fetched mother citizen data:', motherCitizen);
          } else {
            console.error('Failed to fetch mother citizen data:', motherResponse.status, motherResponse.statusText);
            // Try to find in the existing citizens array as fallback
            motherCitizen = citizens.find(c => c.id === parseInt(formData.mother_linked_citizen));
          }
        } else {
          // Try to find in the existing citizens array
          motherCitizen = formData.mother_linked_citizen ?
            citizens.find(c => c.id === parseInt(formData.mother_linked_citizen)) : null;
        }

        const motherData = {
          relationship_type: 'Mother',
          first_name: formData.mother_is_resident && motherCitizen ? motherCitizen.first_name : (formData.mother_first_name || ''),
          middle_name: formData.mother_is_resident && motherCitizen ? motherCitizen.middle_name || '' : (formData.mother_middle_name || ''),
          last_name: formData.mother_is_resident && motherCitizen ? motherCitizen.last_name : (formData.mother_last_name || ''),
          is_resident: formData.mother_is_resident || false,
          nationality: 1, // Assuming Ethiopia has ID 1
          phone: formData.mother_is_resident && motherCitizen ? motherCitizen.phone || '' : '',
          email: formData.mother_is_resident && motherCitizen ? motherCitizen.email || '' : '',
          linked_citizen: formData.mother_linked_citizen || null,
          citizen: citizenId,  // Add the citizen ID
          center: tenant?.id  // Use the tenant ID for the center
        };

        console.log('Mother data being sent:', motherData);
        console.log('Mother linked_citizen ID:', formData.mother_linked_citizen,
          'converted to:', formData.mother_linked_citizen ? parseInt(formData.mother_linked_citizen) : null);

        // Use the tenant-specific family endpoint
        // Get the formatted schema name and API base URL
        const { formatSchemaForUrl } = await import('../utils/schemaUtils');
        const { API_BASE_URL } = await import('../config/apiConfig');
        const formattedSchema = formatSchemaForUrl(tenant?.schema_name || '');

        // Make sure citizenId is defined
        if (!citizenId) {
          console.error('Citizen ID is undefined, cannot add mother information');
          return;
        }

        const parentUrl = `${API_BASE_URL}/api/tenant/${encodeURIComponent(formattedSchema)}/citizens/${citizenId}/parents/`;
        console.log('Parent API URL:', parentUrl);
        console.log('Mother data:', motherData);

        // Get a guaranteed valid token for the API call
        const parentToken = await getGuaranteedValidToken();
        if (!parentToken) {
          console.error('Could not get a valid token for parent API call');
          throw new Error('Authentication failed. Please log in again.');
        }

        console.log('Using guaranteed valid token for parent API call');

        // Make a direct API call with our guaranteed token
        const motherResponse = await fetch(parentUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${parentToken}`,
            'X-Schema-Name': formattedSchema
          },
          body: JSON.stringify(motherData),
          credentials: 'include'
        });

        if (!motherResponse.ok) {
          // Handle 401 Unauthorized errors first
          if (motherResponse.status === 401) {
            console.error('Authentication failed for mother API call (401 Unauthorized)');
            console.warn('Continuing with registration process despite mother API authentication failure');
            // Don't throw an error, just log it and continue
          } else {
            // For other errors, try to get more details
            let errorText = '';
            try {
              errorText = await motherResponse.text();
              try {
                const errorJson = JSON.parse(errorText);
                console.error('Failed to add mother information:', errorJson);
                console.error('Mother data sent:', motherData);
                console.error('Response status:', motherResponse.status);
                console.error('Response status text:', motherResponse.statusText);
                console.error('Error details:', errorJson);
              } catch (e) {
                console.error('Failed to add mother information:', errorText);
                console.error('Mother data sent:', motherData);
                console.error('Response status:', motherResponse.status);
                console.error('Response status text:', motherResponse.statusText);
              }
            } catch (e) {
              console.error('Could not read mother response text');
            }
          }
        } else {
          const responseData = await motherResponse.json();
          console.log('Mother information added successfully:', responseData);
        }
      } catch (motherError) {
        console.error('Error adding mother information:', motherError);
      }
    }

    // Add father information if applicable
    if (formData.father_first_name || formData.father_linked_citizen) {
      try {
        console.log('Adding father information...');

        // If father is a resident, fetch the complete citizen data
        let fatherCitizen = null;
        if (formData.father_is_resident && formData.father_linked_citizen) {
          console.log('Fetching complete father citizen data...');

          // Get a guaranteed valid token for the API call
          const fatherToken = await getGuaranteedValidToken();
          if (!fatherToken) {
            console.error('Could not get a valid token for father citizen fetch');
            throw new Error('Authentication failed. Please log in again.');
          }

          // Get the formatted schema name and API base URL
          const { formatSchemaForUrl } = await import('../utils/schemaUtils');
          const { API_BASE_URL } = await import('../config/apiConfig');
          const formattedSchema = formatSchemaForUrl(tenant?.schema_name || '');

          // Make a direct API call with our guaranteed token
          const fatherUrl = `${API_BASE_URL}/api/tenant/${encodeURIComponent(formattedSchema)}/citizens/${formData.father_linked_citizen}/`;
          console.log('Fetching father from URL:', fatherUrl);

          const fatherResponse = await fetch(fatherUrl, {
            method: 'GET',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${fatherToken}`,
              'X-Schema-Name': formattedSchema
            },
            credentials: 'include'
          });

          if (fatherResponse.ok) {
            fatherCitizen = await fatherResponse.json();
            console.log('Fetched father citizen data:', fatherCitizen);
          } else {
            console.error('Failed to fetch father citizen data:', fatherResponse.status, fatherResponse.statusText);
            // Try to find in the existing citizens array as fallback
            fatherCitizen = citizens.find(c => c.id === parseInt(formData.father_linked_citizen));
          }
        } else {
          // Try to find in the existing citizens array
          fatherCitizen = formData.father_linked_citizen ?
            citizens.find(c => c.id === parseInt(formData.father_linked_citizen)) : null;
        }

        const fatherData = {
          relationship_type: 'Father',
          first_name: formData.father_is_resident && fatherCitizen ? fatherCitizen.first_name : (formData.father_first_name || ''),
          middle_name: formData.father_is_resident && fatherCitizen ? fatherCitizen.middle_name || '' : (formData.father_middle_name || ''),
          last_name: formData.father_is_resident && fatherCitizen ? fatherCitizen.last_name : (formData.father_last_name || ''),
          is_resident: formData.father_is_resident || false,
          nationality: 1, // Assuming Ethiopia has ID 1
          phone: formData.father_is_resident && fatherCitizen ? fatherCitizen.phone || '' : '',
          email: formData.father_is_resident && fatherCitizen ? fatherCitizen.email || '' : '',
          linked_citizen: formData.father_linked_citizen || null,
          citizen: citizenId,  // Add the citizen ID
          center: tenant?.id  // Use the tenant ID for the center
        };

        console.log('Father data being sent:', fatherData);
        console.log('Father linked_citizen ID:', formData.father_linked_citizen,
          'converted to:', formData.father_linked_citizen ? parseInt(formData.father_linked_citizen) : null);

        // Use the tenant-specific family endpoint
        // Get the formatted schema name and API base URL
        const { formatSchemaForUrl } = await import('../utils/schemaUtils');
        const { API_BASE_URL } = await import('../config/apiConfig');
        const formattedSchema = formatSchemaForUrl(tenant?.schema_name || '');

        // Make sure citizenId is defined
        if (!citizenId) {
          console.error('Citizen ID is undefined, cannot add father information');
          return;
        }

        const parentUrl = `${API_BASE_URL}/api/tenant/${encodeURIComponent(formattedSchema)}/citizens/${citizenId}/parents/`;
        console.log('Parent API URL:', parentUrl);
        console.log('Father data:', fatherData);

        // Get a guaranteed valid token for the API call
        const parentToken = await getGuaranteedValidToken();
        if (!parentToken) {
          console.error('Could not get a valid token for parent API call');
          throw new Error('Authentication failed. Please log in again.');
        }

        console.log('Using guaranteed valid token for parent API call');

        // Make a direct API call with our guaranteed token
        const fatherResponse = await fetch(parentUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${parentToken}`,
            'X-Schema-Name': formattedSchema
          },
          body: JSON.stringify(fatherData),
          credentials: 'include'
        });

        if (!fatherResponse.ok) {
          // Handle 401 Unauthorized errors first
          if (fatherResponse.status === 401) {
            console.error('Authentication failed for father API call (401 Unauthorized)');
            console.warn('Continuing with registration process despite father API authentication failure');
            // Don't throw an error, just log it and continue
          } else {
            // For other errors, try to get more details
            let errorText = '';
            try {
              errorText = await fatherResponse.text();
              try {
                const errorJson = JSON.parse(errorText);
                console.error('Failed to add father information:', errorJson);
                console.error('Father data sent:', fatherData);
                console.error('Response status:', fatherResponse.status);
                console.error('Response status text:', fatherResponse.statusText);
                console.error('Error details:', errorJson);
              } catch (e) {
                console.error('Failed to add father information:', errorText);
                console.error('Father data sent:', fatherData);
                console.error('Response status:', fatherResponse.status);
                console.error('Response status text:', fatherResponse.statusText);
              }
            } catch (e) {
              console.error('Could not read father response text');
            }
          }
        } else {
          const responseData = await fatherResponse.json();
          console.log('Father information added successfully:', responseData);
        }
      } catch (fatherError) {
        console.error('Error adding father information:', fatherError);
      }
    }

    // Add emergency contact information if applicable
    if (formData.emergency_contact_first_name || formData.emergency_contact_linked_citizen) {
      try {
        console.log('Adding emergency contact information...');

        // If emergency contact is a resident, fetch the complete citizen data
        let emergencyContactCitizen = null;
        if (formData.emergency_contact_is_resident && formData.emergency_contact_linked_citizen) {
          console.log('Fetching complete emergency contact citizen data...');

          // Get a guaranteed valid token for the API call
          const emergencyToken = await getGuaranteedValidToken();
          if (!emergencyToken) {
            console.error('Could not get a valid token for emergency contact citizen fetch');
            throw new Error('Authentication failed. Please log in again.');
          }

          // Get the formatted schema name and API base URL
          const { formatSchemaForUrl } = await import('../utils/schemaUtils');
          const { API_BASE_URL } = await import('../config/apiConfig');
          const formattedSchema = formatSchemaForUrl(tenant?.schema_name || '');

          // Make a direct API call with our guaranteed token
          const emergencyUrl = `${API_BASE_URL}/api/tenant/${encodeURIComponent(formattedSchema)}/citizens/${formData.emergency_contact_linked_citizen}/`;
          console.log('Fetching emergency contact from URL:', emergencyUrl);

          const emergencyResponse = await fetch(emergencyUrl, {
            method: 'GET',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${emergencyToken}`,
              'X-Schema-Name': formattedSchema
            },
            credentials: 'include'
          });

          if (emergencyResponse.ok) {
            emergencyContactCitizen = await emergencyResponse.json();
            console.log('Fetched emergency contact citizen data:', emergencyContactCitizen);
          } else {
            console.error('Failed to fetch emergency contact citizen data:', emergencyResponse.status, emergencyResponse.statusText);
            // Try to find in the existing citizens array as fallback
            emergencyContactCitizen = citizens.find(c => c.id === parseInt(formData.emergency_contact_linked_citizen));
          }
        } else {
          // Try to find in the existing citizens array
          emergencyContactCitizen = formData.emergency_contact_linked_citizen ?
            citizens.find(c => c.id === parseInt(formData.emergency_contact_linked_citizen)) : null;
        }

        const emergencyContactData = {
          relationship: formData.emergency_contact_relationship || 'Other',
          nationality: 1, // Assuming Ethiopia has ID 1
          primary_contact: true,
          first_name: formData.emergency_contact_is_resident && emergencyContactCitizen ? emergencyContactCitizen.first_name : (formData.emergency_contact_first_name || ''),
          middle_name: formData.emergency_contact_is_resident && emergencyContactCitizen ? emergencyContactCitizen.middle_name || '' : (formData.emergency_contact_middle_name || ''),
          last_name: formData.emergency_contact_is_resident && emergencyContactCitizen ? emergencyContactCitizen.last_name : (formData.emergency_contact_last_name || ''),
          phone: formData.emergency_contact_is_resident && emergencyContactCitizen ? emergencyContactCitizen.phone || '' : (formData.emergency_contact_phone || ''),
          email: formData.emergency_contact_is_resident && emergencyContactCitizen ? emergencyContactCitizen.email || '' : (formData.emergency_contact_email || ''),
          is_resident: formData.emergency_contact_is_resident || false,
          linked_citizen: formData.emergency_contact_linked_citizen || null,
          citizen: citizenId,  // Add the citizen ID
          center: tenant?.id  // Use the tenant ID for the center
        };

        console.log('Emergency contact data being sent:', emergencyContactData);
        console.log('Emergency contact linked_citizen ID:', formData.emergency_contact_linked_citizen,
          'converted to:', formData.emergency_contact_linked_citizen ? parseInt(formData.emergency_contact_linked_citizen) : null);

        // Use the tenant-specific family endpoint
        // Get the formatted schema name and API base URL
        const { formatSchemaForUrl } = await import('../utils/schemaUtils');
        const { API_BASE_URL } = await import('../config/apiConfig');
        const formattedSchema = formatSchemaForUrl(tenant?.schema_name || '');

        // Make sure citizenId is defined
        if (!citizenId) {
          console.error('Citizen ID is undefined, cannot add emergency contact information');
          return;
        }

        const emergencyContactUrl = `${API_BASE_URL}/api/tenant/${encodeURIComponent(formattedSchema)}/citizens/${citizenId}/emergency-contacts/`;
        console.log('Emergency Contact API URL:', emergencyContactUrl);
        console.log('Emergency Contact data:', emergencyContactData);

        // Get a guaranteed valid token for the API call
        const emergencyApiToken = await getGuaranteedValidToken();
        if (!emergencyApiToken) {
          console.error('Could not get a valid token for emergency contact API call');
          throw new Error('Authentication failed. Please log in again.');
        }

        console.log('Using guaranteed valid token for emergency contact API call');

        // Make a direct API call with our guaranteed token
        const emergencyContactResponse = await fetch(emergencyContactUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${emergencyApiToken}`,
            'X-Schema-Name': formattedSchema
          },
          body: JSON.stringify(emergencyContactData),
          credentials: 'include'
        });

        if (!emergencyContactResponse.ok) {
          // Handle 401 Unauthorized errors first
          if (emergencyContactResponse.status === 401) {
            console.error('Authentication failed for emergency contact API call (401 Unauthorized)');
            console.warn('Continuing with registration process despite emergency contact API authentication failure');
            // Don't throw an error, just log it and continue
          } else {
            // For other errors, try to get more details
            let errorText = '';
            try {
              errorText = await emergencyContactResponse.text();
              try {
                const errorJson = JSON.parse(errorText);
                console.error('Failed to add emergency contact information:', errorJson);
                console.error('Emergency contact data sent:', emergencyContactData);
                console.error('Response status:', emergencyContactResponse.status);
                console.error('Response status text:', emergencyContactResponse.statusText);
                console.error('Error details:', errorJson);
              } catch (e) {
                console.error('Failed to add emergency contact information:', errorText);
                console.error('Emergency contact data sent:', emergencyContactData);
                console.error('Response status:', emergencyContactResponse.status);
                console.error('Response status text:', emergencyContactResponse.statusText);
              }
            } catch (e) {
              console.error('Could not read emergency contact response text');
            }
          }
        } else {
          const responseData = await emergencyContactResponse.json();
          console.log('Emergency contact information added successfully:', responseData);
        }
      } catch (emergencyContactError) {
        console.error('Error adding emergency contact information:', emergencyContactError);
      }
    }

    // Add children information if applicable
    if (formData.has_children && formData.children && formData.children.length > 0) {
      try {
        console.log('Adding children information...');

        for (const child of formData.children) {
          // Format the date to YYYY-MM-DD string format
          const formattedDate = child.date_of_birth instanceof Date
            ? child.date_of_birth.toISOString().split('T')[0]
            : typeof child.date_of_birth === 'string'
              ? child.date_of_birth
              : '';

          const childData = {
            first_name: child.first_name,
            middle_name: child.middle_name || '',
            last_name: child.last_name,
            gender: child.gender,
            date_of_birth: formattedDate,
            is_resident: false,
            nationality: 1, // Assuming Ethiopia has ID 1
            citizen: citizenId,  // Add the citizen ID
            center: tenant?.id  // Use the tenant ID for the center
          };

          // Use the tenant-specific family endpoint
          // Get the formatted schema name and API base URL
          const { formatSchemaForUrl } = await import('../utils/schemaUtils');
          const { API_BASE_URL } = await import('../config/apiConfig');
          const formattedSchema = formatSchemaForUrl(tenant?.schema_name || '');

          // Make sure citizenId is defined
          if (!citizenId) {
            console.error('Citizen ID is undefined, cannot add child information');
            continue;
          }

          const childUrl = `${API_BASE_URL}/api/tenant/${encodeURIComponent(formattedSchema)}/citizens/${citizenId}/children/`;
          console.log('Child API URL:', childUrl);
          console.log('Child data:', childData);

          // Get a guaranteed valid token for the API call
          const childApiToken = await getGuaranteedValidToken();
          if (!childApiToken) {
            console.error('Could not get a valid token for child API call');
            console.warn('Continuing with next child despite token failure');
            continue;
          }

          console.log('Using guaranteed valid token for child API call');

          // Make a direct API call with our guaranteed token
          const childResponse = await fetch(childUrl, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${childApiToken}`,
              'X-Schema-Name': formattedSchema
            },
            body: JSON.stringify(childData),
            credentials: 'include'
          });

          if (!childResponse.ok) {
            // Handle 401 Unauthorized errors first
            if (childResponse.status === 401) {
              console.error('Authentication failed for child API call (401 Unauthorized)');
              console.warn('Continuing with registration process despite child API authentication failure');
              // Don't throw an error, just log it and continue
            } else {
              // For other errors, try to get more details
              let errorText = '';
              try {
                errorText = await childResponse.text();
                try {
                  const errorJson = JSON.parse(errorText);
                  console.error('Failed to add child information:', errorJson);
                  // Log detailed error information
                  console.error('Child data sent:', childData);
                  console.error('Response status:', childResponse.status);
                  console.error('Response status text:', childResponse.statusText);
                  console.error('Error details:', errorJson);
                } catch (e) {
                  console.error('Failed to add child information:', errorText);
                  console.error('Child data sent:', childData);
                  console.error('Response status:', childResponse.status);
                  console.error('Response status text:', childResponse.statusText);
                }
              } catch (e) {
                console.error('Could not read child response text');
              }
            }
          } else {
            const responseData = await childResponse.json();
            console.log('Child information added successfully:', responseData);
          }
        }
      } catch (childError) {
        console.error('Error adding child information:', childError);
      }
    }
  };

  // Function to get a guaranteed valid token for API calls
  const getGuaranteedValidToken = async (): Promise<string | null> => {
    try {
      // Get the current schema
      const currentSchema = localStorage.getItem('schema_name') || tenant?.schema_name || '';

      // First try to get schema-specific token from localStorage
      if (currentSchema) {
        const schemaToken = localStorage.getItem(`jwt_access_token_${currentSchema}`);
        if (schemaToken) {
          console.log(`Found schema-specific JWT token for ${currentSchema}`);
          // Store it with the standard name for consistency
          localStorage.setItem('jwt_access_token', schemaToken);
          return schemaToken;
        }
      }

      // Try to get token from localStorage with consistent naming
      const jwtToken = localStorage.getItem('jwt_access_token');
      if (jwtToken) {
        console.log('Found JWT token in localStorage:', jwtToken.substring(0, 10) + '...');

        // Store it for the current schema if we have one
        if (currentSchema) {
          localStorage.setItem(`jwt_access_token_${currentSchema}`, jwtToken);
        }

        return jwtToken;
      }

      // Try alternative JWT token storage locations
      const altJwtToken = localStorage.getItem('jwt_token');
      if (altJwtToken) {
        console.log('Found JWT token in alternative localStorage location');
        // Store it with the standard name for consistency
        localStorage.setItem('jwt_access_token', altJwtToken);

        // Store it for the current schema if we have one
        if (currentSchema) {
          localStorage.setItem(`jwt_access_token_${currentSchema}`, altJwtToken);
        }

        return altJwtToken;
      }

      // If no token in localStorage, try to get one from the citizen auth service
      const { getCitizenApiToken } = await import('../services/citizenAuthService');
      const token = await getCitizenApiToken();

      if (token) {
        console.log('Got valid token from citizenAuthService:', token.substring(0, 10) + '...');

        // Store the token in localStorage with consistent naming
        localStorage.setItem('jwt_token', token);
        localStorage.setItem('jwt_access_token', token);

        // Also store it for the current schema
        if (currentSchema) {
          localStorage.setItem(`jwt_token_${currentSchema}`, token);
          localStorage.setItem(`jwt_access_token_${currentSchema}`, token);
        }

        return token;
      }

      // If we still don't have a token, try to log in again
      console.error('Failed to get valid token from citizenAuthService');

      // Redirect to login page
      const { loginRedirect } = await import('../utils/authUtils');
      loginRedirect();

      return null;
    } catch (error) {
      console.error('Error getting guaranteed valid token:', error);
      return null;
    }
  };

  // Function to ensure we have a valid token before submitting
  const ensureValidToken = async (): Promise<boolean> => {
    try {
      // Get the current schema
      const schema = localStorage.getItem('schema_name') || tenant?.schema_name || '';
      if (!schema) {
        console.error('No schema found for token validation');
        return false;
      }

      console.log('Ensuring valid token for schema:', schema);

      // First try to get a token using our guaranteed valid token function
      const token = await getGuaranteedValidToken();

      if (token) {
        console.log('Got token from getGuaranteedValidToken:', token.substring(0, 10) + '...');

        // Import token service for additional validation
        const { getAccessTokenForSchema, refreshJWTTokens } = await import('../services/tokenService');

        // Store the token in the token service
        localStorage.setItem('jwt_access_token', token);
        localStorage.setItem(`jwt_access_token_${schema}`, token);

        // Get the token from the token service to ensure it's properly stored
        let accessToken = getAccessTokenForSchema(schema);

        // Validate the token
        const isValidToken = accessToken &&
                            accessToken.trim() !== '' &&
                            accessToken !== 'null' &&
                            accessToken !== 'undefined';

        if (isValidToken) {
          console.log('Current access token:', accessToken.substring(0, 10) + '...');
        } else {
          console.log('Current access token: Missing or invalid');
          accessToken = token; // Use the token we got from getGuaranteedValidToken
        }

      // If no valid token, try to get one from localStorage
      if (!accessToken) {
        console.log('Checking localStorage for valid tokens');

        // Try each token source and validate it's not empty
        const possibleTokens = [
          localStorage.getItem('jwt_token'),
          localStorage.getItem(`jwt_token_${schema}`),
          localStorage.getItem('jwt_access_token'),
          localStorage.getItem(`jwt_access_token_${schema}`)
        ];

        // Find the first non-empty token
        for (const token of possibleTokens) {
          if (token && token.trim() !== '' && token !== 'null' && token !== 'undefined') {
            accessToken = token;
            console.log('Found valid token in localStorage:', token.substring(0, 10) + '...');
            break;
          }
        }
      }

      // If still no valid token, try to refresh
      if (!accessToken) {
        console.log('No valid access token found, attempting to refresh');
        try {
          const refreshResult = await refreshJWTTokens(schema, true);
          if (refreshResult && refreshResult.access_token &&
              refreshResult.access_token.trim() !== '' &&
              refreshResult.access_token !== 'null' &&
              refreshResult.access_token !== 'undefined') {
            console.log('Token refresh successful:', refreshResult.access_token.substring(0, 10) + '...');

            // Store the token in multiple locations for redundancy
            localStorage.setItem('jwt_token', refreshResult.access_token);
            localStorage.setItem(`jwt_token_${schema}`, refreshResult.access_token);
            localStorage.setItem('jwt_access_token', refreshResult.access_token);
            localStorage.setItem(`jwt_access_token_${schema}`, refreshResult.access_token);

            return true;
          } else {
            console.error('Token refresh failed or returned invalid token');
            return false;
          }
        } catch (refreshError) {
          console.error('Error refreshing token:', refreshError);
          return false;
        }
      }

      // If we have a token, verify it works by making a test request
      if (accessToken) {
        try {
          console.log('Verifying token with a test request');

          // Import API config
          const { API_BASE_URL } = await import('../config/apiConfig');

          // Make a simple GET request to verify the token
          // Use a dedicated token validation endpoint if available
          const testUrl = `${API_BASE_URL}/api/jwt/validate-token/`;

          const testResponse = await fetch(testUrl, {
            method: 'GET',
            headers: {
              'Authorization': `Bearer ${accessToken}`,
              'X-Schema-Name': schema
            },
            credentials: 'include'
          });

          if (testResponse.ok) {
            try {
              // Parse the response to check if the token is valid
              const validationData = await testResponse.json();

              if (validationData.valid) {
                console.log('Token validation successful');
                return true;
              } else {
                console.error('Token validation failed:', validationData.error);
              }
            } catch (parseError) {
              console.log('Token verification successful (response OK)');
              return true;
            }
          } else {
            console.error('Token verification failed with status:', testResponse.status);

            // If unauthorized, try one more refresh
            if (testResponse.status === 401) {
              console.log('Unauthorized in verification, trying one final refresh');
              try {
                const finalRefreshResult = await refreshJWTTokens(schema, true);
                if (finalRefreshResult && finalRefreshResult.access_token &&
                    finalRefreshResult.access_token.trim() !== '' &&
                    finalRefreshResult.access_token !== 'null' &&
                    finalRefreshResult.access_token !== 'undefined') {
                  console.log('Final token refresh successful');

                  // Store the token in multiple locations for consistency
                  localStorage.setItem('jwt_token', finalRefreshResult.access_token);
                  localStorage.setItem(`jwt_token_${schema}`, finalRefreshResult.access_token);
                  localStorage.setItem('jwt_access_token', finalRefreshResult.access_token);
                  localStorage.setItem(`jwt_access_token_${schema}`, finalRefreshResult.access_token);

                  return true;
                }
              } catch (finalRefreshError) {
                console.error('Final token refresh failed:', finalRefreshError);
              }
            }

            // If validation endpoint fails, try a fallback to a simple API endpoint
            console.log('Trying fallback validation with citizens endpoint');
            try {
              const fallbackUrl = `${API_BASE_URL}/api/tenant/${schema}/citizens/?limit=1`;

              const fallbackResponse = await fetch(fallbackUrl, {
                method: 'GET',
                headers: {
                  'Authorization': `Bearer ${accessToken}`,
                  'X-Schema-Name': schema
                },
                credentials: 'include'
              });

              if (fallbackResponse.ok) {
                console.log('Fallback token verification successful');
                return true;
              } else {
                console.error('Fallback token verification failed with status:', fallbackResponse.status);
                return false;
              }
            } catch (fallbackError) {
              console.error('Error in fallback verification:', fallbackError);
              return false;
            }
          }
        } catch (verifyError) {
          console.error('Error verifying token:', verifyError);
          return false;
        }
      }

      return true;
    } catch (error) {
      console.error('Error ensuring valid token:', error);
      return false;
    }
  };

  // Handle form submission
  const handleSubmit = async (e?: React.FormEvent) => {
    if (e) e.preventDefault();

    // Log all linked citizen IDs for debugging
    console.log('SUBMISSION: Linked Citizen IDs');
    console.log('Mother linked citizen:', formData.mother_linked_citizen,
      'type:', typeof formData.mother_linked_citizen);
    console.log('Father linked citizen:', formData.father_linked_citizen,
      'type:', typeof formData.father_linked_citizen);
    console.log('Spouse linked citizen:', formData.spouse_linked_citizen,
      'type:', typeof formData.spouse_linked_citizen);
    console.log('Emergency contact linked citizen:', formData.emergency_contact_linked_citizen,
      'type:', typeof formData.emergency_contact_linked_citizen);

    // Ensure location values are set correctly before submission for kebele tenants
    if (tenant?.type === 'KEBELE') {
      // Get subcity from parent tenant
      const parentSubcityId = tenant?.parent_id || determineParentSubcity();
      const parentSubcityName = tenant?.parent_name || 'Parent Subcity';

      // Get kebele from current tenant
      const kebeleId = tenant.id || '';
      const kebeleName = tenant.name || 'Current Kebele';

      if (parentSubcityId) {
        console.log('SUBMISSION: Setting location values from tenant hierarchy');
        console.log('  subcity (from parent tenant):', parentSubcityId, `(${parentSubcityName})`);
        console.log('  kebele (from current tenant):', kebeleId, `(${kebeleName})`);

        // Update form data directly
        formData.subcity = parentSubcityId;
        formData.kebele = kebeleId;
      } else {
        console.warn('WARNING: Could not determine parent subcity for submission');

        // Try one last time to fetch tenant information
        fetchTenantFromAPI().then(() => {
          // This will update the tenant state and form data
          console.log('Attempted to fetch tenant information one last time');
        });

        // Continue with submission using whatever values we have
        console.log('Continuing with submission using current values:');
        console.log('  subcity:', formData.subcity);
        console.log('  kebele:', formData.kebele);
      }
    }

    // Validate form
    const errors = [];

    // Basic Information validation
    if (!formData.first_name) errors.push('First name is required');
    if (!formData.last_name) errors.push('Last name is required');
    if (!formData.gender) errors.push('Gender is required');
    if (!formData.date_of_birth) errors.push('Date of birth is required');

    // Validate age (must be at least 18 years old)
    const today = new Date();
    const birthDate = new Date(formData.date_of_birth);
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }
    if (age < 18) errors.push('Citizen must be at least 18 years old');

    // Contact Information validation
    if (!formData.address) errors.push('Address is required');

    // Display all validation errors
    if (errors.length > 0) {
      setSubmitError(`Please correct the following errors:\n${errors.join('\n')}`);
      return;
    }

    setIsSubmitting(true);
    setSubmitError('');

    // Ensure we have a valid token before submitting
    const hasValidToken = await ensureValidToken();
    if (!hasValidToken) {
      setSubmitError('Authentication failed. Please try refreshing the page or log in again.');
      setIsSubmitting(false);
      return;
    }

    try {
      // Prepare the data for API call
      const formDataToSend = new FormData();

      // Basic Information
      // Generate a registration number if not provided
      const registrationNumber = formData.registration_number || generateRegistrationNumber();
      formDataToSend.append('registration_number', registrationNumber);

      // The backend expects a UUID field, but we should not send it
      // The backend will generate it automatically in the save method
      console.log('Not sending UUID - backend will generate it automatically');

      formDataToSend.append('first_name', formData.first_name);
      formDataToSend.append('middle_name', formData.middle_name || '');
      formDataToSend.append('last_name', formData.last_name);
      formDataToSend.append('first_name_am', formData.first_name_am || '');
      formDataToSend.append('middle_name_am', formData.middle_name_am || '');
      formDataToSend.append('last_name_am', formData.last_name_am || '');
      formDataToSend.append('date_of_birth', format(formData.date_of_birth, 'yyyy-MM-dd'));
      formDataToSend.append('gender', formData.gender);

      // Add religion and citizen status
      // Make sure religion is a valid ID
      if (formData.religion) {
        console.log('Using religion:', formData.religion);
        formDataToSend.append('religion', formData.religion);
      } else {
        // Default to 1 (Orthodox) if not provided
        console.log('No religion provided, defaulting to 1 (Orthodox)');
        formDataToSend.append('religion', '1');
      }

      // Make sure citizen_status is a valid ID
      if (formData.citizen_status) {
        console.log('Using citizen_status:', formData.citizen_status);
        formDataToSend.append('citizen_status', formData.citizen_status);
      } else {
        // Default to 1 (Active) if not provided
        console.log('No citizen_status provided, defaulting to 1 (Active)');
        formDataToSend.append('citizen_status', '1');
      }

      // Contact Information
      // Address is required by the backend model
      formDataToSend.append('address', formData.address || 'Default Address');
      if (formData.phone) formDataToSend.append('phone', formData.phone);
      if (formData.email) formDataToSend.append('email', formData.email);
      if (formData.house_number) formDataToSend.append('house_number', formData.house_number);

      // Don't send subcity, kebele, and ketena fields - backend will handle these
      console.log('NOT sending subcity, kebele, and ketena - backend will handle these');
      console.log('Form data subcity:', formData.subcity);
      console.log('Form data kebele:', formData.kebele);
      console.log('Form data ketena:', formData.ketena);

      // Log the tenant information for debugging
      console.log('Tenant information:', tenant);

      // Don't send ketena - backend will handle it
      console.log('Not sending ketena - backend will handle it');

      // Nationality Information
      formDataToSend.append('nationality', formData.nationality || 'Ethiopian');

      // Make sure nationality_country is a valid ID (default to 1 for Ethiopia)
      if (formData.nationality_country) {
        formDataToSend.append('nationality_country', formData.nationality_country);
      } else {
        console.log('No nationality_country provided, defaulting to 1 (Ethiopia)');
        formDataToSend.append('nationality_country', '1');
      }

      // Don't send region, subcity, or kebele - backend will handle these
      console.log('Not sending region, subcity, or kebele - backend will handle these');

      // Explicitly remove subcity, kebele, and center from formData to avoid validation errors
      if (formDataToSend.has('subcity')) {
        formDataToSend.delete('subcity');
        console.log('Removed subcity from form data');
      }
      if (formDataToSend.has('kebele')) {
        formDataToSend.delete('kebele');
        console.log('Removed kebele from form data');
      }
      if (formDataToSend.has('center')) {
        formDataToSend.delete('center');
        console.log('Removed center from form data');
      }

      // Employment Information
      formDataToSend.append('employment', formData.employment.toString());
      if (formData.employment) {
        // Make sure employee_type is a valid ID
        if (formData.employee_type) {
          console.log('Using employee_type:', formData.employee_type);
          formDataToSend.append('employee_type', formData.employee_type);
        }

        // Make sure organization_name is a string
        if (formData.organization_name) {
          console.log('Using organization_name:', formData.organization_name);
          formDataToSend.append('organization_name', formData.organization_name.toString());
        }

        // Make sure occupation is a string
        if (formData.occupation) {
          console.log('Using occupation:', formData.occupation);
          formDataToSend.append('occupation', formData.occupation.toString());
        }

        // Make sure employment_type is a valid ID
        if (formData.employment_type) {
          console.log('Using employment_type:', formData.employment_type);
          formDataToSend.append('employment_type', formData.employment_type);
        }
      }

      // Family Information - marital_status is a foreign key to MaritalStatus model
      if (formData.marital_status) {
        console.log('Sending marital_status:', formData.marital_status);
        // Make sure we're sending the ID as a number
        formDataToSend.append('marital_status', formData.marital_status);
      } else {
        // Default to 1 (Single) if not provided
        console.log('No marital_status provided, defaulting to 1 (Single)');
        formDataToSend.append('marital_status', '1');
      }

      // Add family-related data directly to the form data
      // The public endpoint will handle creating these related records

      // Add mother information if provided
      if (formData.mother_name) {
        console.log('Adding mother information to form data');

        // Send the full name as mother_name
        formDataToSend.append('mother_name', formData.mother_name);
        console.log('Added mother_name:', formData.mother_name);

        // Also try the split approach for backward compatibility
        const motherNames = formData.mother_name.split(' ');
        if (motherNames.length >= 2) {
          formDataToSend.append('mother_first_name', motherNames[0]);
          if (motherNames.length >= 3) {
            formDataToSend.append('mother_middle_name', motherNames[1]);
            formDataToSend.append('mother_last_name', motherNames.slice(2).join(' '));
          } else {
            formDataToSend.append('mother_last_name', motherNames[1]);
          }
        } else {
          console.log('Mother name does not have enough parts, using as first name');
          formDataToSend.append('mother_first_name', formData.mother_name);
          formDataToSend.append('mother_last_name', formData.mother_name);
        }
      }

      // Add father information if provided
      if (formData.father_name) {
        console.log('Adding father information to form data');

        // Send the full name as father_name
        formDataToSend.append('father_name', formData.father_name);
        console.log('Added father_name:', formData.father_name);

        // Also try the split approach for backward compatibility
        const fatherNames = formData.father_name.split(' ');
        if (fatherNames.length >= 2) {
          formDataToSend.append('father_first_name', fatherNames[0]);
          if (fatherNames.length >= 3) {
            formDataToSend.append('father_middle_name', fatherNames[1]);
            formDataToSend.append('father_last_name', fatherNames.slice(2).join(' '));
          } else {
            formDataToSend.append('father_last_name', fatherNames[1]);
          }
        } else {
          console.log('Father name does not have enough parts, using as first name');
          formDataToSend.append('father_first_name', formData.father_name);
          formDataToSend.append('father_last_name', formData.father_name);
        }
      }

      // Add spouse information if provided and marital status is 'Married'
      if (formData.spouse_name && formData.marital_status === '2') { // Assuming 2 is the ID for 'Married'
        console.log('Adding spouse information to form data');

        // Send the full name as spouse_name
        formDataToSend.append('spouse_name', formData.spouse_name);
        console.log('Added spouse_name:', formData.spouse_name);

        // Also try the split approach for backward compatibility
        const spouseNames = formData.spouse_name.split(' ');
        if (spouseNames.length >= 2) {
          formDataToSend.append('spouse_first_name', spouseNames[0]);
          if (spouseNames.length >= 3) {
            formDataToSend.append('spouse_middle_name', spouseNames[1]);
            formDataToSend.append('spouse_last_name', spouseNames.slice(2).join(' '));
          } else {
            formDataToSend.append('spouse_last_name', spouseNames[1]);
          }
        } else {
          console.log('Spouse name does not have enough parts, using as first name');
          formDataToSend.append('spouse_first_name', formData.spouse_name);
          formDataToSend.append('spouse_last_name', formData.spouse_name);
        }
      }

      // Add emergency contact information if provided
      if (formData.emergency_contact_name) {
        console.log('Adding emergency contact information to form data');

        // Send the full name as emergency_contact_name
        formDataToSend.append('emergency_contact_name', formData.emergency_contact_name);
        console.log('Added emergency_contact_name:', formData.emergency_contact_name);

        // Also try the split approach for backward compatibility
        const emergencyContactNames = formData.emergency_contact_name.split(' ');
        if (emergencyContactNames.length >= 2) {
          formDataToSend.append('emergency_contact_first_name', emergencyContactNames[0]);
          if (emergencyContactNames.length >= 3) {
            formDataToSend.append('emergency_contact_middle_name', emergencyContactNames[1]);
            formDataToSend.append('emergency_contact_last_name', emergencyContactNames.slice(2).join(' '));
          } else {
            formDataToSend.append('emergency_contact_last_name', emergencyContactNames[1]);
          }
        } else {
          console.log('Emergency contact name does not have enough parts, using as first name');
          formDataToSend.append('emergency_contact_first_name', formData.emergency_contact_name);
          formDataToSend.append('emergency_contact_last_name', formData.emergency_contact_name);
        }

        // Add emergency contact phone if provided
        if (formData.emergency_contact_phone) {
          formDataToSend.append('emergency_contact_phone', formData.emergency_contact_phone);
        }

        // Add emergency contact relationship if provided
        formDataToSend.append('emergency_contact_relationship', 'OTHER'); // Default to OTHER
      }

      // Log what we're sending
      console.log('Sending family-related data directly with the citizen creation request');

      // Other Information
      formDataToSend.append('is_resident', formData.is_resident.toString());

      // Photo
      if (formData.photo) {
        console.log('Adding photo to form data');
        console.log('Photo type:', formData.photo.type);
        console.log('Photo size:', formData.photo.size, 'bytes');

        // Make sure we're sending the actual File object, not a string or URL
        if (formData.photo instanceof File) {
          formDataToSend.append('photo', formData.photo);
        } else if (typeof formData.photo === 'string' && formData.photo.startsWith('data:image')) {
          // Convert base64 to File object
          try {
            const byteString = atob(formData.photo.split(',')[1]);
            const mimeString = formData.photo.split(',')[0].split(':')[1].split(';')[0];
            const ab = new ArrayBuffer(byteString.length);
            const ia = new Uint8Array(ab);
            for (let i = 0; i < byteString.length; i++) {
              ia[i] = byteString.charCodeAt(i);
            }
            const blob = new Blob([ab], { type: mimeString });
            const file = new File([blob], 'photo.jpg', { type: mimeString });
            formDataToSend.append('photo', file);
            console.log('Converted base64 photo to File object');
          } catch (e) {
            console.error('Error converting base64 to File:', e);
          }
        } else {
          console.warn('Photo is not a File object or base64 string, cannot send');
        }
      } else {
        console.log('No photo to add to form data');
      }

      // When using the public endpoint, we don't need to send kebele, subcity, or ketena
      // The backend will handle these fields automatically
      console.log('Using public endpoint - kebele, subcity, and ketena will be handled by the backend');

      // We'll use fetchWithAuth which handles token validation automatically

      try {
        // Get the schema name
        const schemaName = localStorage.getItem('schema_name') || tenant?.schema_name || '';

        // Use the tenant-specific API endpoint
        let schema = schemaName || tenant?.schema_name || '';
        console.log('Using schema name:', schema);

        // Use the public endpoint to avoid authentication issues
        const { API_BASE_URL } = await import('../config/apiConfig');
        const { formatSchemaForUrl } = await import('../utils/schemaUtils');
        const formattedSchema = formatSchemaForUrl(schema);
        const url = `${API_BASE_URL}/api/tenant/${encodeURIComponent(formattedSchema)}/citizens/public/register/`;
        console.log('Using public endpoint URL for submission:', url);
        // Log the URL and schema name for debugging
        console.log('Submission URL:', url);
        console.log('Schema name:', schemaName || tenant?.schema_name || '');
        console.log('Form data being sent:', Object.fromEntries(formDataToSend.entries()));

        // Get the CSRF token for the main API call
        const csrftoken = document.cookie
          .split('; ')
          .find(row => row.startsWith('csrftoken='))
          ?.split('=')[1] || '';

        console.log('CSRF Token for main API call:', csrftoken);

        // Log the complete form data being sent
        console.log('Complete form data being sent:');
        for (const [key, value] of formDataToSend.entries()) {
          console.log(`${key}: ${value}`);
        }
        // We already have formattedSchema from above, so we don't need to redefine it
        console.log('Using formatted schema:', formattedSchema);

        // Import the resilient API client
        const { resilientApiClient } = await import('../services/resilientApiClient');

        // Create a custom fetch function that can handle FormData
        const customFetch = async (url: string, options: RequestInit) => {
          try {
            // We already have formattedSchema from above, so we can use it directly
            // This ensures we use the same schema name throughout the request
            if (!formattedSchema) {
              throw new Error('No schema name found for API call');
            }

            // Create headers
            const headers = new Headers(options.headers || {});
            headers.set('X-Schema-Name', formattedSchema);
            headers.set('X-CSRFToken', csrftoken);

            // Since we're using the public endpoint, we don't need authentication
            console.log('Using public endpoint - no authentication required');

            // Remove any existing Authorization header to ensure we're making an unauthenticated request
            headers.delete('Authorization');

            // Make the request
            const response = await fetch(url, {
              ...options,
              headers,
              credentials: 'include'
            });

            // Since we're using a public endpoint, we shouldn't get 401 errors
            // But if we do, log it and continue with the response
            if (response.status === 401) {
              console.log('Unexpected 401 Unauthorized response from public endpoint');
              console.log('This should not happen with the public endpoint - check server configuration');
            }

            return response;
          } catch (error) {
            console.error('Error making API request:', error);
            throw error;
          }
        };

        // Use our custom fetch function with FormData
        const response = await customFetch(url, {
          method: 'POST',
          body: formDataToSend
        });

        console.log('Response status:', response.status);
        console.log('Response headers:', response.headers);
        console.log('URL used for submission:', url);
        console.log('Schema name used:', schemaName || tenant?.schema_name || '');

        // Log the response status text for better error diagnosis
        console.log('Response status text:', response.statusText);

        // Clone the response for debugging purposes
        const responseClone = response.clone();
        try {
          const responseText = await responseClone.text();
          console.log('Raw response text (first 500 chars):', responseText.substring(0, 500));
        } catch (e) {
          console.error('Could not read response text for debugging:', e);
        }

        if (!response.ok) {
          // Handle 401 Unauthorized errors
          if (response.status === 401) {
            console.error('Unexpected 401 Unauthorized response from public endpoint');
            setIsSubmitting(false);
            setSubmitError('Unexpected authentication error. This should not happen with the public endpoint. Please try again or contact support.');
            return;
          }

          // If API call fails, try to get detailed error information
          try {
            // Clone the response before reading it
            const responseClone = response.clone();

            // Try to parse the response as JSON first
            try {
              const errorData = await response.json();
              console.log('Error data:', errorData);
              console.log('Error data type:', typeof errorData);
              console.log('Error data keys:', Object.keys(errorData));

              // Format validation errors if they exist
              if (typeof errorData === 'object' && Object.keys(errorData).length > 0) {
                const errorMessages = [];

                // Handle different error formats
                if (errorData.detail) {
                  errorMessages.push(errorData.detail);
                } else if (errorData.error) {
                  errorMessages.push(errorData.error);
                } else {
                  // Handle field-specific validation errors
                  for (const [field, errors] of Object.entries(errorData)) {
                    console.log(`Field: ${field}, Errors:`, errors);
                    if (Array.isArray(errors)) {
                      errorMessages.push(`${field}: ${errors.join(', ')}`);
                    } else if (typeof errors === 'object' && errors !== null) {
                      // Handle nested error objects
                      const nestedErrors = [];
                      for (const [nestedField, nestedError] of Object.entries(errors as Record<string, any>)) {
                        nestedErrors.push(`${nestedField}: ${nestedError}`);
                      }
                      errorMessages.push(`${field}: ${nestedErrors.join(', ')}`);
                    } else {
                      errorMessages.push(`${field}: ${errors}`);
                    }
                  }
                }

                // Log the full form data being sent for debugging
                console.log('Full form data being sent:');
                for (const [key, value] of formDataToSend.entries()) {
                  console.log(`${key}: ${value}`);
                }

                // Create a more user-friendly error message
                const errorMessage = `Failed to register citizen due to validation errors:\n${errorMessages.join('\n')}`;
                console.error(errorMessage);

                // Set the error directly instead of throwing
                setSubmitError(errorMessage);
                setIsSubmitting(false);
                return;
              } else {
                // Set the error directly instead of throwing
                setSubmitError('Failed to register citizen. Please check your form data and try again.');
                setIsSubmitting(false);
                return;
              }
            } catch (jsonError) {
              // If JSON parsing fails, try to get the response as text
              console.log('Error parsing JSON:', jsonError);
              const responseText = await responseClone.text();
              console.log('Response text:', responseText);

              // For 500 errors, extract the error message from the HTML response
              if (response.status === 500 && responseText.includes('AttributeError')) {
                // Try to extract the specific error message
                const errorMatch = responseText.match(/<pre class="exception_value">(.*?)<\/pre>/s);
                if (errorMatch && errorMatch[1]) {
                  const errorMessage = errorMatch[1].trim();
                  console.error('Extracted error message from 500 response:', errorMessage);
                  // Set the error directly instead of throwing
                  const formattedError = `Failed to register citizen. Server error: ${errorMessage}`;
                  console.error(formattedError);
                  setSubmitError(formattedError);
                  setIsSubmitting(false);
                  return;
                }
              }

              // Set the error directly instead of throwing
              const errorMessage = `Failed to register citizen. Server returned status ${response.status}${responseText ? ': ' + responseText.substring(0, 200) + (responseText.length > 200 ? '...' : '') : ''}`;
              console.error(errorMessage);
              setSubmitError(errorMessage);
              setIsSubmitting(false);
              return;
            }
          } catch (error) {
            console.error('Error handling response:', error);
            // Set the error directly instead of throwing
            setSubmitError('Failed to register citizen. An unexpected error occurred while processing the server response.');
            setIsSubmitting(false);
            return;
          }
        } else {
          // If API call succeeds, get the real response data
          try {
            let responseData;
            try {
              responseData = await response.json();
              console.log('API call succeeded:', responseData);
            } catch (jsonError) {
              console.error('Error parsing JSON response:', jsonError);
              setSubmitError('Failed to register citizen. The server returned an invalid response format.');
              setIsSubmitting(false);
              return;
            }

            // Check if the citizen was actually created successfully
            if (!responseData || typeof responseData !== 'object') {
              console.error('Invalid response data from API:', responseData);
              setSubmitError('Failed to register citizen. The server returned an invalid response.');
              setIsSubmitting(false);
              return;
            }

            // Now that the citizen is created, add related data
            let citizenId = responseData.id;
            console.log('Citizen created with ID:', citizenId);

            // If no ID was returned but the response was successful, try to get the ID by querying for the citizen
            if (!citizenId && response.ok) {
              console.log('No ID returned but response was successful. Attempting to retrieve the citizen ID...');

              try {
                // Get a guaranteed valid token for the API call
                const searchToken = await getGuaranteedValidToken();
                if (!searchToken) {
                  throw new Error('Could not get a valid token for citizen search');
                }

                // Get the formatted schema name and API base URL
                const { formatSchemaForUrl } = await import('../utils/schemaUtils');
                const { API_BASE_URL } = await import('../config/apiConfig');
                const formattedSchema = formatSchemaForUrl(tenant?.schema_name || '');

                // Search for the citizen by unique identifiers (national ID, first name, last name)
                const searchParams = new URLSearchParams();
                if (formData.national_id) {
                  searchParams.append('national_id', formData.national_id);
                }
                searchParams.append('first_name', formData.first_name);
                searchParams.append('last_name', formData.last_name);

                const searchUrl = `${API_BASE_URL}/api/tenant/${encodeURIComponent(formattedSchema)}/citizens/?${searchParams.toString()}`;
                console.log('Searching for citizen at URL:', searchUrl);

                const searchResponse = await fetch(searchUrl, {
                  method: 'GET',
                  headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${searchToken}`,
                    'X-Schema-Name': formattedSchema
                  },
                  credentials: 'include'
                });

                if (searchResponse.ok) {
                  const searchData = await searchResponse.json();
                  console.log('Search results:', searchData);

                  if (searchData.results && searchData.results.length > 0) {
                    // Find the most recently created citizen that matches our criteria
                    const matchingCitizens = searchData.results.filter(citizen =>
                      citizen.first_name === formData.first_name &&
                      citizen.last_name === formData.last_name &&
                      (!formData.national_id || citizen.national_id === formData.national_id)
                    );

                    if (matchingCitizens.length > 0) {
                      // Sort by created_at date (most recent first) and take the first one
                      matchingCitizens.sort((a, b) =>
                        new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
                      );

                      citizenId = matchingCitizens[0].id;
                      console.log('Found citizen ID through search:', citizenId);
                    }
                  }
                } else {
                  console.error('Failed to search for citizen:', searchResponse.status, searchResponse.statusText);
                }
              } catch (searchError) {
                console.error('Error searching for citizen:', searchError);
              }
            }

            if (!citizenId) {
              console.error('No citizen ID returned from API or found through search');
              setSubmitError('Failed to register citizen. No ID was returned from the server.');
              setIsSubmitting(false);
              return;
            }

            // Get the CSRF token
            const csrfToken = document.cookie
              .split('; ')
              .find(row => row.startsWith('csrftoken='))
              ?.split('=')[1];

            console.log('CSRF Token:', csrfToken);

            // Use the addRelatedData helper function to add related data
            try {
              await addRelatedData(citizenId, formData, tenant, csrfToken || '', customFetch);
            } catch (relatedDataError) {
              console.error('Error adding related data:', relatedDataError);
              // Continue with the registration process despite related data errors
            }

            // Verify that the related data was inserted correctly
            try {
              console.log('Verifying related data insertion...');
              // Get the formatted schema name and API base URL
              const { formatSchemaForUrl } = await import('../utils/schemaUtils');
              const { API_BASE_URL } = await import('../config/apiConfig');
              const formattedSchema = formatSchemaForUrl(tenant?.schema_name || '');

              // Make sure citizenId is defined
              if (!citizenId) {
                console.error('Citizen ID is undefined, cannot verify related data insertion');
                return;
              }

              const citizenDetailsUrl = `${API_BASE_URL}/api/tenant/${encodeURIComponent(formattedSchema)}/citizens/${citizenId}/`;
              console.log('Fetching citizen details from:', citizenDetailsUrl);

              // Get a guaranteed valid token for the API call
              const apiToken = await getGuaranteedValidToken();

              if (!apiToken) {
                console.error('Could not get a valid token for citizen details API call');
                console.warn('Skipping citizen details verification');
                return;
              }

              console.log('Using guaranteed valid token for citizen details API call');

              // Use direct fetch with the token
              const citizenDetailsResponse = await fetch(citizenDetailsUrl, {
                method: 'GET',
                headers: {
                  'Content-Type': 'application/json',
                  'Authorization': `Bearer ${apiToken}`,
                  'X-Schema-Name': formattedSchema
                },
                credentials: 'include'
              });

              if (citizenDetailsResponse.ok) {
                const citizenDetails = await citizenDetailsResponse.json();
                console.log('Citizen details after registration:', citizenDetails);

                // Check if related data was inserted
                console.log('Parents:', citizenDetails.parents);
                console.log('Spouse:', citizenDetails.spouses);
                console.log('Emergency contacts:', citizenDetails.emergency_contacts);
                console.log('Children:', citizenDetails.children);

                // Check if photo was uploaded
                console.log('Photo:', citizenDetails.photo);
                if (citizenDetails.photo) {
                  console.log('Photo was successfully uploaded!');
                  console.log('Photo URL:', citizenDetails.photo.photo_url);
                } else {
                  console.warn('Photo was not uploaded or not associated with the citizen');

                  // If photo wasn't uploaded but we have one, try to upload it directly
                  if (formData.photo) {
                    console.log('Attempting to upload photo directly...');
                    const photoFormData = new FormData();
                    photoFormData.append('photo', formData.photo);
                    photoFormData.append('citizen', citizenId);

                    // Get the formatted schema name
                    const { formatSchemaForUrl } = await import('../utils/schemaUtils');
                    const formattedSchema = formatSchemaForUrl(tenant?.schema_name || '');
                    const photoUrl = `/api/tenant/${encodeURIComponent(formattedSchema)}/citizens/${citizenId}/photos/`;
                    console.log('Uploading photo to:', photoUrl);

                    try {
                      // Get a guaranteed valid token for the API call
                      const photoToken = await getGuaranteedValidToken();

                      if (!photoToken) {
                        console.error('Could not get a valid token for photo upload API call');
                        console.warn('Skipping photo upload');
                        return;
                      }

                      console.log('Using guaranteed valid token for photo upload API call');

                      // Use direct fetch with the token
                      const photoResponse = await fetch(photoUrl, {
                        method: 'POST',
                        headers: {
                          'Authorization': `Bearer ${photoToken}`,
                          'X-Schema-Name': formattedSchema
                          // Don't set Content-Type when using FormData
                        },
                        credentials: 'include',
                        body: photoFormData
                      });

                      if (photoResponse.ok) {
                        const photoData = await photoResponse.json();
                        console.log('Photo uploaded successfully:', photoData);
                      } else {
                        console.error('Failed to upload photo:', photoResponse.status, photoResponse.statusText);
                        const errorText = await photoResponse.text();
                        console.error('Error details:', errorText);
                      }
                    } catch (photoError) {
                      console.error('Error uploading photo:', photoError);
                    }
                    }
                  }
              } else {
                console.error('Failed to fetch citizen details:', citizenDetailsResponse.status, citizenDetailsResponse.statusText);
              }
            } catch (verificationError) {
              console.error('Error verifying related data insertion:', verificationError);
            }

            // Set success state
            setSubmitSuccess(true);
            setSnackbarOpen(true);

            // Clear saved form data from local storage
            clearFormData(FORM_STORAGE_KEY);
          } catch (jsonError) {
            console.log('Error parsing success response as JSON:', jsonError);
            // Even if we can't parse the JSON, it's still a success
            setSubmitSuccess(true);
            setSnackbarOpen(true);

            // Clear saved form data from local storage
            clearFormData(FORM_STORAGE_KEY);
          }
        }
      } catch (apiError: any) {
        console.error('API call error:', apiError);
        // Set the error directly instead of throwing
        setSubmitError(`Failed to register citizen: ${apiError.message || 'An unexpected API error occurred'}`);
        setIsSubmitting(false);
        return;
      }

      // Reset form
      setFormData({
        // Basic Information
        registration_number: '',
        first_name: '',
        middle_name: '',
        last_name: '',
        first_name_am: '',
        middle_name_am: '',
        last_name_am: '',
        date_of_birth: new Date(),
        gender: '',
        religion: '',
        citizen_status: '',

        // Contact Information
        phone: '',
        email: '',
        address: '',
        house_number: '',
        subcity: '',
        kebele: '',
        ketena: '',

        // Nationality Information
        nationality: 'Ethiopian',
        nationality_country: '',
        region: '',

        // Employment Information
        employment: false,
        employee_type: '',
        organization_name: '',
        occupation: '',
        employment_type: '',

        // Family Information
        marital_status: '',
        spouse: null,
        mother: null,
        father: null,
        emergency_contact: null,

        // Other Information
        is_resident: true,

        // Photo
        photo: null
      });
      setPhotoPreview(null);
    } catch (error: any) {
      console.error('Error registering citizen:', error);

      // Create a more detailed error message
      let errorMessage = 'An error occurred during citizen registration';

      if (error.message) {
        // Clean up the error message for better readability
        const cleanedMessage = error.message
          .replace(/^Error: /, '')
          .replace(/\n/g, ' ')
          .trim();

        errorMessage += `: ${cleanedMessage}`;
      }

      // Add additional debugging information
      console.error('Error details:', {
        name: error.name,
        message: error.message,
        stack: error.stack,
      });

      // Set a user-friendly error message
      setSubmitError(errorMessage);

      // Log additional diagnostic information
      console.error('Form data at time of error:', formData);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle snackbar close
  const handleSnackbarClose = () => {
    setSnackbarOpen(false);
  };

  // Navigate back to citizens list
  const handleBackToList = () => {
    navigate('/citizens');
  };

  // Stepper functions
  const handleNext = () => {
    setActiveStep((prevActiveStep) => prevActiveStep + 1);
  };

  const handleBack = () => {
    setActiveStep((prevActiveStep) => prevActiveStep - 1);
  };

  const handleStep = (step: number) => () => {
    setActiveStep(step);
  };

  const handleComplete = () => {
    const newCompleted = completed;
    newCompleted[activeStep] = true;
    setCompleted(newCompleted);
    handleNext();
  };

  // Define steps for the stepper
  const steps = [
    { label: 'Basic Information', icon: <PersonIcon /> },
    { label: 'Contact Information', icon: <HomeIcon /> },
    { label: 'Additional Information', icon: <FlagIcon /> },
    { label: 'Family Information', icon: <FamilyRestroomIcon /> },
    { label: 'Emergency Contact', icon: <ContactEmergencyIcon /> },
    { label: 'Photo', icon: <PhotoCameraIcon /> },
    { label: 'Review & Submit', icon: <CheckCircleIcon /> }
  ];

  // Navigate to ID card registration
  const handleRegisterIDCard = () => {
    navigate('/id-cards/new');
  };

  // If tenant is not authorized, show unauthorized access component
  if (!isTenantAuthorized) {
    return (
      <UnauthorizedAccess
        message="Your tenant type does not have permission to register citizens."
        tenantType={tenant?.type}
      />
    );
  }

  // If not in full mode, render only the specific form
  if (mode !== 'full') {
    return (
      <LocalizationProvider dateAdapter={AdapterDateFns}>
        <Box>
          {/* Render only the specific form based on mode */}
          {mode === 'spouse-only' && (
            <Box sx={{ display: 'flex', flexDirection: 'column', width: '100%', flexGrow: 1, mb: 4 }}>
              {/* Spouse Information */}
              <Typography variant="subtitle2" color="text.secondary" sx={{ mt: 1 }}>
                Spouse Information
              </Typography>

              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={formData.spouse_is_resident}
                      onChange={(e) => setFormData(prev => ({ ...prev, spouse_is_resident: e.target.checked }))}
                      color="primary"
                    />
                  }
                  label="Spouse is a resident"
                />
              </Box>

              {formData.spouse_is_resident ? (
                <Box sx={{ mt: 2 }}>
                  <Box sx={{ mb: 2 }}>
                    <TextField
                      fullWidth
                      label="Search for spouse"
                      placeholder="Search by name, ID, or phone..."
                      value={spouseSearchQuery}
                      onChange={handleSpouseSearchChange}
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <SearchIcon color="action" />
                          </InputAdornment>
                        ),
                        endAdornment: searchingSpouse ? (
                          <InputAdornment position="end">
                            <CircularProgress size={20} />
                          </InputAdornment>
                        ) : null
                      }}
                    />
                  </Box>
                  <FormControl fullWidth>
                    <InputLabel id="spouse-citizen-select-label">Select Existing Citizen</InputLabel>
                    <Select
                      labelId="spouse-citizen-select-label"
                      id="spouse-citizen-select"
                      name="spouse_linked_citizen"
                      value={formData.spouse_linked_citizen}
                      onChange={handleChange}
                      label="Select Existing Citizen"
                      disabled={loadingCitizens || searchingSpouse}
                      startAdornment={<InputAdornment position="start"><PersonIcon color="action" /></InputAdornment>}
                      MenuProps={{
                        PaperProps: {
                          style: {
                            maxHeight: 300
                          }
                        }
                      }}
                    >
                      {loadingCitizens || searchingSpouse ? (
                        <MenuItem disabled>Loading citizens...</MenuItem>
                      ) : filteredSpouseCitizens.length === 0 ? (
                        <MenuItem disabled>No matching citizens found</MenuItem>
                      ) : (
                        filteredSpouseCitizens.map((citizen) => (
                          <MenuItem key={citizen.id} value={citizen.id}>
                            {citizen.first_name} {citizen.last_name} ({citizen.registration_number || 'No ID'})
                          </MenuItem>
                        ))
                      )}
                    </Select>
                    <FormHelperText>
                      {filteredSpouseCitizens.length > 0
                        ? `${filteredSpouseCitizens.length} citizens found`
                        : 'Search for a citizen to select as spouse'}
                    </FormHelperText>
                  </FormControl>
                </Box>
              ) : (
                <>
                  <Box sx={{
                    display: 'grid',
                    gridTemplateColumns: '1fr 1fr 1fr',
                    gap: 2,
                    '& > *': { width: '100%' }
                  }}>
                    <TextField
                      fullWidth
                      label="First Name"
                      name="spouse_first_name"
                      value={formData.spouse_first_name}
                      onChange={handleChange}
                      helperText="Enter spouse's first name"
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <PersonIcon color="action" />
                          </InputAdornment>
                        ),
                      }}
                    />
                    <TextField
                      fullWidth
                      label="Middle Name"
                      name="spouse_middle_name"
                      value={formData.spouse_middle_name}
                      onChange={handleChange}
                      helperText="Optional"
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <PersonIcon color="action" />
                          </InputAdornment>
                        ),
                      }}
                    />
                    <TextField
                      fullWidth
                      label="Last Name"
                      name="spouse_last_name"
                      value={formData.spouse_last_name}
                      onChange={handleChange}
                      helperText="Enter spouse's last name"
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <PersonIcon color="action" />
                          </InputAdornment>
                        ),
                      }}
                    />
                  </Box>
                  <Box sx={{
                    display: 'grid',
                    gridTemplateColumns: '1fr 1fr',
                    gap: 2,
                    mt: 2,
                    '& > *': { width: '100%' }
                  }}>
                    <TextField
                      fullWidth
                      label="Phone Number"
                      name="spouse_phone"
                      value={formData.spouse_phone}
                      onChange={handleChange}
                      helperText="Enter spouse's phone number"
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <PhoneIcon color="action" />
                          </InputAdornment>
                        ),
                      }}
                    />
                    <TextField
                      fullWidth
                      label="Email"
                      name="spouse_email"
                      value={formData.spouse_email}
                      onChange={handleChange}
                      helperText="Enter spouse's email address"
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <EmailIcon color="action" />
                          </InputAdornment>
                        ),
                      }}
                    />
                  </Box>
                </>
              )}
            </Box>
          )}

          {mode === 'parent-only' && (
            <Box sx={{ display: 'flex', flexDirection: 'column', width: '100%', flexGrow: 1, mb: 4 }}>
              {/* Parent Information */}
              <Typography variant="subtitle2" color="text.secondary" sx={{ mt: 1 }}>
                Parent Information
              </Typography>
              <Box sx={{
                display: 'grid',
                gridTemplateColumns: '1fr 1fr 1fr',
                gap: 2,
                mt: 2,
                '& > *': { width: '100%' }
              }}>
                <TextField
                  fullWidth
                  label="Mother's First Name"
                  name="mother_first_name"
                  value={formData.mother_first_name}
                  onChange={handleChange}
                  helperText="Enter mother's first name"
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <PersonIcon color="action" />
                      </InputAdornment>
                    ),
                  }}
                />
                <TextField
                  fullWidth
                  label="Mother's Middle Name"
                  name="mother_middle_name"
                  value={formData.mother_middle_name}
                  onChange={handleChange}
                  helperText="Optional"
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <PersonIcon color="action" />
                      </InputAdornment>
                    ),
                  }}
                />
                <TextField
                  fullWidth
                  label="Mother's Last Name"
                  name="mother_last_name"
                  value={formData.mother_last_name}
                  onChange={handleChange}
                  helperText="Enter mother's last name"
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <PersonIcon color="action" />
                      </InputAdornment>
                    ),
                  }}
                />
              </Box>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mt: 2 }}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={formData.mother_is_resident}
                      onChange={(e) => setFormData(prev => ({ ...prev, mother_is_resident: e.target.checked }))}
                      color="primary"
                    />
                  }
                  label="Mother is a resident"
                />
              </Box>
              {citizensError && (
                <Alert severity="error" sx={{ mb: 2 }}>
                  {citizensError}
                </Alert>
              )}

              {formData.mother_is_resident && (
                <Box sx={{ mt: 2 }}>
                  <FormControl fullWidth>
                    <Autocomplete
                      id="mother-citizen-select-parent-mode"
                      options={citizens.length > 0 ? citizens.filter(citizen => citizen.gender === 'F') : []}
                      getOptionLabel={(option) => {
                        if (!option) return '';
                        // Display the ID number that will be printed on the ID card
                        const idDisplay = option.id_number ? option.id_number :
                                         (option.registration_number ? option.registration_number : 'No ID');
                        return `${option.first_name} ${option.last_name} (${idDisplay})`;
                      }}
                      loading={loadingCitizens}
                      value={citizens.find(c => c.id === formData.mother_linked_citizen) || null}
                      onChange={(event, newValue) => {
                        setFormData(prev => ({
                          ...prev,
                          mother_linked_citizen: newValue ? newValue.id : ''
                        }));
                      }}
                      filterOptions={(options, state) => {
                        const inputValue = state.inputValue.toLowerCase();
                        return options.filter(option =>
                          option.first_name?.toLowerCase().includes(inputValue) ||
                          option.last_name?.toLowerCase().includes(inputValue) ||
                          option.id_number?.toLowerCase().includes(inputValue) ||
                          option.registration_number?.toLowerCase().includes(inputValue) ||
                          option.phone?.toLowerCase().includes(inputValue)
                        );
                      }}
                      renderInput={(params) => (
                        <TextField
                          {...params}
                          label="Select Female Citizen"
                          InputProps={{
                            ...params.InputProps,
                            startAdornment: (
                              <>
                                <InputAdornment position="start">
                                  <PersonIcon color="action" />
                                </InputAdornment>
                                {params.InputProps.startAdornment}
                              </>
                            ),
                            endAdornment: (
                              <>
                                {loadingCitizens ? <CircularProgress color="inherit" size={20} /> : null}
                                <Box sx={{ display: 'flex', alignItems: 'center', bgcolor: 'pink.light', px: 1, py: 0.5, borderRadius: 1, mr: 2 }}>
                                  <Typography variant="caption" sx={{ color: 'pink.dark', fontWeight: 'bold' }}>Female Only</Typography>
                                </Box>
                                {params.InputProps.endAdornment}
                              </>
                            )
                          }}
                        />
                      )}
                      renderOption={(props, option) => {
                        // Display the ID number that will be printed on the ID card
                        const idDisplay = option.id_number ? option.id_number :
                                         (option.registration_number ? option.registration_number : 'No ID');
                        return (
                          <li {...props}>
                            {option.first_name} {option.last_name} ({idDisplay})
                          </li>
                        );
                      }}
                      noOptionsText={citizens.length === 0 ? "No citizens found in database. Please add citizens first." : "No matching female citizens found"}
                      loadingText="Loading citizens..."
                    />
                    <FormHelperText>
                      Search and select a female citizen as mother
                    </FormHelperText>
                  </FormControl>
                </Box>
              )}
              <Divider sx={{ my: 3 }} />
              <Box sx={{
                display: 'grid',
                gridTemplateColumns: '1fr 1fr 1fr',
                gap: 2,
                '& > *': { width: '100%' }
              }}>
                <TextField
                  fullWidth
                  label="Father's First Name"
                  name="father_first_name"
                  value={formData.father_first_name}
                  onChange={handleChange}
                  helperText="Enter father's first name"
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <PersonIcon color="action" />
                      </InputAdornment>
                    ),
                  }}
                />
                <TextField
                  fullWidth
                  label="Father's Middle Name"
                  name="father_middle_name"
                  value={formData.father_middle_name}
                  onChange={handleChange}
                  helperText="Optional"
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <PersonIcon color="action" />
                      </InputAdornment>
                    ),
                  }}
                />
                <TextField
                  fullWidth
                  label="Father's Last Name"
                  name="father_last_name"
                  value={formData.father_last_name}
                  onChange={handleChange}
                  helperText="Enter father's last name"
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <PersonIcon color="action" />
                      </InputAdornment>
                    ),
                  }}
                />
              </Box>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mt: 2 }}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={formData.father_is_resident}
                      onChange={(e) => setFormData(prev => ({ ...prev, father_is_resident: e.target.checked }))}
                      color="primary"
                    />
                  }
                  label="Father is a resident"
                />
              </Box>
              {formData.father_is_resident && (
                <Box sx={{ mt: 2 }}>
                  <Box sx={{ mb: 2 }}>
                    <TextField
                      fullWidth
                      label="Search for father"
                      placeholder="Search by name, ID, or phone..."
                      value={fatherSearchQuery}
                      onChange={handleFatherSearchChange}
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <SearchIcon color="action" />
                          </InputAdornment>
                        ),
                        endAdornment: searchingFather ? (
                          <InputAdornment position="end">
                            <CircularProgress size={20} />
                          </InputAdornment>
                        ) : null
                      }}
                    />
                  </Box>
                  <FormControl fullWidth>
                    <InputLabel id="father-citizen-select-label">Select Existing Citizen</InputLabel>
                    <Select
                      labelId="father-citizen-select-label"
                      id="father-citizen-select"
                      name="father_linked_citizen"
                      value={formData.father_linked_citizen}
                      onChange={handleChange}
                      label="Select Existing Citizen"
                      disabled={loadingCitizens || searchingFather}
                      startAdornment={<InputAdornment position="start"><PersonIcon color="action" /></InputAdornment>}
                      MenuProps={{
                        PaperProps: {
                          style: {
                            maxHeight: 300
                          }
                        }
                      }}
                    >
                      {loadingCitizens || searchingFather ? (
                        <MenuItem disabled>Loading citizens...</MenuItem>
                      ) : filteredFatherCitizens.length === 0 ? (
                        <MenuItem disabled>No matching citizens found</MenuItem>
                      ) : (
                        filteredFatherCitizens.map((citizen) => (
                          <MenuItem key={citizen.id} value={citizen.id}>
                            {citizen.first_name} {citizen.last_name} ({citizen.id_number ? citizen.id_number : (citizen.registration_number ? citizen.registration_number : 'No ID')})
                          </MenuItem>
                        ))
                      )}
                    </Select>
                    <FormHelperText>
                      {filteredFatherCitizens.length > 0
                        ? `${filteredFatherCitizens.length} male citizens found`
                        : 'Search for a male citizen to select as father'}
                    </FormHelperText>
                  </FormControl>
                </Box>
              )}
            </Box>
          )}

          {mode === 'child-only' && (
            <Box sx={{ display: 'flex', flexDirection: 'column', width: '100%', flexGrow: 1, mb: 4 }}>
              {/* Children Information */}
              <Typography variant="subtitle2" color="text.secondary" sx={{ mt: 1 }}>
                Child Information
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mt: 2 }}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={formData.has_children}
                      onChange={(e) => setFormData(prev => ({ ...prev, has_children: e.target.checked }))}
                      color="primary"
                    />
                  }
                  label="Add Child Information"
                />
              </Box>
              {formData.has_children && (
                <>
                  <Box sx={{
                    display: 'grid',
                    gridTemplateColumns: '1fr 1fr 1fr',
                    gap: 2,
                    mt: 2,
                    '& > *': { width: '100%' }
                  }}>
                    <TextField
                      fullWidth
                      label="First Name"
                      name="first_name"
                      value={childForm.first_name}
                      onChange={handleChildFormChange}
                      helperText="Enter child's first name"
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <PersonIcon color="action" />
                          </InputAdornment>
                        ),
                      }}
                    />
                    <TextField
                      fullWidth
                      label="Middle Name"
                      name="middle_name"
                      value={childForm.middle_name}
                      onChange={handleChildFormChange}
                      helperText="Optional"
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <PersonIcon color="action" />
                          </InputAdornment>
                        ),
                      }}
                    />
                    <TextField
                      fullWidth
                      label="Last Name"
                      name="last_name"
                      value={childForm.last_name}
                      onChange={handleChildFormChange}
                      helperText="Enter child's last name"
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <PersonIcon color="action" />
                          </InputAdornment>
                        ),
                      }}
                    />
                  </Box>
                  <Box sx={{
                    display: 'grid',
                    gridTemplateColumns: '1fr 1fr',
                    gap: 2,
                    mt: 2,
                    '& > *': { width: '100%' }
                  }}>
                    <FormControl fullWidth>
                      <InputLabel id="child-gender-label">Gender</InputLabel>
                      <Select
                        labelId="child-gender-label"
                        id="child-gender-select"
                        name="gender"
                        value={childForm.gender}
                        onChange={handleChildFormChange}
                        label="Gender"
                        startAdornment={<InputAdornment position="start"><WcIcon color="action" /></InputAdornment>}
                      >
                        <MenuItem value="M">Male</MenuItem>
                        <MenuItem value="F">Female</MenuItem>
                      </Select>
                      <FormHelperText>Select child's gender</FormHelperText>
                    </FormControl>
                    <LocalizationProvider dateAdapter={AdapterDateFns}>
                      <DatePicker
                        label="Date of Birth"
                        value={childForm.date_of_birth}
                        onChange={handleChildDateChange}
                        slotProps={{
                          textField: {
                            fullWidth: true,
                            helperText: "Enter child's date of birth",
                            InputProps: {
                              startAdornment: (
                                <InputAdornment position="start">
                                  <CalendarMonthIcon color="action" />
                                </InputAdornment>
                              ),
                            }
                          }
                        }}
                      />
                    </LocalizationProvider>
                  </Box>
                  <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 2 }}>
                    <Button
                      variant="contained"
                      color="primary"
                      startIcon={<AddIcon />}
                      onClick={handleAddChild}
                      disabled={!childForm.first_name || !childForm.last_name || !childForm.gender || !childForm.date_of_birth}
                      sx={{ borderRadius: 2 }}
                    >
                      Add Child
                    </Button>
                  </Box>
                  {formData.children.length > 0 && (
                    <Box sx={{ mt: 3 }}>
                      <Typography variant="subtitle2" color="text.secondary">
                        Added Children
                      </Typography>
                      <List>
                        {formData.children.map((child, index) => (
                          <ListItem
                            key={index}
                            secondaryAction={
                              <IconButton edge="end" aria-label="delete" onClick={() => handleRemoveChild(index)}>
                                <DeleteIcon />
                              </IconButton>
                            }
                          >
                            <ListItemAvatar>
                              <Avatar>
                                <ChildCareIcon />
                              </Avatar>
                            </ListItemAvatar>
                            <ListItemText
                              primary={`${child.first_name} ${child.middle_name || ''} ${child.last_name}`}
                              secondary={`${child.gender === 'M' ? 'Male' : 'Female'} | Born: ${format(new Date(child.date_of_birth), 'MMMM d, yyyy')}`}
                            />
                          </ListItem>
                        ))}
                      </List>
                    </Box>
                  )}
                </>
              )}
            </Box>
          )}

          {mode === 'emergency-contact-only' && (
            <Box sx={{ display: 'flex', flexDirection: 'column', width: '100%', flexGrow: 1, mb: 4 }}>
              {/* Emergency Contact Information */}
              <Typography variant="subtitle2" color="text.secondary" sx={{ mt: 1 }}>
                Emergency Contact Information
              </Typography>
              <Box sx={{
                display: 'grid',
                gridTemplateColumns: '1fr 1fr',
                gap: 2,
                mt: 2,
                '& > *': { width: '100%' }
              }}>
                <FormControl fullWidth>
                  <InputLabel id="emergency-relationship-label">Relationship</InputLabel>
                  <Select
                    labelId="emergency-relationship-label"
                    id="emergency-relationship-select"
                    name="emergency_contact_relationship"
                    value={formData.emergency_contact_relationship}
                    onChange={handleChange}
                    label="Relationship"
                    startAdornment={<InputAdornment position="start"><ContactEmergencyIcon color="action" /></InputAdornment>}
                  >
                    {loadingRelationshipTypes ? (
                      <MenuItem disabled>Loading relationship types...</MenuItem>
                    ) : relationshipTypes.length === 0 ? (
                      <>
                        <MenuItem value="Spouse">Spouse</MenuItem>
                        <MenuItem value="Parent">Parent</MenuItem>
                        <MenuItem value="Child">Child</MenuItem>
                        <MenuItem value="Sibling">Sibling</MenuItem>
                        <MenuItem value="Friend">Friend</MenuItem>
                        <MenuItem value="Other">Other</MenuItem>
                      </>
                    ) : (
                      relationshipTypes.map((type) => (
                        <MenuItem key={type.id} value={type.id}>
                          {type.name}
                        </MenuItem>
                      ))
                    )}
                  </Select>
                  <FormHelperText>Select relationship to emergency contact</FormHelperText>
                </FormControl>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={formData.emergency_contact_is_resident}
                        onChange={(e) => setFormData(prev => ({ ...prev, emergency_contact_is_resident: e.target.checked }))}
                        color="primary"
                      />
                    }
                    label="Contact is a resident"
                  />
                </Box>
              </Box>
              {formData.emergency_contact_is_resident ? (
                <Box sx={{ mt: 2 }}>
                  <Box sx={{ mb: 2 }}>
                    <TextField
                      fullWidth
                      label="Search for emergency contact"
                      placeholder="Search by name, ID, or phone..."
                      value={emergencyContactSearchQuery}
                      onChange={handleEmergencyContactSearchChange}
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <SearchIcon color="action" />
                          </InputAdornment>
                        ),
                        endAdornment: searchingEmergencyContact ? (
                          <InputAdornment position="end">
                            <CircularProgress size={20} />
                          </InputAdornment>
                        ) : null
                      }}
                    />
                  </Box>
                  <FormControl fullWidth>
                    <InputLabel id="emergency-citizen-select-label">Select Existing Citizen</InputLabel>
                    <Select
                      labelId="emergency-citizen-select-label"
                      id="emergency-citizen-select"
                      name="emergency_contact_linked_citizen"
                      value={formData.emergency_contact_linked_citizen}
                      onChange={handleChange}
                      label="Select Existing Citizen"
                      disabled={loadingCitizens || searchingEmergencyContact}
                      startAdornment={<InputAdornment position="start"><PersonIcon color="action" /></InputAdornment>}
                      MenuProps={{
                        PaperProps: {
                          style: {
                            maxHeight: 300
                          }
                        }
                      }}
                    >
                      {loadingCitizens || searchingEmergencyContact ? (
                        <MenuItem disabled>Loading citizens...</MenuItem>
                      ) : filteredEmergencyContactCitizens.length === 0 ? (
                        <MenuItem disabled>No matching citizens found</MenuItem>
                      ) : (
                        filteredEmergencyContactCitizens.map((citizen) => (
                          <MenuItem key={citizen.id} value={citizen.id}>
                            {citizen.first_name} {citizen.last_name} ({citizen.id_number ? citizen.id_number : (citizen.registration_number ? citizen.registration_number : 'No ID')})
                          </MenuItem>
                        ))
                      )}
                    </Select>
                    <FormHelperText>
                      {filteredEmergencyContactCitizens.length > 0
                        ? `${filteredEmergencyContactCitizens.length} citizens found`
                        : 'Search for a citizen to select as emergency contact'}
                    </FormHelperText>
                  </FormControl>
                </Box>
              ) : (
                <>
                  <Box sx={{
                    display: 'grid',
                    gridTemplateColumns: '1fr 1fr 1fr',
                    gap: 2,
                    mt: 2,
                    '& > *': { width: '100%' }
                  }}>
                    <TextField
                      fullWidth
                      label="First Name"
                      name="emergency_contact_first_name"
                      value={formData.emergency_contact_first_name}
                      onChange={handleChange}
                      helperText="Enter contact's first name"
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <PersonIcon color="action" />
                          </InputAdornment>
                        ),
                      }}
                    />
                    <TextField
                      fullWidth
                      label="Middle Name"
                      name="emergency_contact_middle_name"
                      value={formData.emergency_contact_middle_name}
                      onChange={handleChange}
                      helperText="Optional"
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <PersonIcon color="action" />
                          </InputAdornment>
                        ),
                      }}
                    />
                    <TextField
                      fullWidth
                      label="Last Name"
                      name="emergency_contact_last_name"
                      value={formData.emergency_contact_last_name}
                      onChange={handleChange}
                      helperText="Enter contact's last name"
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <PersonIcon color="action" />
                          </InputAdornment>
                        ),
                      }}
                    />
                  </Box>
                  <Box sx={{
                    display: 'grid',
                    gridTemplateColumns: '1fr 1fr',
                    gap: 2,
                    mt: 2,
                    '& > *': { width: '100%' }
                  }}>
                    <TextField
                      fullWidth
                      label="Phone Number"
                      name="emergency_contact_phone"
                      value={formData.emergency_contact_phone}
                      onChange={handleChange}
                      helperText="Enter contact's phone number"
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <PhoneIcon color="action" />
                          </InputAdornment>
                        ),
                      }}
                    />
                    <TextField
                      fullWidth
                      label="Email"
                      name="emergency_contact_email"
                      value={formData.emergency_contact_email}
                      onChange={handleChange}
                      helperText="Enter contact's email address"
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <EmailIcon color="action" />
                          </InputAdornment>
                        ),
                      }}
                    />
                  </Box>
                </>
              )}
            </Box>
          )}
        </Box>
      </LocalizationProvider>
    );
  }

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns}>

      <Box sx={{ py: 4 }}>
        {/* Banner */}
        <PageBanner
          title="Register New Citizen"
          subtitle={`Add a new citizen to ${tenant?.name || 'your center'}`}
          icon={<PersonAddIcon sx={{ fontSize: 50, color: 'white' }} />}
          actionContent={
            <Box sx={{ textAlign: 'center', width: '100%' }}>
              <Typography variant="body1" sx={{ color: 'white', fontWeight: 500 }}>
                Fill out the form below to register a new citizen. You can then create an ID card for them.
              </Typography>
            </Box>
          }
        />

        {authError && (
          <Paper
            elevation={3}
            sx={{
              p: 3,
              mb: 3,
              backgroundColor: '#fff8e1',
              border: '1px solid #ffd54f',
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center'
            }}
          >
            <Typography variant="h6" color="error" gutterBottom>
              Authentication Error
            </Typography>
            <Typography variant="body1" paragraph>
              Your session has expired or your authentication token is invalid. Please log in again to continue.
            </Typography>
            <Button
              variant="contained"
              color="primary"
              onClick={handleLoginRedirect}
              startIcon={<PersonIcon />}
              sx={{ mt: 1 }}
            >
              Log In Again
            </Button>
          </Paper>
        )}

      <Container maxWidth="xl" sx={{ position: 'relative' }}>
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            mb: 4,
            mt: -2,
            position: 'relative',
            zIndex: 5
          }}
        >
          <Button
            startIcon={<ArrowBackIcon />}
            onClick={handleBackToList}
            variant="contained"
            color="secondary"
            sx={{
              borderRadius: 8,
              px: 4,
              py: 1.5,
              boxShadow: '0 4px 14px rgba(0,0,0,0.15)',
              fontWeight: 600,
              '&:hover': {
                boxShadow: '0 6px 20px rgba(0,0,0,0.2)',
                transform: 'translateY(-2px)'
              },
              transition: 'all 0.3s'
            }}
          >
            Back to Citizens
          </Button>

          <Paper
            elevation={0}
            sx={{
              py: 1.5,
              px: 3,
              borderRadius: 8,
              display: 'flex',
              alignItems: 'center',
              bgcolor: 'rgba(255, 255, 255, 0.9)',
              backdropFilter: 'blur(10px)',
              boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
              border: '1px solid rgba(0,0,0,0.05)'
            }}
          >
            <Typography variant="subtitle1" color="text.secondary" sx={{ fontWeight: 500 }}>
              All fields marked with <Box component="span" sx={{ color: 'error.main', fontWeight: 'bold' }}>*</Box> are required
            </Typography>
          </Paper>
        </Box>

        {/* Horizontal Stepper */}
        <Box sx={{ mb: 4 }}>
          <Stepper
            activeStep={activeStep}
            alternativeLabel
            sx={{
              '& .MuiStepLabel-root': {
                '& .MuiStepLabel-iconContainer': {
                  '& .MuiStepIcon-root': {
                    fontSize: 32,
                    '&.Mui-active': {
                      color: '#4CAF50',
                    },
                    '&.Mui-completed': {
                      color: '#2E7D32',
                    },
                  },
                },
                '& .MuiStepLabel-labelContainer': {
                  '& .MuiStepLabel-label': {
                    mt: 1,
                    fontWeight: 500,
                    '&.Mui-active': {
                      fontWeight: 700,
                      color: '#2E7D32',
                    },
                  },
                },
              },
            }}
          >
            {steps.map((step, index) => (
              <Step key={step.label} completed={completed[index]}>
                <StepButton onClick={handleStep(index)} icon={step.icon}>
                  {step.label}
                </StepButton>
              </Step>
            ))}
          </Stepper>
        </Box>

        <Paper
          elevation={0}
          sx={{
            p: { xs: 2, sm: 3, md: 4 },
            borderRadius: 4,
            boxShadow: '0 8px 30px rgba(0,0,0,0.12)',
            border: '1px solid rgba(0, 0, 0, 0.05)',
            bgcolor: '#FFFFFF',
            minHeight: 'calc(100vh - 300px)',
            display: 'flex',
            flexDirection: 'column',
            overflow: 'hidden',
            position: 'relative',
            '&::before': {
              content: '""',
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              height: '8px',
              background: 'linear-gradient(90deg, #4CAF50, #2E7D32)',
              borderRadius: '4px 4px 0 0'
            }
          }}
        >
          {/* Error message */}
          {submitError && (
            <Alert severity="error" sx={{ mb: 3 }}>
              <AlertTitle>Error</AlertTitle>
              {submitError.split('\n').map((line, index) => (
                <Typography key={index} variant="body2" component="div">
                  {line}
                </Typography>
              ))}
            </Alert>
          )}

          {/* Form */}
          <form onSubmit={(e) => e.preventDefault()} style={{ display: 'flex', flexDirection: 'column', flexGrow: 1 }}>
            <ThemeProvider theme={compactFormTheme}>
            <Typography variant="h5" sx={{ mb: 4, fontWeight: 700, color: '#2E7D32', display: 'flex', alignItems: 'center' }}>
              <PersonAddIcon sx={{ mr: 1.5, fontSize: 28 }} /> Citizen Registration Form
            </Typography>

            {/* Stepper Content */}
            <Box sx={{ mt: 2, mb: 4 }}>
              {/* Step 1: Basic Information */}
              {activeStep === 0 && (
                <Box sx={{
                  display: 'flex',
                  flexDirection: 'column',
                  width: '100%',
                  flexGrow: 1,
                  mb: 4
                }}>
                  <Card sx={{
                    mb: 4,
                    height: '100%',
                    width: '100%',
                    display: 'flex',
                    flexDirection: 'column',
                    transition: 'all 0.3s ease',
                    '&:hover': {
                      transform: 'translateY(-5px)',
                      boxShadow: '0 12px 20px rgba(0, 0, 0, 0.1)',
                    },
                    borderRadius: 2,
                    overflow: 'hidden',
                    border: 'none',
                    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.08)',
                    position: 'relative'
                  }}>
                    <Box sx={{
                      p: 2,
                      bgcolor: 'primary.main',
                      color: 'white',
                      display: 'flex',
                      alignItems: 'center'
                    }}>
                      <Box
                        sx={{
                          mr: 1,
                        }}
                      >
                        <PersonIcon fontSize="medium" />
                      </Box>
                      <Typography variant="h6" sx={{ fontWeight: 600 }}>
                        Basic Information
                      </Typography>
                    </Box>
              <CardContent sx={{ p: 2 }}>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                  Enter the citizen's personal information including name, date of birth, gender, and status details.
                </Typography>
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                  <TextField
                    fullWidth
                    label="Registration Number"
                    name="registration_number"
                    value={formData.registration_number}
                    onChange={handleChange}
                    disabled
                    helperText="Auto-generated unique identifier"
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <PersonIcon color="disabled" />
                        </InputAdornment>
                      ),
                    }}
                  />

                  {/* English Name Fields */}
                  <Typography variant="subtitle2" color="text.secondary" sx={{ mt: 1 }}>
                    Name in English
                  </Typography>
                  <Box sx={{
                    display: 'grid',
                    gridTemplateColumns: '1fr 1fr 1fr',
                    gap: 2,
                    '& > *': { width: '100%' }
                  }}>
                    <TextField
                      required
                      fullWidth
                      label="First Name"
                      name="first_name"
                      value={formData.first_name}
                      onChange={handleChange}
                      helperText="Enter legal first name"
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <PersonIcon color="action" />
                          </InputAdornment>
                        ),
                      }}
                    />
                    <TextField
                      fullWidth
                      label="Middle Name"
                      name="middle_name"
                      value={formData.middle_name}
                      onChange={handleChange}
                      helperText="Optional"
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <PersonIcon color="action" />
                          </InputAdornment>
                        ),
                      }}
                    />
                    <TextField
                      required
                      fullWidth
                      label="Last Name"
                      name="last_name"
                      value={formData.last_name}
                      onChange={handleChange}
                      helperText="Enter legal last name"
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <PersonIcon color="action" />
                          </InputAdornment>
                        ),
                      }}
                    />
                  </Box>

                  {/* Amharic Name Fields */}
                  <Typography variant="subtitle2" color="text.secondary" sx={{ mt: 1 }}>
                    Name in Amharic (Optional)
                  </Typography>
                  <Box sx={{
                    display: 'grid',
                    gridTemplateColumns: '1fr 1fr 1fr',
                    gap: 3,
                    '& > *': { width: '100%' }
                  }}>
                    <TextField
                      fullWidth
                      label="First Name (Amharic)"
                      name="first_name_am"
                      value={formData.first_name_am}
                      onChange={handleChange}
                      helperText="Enter first name in Amharic"
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <PersonIcon color="action" />
                          </InputAdornment>
                        ),
                      }}
                    />
                    <TextField
                      fullWidth
                      label="Middle Name (Amharic)"
                      name="middle_name_am"
                      value={formData.middle_name_am}
                      onChange={handleChange}
                      helperText="Optional"
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <PersonIcon color="action" />
                          </InputAdornment>
                        ),
                      }}
                    />
                    <TextField
                      fullWidth
                      label="Last Name (Amharic)"
                      name="last_name_am"
                      value={formData.last_name_am}
                      onChange={handleChange}
                      helperText="Enter last name in Amharic"
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <PersonIcon color="action" />
                          </InputAdornment>
                        ),
                      }}
                    />
                  </Box>

                  {/* Personal Information */}
                  <Typography variant="subtitle2" color="text.secondary" sx={{ mt: 1 }}>
                    Personal Information
                  </Typography>
                  <Box sx={{
                    display: 'grid',
                    gridTemplateColumns: '1fr 1fr',
                    gap: 3,
                    '& > *': { width: '100%' }
                  }}>
                    <DatePicker
                      label="Date of Birth"
                      value={formData.date_of_birth}
                      onChange={handleDateChange}
                      slotProps={{
                        textField: {
                          fullWidth: true,
                          required: true,
                          helperText: 'Must be at least 18 years old',
                          InputProps: {
                            startAdornment: (
                              <InputAdornment position="start">
                                <CalendarTodayIcon color="action" />
                              </InputAdornment>
                            ),
                          }
                        }
                      }}
                    />
                    <FormControl fullWidth required>
                      <InputLabel id="gender-select-label">Gender</InputLabel>
                      <Select
                        labelId="gender-select-label"
                        id="gender-select"
                        name="gender"
                        value={formData.gender}
                        onChange={handleChange}
                        label="Gender"
                        startAdornment={<InputAdornment position="start"><WcIcon color="action" /></InputAdornment>}
                      >
                        <MenuItem value="M">Male</MenuItem>
                        <MenuItem value="F">Female</MenuItem>
                      </Select>
                      <FormHelperText>Select gender</FormHelperText>
                    </FormControl>
                  </Box>

                  {/* Status Information */}
                  <Typography variant="subtitle2" color="text.secondary" sx={{ mt: 1 }}>
                    Status Information
                  </Typography>
                  <Box sx={{
                    display: 'grid',
                    gridTemplateColumns: '1fr 1fr 1fr',
                    gap: 3,
                    '& > *': { width: '100%' }
                  }}>
                    <FormControl fullWidth>
                      <InputLabel id="religion-select-label">Religion</InputLabel>
                      <Select
                        labelId="religion-select-label"
                        id="religion-select"
                        name="religion"
                        value={formData.religion}
                        onChange={handleChange}
                        label="Religion"
                        startAdornment={<InputAdornment position="start"><PersonIcon color="action" /></InputAdornment>}
                      >
                        {loadingReligions ? (
                          <MenuItem disabled>Loading religions...</MenuItem>
                        ) : religions.length === 0 ? (
                          // Fallback options if no religions are available
                          <>
                            <MenuItem value="1">Orthodox Christianity</MenuItem>
                            <MenuItem value="2">Islam</MenuItem>
                            <MenuItem value="3">Protestantism</MenuItem>
                            <MenuItem value="4">Catholicism</MenuItem>
                            <MenuItem value="5">Judaism</MenuItem>
                            <MenuItem value="6">Hinduism</MenuItem>
                            <MenuItem value="7">Buddhism</MenuItem>
                            <MenuItem value="8">Traditional Beliefs</MenuItem>
                            <MenuItem value="9">Other</MenuItem>
                            <MenuItem value="10">None</MenuItem>
                          </>
                        ) : (
                          // Add debugging to see what's being rendered
                          console.log('Rendering religion dropdown with data:', religions) ||
                          religions.map((religion) => {
                            console.log('Religion item:', religion);
                            return (
                              <MenuItem key={religion.id} value={religion.id}>
                                {religion.name}
                              </MenuItem>
                            );
                          })
                        )}
                      </Select>
                      <FormHelperText>Select religion (optional)</FormHelperText>
                    </FormControl>

                    <FormControl fullWidth>
                      <InputLabel id="citizen-status-select-label">Citizen Status</InputLabel>
                      <Select
                        labelId="citizen-status-select-label"
                        id="citizen-status-select"
                        name="citizen_status"
                        value={formData.citizen_status}
                        onChange={handleChange}
                        label="Citizen Status"
                        startAdornment={<InputAdornment position="start"><PersonIcon color="action" /></InputAdornment>}
                      >
                        {loadingCitizenStatuses ? (
                          <MenuItem disabled>Loading citizen statuses...</MenuItem>
                        ) : citizenStatuses.length === 0 ? (
                          // Fallback options if no citizen statuses are available
                          [
                            <MenuItem key="1" value="1">Resident</MenuItem>,
                            <MenuItem key="2" value="2">Non-Resident</MenuItem>,
                            <MenuItem key="3" value="3">Temporary Resident</MenuItem>,
                            <MenuItem key="4" value="4">Refugee</MenuItem>,
                            <MenuItem key="5" value="5">Asylum Seeker</MenuItem>,
                            <MenuItem key="6" value="6">Diplomat</MenuItem>,
                            <MenuItem key="7" value="7">Student</MenuItem>,
                            <MenuItem key="8" value="8">Worker</MenuItem>,
                            <MenuItem key="9" value="9">Visitor</MenuItem>,
                            <MenuItem key="10" value="10">Other</MenuItem>
                          ]
                        ) : (
                          citizenStatuses.map((status) => (
                            <MenuItem key={status.id} value={status.id}>
                              {status.name}
                            </MenuItem>
                          ))
                        )}
                      </Select>
                      <FormHelperText>Select citizen status (optional)</FormHelperText>
                    </FormControl>

                    <FormControl fullWidth>
                      <InputLabel id="marital-status-select-label">Marital Status</InputLabel>
                      <Select
                        labelId="marital-status-select-label"
                        id="marital-status-select"
                        name="marital_status"
                        value={formData.marital_status}
                        onChange={handleChange}
                        label="Marital Status"
                        startAdornment={<InputAdornment position="start"><PersonIcon color="action" /></InputAdornment>}
                      >
                        {loadingMaritalStatuses ? (
                          <MenuItem disabled>Loading marital statuses...</MenuItem>
                        ) : maritalStatuses.length === 0 ? (
                          // Fallback options if no marital statuses are available
                          [
                            <MenuItem key="SINGLE" value="SINGLE">Single</MenuItem>,
                            <MenuItem key="MARRIED" value="MARRIED">Married</MenuItem>,
                            <MenuItem key="DIVORCED" value="DIVORCED">Divorced</MenuItem>,
                            <MenuItem key="WIDOWED" value="WIDOWED">Widowed</MenuItem>,
                            <MenuItem key="SEPARATED" value="SEPARATED">Separated</MenuItem>
                          ]
                        ) : (
                          maritalStatuses.map((status) => (
                            <MenuItem key={status.id} value={status.id}>
                              {status.name}
                            </MenuItem>
                          ))
                        )}
                      </Select>
                      <FormHelperText>Select marital status (optional)</FormHelperText>
                    </FormControl>
                  </Box>
                </Box>
              </CardContent>
            </Card>








                </Box>
              )}

              {activeStep === 1 && (
                <Box sx={{
                  display: 'flex',
                  flexDirection: 'column',
                  width: '100%',
                  flexGrow: 1,
                  mb: 4
                }}>
                  <Card sx={{
                    mb: 4,
                    height: '100%',
                    width: '100%',
                    display: 'flex',
                    flexDirection: 'column',
                    transition: 'all 0.3s ease',
                    '&:hover': {
                      transform: 'translateY(-5px)',
                      boxShadow: '0 12px 20px rgba(0, 0, 0, 0.1)',
                    },
                    borderRadius: 2,
                    overflow: 'hidden',
                    border: 'none',
                    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.08)',
                    position: 'relative'
                  }}>
                    <Box sx={{
                      p: 2,
                      bgcolor: 'secondary.main',
                      color: 'white',
                      display: 'flex',
                      alignItems: 'center'
                    }}>
                      <Box
                        sx={{
                          mr: 1,
                        }}
                      >
                        <HomeIcon fontSize="medium" />
                      </Box>
                      <Typography variant="h6" sx={{ fontWeight: 600 }}>
                        Contact Information
                      </Typography>
                    </Box>
                    <CardContent sx={{ p: 2, flexGrow: 1 }}>
                      <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                        Enter the citizen's contact information including phone numbers, email, and residential address details.
                      </Typography>
                      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                        {/* Contact Details */}
                        <Typography variant="subtitle2" color="text.secondary" sx={{ mt: 1 }}>
                          Contact Details
                        </Typography>
                        <Box sx={{
                          display: 'grid',
                          gridTemplateColumns: '1fr 1fr',
                          gap: 3,
                          '& > *': { width: '100%' }
                        }}>
                          <TextField
                            fullWidth
                            label="Phone Number"
                            name="phone"
                            value={formData.phone}
                            onChange={handleChange}
                            helperText="Enter contact phone number"
                            InputProps={{
                              startAdornment: (
                                <InputAdornment position="start">
                                  <PhoneIcon color="action" />
                                </InputAdornment>
                              ),
                            }}
                          />
                          <TextField
                            fullWidth
                            label="Email"
                            name="email"
                            type="email"
                            value={formData.email}
                            onChange={handleChange}
                            helperText="Enter email address (optional)"
                            InputProps={{
                              startAdornment: (
                                <InputAdornment position="start">
                                  <EmailIcon color="action" />
                                </InputAdornment>
                              ),
                            }}
                          />
                        </Box>

                        {/* Location Information */}
                        <Box sx={{ mb: 2 }}>
                          <Typography variant="subtitle2" color="text.secondary" sx={{ mt: 1 }}>
                            Location Information
                          </Typography>
                        </Box>

                        <Box sx={{
                          display: 'grid',
                          gridTemplateColumns: '1fr 1fr 1fr',
                          gap: 2,
                          '& > *': { width: '100%' }
                        }}>
                          <FormControl fullWidth>
                            <TextField
                              fullWidth
                              label="SubCity"
                              name="subcity_display"
                              value={(() => {
                                // Get the subcity name from various sources

                                // First try to get tenant information from localStorage
                                const tenantString = localStorage.getItem('tenant');
                                if (tenantString) {
                                  try {
                                    const localTenant = JSON.parse(tenantString);
                                    if (localTenant?.parent_name) {
                                      return localTenant.parent_name;
                                    }
                                  } catch (error) {
                                    console.error('Error parsing tenant from localStorage:', error);
                                  }
                                }

                                // Try to determine parent subcity from schema_name
                                const schemaName = localStorage.getItem('schema_name');
                                if (schemaName && schemaName.startsWith('kebele')) {
                                  // For kebele schemas, the parent is typically a subcity

                                  // Use a mapping of kebele schemas to parent subcity names
                                  const kebeleToSubcityMap = {
                                    'kebele14': 'Zoble',
                                    'kebele15': 'Zoble',
                                    'kebele16': 'Zoble',
                                    // Add more mappings as needed
                                  };

                                  const parentName = kebeleToSubcityMap[schemaName];
                                  if (parentName) {
                                    return parentName;
                                  }
                                }

                                // Fallback to tenant context
                                if (tenant?.parent_name) {
                                  return tenant.parent_name;
                                }

                                // Last resort fallback
                                return 'Zoble';
                              })()}
                              disabled={true}
                              InputProps={{
                                startAdornment: (
                                  <InputAdornment position="start">
                                    <HomeIcon color="action" />
                                  </InputAdornment>
                                ),
                                readOnly: true,
                                sx: {
                                  '&.Mui-disabled': {
                                    color: 'text.primary', // Make the text more visible when disabled
                                    backgroundColor: 'rgba(0, 0, 0, 0.02)', // Light background to indicate it's disabled
                                  }
                                }
                              }}
                            />
                            <FormHelperText>
                              Auto-filled from parent tenant
                            </FormHelperText>
                          </FormControl>

                          <FormControl fullWidth>
                            <TextField
                              fullWidth
                              label="Kebele"
                              name="kebele_display"
                              value={(() => {
                                // Get the kebele name from various sources

                                // First try to get tenant information from localStorage
                                const tenantString = localStorage.getItem('tenant');
                                if (tenantString) {
                                  try {
                                    const localTenant = JSON.parse(tenantString);
                                    if (localTenant?.name) {
                                      return localTenant.name;
                                    }
                                  } catch (error) {
                                    console.error('Error parsing tenant from localStorage:', error);
                                  }
                                }

                                // Try to get kebele name from the schema_name
                                const schemaName = localStorage.getItem('schema_name');
                                if (schemaName && schemaName.startsWith('kebele')) {
                                  // Extract the kebele ID from the schema name (e.g., 'kebele14' -> '14')
                                  const extractedId = schemaName.replace('kebele', '');
                                  if (extractedId) {
                                    return `Kebele ${extractedId}`;
                                  }
                                }

                                // Fallback to tenant context
                                if (tenant?.name) {
                                  return tenant.name;
                                }

                                // Last resort fallback - use a hardcoded value
                                return 'Kebele 14';
                              })()}
                              disabled={true}
                              InputProps={{
                                startAdornment: (
                                  <InputAdornment position="start">
                                    <HomeIcon color="action" />
                                  </InputAdornment>
                                ),
                                readOnly: true,
                                sx: {
                                  '&.Mui-disabled': {
                                    color: 'text.primary', // Make the text more visible when disabled
                                    backgroundColor: 'rgba(0, 0, 0, 0.02)', // Light background to indicate it's disabled
                                  }
                                }
                              }}
                            />
                            <FormHelperText>
                              Auto-filled from current tenant
                            </FormHelperText>
                          </FormControl>

                          <FormControl fullWidth>
                            <InputLabel id="ketena-select-label">Ketena</InputLabel>
                            <Select
                              labelId="ketena-select-label"
                              id="ketena-select"
                              name="ketena"
                              value={formData.ketena || ''}
                              onChange={handleChange}
                              label="Ketena"
                              startAdornment={<InputAdornment position="start"><HomeIcon color="action" /></InputAdornment>}
                            >
                              {console.log('Rendering ketena dropdown with:', { kebele: formData.kebele, loading: loadingKetenas, ketenas }) ||
                                (loadingKetenas ? (
                                <MenuItem disabled>Loading ketenas...</MenuItem>
                              ) : ketenas.length === 0 ? (
                                // When no ketenas are available, show only 3 default options as an array
                                [
                                  <MenuItem key="1" value="1">Ketena 01</MenuItem>,
                                  <MenuItem key="2" value="2">Ketena 02</MenuItem>,
                                  <MenuItem key="3" value="3">Ketena 03</MenuItem>
                                ]
                              ) : (
                                // Add debugging to see what's being rendered
                                console.log('Mapping ketenas:', ketenas) ||
                                ketenas.map((ketena) => {
                                  console.log('Ketena item:', ketena);
                                  return (
                                    <MenuItem key={ketena.id} value={ketena.id}>
                                      {ketena.name}
                                    </MenuItem>
                                  );
                                })
                              ))}
                            </Select>
                            <FormHelperText>Select ketena</FormHelperText>
                          </FormControl>
                        </Box>

                        {/* Address Details */}
                        <Typography variant="subtitle2" color="text.secondary" sx={{ mt: 1 }}>
                          Address Details
                        </Typography>
                        <Box sx={{
                          display: 'grid',
                          gridTemplateColumns: '1fr 1fr',
                          gap: 2,
                          '& > *': { width: '100%' }
                        }}>
                          <TextField
                            fullWidth
                            label="House Number"
                            name="house_number"
                            value={formData.house_number}
                            onChange={handleChange}
                            helperText="Enter house number (optional)"
                            InputProps={{
                              startAdornment: (
                                <InputAdornment position="start">
                                  <HomeIcon color="action" />
                                </InputAdornment>
                              ),
                            }}
                          />
                          <TextField
                            required
                            fullWidth
                            label="Address"
                            name="address"
                            multiline
                            rows={1}
                            value={formData.address}
                            onChange={handleChange}
                            placeholder="Street address, city, postal code"
                            helperText="Enter residential address"
                            InputProps={{
                              startAdornment: (
                                <InputAdornment position="start">
                                  <HomeIcon color="action" />
                                </InputAdornment>
                              ),
                            }}
                          />
                        </Box>
                      </Box>
                    </CardContent>
                  </Card>
                </Box>
              )}

              {/* Step 3: Additional Information */}
              {activeStep === 2 && (
                <Box sx={{
                  display: 'flex',
                  flexDirection: 'column',
                  width: '100%',
                  flexGrow: 1,
                  mb: 4
                }}>
                  <Card sx={{
                    mb: 4,
                    height: '100%',
                    width: '100%',
                    display: 'flex',
                    flexDirection: 'column',
                    transition: 'all 0.3s ease',
                    '&:hover': {
                      transform: 'translateY(-5px)',
                      boxShadow: '0 12px 20px rgba(0, 0, 0, 0.1)',
                    },
                    borderRadius: 2,
                    overflow: 'hidden',
                    border: 'none',
                    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.08)',
                    position: 'relative'
                  }}>
                    <Box sx={{
                      p: 2,
                      bgcolor: 'info.main',
                      color: 'white',
                      display: 'flex',
                      alignItems: 'center'
                    }}>
                      <Box
                        sx={{
                          mr: 1,
                        }}
                      >
                        <FlagIcon fontSize="medium" />
                      </Box>
                      <Typography variant="h6" sx={{ fontWeight: 600 }}>
                        Additional Information
                      </Typography>
                    </Box>
                    <CardContent sx={{ p: 2, flexGrow: 1 }}>
                      <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                        Enter additional information about the citizen including nationality, country of origin, and employment details.
                      </Typography>
                      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                        {/* Nationality Information */}
                        <Typography variant="subtitle2" color="text.secondary" sx={{ mt: 1 }}>
                          Nationality Information
                        </Typography>
                        <Box sx={{
                          display: 'grid',
                          gridTemplateColumns: '1fr 1fr 1fr',
                          gap: 2,
                          '& > *': { width: '100%' }
                        }}>
                          <TextField
                            fullWidth
                            label="Nationality"
                            name="nationality"
                            value={formData.nationality}
                            onChange={handleChange}
                            helperText="Enter nationality"
                            InputProps={{
                              startAdornment: (
                                <InputAdornment position="start">
                                  <FlagIcon color="action" />
                                </InputAdornment>
                              ),
                            }}
                          />
                          <FormControl fullWidth>
                            <InputLabel id="country-select-label">Country</InputLabel>
                            <Select
                              labelId="country-select-label"
                              id="country-select"
                              name="nationality_country"
                              value={formData.nationality_country}
                              onChange={handleChange}
                              label="Country"
                              startAdornment={<InputAdornment position="start"><FlagIcon color="action" /></InputAdornment>}
                            >
                              {loadingCountries ? (
                                <MenuItem disabled>Loading countries...</MenuItem>
                              ) : countries.length === 0 ? (
                                // Fallback options if no countries are available
                                [
                                  <MenuItem key="1" value="1">Ethiopia</MenuItem>,
                                  <MenuItem key="2" value="2">Kenya</MenuItem>,
                                  <MenuItem key="3" value="3">Sudan</MenuItem>,
                                  <MenuItem key="4" value="4">Djibouti</MenuItem>,
                                  <MenuItem key="5" value="5">Somalia</MenuItem>,
                                  <MenuItem key="6" value="6">Eritrea</MenuItem>,
                                  <MenuItem key="7" value="7">Egypt</MenuItem>,
                                  <MenuItem key="8" value="8">United States</MenuItem>,
                                  <MenuItem key="9" value="9">United Kingdom</MenuItem>,
                                  <MenuItem key="10" value="10">Canada</MenuItem>
                                ]
                              ) : (
                                countries.map((country) => (
                                  <MenuItem key={country.id} value={country.id}>
                                    {country.name}
                                  </MenuItem>
                                ))
                              )}
                            </Select>
                            <FormHelperText>Select country of nationality</FormHelperText>
                          </FormControl>
                          <FormControl fullWidth disabled={!formData.nationality_country || loadingRegions}>
                            <InputLabel id="region-select-label">Region</InputLabel>
                            <Select
                              labelId="region-select-label"
                              id="region-select"
                              name="region"
                              value={formData.region}
                              onChange={handleChange}
                              label="Region"
                              startAdornment={<InputAdornment position="start"><FlagIcon color="action" /></InputAdornment>}
                              sx={{
                                '&.Mui-disabled': {
                                  color: 'text.primary', // Make the text more visible when disabled
                                  '& .MuiSelect-icon': {
                                    color: 'action.active', // Make the dropdown icon more visible
                                  },
                                  backgroundColor: 'rgba(0, 0, 0, 0.02)', // Light background to indicate it's disabled
                                }
                              }}
                            >
                              {!formData.nationality_country ? (
                                <MenuItem disabled>Select a country first</MenuItem>
                              ) : loadingRegions ? (
                                <MenuItem disabled>Loading regions...</MenuItem>
                              ) : regions.length === 0 ? (
                                <MenuItem disabled>No regions available</MenuItem>
                              ) : (
                                regions.map((region) => (
                                  <MenuItem key={region.id} value={region.id}>
                                    {region.name}
                                  </MenuItem>
                                ))
                              )}
                            </Select>
                            <FormHelperText>Select region</FormHelperText>
                          </FormControl>
                        </Box>

                        {/* Employment Information */}
                        <Typography variant="subtitle2" color="text.secondary" sx={{ mt: 1 }}>
                          Employment Information
                        </Typography>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                          <FormControlLabel
                            control={
                              <Switch
                                checked={formData.employment}
                                onChange={(e) => setFormData(prev => ({ ...prev, employment: e.target.checked }))}
                                color="primary"
                              />
                            }
                            label="Currently Employed"
                          />
                        </Box>

                        {formData.employment && (
                          <Box sx={{
                            display: 'grid',
                            gridTemplateColumns: '1fr 1fr',
                            gap: 2,
                            '& > *': { width: '100%' }
                          }}>
                            <FormControl fullWidth>
                              <InputLabel id="employee-type-select-label">Employee Type</InputLabel>
                              <Select
                                labelId="employee-type-select-label"
                                id="employee-type-select"
                                name="employee_type"
                                value={formData.employee_type}
                                onChange={handleChange}
                                label="Employee Type"
                                startAdornment={<InputAdornment position="start"><WorkIcon color="action" /></InputAdornment>}
                              >
                                {loadingEmployeeTypes ? (
                                  <MenuItem disabled>Loading employee types...</MenuItem>
                                ) : employeeTypes.length === 0 ? (
                                  // Fallback options if no employee types are available
                                  <>
                                    <MenuItem value="1">Government Employee</MenuItem>
                                    <MenuItem value="2">Private Sector Employee</MenuItem>
                                    <MenuItem value="3">Self-Employed</MenuItem>
                                    <MenuItem value="4">Business Owner</MenuItem>
                                    <MenuItem value="5">Contractor</MenuItem>
                                    <MenuItem value="6">Consultant</MenuItem>
                                    <MenuItem value="7">Freelancer</MenuItem>
                                    <MenuItem value="8">Other</MenuItem>
                                  </>
                                ) : (
                                  employeeTypes.map((type) => (
                                    <MenuItem key={type.id} value={type.id}>
                                      {type.name}
                                    </MenuItem>
                                  ))
                                )}
                              </Select>
                              <FormHelperText>Select employee type</FormHelperText>
                            </FormControl>
                            <FormControl fullWidth>
                              <InputLabel id="employment-type-select-label">Employment Type</InputLabel>
                              <Select
                                labelId="employment-type-select-label"
                                id="employment-type-select"
                                name="employment_type"
                                value={formData.employment_type}
                                onChange={handleChange}
                                label="Employment Type"
                                startAdornment={<InputAdornment position="start"><WorkIcon color="action" /></InputAdornment>}
                              >
                                {loadingEmploymentTypes ? (
                                  <MenuItem disabled>Loading employment types...</MenuItem>
                                ) : employmentTypes.length === 0 ? (
                                  // Fallback options if no employment types are available
                                  <>
                                    <MenuItem value="1">Full-time</MenuItem>
                                    <MenuItem value="2">Part-time</MenuItem>
                                    <MenuItem value="3">Contract</MenuItem>
                                    <MenuItem value="4">Temporary</MenuItem>
                                    <MenuItem value="5">Seasonal</MenuItem>
                                    <MenuItem value="6">Internship</MenuItem>
                                    <MenuItem value="7">Apprenticeship</MenuItem>
                                    <MenuItem value="8">Other</MenuItem>
                                  </>
                                ) : (
                                  employmentTypes.map((type) => (
                                    <MenuItem key={type.id} value={type.id}>
                                      {type.name}
                                    </MenuItem>
                                  ))
                                )}
                              </Select>
                              <FormHelperText>Select employment type</FormHelperText>
                            </FormControl>
                            <TextField
                              fullWidth
                              label="Organization Name"
                              name="organization_name"
                              value={formData.organization_name}
                              onChange={handleChange}
                              helperText="Enter organization name"
                              InputProps={{
                                startAdornment: (
                                  <InputAdornment position="start">
                                    <WorkIcon color="action" />
                                  </InputAdornment>
                                ),
                              }}
                            />
                            <TextField
                              fullWidth
                              label="Occupation"
                              name="occupation"
                              value={formData.occupation}
                              onChange={handleChange}
                              helperText="Enter current occupation"
                              InputProps={{
                                startAdornment: (
                                  <InputAdornment position="start">
                                    <WorkIcon color="action" />
                                  </InputAdornment>
                                ),
                              }}
                            />
                          </Box>
                        )}
                      </Box>
                    </CardContent>
                  </Card>
                </Box>
              )}

              {/* Step 3: Family Information */}
              {activeStep === 3 && (
                <Box sx={{
                  display: 'flex',
                  flexDirection: 'column',
                  width: '100%',
                  flexGrow: 1,
                  mb: 4
                }}>
                  <Card sx={{
                    mb: 4,
                    height: '100%',
                    width: '100%',
                    display: 'flex',
                    flexDirection: 'column',
                    transition: 'all 0.3s ease',
                    '&:hover': {
                      transform: 'translateY(-5px)',
                      boxShadow: '0 12px 20px rgba(0, 0, 0, 0.1)',
                    },
                    borderRadius: 2,
                    overflow: 'hidden',
                    border: 'none',
                    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.08)',
                    position: 'relative'
                  }}>
                    <Box sx={{
                      p: 2,
                      bgcolor: 'warning.main',
                      color: 'white',
                      display: 'flex',
                      alignItems: 'center'
                    }}>
                      <Box
                        sx={{
                          mr: 1,
                        }}
                      >
                        <FamilyRestroomIcon fontSize="medium" />
                      </Box>
                      <Typography variant="h6" sx={{ fontWeight: 600 }}>
                        Family Information
                      </Typography>
                    </Box>
                    <CardContent sx={{ p: 2, flexGrow: 1 }}>
                      <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                        Enter information about the citizen's family members including spouse and children if applicable.
                      </Typography>
                      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                        {/* Spouse Information - Only show if not single */}
                        {formData.marital_status && formData.marital_status !== 'SINGLE' && (
                          <>
                            <Typography variant="subtitle2" color="text.secondary" sx={{ mt: 1 }}>
                              Spouse Information
                            </Typography>

                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                              <FormControlLabel
                                control={
                                  <Switch
                                    checked={formData.spouse_is_resident}
                                    onChange={(e) => {
                                      const isChecked = e.target.checked;
                                      console.log('Spouse is resident toggle changed to:', isChecked);

                                      setFormData(prev => ({
                                        ...prev,
                                        spouse_is_resident: isChecked,
                                        // Clear the linked citizen if toggling off
                                        spouse_linked_citizen: isChecked ? prev.spouse_linked_citizen : ''
                                      }));

                                      // Clear any existing search results when toggling
                                      setSpouseSearchResults([]);
                                      setSpouseSearchQuery('');
                                    }}
                                    color="primary"
                                  />
                                }
                                label="Spouse is a resident"
                              />
                            </Box>

                            {formData.spouse_is_resident ? (
                              <FormControl fullWidth>
                                <Autocomplete
                                  id="spouse-citizen-select"
                                  options={spouseSearchResults}
                                  getOptionLabel={(option) => {
                                    if (!option) return '';
                                    // Display the ID number that will be printed on the ID card
                                    const idDisplay = option.id_number ? option.id_number :
                                                     (option.registration_number ? option.registration_number : 'No ID');
                                    return `${option.first_name} ${option.middle_name ? option.middle_name + ' ' : ''}${option.last_name} (${idDisplay})`;
                                  }}
                                  loading={searchingSpouse}
                                  loadingText="Searching for potential spouse..."
                                  value={citizens.find(c => c.id === formData.spouse_linked_citizen) || null}
                                  onChange={(event, newValue) => {
                                    console.log('Spouse citizen selected:', newValue);
                                    setFormData(prev => ({
                                      ...prev,
                                      spouse_linked_citizen: newValue ? newValue.id : ''
                                    }));
                                  }}
                                  onInputChange={(event, newInputValue) => {
                                    console.log('Spouse search input changed to:', newInputValue);
                                    setSpouseSearchQuery(newInputValue);

                                    // Clear any existing timeout
                                    if (window.spouseSearchTimeout) {
                                      clearTimeout(window.spouseSearchTimeout);
                                      console.log('Cleared previous spouse search timeout');
                                    }

                                    // Only search if query is at least 2 characters
                                    if (newInputValue.length >= 2) {
                                      setSearchingSpouse(true);
                                      console.log('Setting spouse search loading state to true');

                                      // Debounce the search
                                      window.spouseSearchTimeout = setTimeout(() => {
                                        console.log('Spouse search timeout expired, searching for:', newInputValue);
                                        searchSpouseCitizens(newInputValue);
                                      }, 500);
                                    } else {
                                      // Don't clear results if the input is empty
                                      if (newInputValue.length > 0) {
                                        console.log('Spouse search query too short, clearing results');
                                        setSpouseSearchResults([]);
                                      } else {
                                        console.log('Spouse search input empty, keeping existing results');
                                      }
                                      setSearchingSpouse(false);
                                    }
                                  }}
                                  filterOptions={(x) => x} // No client-side filtering, we do it in the search function
                                  renderInput={(params) => (
                                    <TextField
                                      {...params}
                                      label="Search for Spouse"
                                      placeholder="Type to search"
                                      InputProps={{
                                        ...params.InputProps,
                                        startAdornment: (
                                          <>
                                            <InputAdornment position="start">
                                              <SearchIcon color="action" />
                                            </InputAdornment>
                                            {params.InputProps.startAdornment}
                                          </>
                                        ),
                                        endAdornment: (
                                          <>
                                            {searchingSpouse ? <CircularProgress color="inherit" size={20} /> : null}
                                            {params.InputProps.endAdornment}
                                          </>
                                        )
                                      }}
                                    />
                                  )}
                                  renderOption={(props, option) => {
                                    // Display the ID number that will be printed on the ID card
                                    const idDisplay = option.id_number ? option.id_number :
                                                     (option.registration_number ? option.registration_number : 'No ID');
                                    return (
                                      <li {...props}>
                                        {option.first_name} {option.middle_name ? option.middle_name + ' ' : ''}{option.last_name} ({idDisplay})
                                      </li>
                                    );
                                  }}
                                  noOptionsText={
                                    spouseSearchQuery.length < 2
                                      ? "Type at least 2 characters to search for potential spouse"
                                      : searchingSpouse
                                        ? "Searching..."
                                        : citizens.length === 0
                                          ? "Loading citizens..."
                                          : formData.gender === 'M' && citizens.filter(citizen => citizen.gender === 'F').length === 0
                                            ? "No female citizens found. Please add a female citizen first."
                                            : formData.gender === 'F' && citizens.filter(citizen => citizen.gender === 'M').length === 0
                                              ? "No male citizens found. Please add a male citizen first."
                                              : "No matching citizens found. Try a different search term."
                                  }
                                  ListboxProps={{
                                    style: { maxHeight: '200px' }
                                  }}
                                />
                                <FormHelperText>
                                  {formData.gender === 'M'
                                    ? "Search and select a female citizen as spouse"
                                    : formData.gender === 'F'
                                      ? "Search and select a male citizen as spouse"
                                      : "Search and select a citizen as spouse"}
                                </FormHelperText>
                              </FormControl>
                            ) : (
                              <>
                                <Box sx={{
                                  display: 'grid',
                                  gridTemplateColumns: '1fr 1fr 1fr',
                                  gap: 2,
                                  '& > *': { width: '100%' }
                                }}>
                                  <TextField
                                    fullWidth
                                    label="First Name"
                                    name="spouse_first_name"
                                    value={formData.spouse_first_name}
                                    onChange={handleChange}
                                    helperText="Enter spouse's first name"
                                    InputProps={{
                                      startAdornment: (
                                        <InputAdornment position="start">
                                          <PersonIcon color="action" />
                                        </InputAdornment>
                                      ),
                                    }}
                                  />
                                  <TextField
                                    fullWidth
                                    label="Middle Name"
                                    name="spouse_middle_name"
                                    value={formData.spouse_middle_name}
                                    onChange={handleChange}
                                    helperText="Optional"
                                    InputProps={{
                                      startAdornment: (
                                        <InputAdornment position="start">
                                          <PersonIcon color="action" />
                                        </InputAdornment>
                                      ),
                                    }}
                                  />
                                  <TextField
                                    fullWidth
                                    label="Last Name"
                                    name="spouse_last_name"
                                    value={formData.spouse_last_name}
                                    onChange={handleChange}
                                    helperText="Enter spouse's last name"
                                    InputProps={{
                                      startAdornment: (
                                        <InputAdornment position="start">
                                          <PersonIcon color="action" />
                                        </InputAdornment>
                                      ),
                                    }}
                                  />
                                </Box>

                                <Box sx={{
                                  display: 'grid',
                                  gridTemplateColumns: '1fr 1fr',
                                  gap: 2,
                                  '& > *': { width: '100%' }
                                }}>
                                  <TextField
                                    fullWidth
                                    label="Phone Number"
                                    name="spouse_phone"
                                    value={formData.spouse_phone}
                                    onChange={handleChange}
                                    helperText="Enter spouse's phone number"
                                    InputProps={{
                                      startAdornment: (
                                        <InputAdornment position="start">
                                          <PhoneIcon color="action" />
                                        </InputAdornment>
                                      ),
                                    }}
                                  />
                                  <TextField
                                    fullWidth
                                    label="Email"
                                    name="spouse_email"
                                    type="email"
                                    value={formData.spouse_email}
                                    onChange={handleChange}
                                    helperText="Enter spouse's email (optional)"
                                    InputProps={{
                                      startAdornment: (
                                        <InputAdornment position="start">
                                          <EmailIcon color="action" />
                                        </InputAdornment>
                                      ),
                                    }}
                                  />
                                </Box>
                              </>
                            )}

                            <Divider sx={{ my: 2 }} />
                          </>
                        )}

                        {/* Children Information */}
                        <Typography variant="subtitle2" color="text.secondary">
                          Children Information
                        </Typography>

                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                          <FormControlLabel
                            control={
                              <Switch
                                checked={formData.has_children}
                                onChange={(e) => setFormData(prev => ({ ...prev, has_children: e.target.checked }))}
                                color="primary"
                              />
                            }
                            label="Has Children"
                          />
                        </Box>

                        {formData.has_children && (
                          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                            {/* Children List */}
                            {formData.children && formData.children.length > 0 ? (
                              <Box sx={{ width: '100%' }}>
                                <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                                  Added Children
                                </Typography>
                                <Paper variant="outlined" sx={{ mb: 2 }}>
                                  <List sx={{ width: '100%', bgcolor: 'background.paper' }}>
                                    {formData.children.map((child, index) => (
                                      <React.Fragment key={index}>
                                        <ListItem
                                          secondaryAction={
                                            <IconButton edge="end" aria-label="delete" onClick={() => {
                                              const updatedChildren = [...formData.children];
                                              updatedChildren.splice(index, 1);
                                              setFormData(prev => ({ ...prev, children: updatedChildren }));
                                            }}>
                                              <DeleteIcon />
                                            </IconButton>
                                          }
                                        >
                                          <ListItemAvatar>
                                            <Avatar>
                                              <ChildCareIcon />
                                            </Avatar>
                                          </ListItemAvatar>
                                          <ListItemText
                                            primary={`${child.first_name} ${child.middle_name ? child.middle_name + ' ' : ''}${child.last_name}`}
                                            secondary={
                                              <>
                                                <Typography component="span" variant="body2" color="text.primary">
                                                  {child.gender === 'MALE' ? 'Male' : child.gender === 'FEMALE' ? 'Female' : 'Other'}
                                                </Typography>
                                                {child.date_of_birth && (
                                                  <> — Born: {new Date(child.date_of_birth).toLocaleDateString()}</>
                                                )}
                                              </>
                                            }
                                          />
                                        </ListItem>
                                        {index < formData.children.length - 1 && <Divider variant="inset" component="li" />}
                                      </React.Fragment>
                                    ))}
                                  </List>
                                </Paper>
                              </Box>
                            ) : (
                              <Alert severity="info" sx={{ mb: 2 }}>
                                No children added yet. Use the form below to add children.
                              </Alert>
                            )}

                            {/* Add Child Form */}
                            <Paper variant="outlined" sx={{ p: 2, mb: 2 }}>
                              <Typography variant="subtitle2" color="primary" gutterBottom>
                                Add a Child
                              </Typography>
                              <Box sx={{
                                display: 'grid',
                                gridTemplateColumns: '1fr 1fr 1fr',
                                gap: 2,
                                mb: 2,
                                '& > *': { width: '100%' }
                              }}>
                                <TextField
                                  label="First Name"
                                  value={childForm.first_name}
                                  onChange={(e) => setChildForm({ ...childForm, first_name: e.target.value })}
                                  size="small"
                                  InputProps={{
                                    startAdornment: (
                                      <InputAdornment position="start">
                                        <PersonIcon color="action" fontSize="small" />
                                      </InputAdornment>
                                    ),
                                  }}
                                />
                                <TextField
                                  label="Middle Name"
                                  value={childForm.middle_name}
                                  onChange={(e) => setChildForm({ ...childForm, middle_name: e.target.value })}
                                  size="small"
                                  InputProps={{
                                    startAdornment: (
                                      <InputAdornment position="start">
                                        <PersonIcon color="action" fontSize="small" />
                                      </InputAdornment>
                                    ),
                                  }}
                                />
                                <TextField
                                  label="Last Name"
                                  value={childForm.last_name}
                                  onChange={(e) => setChildForm({ ...childForm, last_name: e.target.value })}
                                  size="small"
                                  InputProps={{
                                    startAdornment: (
                                      <InputAdornment position="start">
                                        <PersonIcon color="action" fontSize="small" />
                                      </InputAdornment>
                                    ),
                                  }}
                                />
                              </Box>
                              <Box sx={{
                                display: 'grid',
                                gridTemplateColumns: '1fr 1fr',
                                gap: 2,
                                mb: 2,
                                '& > *': { width: '100%' }
                              }}>
                                <FormControl size="small">
                                  <InputLabel id="child-gender-label">Gender</InputLabel>
                                  <Select
                                    labelId="child-gender-label"
                                    value={childForm.gender}
                                    onChange={(e) => setChildForm({ ...childForm, gender: e.target.value })}
                                    label="Gender"
                                    startAdornment={<InputAdornment position="start"><WcIcon color="action" fontSize="small" /></InputAdornment>}
                                  >
                                    <MenuItem value="MALE">Male</MenuItem>
                                    <MenuItem value="FEMALE">Female</MenuItem>
                                  </Select>
                                </FormControl>
                                <LocalizationProvider dateAdapter={AdapterDateFns}>
                                  <DatePicker
                                    label="Date of Birth"
                                    value={childForm.date_of_birth}
                                    onChange={(date) => setChildForm({ ...childForm, date_of_birth: date })}
                                    slotProps={{
                                      textField: {
                                        size: 'small',
                                        InputProps: {
                                          startAdornment: (
                                            <InputAdornment position="start">
                                              <CalendarMonthIcon color="action" fontSize="small" />
                                            </InputAdornment>
                                          ),
                                        }
                                      }
                                    }}
                                  />
                                </LocalizationProvider>
                              </Box>
                              <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 1 }}>
                                <Button
                                  variant="outlined"
                                  size="small"
                                  onClick={() => setChildForm({
                                    first_name: '',
                                    middle_name: '',
                                    last_name: '',
                                    gender: '',
                                    date_of_birth: null
                                  })}
                                >
                                  Clear
                                </Button>
                                <Button
                                  variant="contained"
                                  size="small"
                                  startIcon={<AddIcon />}
                                  onClick={() => {
                                    if (childForm.first_name && childForm.last_name && childForm.gender) {
                                      // Format the date to YYYY-MM-DD string format if it exists
                                      const formattedDate = childForm.date_of_birth instanceof Date
                                        ? childForm.date_of_birth.toISOString().split('T')[0]
                                        : childForm.date_of_birth;

                                      const newChild = {
                                        ...childForm,
                                        date_of_birth: formattedDate
                                      };
                                      const updatedChildren = formData.children ? [...formData.children, newChild] : [newChild];
                                      setFormData(prev => ({ ...prev, children: updatedChildren }));
                                      setChildForm({
                                        first_name: '',
                                        middle_name: '',
                                        last_name: '',
                                        gender: '',
                                        date_of_birth: null
                                      });
                                    } else {
                                      // Show error or validation message
                                      alert('Please fill in at least first name, last name, and gender');
                                    }
                                  }}
                                  disabled={!childForm.first_name || !childForm.last_name || !childForm.gender}
                                >
                                  Add Child
                                </Button>
                              </Box>
                            </Paper>
                          </Box>
                        )}

                        <Divider sx={{ my: 2 }} />

                        {/* Parent Information */}
                        <Typography variant="subtitle2" color="text.secondary">
                          Parent Information
                        </Typography>

                        {/* Mother Information */}
                        <Typography variant="subtitle2" color="text.secondary" sx={{ ml: 2, color: 'text.secondary' }}>
                          Mother Information
                        </Typography>

                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                          <FormControlLabel
                            control={
                              <Switch
                                checked={formData.mother_is_resident}
                                onChange={(e) => setFormData(prev => ({ ...prev, mother_is_resident: e.target.checked }))}
                                color="primary"
                              />
                            }
                            label="Mother is a resident"
                          />
                        </Box>

                        {citizensError && (
                          <Alert severity="error" sx={{ mb: 2 }}>
                            {citizensError}
                          </Alert>
                        )}

                        {formData.mother_is_resident ? (
                          <FormControl fullWidth>
                            <Autocomplete
                              id="mother-citizen-select"
                              options={motherSearchResults}
                              getOptionLabel={(option) => {
                                if (!option) return '';
                                // Display the ID number that will be printed on the ID card
                                const idDisplay = option.id_number ? option.id_number :
                                                 (option.registration_number ? option.registration_number : 'No ID');
                                return `${option.first_name} ${option.middle_name ? option.middle_name + ' ' : ''}${option.last_name} (${idDisplay})`;
                              }}
                              loading={searchingMother}
                              value={citizens.find(c => c.id === formData.mother_linked_citizen) || null}
                              onChange={(event, newValue) => {
                                setFormData(prev => ({
                                  ...prev,
                                  mother_linked_citizen: newValue ? newValue.id : ''
                                }));
                              }}
                              onInputChange={(event, newInputValue) => {
                                setMotherSearchQuery(newInputValue);

                                // Clear any existing timeout
                                if (window.motherSearchTimeout) {
                                  clearTimeout(window.motherSearchTimeout);
                                }

                                // Only search if query is at least 2 characters
                                if (newInputValue.length >= 2) {
                                  setSearchingMother(true);

                                  // Debounce the search
                                  window.motherSearchTimeout = setTimeout(() => {
                                    searchMotherCitizens(newInputValue);
                                  }, 300);
                                } else {
                                  setMotherSearchResults([]);
                                  setSearchingMother(false);
                                }
                              }}
                              filterOptions={(x) => x} // No client-side filtering, we do it in the search function
                              renderInput={(params) => (
                                <TextField
                                  {...params}
                                  label="Search Female Citizens"
                                  placeholder="Type to search"
                                  InputProps={{
                                    ...params.InputProps,
                                    startAdornment: (
                                      <>
                                        <InputAdornment position="start">
                                          <SearchIcon color="action" />
                                        </InputAdornment>
                                        {params.InputProps.startAdornment}
                                      </>
                                    ),
                                    endAdornment: (
                                      <>
                                        {loadingCitizens ? <CircularProgress color="inherit" size={20} /> : null}
                                        {params.InputProps.endAdornment}
                                      </>
                                    )
                                  }}
                                />
                              )}
                              renderOption={(props, option) => {
                                // Display the ID number that will be printed on the ID card
                                const idDisplay = option.id_number ? option.id_number :
                                                 (option.registration_number ? option.registration_number : 'No ID');
                                return (
                                  <li {...props}>
                                    {option.first_name} {option.middle_name ? option.middle_name + ' ' : ''}{option.last_name} ({idDisplay})
                                  </li>
                                );
                              }}
                              noOptionsText={
                                motherSearchQuery.length < 2
                                  ? "Type at least 2 characters to search"
                                  : searchingMother
                                    ? "Searching..."
                                    : citizens.filter(citizen => citizen.gender === 'F').length === 0
                                      ? "No female citizens found. Please add citizens first."
                                      : "No matching female citizens found. Try a different search term."
                              }
                              ListboxProps={{
                                style: { maxHeight: '200px' }
                              }}
                            />
                            <FormHelperText>
                              Search and select a female citizen as mother
                            </FormHelperText>
                          </FormControl>
                        ) : (
                          <Box sx={{
                            display: 'grid',
                            gridTemplateColumns: '1fr 1fr 1fr',
                            gap: 3,
                            '& > *': { width: '100%' }
                          }}>
                            <TextField
                              fullWidth
                              label="First Name"
                              name="mother_first_name"
                              value={formData.mother_first_name}
                              onChange={handleChange}
                              helperText="Enter mother's first name"
                              InputProps={{
                                startAdornment: (
                                  <InputAdornment position="start">
                                    <PersonIcon color="action" />
                                  </InputAdornment>
                                ),
                              }}
                            />
                            <TextField
                              fullWidth
                              label="Middle Name"
                              name="mother_middle_name"
                              value={formData.mother_middle_name}
                              onChange={handleChange}
                              helperText="Optional"
                              InputProps={{
                                startAdornment: (
                                  <InputAdornment position="start">
                                    <PersonIcon color="action" />
                                  </InputAdornment>
                                ),
                              }}
                            />
                            <TextField
                              fullWidth
                              label="Last Name"
                              name="mother_last_name"
                              value={formData.mother_last_name}
                              onChange={handleChange}
                              helperText="Enter mother's last name"
                              InputProps={{
                                startAdornment: (
                                  <InputAdornment position="start">
                                    <PersonIcon color="action" />
                                  </InputAdornment>
                                ),
                              }}
                            />
                          </Box>
                        )}

                        <Divider sx={{ my: 2 }} />

                        {/* Father Information */}
                        <Typography variant="subtitle2" color="text.secondary" sx={{ ml: 2, color: 'text.secondary' }}>
                          Father Information
                        </Typography>

                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                          <FormControlLabel
                            control={
                              <Switch
                                checked={formData.father_is_resident}
                                onChange={(e) => {
                                  const isChecked = e.target.checked;
                                  console.log('Father is resident toggle changed to:', isChecked);

                                  setFormData(prev => ({
                                    ...prev,
                                    father_is_resident: isChecked,
                                    // Clear the linked citizen if toggling off
                                    father_linked_citizen: isChecked ? prev.father_linked_citizen : ''
                                  }));

                                  // Clear any existing search results when toggling
                                  setFatherSearchResults([]);
                                  setFatherSearchQuery('');
                                }}
                                color="primary"
                              />
                            }
                            label="Father is a resident"
                          />
                        </Box>

                        {formData.father_is_resident ? (
                          <FormControl fullWidth>
                            <Autocomplete
                              id="father-citizen-select"
                              options={fatherSearchResults}
                              getOptionLabel={(option) => {
                                if (!option) return '';
                                // Display the ID number that will be printed on the ID card
                                const idDisplay = option.id_number ? option.id_number :
                                                 (option.registration_number ? option.registration_number : 'No ID');
                                return `${option.first_name} ${option.middle_name ? option.middle_name + ' ' : ''}${option.last_name} (${idDisplay})`;
                              }}
                              loading={searchingFather}
                              loadingText="Searching for male citizens..."
                              value={citizens.find(c => c.id === formData.father_linked_citizen) || null}
                              onChange={(event, newValue) => {
                                console.log('Father citizen selected:', newValue);
                                setFormData(prev => ({
                                  ...prev,
                                  father_linked_citizen: newValue ? newValue.id : ''
                                }));
                              }}
                              blurOnSelect={false} // Prevent closing on select
                              disableCloseOnSelect={false} // Allow closing on select
                              onInputChange={(event, newInputValue) => {
                                console.log('Father search input changed to:', newInputValue);
                                setFatherSearchQuery(newInputValue);

                                // Clear any existing timeout
                                if (window.fatherSearchTimeout) {
                                  clearTimeout(window.fatherSearchTimeout);
                                  console.log('Cleared previous father search timeout');
                                }

                                // Only search if query is at least 2 characters
                                if (newInputValue.length >= 2) {
                                  setSearchingFather(true);
                                  console.log('Setting father search loading state to true');

                                  // Debounce the search
                                  window.fatherSearchTimeout = setTimeout(() => {
                                    console.log('Father search timeout expired, searching for:', newInputValue);
                                    searchFatherCitizens(newInputValue);
                                  }, 500); // Increased timeout for stability
                                } else {
                                  // Don't clear results if the input is empty - this allows selection after clearing
                                  if (newInputValue.length > 0) {
                                    console.log('Father search query too short, clearing results');
                                    setFatherSearchResults([]);
                                  } else {
                                    console.log('Father search input empty, keeping existing results');
                                  }
                                  setSearchingFather(false);
                                }
                              }}
                              // Only show dropdown when searching or when there are results and the input is focused
                              filterOptions={(x) => x} // No client-side filtering, we do it in the search function
                              renderInput={(params) => (
                                <TextField
                                  {...params}
                                  label="Search Male Citizens"
                                  placeholder="Type to search"
                                  InputProps={{
                                    ...params.InputProps,
                                    startAdornment: (
                                      <>
                                        <InputAdornment position="start">
                                          <SearchIcon color="action" />
                                        </InputAdornment>
                                        {params.InputProps.startAdornment}
                                      </>
                                    ),
                                    endAdornment: (
                                      <>
                                        {searchingFather ? <CircularProgress color="inherit" size={20} /> : null}
                                        {params.InputProps.endAdornment}
                                      </>
                                    )
                                  }}
                                />
                              )}
                              renderOption={(props, option) => {
                                // Display the ID number that will be printed on the ID card
                                const idDisplay = option.id_number ? option.id_number :
                                                 (option.registration_number ? option.registration_number : 'No ID');
                                return (
                                  <li {...props}>
                                    {option.first_name} {option.middle_name ? option.middle_name + ' ' : ''}{option.last_name} ({idDisplay})
                                  </li>
                                );
                              }}
                              noOptionsText={
                                fatherSearchQuery.length < 2
                                  ? "Type at least 2 characters to search for male citizens"
                                  : searchingFather
                                    ? "Searching for male citizens..."
                                    : citizens.length === 0
                                      ? "Loading citizens..."
                                      : citizens.filter(citizen => citizen.gender === 'M').length === 0
                                        ? "No male citizens found. Please add a male citizen first."
                                        : "No matching male citizens found. Try a different search term."
                              }
                              ListboxProps={{
                                style: { maxHeight: '200px' }
                              }}
                            />
                            <FormHelperText>Search and select a male citizen as father</FormHelperText>
                          </FormControl>
                        ) : (
                          <Box sx={{
                            display: 'grid',
                            gridTemplateColumns: '1fr 1fr 1fr',
                            gap: 3,
                            '& > *': { width: '100%' }
                          }}>
                            <TextField
                              fullWidth
                              label="First Name"
                              name="father_first_name"
                              value={formData.father_first_name}
                              onChange={handleChange}
                              helperText="Enter father's first name"
                              InputProps={{
                                startAdornment: (
                                  <InputAdornment position="start">
                                    <PersonIcon color="action" />
                                  </InputAdornment>
                                ),
                              }}
                            />
                            <TextField
                              fullWidth
                              label="Middle Name"
                              name="father_middle_name"
                              value={formData.father_middle_name}
                              onChange={handleChange}
                              helperText="Optional"
                              InputProps={{
                                startAdornment: (
                                  <InputAdornment position="start">
                                    <PersonIcon color="action" />
                                  </InputAdornment>
                                ),
                              }}
                            />
                            <TextField
                              fullWidth
                              label="Last Name"
                              name="father_last_name"
                              value={formData.father_last_name}
                              onChange={handleChange}
                              helperText="Enter father's last name"
                              InputProps={{
                                startAdornment: (
                                  <InputAdornment position="start">
                                    <PersonIcon color="action" />
                                  </InputAdornment>
                                ),
                              }}
                            />
                          </Box>
                        )}
                      </Box>
                    </CardContent>
                  </Card>
                </Box>
              )}

              {/* Step 4: Emergency Contact */}
              {activeStep === 4 && (
                <Box sx={{
                  display: 'flex',
                  flexDirection: 'column',
                  width: '100%',
                  flexGrow: 1,
                  mb: 4
                }}>
                  <Card sx={{
                    mb: 4,
                    height: '100%',
                    width: '100%',
                    display: 'flex',
                    flexDirection: 'column',
                    transition: 'all 0.3s ease',
                    '&:hover': {
                      transform: 'translateY(-5px)',
                      boxShadow: '0 12px 20px rgba(0, 0, 0, 0.1)',
                    },
                    borderRadius: 2,
                    overflow: 'hidden',
                    border: 'none',
                    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.08)',
                    position: 'relative'
                  }}>
                    <Box sx={{
                      p: 2,
                      bgcolor: 'error.main',
                      color: 'white',
                      display: 'flex',
                      alignItems: 'center'
                    }}>
                      <Box
                        sx={{
                          mr: 1,
                        }}
                      >
                        <ContactEmergencyIcon fontSize="medium" />
                      </Box>
                      <Typography variant="h6" sx={{ fontWeight: 600 }}>
                        Emergency Contact
                      </Typography>
                    </Box>
                    <CardContent sx={{ p: 2, flexGrow: 1 }}>
                      <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                        Provide emergency contact information for this citizen. This person will be contacted in case of emergency.
                      </Typography>
                      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                        <Typography variant="subtitle2" color="text.secondary" sx={{ mt: 1 }}>
                          Emergency Contact Information
                        </Typography>

                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                          <FormControlLabel
                            control={
                              <Switch
                                checked={formData.emergency_contact_is_resident}
                                onChange={(e) => {
                                  const isChecked = e.target.checked;
                                  console.log('Emergency contact is resident toggle changed to:', isChecked);

                                  setFormData(prev => ({
                                    ...prev,
                                    emergency_contact_is_resident: isChecked,
                                    // Clear the linked citizen if toggling off
                                    emergency_contact_linked_citizen: isChecked ? prev.emergency_contact_linked_citizen : ''
                                  }));

                                  // Clear any existing search results when toggling
                                  setEmergencyContactSearchResults([]);
                                  setEmergencyContactSearchQuery('');
                                }}
                                color="primary"
                              />
                            }
                            label="Contact is a resident"
                          />
                        </Box>

                        {formData.emergency_contact_is_resident ? (
                          <FormControl fullWidth>
                            <Autocomplete
                              id="emergency-contact-citizen-select"
                              options={emergencyContactSearchResults}
                              getOptionLabel={(option) => {
                                if (!option) return '';
                                // Display the ID number that will be printed on the ID card
                                const idDisplay = option.id_number ? option.id_number :
                                                 (option.registration_number ? option.registration_number : 'No ID');
                                return `${option.first_name} ${option.middle_name ? option.middle_name + ' ' : ''}${option.last_name} (${idDisplay})`;
                              }}
                              loading={searchingEmergencyContact}
                              loadingText="Searching for citizens..."
                              value={citizens.find(c => c.id === formData.emergency_contact_linked_citizen) || null}
                              onChange={(event, newValue) => {
                                console.log('Emergency contact citizen selected:', newValue);
                                setFormData(prev => ({
                                  ...prev,
                                  emergency_contact_linked_citizen: newValue ? newValue.id : ''
                                }));
                              }}
                              onInputChange={(event, newInputValue) => {
                                console.log('Emergency contact search input changed to:', newInputValue);
                                setEmergencyContactSearchQuery(newInputValue);

                                // Clear any existing timeout
                                if (window.emergencyContactSearchTimeout) {
                                  clearTimeout(window.emergencyContactSearchTimeout);
                                  console.log('Cleared previous emergency contact search timeout');
                                }

                                // Only search if query is at least 2 characters
                                if (newInputValue.length >= 2) {
                                  setSearchingEmergencyContact(true);
                                  console.log('Setting emergency contact search loading state to true');

                                  // Debounce the search
                                  window.emergencyContactSearchTimeout = setTimeout(() => {
                                    console.log('Emergency contact search timeout expired, searching for:', newInputValue);
                                    searchEmergencyContactCitizens(newInputValue);
                                  }, 500);
                                } else {
                                  // Don't clear results if the input is empty
                                  if (newInputValue.length > 0) {
                                    console.log('Emergency contact search query too short, clearing results');
                                    setEmergencyContactSearchResults([]);
                                  } else {
                                    console.log('Emergency contact search input empty, keeping existing results');
                                  }
                                  setSearchingEmergencyContact(false);
                                }
                              }}
                              filterOptions={(x) => x} // No client-side filtering, we do it in the search function
                              renderInput={(params) => (
                                <TextField
                                  {...params}
                                  label="Search for Emergency Contact"
                                  placeholder="Type to search"
                                  InputProps={{
                                    ...params.InputProps,
                                    startAdornment: (
                                      <>
                                        <InputAdornment position="start">
                                          <SearchIcon color="action" />
                                        </InputAdornment>
                                        {params.InputProps.startAdornment}
                                      </>
                                    ),
                                    endAdornment: (
                                      <>
                                        {searchingEmergencyContact ? <CircularProgress color="inherit" size={20} /> : null}
                                        {params.InputProps.endAdornment}
                                      </>
                                    )
                                  }}
                                />
                              )}
                              renderOption={(props, option) => {
                                // Display the ID number that will be printed on the ID card
                                const idDisplay = option.id_number ? option.id_number :
                                                 (option.registration_number ? option.registration_number : 'No ID');
                                return (
                                  <li {...props}>
                                    {option.first_name} {option.middle_name ? option.middle_name + ' ' : ''}{option.last_name} ({idDisplay})
                                  </li>
                                );
                              }}
                              noOptionsText={
                                emergencyContactSearchQuery.length < 2
                                  ? "Type at least 2 characters to search for citizens"
                                  : searchingEmergencyContact
                                    ? "Searching..."
                                    : citizens.length === 0
                                      ? "Loading citizens..."
                                      : "No matching citizens found. Try a different search term."
                              }
                              ListboxProps={{
                                style: { maxHeight: '200px' }
                              }}
                            />
                            <FormHelperText>Search and select a citizen as emergency contact</FormHelperText>
                          </FormControl>
                        ) : (
                          <>
                            <Box sx={{
                              display: 'grid',
                              gridTemplateColumns: '1fr 1fr 1fr',
                              gap: 2,
                              '& > *': { width: '100%' }
                            }}>
                              <TextField
                                fullWidth
                                label="First Name"
                                name="emergency_contact_first_name"
                                value={formData.emergency_contact_first_name}
                                onChange={handleChange}
                                helperText="Enter contact's first name"
                                InputProps={{
                                  startAdornment: (
                                    <InputAdornment position="start">
                                      <PersonIcon color="action" />
                                    </InputAdornment>
                                  ),
                                }}
                              />
                              <TextField
                                fullWidth
                                label="Middle Name"
                                name="emergency_contact_middle_name"
                                value={formData.emergency_contact_middle_name}
                                onChange={handleChange}
                                helperText="Optional"
                                InputProps={{
                                  startAdornment: (
                                    <InputAdornment position="start">
                                      <PersonIcon color="action" />
                                    </InputAdornment>
                                  ),
                                }}
                              />
                              <TextField
                                fullWidth
                                label="Last Name"
                                name="emergency_contact_last_name"
                                value={formData.emergency_contact_last_name}
                                onChange={handleChange}
                                helperText="Enter contact's last name"
                                InputProps={{
                                  startAdornment: (
                                    <InputAdornment position="start">
                                      <PersonIcon color="action" />
                                    </InputAdornment>
                                  ),
                                }}
                              />
                            </Box>

                            <Box sx={{
                              display: 'grid',
                              gridTemplateColumns: '1fr 1fr',
                              gap: 2,
                              '& > *': { width: '100%' }
                            }}>
                              <TextField
                                fullWidth
                                label="Phone Number"
                                name="emergency_contact_phone"
                                value={formData.emergency_contact_phone}
                                onChange={handleChange}
                                helperText="Enter contact's phone number"
                                InputProps={{
                                  startAdornment: (
                                    <InputAdornment position="start">
                                      <PhoneIcon color="action" />
                                    </InputAdornment>
                                  ),
                                }}
                              />
                              <TextField
                                fullWidth
                                label="Email"
                                name="emergency_contact_email"
                                type="email"
                                value={formData.emergency_contact_email}
                                onChange={handleChange}
                                helperText="Enter contact's email (optional)"
                                InputProps={{
                                  startAdornment: (
                                    <InputAdornment position="start">
                                      <EmailIcon color="action" />
                                    </InputAdornment>
                                  ),
                                }}
                              />
                            </Box>

                            <FormControl fullWidth>
                              <InputLabel id="relationship-select-label">Relationship</InputLabel>
                              <Select
                                labelId="relationship-select-label"
                                id="relationship-select"
                                name="emergency_contact_relationship"
                                value={formData.emergency_contact_relationship}
                                onChange={handleChange}
                                label="Relationship"
                                disabled={loadingRelationshipTypes}
                                startAdornment={<InputAdornment position="start"><PersonIcon color="action" /></InputAdornment>}
                              >
                                {loadingRelationshipTypes ? (
                                  <MenuItem disabled>Loading relationship types...</MenuItem>
                                ) : relationshipTypes.length === 0 ? (
                                  <>
                                    <MenuItem value="SPOUSE">Spouse</MenuItem>
                                    <MenuItem value="PARENT">Parent</MenuItem>
                                    <MenuItem value="CHILD">Child</MenuItem>
                                    <MenuItem value="SIBLING">Sibling</MenuItem>
                                    <MenuItem value="FRIEND">Friend</MenuItem>
                                    <MenuItem value="OTHER">Other</MenuItem>
                                  </>
                                ) : (
                                  relationshipTypes.map((type) => (
                                    <MenuItem key={type.id} value={type.id}>
                                      {type.name}
                                    </MenuItem>
                                  ))
                                )}
                              </Select>
                              <FormHelperText>Select relationship to the citizen</FormHelperText>
                            </FormControl>
                          </>
                        )}
                      </Box>
                    </CardContent>
                  </Card>
                </Box>
              )}

              {/* Step 5: Photo */}
              {activeStep === 5 && (
                <Box sx={{
                  display: 'flex',
                  flexDirection: 'column',
                  width: '100%',
                  flexGrow: 1,
                  mb: 4
                }}>
                  <Card sx={{
                    mb: 4,
                    height: '100%',
                    width: '100%',
                    display: 'flex',
                    flexDirection: 'column',
                    transition: 'all 0.3s ease',
                    '&:hover': {
                      transform: 'translateY(-5px)',
                      boxShadow: '0 12px 20px rgba(0, 0, 0, 0.1)',
                    },
                    borderRadius: 2,
                    overflow: 'hidden',
                    border: 'none',
                    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.08)',
                    position: 'relative'
                  }}>
                    <Box sx={{
                      p: 2,
                      bgcolor: '#9C27B0',
                      color: 'white',
                      display: 'flex',
                      alignItems: 'center'
                    }}>
                      <Box
                        sx={{
                          mr: 1,
                        }}
                      >
                        <PhotoCameraIcon fontSize="medium" />
                      </Box>
                      <Typography variant="h6" sx={{ fontWeight: 600 }}>
                        Photo
                      </Typography>
                    </Box>
                    <CardContent sx={{ p: 2, flexGrow: 1 }}>
                      <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                        Upload a passport-sized photo of the citizen. This photo will be used on the ID card.
                      </Typography>
                      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                        <Box sx={{
                          display: 'flex',
                          flexDirection: { xs: 'column', sm: 'row' },
                          gap: 2,
                          alignItems: 'flex-start',
                          '& > *': { width: { xs: '100%', sm: '50%' } }
                        }}>
                          <Box sx={{ display: 'flex', flexDirection: 'column', height: '100%' }}>
                            <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                              Add a passport-sized photo (optional)
                            </Typography>
                            <Box sx={{ display: 'flex', gap: 1, mb: 1, justifyContent: 'center' }}>
                              <Button
                                variant="outlined"
                                component="label"
                                startIcon={<PhotoCameraIcon />}
                                size="small"
                                sx={{
                                  flex: 1,
                                  maxWidth: '120px',
                                  height: '40px',
                                  borderStyle: 'dashed',
                                  borderWidth: 2,
                                  transition: 'all 0.3s',
                                  '&:hover': {
                                    borderColor: 'primary.main',
                                    bgcolor: 'rgba(63, 81, 181, 0.04)',
                                    transform: 'translateY(-2px)',
                                    boxShadow: '0 4px 8px rgba(0,0,0,0.05)'
                                  }
                                }}
                              >
                                Upload
                                <input
                                  type="file"
                                  hidden
                                  accept="image/*"
                                  onChange={handlePhotoChange}
                                />
                              </Button>
                              <Button
                                variant="outlined"
                                color="secondary"
                                startIcon={<PhotoCameraIcon />}
                                size="small"
                                onClick={toggleCapture}
                                sx={{
                                  flex: 1,
                                  maxWidth: '120px',
                                  height: '40px',
                                  borderStyle: 'dashed',
                                  borderWidth: 2,
                                  transition: 'all 0.3s',
                                  '&:hover': {
                                    borderColor: 'secondary.main',
                                    bgcolor: 'rgba(156, 39, 176, 0.04)',
                                    transform: 'translateY(-2px)',
                                    boxShadow: '0 4px 8px rgba(0,0,0,0.05)'
                                  }
                                }}
                              >
                                Capture
                              </Button>
                            </Box>
                            <Typography variant="caption" color="text.secondary" sx={{ textAlign: 'center', display: 'block' }}>
                              Recommended size: 320x320 pixels, max 2MB
                            </Typography>
                            {isCapturing && (
                              <Box sx={{ mt: 2, position: 'relative', maxWidth: '320px', margin: '0 auto' }}>
                                <Webcam
                                  audio={false}
                                  ref={webcamRef}
                                  screenshotFormat="image/jpeg"
                                  videoConstraints={{
                                    width: 320,
                                    height: 320,
                                    facingMode: "user"
                                  }}
                                  style={{
                                    width: '100%',
                                    height: 'auto',
                                    borderRadius: '4px',
                                    border: '1px solid #ccc',
                                    boxShadow: '0 4px 8px rgba(0,0,0,0.1)'
                                  }}
                                />
                                <Box sx={{ display: 'flex', justifyContent: 'center', mt: 1, gap: 1 }}>
                                  <Button
                                    variant="contained"
                                    color="secondary"
                                    onClick={capturePhoto}
                                    startIcon={<PhotoCameraIcon />}
                                    size="small"
                                  >
                                    Capture
                                  </Button>
                                  <Button
                                    variant="outlined"
                                    onClick={() => setIsCapturing(false)}
                                    size="small"
                                  >
                                    Cancel
                                  </Button>
                                </Box>
                              </Box>
                            )}
                          </Box>
                          <Box>
                            <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                              Photo Preview
                            </Typography>
                            <Box
                              sx={{
                                width: '100%',
                                height: '320px',
                                maxWidth: '320px',
                                margin: '0 auto',
                                border: '2px dashed',
                                borderColor: photoPreview ? 'primary.main' : '#ccc',
                                display: 'flex',
                                justifyContent: 'center',
                                alignItems: 'center',
                                overflow: 'hidden',
                                bgcolor: photoPreview ? 'white' : '#f5f5f5',
                                transition: 'all 0.3s',
                                position: 'relative',
                                boxShadow: photoPreview ? '0 4px 12px rgba(63, 81, 181, 0.15)' : 'none',
                                borderRadius: '4px'
                              }}
                            >
                              {photoPreview ? (
                                <>
                                  <Box
                                    component="img"
                                    src={photoPreview}
                                    alt="Citizen photo preview"
                                    sx={{
                                      maxWidth: '100%',
                                      maxHeight: '100%',
                                      objectFit: 'contain',
                                      transition: 'transform 0.3s',
                                      '&:hover': {
                                        transform: 'scale(1.02)'
                                      }
                                    }}
                                  />
                                  <IconButton
                                    size="small"
                                    sx={{
                                      position: 'absolute',
                                      top: 8,
                                      right: 8,
                                      bgcolor: 'rgba(0,0,0,0.1)',
                                      color: 'white',
                                      transition: 'all 0.2s',
                                      '&:hover': {
                                        bgcolor: 'rgba(0,0,0,0.2)',
                                        transform: 'rotate(90deg)'
                                      }
                                    }}
                                    onClick={() => {
                                      setPhotoPreview(null);
                                      setFormData(prev => ({ ...prev, photo: null }));
                                    }}
                                  >
                                    <Box component="span" sx={{ fontSize: 18, fontWeight: 'bold' }}>×</Box>
                                  </IconButton>
                                </>
                              ) : (
                                <Box
                                  sx={{
                                    textAlign: 'center',
                                    p: 2,
                                    transition: 'all 0.3s',
                                    '&:hover': {
                                      transform: 'translateY(-5px)'
                                    }
                                  }}
                                >
                                  <PhotoCameraIcon sx={{ fontSize: 50, color: 'text.disabled', mb: 1 }} />
                                  <Typography variant="body2" color="text.secondary">
                                    No photo uploaded yet
                                  </Typography>
                                  <Typography variant="caption" color="text.disabled" sx={{ display: 'block', mt: 1 }}>
                                    Click upload or capture to add a photo
                                  </Typography>
                                </Box>
                              )}
                            </Box>
                          </Box>
                        </Box>
                      </Box>
                    </CardContent>
                  </Card>
                </Box>
              )}

              {/* Step 6: Review & Submit */}
              {activeStep === 6 && (
                <Box sx={{
                  display: 'flex',
                  flexDirection: 'column',
                  width: '100%',
                  flexGrow: 1,
                  mb: 4
                }}>
                  <Card sx={{
                    mb: 4,
                    height: '100%',
                    width: '100%',
                    display: 'flex',
                    flexDirection: 'column',
                    transition: 'all 0.3s ease',
                    '&:hover': {
                      transform: 'translateY(-5px)',
                      boxShadow: '0 12px 20px rgba(0, 0, 0, 0.1)',
                    },
                    borderRadius: 2,
                    overflow: 'hidden',
                    border: 'none',
                    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.08)',
                    position: 'relative'
                  }}>
                    <Box sx={{
                      p: 2,
                      bgcolor: 'success.main',
                      color: 'white',
                      display: 'flex',
                      alignItems: 'center'
                    }}>
                      <Box
                        sx={{
                          mr: 1,
                        }}
                      >
                        <CheckCircleIcon fontSize="medium" />
                      </Box>
                      <Typography variant="h6" sx={{ fontWeight: 600 }}>
                        Review & Submit
                      </Typography>
                    </Box>
                    <CardContent sx={{ p: 2, flexGrow: 1 }}>
                      <Typography variant="body1" color="text.secondary" sx={{ mb: 2 }}>
                        Please review all the information before submitting.
                      </Typography>
                      <Alert severity="info" sx={{ mb: 2, borderRadius: 2 }}>
                        <AlertTitle>Ready to Submit</AlertTitle>
                        Click the Submit button below to register this citizen. Once submitted, you'll be able to create an ID card for this citizen.
                      </Alert>

                      {/* Review Information */}
                      <Box sx={{ mt: 3 }}>
                        <Grid container spacing={3}>
                          {/* Basic Information */}
                          <Grid item xs={12} md={6}>
                            <Paper elevation={0} sx={{ p: 2, borderRadius: 2, bgcolor: 'rgba(0,0,0,0.02)', border: '1px solid rgba(0,0,0,0.05)' }}>
                              <Typography variant="h6" sx={{ mb: 2, fontWeight: 600, color: 'primary.main', display: 'flex', alignItems: 'center' }}>
                                <PersonIcon sx={{ mr: 1 }} /> Basic Information
                              </Typography>
                              <Grid container spacing={2}>
                                <Grid item xs={6}>
                                  <Typography variant="subtitle2" color="text.secondary">First Name:</Typography>
                                  <Typography variant="body1">{formData.first_name || '-'}</Typography>
                                </Grid>
                                <Grid item xs={6}>
                                  <Typography variant="subtitle2" color="text.secondary">Middle Name:</Typography>
                                  <Typography variant="body1">{formData.middle_name || '-'}</Typography>
                                </Grid>
                                <Grid item xs={6}>
                                  <Typography variant="subtitle2" color="text.secondary">Last Name:</Typography>
                                  <Typography variant="body1">{formData.last_name || '-'}</Typography>
                                </Grid>
                                <Grid item xs={6}>
                                  <Typography variant="subtitle2" color="text.secondary">Date of Birth:</Typography>
                                  <Typography variant="body1">{formData.date_of_birth ? new Date(formData.date_of_birth).toLocaleDateString() : '-'}</Typography>
                                </Grid>
                                <Grid item xs={6}>
                                  <Typography variant="subtitle2" color="text.secondary">Gender:</Typography>
                                  <Typography variant="body1">{formData.gender || '-'}</Typography>
                                </Grid>
                                <Grid item xs={6}>
                                  <Typography variant="subtitle2" color="text.secondary">Religion:</Typography>
                                  <Typography variant="body1">{religions.find(r => r.id === formData.religion)?.name || formData.religion || '-'}</Typography>
                                </Grid>
                              </Grid>
                            </Paper>
                          </Grid>

                          {/* Contact Information */}
                          <Grid item xs={12} md={6}>
                            <Paper elevation={0} sx={{ p: 2, borderRadius: 2, bgcolor: 'rgba(0,0,0,0.02)', border: '1px solid rgba(0,0,0,0.05)' }}>
                              <Typography variant="h6" sx={{ mb: 2, fontWeight: 600, color: 'primary.main', display: 'flex', alignItems: 'center' }}>
                                <HomeIcon sx={{ mr: 1 }} /> Contact Information
                              </Typography>
                              <Grid container spacing={2}>
                                <Grid item xs={6}>
                                  <Typography variant="subtitle2" color="text.secondary">Phone:</Typography>
                                  <Typography variant="body1">{formData.phone || '-'}</Typography>
                                </Grid>
                                <Grid item xs={6}>
                                  <Typography variant="subtitle2" color="text.secondary">Email:</Typography>
                                  <Typography variant="body1">{formData.email || '-'}</Typography>
                                </Grid>
                                <Grid item xs={6}>
                                  <Typography variant="subtitle2" color="text.secondary">Address:</Typography>
                                  <Typography variant="body1">{formData.address || '-'}</Typography>
                                </Grid>
                                <Grid item xs={6}>
                                  <Typography variant="subtitle2" color="text.secondary">House Number:</Typography>
                                  <Typography variant="body1">{formData.house_number || '-'}</Typography>
                                </Grid>
                                <Grid item xs={6}>
                                  <Typography variant="subtitle2" color="text.secondary">Subcity:</Typography>
                                  <Typography variant="body1">
                                    {(() => {
                                      // Try to get subcity name from subcities array first
                                      const subcityFromArray = subcities.find(s => s.id === formData.subcity)?.name;
                                      if (subcityFromArray) return subcityFromArray;

                                      // Try to get subcity name from tenant
                                      if (tenant?.parent_name) {
                                        return tenant.parent_name;
                                      }

                                      // Try to get subcity name from localStorage tenant
                                      const tenantString = localStorage.getItem('tenant');
                                      if (tenantString) {
                                        try {
                                          const localTenant = JSON.parse(tenantString);
                                          if (localTenant?.parent_name) {
                                            return localTenant.parent_name;
                                          }
                                        } catch (error) {
                                          console.error('Error parsing tenant from localStorage:', error);
                                        }
                                      }

                                      // Try to determine parent subcity from schema_name
                                      const schemaName = localStorage.getItem('schema_name');
                                      if (schemaName && schemaName.startsWith('kebele')) {
                                        // For kebele schemas, the parent is typically a subcity
                                        const kebeleToSubcityMap = {
                                          'kebele14': 'Zoble',
                                          'kebele15': 'Zoble',
                                          'kebele16': 'Zoble',
                                          // Add more mappings as needed
                                        };

                                        const parentName = kebeleToSubcityMap[schemaName];
                                        if (parentName) {
                                          return parentName;
                                        }
                                      }

                                      // Fallback to formData.subcity or hardcoded value
                                      return formData.subcity || 'Zoble';
                                    })()}
                                  </Typography>
                                </Grid>
                                <Grid item xs={6}>
                                  <Typography variant="subtitle2" color="text.secondary">Kebele:</Typography>
                                  <Typography variant="body1">
                                    {(() => {
                                      // Try to get kebele name from kebeles array first
                                      const kebeleFromArray = kebeles.find(k => k.id === formData.kebele)?.name;
                                      if (kebeleFromArray) return kebeleFromArray;

                                      // Try to get kebele name from tenant
                                      if (tenant?.type === 'KEBELE' && tenant?.name) {
                                        return tenant.name;
                                      }

                                      // Try to get kebele name from localStorage tenant
                                      const tenantString = localStorage.getItem('tenant');
                                      if (tenantString) {
                                        try {
                                          const localTenant = JSON.parse(tenantString);
                                          if (localTenant?.name) {
                                            return localTenant.name;
                                          }
                                        } catch (error) {
                                          console.error('Error parsing tenant from localStorage:', error);
                                        }
                                      }

                                      // Try to get kebele name from schema_name
                                      const schemaName = localStorage.getItem('schema_name');
                                      if (schemaName && schemaName.startsWith('kebele')) {
                                        const extractedId = schemaName.replace('kebele', '');
                                        if (extractedId) {
                                          return `Kebele ${extractedId}`;
                                        }
                                      }

                                      // Fallback to formData.kebele or hardcoded value
                                      return formData.kebele ? (typeof formData.kebele === 'string' && formData.kebele.startsWith('kebele') ?
                                        `Kebele ${formData.kebele.replace('kebele', '')}` : formData.kebele) : 'Kebele 14';
                                    })()}
                                  </Typography>
                                </Grid>
                                <Grid item xs={6}>
                                  <Typography variant="subtitle2" color="text.secondary">Ketena:</Typography>
                                  <Typography variant="body1">{ketenas.find(k => k.id === formData.ketena)?.name || formData.ketena || '-'}</Typography>
                                </Grid>
                              </Grid>
                            </Paper>
                          </Grid>

                          {/* Additional Information */}
                          <Grid item xs={12} md={6}>
                            <Paper elevation={0} sx={{ p: 2, borderRadius: 2, bgcolor: 'rgba(0,0,0,0.02)', border: '1px solid rgba(0,0,0,0.05)' }}>
                              <Typography variant="h6" sx={{ mb: 2, fontWeight: 600, color: 'primary.main', display: 'flex', alignItems: 'center' }}>
                                <FlagIcon sx={{ mr: 1 }} /> Additional Information
                              </Typography>
                              <Grid container spacing={2}>
                                <Grid item xs={6}>
                                  <Typography variant="subtitle2" color="text.secondary">Nationality:</Typography>
                                  <Typography variant="body1">{formData.nationality || '-'}</Typography>
                                </Grid>
                                <Grid item xs={6}>
                                  <Typography variant="subtitle2" color="text.secondary">Country:</Typography>
                                  <Typography variant="body1">{countries.find(c => c.id === formData.nationality_country)?.name || formData.nationality_country || '-'}</Typography>
                                </Grid>
                                <Grid item xs={6}>
                                  <Typography variant="subtitle2" color="text.secondary">Region:</Typography>
                                  <Typography variant="body1">{regions.find(r => r.id === formData.region)?.name || formData.region || '-'}</Typography>
                                </Grid>
                                <Grid item xs={6}>
                                  <Typography variant="subtitle2" color="text.secondary">Employed:</Typography>
                                  <Typography variant="body1">{formData.employment ? 'Yes' : 'No'}</Typography>
                                </Grid>
                                {formData.employment && (
                                  <>
                                    <Grid item xs={6}>
                                      <Typography variant="subtitle2" color="text.secondary">Employee Type:</Typography>
                                      <Typography variant="body1">{employeeTypes.find(et => et.id === formData.employee_type)?.name || formData.employee_type || '-'}</Typography>
                                    </Grid>
                                    <Grid item xs={6}>
                                      <Typography variant="subtitle2" color="text.secondary">Organization:</Typography>
                                      <Typography variant="body1">{formData.organization_name || '-'}</Typography>
                                    </Grid>
                                    <Grid item xs={6}>
                                      <Typography variant="subtitle2" color="text.secondary">Occupation:</Typography>
                                      <Typography variant="body1">{formData.occupation || '-'}</Typography>
                                    </Grid>
                                    <Grid item xs={6}>
                                      <Typography variant="subtitle2" color="text.secondary">Employment Type:</Typography>
                                      <Typography variant="body1">{employmentTypes.find(et => et.id === formData.employment_type)?.name || formData.employment_type || '-'}</Typography>
                                    </Grid>
                                  </>
                                )}
                              </Grid>
                            </Paper>
                          </Grid>

                          {/* Family Information */}
                          <Grid item xs={12} md={6}>
                            <Paper elevation={0} sx={{ p: 2, borderRadius: 2, bgcolor: 'rgba(0,0,0,0.02)', border: '1px solid rgba(0,0,0,0.05)' }}>
                              <Typography variant="h6" sx={{ mb: 2, fontWeight: 600, color: 'primary.main', display: 'flex', alignItems: 'center' }}>
                                <FamilyRestroomIcon sx={{ mr: 1 }} /> Family Information
                              </Typography>
                              <Grid container spacing={2}>
                                <Grid item xs={6}>
                                  <Typography variant="subtitle2" color="text.secondary">Marital Status:</Typography>
                                  <Typography variant="body1">{maritalStatuses.find(ms => ms.id === formData.marital_status)?.name || formData.marital_status || '-'}</Typography>
                                </Grid>
                                {formData.marital_status && formData.marital_status !== 'SINGLE' && (
                                  <Grid item xs={12}>
                                    <Typography variant="subtitle2" color="text.secondary">Spouse:</Typography>
                                    {formData.spouse_is_resident && formData.spouse_linked_citizen ? (
                                      <>
                                        <Typography variant="body1">
                                          {citizens.find(c => c.id === formData.spouse_linked_citizen) ?
                                            `${citizens.find(c => c.id === formData.spouse_linked_citizen)?.first_name} ${citizens.find(c => c.id === formData.spouse_linked_citizen)?.middle_name || ''} ${citizens.find(c => c.id === formData.spouse_linked_citizen)?.last_name}` :
                                            'Selected Resident'}
                                        </Typography>
                                        <Chip
                                          size="small"
                                          color="primary"
                                          label="Resident"
                                          icon={<PersonIcon />}
                                          sx={{ mt: 0.5 }}
                                        />
                                      </>
                                    ) : (
                                      <Typography variant="body1">{formData.spouse_first_name ? `${formData.spouse_first_name} ${formData.spouse_middle_name || ''} ${formData.spouse_last_name || ''}` : '-'}</Typography>
                                    )}
                                  </Grid>
                                )}
                                <Grid item xs={12}>
                                  <Typography variant="subtitle2" color="text.secondary" sx={{ mt: 1 }}>Mother:</Typography>
                                  {formData.mother_is_resident && formData.mother_linked_citizen ? (
                                    <>
                                      <Typography variant="body1">
                                        {citizens.find(c => c.id === formData.mother_linked_citizen) ?
                                          `${citizens.find(c => c.id === formData.mother_linked_citizen)?.first_name} ${citizens.find(c => c.id === formData.mother_linked_citizen)?.middle_name || ''} ${citizens.find(c => c.id === formData.mother_linked_citizen)?.last_name}` :
                                          'Selected Resident'}
                                      </Typography>
                                      <Chip
                                        size="small"
                                        color="primary"
                                        label="Resident"
                                        icon={<PersonIcon />}
                                        sx={{ mt: 0.5 }}
                                      />
                                    </>
                                  ) : (
                                    <Typography variant="body1">{formData.mother_first_name ? `${formData.mother_first_name} ${formData.mother_middle_name || ''} ${formData.mother_last_name || ''}` : '-'}</Typography>
                                  )}
                                </Grid>
                                <Grid item xs={12}>
                                  <Typography variant="subtitle2" color="text.secondary">Father:</Typography>
                                  {formData.father_is_resident && formData.father_linked_citizen ? (
                                    <>
                                      <Typography variant="body1">
                                        {citizens.find(c => c.id === formData.father_linked_citizen) ?
                                          `${citizens.find(c => c.id === formData.father_linked_citizen)?.first_name} ${citizens.find(c => c.id === formData.father_linked_citizen)?.middle_name || ''} ${citizens.find(c => c.id === formData.father_linked_citizen)?.last_name}` :
                                          'Selected Resident'}
                                      </Typography>
                                      <Chip
                                        size="small"
                                        color="primary"
                                        label="Resident"
                                        icon={<PersonIcon />}
                                        sx={{ mt: 0.5 }}
                                      />
                                    </>
                                  ) : (
                                    <Typography variant="body1">{formData.father_first_name ? `${formData.father_first_name} ${formData.father_middle_name || ''} ${formData.father_last_name || ''}` : '-'}</Typography>
                                  )}
                                </Grid>
                                <Grid item xs={12}>
                                  <Typography variant="subtitle2" color="text.secondary">Emergency Contact:</Typography>
                                  {formData.emergency_contact_is_resident && formData.emergency_contact_linked_citizen ? (
                                    <>
                                      <Typography variant="body1">
                                        {citizens.find(c => c.id === formData.emergency_contact_linked_citizen) ?
                                          `${citizens.find(c => c.id === formData.emergency_contact_linked_citizen)?.first_name} ${citizens.find(c => c.id === formData.emergency_contact_linked_citizen)?.middle_name || ''} ${citizens.find(c => c.id === formData.emergency_contact_linked_citizen)?.last_name}` :
                                          'Selected Resident'}
                                      </Typography>
                                      <Chip
                                        size="small"
                                        color="primary"
                                        label="Resident"
                                        icon={<PersonIcon />}
                                        sx={{ mt: 0.5 }}
                                      />
                                    </>
                                  ) : (
                                    <>
                                      <Typography variant="body1">{formData.emergency_contact_first_name ? `${formData.emergency_contact_first_name} ${formData.emergency_contact_middle_name || ''} ${formData.emergency_contact_last_name || ''}` : '-'}</Typography>
                                      {formData.emergency_contact_phone && (
                                        <Typography variant="body2" color="text.secondary">{formData.emergency_contact_phone}</Typography>
                                      )}
                                    </>
                                  )}
                                </Grid>
                              </Grid>
                            </Paper>
                          </Grid>

                          {/* Photo Preview */}
                          <Grid item xs={12}>
                            <Paper elevation={0} sx={{ p: 2, borderRadius: 2, bgcolor: 'rgba(0,0,0,0.02)', border: '1px solid rgba(0,0,0,0.05)' }}>
                              <Typography variant="h6" sx={{ mb: 2, fontWeight: 600, color: 'primary.main', display: 'flex', alignItems: 'center' }}>
                                <PhotoCameraIcon sx={{ mr: 1 }} /> Photo
                              </Typography>
                              <Box sx={{ display: 'flex', justifyContent: 'center' }}>
                                {photoPreview ? (
                                  <Box
                                    component="img"
                                    src={photoPreview}
                                    alt="Citizen photo"
                                    sx={{
                                      width: 150,
                                      height: 150,
                                      objectFit: 'cover',
                                      borderRadius: 2,
                                      border: '1px solid rgba(0,0,0,0.1)',
                                      boxShadow: '0 4px 8px rgba(0,0,0,0.1)'
                                    }}
                                  />
                                ) : (
                                  <Box sx={{ textAlign: 'center', p: 2 }}>
                                    <PhotoCameraIcon sx={{ fontSize: 40, color: 'text.disabled', mb: 1 }} />
                                    <Typography variant="body2" color="text.secondary">
                                      No photo uploaded
                                    </Typography>
                                  </Box>
                                )}
                              </Box>
                            </Paper>
                          </Grid>
                        </Grid>
                      </Box>
                    </CardContent>
                  </Card>
                </Box>
              )}

              {/* Stepper Navigation */}
              <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 4, mb: 2 }}>
                <Button
                  disabled={activeStep === 0}
                  onClick={handleBack}
                  startIcon={<NavigateBeforeIcon />}
                  variant="outlined"
                  sx={{
                    borderRadius: 2,
                    px: 3,
                    py: 1,
                    fontWeight: 500,
                    borderColor: 'divider',
                    color: 'text.secondary',
                    '&:hover': {
                      borderColor: 'primary.main',
                      color: 'primary.main',
                      bgcolor: 'rgba(0, 0, 0, 0.02)'
                    },
                    transition: 'all 0.2s'
                  }}
                >
                  Back
                </Button>

                <Button
                  onClick={activeStep === steps.length - 1 ? handleSubmit : handleNext}
                  type="button"
                  variant="contained"
                  color="primary"
                  disabled={activeStep === steps.length - 1 && isSubmitting}
                  endIcon={
                    activeStep === steps.length - 1 ?
                      (isSubmitting ? <CircularProgress size={20} color="inherit" /> : <SaveIcon />) :
                      <NavigateNextIcon />
                  }
                  sx={{
                    borderRadius: 2,
                    px: 3,
                    py: 1,
                    fontWeight: 500,
                    boxShadow: 2,
                    '&:hover': {
                      boxShadow: 4,
                      transform: 'translateY(-1px)'
                    },
                    transition: 'all 0.2s'
                  }}
                >
                  {activeStep === steps.length - 1 ? (isSubmitting ? 'Submitting...' : 'Submit') : 'Next'}
                </Button>
              </Box>
            </Box>

            {/* Success message */}
            {submitSuccess && (
              <Paper
                elevation={0}
                sx={{
                  px: 3,
                  py: 1.5,
                  mt: 4,
                  bgcolor: 'rgba(76, 175, 80, 0.9)',
                  color: 'white',
                  borderRadius: '50px',
                  display: 'flex',
                  alignItems: 'center',
                  animation: 'fadeIn 0.5s ease-in-out',
                  boxShadow: '0 4px 12px rgba(76, 175, 80, 0.3)',
                  '@keyframes fadeIn': {
                    '0%': { opacity: 0, transform: 'translateY(10px)' },
                    '100%': { opacity: 1, transform: 'translateY(0)' }
                  }
                }}
              >
                <Box component="span" sx={{
                  display: 'inline-flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  bgcolor: 'white',
                  color: '#4CAF50',
                  borderRadius: '50%',
                  width: 28,
                  height: 28,
                  mr: 1.5,
                  fontSize: 16,
                  fontWeight: 'bold'
                }}>✓</Box>
                <Typography variant="body1" sx={{ fontWeight: 600 }}>
                  Citizen registered successfully!
                </Typography>
              </Paper>
            )}
            </ThemeProvider>
          </form>
        </Paper>
      </Container>

      {/* Success Snackbar */}
      <Snackbar
        open={snackbarOpen}
        autoHideDuration={8000}
        onClose={handleSnackbarClose}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
        sx={{
          '& .MuiSnackbarContent-root': {
            bgcolor: 'success.dark',
            minWidth: '400px',
            borderRadius: 8,
            boxShadow: '0 8px 30px rgba(0,0,0,0.2)',
            border: '1px solid rgba(255,255,255,0.1)'
          }
        }}
      >
        <Alert
          severity="success"
          variant="filled"
          sx={{
            width: '100%',
            alignItems: 'center',
            py: 1.5,
            px: 2,
            '& .MuiAlert-icon': { fontSize: 28, mr: 2 },
            background: 'linear-gradient(90deg, #4CAF50, #2E7D32)',
            borderRadius: 8
          }}
          action={
            <Button
              color="inherit"
              size="medium"
              variant="outlined"
              onClick={handleRegisterIDCard}
              sx={{
                borderRadius: 50,
                px: 2,
                py: 1,
                fontWeight: 600,
                borderColor: 'rgba(255,255,255,0.6)',
                borderWidth: 2,
                '&:hover': {
                  borderColor: 'white',
                  bgcolor: 'rgba(255,255,255,0.15)',
                  transform: 'translateY(-2px)',
                  boxShadow: '0 4px 12px rgba(0,0,0,0.2)'
                },
                transition: 'all 0.3s'
              }}
            >
              Register ID Card
            </Button>
          }
        >
          <Typography variant="body1" sx={{ fontWeight: 600, fontSize: '1rem' }}>
            Citizen registered successfully
          </Typography>
        </Alert>
      </Snackbar>
    </Box>
    </LocalizationProvider>
  );

  // CRITICAL: Force set location values right before rendering
  // This is a hack to ensure the values are set
  if (tenant?.type === 'KEBELE') {
    // Run the debug function to see what's in localStorage
    debugTenantInfo();

    // First try to get tenant information from localStorage
    const tenantString = localStorage.getItem('tenant');
    let localTenant = tenant; // Default to the tenant from context

    if (tenantString) {
      try {
        const parsedTenant = JSON.parse(tenantString);
        console.log('Parsed tenant from localStorage for final render:', parsedTenant);

        // Use the tenant from localStorage if it has more information
        if (parsedTenant && parsedTenant.id) {
          localTenant = parsedTenant;
          console.log('Using tenant from localStorage for final render:', localTenant);
        }
      } catch (error) {
        console.error('Error parsing tenant from localStorage:', error);
      }
    }

    // Get subcity from parent tenant
    const parentSubcityId = localTenant?.parent_id || tenant?.parent_id || determineParentSubcity();
    const parentSubcityName = localTenant?.parent_name || tenant?.parent_name || 'Parent Subcity';

    // Get kebele from current tenant - IMPORTANT: This is where we set the kebele value
    let kebeleId = localTenant?.id || tenant?.id || '';
    let kebeleName = localTenant?.name || tenant?.name || 'Current Kebele';

    // If we still don't have a kebele ID, try to get it from the schema_name
    if (!kebeleId || !kebeleName) {
      console.log('No kebele ID or name found, trying to extract from schema_name');
      const schemaName = localStorage.getItem('schema_name');
      if (schemaName && schemaName.startsWith('kebele')) {
        // Extract the kebele ID from the schema name (e.g., 'kebele14' -> '14')
        const extractedId = schemaName.replace('kebele', '');
        if (extractedId) {
          console.log('Extracted kebele ID from schema_name:', extractedId);
          kebeleId = extractedId;
          kebeleName = `Kebele ${extractedId}`;

          // Also update the kebeles array to include this kebele
          if (kebeles.length === 0 || !kebeles.some(k => k.id === kebeleId)) {
            console.log('Adding extracted kebele to kebeles array');
            setKebeles([{
              id: kebeleId,
              name: kebeleName,
              subcity: parentSubcityId,
              code: kebeleId.toString().padStart(2, '0')
            }]);
          }
        }
      }
    }

    // If we still don't have a kebele ID, use a hardcoded fallback
    if (!kebeleId || !kebeleName) {
      console.log('No kebele ID or name found, using hardcoded fallback');
      kebeleId = '14'; // Fallback to a default kebele ID
      kebeleName = 'Kebele 14'; // Fallback to a default kebele name

      // Also update the kebeles array to include this kebele
      if (kebeles.length === 0 || !kebeles.some(k => k.id === kebeleId)) {
        console.log('Adding fallback kebele to kebeles array');
        setKebeles([{
          id: kebeleId,
          name: kebeleName,
          subcity: parentSubcityId,
          code: kebeleId.toString().padStart(2, '0')
        }]);
      }
    }

    console.log('CRITICAL VALUES:');
    console.log('  kebeleId:', kebeleId);
    console.log('  kebeleName:', kebeleName);
    console.log('  localTenant?.id:', localTenant?.id);
    console.log('  tenant?.id:', tenant?.id);

    // Check if we need to update the form data
    if (formData.subcity !== parentSubcityId || formData.kebele !== kebeleId) {
      console.log('RENDER: Forcing location values from localStorage/context');
      console.log('  subcity (from parent tenant):', parentSubcityId, `(${parentSubcityName})`);
      console.log('  kebele (from current tenant):', kebeleId, `(${kebeleName})`);

      // Direct modification of formData
      formData.subcity = parentSubcityId;
      formData.kebele = kebeleId;

      console.log('DIRECT MODIFICATION: Setting form data values');
      console.log('  formData.subcity =', parentSubcityId);
      console.log('  formData.kebele =', kebeleId);

      // Also update the state to ensure the UI reflects these values
      setFormData(prev => {
        console.log('STATE UPDATE: Setting form data values');
        console.log('  prev.subcity =', prev.subcity);
        console.log('  prev.kebele =', prev.kebele);
        console.log('  new subcity =', parentSubcityId);
        console.log('  new kebele =', kebeleId);

        return {
          ...prev,
          subcity: parentSubcityId,
          kebele: kebeleId
        };
      });

      // Make sure we have subcities and kebeles in the dropdowns
      if (subcities.length === 0 && parentSubcityId) {
        console.log('No subcities found, creating one from parent tenant');
        setSubcities([{
          id: parentSubcityId,
          name: parentSubcityName,
          city: '1' // Assuming city ID 1
        }]);
      }

      if (kebeles.length === 0 && kebeleId) {
        console.log('No kebeles found, creating one from current tenant');
        setKebeles([{
          id: kebeleId,
          name: kebeleName,
          subcity: parentSubcityId,
          code: kebeleId.toString().padStart(2, '0')
        }]);
      }
    }

    // Try to directly set the select values using DOM manipulation
    setTimeout(() => {
      try {
        const subcitySelect = document.getElementById('subcity-select');
        const kebeleSelect = document.getElementById('kebele-select');

        if (subcitySelect) {
          // @ts-ignore - TypeScript doesn't know about the value property
          subcitySelect.value = parentSubcityId;
          console.log('DOM MANIPULATION: Set subcity select value to', parentSubcityId);
        } else {
          console.warn('Could not find subcity select element');
        }

        if (kebeleSelect) {
          // @ts-ignore - TypeScript doesn't know about the value property
          kebeleSelect.value = kebeleId;
          console.log('DOM MANIPULATION: Set kebele select value to', kebeleId);
        } else {
          console.warn('Could not find kebele select element');
        }

        // Also try to trigger a change event to update any dependent components
        if (subcitySelect) {
          const event = new Event('change', { bubbles: true });
          subcitySelect.dispatchEvent(event);
        }

        if (kebeleSelect) {
          const event = new Event('change', { bubbles: true });
          kebeleSelect.dispatchEvent(event);
        }
      } catch (error) {
        console.error('Error with DOM manipulation:', error);
      }
    }, 500);

    // Schedule another attempt after a longer delay
    setTimeout(() => {
      console.log('DELAYED RENDER: Forcing location values again');

      // Update the state again to ensure the UI reflects these values
      setFormData(prev => ({
        ...prev,
        subcity: parentSubcityId,
        kebele: kebeleId
      }));

      // Try DOM manipulation again
      try {
        const subcitySelect = document.getElementById('subcity-select');
        const kebeleSelect = document.getElementById('kebele-select');

        if (subcitySelect) {
          // @ts-ignore - TypeScript doesn't know about the value property
          subcitySelect.value = parentSubcityId;
          console.log('DELAYED DOM MANIPULATION: Set subcity select value to', parentSubcityId);
        }

        if (kebeleSelect) {
          // @ts-ignore - TypeScript doesn't know about the value property
          kebeleSelect.value = kebeleId;
          console.log('DELAYED DOM MANIPULATION: Set kebele select value to', kebeleId);
        }
      } catch (error) {
        console.error('Error with delayed DOM manipulation:', error);
      }
    }, 2000);
  }
};

export default RegisterCitizen;
