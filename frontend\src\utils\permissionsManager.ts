/**
 * Permissions Manager
 *
 * This module provides utilities for managing permissions in the application.
 * It handles role-based access control and tenant-specific permissions.
 */

// User roles
export enum UserRole {
  ADMIN = 'ADMIN',
  CITY_ADMIN = 'CITY_ADMIN',
  SUBCITY_ADMIN = 'SUBCITY_ADMIN',
  K<PERSON><PERSON>LE_LEADER = 'KEBELE_LEADER',
  CENTER_ADMIN = 'CENTER_ADMIN',
  CENTER_STAFF = 'CENTER_STAFF',
}

// Tenant types
export enum TenantType {
  PUBLIC = 'PUBLIC',
  CITY = 'CITY',
  SUBCITY = 'SUBCITY',
  KEBELE = 'KEBELE',
}

// Permission types
export enum Permission {
  // Citizen permissions
  VIEW_CITIZENS = 'VIEW_CITIZENS',
  CREATE_CITIZEN = 'CREATE_CITIZEN',
  EDIT_CITIZEN = 'EDIT_CITIZEN',
  DELETE_CITIZEN = 'DELETE_CITIZEN',

  // ID card permissions
  VIEW_ID_CARDS = 'VIEW_ID_CARDS',
  CREATE_ID_CARD = 'CREATE_ID_CARD',
  APPROVE_ID_CARD = 'APPROVE_ID_CARD',
  PRINT_ID_CARD = 'PRINT_ID_CARD',
  REJECT_ID_CARD = 'REJECT_ID_CARD',

  // Document permissions
  VERIFY_DOCUMENTS = 'VERIFY_DOCUMENTS',
  UPLOAD_DOCUMENTS = 'UPLOAD_DOCUMENTS',

  // User management permissions
  MANAGE_USERS = 'MANAGE_USERS',
  MANAGE_KEBELE_USERS = 'MANAGE_KEBELE_USERS',
  MANAGE_SUBCITY_USERS = 'MANAGE_SUBCITY_USERS',

  // Tenant permissions
  VIEW_CHILD_TENANT_DATA = 'VIEW_CHILD_TENANT_DATA',
  EDIT_CHILD_TENANT_DATA = 'EDIT_CHILD_TENANT_DATA',

  // Settings permissions
  MANAGE_SETTINGS = 'MANAGE_SETTINGS',

  // Report permissions
  VIEW_REPORTS = 'VIEW_REPORTS',
}

// Role-based permissions
const rolePermissions: Record<UserRole, Permission[]> = {
  [UserRole.ADMIN]: Object.values(Permission), // Admin has all permissions

  [UserRole.CITY_ADMIN]: [
    Permission.VIEW_REPORTS,
    Permission.MANAGE_SUBCITY_USERS,
    Permission.MANAGE_SETTINGS,
    Permission.VIEW_CHILD_TENANT_DATA,
  ],

  [UserRole.SUBCITY_ADMIN]: [
    Permission.VIEW_REPORTS,
    Permission.MANAGE_KEBELE_USERS,
    Permission.MANAGE_SETTINGS,
    Permission.VIEW_CHILD_TENANT_DATA,
    Permission.PRINT_ID_CARD,
  ],

  [UserRole.KEBELE_LEADER]: [
    Permission.VIEW_CITIZENS,
    Permission.CREATE_CITIZEN,
    Permission.EDIT_CITIZEN,
    Permission.VIEW_ID_CARDS,
    Permission.APPROVE_ID_CARD,
    Permission.REJECT_ID_CARD,
    Permission.VERIFY_DOCUMENTS,
    Permission.MANAGE_SETTINGS,
  ],

  [UserRole.CENTER_ADMIN]: [
    Permission.VIEW_CITIZENS,
    Permission.CREATE_CITIZEN,
    Permission.EDIT_CITIZEN,
    Permission.DELETE_CITIZEN,
    Permission.VIEW_ID_CARDS,
    Permission.CREATE_ID_CARD,
    Permission.UPLOAD_DOCUMENTS,
    Permission.MANAGE_SETTINGS,
  ],

  [UserRole.CENTER_STAFF]: [
    Permission.VIEW_CITIZENS,
    Permission.CREATE_CITIZEN,
    Permission.EDIT_CITIZEN,
    Permission.VIEW_ID_CARDS,
    Permission.CREATE_ID_CARD,
    Permission.UPLOAD_DOCUMENTS,
  ],
};

// Tenant-specific permissions
const tenantTypePermissions: Record<TenantType, Permission[]> = {
  [TenantType.PUBLIC]: [],

  [TenantType.CITY]: [
    Permission.VIEW_REPORTS,
    Permission.MANAGE_SUBCITY_USERS,
    Permission.MANAGE_SETTINGS,
    Permission.VIEW_CHILD_TENANT_DATA,
  ],

  [TenantType.SUBCITY]: [
    Permission.VIEW_REPORTS,
    Permission.MANAGE_KEBELE_USERS,
    Permission.MANAGE_SETTINGS,
    Permission.VIEW_CHILD_TENANT_DATA,
    Permission.PRINT_ID_CARD,
  ],

  [TenantType.KEBELE]: [
    Permission.VIEW_CITIZENS,
    Permission.CREATE_CITIZEN,
    Permission.EDIT_CITIZEN,
    Permission.VIEW_ID_CARDS,
    Permission.CREATE_ID_CARD,
    Permission.APPROVE_ID_CARD,
    Permission.REJECT_ID_CARD,
    Permission.VERIFY_DOCUMENTS,
    Permission.UPLOAD_DOCUMENTS,
    Permission.MANAGE_SETTINGS,
  ],
};

/**
 * Check if a user has a specific permission
 * @param userRole The user's role
 * @param tenantType The tenant type
 * @param permission The permission to check
 * @returns True if the user has the permission, false otherwise
 */
export const hasPermission = (
  userRole: string,
  tenantType: string,
  permission: Permission
): boolean => {
  // Convert string values to enum values
  const role = userRole as UserRole;
  const tenant = tenantType as TenantType;

  // Check if the role exists
  if (!rolePermissions[role]) {
    console.warn(`Unknown role: ${role}`);
    return false;
  }

  // Check if the tenant type exists
  if (!tenantTypePermissions[tenant]) {
    console.warn(`Unknown tenant type: ${tenant}`);
    return false;
  }

  // Check if the user's role has the permission
  const hasRolePermission = rolePermissions[role].includes(permission);

  // Check if the tenant type allows the permission
  const hasTenantPermission = tenantTypePermissions[tenant].includes(permission);

  // The user must have both role permission and tenant permission
  return hasRolePermission && hasTenantPermission;
};

/**
 * Get all permissions for a user
 * @param userRole The user's role
 * @param tenantType The tenant type
 * @returns Array of permissions the user has
 */
export const getUserPermissions = (
  userRole: string,
  tenantType: string
): Permission[] => {
  // Convert string values to enum values
  const role = userRole as UserRole;
  const tenant = tenantType as TenantType;

  // Check if the role exists
  if (!rolePermissions[role]) {
    console.warn(`Unknown role: ${role}`);
    return [];
  }

  // Check if the tenant type exists
  if (!tenantTypePermissions[tenant]) {
    console.warn(`Unknown tenant type: ${tenant}`);
    return [];
  }

  // Get the intersection of role permissions and tenant permissions
  return rolePermissions[role].filter(permission =>
    tenantTypePermissions[tenant].includes(permission)
  );
};

/**
 * Check if the current user has a specific permission
 * @param permission The permission to check
 * @returns True if the current user has the permission, false otherwise
 */
export const currentUserHasPermission = (permission: Permission): boolean => {
  try {
    // Get the current user and tenant from localStorage
    const userStr = localStorage.getItem('user');
    const tenantStr = localStorage.getItem('tenant');

    if (!userStr || !tenantStr) {
      console.warn('No user or tenant data found in localStorage');
      return false;
    }

    const user = JSON.parse(userStr);
    const tenant = JSON.parse(tenantStr);

    // Get the user's role and tenant type
    const userRole = user.role;
    const tenantType = tenant.schema_type || tenant.type;

    // Check if the user has the permission
    return hasPermission(userRole, tenantType, permission);
  } catch (error) {
    console.error('Error checking permission:', error);
    return false;
  }
};

/**
 * Get all permissions for the current user
 * @returns Array of permissions the current user has
 */
export const getCurrentUserPermissions = (): Permission[] => {
  try {
    // Get the current user and tenant from localStorage
    const userStr = localStorage.getItem('user');
    const tenantStr = localStorage.getItem('tenant');

    if (!userStr || !tenantStr) {
      console.warn('No user or tenant data found in localStorage');
      return [];
    }

    const user = JSON.parse(userStr);
    const tenant = JSON.parse(tenantStr);

    // Get the user's role and tenant type
    const userRole = user.role;
    const tenantType = tenant.schema_type || tenant.type;

    // Get the user's permissions
    return getUserPermissions(userRole, tenantType);
  } catch (error) {
    console.error('Error getting permissions:', error);
    return [];
  }
};

// Note: The withPermission HOC has been moved to PermissionsContext.tsx
// since it requires React to be in scope for JSX syntax
