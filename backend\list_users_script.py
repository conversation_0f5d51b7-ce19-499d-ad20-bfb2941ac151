import os
import django

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'neocamelot.settings')
django.setup()

from django.contrib.auth import get_user_model
from django_tenants.utils import tenant_context
from centers.models import Client

User = get_user_model()

def main():
    # Get all tenants
    tenants = Client.objects.all().order_by('schema_type', 'name')
    
    # For each tenant, get users
    print("\nUSERS BY TENANT:")
    
    for tenant in tenants:
        print(f"\nTenant: {tenant.name} ({tenant.schema_name})")
        
        try:
            with tenant_context(tenant):
                users = User.objects.all().order_by('email')
                
                if users.exists():
                    print("{:<40} {:<30} {:<20} {:<10} {:<10} {:<10}".format(
                        "Email", "Name", "Role", "Active", "Superuser", "Staff"
                    ))
                    print("-" * 120)
                    
                    for user in users:
                        role = getattr(user, 'role', 'N/A')
                        print("{:<40} {:<30} {:<20} {:<10} {:<10} {:<10}".format(
                            user.email,
                            user.get_full_name() or "N/A",
                            role,
                            "Yes" if user.is_active else "No",
                            "Yes" if user.is_superuser else "No",
                            "Yes" if user.is_staff else "No"
                        ))
                else:
                    print("  No users found in this tenant")
        except Exception as e:
            print(f"  Error accessing tenant {tenant.schema_name}: {str(e)}")

if __name__ == "__main__":
    main()
