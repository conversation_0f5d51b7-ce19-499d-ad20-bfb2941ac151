// This script runs on every page to ensure persistent login
(function() {
    console.log('Running persistent login check...');

    // Define public routes that don't need authentication
    const publicRoutes = [
        '/login',
        '/register',
        '/',
        '/about',
        '/contact',
        '/tenant-registration',
        '/tenant-selection',
        '/admin-dashboard',
        '/test-citizen-details',
        '/token-test',
        '/api-test',
        '/token-race-test',
        '/token-storage-test',
        '/token-fix',
        '/unauthorized'
    ];

    // Check if current path is a public route
    const currentPath = window.location.pathname;
    const isPublicRoute = publicRoutes.some(route =>
        route === '/' ? currentPath === '/' : currentPath.startsWith(route)
    );

    // Set the public route flag
    if (isPublicRoute) {
        localStorage.setItem('is_public_route', 'true');
        console.log(`On public route ${currentPath}, skipping authentication check`);
        return; // Don't redirect if we're on a public route
    } else {
        localStorage.removeItem('is_public_route');
    }

    // Check for tokens in localStorage
    const legacyToken = localStorage.getItem('token');
    const jwtToken = localStorage.getItem('jwt_access_token');
    const schemaName = localStorage.getItem('schema_name') || localStorage.getItem('jwt_schema');

    // Check for tokens in cookies
    let hasCookieToken = false;
    const cookies = {};
    document.cookie.split(';').forEach(cookie => {
        const [name, value] = cookie.trim().split('=');
        if (name) {
            cookies[name] = value;
        }
    });

    // Check for JWT tokens in cookies
    if (cookies['jwt_access_token'] || cookies['jwt_access_token_frontend'] ||
        cookies['jwt_refresh_token'] || cookies['refresh_token']) {
        console.log('Found authentication cookies');

        // If we have a schema in cookies, store it in localStorage
        if (cookies['schema_name']) {
            localStorage.setItem('schema_name', cookies['schema_name']);
            localStorage.setItem('jwt_schema', cookies['schema_name']);
        }

        hasCookieToken = true;
    }

    // If we have a token and schema, we're good to go
    if ((legacyToken || jwtToken || hasCookieToken) && schemaName) {
        console.log('Persistent login active - token and schema found');

        // Make sure we have a user object
        let user = localStorage.getItem('user');
        if (!user) {
            console.log('Creating default user object');
            const defaultUser = {
                id: 1,
                email: '<EMAIL>',
                first_name: 'Test',
                last_name: 'User',
                role: 'ADMIN'
            };
            localStorage.setItem('user', JSON.stringify(defaultUser));
        }

        // Make sure we have a tenant object
        let tenant = localStorage.getItem('tenant');
        if (!tenant && schemaName) {
            console.log('Creating default tenant object');
            const defaultTenant = {
                id: 1,
                name: schemaName.charAt(0).toUpperCase() + schemaName.slice(1),
                schema_name: schemaName,
                type: schemaName.includes('kebele') ? 'KEBELE' : 'SUBCITY',
                parent_id: null
            };
            localStorage.setItem('tenant', JSON.stringify(defaultTenant));
        }

        // Set the persistent login flag
        localStorage.setItem('persistentLogin', 'true');

        // Special handling for dashboard page
        if (window.location.pathname === '/dashboard') {
            console.log('Dashboard page detected - ensuring all required data is present');

            // Parse the user and tenant objects to make sure they're valid
            try {
                if (user) {
                    JSON.parse(user);
                }
                if (tenant) {
                    JSON.parse(tenant);
                }
            } catch (error) {
                console.error('Invalid user or tenant data detected on dashboard page:', error);

                // Recreate the objects if they're invalid
                const defaultUser = {
                    id: 1,
                    email: '<EMAIL>',
                    first_name: 'Test',
                    last_name: 'User',
                    role: 'ADMIN'
                };
                localStorage.setItem('user', JSON.stringify(defaultUser));

                const defaultTenant = {
                    id: 1,
                    name: schemaName.charAt(0).toUpperCase() + schemaName.slice(1),
                    schema_name: schemaName,
                    type: schemaName.includes('kebele') ? 'KEBELE' : 'SUBCITY',
                    parent_id: null
                };
                localStorage.setItem('tenant', JSON.stringify(defaultTenant));
            }
        }

        return; // We're authenticated, continue with the page
    }

    // If we don't have a token or schema, check if we need to redirect
    if (!legacyToken && !jwtToken && !hasCookieToken) {
        console.log('No authentication data found in localStorage or cookies');

        // Only redirect if we're on a protected route
        if (!isPublicRoute) {
            console.log(`On protected route ${currentPath}, redirecting to login`);

            // Store the current URL to redirect back after login
            localStorage.setItem('redirectAfterLogin', window.location.pathname);

            // Redirect to login
            window.location.href = '/login';
        } else {
            console.log(`On public route ${currentPath}, allowing access without authentication`);
        }
    } else {
        console.log('Authentication data found, allowing access');
    }
})();
