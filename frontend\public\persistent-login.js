// This script runs on every page to ensure persistent login
(function() {
    // Check if we're on the login page
    if (window.location.pathname === '/login') {
        return; // Don't redirect if we're already on the login page
    }

    // Check if we have the necessary authentication data
    const token = localStorage.getItem('token');
    const schemaName = localStorage.getItem('schema_name');

    // If we have a token and schema, we're good to go
    if (token && schemaName) {
        console.log('Persistent login active - token and schema found');

        // Make sure we have a user object
        let user = localStorage.getItem('user');
        if (!user) {
            console.log('Creating default user object');
            const defaultUser = {
                id: 1,
                email: '<EMAIL>',
                first_name: 'Test',
                last_name: 'User',
                role: 'ADMIN'
            };
            localStorage.setItem('user', JSON.stringify(defaultUser));
        }

        // Make sure we have a tenant object
        let tenant = localStorage.getItem('tenant');
        if (!tenant && schemaName) {
            console.log('Creating default tenant object');
            const defaultTenant = {
                id: 1,
                name: schemaName.charAt(0).toUpperCase() + schemaName.slice(1),
                schema_name: schemaName,
                type: schemaName.includes('kebele') ? 'KEBELE' : 'SUBCITY',
                parent_id: null
            };
            localStorage.setItem('tenant', JSON.stringify(defaultTenant));
        }

        // Set the persistent login flag
        localStorage.setItem('persistentLogin', 'true');

        // Special handling for dashboard page
        if (window.location.pathname === '/dashboard') {
            console.log('Dashboard page detected - ensuring all required data is present');

            // Parse the user and tenant objects to make sure they're valid
            try {
                if (user) {
                    JSON.parse(user);
                }
                if (tenant) {
                    JSON.parse(tenant);
                }
            } catch (error) {
                console.error('Invalid user or tenant data detected on dashboard page:', error);

                // Recreate the objects if they're invalid
                const defaultUser = {
                    id: 1,
                    email: '<EMAIL>',
                    first_name: 'Test',
                    last_name: 'User',
                    role: 'ADMIN'
                };
                localStorage.setItem('user', JSON.stringify(defaultUser));

                const defaultTenant = {
                    id: 1,
                    name: schemaName.charAt(0).toUpperCase() + schemaName.slice(1),
                    schema_name: schemaName,
                    type: schemaName.includes('kebele') ? 'KEBELE' : 'SUBCITY',
                    parent_id: null
                };
                localStorage.setItem('tenant', JSON.stringify(defaultTenant));
            }
        }

        return; // We're authenticated, continue with the page
    }

    // If we don't have a token or schema, redirect to login
    console.log('No authentication data found, redirecting to login');
    window.location.href = '/login';
})();
