import React, { useState } from 'react';
import { Box, Button, Typography, CircularProgress, Alert, Paper } from '@mui/material';
import CloudUploadIcon from '@mui/icons-material/CloudUpload';
import apiService from '../../services/apiService';

interface UpdateTenantLogoProps {
  onLogoUpdated: (newLogoUrl: string) => void;
}

const UpdateTenantLogo: React.FC<UpdateTenantLogoProps> = ({ onLogoUpdated }) => {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  // Handle file selection
  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files[0]) {
      const file = event.target.files[0];

      // Check if file is an image
      if (!file.type.match('image.*')) {
        setError('Please select an image file');
        return;
      }

      // Check file size (max 2MB)
      if (file.size > 2 * 1024 * 1024) {
        setError('File size should not exceed 2MB');
        return;
      }

      setSelectedFile(file);
      setError(null);

      // Create preview URL
      const reader = new FileReader();
      reader.onload = (e) => {
        if (e.target && e.target.result) {
          setPreviewUrl(e.target.result as string);
        }
      };
      reader.readAsDataURL(file);
    }
  };

  // Handle form submission
  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();

    if (!selectedFile) {
      setError('Please select a file');
      return;
    }

    setLoading(true);
    setError(null);
    setSuccess(false);

    // Get tenant data from localStorage
    const tenantStr = localStorage.getItem('tenant');
    let tenant = null;
    if (tenantStr) {
      try {
        tenant = JSON.parse(tenantStr);
      } catch (e) {
        console.error('Error parsing tenant data:', e);
      }
    }

    // Create form data
    const formData = new FormData();
    formData.append('logo', selectedFile);

    // Get token and schema_name from localStorage
    const token = localStorage.getItem('token');
    let schema_name = localStorage.getItem('schema_name');

    // If schema_name is not in localStorage, try to get it from tenant object
    if (!schema_name && tenant && tenant.schema_name) {
      schema_name = tenant.schema_name;
      // Store it in localStorage for future use
      localStorage.setItem('schema_name', schema_name);
    }

    console.log('Using schema_name:', schema_name);

    // Add schema_name to form data if available
    if (schema_name) {
      formData.append('schema_name', schema_name);
    } else {
      setError('Schema name not found. Please log out and log in again.');
      setLoading(false);
      return;
    }

    try {
      // Log debugging information
      console.log('Schema name:', schema_name);
      console.log('Form data:', formData);

      // Send request to update logo
      const response = await apiService.post(
        '/api/update-tenant-logo/',
        formData
      );

      // Handle success
      setSuccess(true);

      // Call the callback with the new logo URL
      if (response.data && response.data.logo_url) {
        onLogoUpdated(response.data.logo_url);

        // Update the logo URL in localStorage
        localStorage.setItem('logo_url', response.data.logo_url);

        // Update the tenant object in localStorage
        const tenantStr = localStorage.getItem('tenant');
        if (tenantStr) {
          const tenant = JSON.parse(tenantStr);
          tenant.logo_url = response.data.logo_url;
          localStorage.setItem('tenant', JSON.stringify(tenant));
        }
      }
    } catch (err) {
      // Handle error with more details
      console.error('Error updating logo:', err);

      // Get more detailed error information
      if (err.response) {
        // The request was made and the server responded with a status code
        // that falls out of the range of 2xx
        console.error('Error response data:', err.response.data);
        console.error('Error response status:', err.response.status);
        console.error('Error response headers:', err.response.headers);

        // Set a more detailed error message
        if (err.response.data && err.response.data.error) {
          setError(`Error: ${err.response.data.error}`);
        } else {
          setError(`Failed to update logo. Server returned status ${err.response.status}.`);
        }
      } else if (err.request) {
        // The request was made but no response was received
        console.error('Error request:', err.request);
        setError('Failed to update logo. No response received from server.');
      } else {
        // Something happened in setting up the request that triggered an Error
        console.error('Error message:', err.message);
        setError(`Failed to update logo: ${err.message}`);
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <Paper elevation={2} sx={{ p: 3, mb: 3 }}>
      <Typography variant="h6" gutterBottom>
        Update Tenant Logo
      </Typography>

      <Box component="form" onSubmit={handleSubmit} sx={{ mt: 2 }}>
        {/* File input */}
        <Box sx={{ mb: 2 }}>
          <input
            accept="image/*"
            style={{ display: 'none' }}
            id="logo-upload"
            type="file"
            onChange={handleFileChange}
          />
          <label htmlFor="logo-upload">
            <Button
              variant="outlined"
              component="span"
              startIcon={<CloudUploadIcon />}
              sx={{ mb: 2 }}
            >
              Select Logo
            </Button>
          </label>

          {selectedFile && (
            <Typography variant="body2" sx={{ ml: 1 }}>
              Selected: {selectedFile.name}
            </Typography>
          )}
        </Box>

        {/* Preview */}
        {previewUrl && (
          <Box sx={{ mb: 2, display: 'flex', justifyContent: 'center' }}>
            <Box
              sx={{
                width: 150,
                height: 150,
                border: '1px solid #ddd',
                borderRadius: 1,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                overflow: 'hidden',
                bgcolor: 'white'
              }}
            >
              <img
                src={previewUrl}
                alt="Logo Preview"
                style={{ maxWidth: '100%', maxHeight: '100%', objectFit: 'contain' }}
              />
            </Box>
          </Box>
        )}

        {/* Error message */}
        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        {/* Success message */}
        {success && (
          <Alert severity="success" sx={{ mb: 2 }}>
            Logo updated successfully!
          </Alert>
        )}

        {/* Submit button */}
        <Button
          type="submit"
          variant="contained"
          disabled={!selectedFile || loading}
          sx={{ mt: 1 }}
        >
          {loading ? (
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <CircularProgress size={20} sx={{ mr: 1, color: 'white' }} />
              Updating...
            </Box>
          ) : (
            'Update Logo'
          )}
        </Button>
      </Box>
    </Paper>
  );
};

export default UpdateTenantLogo;
