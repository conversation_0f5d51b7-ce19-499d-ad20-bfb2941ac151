/**
 * Schema Mapper Utility
 *
 * This utility provides functions to handle schema names and formats.
 * It helps solve the issue of users trying to log in with the wrong schema format.
 */

import { API_BASE_URL } from '../config/api';

/**
 * Get possible schema variations for a given schema name
 *
 * This function generates different variations of a schema name to try during login.
 * For example, if the schema is "kebele 14", it will generate ["kebele 14", "kebele_14", "kebele14"]
 *
 * @param schema The base schema name
 * @returns An array of schema variations to try
 */
export const getSchemaVariations = async (schema?: string): Promise<string[]> => {
  if (!schema) {
    // Get the current schema from localStorage
    const currentSchema = localStorage.getItem('jwt_schema') ||
                         localStorage.getItem('schema_name');

    // Try to get available tenants from the API
    try {
      const response = await fetch('/api/available-tenants/');
      if (response.ok) {
        const data = await response.json();
        if (data && Array.isArray(data)) {
          // Extract schema names from the tenant objects
          const availableTenants = data
            .filter(tenant => tenant.schema_name && tenant.schema_name !== 'public')
            .map(tenant => tenant.schema_name);

          console.log('Available tenants from API:', availableTenants);

          // If we have a current schema, prioritize it
          if (currentSchema) {
            // Remove it from the list if it's there
            const filteredTenants = availableTenants.filter(t => t !== currentSchema);
            // Add it to the beginning
            return [currentSchema, ...filteredTenants];
          }

          return availableTenants;
        }
      }
    } catch (error) {
      console.error('Error fetching available tenants:', error);
    }

    // Fallback if API call fails
    // If we have a current schema, use it as the only option
    if (currentSchema) {
      return [currentSchema];
    }

    // Last resort fallback - only used if everything else fails
    return [];
  }

  const variations: string[] = [];
  const lowerSchema = schema.toLowerCase();

  // Skip public schema
  if (lowerSchema === 'public') {
    return ['public'];
  }

  // Add the original schema
  variations.push(lowerSchema);

  // Add variations with spaces and underscores
  if (lowerSchema.includes(' ')) {
    // If it has spaces, add a version with underscores
    variations.push(lowerSchema.replace(/\s+/g, '_'));
    // Also add a version with no spaces
    variations.push(lowerSchema.replace(/\s+/g, ''));
  } else if (lowerSchema.includes('_')) {
    // If it has underscores, add a version with spaces
    variations.push(lowerSchema.replace(/_/g, ' '));
    // Also add a version with no underscores
    variations.push(lowerSchema.replace(/_/g, ''));
  }

  // For kebele schemas, add variations with different formats
  if (lowerSchema.startsWith('kebele') || lowerSchema.startsWith('kebele_') || lowerSchema.startsWith('kebele ')) {
    // Extract the kebele number
    const match = lowerSchema.match(/kebele[_ ]?(\d+)/i);
    if (match && match[1]) {
      const kebeleNumber = match[1];
      variations.push(`kebele ${kebeleNumber}`);
      variations.push(`kebele_${kebeleNumber}`);
      variations.push(`kebele${kebeleNumber}`);
    }
  }

  // For subcity schemas, add variations with different formats
  if (lowerSchema.startsWith('subcity') || lowerSchema.startsWith('subcity_') || lowerSchema.startsWith('subcity ')) {
    // Extract the subcity number
    const match = lowerSchema.match(/subcity[_ ]?(\d+)/i);
    if (match && match[1]) {
      const subcityNumber = match[1];
      variations.push(`subcity ${subcityNumber}`);
      variations.push(`subcity_${subcityNumber}`);
      variations.push(`subcity${subcityNumber}`);
    }
  }

  // Remove duplicates
  return [...new Set(variations)];
};

/**
 * Find the correct schema for a user
 *
 * This function tries to find the correct schema for a user by making an API call.
 * It's an async function that returns a promise.
 *
 * @param email The user's email
 * @param password The user's password
 * @returns A promise that resolves to the correct schema or undefined
 */
export const findSchemaForUser = async (email: string, password: string): Promise<string | undefined> => {
  try {
    // Make an API call to find the correct schema
    const response = await fetch(`${API_BASE_URL}/find-schema/`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email,
        password,
      }),
    });

    if (response.ok) {
      const data = await response.json();
      if (data.schema) {
        console.log(`Found schema for user ${email}: ${data.schema}`);
        return data.schema;
      }
    }

    return undefined;
  } catch (error) {
    console.error('Error finding schema for user:', error);
    return undefined;
  }
};

/**
 * Get the correct schema name format
 *
 * This function ensures that the schema name is in the correct format.
 * Some APIs expect spaces, others expect underscores.
 *
 * @param schema The schema name
 * @param format The format to use ('space' or 'underscore')
 * @returns The formatted schema name
 */
export const formatSchemaName = (schema: string | undefined, format: 'space' | 'underscore' = 'space'): string | undefined => {
  if (!schema) {
    return undefined;
  }

  if (format === 'space') {
    // Convert underscores to spaces
    return schema.replace(/_/g, ' ');
  } else {
    // Convert spaces to underscores
    return schema.replace(/\s+/g, '_');
  }
};

/**
 * Normalize a schema name to ensure it's in the correct format
 *
 * @param schema The schema name to normalize
 * @param preferredFormat Whether to prefer spaces or underscores in the output
 * @returns The normalized schema name
 */
export const normalizeSchemaName = (
  schema: string | undefined,
  preferredFormat: 'space' | 'underscore' = 'space'
): string | undefined => {
  if (!schema) {
    return undefined;
  }

  // Convert to lowercase
  const lowerSchema = schema.toLowerCase();

  // Handle special cases
  if (lowerSchema === 'public') {
    console.warn('Public schema is not recommended for authentication');
    return undefined;
  }

  // First convert to a standard format with spaces
  let normalizedSchema = lowerSchema;
  if (lowerSchema.startsWith('kebele_')) {
    normalizedSchema = lowerSchema.replace('kebele_', 'kebele ');
  }

  // Then convert to the preferred format
  if (preferredFormat === 'underscore') {
    return normalizedSchema.replace(/\s+/g, '_');
  }

  return normalizedSchema;
};
