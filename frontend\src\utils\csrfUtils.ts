/**
 * CSRF Utilities
 * 
 * This module provides utilities for handling CSRF tokens.
 */

/**
 * Add CSRF token to headers
 * @param headers Headers object to add CSRF token to
 */
export const addCsrfToken = (headers: Headers): void => {
  // Get CSRF token from cookie
  const csrfToken = document.cookie
    .split('; ')
    .find(row => row.startsWith('csrftoken='))
    ?.split('=')[1];

  // Add CSRF token to headers if available
  if (csrfToken) {
    headers.set('X-CSRFToken', csrfToken);
  }
};

/**
 * Get CSRF token from cookie
 * @returns CSRF token or null if not found
 */
export const getCsrfToken = (): string | null => {
  return document.cookie
    .split('; ')
    .find(row => row.startsWith('csrftoken='))
    ?.split('=')[1] || null;
};

export default {
  addCsrfToken,
  getCsrfToken
};
