from rest_framework.decorators import api_view, permission_classes, authentication_classes
from rest_framework.permissions import AllowAny
from rest_framework.authentication import TokenAuthentication
from rest_framework.response import Response
from rest_framework import status
from django.db import connection
from django.db.models import Q
from django_tenants.utils import tenant_context
from django.contrib.auth import get_user_model
from .models import Client
from citizens.models_family import Child, Parent, EmergencyContact, Spouse
from citizens.serializers_family import ChildSerializer, ParentSerializer, EmergencyContactSerializer, SpouseSerializer
import logging

logger = logging.getLogger(__name__)

@api_view(['GET', 'POST'])
@authentication_classes([TokenAuthentication])
@permission_classes([AllowAny])  # We'll handle authentication manually
def tenant_family_members(request, schema_name, relation_type, citizen_id=None):
    """
    Get or create family members for a specific citizen in a tenant.
    relation_type can be: children, parents, emergency_contacts, spouses
    """
    # Manual authentication (similar to tenant_citizens)
    auth_header = request.headers.get('Authorization', '')
    if not auth_header or not auth_header.startswith('Token '):
        return Response({"error": "Authentication credentials were not provided."}, status=status.HTTP_401_UNAUTHORIZED)
    
    token_key = auth_header.split(' ')[1]
    
    try:
        # Get the tenant by schema name
        tenant = Client.objects.get(schema_name=schema_name)
        
        # Set the tenant for this request
        connection.set_tenant(tenant)
        
        # Use tenant_context to ensure we're querying the right database
        with tenant_context(tenant):
            # Authenticate the user (similar to tenant_citizens)
            User = get_user_model()
            from rest_framework.authtoken.models import Token
            
            try:
                # First try to find the token in the tenant's database
                token = Token.objects.get(key=token_key)
                user = token.user
                request.user = user
            except Token.DoesNotExist:
                # If not found, try the public schema
                connection.set_schema_to_public()
                try:
                    token = Token.objects.get(key=token_key)
                    # Now switch back to the tenant schema
                    connection.set_tenant(tenant)
                    # Try to find the user in the tenant's database
                    try:
                        user = User.objects.get(email=token.user.email)
                        request.user = user
                    except User.DoesNotExist:
                        # If user doesn't exist in tenant, create one
                        try:
                            # Create a new user in the tenant's database
                            user = User.objects.create(
                                email=token.user.email,
                                first_name=token.user.first_name,
                                last_name=token.user.last_name,
                                is_staff=token.user.is_staff,
                                is_superuser=token.user.is_superuser,
                                is_active=token.user.is_active
                            )
                            # Set the password to the same as the public schema user
                            user.password = token.user.password
                            user.save()
                            request.user = user
                        except Exception as e:
                            return Response({"error": f"Error creating user in tenant: {str(e)}"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
                except Token.DoesNotExist:
                    return Response({"error": "Invalid token."}, status=status.HTTP_401_UNAUTHORIZED)
                except Exception as e:
                    return Response({"error": f"Error authenticating user: {str(e)}"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
            
            # Now we have an authenticated user and the correct tenant context
            
            # Determine the model and serializer based on relation_type
            if relation_type == 'children':
                model_class = Child
                serializer_class = ChildSerializer
                relation_field = 'citizen'
            elif relation_type == 'parents':
                model_class = Parent
                serializer_class = ParentSerializer
                relation_field = 'citizen'
            elif relation_type == 'emergency_contacts':
                model_class = EmergencyContact
                serializer_class = EmergencyContactSerializer
                relation_field = 'citizen'
            elif relation_type == 'spouses':
                model_class = Spouse
                serializer_class = SpouseSerializer
                relation_field = 'citizen'
            else:
                return Response({"error": f"Invalid relation type: {relation_type}"}, status=status.HTTP_400_BAD_REQUEST)
            
            # Handle GET request
            if request.method == 'GET':
                # If citizen_id is provided, filter by citizen
                if citizen_id:
                    family_members = model_class.objects.filter(**{relation_field: citizen_id})
                else:
                    family_members = model_class.objects.all()
                
                # Apply search filter if provided
                search_query = request.query_params.get('search', '')
                if search_query:
                    family_members = family_members.filter(
                        Q(first_name__icontains=search_query) |
                        Q(middle_name__icontains=search_query) |
                        Q(last_name__icontains=search_query) |
                        Q(first_name_am__icontains=search_query) |
                        Q(middle_name_am__icontains=search_query) |
                        Q(last_name_am__icontains=search_query) |
                        Q(phone__icontains=search_query) |
                        Q(email__icontains=search_query)
                    )
                
                # Serialize the data
                serializer = serializer_class(family_members, many=True, context={'request': request})
                return Response(serializer.data)
            
            # Handle POST request
            elif request.method == 'POST':
                # If citizen_id is provided in the URL, use it
                if citizen_id:
                    request.data[relation_field] = citizen_id
                
                serializer = serializer_class(data=request.data, context={'request': request})
                if serializer.is_valid():
                    serializer.save()
                    return Response(serializer.data, status=status.HTTP_201_CREATED)
                return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    except Client.DoesNotExist:
        return Response(
            {"error": f"Tenant with schema {schema_name} does not exist"},
            status=status.HTTP_404_NOT_FOUND
        )
    except Exception as e:
        logger.error(f"Error in tenant_family_members: {str(e)}")
        return Response(
            {"error": str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )
