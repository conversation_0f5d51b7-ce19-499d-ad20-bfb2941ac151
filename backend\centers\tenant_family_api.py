from rest_framework.decorators import api_view, permission_classes, authentication_classes
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from rest_framework import status
from django.db import connection
from django.db.models import Q
from django_tenants.utils import tenant_context
from django.contrib.auth import get_user_model
from .models import Client
from citizens.models_family import Child, Parent, EmergencyContact, Spouse
from citizens.serializers_family import ChildSerializer, ParentSerializer, EmergencyContactSerializer, SpouseSerializer
from accounts.jwt_utils import validate_jwt_token
import logging

logger = logging.getLogger(__name__)

@api_view(['GET', 'POST'])
@permission_classes([AllowAny])  # We'll handle authentication manually
def tenant_family_members(request, schema_name, relation_type, citizen_id=None):
    """
    Get or create family members for a specific citizen in a tenant.
    relation_type can be: children, parents, emergency_contacts, spouses
    """
    # Manual authentication using JWT
    auth_header = request.headers.get('Authorization', '')
    if not auth_header:
        return Response({"error": "Authentication credentials were not provided."}, status=status.HTTP_401_UNAUTHORIZED)

    # Only support Bearer authentication
    if not auth_header.startswith('Bearer '):
        logger.warning(f"Invalid authorization header format: {auth_header[:10]}...")
        return Response({
            "error": "Invalid authorization header format. Use 'Bearer <token>'.",
            "detail": "This API only supports JWT authentication with Bearer token."
        }, status=status.HTTP_401_UNAUTHORIZED)

    token_key = auth_header.split(' ')[1]
    logger.debug(f"Extracted token: {token_key[:10]}...")

    # Validate JWT token
    payload = validate_jwt_token(token_key)
    if not payload:
        logger.warning("Invalid JWT token")
        return Response({
            "error": "Invalid JWT token.",
            "detail": "The provided token is invalid or has expired."
        }, status=status.HTTP_401_UNAUTHORIZED)

    # Extract user ID from payload
    user_id = payload.get('sub')
    if not user_id:
        logger.warning("Invalid JWT token payload - missing 'sub' claim")
        return Response({
            "error": "Invalid JWT token payload.",
            "detail": "The token payload is missing required claims."
        }, status=status.HTTP_401_UNAUTHORIZED)

    # Get schema from payload
    token_schema = payload.get('schema')
    if token_schema and token_schema != schema_name:
        logger.warning(f"Token schema ({token_schema}) does not match requested schema ({schema_name})")
        # Continue anyway, but log the mismatch

    try:
        # Get the tenant by schema name
        tenant = Client.objects.get(schema_name=schema_name)

        # Set the tenant for this request
        connection.set_tenant(tenant)

        # Use tenant_context to ensure we're querying the right database
        with tenant_context(tenant):
            # Authenticate the user
            User = get_user_model()

            # JWT authentication
            try:
                logger.debug(f"Looking for user with ID {user_id} in tenant {schema_name}")
                # Try to find the user in the tenant's database using the user_id from JWT
                user = User.objects.get(id=user_id)
                logger.info(f"Found user {user.email} (ID: {user.id}) in tenant {schema_name}")
                request.user = user
            except User.DoesNotExist:
                logger.warning(f"User with ID {user_id} not found in tenant {schema_name}, checking public schema")
                # If not found in tenant, try the public schema
                connection.set_schema_to_public()
                try:
                    public_user = User.objects.get(id=user_id)
                    logger.info(f"Found user {public_user.email} (ID: {public_user.id}) in public schema")

                    # Switch back to tenant schema
                    connection.set_tenant(tenant)

                    # Try to find by email instead
                    try:
                        user = User.objects.get(email=public_user.email)
                        logger.info(f"Found user by email {user.email} in tenant {schema_name}")
                        request.user = user
                    except User.DoesNotExist:
                        logger.info(f"Creating user {public_user.email} in tenant {schema_name}")
                        # Create user in tenant if not exists
                        user = User.objects.create(
                            email=public_user.email,
                            first_name=public_user.first_name,
                            last_name=public_user.last_name,
                            is_staff=public_user.is_staff,
                            is_superuser=public_user.is_superuser,
                            is_active=public_user.is_active
                        )
                        user.password = public_user.password
                        user.save()
                        logger.info(f"Created user {user.email} (ID: {user.id}) in tenant {schema_name}")
                        request.user = user
                except User.DoesNotExist:
                    logger.error(f"User with ID {user_id} not found in any schema")
                    return Response({
                        "error": "User not found.",
                        "detail": "The user associated with this token does not exist in this tenant."
                    }, status=status.HTTP_401_UNAUTHORIZED)
                except Exception as e:
                    logger.error(f"Error finding user: {str(e)}")
                    return Response({
                        "error": f"Error finding user: {str(e)}",
                        "detail": "An error occurred while trying to find the user."
                    }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

            # Now we have an authenticated user and the correct tenant context

            # Determine the model and serializer based on relation_type
            if relation_type == 'children':
                model_class = Child
                serializer_class = ChildSerializer
                relation_field = 'citizen'
            elif relation_type == 'parents':
                model_class = Parent
                serializer_class = ParentSerializer
                relation_field = 'citizen'
            elif relation_type == 'emergency_contacts':
                model_class = EmergencyContact
                serializer_class = EmergencyContactSerializer
                relation_field = 'citizen'
            elif relation_type == 'spouses':
                model_class = Spouse
                serializer_class = SpouseSerializer
                relation_field = 'citizen'
            else:
                return Response({"error": f"Invalid relation type: {relation_type}"}, status=status.HTTP_400_BAD_REQUEST)

            # Handle GET request
            if request.method == 'GET':
                # If citizen_id is provided, filter by citizen
                if citizen_id:
                    family_members = model_class.objects.filter(**{relation_field: citizen_id})
                else:
                    family_members = model_class.objects.all()

                # Apply search filter if provided
                search_query = request.query_params.get('search', '')
                if search_query:
                    family_members = family_members.filter(
                        Q(first_name__icontains=search_query) |
                        Q(middle_name__icontains=search_query) |
                        Q(last_name__icontains=search_query) |
                        Q(first_name_am__icontains=search_query) |
                        Q(middle_name_am__icontains=search_query) |
                        Q(last_name_am__icontains=search_query) |
                        Q(phone__icontains=search_query) |
                        Q(email__icontains=search_query)
                    )

                # Serialize the data
                serializer = serializer_class(family_members, many=True, context={'request': request})
                return Response(serializer.data)

            # Handle POST request
            elif request.method == 'POST':
                # If citizen_id is provided in the URL, use it
                if citizen_id:
                    request.data[relation_field] = citizen_id

                serializer = serializer_class(data=request.data, context={'request': request})
                if serializer.is_valid():
                    serializer.save()
                    return Response(serializer.data, status=status.HTTP_201_CREATED)
                return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    except Client.DoesNotExist:
        return Response(
            {"error": f"Tenant with schema {schema_name} does not exist"},
            status=status.HTTP_404_NOT_FOUND
        )
    except Exception as e:
        logger.error(f"Error in tenant_family_members: {str(e)}")
        return Response(
            {"error": str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )
