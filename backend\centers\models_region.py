from django.db import models
from django.utils.text import slugify
import uuid
from django.core.validators import MinValueValidator

class Timestamp(models.Model):
    """
    Abstract base model that provides created_at and updated_at fields.
    """
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        abstract = True

class Country(Timestamp):
    """
    Model representing a country.
    """
    name = models.CharField(max_length=100, unique=True)
    code = models.CharField(max_length=3, unique=True, help_text="ISO 3-letter country code")
    flag = models.ImageField(upload_to='country_flags/', blank=True, null=True)

    def __str__(self):
        return f"{self.name} ({self.code})"

    class Meta:
        verbose_name = "Country"
        verbose_name_plural = "Countries"
        ordering = ['name']

class Region(Timestamp):
    """
    Model representing a region within a country.
    Cities are located within regions, but regions are not tenants.
    """
    country = models.ForeignKey(Country, on_delete=models.CASCADE, related_name='regions')
    name = models.Char<PERSON><PERSON>(max_length=100)
    code = models.CharField(max_length=5, unique=True, help_text="5-character unique code for the region")

    def __str__(self):
        return f"{self.name} ({self.code})"

    class Meta:
        verbose_name = "Region"
        verbose_name_plural = "Regions"
        ordering = ['name', 'code']

class CityAdministration(Timestamp):
    """
    Model representing a city administration with detailed information.
    """
    city_code = models.CharField(max_length=10, unique=True)
    country = models.ForeignKey(Country, on_delete=models.SET_NULL, null=True, blank=True)
    region = models.ForeignKey(Region, on_delete=models.SET_NULL, null=True, blank=True)
    city_name = models.CharField(max_length=100)
    logo = models.ImageField(upload_to='logos/', blank=True, null=True)
    motto_slogan = models.TextField(blank=True, null=True)
    city_intro = models.TextField(blank=True, null=True)
    mayor_name = models.CharField(max_length=100, blank=True, null=True)
    deputy_mayor = models.CharField(max_length=100, blank=True, null=True)
    contact_email = models.EmailField(blank=True, null=True)  # renamed to contact_email
    contact_phone = models.CharField(max_length=20, blank=True, null=True)  # renamed to contact_phone
    google_maps_url = models.URLField(blank=True, null=True)
    area_sq_km = models.FloatField(blank=True, null=True, validators=[MinValueValidator(0.0)])
    elevation_meters = models.IntegerField(blank=True, null=True)
    timezone = models.CharField(max_length=50, default="EAT")
    website = models.URLField(blank=True, null=True)
    headquarter_address = models.TextField(blank=True, null=True)
    postal_code = models.CharField(max_length=20, blank=True, null=True)
    established_date = models.DateField(blank=True, null=True)
    is_resident = models.BooleanField(default=False)  # Boolean field to check if the city is a resident
    is_active = models.BooleanField(default=True)  # Boolean field for activity status

    def __str__(self):
        return f"{self.city_name} ({self.city_code})"

    class Meta:
        verbose_name = "City Administration"
        verbose_name_plural = "City Administrations"
        ordering = ['city_name', 'city_code']
