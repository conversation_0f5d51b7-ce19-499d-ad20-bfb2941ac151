import { useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ThemeProvider } from '@mui/material/styles';
import DynamicThemeProvider from './components/theme/DynamicThemeProvider';
import { SnackbarProvider } from 'notistack';
import CssBaseline from '@mui/material/CssBaseline';
import theme from './theme';
import { runMigrationIfNeeded } from './utils/storageMigration';
// @ts-ignore
import { fixTokenStorage, protectTokenStorage, preserveCookies } from './utils/tokenFix';
import { PermissionsProvider } from './contexts/PermissionsContext';
import { AuthProvider } from './contexts/AuthContext';
import PublicRouteGuard from './components/PublicRouteGuard';
import TokenValidator from './components/TokenValidator';
import ProtectedRoute from './components/ProtectedRoute';
import NavigateProvider from './components/NavigateProvider';
import Layout from './components/layout/Layout';
import TenantLayout from './components/tenant/TenantLayout';
import CookiePreserver from './components/CookiePreserver';
import Home from './pages/Home';
import Login from './pages/Login';
import Dashboard from './pages/Dashboard';
import TenantDashboard from './pages/TenantDashboard';
import TenantRegistration from './pages/TenantRegistration';
import TenantSelection from './pages/TenantSelection';
import IDCardRegistration from './pages/IDCardRegistration';
import IDCardsList from './components/IDCardsList_Fixed';
import IDCardDetails from './pages/IDCardDetails';
import RegisterCitizen from './pages/RegisterCitizen';
import SimpleCitizenRegistration from './pages/SimpleCitizenRegistration';
import CitizenApiTest from './pages/CitizenApiTest';
import CitizensList from './pages/CitizensList';
// import CitizensListNew from './pages/CitizensListNew';
// import CitizensListSimple from './pages/CitizensListSimple';
import CitizenDetails from './pages/CitizenDetails';
import PrintIDCards from './pages/PrintIDCards_Fixed';
import PrintCards from './pages/PrintCards';
import Settings from './pages/Settings';
import TestCitizenDetails from './pages/TestCitizenDetails';
import AddSpouseWrapper from './pages/AddSpouseWrapper';
import AddParent from './pages/AddParent';
import AddChild from './pages/AddChild';
import AddEmergencyContact from './pages/AddEmergencyContact';
import UploadDocuments from './pages/UploadDocuments';
import DocumentVerification from './pages/DocumentVerification';
import KebeleUserManagement from './pages/KebeleUserManagement';
import SubcityUserManagement from './pages/SubcityUserManagement';
import TokenTest from './pages/TokenTest';
import Unauthorized from './pages/Unauthorized';
import ApiTestPage from './pages/ApiTestPage';
import TokenRaceTest from './pages/TokenRaceTest';
import TokenStorageTest from './pages/TokenStorageTest';
import TokenFixPage from './pages/TokenFixPage';

// Placeholder components for other pages
const About = () => <div style={{ padding: '100px 20px', textAlign: 'center' }}>About Page</div>;
const Contact = () => <div style={{ padding: '100px 20px', textAlign: 'center' }}>Contact Page</div>;
const Register = () => <div style={{ padding: '100px 20px', textAlign: 'center' }}>Register Page</div>;
const NotFound = () => <div style={{ padding: '100px 20px', textAlign: 'center' }}>404 - Page Not Found</div>;

// Placeholder components for tenant pages
const Citizens = () => <div style={{ padding: '20px' }}>Citizens Management Page</div>;
const SearchPage = () => <div style={{ padding: '20px' }}>Search Page</div>;
// PrintIDCards component is now imported above
const Reports = () => <div style={{ padding: '20px' }}>Reports Page</div>;
// Settings component is now imported above
const Profile = () => <div style={{ padding: '20px' }}>User Profile Page</div>;
const Help = () => <div style={{ padding: '20px' }}>Help Page</div>;

function App() {
  // EMERGENCY FIX: Completely disable all automatic refresh mechanisms
  // This is a temporary fix to stop the infinite refresh loop

  // Create a global navigate function for use in non-component code
  useEffect(() => {
    // Import the navigate function from react-router-dom
    import('react-router-dom').then(({ useNavigate }) => {
      // Create a custom hook to get the navigate function
      const NavigateProvider = () => {
        const navigate = useNavigate();

        // Store the navigate function in the window object
        (window as any).__navigateFunction = navigate;

        return null;
      };

      // Render the NavigateProvider component
      const root = document.createElement('div');
      root.style.display = 'none';
      document.body.appendChild(root);

      // We can't directly render here, so we'll set a flag for later
      (window as any).__needsNavigateProvider = true;
    }).catch(error => {
      console.error('Error importing useNavigate:', error);
    });
  }, []);

  // Initialize token stores and token validation
  useEffect(() => {
    console.log('Initializing application...');

    // Call runMigrationIfNeeded for backward compatibility
    try {
      runMigrationIfNeeded();
    } catch (error) {
      console.error('Error during storage initialization:', error);
    }

    // Fix token storage issues
    try {
      console.log('Fixing token storage issues...');
      const fixResult = fixTokenStorage();
      console.log('Token storage fix result:', fixResult);

      // Protect token storage from being cleared
      const protectResult = protectTokenStorage();
      console.log('Token storage protection result:', protectResult);

      // Preserve cookies
      const preserveResult = preserveCookies();
      console.log('Cookie preservation result:', preserveResult);
    } catch (error) {
      console.error('Error fixing token storage:', error);
    }

    // Check if we're in an infinite refresh loop
    const lastRefreshTime = localStorage.getItem('last_refresh_time');
    const currentTime = Date.now();
    const refreshCount = parseInt(localStorage.getItem('refresh_count') || '0');

    if (lastRefreshTime) {
      const timeSinceLastRefresh = currentTime - parseInt(lastRefreshTime);

      // If less than 1 second since last refresh, we might be in a loop
      if (timeSinceLastRefresh < 1000) {
        // Increment refresh count
        const newRefreshCount = refreshCount + 1;
        localStorage.setItem('refresh_count', newRefreshCount.toString());

        // Only clear storage if we've refreshed more than 5 times in quick succession
        if (newRefreshCount > 5) {
          console.error(`Possible refresh loop detected! (${newRefreshCount} refreshes) Clearing all storage...`);

          // Clear all storage to break the loop
          try {
            localStorage.clear();
            console.log('All storage cleared to break refresh loop');

            // Add a flag to show a message on the login page
            localStorage.setItem('emergency_reset', 'true');
          } catch (error) {
            console.error('Error clearing storage:', error);
          }
        } else {
          console.warn(`Quick refreshes detected (${newRefreshCount}), but not clearing storage yet`);
        }
      } else {
        // Reset refresh count if it's been more than 1 second
        localStorage.setItem('refresh_count', '1');
      }
    } else {
      // First refresh, set count to 1
      localStorage.setItem('refresh_count', '1');
    }

    // Record this refresh time
    localStorage.setItem('last_refresh_time', currentTime.toString());

    // Initialize token service
    try {
      // Import and initialize the token service
      import('./services/tokenService').then(() => {
        console.log('Token service initialized');
      }).catch(error => {
        console.error('Error initializing token service:', error);
      });

      // Then import and run the token initializer
      import('./utils/tokenInitializer').then(module => {
        // Initialize tokens for the application
        module.initializeTokens();
        console.log('Tokens initialized');
      }).catch(error => {
        console.error('Error importing token initializer:', error);
      });
    } catch (error) {
      console.error('Error initializing token stores:', error);
    }

    // Check if we're on a protected route and need to validate the token
    // First, define public routes that don't need authentication
    const publicRoutes = [
      '/login',
      '/register',
      '/',
      '/about',
      '/contact',
      '/tenant-registration',
      '/tenant-selection',
      '/admin-dashboard',
      '/test-citizen-details',
      '/token-test',
      '/api-test',
      '/token-race-test',
      '/token-storage-test',
      '/token-fix',
      '/unauthorized'
    ];

    // Check if current path is a public route
    const currentPath = window.location.pathname;
    const isPublicRoute = publicRoutes.some(route =>
      route === '/' ? currentPath === '/' : currentPath.startsWith(route)
    );

    // Set the public route flag
    if (isPublicRoute) {
      localStorage.setItem('is_public_route', 'true');
    } else {
      localStorage.removeItem('is_public_route');
    }

    // A route is protected if it's not a public route
    const isProtectedRoute = !isPublicRoute;

    // Always synchronize cookies, but only validate tokens for protected routes
    import('./services/tokenManager').then(async ({ tokenManager }) => {
      console.log('App: Synchronizing cookies');
      tokenManager.synchronizeCookies();

      // Only validate tokens for protected routes
      if (isProtectedRoute) {
        console.log('App: On protected route, validating token');

        // Then import and use the auth redirect utility
        try {
          const module = await import('./utils/authRedirect');

          // Get the current schema from localStorage or cookies
          let schema = localStorage.getItem('schema_name') || localStorage.getItem('jwt_schema');

          // If no schema in localStorage, check cookies
          if (!schema) {
            console.log('App: No schema in localStorage, checking cookies');

            // Parse cookies
            const cookies: Record<string, string> = {};
            document.cookie.split(';').forEach(cookie => {
              const [name, value] = cookie.trim().split('=');
              if (name) {
                cookies[name] = decodeURIComponent(value || '');
              }
            });

            // Check for schema in cookies
            if (cookies['schema_name']) {
              console.log('App: Found schema in cookies');
              schema = cookies['schema_name'];
              localStorage.setItem('schema_name', schema);
            }
          }

          if (schema) {
            // Check if a token exists for this schema
            module.checkTokenExists(schema);
          } else {
            // No schema, redirect to login
            module.redirectToLogin('no_schema');
          }
        } catch (error) {
          console.error('Error importing auth redirect utility:', error);
        }
      } else {
        console.log('App: On public route, skipping token validation');
      }
    }).catch(error => {
      console.error('Error importing tokenManager:', error);
    });

    return () => {
      console.log('App component unmounted');
    };
  }, []);

  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <SnackbarProvider maxSnack={3}>
        <PermissionsProvider>
          <Router>
            <NavigateProvider />
            <CookiePreserver />
            <PublicRouteGuard />
            <AuthProvider>
              <DynamicThemeProvider>
            <Routes>
              {/* Public routes with standard layout */}
              <Route path="/" element={<Layout><Home /></Layout>} />
              <Route path="/about" element={<Layout><About /></Layout>} />
              <Route path="/contact" element={<Layout><Contact /></Layout>} />
              <Route path="/login" element={<Layout><Login /></Layout>} />
              <Route path="/register" element={<Layout><Register /></Layout>} />
              <Route path="/tenant-registration" element={<Layout><TenantRegistration /></Layout>} />
              <Route path="/tenant-selection" element={<Layout><TenantSelection /></Layout>} />
              <Route path="/admin-dashboard" element={<Layout><Dashboard /></Layout>} />
             
              <Route path="/unauthorized" element={<Layout><Unauthorized /></Layout>} />

              {/* Tenant routes with tenant layout and JWT authentication */}
              <Route path="/dashboard" element={<ProtectedRoute><TenantLayout><TenantDashboard /></TenantLayout></ProtectedRoute>} />

              {/* Citizen management routes */}
              <Route path="/citizens" element={<ProtectedRoute><TenantLayout><CitizensList /></TenantLayout></ProtectedRoute>} />
              {/* <Route path="/citizens-new" element={<ProtectedRoute><TenantLayout><CitizensListNew /></TenantLayout></ProtectedRoute>} /> */}
              {/* <Route path="/citizens-simple" element={<ProtectedRoute><TenantLayout><CitizensListSimple /></TenantLayout></ProtectedRoute>} /> */}
              <Route path="/citizens/new" element={<ProtectedRoute><TenantLayout><RegisterCitizen /></TenantLayout></ProtectedRoute>} />
              <Route path="/citizens/simple" element={<ProtectedRoute><TenantLayout><SimpleCitizenRegistration /></TenantLayout></ProtectedRoute>} />
              <Route path="/citizens/api-test" element={<ProtectedRoute><TenantLayout><CitizenApiTest /></TenantLayout></ProtectedRoute>} />
              <Route path="/citizens/:id" element={<ProtectedRoute><TenantLayout><CitizenDetails /></TenantLayout></ProtectedRoute>} />
              <Route path="/citizens/:id/add-spouse" element={<ProtectedRoute><TenantLayout><AddSpouseWrapper /></TenantLayout></ProtectedRoute>} />
              <Route path="/citizens/:id/add-parent" element={<ProtectedRoute><TenantLayout><AddParent /></TenantLayout></ProtectedRoute>} />
              <Route path="/citizens/:id/add-child" element={<ProtectedRoute><TenantLayout><AddChild /></TenantLayout></ProtectedRoute>} />
              <Route path="/citizens/:id/add-emergency-contact" element={<ProtectedRoute><TenantLayout><AddEmergencyContact /></TenantLayout></ProtectedRoute>} />
              <Route path="/citizens/:id/upload-documents" element={<ProtectedRoute><TenantLayout><UploadDocuments /></TenantLayout></ProtectedRoute>} />

              {/* ID Card management routes */}
              <Route path="/id-cards" element={<ProtectedRoute><TenantLayout><IDCardsList /></TenantLayout></ProtectedRoute>} />
              <Route path="/id-cards/new" element={<ProtectedRoute><TenantLayout><IDCardRegistration /></TenantLayout></ProtectedRoute>} />
              <Route path="/id-cards/:id" element={<ProtectedRoute><TenantLayout><IDCardDetails /></TenantLayout></ProtectedRoute>} />
              <Route path="/search" element={<ProtectedRoute><TenantLayout><SearchPage /></TenantLayout></ProtectedRoute>} />

              {/* Subcity admin routes */}
              <Route path="/print-cards" element={<ProtectedRoute><TenantLayout><PrintCards /></TenantLayout></ProtectedRoute>} />
              <Route path="/kebele-users" element={<ProtectedRoute><TenantLayout><KebeleUserManagement /></TenantLayout></ProtectedRoute>} />

              {/* Kebele leader routes */}
              <Route path="/document-verification" element={<ProtectedRoute><TenantLayout><DocumentVerification /></TenantLayout></ProtectedRoute>} />

              {/* City admin routes */}
              <Route path="/subcity-users" element={<ProtectedRoute><TenantLayout><SubcityUserManagement /></TenantLayout></ProtectedRoute>} />

              {/* Common routes for all roles */}
              <Route path="/reports" element={<ProtectedRoute><TenantLayout><Reports /></TenantLayout></ProtectedRoute>} />
              <Route path="/settings" element={<ProtectedRoute><TenantLayout><Settings /></TenantLayout></ProtectedRoute>} />
              <Route path="/profile" element={<ProtectedRoute><TenantLayout><Profile /></TenantLayout></ProtectedRoute>} />
              <Route path="/help" element={<ProtectedRoute><TenantLayout><Help /></TenantLayout></ProtectedRoute>} />

              {/* Fallback route */}
              <Route path="*" element={<Layout><NotFound /></Layout>} />
            </Routes>
          </DynamicThemeProvider>
            </AuthProvider>
          </Router>
        </PermissionsProvider>
      </SnackbarProvider>
    </ThemeProvider>
  );
}

export default App;


