/**
 * Utility functions for handling tenant information
 */

/**
 * Gets tenant information from the API
 * @param schemaName The schema name to get tenant info for
 * @returns Promise<any> The tenant information
 */
export const getTenantInfo = async (schemaName: string): Promise<any> => {
  try {
    console.log(`Getting tenant info for schema: ${schemaName}`);

    // Format the schema name for URL (replacing spaces with underscores)
    const formattedSchema = schemaName.replace(/\s+/g, '_');

    // Call the tenant-info endpoint
    const response = await fetch(`/api/tenant-info/${encodeURIComponent(formattedSchema)}/`);

    if (!response.ok) {
      console.error(`Failed to get tenant info for ${schemaName}:`, response.status, response.statusText);
      return null;
    }

    const data = await response.json();
    console.log(`Tenant info for ${schemaName}:`, data);

    return data;
  } catch (error) {
    console.error(`Error getting tenant info for ${schemaName}:`, error);
    return null;
  }
};

/**
 * Gets the correct schema name for a token
 * @param token The token to check
 * @returns Promise<string|null> The correct schema name or null if not found
 */
export const getCorrectSchemaForToken = async (token: string): Promise<string|null> => {
  // First try to get available tenants from the API
  let availableTenants: string[] = [];

  try {
    // Fetch available tenants from the API
    const response = await fetch('/api/available-tenants/');
    if (response.ok) {
      const data = await response.json();
      if (data && Array.isArray(data)) {
        // Extract schema names from the tenant objects
        availableTenants = data
          .filter(tenant => tenant.schema_name && tenant.schema_name !== 'public')
          .map(tenant => tenant.schema_name);

        console.log('Available tenants from API:', availableTenants);
      }
    }
  } catch (error) {
    console.error('Error fetching available tenants:', error);
  }

  // If we couldn't get tenants from the API, use the current schema as a fallback
  if (availableTenants.length === 0) {
    console.log('No tenants found from API, using fallback approach');

    // Get the current schema from localStorage
    const currentSchema = localStorage.getItem('jwt_schema') ||
                         localStorage.getItem('schema_name');

    if (currentSchema) {
      availableTenants = [currentSchema];
    } else {
      // If we still don't have any schemas, use a minimal set of test schemas
      // This is only for development/testing and should not be relied upon in production
      console.warn('No schema found in storage, using minimal test set');
      availableTenants = ['kebele 16'];
    }
  }

  // Ensure the current schema is prioritized if it exists
  const currentSchema = localStorage.getItem('jwt_schema') ||
                       localStorage.getItem('schema_name');

  if (currentSchema && availableTenants.includes(currentSchema)) {
    // Remove it from the list
    availableTenants = availableTenants.filter(schema => schema !== currentSchema);
    // Add it to the beginning
    availableTenants.unshift(currentSchema);
  }

  // Generate variations for each schema (with spaces, underscores, etc.)
  const schemasWithVariations: string[] = [];
  availableTenants.forEach(schema => {
    // Add the original schema
    schemasWithVariations.push(schema);

    // Add variations with spaces and underscores
    if (schema.includes(' ')) {
      // If it has spaces, add a version with underscores
      schemasWithVariations.push(schema.replace(/\s+/g, '_'));
      // Also add a version with no spaces
      schemasWithVariations.push(schema.replace(/\s+/g, ''));
    } else if (schema.includes('_')) {
      // If it has underscores, add a version with spaces
      schemasWithVariations.push(schema.replace(/_/g, ' '));
      // Also add a version with no underscores
      schemasWithVariations.push(schema.replace(/_/g, ''));
    }
  });

  // Remove duplicates
  const uniqueSchemas = [...new Set(schemasWithVariations)];

  // Try each schema
  for (const schema of uniqueSchemas) {
    try {
      console.log(`Checking if token is valid in schema: ${schema}`);

      // Format the schema name for URL
      const formattedSchema = schema.replace(/\s+/g, '_');

      // Try to fetch tenant info first to make sure the schema exists
      const tenantInfo = await getTenantInfo(schema);
      if (!tenantInfo) {
        console.log(`No tenant info found for schema: ${schema}`);
        continue;
      }

      // Try multiple endpoints to check if token is valid
      const endpoints = [
        `/api/tenant/${formattedSchema}/citizens/count/`,
        `/api/tenant/${formattedSchema}/citizens/?limit=1`,
        `/api/tenant/${formattedSchema}/citizens/`,
        `/api/tenant/${formattedSchema}/`
      ];

      let isValid = false;

      for (const endpoint of endpoints) {
        try {
          console.log(`Trying endpoint: ${endpoint}`);

          const response = await fetch(endpoint, {
            method: 'GET',
            headers: {
              'Authorization': `Token ${token}`,
              'X-Schema-Name': schema,
              'Accept': 'application/json'
            },
            credentials: 'include'
          });

          // If we get a 200 response, the token is valid in this schema
          if (response.ok) {
            console.log(`Token is valid in schema: ${schema} (endpoint: ${endpoint})`);
            isValid = true;
            break;
          }

          console.log(`Token is not valid in schema: ${schema} (endpoint: ${endpoint})`);
        } catch (endpointError) {
          console.error(`Error checking endpoint ${endpoint} in schema ${schema}:`, endpointError);
        }
      }

      if (isValid) {
        return schema;
      }
    } catch (error) {
      console.error(`Error checking token in schema ${schema}:`, error);
    }
  }

  // If all else fails, return null and let the caller handle it
  console.warn('Could not find a valid schema for the token');
  return null;
};

/**
 * Updates the current schema in localStorage and cookies
 * @param schemaName The schema name to set
 */
export const updateCurrentSchema = (schemaName: string): void => {
  if (!schemaName) return;

  console.log(`Updating current schema to: ${schemaName}`);

  // Store in both localStorage and sessionStorage for better compatibility
  localStorage.setItem('schema_name', schemaName);
  sessionStorage.setItem('schema_name', schemaName);
  sessionStorage.setItem('jwt_schema', schemaName);

  // Set as cookie with proper attributes
  document.cookie = `schema_name=${encodeURIComponent(schemaName)}; path=/; SameSite=Lax`;

  // Also update the tenant info in sessionStorage if possible
  getTenantInfo(schemaName).then(tenantInfo => {
    if (tenantInfo) {
      sessionStorage.setItem('tenant', JSON.stringify(tenantInfo));
      console.log('Updated tenant info in sessionStorage');
    }
  }).catch(error => {
    console.error('Error updating tenant info:', error);
  });
};

export default {
  getTenantInfo,
  getCorrectSchemaForToken,
  updateCurrentSchema
};
