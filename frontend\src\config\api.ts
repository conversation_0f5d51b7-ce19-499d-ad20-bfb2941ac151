// API configuration
// Use direct URL to backend server
export const API_BASE_URL = 'http://localhost:8000';
// Don't use hardcoded tokens - this is a security risk
// export const API_TOKEN = '01aa7be65fbda335a0b29edd56c967ad6112fa6b';

// Helper function to create API URLs
export const createApiUrl = (endpoint: string): string => {
  // If the endpoint already starts with http, return it as is
  if (endpoint.startsWith('http')) {
    return endpoint;
  }

  // If the endpoint already starts with /api, append it to the base URL
  if (endpoint.startsWith('/api')) {
    return `${API_BASE_URL}${endpoint}`;
  }

  // Otherwise, append /api/ and the endpoint to the base URL
  return `${API_BASE_URL}/api/${endpoint}`;
};

// Helper function to create tenant-specific API URLs
export const createTenantApiUrl = (schema: string, endpoint: string): string => {
  // Normalize the endpoint (remove leading /api/ if present)
  const normalizedEndpoint = endpoint.replace(/^\/api\//, '');

  // Format schema for API endpoint (replace spaces with underscores)
  const formattedSchema = schema.replace(/\s+/g, '_');

  // Create the tenant-specific URL
  return `${API_BASE_URL}/api/tenant/${encodeURIComponent(formattedSchema)}/${normalizedEndpoint}`;
};

// Helper function to create common API URLs
export const createCommonApiUrl = (endpoint: string): string => {
  // Normalize the endpoint (remove leading /api/ if present)
  const normalizedEndpoint = endpoint.replace(/^\/api\//, '');

  // Create the common API URL
  return `${API_BASE_URL}/api/common/${normalizedEndpoint}`;
};

// Default headers for API requests
export const getDefaultHeaders = () => {
  // Get JWT token from localStorage - try all possible storage locations
  const token = localStorage.getItem('jwt_access_token') ||
                localStorage.getItem(`jwt_access_token_${localStorage.getItem('schema_name')}`);

  console.log('getDefaultHeaders: JWT token found:', token ? 'Yes' : 'No');
  if (token) {
    console.log('getDefaultHeaders: token first 10 chars:', token.substring(0, 10) + '...');
  }

  // Get schema from localStorage
  const schema = localStorage.getItem('schema_name') ||
                 localStorage.getItem('jwt_schema');

  console.log('getDefaultHeaders: schema found:', schema ? 'Yes' : 'No');
  console.log('getDefaultHeaders: schema value:', schema);

  const headers: Record<string, string> = {
    'Content-Type': 'application/json'
  };

  // Add Authorization header if token exists
  if (token) {
    // Use Bearer prefix for JWT tokens
    headers['Authorization'] = `Bearer ${token.replace(/\s+/g, '')}`;
    console.log('getDefaultHeaders: Added auth header with Bearer prefix for JWT token');
  } else {
    console.error('getDefaultHeaders: No JWT token found in localStorage!');
  }

  // Add X-Schema-Name header if schema exists
  if (schema) {
    headers['X-Schema-Name'] = schema;
    console.log('getDefaultHeaders: Added X-Schema-Name header with value:', schema);
  } else {
    console.error('getDefaultHeaders: No schema found in localStorage!');
  }

  console.log('getDefaultHeaders: Final headers:', headers);
  return headers;
};
