/**
 * Utility functions for token validation
 */

// Base API URL
const API_BASE_URL = 'http://localhost:8000';

/**
 * Validates a token using the dedicated validation endpoint
 * @param token The token to validate
 * @returns Promise<boolean> - True if token is valid, false otherwise
 */
export const validateToken = async (token: string): Promise<boolean> => {
  if (!token) {
    console.error('No token provided for validation');
    return false;
  }

  try {
    console.log('Validating token with dedicated endpoint');
    const response = await fetch(`${API_BASE_URL}/api/validate-token/`, {
      method: 'GET',
      headers: {
        'Authorization': `Token ${token}`,
        'Content-Type': 'application/json'
      }
    });

    // If the response is 200 OK, the token is valid
    if (response.ok) {
      const data = await response.json();
      console.log('Token validation response:', data);
      return true;
    }

    // If the response is 401 Unauthorized, the token is invalid
    if (response.status === 401) {
      console.error('Token validation failed: Unauthorized');
      return false;
    }

    // For other status codes, log the error and return false
    console.error(`Token validation failed with status ${response.status}`);
    return false;
  } catch (error) {
    console.error('Error validating token:', error);

    // Fallback to trying other endpoints if the dedicated endpoint fails
    return validateTokenFallback(token);
  }
};

/**
 * Fallback token validation that tries multiple endpoints
 * @param token The token to validate
 * @returns Promise<boolean> - True if token is valid, false otherwise
 */
const validateTokenFallback = async (token: string): Promise<boolean> => {
  // List of endpoints to try for validation
  const endpoints = [
    '/api/common/countries/',
    '/api/common/regions/',
    '/api/common/ketenas/',
    '/api/common/religions/',
    '/api/common/marital-statuses/'
  ];

  // Try each endpoint until one succeeds
  for (const endpoint of endpoints) {
    try {
      console.log(`Trying fallback token validation with endpoint: ${endpoint}`);
      const response = await fetch(`${API_BASE_URL}${endpoint}`, {
        method: 'GET',
        headers: {
          'Authorization': `Token ${token}`,
          'Content-Type': 'application/json'
        }
      });

      // If the response is 200 OK, the token is valid
      if (response.ok) {
        console.log(`Token validated successfully with fallback endpoint: ${endpoint}`);
        return true;
      }

      // If the response is 401 Unauthorized, the token is invalid
      if (response.status === 401) {
        console.error(`Token validation failed with fallback endpoint: ${endpoint}`);
        return false;
      }

      // For other status codes, try the next endpoint
      console.warn(`Fallback endpoint ${endpoint} returned status ${response.status}, trying next endpoint`);
    } catch (error) {
      console.error(`Error validating token with fallback endpoint ${endpoint}:`, error);
      // Continue to the next endpoint
    }
  }

  // If all endpoints failed, the token is invalid
  console.error('All token validation fallback endpoints failed');
  return false;
};

/**
 * Gets a valid token from localStorage and validates it
 * @param redirectOnFailure Whether to redirect to login page on failure
 * @returns Promise<string | null> - Valid token or null
 */
export const getValidToken = async (redirectOnFailure: boolean = true): Promise<string | null> => {
  const token = localStorage.getItem('token');

  if (!token) {
    console.error('No token found in localStorage');
    if (redirectOnFailure) {
      window.location.href = '/login?error=no_token';
    }
    return null;
  }

  const isValid = await validateToken(token);

  if (isValid) {
    return token;
  } else {
    console.error('Token validation failed');
    if (redirectOnFailure) {
      // Clear authentication data
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      localStorage.removeItem('tenant');
      localStorage.removeItem('schema_name');

      // Redirect to login page
      window.location.href = '/login?error=session_expired';
    }
    return null;
  }
};

/**
 * Creates headers with authentication token for API requests
 * @param token The token to use for authentication
 * @param includeContentType Whether to include Content-Type header
 * @returns Headers object
 */
export const createAuthHeaders = (token: string, includeContentType: boolean = true): HeadersInit => {
  const headers: HeadersInit = {
    'Authorization': `Token ${token}`
  };

  if (includeContentType) {
    headers['Content-Type'] = 'application/json';
  }

  return headers;
};

/**
 * Makes an authenticated API request
 * @param url The URL to fetch
 * @param options The fetch options
 * @param validateTokenFirst Whether to validate the token before making the request
 * @returns Promise with the response
 */
export const authenticatedFetch = async (
  url: string,
  options: RequestInit = {},
  validateTokenFirst: boolean = true
): Promise<Response> => {
  const token = validateTokenFirst
    ? await getValidToken()
    : localStorage.getItem('token');

  if (!token) {
    throw new Error('Authentication failed. Please log in again.');
  }

  // Create default headers with authentication
  const headers = createAuthHeaders(token, options.body instanceof FormData ? false : true);

  // Merge with any headers provided in options
  const mergedHeaders = {
    ...headers,
    ...(options.headers || {})
  };

  // Get CSRF token if needed
  const csrftoken = document.cookie
    .split('; ')
    .find(row => row.startsWith('csrftoken='))
    ?.split('=')[1] || '';

  if (csrftoken) {
    mergedHeaders['X-CSRFToken'] = csrftoken;
  }

  // Make the request with authentication
  const response = await fetch(url, {
    ...options,
    headers: mergedHeaders,
    credentials: 'include'
  });

  // Handle 401 Unauthorized errors
  if (response.status === 401) {
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    localStorage.removeItem('tenant');
    localStorage.removeItem('schema_name');

    window.location.href = '/login?error=session_expired';
    throw new Error('Authentication failed. Please log in again.');
  }

  return response;
};
