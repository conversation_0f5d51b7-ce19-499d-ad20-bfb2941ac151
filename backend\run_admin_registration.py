import os
import django

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'neocamelot.settings')
django.setup()

# Import models and admin
from django.contrib import admin
from citizens.models import Citizen
from idcards.models import IDCard, IDCardTemplate
from global_admin import GlobalCitizenAdmin, GlobalIDCardTemplateAdmin, GlobalIDCardAdmin

print("Registering models in the admin panel...")

# Unregister models if they are already registered
try:
    admin.site.unregister(Citizen)
    print("Unregistered Citizen model")
except admin.sites.NotRegistered:
    print("Citizen model was not registered")

try:
    admin.site.unregister(IDCardTemplate)
    print("Unregistered IDCardTemplate model")
except admin.sites.NotRegistered:
    print("IDCardTemplate model was not registered")

try:
    admin.site.unregister(IDCard)
    print("Unregistered IDCard model")
except admin.sites.NotRegistered:
    print("IDCard model was not registered")

# Register models with global admin classes
admin.site.register(Citizen, GlobalCitizenAdmin)
print("Registered Citizen model with GlobalCitizenAdmin")

admin.site.register(IDCardTemplate, GlobalIDCardTemplateAdmin)
print("Registered IDCardTemplate model with GlobalIDCardTemplateAdmin")

admin.site.register(IDCard, GlobalIDCardAdmin)
print("Registered IDCard model with GlobalIDCardAdmin")

# Test the global admin classes
print("\nTesting GlobalCitizenAdmin...")
citizen_admin = GlobalCitizenAdmin(Citizen, admin.site)
citizens = citizen_admin.get_queryset(None)
print(f"Found {len(citizens)} citizens across all tenants")

print("\nTesting GlobalIDCardTemplateAdmin...")
template_admin = GlobalIDCardTemplateAdmin(IDCardTemplate, admin.site)
templates = template_admin.get_queryset(None)
print(f"Found {len(templates)} ID card templates across all tenants")

print("\nTesting GlobalIDCardAdmin...")
card_admin = GlobalIDCardAdmin(IDCard, admin.site)
cards = card_admin.get_queryset(None)
print(f"Found {len(cards)} ID cards across all tenants")

print("\nDone! The models are now registered in the admin panel.")
print("You can now access citizens, ID cards, and ID card templates in the admin panel.")
print("Please restart the Django server for the changes to take effect.")
