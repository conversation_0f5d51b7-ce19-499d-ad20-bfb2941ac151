import React, { useEffect } from 'react';
import { useLocation } from 'react-router-dom';

/**
 * PublicRouteGuard Component
 * 
 * This component prevents authentication redirects on public routes.
 * It sets a flag in localStorage that tells the application not to
 * redirect to login when on public routes.
 */
const PublicRouteGuard: React.FC = () => {
  const location = useLocation();

  useEffect(() => {
    // Define public routes that don't need authentication
    const publicRoutes = [
      '/login',
      '/register',
      '/',
      '/about',
      '/contact',
      '/tenant-registration',
      '/tenant-selection',
      '/admin-dashboard',
      '/test-citizen-details',
      '/token-test',
      '/api-test',
      '/token-race-test',
      '/token-storage-test',
      '/token-fix',
      '/unauthorized'
    ];
    
    // Check if current path is a public route
    const currentPath = location.pathname;
    const isPublicRoute = publicRoutes.some(route => 
      route === '/' ? currentPath === '/' : currentPath.startsWith(route)
    );
    
    if (isPublicRoute) {
      // Set a flag to indicate we're on a public route
      localStorage.setItem('is_public_route', 'true');
      console.log(`PublicRouteGuard: On public route ${currentPath}, setting flag to prevent redirects`);
    } else {
      // Clear the flag if we're not on a public route
      localStorage.removeItem('is_public_route');
    }
  }, [location.pathname]);

  // This component doesn't render anything
  return null;
};

export default PublicRouteGuard;
