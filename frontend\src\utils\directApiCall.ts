/**
 * Direct API Call Utility
 * 
 * This utility provides functions for making direct API calls without going through
 * the standard API interceptors. This is useful for debugging authentication issues.
 */

import { API_BASE_URL } from '../config/apiConfig';
import { getAuthHeaders, refreshJWTTokens } from '../services/tokenService';

/**
 * Make a direct GET request to the API
 * @param endpoint The API endpoint to call
 * @param schema The schema name
 * @returns The response data
 */
export const directApiGet = async (endpoint: string, schema: string): Promise<any> => {
  try {
    console.log(`Making direct API GET request to ${endpoint} with schema ${schema}`);
    
    // Format schema for consistency
    const formattedSchema = schema.replace(/\s+/g, '_');
    
    // Store the schema for future use
    localStorage.setItem('jwt_schema', formattedSchema);
    localStorage.setItem('schema_name', formattedSchema);
    
    // Get auth headers
    const headers = getAuthHeaders(formattedSchema);
    
    // Create the tenant-specific URL
    const url = `${API_BASE_URL}/api/tenant/${formattedSchema}/${endpoint.replace(/^\/api\//, '')}`;
    console.log(`Direct API URL: ${url}`);
    
    // Make the request
    const response = await fetch(url, {
      method: 'GET',
      headers,
      credentials: 'include',
    });
    
    // Check if the response is OK
    if (!response.ok) {
      // If unauthorized, try to refresh the token
      if (response.status === 401) {
        console.log('Unauthorized response, attempting to refresh token');
        
        // Force refresh the token
        const refreshResult = await refreshJWTTokens(formattedSchema, true);
        
        if (refreshResult && refreshResult.access_token) {
          console.log('Token refreshed successfully, retrying request');
          
          // Get new auth headers
          const newHeaders = getAuthHeaders(formattedSchema);
          
          // Retry the request
          const retryResponse = await fetch(url, {
            method: 'GET',
            headers: newHeaders,
            credentials: 'include',
          });
          
          if (retryResponse.ok) {
            return await retryResponse.json();
          } else {
            const errorText = await retryResponse.text();
            throw new Error(`Request failed after token refresh: ${retryResponse.status} ${retryResponse.statusText} - ${errorText}`);
          }
        } else {
          throw new Error('Token refresh failed');
        }
      } else {
        const errorText = await response.text();
        throw new Error(`Request failed: ${response.status} ${response.statusText} - ${errorText}`);
      }
    }
    
    // Parse and return the response
    return await response.json();
  } catch (error) {
    console.error(`Error making direct API call to ${endpoint}:`, error);
    throw error;
  }
};

export default {
  directApiGet,
};
