"""
Management command to fix parent-child relationships between tenants.
"""
from django.core.management.base import BaseCommand, CommandError
from centers.models import Client
from django.db import transaction

class Command(BaseCommand):
    help = 'Fix parent-child relationships between tenants'

    def add_arguments(self, parser):
        subparsers = parser.add_subparsers(dest='command', help='Command')
        
        # List tenants command
        list_parser = subparsers.add_parser('list', help='List all tenants')
        
        # Fix tenant parent command
        fix_parser = subparsers.add_parser('fix', help='Fix parent for a tenant')
        fix_parser.add_argument('tenant_name', type=str, help='Name of the tenant to update')
        fix_parser.add_argument('parent_name', type=str, help='Name of the parent tenant')

    def handle(self, *args, **options):
        command = options['command']
        
        if command == 'list':
            self.list_tenants()
        elif command == 'fix':
            tenant_name = options['tenant_name']
            parent_name = options['parent_name']
            self.fix_tenant_parent(tenant_name, parent_name)
        else:
            self.stdout.write(self.style.ERROR('Invalid command'))

    def list_tenants(self):
        """List all tenants in the system."""
        try:
            tenants = Client.objects.all()
            self.stdout.write(self.style.SUCCESS(f"Found {len(tenants)} tenants:"))
            for tenant in tenants:
                parent_name = tenant.parent.name if tenant.parent else 'None'
                self.stdout.write(f"  {tenant.name} (ID: {tenant.id}, Schema: {tenant.schema_name}, Type: {tenant.schema_type}, Parent: {parent_name})")
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"Error listing tenants: {str(e)}"))

    def fix_tenant_parent(self, tenant_name, parent_name):
        """
        Fix the parent-child relationship for a tenant.
        
        Args:
            tenant_name (str): The name of the tenant to update
            parent_name (str): The name of the parent tenant
        """
        try:
            # Find the tenant and parent by name
            tenant = Client.objects.filter(name=tenant_name).first()
            parent = Client.objects.filter(name=parent_name).first()
            
            if not tenant:
                self.stdout.write(self.style.ERROR(f"Error: Tenant '{tenant_name}' not found."))
                return
            
            if not parent:
                self.stdout.write(self.style.ERROR(f"Error: Parent tenant '{parent_name}' not found."))
                return
            
            # Print current state
            self.stdout.write(self.style.SUCCESS(f"Current state:"))
            self.stdout.write(f"  Tenant: {tenant.name} (ID: {tenant.id}, Schema: {tenant.schema_name})")
            self.stdout.write(f"  Current parent: {tenant.parent.name if tenant.parent else 'None'}")
            self.stdout.write(f"  Setting parent to: {parent.name} (ID: {parent.id}, Schema: {parent.schema_name})")
            
            # Update the parent
            with transaction.atomic():
                tenant.parent = parent
                tenant.save()
            
            # Verify the update
            tenant.refresh_from_db()
            self.stdout.write(self.style.SUCCESS(f"Updated state:"))
            self.stdout.write(f"  Tenant: {tenant.name} (ID: {tenant.id}, Schema: {tenant.schema_name})")
            self.stdout.write(f"  New parent: {tenant.parent.name if tenant.parent else 'None'}")
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"Error updating tenant parent: {str(e)}"))
