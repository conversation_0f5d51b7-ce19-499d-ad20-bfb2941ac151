from django.core.management.base import BaseCommand
from django.db import transaction
from centers.models import City, Subcity, Kebele
from centers.tenant_models import Client, Domain
from django.conf import settings
import logging

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Migrate data from the old models to the new tenant models'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('Starting migration to tenant model...'))

        # Create public schema tenant
        self.create_public_tenant()

        # Migrate cities
        self.migrate_cities()

        # Migrate subcities
        self.migrate_subcities()

        # Migrate centers
        self.migrate_centers()

        self.stdout.write(self.style.SUCCESS('Migration to tenant model completed successfully!'))

    def create_public_tenant(self):
        """Create the public schema tenant if it doesn't exist."""
        try:
            # Check if public tenant already exists
            public_tenant = Client.objects.filter(schema_name='public').first()
            if public_tenant:
                self.stdout.write(self.style.WARNING('Public tenant already exists, skipping creation.'))
                return public_tenant

            # Create public tenant
            with transaction.atomic():
                public_tenant = Client(
                    schema_name='public',
                    name='Public',
                    schema_type='CITY',  # Use CITY as default
                    description='Public schema for shared data',
                    is_active=True,
                )
                public_tenant.save()

                # Create domain for public tenant
                domain = Domain(
                    domain='localhost',
                    tenant=public_tenant,
                    is_primary=True
                )
                domain.save()

                self.stdout.write(self.style.SUCCESS('Public tenant created successfully!'))
                return public_tenant
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'Error creating public tenant: {str(e)}'))
            raise

    def migrate_cities(self):
        """Migrate cities to tenant model."""
        self.stdout.write(self.style.SUCCESS('Migrating cities...'))
        cities = City.objects.all()

        for city in cities:
            try:
                # Check if tenant already exists for this city
                if hasattr(city, 'tenant') and city.tenant:
                    self.stdout.write(self.style.WARNING(f'City {city.name} already has a tenant, skipping.'))
                    continue

                # Create tenant for city
                with transaction.atomic():
                    tenant = Client(
                        schema_name=f'city_{city.slug}',
                        name=city.name,
                        schema_type='CITY',
                        description=city.description,
                        address=city.address,
                        phone=city.phone,
                        email=city.email,
                        website=city.website,
                        logo=city.logo,
                        header_color=city.header_color,
                        accent_color=city.accent_color,
                        admin_name=city.admin_name,
                        admin_email=city.admin_email,
                        admin_phone=city.admin_phone,
                        is_active=city.is_active,
                        created_by=city.created_by,
                    )
                    tenant.save()

                    # Create domain for city tenant
                    domain = Domain(
                        domain=f'{city.slug}.localhost',
                        tenant=tenant,
                        is_primary=True
                    )
                    domain.save()

                    # Update city with tenant reference
                    city.tenant = tenant
                    city.save()

                    self.stdout.write(self.style.SUCCESS(f'City {city.name} migrated successfully!'))
            except Exception as e:
                self.stdout.write(self.style.ERROR(f'Error migrating city {city.name}: {str(e)}'))

    def migrate_subcities(self):
        """Migrate subcities to tenant model."""
        self.stdout.write(self.style.SUCCESS('Migrating subcities...'))
        subcities = Subcity.objects.all()

        for subcity in subcities:
            try:
                # Check if tenant already exists for this subcity
                if hasattr(subcity, 'tenant') and subcity.tenant:
                    self.stdout.write(self.style.WARNING(f'Subcity {subcity.name} already has a tenant, skipping.'))
                    continue

                # Get city tenant
                if not hasattr(subcity.city, 'tenant') or not subcity.city.tenant:
                    self.stdout.write(self.style.WARNING(f'City {subcity.city.name} does not have a tenant, skipping subcity {subcity.name}.'))
                    continue

                # Create tenant for subcity
                with transaction.atomic():
                    tenant = Client(
                        schema_name=f'subcity_{subcity.slug}',
                        name=subcity.name,
                        schema_type='SUBCITY',
                        description=subcity.description,
                        parent=subcity.city.tenant,
                        address=subcity.address,
                        phone=subcity.phone,
                        email=subcity.email,
                        website=subcity.website,
                        logo=subcity.logo,
                        header_color=subcity.header_color,
                        accent_color=subcity.accent_color,
                        admin_name=subcity.admin_name,
                        admin_email=subcity.admin_email,
                        admin_phone=subcity.admin_phone,
                        has_printing_facility=subcity.has_printing_facility,
                        printing_capacity=subcity.printing_capacity,
                        is_active=subcity.is_active,
                        created_by=subcity.created_by,
                    )
                    tenant.save()

                    # Create domain for subcity tenant
                    domain = Domain(
                        domain=f'{subcity.slug}.{subcity.city.slug}.localhost',
                        tenant=tenant,
                        is_primary=True
                    )
                    domain.save()

                    # Update subcity with tenant reference
                    subcity.tenant = tenant
                    subcity.save()

                    self.stdout.write(self.style.SUCCESS(f'Subcity {subcity.name} migrated successfully!'))
            except Exception as e:
                self.stdout.write(self.style.ERROR(f'Error migrating subcity {subcity.name}: {str(e)}'))

    def migrate_centers(self):
        """Migrate centers to tenant model."""
        self.stdout.write(self.style.SUCCESS('Migrating centers...'))
        centers = Kebele.objects.all()

        for center in centers:
            try:
                # Check if tenant already exists for this center
                if hasattr(center, 'tenant') and center.tenant:
                    self.stdout.write(self.style.WARNING(f'Center {center.name} already has a tenant, skipping.'))
                    continue

                # Get subcity tenant
                if not center.subcity or not hasattr(center.subcity, 'tenant') or not center.subcity.tenant:
                    self.stdout.write(self.style.WARNING(f'Subcity for center {center.name} does not have a tenant, skipping.'))
                    continue

                # Create tenant for center
                with transaction.atomic():
                    tenant = Client(
                        schema_name=f'center_{center.slug}',
                        name=center.name,
                        schema_type='KEBELE',
                        description=center.description,
                        parent=center.subcity.tenant,
                        address=center.address,
                        phone=center.phone,
                        email=center.email,
                        website=center.website,
                        logo=center.logo,
                        header_color=center.header_color,
                        accent_color=center.accent_color,
                        admin_name=center.admin_name,
                        admin_email=center.admin_email,
                        admin_phone=center.admin_phone,
                        center_type=center.type.name if center.type else '',
                        is_verified=center.is_verified,
                        subscription_status=center.subscription_status,
                        subscription_expiry=center.subscription_expiry,
                        max_users=center.max_users,
                        max_citizens=center.max_citizens,
                        max_id_cards=center.max_id_cards,
                        settings=center.settings,
                        is_active=center.is_active,
                        created_by=center.created_by,
                    )
                    tenant.save()

                    # Create domain for center tenant
                    domain = Domain(
                        domain=f'{center.slug}.{center.subcity.slug}.{center.subcity.city.slug}.localhost',
                        tenant=tenant,
                        is_primary=True
                    )
                    domain.save()

                    # Update center with tenant reference
                    center.tenant = tenant
                    center.save()

                    self.stdout.write(self.style.SUCCESS(f'Center {center.name} migrated successfully!'))
            except Exception as e:
                self.stdout.write(self.style.ERROR(f'Error migrating center {center.name}: {str(e)}'))
