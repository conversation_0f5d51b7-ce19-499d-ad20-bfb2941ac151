// Mock login data
const mockUser = {
  id: 1,
  email: '<EMAIL>',
  first_name: 'Admin',
  last_name: 'User',
  role: 'admin'
};

const mockTenant = {
  schema_name: 'kebele_01',
  name: '<PERSON><PERSON><PERSON> 01',
  type: 'kebele'
};

// Set mock data in localStorage
localStorage.setItem('token', 'mock-token-12345');
localStorage.setItem('user', JSON.stringify(mockUser));
localStorage.setItem('tenant', JSON.stringify(mockTenant));
localStorage.setItem('schema_name', 'kebele_01');

console.log('Mock login data set in localStorage');
console.log('You can now navigate to /citizens/1 to see the citizen details page');

// Optionally navigate to the citizen details page
// window.location.href = '/citizens/1';
