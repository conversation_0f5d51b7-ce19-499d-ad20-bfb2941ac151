from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from django_tenants.utils import tenant_context, get_tenant_model
from centers.models import Client
import tabulate

User = get_user_model()

class Command(BaseCommand):
    help = 'List all tenants and users in each tenant'

    def handle(self, *args, **options):
        # Get all tenants
        tenants = Client.objects.all().order_by('schema_type', 'name')
        
        self.stdout.write(self.style.SUCCESS(f"Found {tenants.count()} tenants"))
        
        # Create a table for tenants
        tenant_data = []
        for tenant in tenants:
            tenant_data.append([
                tenant.schema_name,
                tenant.name,
                tenant.schema_type,
                tenant.parent.name if tenant.parent else "None",
                "Active" if tenant.is_active else "Inactive"
            ])
        
        self.stdout.write(self.style.SUCCESS("\nTENANTS:"))
        self.stdout.write(tabulate.tabulate(
            tenant_data,
            headers=["Schema Name", "Name", "Type", "Parent", "Status"],
            tablefmt="grid"
        ))
        
        # For each tenant, get users
        self.stdout.write(self.style.SUCCESS("\nUSERS BY TENANT:"))
        
        for tenant in tenants:
            self.stdout.write(self.style.SUCCESS(f"\nTenant: {tenant.name} ({tenant.schema_name})"))
            
            try:
                with tenant_context(tenant):
                    users = User.objects.all().order_by('email')
                    
                    if users.exists():
                        user_data = []
                        for user in users:
                            user_data.append([
                                user.email,
                                user.get_full_name() or "N/A",
                                user.role,
                                "Yes" if user.is_active else "No",
                                "Yes" if user.is_superuser else "No",
                                "Yes" if user.is_staff else "No"
                            ])
                        
                        self.stdout.write(tabulate.tabulate(
                            user_data,
                            headers=["Email", "Name", "Role", "Active", "Superuser", "Staff"],
                            tablefmt="grid"
                        ))
                    else:
                        self.stdout.write(self.style.WARNING("  No users found in this tenant"))
            except Exception as e:
                self.stdout.write(self.style.ERROR(f"  Error accessing tenant {tenant.schema_name}: {str(e)}"))
