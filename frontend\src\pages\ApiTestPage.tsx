import React from 'react';
import { Container, Typography, Box } from '@mui/material';
import CitizensApiTest from '../components/CitizensApiTest';
import { useAuth } from '../contexts/AuthContext';

/**
 * Page for testing API endpoints
 */
const ApiTestPage: React.FC = () => {
  const { isAuthenticated } = useAuth();
  
  return (
    <Container maxWidth="md">
      <Box sx={{ my: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          API Test Page
        </Typography>
        
        {!isAuthenticated ? (
          <Typography color="error">
            You must be logged in to use this page.
          </Typography>
        ) : (
          <CitizensApiTest />
        )}
      </Box>
    </Container>
  );
};

export default ApiTestPage;
