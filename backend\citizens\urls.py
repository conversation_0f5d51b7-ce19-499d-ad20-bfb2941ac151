from django.urls import path, include
from rest_framework.routers import <PERSON>fault<PERSON><PERSON><PERSON>
from .views import CitizenViewSet
from .views_family import (RelationshipTypeViewSet, ChildViewSet, ParentViewSet,
                          EmergencyContactViewSet, SpouseViewSet)
from .views_document import DocumentViewSet, DocumentTypeViewSet
from .views_biometric import BiometricViewSet, PhotoViewSet
from .public_views import public_register_citizen

# Create a router and register our viewsets with it
router = DefaultRouter()
router.register(r'citizens', CitizenViewSet)
router.register(r'relationship-types', RelationshipTypeViewSet)
router.register(r'children', ChildViewSet)
router.register(r'parents', ParentViewSet)
router.register(r'emergency-contacts', EmergencyContactViewSet)
router.register(r'spouses', SpouseViewSet)
router.register(r'documents', DocumentViewSet)
router.register(r'document-types', DocumentTypeViewSet)
router.register(r'biometrics', BiometricViewSet)
router.register(r'photos', PhotoViewSet)

urlpatterns = [
    path('', include(router.urls)),
    path('public/register/', public_register_citizen, name='public-register-citizen'),
]
