import os
import django

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'neocamelot.settings')
django.setup()

# Import models
from centers.models import Client
from django.core.management import call_command
from django_tenants.utils import tenant_context
from django.db import connection

def migrate_accounts_to_tenants():
    """Migrate the accounts app to all tenant schemas."""
    print("\n===== MIGRATING ACCOUNTS APP TO TENANT SCHEMAS =====\n")
    
    # Get all tenants except public
    tenants = Client.objects.exclude(schema_name='public')
    print(f"Found {tenants.count()} tenants to migrate")
    
    for tenant in tenants:
        print(f"\nMigrating accounts app to tenant: {tenant.name} (Schema: {tenant.schema_name})")
        
        try:
            # Set the connection to the tenant's schema
            connection.set_schema(tenant.schema_name)
            
            # Create a cursor
            cursor = connection.cursor()
            
            # Check if the accounts_user table exists
            cursor.execute(
                "SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'accounts_user')"
            )
            table_exists = cursor.fetchone()[0]
            
            if not table_exists:
                print(f"accounts_user table does not exist in {tenant.schema_name} schema. Creating it...")
                # Migrate the accounts app to the tenant schema
                with tenant_context(tenant):
                    call_command('migrate', 'accounts')
                print(f"Migrated accounts app to {tenant.schema_name} schema")
            else:
                print(f"accounts_user table already exists in {tenant.schema_name} schema")
                
        except Exception as e:
            print(f"Error migrating accounts app to {tenant.schema_name} schema: {str(e)}")
    
    print("\n===== ACCOUNTS APP MIGRATION COMPLETE =====\n")

if __name__ == '__main__':
    migrate_accounts_to_tenants()
