from django.db import migrations

def create_initial_data(apps, schema_editor):
    """
    Create initial data for EmployeeType, Country, and Region models.
    """
    EmployeeType = apps.get_model('common', 'EmployeeType')
    Country = apps.get_model('common', 'Country')
    Region = apps.get_model('common', 'Region')
    
    # Create employee types
    employee_types = [
        'Government Employee',
        'Private Sector Employee',
        'Self-Employed',
        'Business Owner',
        'Contractor',
        'Consultant',
        'Freelancer',
        'NGO Employee',
        'International Organization Employee',
        'Other'
    ]
    for name in employee_types:
        EmployeeType.objects.create(name=name)
    
    # Create countries
    countries = [
        {'name': 'Ethiopia', 'code': 'ETH'},
        {'name': 'Kenya', 'code': 'KEN'},
        {'name': 'Sudan', 'code': 'SDN'},
        {'name': 'South Sudan', 'code': 'SSD'},
        {'name': 'Somalia', 'code': 'SOM'},
        {'name': 'Djibouti', 'code': 'DJI'},
        {'name': 'Eritrea', 'code': 'ERI'},
        {'name': 'Egypt', 'code': 'EGY'},
        {'name': 'United States', 'code': 'USA'},
        {'name': 'United Kingdom', 'code': 'GBR'},
        {'name': 'Canada', 'code': 'CAN'},
        {'name': 'Australia', 'code': 'AUS'},
        {'name': 'Germany', 'code': 'DEU'},
        {'name': 'France', 'code': 'FRA'},
        {'name': 'China', 'code': 'CHN'},
        {'name': 'India', 'code': 'IND'},
        {'name': 'Japan', 'code': 'JPN'},
        {'name': 'South Africa', 'code': 'ZAF'},
        {'name': 'Nigeria', 'code': 'NGA'},
        {'name': 'Brazil', 'code': 'BRA'}
    ]
    country_objects = {}
    for country_data in countries:
        country = Country.objects.create(name=country_data['name'], code=country_data['code'])
        country_objects[country_data['code']] = country
    
    # Create regions for Ethiopia
    ethiopia_regions = [
        {'name': 'Addis Ababa', 'code': 'AA'},
        {'name': 'Afar', 'code': 'AF'},
        {'name': 'Amhara', 'code': 'AM'},
        {'name': 'Benishangul-Gumuz', 'code': 'BG'},
        {'name': 'Dire Dawa', 'code': 'DD'},
        {'name': 'Gambela', 'code': 'GM'},
        {'name': 'Harari', 'code': 'HR'},
        {'name': 'Oromia', 'code': 'OR'},
        {'name': 'Sidama', 'code': 'SD'},
        {'name': 'Somali', 'code': 'SM'},
        {'name': 'South West Ethiopia', 'code': 'SW'},
        {'name': 'Southern Nations, Nationalities, and Peoples', 'code': 'SN'},
        {'name': 'Tigray', 'code': 'TG'}
    ]
    for region_data in ethiopia_regions:
        Region.objects.create(
            name=region_data['name'],
            code=region_data['code'],
            country=country_objects['ETH']
        )
    
    # Create regions for Kenya
    kenya_regions = [
        {'name': 'Nairobi', 'code': 'NBO'},
        {'name': 'Central', 'code': 'CEN'},
        {'name': 'Coast', 'code': 'CST'},
        {'name': 'Eastern', 'code': 'EST'},
        {'name': 'North Eastern', 'code': 'NEA'},
        {'name': 'Nyanza', 'code': 'NYZ'},
        {'name': 'Rift Valley', 'code': 'RVA'},
        {'name': 'Western', 'code': 'WES'}
    ]
    for region_data in kenya_regions:
        Region.objects.create(
            name=region_data['name'],
            code=region_data['code'],
            country=country_objects['KEN']
        )

class Migration(migrations.Migration):
    dependencies = [
        ('common', '0003_add_new_models'),
    ]

    operations = [
        migrations.RunPython(create_initial_data),
    ]
