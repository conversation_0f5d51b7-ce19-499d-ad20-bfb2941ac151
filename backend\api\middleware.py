"""
CSRF Exempt Middleware for API endpoints

This middleware exempts API endpoints from CSRF protection.
"""

from django.utils.deprecation import MiddlewareMixin


class ApiCsrfExemptMiddleware(MiddlewareMixin):
    """
    Middleware that exempts API endpoints from CSRF protection.

    This middleware should be placed before CsrfViewMiddleware in MIDDLEWARE settings.
    """

    def process_view(self, request, view_func, view_args, view_kwargs):
        # Check if the request path starts with /api/
        if request.path.startswith('/api/'):
            # Mark the request as CSRF exempt
            request._dont_enforce_csrf_checks = True

        # Always return None to let the next middleware handle the request
        return None
