from django.db import migrations

def create_initial_data(apps, schema_editor):
    """
    Create initial data for Religion, CitizenStatus, MaritalStatus, EmploymentType, and RelationshipType models.
    """
    Religion = apps.get_model('common', 'Religion')
    CitizenStatus = apps.get_model('common', 'CitizenStatus')
    MaritalStatus = apps.get_model('common', 'MaritalStatus')
    EmploymentType = apps.get_model('common', 'EmploymentType')
    RelationshipType = apps.get_model('common', 'RelationshipType')
    
    # Create religions
    religions = [
        'Orthodox Christianity',
        'Islam',
        'Protestantism',
        'Catholicism',
        'Judaism',
        'Hinduism',
        'Buddhism',
        'Traditional Beliefs',
        'Other',
        'None'
    ]
    for name in religions:
        Religion.objects.create(name=name)
    
    # Create citizen statuses
    statuses = [
        'Resident',
        'Non-Resident',
        'Temporary Resident',
        'Refugee',
        'Asylum Seeker',
        'Diplomat',
        'Student',
        'Worker',
        'Visitor',
        'Other'
    ]
    for name in statuses:
        CitizenStatus.objects.create(name=name)
    
    # Create marital statuses
    marital_statuses = [
        {'id': 'SINGLE', 'name': 'Single'},
        {'id': 'MARRIED', 'name': 'Married'},
        {'id': 'DIVORCED', 'name': 'Divorced'},
        {'id': 'WIDOWED', 'name': 'Widowed'},
        {'id': 'SEPARATED', 'name': 'Separated'}
    ]
    for status in marital_statuses:
        MaritalStatus.objects.create(id=status['id'], name=status['name'])
    
    # Create employment types
    employment_types = [
        'Full-time',
        'Part-time',
        'Contract',
        'Self-employed',
        'Freelance',
        'Temporary',
        'Seasonal',
        'Internship',
        'Apprenticeship',
        'Other'
    ]
    for name in employment_types:
        EmploymentType.objects.create(name=name)
    
    # Create relationship types
    relationship_types = [
        'Spouse',
        'Parent',
        'Child',
        'Sibling',
        'Grandparent',
        'Grandchild',
        'Aunt/Uncle',
        'Niece/Nephew',
        'Cousin',
        'Friend',
        'Neighbor',
        'Colleague',
        'Other'
    ]
    for name in relationship_types:
        RelationshipType.objects.create(name=name)

class Migration(migrations.Migration):
    dependencies = [
        ('common', '0001_initial'),
    ]

    operations = [
        migrations.RunPython(create_initial_data),
    ]
