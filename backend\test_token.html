<!DOCTYPE html>
<html>
<head>
    <title>Test Token</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"] {
            width: 100%;
            padding: 8px;
            box-sizing: border-box;
        }
        button {
            padding: 10px 15px;
            background-color: #4CAF50;
            color: white;
            border: none;
            cursor: pointer;
        }
        button:hover {
            background-color: #45a049;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            background-color: #f9f9f9;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Test Token</h1>
        <div class="form-group">
            <label for="token">Token:</label>
            <input type="text" id="token" value="9425ec3171e23a01903152c33f7a212106916a4b">
        </div>
        <div class="form-group">
            <label for="schema">Schema Name:</label>
            <input type="text" id="schema" value="subcity_zoble">
        </div>
        <div class="form-group">
            <label for="url">URL:</label>
            <input type="text" id="url" value="http://localhost:8000/api/tenant/subcity_zoble/idcards/statistics/">
        </div>
        <button onclick="testToken()">Test Token</button>
        <div class="result" id="result"></div>
    </div>

    <script>
        function testToken() {
            const token = document.getElementById('token').value;
            const schema = document.getElementById('schema').value;
            const url = document.getElementById('url').value;
            const resultElement = document.getElementById('result');
            
            resultElement.textContent = 'Testing...';
            
            fetch(url, {
                method: 'GET',
                headers: {
                    'Authorization': `Token ${token}`,
                    'Content-Type': 'application/json',
                    'X-Schema-Name': schema
                }
            })
            .then(response => {
                if (!response.ok) {
                    return response.text().then(text => {
                        throw new Error(`Status: ${response.status} ${response.statusText}\nResponse: ${text}`);
                    });
                }
                return response.json();
            })
            .then(data => {
                resultElement.textContent = 'Success!\n\n' + JSON.stringify(data, null, 2);
            })
            .catch(error => {
                resultElement.textContent = 'Error!\n\n' + error.message;
            });
        }
    </script>
</body>
</html>
