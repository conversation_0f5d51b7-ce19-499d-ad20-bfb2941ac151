import os
import django
import json

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'neocamelot.settings')
django.setup()

from django.db import connection
from django_tenants.utils import tenant_context
from centers.models import Client, Center
from idcards.models import IDCardTemplate

# Default template configuration
default_front_layout = {
    "header": {
        "text": "የነዋሪዎች መታወቂያ ካርድ",
        "color": "#FFFFFF",
        "background": "linear-gradient(135deg, #0066b2 0%, #004c99 100%)"
    },
    "photo": {
        "width": 90,
        "height": 110,
        "border": "1px solid rgba(0,0,0,0.15)"
    },
    "fields": [
        {"name": "full_name", "label": "ሙሉ ስም / FULL NAME", "fontSize": "0.9rem", "fontWeight": 700},
        {"name": "gender", "label": "ፆታ / SEX", "fontSize": "0.75rem", "fontWeight": 600},
        {"name": "date_of_birth", "label": "የትውልድ ቀን / DOB", "fontSize": "0.75rem", "fontWeight": 600},
        {"name": "address", "label": "አድራሻ / ADDRESS", "fontSize": "0.75rem", "fontWeight": 600},
        {"name": "issue_date", "label": "የተሰጠበት ቀን / ISSUED", "fontSize": "0.75rem", "fontWeight": 600},
        {"name": "expiry_date", "label": "የሚያበቃበት ቀን / EXPIRES", "fontSize": "0.75rem", "fontWeight": 700}
    ],
    "footer": {
        "text": "በጎንደር ከተማ አስተዳደር የተሰጠ ህጋዊ መታወቂያ",
        "color": "#0066b2",
        "fontSize": "0.65rem",
        "fontWeight": 600
    }
}

default_back_layout = {
    "header": {
        "text": "መታወቂያ ካርድ - ጀርባ",
        "color": "#FFFFFF",
        "background": "linear-gradient(135deg, #0066b2 0%, #004c99 100%)"
    },
    "qrCode": {
        "width": 130,
        "height": 130,
        "text": "ለማረጋገጥ ይስካን ያድርጉ"
    },
    "fields": [
        {"name": "id_number", "label": "መታወቂያ ቁጥር / ID NUMBER", "fontSize": "0.8rem", "fontWeight": 700},
        {"name": "center_name", "label": "የተሰጠበት ቦታ / ISSUED BY", "fontSize": "0.75rem", "fontWeight": 600},
        {"name": "contact", "label": "ለበለጠ መረጃ / FOR MORE INFO", "fontSize": "0.75rem", "fontWeight": 600}
    ],
    "footer": {
        "text": "ይህ መታወቂያ የጎንደር ከተማ አስተዳደር ንብረት ነው",
        "color": "#0066b2",
        "fontSize": "0.65rem",
        "fontWeight": 600
    }
}

def create_default_template_for_tenant(tenant):
    print(f"Creating default template for tenant: {tenant.schema_name}")
    
    with tenant_context(tenant):
        # Get the first center in the tenant
        try:
            center = Center.objects.first()
            if not center:
                print(f"No center found for tenant {tenant.schema_name}, skipping...")
                return
            
            # Check if a default template already exists
            existing_template = IDCardTemplate.objects.filter(is_default=True).first()
            if existing_template:
                print(f"Default template already exists for tenant {tenant.schema_name}: {existing_template.name}")
                return
            
            # Create a new default template
            template = IDCardTemplate.objects.create(
                name="Standard ID Card",
                center=center,
                is_default=True,
                front_layout=default_front_layout,
                back_layout=default_back_layout
            )
            
            print(f"Created default template for tenant {tenant.schema_name}: {template.name}")
        except Exception as e:
            print(f"Error creating template for tenant {tenant.schema_name}: {str(e)}")

# Get all tenants
tenants = Client.objects.exclude(schema_name='public')
print(f"Found {len(tenants)} tenants")

# Create default template for each tenant
for tenant in tenants:
    create_default_template_for_tenant(tenant)

print("Done creating default templates")
