# Generated by Django 5.1.7 on 2025-04-17 15:01

import django.core.validators
import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('centers', '0008_globalcitizen_globalidcard_globalidcardtemplate_and_more'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='city',
            options={'ordering': ['city_name', 'city_code'], 'verbose_name': 'City Administration', 'verbose_name_plural': 'City Administrations'},
        ),
        migrations.RemoveField(
            model_name='city',
            name='accent_color',
        ),
        migrations.RemoveField(
            model_name='city',
            name='address',
        ),
        migrations.RemoveField(
            model_name='city',
            name='admin_email',
        ),
        migrations.RemoveField(
            model_name='city',
            name='admin_name',
        ),
        migrations.RemoveField(
            model_name='city',
            name='admin_phone',
        ),
        migrations.RemoveField(
            model_name='city',
            name='code',
        ),
        migrations.RemoveField(
            model_name='city',
            name='description',
        ),
        migrations.RemoveField(
            model_name='city',
            name='email',
        ),
        migrations.RemoveField(
            model_name='city',
            name='header_color',
        ),
        migrations.RemoveField(
            model_name='city',
            name='name',
        ),
        migrations.RemoveField(
            model_name='city',
            name='phone',
        ),
        migrations.RemoveField(
            model_name='city',
            name='slug',
        ),
        migrations.AddField(
            model_name='city',
            name='area_sq_km',
            field=models.FloatField(blank=True, null=True, validators=[django.core.validators.MinValueValidator(0.0)]),
        ),
        migrations.AddField(
            model_name='city',
            name='city_code',
            field=models.CharField(default='CTY001', max_length=10, unique=True),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='city',
            name='city_intro',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='city',
            name='city_name',
            field=models.CharField(default='Default City', max_length=100),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='city',
            name='contact_email',
            field=models.EmailField(blank=True, max_length=254, null=True),
        ),
        migrations.AddField(
            model_name='city',
            name='contact_phone',
            field=models.CharField(blank=True, max_length=20, null=True),
        ),
        migrations.AddField(
            model_name='city',
            name='country',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='centers.country'),
        ),
        migrations.AddField(
            model_name='city',
            name='deputy_mayor',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='city',
            name='elevation_meters',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='city',
            name='established_date',
            field=models.DateField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='city',
            name='google_maps_url',
            field=models.URLField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='city',
            name='headquarter_address',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='city',
            name='is_resident',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='city',
            name='mayor_name',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='city',
            name='motto_slogan',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='city',
            name='postal_code',
            field=models.CharField(blank=True, max_length=20, null=True),
        ),
        migrations.AddField(
            model_name='city',
            name='timezone',
            field=models.CharField(default='EAT', max_length=50),
        ),
        migrations.AlterField(
            model_name='city',
            name='logo',
            field=models.ImageField(blank=True, null=True, upload_to='logos/'),
        ),
        migrations.AlterField(
            model_name='city',
            name='region',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='cities', to='centers.region'),
        ),
        migrations.AlterField(
            model_name='city',
            name='website',
            field=models.URLField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='subcity',
            name='city',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='subcities', to='centers.city'),
        ),
        migrations.CreateModel(
            name='CityAdministration',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('city_code', models.CharField(max_length=10, unique=True)),
                ('city_name', models.CharField(max_length=100)),
                ('logo', models.ImageField(blank=True, null=True, upload_to='logos/')),
                ('motto_slogan', models.TextField(blank=True, null=True)),
                ('city_intro', models.TextField(blank=True, null=True)),
                ('mayor_name', models.CharField(blank=True, max_length=100, null=True)),
                ('deputy_mayor', models.CharField(blank=True, max_length=100, null=True)),
                ('contact_email', models.EmailField(blank=True, max_length=254, null=True)),
                ('contact_phone', models.CharField(blank=True, max_length=20, null=True)),
                ('google_maps_url', models.URLField(blank=True, null=True)),
                ('area_sq_km', models.FloatField(blank=True, null=True, validators=[django.core.validators.MinValueValidator(0.0)])),
                ('elevation_meters', models.IntegerField(blank=True, null=True)),
                ('timezone', models.CharField(default='EAT', max_length=50)),
                ('website', models.URLField(blank=True, null=True)),
                ('headquarter_address', models.TextField(blank=True, null=True)),
                ('postal_code', models.CharField(blank=True, max_length=20, null=True)),
                ('established_date', models.DateField(blank=True, null=True)),
                ('is_resident', models.BooleanField(default=False)),
                ('is_active', models.BooleanField(default=True)),
                ('country', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='centers.country')),
                ('region', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='centers.region')),
            ],
            options={
                'verbose_name': 'City Administration',
                'verbose_name_plural': 'City Administrations',
                'ordering': ['city_name', 'city_code'],
            },
        ),
    ]
