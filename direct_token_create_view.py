"""
Direct Token Creation View

This module provides a view for directly creating tokens without authentication.
This is a temporary solution for development/testing only.
"""

from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from rest_framework.authtoken.models import Token
from django.contrib.auth import get_user_model
from django_tenants.utils import tenant_context
from centers.models import Client
import logging
import secrets
import binascii

# Configure logging
logger = logging.getLogger(__name__)

User = get_user_model()

def generate_token_key():
    """
    Generate a token key.
    This is the same method used by the Token model to generate keys.
    """
    return binascii.hexlify(secrets.token_bytes(20)).decode()

@api_view(['POST', 'OPTIONS'])
@permission_classes([AllowAny])
def direct_token_create_view(request):
    """
    Direct token creation endpoint.
    This endpoint creates a token for a user without authentication.
    This is a temporary solution for development/testing only.
    """
    # Handle preflight OPTIONS request
    if request.method == 'OPTIONS':
        response = Response()
        origin = request.headers.get('Origin', '')
        allowed_origins = ['http://localhost:5173', 'http://127.0.0.1:5173', 'http://localhost:5174', 'http://127.0.0.1:5174']
        
        if origin in allowed_origins:
            response['Access-Control-Allow-Origin'] = origin
            response['Access-Control-Allow-Methods'] = 'POST, OPTIONS'
            response['Access-Control-Allow-Headers'] = 'Content-Type, Authorization, X-Requested-With, X-CSRFToken, X-Schema-Name'
            response['Access-Control-Allow-Credentials'] = 'true'
            response['Access-Control-Max-Age'] = '86400'  # 24 hours
        return response
    
    # Get email and schema_name from request
    email = request.data.get('email')
    schema_name = request.data.get('schema_name')
    
    # Get schema from request headers if not in request body
    if not schema_name:
        schema_name = request.headers.get('X-Schema-Name')
    
    logger.info(f"Direct token creation request for email: {email}, schema: {schema_name}")
    
    if not email or not schema_name:
        logger.warning("Email or schema_name missing in request")
        return Response({'error': 'Email and schema_name are required'}, status=status.HTTP_400_BAD_REQUEST)
    
    try:
        # Get the tenant
        tenant = Client.objects.get(schema_name=schema_name)
        logger.info(f"Found tenant: {tenant.name} (Schema: {tenant.schema_name})")
        
        # Switch to the tenant context
        with tenant_context(tenant):
            # Try to find the user
            try:
                user = User.objects.get(email=email)
                logger.info(f"Found user: {user.email} (ID: {user.id})")
                
                # Delete any existing tokens for this user
                Token.objects.filter(user=user).delete()
                logger.info(f"Deleted existing tokens for user {user.email}")
                
                # Generate a new token key
                token_key = generate_token_key()
                
                # Create a new token directly in the database
                token = Token.objects.create(key=token_key, user=user)
                logger.info(f"Created token for user {user.email}: {token.key}")
                
                # Return the token
                response_data = {
                    'token': token.key,
                    'user': {
                        'id': user.id,
                        'email': user.email,
                        'name': f"{user.first_name} {user.last_name}".strip()
                    },
                    'tenant': {
                        'id': tenant.id,
                        'schema_name': tenant.schema_name,
                        'name': tenant.name
                    }
                }
                
                response = Response(response_data)
                
                # Add CORS headers
                origin = request.headers.get('Origin', '')
                allowed_origins = ['http://localhost:5173', 'http://127.0.0.1:5173', 'http://localhost:5174', 'http://127.0.0.1:5174']
                if origin in allowed_origins:
                    response['Access-Control-Allow-Origin'] = origin
                    response['Access-Control-Allow-Credentials'] = 'true'
                
                return response
            except User.DoesNotExist:
                logger.warning(f"User with email {email} not found in schema {schema_name}")
                return Response({'error': f'User with email {email} not found in schema {schema_name}'}, status=status.HTTP_404_NOT_FOUND)
    except Client.DoesNotExist:
        logger.warning(f"Tenant with schema {schema_name} does not exist")
        return Response({'error': f'Tenant with schema {schema_name} not found'}, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        logger.error(f"Error creating token: {str(e)}")
        return Response({'error': f'Error creating token: {str(e)}'}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
