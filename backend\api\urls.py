from django.urls import path, include
from django.views.decorators.csrf import csrf_exempt
from rest_framework.routers import Default<PERSON><PERSON><PERSON>
from .views import citizen_views, id_card_views, tenant_views, common_views, csrf_exempt_views
from .views.idcard_statistics_view import idcard_statistics_view
from .views.csrf_token_view import get_csrf_token

router = DefaultRouter()
router.register(r'citizens', citizen_views.CitizenViewSet, basename='citizen')
router.register(r'id-cards', id_card_views.IDCardViewSet, basename='id-card')

urlpatterns = [
    # CSRF token endpoint - this one should NOT be CSRF exempt
    path('csrf-token/', get_csrf_token, name='csrf-token'),

    # ID card statistics endpoint
    path('idcards/statistics/', csrf_exempt(idcard_statistics_view), name='idcard-statistics'),

    # Tenant-specific endpoints
    path('tenant/<str:schema_name>/', include([
        path('', include(router.urls)),
        # Citizen relation endpoints - support both singular and plural forms for compatibility
        # Singular forms (original)
        path('citizens/<int:citizen_id>/spouse/', csrf_exempt_views.add_spouse_csrf_exempt, name='add-spouse'),
        path('citizens/<int:citizen_id>/parent/', csrf_exempt_views.add_parent_csrf_exempt, name='add-parent'),
        path('citizens/<int:citizen_id>/child/', csrf_exempt_views.add_child_csrf_exempt, name='add-child'),
        path('citizens/<int:citizen_id>/emergency-contact/', csrf_exempt_views.add_emergency_contact_csrf_exempt, name='add-emergency-contact'),
        path('citizens/<int:citizen_id>/document/', csrf_exempt_views.add_document_csrf_exempt, name='add-document'),

        # Plural forms (for frontend compatibility)
        path('citizens/<int:citizen_id>/spouses/', csrf_exempt_views.add_spouse_csrf_exempt, name='add-spouses'),
        path('citizens/<int:citizen_id>/parents/', csrf_exempt_views.add_parent_csrf_exempt, name='add-parents'),
        path('citizens/<int:citizen_id>/children/', csrf_exempt_views.add_child_csrf_exempt, name='add-children'),
        path('citizens/<int:citizen_id>/emergency-contacts/', csrf_exempt_views.add_emergency_contact_csrf_exempt, name='add-emergency-contacts'),
        path('citizens/<int:citizen_id>/documents/', csrf_exempt_views.add_document_csrf_exempt, name='add-documents'),
    ])),

    # Common endpoints (not tenant-specific)
    path('common/', include([
        path('religions/', csrf_exempt(common_views.ReligionListView.as_view()), name='religion-list'),
        path('marital-statuses/', csrf_exempt(common_views.MaritalStatusListView.as_view()), name='marital-status-list'),
        path('citizen-statuses/', csrf_exempt(common_views.CitizenStatusListView.as_view()), name='citizen-status-list'),
        path('employment-types/', csrf_exempt(common_views.EmploymentTypeListView.as_view()), name='employment-type-list'),
        path('document-types/', csrf_exempt(common_views.DocumentTypeListView.as_view()), name='document-type-list'),
        path('relationship-types/', csrf_exempt(common_views.RelationshipTypeListView.as_view()), name='relationship-type-list'),
        path('countries/', csrf_exempt(common_views.CountryListView.as_view()), name='country-list'),
        path('regions/', csrf_exempt(common_views.RegionListView.as_view()), name='region-list'),
        path('ketenas/', csrf_exempt(common_views.KetenaListView.as_view()), name='ketena-list'),
    ])),

    # Tenant management endpoints
    path('tenants/', csrf_exempt(tenant_views.TenantListCreateView.as_view()), name='tenant-list-create'),
    path('tenants/<int:pk>/', csrf_exempt(tenant_views.TenantDetailView.as_view()), name='tenant-detail'),
    path('tenants/types/', csrf_exempt(tenant_views.TenantTypeListView.as_view()), name='tenant-type-list'),
]
