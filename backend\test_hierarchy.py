import os
import django
import json
import requests

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'neocamelot.settings')
django.setup()

from centers.models import City, Subcity, Center
from django.contrib.auth import get_user_model

User = get_user_model()

def test_hierarchy_isolation():
    """Test that hierarchy isolation works correctly."""
    print("Testing hierarchy isolation...")
    
    # Get the city
    city = City.objects.first()
    if not city:
        print("No city found. Please run setup_hierarchy.py first.")
        return
    
    print(f"\nTesting with city: {city.name}")
    
    # Get subcities for this city
    subcities = city.subcities.all()
    print(f"  Found {subcities.count()} subcities for this city")
    
    # Test with each subcity
    for subcity in subcities:
        print(f"\n  Testing with subcity: {subcity.name}")
        
        # Get centers for this subcity
        centers = subcity.centers.all()
        print(f"    Found {centers.count()} centers for this subcity")
        
        # Test with each center
        for center in centers:
            print(f"\n    Testing with center: {center.name}")
            
            # Get citizens for this center
            citizens = center.citizens.all()
            print(f"      Found {citizens.count()} citizens for this center")

def test_api_hierarchy_isolation():
    """Test that API hierarchy isolation works correctly."""
    print("\nTesting API hierarchy isolation...")
    
    # Base URL
    base_url = 'http://127.0.0.1:8000'
    
    # Login to get token
    def login(email, password):
        url = base_url + '/api/login/'
        data = {'email': email, 'password': password}
        response = requests.post(url, json=data)
        if response.status_code == 200:
            return response.json().get('token')
        return None
    
    # Get centers with different hierarchy levels
    def get_centers(token, city_slug=None, subcity_slug=None, center_slug=None):
        url = base_url + '/api/centers/'
        headers = {'Authorization': f'Token {token}'}
        if city_slug:
            headers['X-City'] = city_slug
        if subcity_slug:
            headers['X-Subcity'] = subcity_slug
        if center_slug:
            headers['X-Center'] = center_slug
        response = requests.get(url, headers=headers)
        return response
    
    # Login as super admin
    token = login('<EMAIL>', 'admin123')
    if not token:
        print("Failed to login as super admin")
        return
    
    # Get all cities
    cities = City.objects.all()
    if not cities.exists():
        print("No cities found. Please run setup_hierarchy.py first.")
        return
    
    # Test with each city
    for city in cities:
        print(f"\nTesting API with city: {city.name}")
        
        # Get centers for this city
        response = get_centers(token, city_slug=city.slug)
        if response.status_code != 200:
            print(f"  Failed to get centers: {response.status_code}")
            continue
        
        centers_data = response.json()
        print(f"  Found {len(centers_data)} centers for this city via API")
        
        # Get subcities for this city
        subcities = city.subcities.all()
        
        # Test with each subcity
        for subcity in subcities:
            print(f"\n  Testing API with subcity: {subcity.name}")
            
            # Get centers for this subcity
            response = get_centers(token, city_slug=city.slug, subcity_slug=subcity.slug)
            if response.status_code != 200:
                print(f"    Failed to get centers: {response.status_code}")
                continue
            
            centers_data = response.json()
            print(f"    Found {len(centers_data)} centers for this subcity via API")
            
            # Get centers for this subcity
            centers = subcity.centers.all()
            
            # Test with each center
            for center in centers:
                print(f"\n    Testing API with center: {center.name}")
                
                # Get center details
                response = get_centers(token, city_slug=city.slug, subcity_slug=subcity.slug, center_slug=center.slug)
                if response.status_code != 200:
                    print(f"      Failed to get center: {response.status_code}")
                    continue
                
                centers_data = response.json()
                if centers_data:
                    print(f"      Successfully retrieved center via API")
                else:
                    print(f"      Failed to retrieve center via API")

def test_user_roles():
    """Test that user roles work correctly with the hierarchy."""
    print("\nTesting user roles...")
    
    # Base URL
    base_url = 'http://127.0.0.1:8000'
    
    # Login to get token
    def login(email, password):
        url = base_url + '/api/login/'
        data = {'email': email, 'password': password}
        response = requests.post(url, json=data)
        if response.status_code == 200:
            return response.json().get('token')
        return None
    
    # Get centers
    def get_centers(token):
        url = base_url + '/api/centers/'
        headers = {'Authorization': f'Token {token}'}
        response = requests.get(url, headers=headers)
        return response
    
    # Get subcities
    def get_subcities(token):
        url = base_url + '/api/subcities/'
        headers = {'Authorization': f'Token {token}'}
        response = requests.get(url, headers=headers)
        return response
    
    # Get cities
    def get_cities(token):
        url = base_url + '/api/cities/'
        headers = {'Authorization': f'Token {token}'}
        response = requests.get(url, headers=headers)
        return response
    
    # Test super admin
    print("\nTesting super admin role...")
    token = login('<EMAIL>', 'admin123')
    if token:
        # Get cities
        response = get_cities(token)
        if response.status_code == 200:
            cities_data = response.json()
            print(f"  Super admin can access {len(cities_data)} cities")
        else:
            print(f"  Failed to get cities: {response.status_code}")
        
        # Get subcities
        response = get_subcities(token)
        if response.status_code == 200:
            subcities_data = response.json()
            print(f"  Super admin can access {len(subcities_data)} subcities")
        else:
            print(f"  Failed to get subcities: {response.status_code}")
        
        # Get centers
        response = get_centers(token)
        if response.status_code == 200:
            centers_data = response.json()
            print(f"  Super admin can access {len(centers_data)} centers")
        else:
            print(f"  Failed to get centers: {response.status_code}")
    else:
        print("  Failed to login as super admin")
    
    # Test city admin
    print("\nTesting city admin role...")
    city = City.objects.first()
    if city:
        city_admin = User.objects.filter(role='CITY_ADMIN', city=city).first()
        if city_admin:
            token = login(city_admin.email, 'password123')
            if token:
                # Get cities
                response = get_cities(token)
                if response.status_code == 200:
                    cities_data = response.json()
                    print(f"  City admin can access {len(cities_data)} cities")
                else:
                    print(f"  Failed to get cities: {response.status_code}")
                
                # Get subcities
                response = get_subcities(token)
                if response.status_code == 200:
                    subcities_data = response.json()
                    print(f"  City admin can access {len(subcities_data)} subcities")
                else:
                    print(f"  Failed to get subcities: {response.status_code}")
                
                # Get centers
                response = get_centers(token)
                if response.status_code == 200:
                    centers_data = response.json()
                    print(f"  City admin can access {len(centers_data)} centers")
                else:
                    print(f"  Failed to get centers: {response.status_code}")
            else:
                print("  Failed to login as city admin")
        else:
            print("  No city admin found")
    else:
        print("  No city found")
    
    # Test subcity admin
    print("\nTesting subcity admin role...")
    subcity = Subcity.objects.first()
    if subcity:
        subcity_admin = User.objects.filter(role='SUBCITY_ADMIN', subcity=subcity).first()
        if subcity_admin:
            token = login(subcity_admin.email, 'password123')
            if token:
                # Get subcities
                response = get_subcities(token)
                if response.status_code == 200:
                    subcities_data = response.json()
                    print(f"  Subcity admin can access {len(subcities_data)} subcities")
                else:
                    print(f"  Failed to get subcities: {response.status_code}")
                
                # Get centers
                response = get_centers(token)
                if response.status_code == 200:
                    centers_data = response.json()
                    print(f"  Subcity admin can access {len(centers_data)} centers")
                else:
                    print(f"  Failed to get centers: {response.status_code}")
            else:
                print("  Failed to login as subcity admin")
        else:
            print("  No subcity admin found")
    else:
        print("  No subcity found")
    
    # Test center admin
    print("\nTesting center admin role...")
    center = Center.objects.first()
    if center:
        center_admin = User.objects.filter(role='CENTER_ADMIN', center=center).first()
        if center_admin:
            token = login(center_admin.email, 'password123')
            if token:
                # Get centers
                response = get_centers(token)
                if response.status_code == 200:
                    centers_data = response.json()
                    print(f"  Center admin can access {len(centers_data)} centers")
                else:
                    print(f"  Failed to get centers: {response.status_code}")
            else:
                print("  Failed to login as center admin")
        else:
            print("  No center admin found")
    else:
        print("  No center found")

if __name__ == '__main__':
    test_hierarchy_isolation()
    test_api_hierarchy_isolation()
    test_user_roles()
