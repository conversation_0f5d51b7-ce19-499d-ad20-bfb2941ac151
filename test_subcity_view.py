import requests
import json

# Base URL
base_url = 'http://127.0.0.1:8000/api/'

# Token and schema name
token = '01aa7be65fbda335a0b29edd56c967ad6112fa6b'  # This is the hardcoded token from IDCardDetails.tsx
schema_name = 'subcity_zoble'  # Use the subcity schema

# Test the subcity view
print("\n=== Testing Subcity View ===")
try:
    url = base_url + f'tenant/{schema_name}/idcards/'
    headers = {
        'Authorization': f'Token {token}',
        'Content-Type': 'application/json',
        'X-Schema-Name': schema_name
    }
    
    print(f"URL: {url}")
    print(f"Headers: {headers}")
    
    response = requests.get(url, headers=headers)
    print(f"Status Code: {response.status_code}")
    
    if response.status_code == 200:
        data = response.json()
        if isinstance(data, dict) and 'results' in data:
            id_cards = data['results']
        else:
            id_cards = data
            
        print(f"Found {len(id_cards)} ID cards in subcity view")
        
        for card in id_cards:
            print(f"\nID Card {card.get('id')}:")
            print(f"  Status: {card.get('status')}")
            print(f"  Kebele Approval Status: {card.get('kebele_approval_status')}")
            print(f"  Kebele Pattern: {card.get('kebele_pattern') is not None}")
            
            # Check if this is a card from kebele 14 with status PENDING_SUBCITY
            if card.get('status') == 'PENDING_SUBCITY':
                print(f"  ✓ This card is pending subcity approval")
                
                # Try to approve this card
                print(f"\n=== Testing Subcity Approval for ID Card {card.get('id')} ===")
                approval_url = base_url + f'tenant/{schema_name}/idcards/{card.get("id")}/subcity_approve/'
                
                print(f"URL: {approval_url}")
                print(f"Headers: {headers}")
                
                approval_response = requests.post(
                    approval_url, 
                    headers=headers,
                    json={"notes": "Approved by subcity admin via test script"}
                )
                
                print(f"Status Code: {approval_response.status_code}")
                
                if approval_response.status_code == 200:
                    approval_data = approval_response.json()
                    print(f"Response Data: {json.dumps(approval_data, indent=2)}")
                    print(f"New Status: {approval_data.get('status')}")
                    
                    # Break after approving one card
                    break
                else:
                    print(f"Error: {approval_response.text}")
            else:
                print(f"  ✗ This card is not pending subcity approval")
    else:
        print(f"Error: {response.text}")
        
except Exception as e:
    print(f"Error testing subcity view: {str(e)}")
