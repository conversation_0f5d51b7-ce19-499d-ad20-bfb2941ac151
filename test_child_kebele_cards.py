import requests
import json

# Base URL
base_url = 'http://127.0.0.1:8000/api/'

# Token and schema name
token = '01aa7be65fbda335a0b29edd56c967ad6112fa6b'  # This is the hardcoded token from IDCardDetails.tsx
schema_name = 'subcity_zoble'  # Use the subcity schema

# Test the child_kebele_cards endpoint
print("\n=== Testing Child Kebele Cards Endpoint ===")
try:
    url = base_url + f'tenant/{schema_name}/idcards/child-kebele-cards/'
    headers = {
        'Authorization': f'Token {token}',
        'Content-Type': 'application/json',
        'X-Schema-Name': schema_name
    }

    print(f"URL: {url}")
    print(f"Headers: {headers}")

    response = requests.get(url, headers=headers)
    print(f"Status Code: {response.status_code}")

    if response.status_code == 200:
        data = response.json()
        print(f"Found {len(data)} ID cards from child kebeles")

        for card in data:
            print(f"\nID Card {card.get('id')}:")
            print(f"  Source Schema: {card.get('source_schema')}")
            print(f"  Status: {card.get('status')}")
            print(f"  Kebele Approval Status: {card.get('kebele_approval_status')}")
            print(f"  Kebele Pattern: {card.get('kebele_pattern') is not None}")
            print(f"  Citizen: {card.get('citizen', {}).get('first_name')} {card.get('citizen', {}).get('last_name')}")
    else:
        print(f"Error: {response.text}")

except Exception as e:
    print(f"Error testing child kebele cards endpoint: {str(e)}")
