import os
import django
import requests
import json

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'neocamelot.settings')
django.setup()

# Test the available-tenants API endpoint
print("\n=== Testing Available Tenants API ===")
try:
    # Test for subcity parents (when registering a kebele)
    response = requests.get("http://localhost:8000/api/available-tenants/?type=SUBCITY")
    print(f"Status Code: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        print(f"Response Data: {json.dumps(data, indent=2)}")
        print(f"Number of tenants returned: {len(data)}")
        for tenant in data:
            print(f"- {tenant['name']} (ID: {tenant['id']}, Type: {tenant['schema_type']}, Schema: {tenant['schema_name']})")
    else:
        print(f"Error: {response.text}")
    
    # Test for city parents (when registering a subcity)
    print("\n--- Testing for City Parents ---")
    response = requests.get("http://localhost:8000/api/available-tenants/?type=CITY")
    print(f"Status Code: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        print(f"Response Data: {json.dumps(data, indent=2)}")
        print(f"Number of tenants returned: {len(data)}")
        for tenant in data:
            print(f"- {tenant['name']} (ID: {tenant['id']}, Type: {tenant['schema_type']}, Schema: {tenant['schema_name']})")
    else:
        print(f"Error: {response.text}")
    
except Exception as e:
    print(f"Error testing API: {str(e)}")
