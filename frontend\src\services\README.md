# JWT Authentication Implementation

This document describes the JWT authentication implementation in the application.

## Overview

The application uses JWT (JSON Web Tokens) for authentication in a multi-tenant system. The implementation spans both backend (Django) and frontend (React) components.

## Frontend Implementation

### Token Storage

Tokens are stored in:
- In-memory `tokenStore` object
- localStorage (both general and schema-specific keys)
- HTTP-only cookies for refresh tokens

### Token Management

The `tokenService.ts` file provides a centralized way to manage JWT tokens:

- `storeTokensForSchema`: Stores tokens for a specific schema
- `getAccessTokenForSchema`: Retrieves access token for a schema
- `getRefreshTokenForSchema`: Retrieves refresh token for a schema
- `clearTokensForSchema`: Clears tokens for a specific schema
- `clearAllTokens`: Clears all tokens from storage

### Authentication Headers

The `getAuthHeaders` function in `tokenService.ts` generates authentication headers for API requests:

```typescript
const headers = {
  'Content-Type': 'application/json',
  'Authorization': `Bear<PERSON> ${accessToken}`,
  'X-Schema-Name': schemaName
};
```

### Token Refresh

The `refreshJWTTokens` function in `tokenService.ts` handles token refresh:

1. Gets the refresh token for the specified schema
2. Makes a refresh request to the backend
3. Stores the new tokens

## Backend Implementation

### Token Extraction

The `extract_token_from_request` function in `jwt_utils.py` extracts tokens from requests:

1. Authorization header with Bearer prefix
2. Authorization header with Token prefix (backward compatibility)
3. Request parameters (GET or POST)
4. Cookies

### Schema Extraction

The `get_schema_from_request` function in `jwt_utils.py` extracts schema names from requests:

1. X-Schema-Name header
2. schema_name cookie
3. JWT token in the request

### Middleware

The `JWTSchemaMiddleware` in `jwt_middleware.py` sets the tenant schema for requests based on the JWT token.

### Authentication

The `JWTAuthentication` class in `jwt_authentication.py` provides JWT authentication for Django REST Framework.

## Best Practices

1. Always use the `tokenService.ts` for token management
2. Always include the `X-Schema-Name` header in API requests
3. Always use the Bearer prefix for JWT tokens
4. Use HTTP-only cookies for refresh tokens
5. Implement token refresh before expiration
