import axios, { AxiosRequestConfig, AxiosResponse, AxiosError } from 'axios';
import { getAuthHeaders, getCurrentSchema, refreshJWTTokens, isTokenRefreshInProgress } from './tokenService';
import { API_BASE_URL } from '../config/apiConfig';

// Create an Axios instance with default configuration
// Use direct connection to backend server
const api = axios.create({
  baseURL: API_BASE_URL,  // Direct connection to backend server
  timeout: 10000,
  withCredentials: true, // Important: include credentials to send/receive cookies
});

// Request interceptor to add auth headers
api.interceptors.request.use(
  (config) => {
    // Get the current schema from unified token service
    const schema = getCurrentSchema() || '';

    // Get authentication headers
    const headers = getAuthHeaders(schema);

    // Add headers to the request
    // Use Object.assign to update headers to maintain the AxiosHeaders type
    Object.assign(config.headers, headers);

    console.log('API Request:', {
      url: config.url,
      method: config.method,
      headers: {
        'Content-Type': config.headers['Content-Type'],
        'Authorization': config.headers['Authorization'] ? 'Token [redacted]' : 'None',
        'X-Schema-Name': config.headers['X-Schema-Name'] || 'None',
      },
    });

    return config;
  },
  (error) => {
    console.error('API Request Error:', error);
    return Promise.reject(error);
  }
);

// Track failed requests to prevent infinite retry loops
const failedRequestsCache = new Map<string, number>();
const MAX_RETRY_ATTEMPTS = 2; // Maximum number of retry attempts per URL

// Response interceptor to handle errors
api.interceptors.response.use(
  (response) => {
    console.log('API Response:', {
      status: response.status,
      url: response.config.url,
      method: response.config.method,
    });

    // Clear the failed request count for this URL on success
    if (response.config.url) {
      failedRequestsCache.delete(response.config.url);
    }

    return response;
  },
  async (error: AxiosError) => {
    console.error('API Response Error:', {
      status: error.response?.status,
      url: error.config?.url,
      method: error.config?.method,
      message: error.message,
    });

    // Check if we should retry this request
    const requestUrl = error.config?.url || '';
    const currentAttempts = failedRequestsCache.get(requestUrl) || 0;

    // Handle 401 Unauthorized errors
    if (error.response?.status === 401) {
      console.log(`401 Unauthorized error, attempt ${currentAttempts + 1} for URL: ${requestUrl}`);

      // Check if we've exceeded the maximum retry attempts
      if (currentAttempts >= MAX_RETRY_ATTEMPTS) {
        console.error(`Maximum retry attempts (${MAX_RETRY_ATTEMPTS}) exceeded for URL: ${requestUrl}`);

        // Clear the failed request count for this URL
        failedRequestsCache.delete(requestUrl);

        // Set token expired flag
        localStorage.setItem('token_expired', 'true');
        localStorage.setItem('token_expired_reason', 'max_retry_attempts');

        return Promise.reject(new Error('Authentication failed after multiple retry attempts. Please log in again.'));
      }

      // Increment the failed request count for this URL
      failedRequestsCache.set(requestUrl, currentAttempts + 1);

      console.log('Trying to refresh token');

      // Try to refresh the token
      try {
        // Get the current schema from unified token service
        let schema = getCurrentSchema() || '';

        // If no schema, try to get it from localStorage
        if (!schema) {
          schema = localStorage.getItem('schema_name') || '';
          console.log('Using schema from localStorage:', schema);
        }

        // If still no schema, try to get it from the tenant object
        if (!schema) {
          try {
            const tenantStr = localStorage.getItem('tenant');
            if (tenantStr) {
              const tenant = JSON.parse(tenantStr);
              if (tenant && tenant.schema_name) {
                schema = tenant.schema_name;
                console.log('Using schema from tenant object:', schema);
              }
            }
          } catch (e) {
            console.error('Error parsing tenant from localStorage:', e);
          }
        }

        // If still no schema, try to extract it from the URL
        if (!schema && error.config?.url) {
          const urlMatch = error.config.url.match(/\/tenant\/([^\/]+)/);
          if (urlMatch && urlMatch[1]) {
            schema = urlMatch[1];
            console.log('Extracted schema from URL:', schema);

            // Store the schema for future use
            localStorage.setItem('jwt_schema', schema);
            localStorage.setItem('schema_name', schema);
          }
        }

        if (!schema) {
          console.error('No schema available for token refresh');
          return Promise.reject(error);
        }

        // Format schema (replace spaces with underscores if needed)
        // Most schema names like "kebele14" don't have spaces, so this won't change them
        const formattedSchema = schema.replace(/\s+/g, '_');
        if (formattedSchema !== schema) {
          console.log(`Formatted schema from "${schema}" to "${formattedSchema}"`);
          schema = formattedSchema;

          // Store the formatted schema
          localStorage.setItem('jwt_schema', formattedSchema);
          localStorage.setItem('schema_name', formattedSchema);
        }

        console.log('Attempting to refresh token with schema:', schema);

        // Check if a token refresh is already in progress for this schema
        if (isTokenRefreshInProgress(schema)) {
          console.log(`Token refresh already in progress for schema ${schema}, waiting for it to complete...`);

          // Wait for a short time to allow the in-progress refresh to complete
          await new Promise(resolve => setTimeout(resolve, 1000));

          // Check if the refresh is still in progress
          if (isTokenRefreshInProgress(schema)) {
            console.log(`Token refresh still in progress for schema ${schema} after waiting, will use existing promise`);
            // We'll use the existing promise, so we don't need to force a refresh
          }
        } else {
          console.log(`No token refresh in progress for schema ${schema}, initiating new refresh`);
        }

        // Add a small delay before refreshing to prevent race conditions
        await new Promise(resolve => setTimeout(resolve, 100));

        // Call refreshJWTTokens which will use the HttpOnly cookie
        // Only force refresh if there's no refresh in progress
        const refreshResult = await refreshJWTTokens(schema, !isTokenRefreshInProgress(schema));

        if (refreshResult && refreshResult.access_token) {
          console.log('Token refreshed successfully, retrying request');

          // Store the token in localStorage as a backup
          localStorage.setItem('jwt_access_token', refreshResult.access_token);
          if (refreshResult.refresh_token) {
            localStorage.setItem('jwt_refresh_token', refreshResult.refresh_token);
          }

          // Retry the original request with the new token
          const originalRequest = error.config;
          if (originalRequest) {
            // Get new auth headers with the refreshed access token
            const headers = getAuthHeaders(schema);

            // Update the Authorization header
            // Use Object.assign to update headers to maintain the AxiosHeaders type
            Object.assign(originalRequest.headers, headers);

            // Make sure credentials are included to send cookies
            originalRequest.withCredentials = true;

            // Retry the request
            return api(originalRequest);
          }
        }
      } catch (refreshError) {
        console.error('Error refreshing token:', refreshError);

        // Check if this is a SESSION_EXPIRED error, which means the user needs to log in again
        if (refreshError instanceof Error && refreshError.message === 'SESSION_EXPIRED') {
          console.log('Session expired during token refresh. User will be redirected to login.');

          // The tokenService has already set the token_expired flag and redirected to login
          // Just reject the promise to stop the request chain
          return Promise.reject(new Error('Authentication failed. Please log in again.'));
        }

        // If refresh fails, try with a different schema as a last resort
        try {
          // Try to get the schema from the tenant object
          const tenantStr = localStorage.getItem('tenant');
          if (tenantStr) {
            const tenant = JSON.parse(tenantStr);
            if (tenant && tenant.schema_name) {
              const altSchema = tenant.schema_name;
              console.log('Trying refresh with alternative schema:', altSchema);

              // Call refreshJWTTokens which will use the HttpOnly cookie
              const altRefreshResult = await refreshJWTTokens(altSchema);

              if (altRefreshResult && altRefreshResult.access_token) {
                console.log('Token refreshed successfully with alternative schema');

                // Update schema in localStorage
                localStorage.setItem('jwt_schema', altSchema);
                localStorage.setItem('schema_name', altSchema);

                // Retry the original request with the new token
                const originalRequest = error.config;
                if (originalRequest) {
                  // Get new auth headers
                  const headers = getAuthHeaders(altSchema);

                  // Update the Authorization header
                  Object.assign(originalRequest.headers, headers);

                  // Make sure credentials are included to send cookies
                  originalRequest.withCredentials = true;

                  // Retry the request
                  return api(originalRequest);
                }
              }
            }
          }
        } catch (altError) {
          console.error('Error with alternative schema refresh:', altError);

          // Check if this is a SESSION_EXPIRED error from the alternative schema refresh
          if (altError instanceof Error && altError.message === 'SESSION_EXPIRED') {
            console.log('Session expired during alternative schema token refresh. User will be redirected to login.');
            return Promise.reject(new Error('Authentication failed. Please log in again.'));
          }
        }
      }
    }

    return Promise.reject(error);
  }
);

// Helper function to make API requests to tenant-specific endpoints
export const tenantApi = {
  // Get data from a tenant-specific endpoint
  get: async <T>(tenantSchema: string, endpoint: string, config?: AxiosRequestConfig): Promise<T> => {
    try {
      // DIAGNOSTIC: Check for schema mismatches
      console.log('API GET SCHEMA DIAGNOSTIC:');
      console.log('- Original schema passed to API:', tenantSchema);
      console.log('- localStorage.getItem("schema_name"):', localStorage.getItem('schema_name'));
      console.log('- localStorage.getItem("jwt_schema"):', localStorage.getItem('jwt_schema'));

      // Format schema (replace spaces with underscores if needed)
      // Most schema names like "kebele14" don't have spaces, so this won't change them
      const formattedSchema = tenantSchema.replace(/\s+/g, '_');
      console.log('- Formatted schema:', formattedSchema);

      // Check if there's a mismatch
      if (formattedSchema !== localStorage.getItem('schema_name') ||
          formattedSchema !== localStorage.getItem('jwt_schema')) {
        console.warn('SCHEMA MISMATCH DETECTED in API call!');
      }

      // Store the schema for future use
      localStorage.setItem('jwt_schema', formattedSchema);
      localStorage.setItem('schema_name', formattedSchema);

      // Create the tenant-specific URL
      const url = `/api/tenant/${formattedSchema}/${endpoint.replace(/^\/api\//, '')}`;
      console.log(`Making GET request to endpoint: ${url}`);

      // Create a new config with the schema name in the headers
      const requestConfig: AxiosRequestConfig = {
        ...config,
        headers: {
          ...config?.headers,
          'X-Schema-Name': formattedSchema
        }
      };

      const response = await api.get<T>(url, requestConfig);
      return response.data;
    } catch (error) {
      console.error(`Error getting data from endpoint ${endpoint}:`, error);
      throw error;
    }
  },

  // Post data to a tenant-specific endpoint
  post: async <T>(tenantSchema: string, endpoint: string, data: any, config?: AxiosRequestConfig): Promise<T> => {
    try {
      // Format schema (replace spaces with underscores if needed)
      // Most schema names like "kebele14" don't have spaces, so this won't change them
      const formattedSchema = tenantSchema.replace(/\s+/g, '_');

      // Store the schema for future use
      localStorage.setItem('jwt_schema', formattedSchema);
      localStorage.setItem('schema_name', formattedSchema);

      // Create the tenant-specific URL
      const url = `/api/tenant/${formattedSchema}/${endpoint.replace(/^\/api\//, '')}`;
      console.log(`Making POST request to endpoint: ${url}`);

      // Create a new config with the schema name in the headers
      const requestConfig: AxiosRequestConfig = {
        ...config,
        headers: {
          ...config?.headers,
          'X-Schema-Name': formattedSchema
        }
      };

      const response = await api.post<T>(url, data, requestConfig);
      return response.data;
    } catch (error) {
      console.error(`Error posting data to endpoint ${endpoint}:`, error);
      throw error;
    }
  },

  // Put data to a tenant-specific endpoint
  put: async <T>(tenantSchema: string, endpoint: string, data: any, config?: AxiosRequestConfig): Promise<T> => {
    try {
      // Format schema (replace spaces with underscores if needed)
      // Most schema names like "kebele14" don't have spaces, so this won't change them
      const formattedSchema = tenantSchema.replace(/\s+/g, '_');

      // Store the schema for future use
      localStorage.setItem('jwt_schema', formattedSchema);
      localStorage.setItem('schema_name', formattedSchema);

      // Create the tenant-specific URL
      const url = `/api/tenant/${formattedSchema}/${endpoint.replace(/^\/api\//, '')}`;
      console.log(`Making PUT request to endpoint: ${url}`);

      // Create a new config with the schema name in the headers
      const requestConfig: AxiosRequestConfig = {
        ...config,
        headers: {
          ...config?.headers,
          'X-Schema-Name': formattedSchema
        }
      };

      const response = await api.put<T>(url, data, requestConfig);
      return response.data;
    } catch (error) {
      console.error(`Error putting data to endpoint ${endpoint}:`, error);
      throw error;
    }
  },

  // Delete data from a tenant-specific endpoint
  delete: async <T>(tenantSchema: string, endpoint: string, config?: AxiosRequestConfig): Promise<T> => {
    try {
      // Format schema (replace spaces with underscores if needed)
      // Most schema names like "kebele14" don't have spaces, so this won't change them
      const formattedSchema = tenantSchema.replace(/\s+/g, '_');

      // Store the schema for future use
      localStorage.setItem('jwt_schema', formattedSchema);
      localStorage.setItem('schema_name', formattedSchema);

      // Create the tenant-specific URL
      const url = `/api/tenant/${formattedSchema}/${endpoint.replace(/^\/api\//, '')}`;
      console.log(`Making DELETE request to endpoint: ${url}`);

      // Create a new config with the schema name in the headers
      const requestConfig: AxiosRequestConfig = {
        ...config,
        headers: {
          ...config?.headers,
          'X-Schema-Name': formattedSchema
        }
      };

      const response = await api.delete<T>(url, requestConfig);
      return response.data;
    } catch (error) {
      console.error(`Error deleting data from endpoint ${endpoint}:`, error);
      throw error;
    }
  },
};

// Helper function to check if the API is accessible
export const checkApiConnection = async (): Promise<boolean> => {
  try {
    console.log('Checking API connection...');
    const response = await api.get('/api/health/', {
      timeout: 5000, // Short timeout for quick feedback
    });
    console.log('API connection check result:', response.status, response.data);
    return response.status === 200 && response.data?.status === 'ok';
  } catch (error) {
    console.error('API connection check failed:', error);

    // Try alternative endpoint as fallback
    try {
      console.log('Trying alternative health check endpoint...');
      const altResponse = await api.get('/api/debug-cors/', {
        timeout: 5000,
      });
      console.log('Alternative API connection check result:', altResponse.status);
      return altResponse.status === 200;
    } catch (altError) {
      console.error('Alternative API connection check failed:', altError);
      return false;
    }
  }
};

export default api;
