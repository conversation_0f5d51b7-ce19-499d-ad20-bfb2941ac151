import React from 'react';
import { Box, Typography, Button, Paper, Container } from '@mui/material';
import { useNavigate } from 'react-router-dom';
import BlockIcon from '@mui/icons-material/Block';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';

interface UnauthorizedAccessProps {
  message?: string;
  tenantType?: string;
  userRole?: string;
}

const UnauthorizedAccess: React.FC<UnauthorizedAccessProps> = ({
  message = "You don't have permission to access this page.",
  tenantType,
  userRole = localStorage.getItem('user_role')
}) => {
  const navigate = useNavigate();

  return (
    <Container maxWidth="md" sx={{ py: 8 }}>
      <Paper
        elevation={0}
        sx={{
          p: 4,
          borderRadius: 3,
          textAlign: 'center',
          border: '1px solid rgba(0, 0, 0, 0.08)',
          boxShadow: '0 4px 20px rgba(0, 0, 0, 0.05)'
        }}
      >
        <Box
          sx={{
            width: 80,
            height: 80,
            bgcolor: '#f44336',
            color: 'white',
            borderRadius: '50%',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            mx: 'auto',
            mb: 3
          }}
        >
          <BlockIcon sx={{ fontSize: 40 }} />
        </Box>

        <Typography variant="h4" gutterBottom fontWeight="bold">
          Unauthorized Access
        </Typography>

        <Typography variant="body1" color="text.secondary" sx={{ mb: 3, maxWidth: 600, mx: 'auto' }}>
          {message}
        </Typography>

        {tenantType && (
          <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
            <strong>Tenant type:</strong> {tenantType}
          </Typography>
        )}

        {userRole && (
          <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
            <strong>User role:</strong> {userRole}
          </Typography>
        )}

        <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
          {tenantType === 'CITY' && 'City level tenants can only access reports and settings.'}
          {tenantType === 'SUBCITY' && 'Subcity level tenants can only access ID card printing and reports.'}
          {tenantType === 'KEBELE' && 'Kebele level tenants can access citizen registration and ID card management.'}
          {tenantType !== 'CITY' && tenantType !== 'SUBCITY' && tenantType !== 'KEBELE' && tenantType &&
            `Your tenant type is "${tenantType}". This may not be recognized by the system.`}

          {userRole === 'CENTER_STAFF' && ' CENTER_STAFF users can register citizens, view citizens list, view citizen details, generate ID cards, view ID cards list, and send ID cards to kebele leaders.'}
          {userRole === 'KEBELE_LEADER' && ' KEBELE_LEADER users can approve ID cards and verify documents.'}
          {userRole === 'CENTER_ADMIN' && ' CENTER_ADMIN users can manage citizens and ID cards.'}
          {userRole === 'SUBCITY_ADMIN' && ' SUBCITY_ADMIN users can manage kebele users and print ID cards.'}
        </Typography>

        <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
          If you believe you should have access to this page, please contact your administrator.
        </Typography>

        <Button
          variant="contained"
          color="primary"
          startIcon={<ArrowBackIcon />}
          onClick={() => navigate('/dashboard')}
          sx={{ mt: 2 }}
        >
          Back to Dashboard
        </Button>
      </Paper>
    </Container>
  );
};

export default UnauthorizedAccess;
