# Generated by Django 4.2.7 on 2025-05-25 08:49

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('centers', '0020_add_center_to_family_models'),
        ('citizens', '0028_parent_relationship_type'),
    ]

    operations = [
        migrations.AddField(
            model_name='child',
            name='center',
            field=models.ForeignKey(default=1, on_delete=django.db.models.deletion.CASCADE, related_name='%(class)ss', to='centers.kebele', verbose_name='Center'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='emergencycontact',
            name='center',
            field=models.ForeignKey(default=1, on_delete=django.db.models.deletion.CASCADE, related_name='%(class)ss', to='centers.kebele', verbose_name='Center'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='parent',
            name='center',
            field=models.ForeignKey(default=1, on_delete=django.db.models.deletion.CASCADE, related_name='%(class)ss', to='centers.kebele', verbose_name='Center'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='spouse',
            name='center',
            field=models.ForeignKey(default=1, on_delete=django.db.models.deletion.CASCADE, related_name='%(class)ss', to='centers.kebele', verbose_name='Center'),
            preserve_default=False,
        ),
    ]
