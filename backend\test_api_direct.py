import os
import django
import requests
import json

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'neocamelot.settings')
django.setup()

# Import models
from centers.models import Client

# Print all tenants from the database
print("\n=== All Tenants in Database ===")
tenants = Client.objects.all()
for tenant in tenants:
    print(f"ID: {tenant.id}")
    print(f"Name: {tenant.name}")
    print(f"Schema Name: {tenant.schema_name}")
    print(f"Schema Type: {tenant.schema_type}")
    print(f"Schema Type Type: {type(tenant.schema_type)}")
    if tenant.parent:
        print(f"Parent: {tenant.parent.name} ({tenant.parent.schema_type})")
    else:
        print("Parent: None")
    print("-" * 30)

# Test the available-tenants API endpoint directly
print("\n=== Testing Available Tenants API for Kebele ===")
try:
    # Test for subcity parents (when registering a kebele)
    response = requests.get("http://localhost:8000/api/available-tenants/?type=CENTER")
    print(f"Status Code: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        print(f"Response Data: {json.dumps(data, indent=2)}")
        print(f"Number of tenants returned: {len(data)}")
        for tenant in data:
            print(f"- {tenant['name']} (ID: {tenant['id']}, Type: {tenant['schema_type']}, Schema: {tenant['schema_name']})")
    else:
        print(f"Error: {response.text}")
    
except Exception as e:
    print(f"Error testing API: {str(e)}")

# Test the API with curl equivalent
print("\n=== Testing with curl equivalent ===")
import subprocess
try:
    result = subprocess.run(["curl", "-s", "http://localhost:8000/api/available-tenants/?type=CENTER"], 
                           capture_output=True, text=True)
    print(f"Curl output: {result.stdout}")
    if result.stdout:
        try:
            data = json.loads(result.stdout)
            print(f"Parsed JSON: {json.dumps(data, indent=2)}")
        except json.JSONDecodeError:
            print("Failed to parse JSON response")
except Exception as e:
    print(f"Error running curl: {str(e)}")

# Test the frontend fetch logic
print("\n=== Testing Frontend Fetch Logic ===")
try:
    response = requests.get("http://localhost:8000/api/available-tenants/?type=SUBCITY")
    if response.status_code == 200:
        data = response.json()
        print(f"API Response: {json.dumps(data, indent=2)}")
        
        # Simulate frontend filtering logic
        expected_type = "SUBCITY"
        valid_parents = [tenant for tenant in data if tenant["schema_type"].upper() == expected_type.upper()]
        
        print(f"After filtering for {expected_type}:")
        print(f"Valid parents: {json.dumps(valid_parents, indent=2)}")
        print(f"Number of valid parents: {len(valid_parents)}")
    else:
        print(f"Error: {response.text}")
except Exception as e:
    print(f"Error simulating frontend logic: {str(e)}")
