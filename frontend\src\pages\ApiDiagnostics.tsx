import React from 'react';
import { Container, Typography, Box, Divider, Paper, Alert, Link } from '@mui/material';
import ApiHealthCheck from '../components/ApiHealthCheck';
import ApiTest from '../components/ApiTest';

/**
 * ApiDiagnostics page
 * 
 * This page provides tools to diagnose API connection issues.
 */
const ApiDiagnostics: React.FC = () => {
  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Typography variant="h4" component="h1" gutterBottom>
        API Diagnostics
      </Typography>
      
      <Paper elevation={2} sx={{ p: 3, mb: 4, bgcolor: 'info.light', color: 'info.contrastText' }}>
        <Typography variant="h6" gutterBottom>
          Important Information
        </Typography>
        <Typography variant="body1">
          This page provides tools to diagnose API connection issues. If you're experiencing problems with API requests,
          try using the direct API connection (localhost:8000) instead of the proxy connection.
        </Typography>
      </Paper>
      
      <Box sx={{ mb: 4 }}>
        <ApiHealthCheck />
      </Box>
      
      <Divider sx={{ mb: 4 }} />
      
      <Box>
        <ApiTest />
      </Box>
      
      <Paper elevation={2} sx={{ p: 3, mt: 4 }}>
        <Typography variant="h6" gutterBottom>
          Troubleshooting Guide
        </Typography>
        <Typography variant="body1" paragraph>
          If you're experiencing API connection issues, try the following steps:
        </Typography>
        <ol>
          <li>
            <Typography variant="body1" paragraph>
              <strong>Check if the backend server is running:</strong> Make sure the Django backend server is running at http://localhost:8000.
              You can verify this by opening <Link href="http://localhost:8000/admin/" target="_blank" rel="noopener">http://localhost:8000/admin/</Link> in your browser.
            </Typography>
          </li>
          <li>
            <Typography variant="body1" paragraph>
              <strong>Try direct API connection:</strong> Use the "Direct API" option in the tools above to bypass the Vite proxy.
              This connects directly to the backend server at http://localhost:8000.
            </Typography>
          </li>
          <li>
            <Typography variant="body1" paragraph>
              <strong>Check browser console for errors:</strong> Open your browser's developer tools (F12) and look for any errors in the console.
            </Typography>
          </li>
          <li>
            <Typography variant="body1" paragraph>
              <strong>Check CORS configuration:</strong> If you see CORS errors in the console, make sure the backend server is configured to allow requests from http://localhost:5173.
            </Typography>
          </li>
          <li>
            <Typography variant="body1" paragraph>
              <strong>Check authentication:</strong> Make sure you're logged in and have a valid token. You can try logging out and logging back in.
            </Typography>
          </li>
        </ol>
      </Paper>
    </Container>
  );
};

export default ApiDiagnostics;
