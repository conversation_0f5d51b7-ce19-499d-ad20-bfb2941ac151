from rest_framework import serializers
from rest_framework.decorators import action
from rest_framework.response import Response
import uuid
from datetime import date
from django.core.exceptions import ValidationError
from .models import Citizen
from .models_family import Child, Parent, EmergencyContact, Spouse
from .models_document import Document
from .models_biometric import Photo
from .models_base import validate_age
from centers.serializers import CenterListSerializer
from centers.models_region import Country

class CitizenSerializer(serializers.ModelSerializer):
    """Serializer for the Citizen model."""
    center_details = CenterListSerializer(source='center', read_only=True)
    created_by_name = serializers.CharField(source='created_by.get_full_name', read_only=True)
    generate_id = serializers.SerializerMethodField(read_only=True)
    age = serializers.SerializerMethodField(read_only=True)
    full_name = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = Citizen
        fields = '__all__'
        read_only_fields = ('digital_id', 'uuid', 'id_number', 'created_at', 'updated_at', 'generate_id', 'age', 'full_name')
        extra_kwargs = {'center': {'write_only': True}, 'created_by': {'write_only': True}}
        ref_name = "CitizenDetail"

    def get_generate_id(self, obj):
        """Method field to provide a URL for generating ID."""
        return True

    def get_age(self, obj):
        """Calculate the age of the citizen."""
        today = date.today()
        born = obj.date_of_birth
        return today.year - born.year - ((today.month, today.day) < (born.month, born.day))

    def get_full_name(self, obj):
        """Get the full name of the citizen."""
        return obj.get_full_name()

    def validate_date_of_birth(self, value):
        """Validate that the citizen is at least 18 years old."""
        try:
            validate_age(value, min_age=18)
            return value
        except ValidationError as e:
            raise serializers.ValidationError(str(e))

class CitizenListSerializer(serializers.ModelSerializer):
    """Simplified serializer for listing citizens."""
    center_name = serializers.CharField(source='center.name', read_only=True)
    id_number = serializers.CharField(read_only=True)
    digital_id = serializers.CharField(read_only=True)

    class Meta:
        model = Citizen
        fields = ('id', 'id_number', 'digital_id', 'first_name', 'last_name',
                  'gender', 'date_of_birth', 'center_name', 'is_active')
        ref_name = "CitizensList"

class CitizenDetailSerializer(serializers.ModelSerializer):
    """Detailed serializer for citizen information."""
    center_details = CenterListSerializer(source='center', read_only=True)
    created_by_name = serializers.CharField(source='created_by.get_full_name', read_only=True)
    id_cards = serializers.SerializerMethodField()
    documents = serializers.SerializerMethodField()
    children = serializers.SerializerMethodField()
    parents = serializers.SerializerMethodField()
    emergency_contacts = serializers.SerializerMethodField()
    spouses = serializers.SerializerMethodField()
    photo = serializers.SerializerMethodField()
    age = serializers.SerializerMethodField()
    full_name = serializers.SerializerMethodField()

    class Meta:
        model = Citizen
        fields = '__all__'
        read_only_fields = ('digital_id', 'uuid', 'id_number', 'created_at', 'updated_at',
                           'center_details', 'created_by_name', 'id_cards', 'documents',
                           'children', 'parents', 'emergency_contacts', 'spouses', 'photo',
                           'age', 'full_name')
        ref_name = "CitizenFullDetail"

    def get_id_cards(self, obj):
        from idcards.serializers import IDCardListSerializer
        return IDCardListSerializer(obj.id_cards.all(), many=True).data

    def get_documents(self, obj):
        from .serializers_document import DocumentSerializer
        return DocumentSerializer(obj.documents.all(), many=True, context=self.context).data

    def get_children(self, obj):
        from .serializers_family import ChildSerializer
        return ChildSerializer(obj.children.all(), many=True, context=self.context).data

    def get_parents(self, obj):
        from .serializers_family import ParentSerializer
        return ParentSerializer(obj.parents.all(), many=True, context=self.context).data

    def get_emergency_contacts(self, obj):
        from .serializers_family import EmergencyContactSerializer
        return EmergencyContactSerializer(obj.emergency_contacts.all(), many=True, context=self.context).data

    def get_spouses(self, obj):
        from .serializers_family import SpouseSerializer
        return SpouseSerializer(obj.spouses.all(), many=True, context=self.context).data

    def get_photo(self, obj):
        from .serializers_biometric import PhotoSerializer
        if hasattr(obj, 'photo_obj') and obj.photo_obj:
            return PhotoSerializer(obj.photo_obj, context=self.context).data
        return None

    def get_age(self, obj):
        """Calculate the age of the citizen."""
        today = date.today()
        born = obj.date_of_birth
        return today.year - born.year - ((today.month, today.day) < (born.month, born.day))

    def get_full_name(self, obj):
        """Get the full name of the citizen."""
        return obj.get_full_name()
