import os
import django
import sys

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'neocamelot.settings')
django.setup()

from django.db import connection
from centers.models import Client

# Get the schema name from command line argument
if len(sys.argv) > 1:
    schema_name = sys.argv[1]
else:
    print("Please provide a schema name as a command line argument")
    sys.exit(1)

# Set to public schema
connection.set_schema_to_public()

try:
    # Get the tenant by schema name
    tenant = Client.objects.get(schema_name=schema_name)
    print(f"Found tenant: {tenant.name} (ID: {tenant.id})")
    print(f"Schema name: {tenant.schema_name}")
    print(f"Schema type: {tenant.schema_type}")
    print(f"Parent: {tenant.parent.name if tenant.parent else 'None'}")

    # List all tenants
    print("\nAll tenants:")
    for t in Client.objects.all():
        print(f"- {t.name} (Schema: {t.schema_name}, Type: {t.schema_type})")

except Client.DoesNotExist:
    print(f"Tenant with schema {schema_name} does not exist")

    # List all tenants
    print("\nAll tenants:")
    for t in Client.objects.all():
        print(f"- {t.name} (Schema: {t.schema_name}, Type: {t.schema_type})")

except Exception as e:
    print(f"Error: {str(e)}")
