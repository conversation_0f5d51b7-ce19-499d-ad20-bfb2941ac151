import os
import django
import json

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'neocamelot.settings')
django.setup()

from django.contrib.auth import get_user_model
from rest_framework.authtoken.models import Token
from django.db import connection

User = get_user_model()

def get_subcity_admin_users():
    """Get all users with subcity admin role."""
    print("=== Finding Subcity Admin Users ===")
    
    # Get all schemas in the database
    with connection.cursor() as cursor:
        cursor.execute("""
            SELECT schema_name
            FROM information_schema.schemata
            WHERE schema_name LIKE 'subcity%'
            ORDER BY schema_name
        """)
        schemas = cursor.fetchall()
        
        print(f"Found {len(schemas)} subcity schemas in the database")
        
        subcity_admin_users = []
        
        # Process each schema
        for schema in schemas:
            schema_name = schema[0]
            print(f"\nProcessing schema: {schema_name}")
            
            # Check if the accounts_user table exists in this schema
            cursor.execute(f"""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_schema = '{schema_name}' 
                    AND table_name = 'accounts_user'
                )
            """)
            table_exists = cursor.fetchone()[0]
            
            if not table_exists:
                print(f"  accounts_user table does not exist in schema {schema_name}")
                continue
            
            # Get all users with subcity admin role in this schema
            cursor.execute(f"""
                SELECT id, username, email, first_name, last_name, role
                FROM "{schema_name}".accounts_user
                WHERE role = 'SUBCITY_ADMIN'
                ORDER BY id
            """)
            users = cursor.fetchall()
            
            print(f"  Found {len(users)} subcity admin users in schema {schema_name}")
            
            for user in users:
                user_id, username, email, first_name, last_name, role = user
                print(f"  User {user_id}: {username} ({email}) - {first_name} {last_name} - {role}")
                subcity_admin_users.append({
                    'id': user_id,
                    'username': username,
                    'email': email,
                    'first_name': first_name,
                    'last_name': last_name,
                    'role': role,
                    'schema': schema_name
                })
        
        return subcity_admin_users

def generate_token_for_user(user_info):
    """Generate a token for a user."""
    print(f"\n=== Generating Token for User {user_info['username']} ===")
    
    # Check if the user exists in the public schema
    try:
        user = User.objects.get(username=user_info['username'])
        print(f"User {user.username} found in public schema")
    except User.DoesNotExist:
        print(f"User {user_info['username']} not found in public schema")
        print("Creating user in public schema...")
        
        # Create the user in the public schema
        user = User.objects.create_user(
            username=user_info['username'],
            email=user_info['email'],
            first_name=user_info['first_name'],
            last_name=user_info['last_name'],
            password='password123'  # Set a default password
        )
        print(f"User {user.username} created in public schema")
    
    # Generate a token for the user
    token, created = Token.objects.get_or_create(user=user)
    
    if created:
        print(f"Token created for user {user.username}: {token.key}")
    else:
        print(f"Token already exists for user {user.username}: {token.key}")
    
    return token.key

def main():
    print("=== Subcity Admin Token Generator ===")
    
    # Get all subcity admin users
    subcity_admin_users = get_subcity_admin_users()
    
    if not subcity_admin_users:
        print("\nNo subcity admin users found")
        return
    
    # Generate tokens for all subcity admin users
    for user_info in subcity_admin_users:
        token_key = generate_token_for_user(user_info)
        
        print(f"\nToken for {user_info['username']} ({user_info['schema']}):")
        print(f"Token: {token_key}")
        print(f"Use this token in the Authorization header: Token {token_key}")
        print(f"Or use it in the frontend by setting localStorage.token = '{token_key}'")
    
    print("\n=== Token Generation Complete ===")

if __name__ == "__main__":
    main()
