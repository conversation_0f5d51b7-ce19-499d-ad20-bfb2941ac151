# Authentication Token Management

This document provides instructions on how to manage authentication tokens in the system.

## Background

The system uses two different authentication mechanisms:

1. **Token-based authentication** (using `authtoken_token` table)
2. **JWT-based authentication** (using JWT tokens with refresh tokens stored in cookies)

The system is transitioning from token-based authentication to JWT-based authentication, but there's a conflict between the two systems. When JWT tokens are refreshed, the old tokens are blacklisted (added to `accounts_blacklistedtoken`), but new tokens aren't being properly created in the `authtoken_token` table.

## Symptoms of Token Issues

If you're experiencing any of the following issues, you may have a token problem:

- Authentication errors when submitting forms
- "Your session has expired" messages
- Empty `authtoken_token` table but many entries in `accounts_blacklistedtoken`
- API calls returning 401 Unauthorized errors

## Solution: Using the Token Creation Script

We've created a script to manually create tokens in the `authtoken_token` table. This script can be used to fix authentication issues when the automatic token creation fails.

### Prerequisites

- Python 3.x
- Django environment set up
- Access to the database

### Usage

1. Copy the `create_auth_token.py` script to the root directory of your backend project.

2. List all available schemas:

```bash
python create_auth_token.py list_schemas
```

3. Create a token for a specific user in a specific schema:

```bash
python create_auth_token.py create_for_user <email> <schema_name>
```

Example:
```bash
python create_auth_token.py create_for_user <EMAIL> kebele14
```

4. Create tokens for all users in a specific schema:

```bash
python create_auth_token.py create_for_all <schema_name>
```

Example:
```bash
python create_auth_token.py create_for_all kebele14
```

### Example Workflow

If you're experiencing authentication issues in a specific tenant (e.g., kebele14), follow these steps:

1. List all schemas to confirm the schema name:
```bash
python create_auth_token.py list_schemas
```

2. Create tokens for all users in that schema:
```bash
python create_auth_token.py create_for_all kebele14
```

3. If you know which specific user is having issues, create a token just for that user:
```bash
python create_auth_token.py create_for_user <EMAIL> kebele14
```

4. Refresh the application and try again.

## Frontend Changes

The frontend code has been updated to handle both authentication mechanisms:

1. Enhanced token handling with multiple fallback mechanisms
2. Automatic token creation when authentication fails
3. Support for both JWT and token-based authentication
4. Proper schema formatting for API URLs

## Troubleshooting

If you're still experiencing authentication issues after running the script:

1. Check the database to confirm that tokens were created:
```sql
-- Connect to the tenant schema
SET search_path TO kebele14;

-- Check if tokens were created
SELECT * FROM authtoken_token;

-- Check blacklisted tokens
SELECT * FROM accounts_blacklistedtoken;
```

2. Clear blacklisted tokens if necessary:
```sql
-- Connect to the tenant schema
SET search_path TO kebele14;

-- Delete blacklisted tokens
DELETE FROM accounts_blacklistedtoken;
```

3. Check the browser's localStorage and cookies:
   - Open the browser's developer tools
   - Go to the Application tab
   - Check localStorage for tokens
   - Check cookies for JWT tokens

4. Try logging out and logging in again.

## Long-term Solution

The long-term solution is to complete the transition to JWT-based authentication and remove the token-based authentication entirely. This will prevent conflicts between the two systems and ensure consistent authentication behavior.

## Support

If you continue to experience authentication issues, please contact the development team for assistance.
