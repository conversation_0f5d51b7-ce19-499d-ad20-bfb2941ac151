from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from centers.models import Center
from citizens.models import Citizen
from idcards.models import ID<PERSON>ardTemplate, IDCard
from django.utils.text import slugify
import random
from datetime import date, timedelta

User = get_user_model()

class Command(BaseCommand):
    help = 'Create sample data for testing the NeoCamelot system'

    def add_arguments(self, parser):
        parser.add_argument('--centers', type=int, default=3, help='Number of centers to create')
        parser.add_argument('--users', type=int, default=10, help='Number of users to create')
        parser.add_argument('--citizens', type=int, default=50, help='Number of citizens to create')
        parser.add_argument('--templates', type=int, default=5, help='Number of ID card templates to create')
        parser.add_argument('--cards', type=int, default=30, help='Number of ID cards to create')

    def handle(self, *args, **options):
        num_centers = options['centers']
        num_users = options['users']
        num_citizens = options['citizens']
        num_templates = options['templates']
        num_cards = options['cards']
        
        # Create a superuser if none exists
        if not User.objects.filter(is_superuser=True).exists():
            User.objects.create_superuser(
                email='<EMAIL>',
                password='admin123',
                first_name='Super',
                last_name='Admin'
            )
            self.stdout.write(self.style.SUCCESS('Created superuser: <EMAIL>'))
        
        # Create centers
        centers = []
        center_names = ['Central Office', 'North District', 'South District', 'East District', 
                        'West District', 'Downtown', 'Uptown', 'Riverside', 'Hillside']
        
        for i in range(num_centers):
            name = center_names[i] if i < len(center_names) else f"Center {i+1}"
            center = Center.objects.create(
                name=name,
                slug=slugify(name),
                address=f"123 Main St, District {i+1}",
                phone=f"+251-9{random.randint(10000000, 99999999)}",
                email=f"center{i+1}@neocamelot.com"
            )
            centers.append(center)
            self.stdout.write(self.style.SUCCESS(f'Created center: {center.name}'))
        
        # Create users
        users = []
        roles = ['CENTER_ADMIN', 'CENTER_STAFF']
        
        for i in range(num_users):
            role = roles[i % len(roles)]
            center = centers[i % len(centers)] if role != 'SUPER_ADMIN' else None
            
            user = User.objects.create_user(
                email=f"user{i+1}@neocamelot.com",
                password=f"password{i+1}",
                first_name=f"User{i+1}",
                last_name=f"Test",
                role=role,
                center=center
            )
            users.append(user)
            self.stdout.write(self.style.SUCCESS(f'Created user: {user.email} ({role})'))
        
        # Create citizens
        citizens = []
        first_names = ['Abebe', 'Kebede', 'Almaz', 'Tigist', 'Dawit', 'Yonas', 'Hanna', 
                      'Meron', 'Tadesse', 'Girma', 'Rahel', 'Samrawit']
        last_names = ['Bekele', 'Tadesse', 'Haile', 'Gebre', 'Tesfaye', 'Demissie', 
                     'Negash', 'Mengistu', 'Assefa', 'Alemu']
        genders = ['M', 'F']
        
        for i in range(num_citizens):
            center = centers[i % len(centers)]
            gender = genders[i % len(genders)]
            first_name = random.choice(first_names)
            last_name = random.choice(last_names)
            
            # Random date of birth between 18 and 70 years ago
            days_old = random.randint(18*365, 70*365)
            dob = date.today() - timedelta(days=days_old)
            
            citizen = Citizen.objects.create(
                center=center,
                first_name=first_name,
                last_name=last_name,
                date_of_birth=dob,
                gender=gender,
                address=f"456 Side St, District {i%5 + 1}",
                phone=f"+251-9{random.randint(10000000, 99999999)}",
                email=f"{first_name.lower()}.{last_name.lower()}{i}@example.com",
                occupation=random.choice(['Teacher', 'Farmer', 'Driver', 'Engineer', 'Doctor', 'Student', 'Business Owner']),
                created_by=users[i % len(users)]
            )
            citizens.append(citizen)
            self.stdout.write(self.style.SUCCESS(f'Created citizen: {citizen.full_name}'))
        
        # Create ID card templates
        templates = []
        for i in range(num_templates):
            center = centers[i % len(centers)]
            is_default = (i == 0)  # Make the first template default
            
            template = IDCardTemplate.objects.create(
                name=f"Template {i+1}",
                center=center,
                is_default=is_default,
                front_layout={
                    'title': {'x': 10, 'y': 10, 'font_size': 18},
                    'photo': {'x': 20, 'y': 40, 'width': 100, 'height': 120},
                    'name': {'x': 130, 'y': 50, 'font_size': 14},
                    'id_number': {'x': 130, 'y': 70, 'font_size': 12},
                },
                back_layout={
                    'address': {'x': 10, 'y': 10, 'font_size': 12},
                    'issue_date': {'x': 10, 'y': 30, 'font_size': 12},
                    'expiry_date': {'x': 10, 'y': 50, 'font_size': 12},
                }
            )
            templates.append(template)
            self.stdout.write(self.style.SUCCESS(f'Created template: {template.name} for {center.name}'))
        
        # Create ID cards
        statuses = ['DRAFT', 'PENDING', 'APPROVED', 'PRINTED', 'ISSUED']
        for i in range(num_cards):
            citizen = citizens[i % len(citizens)]
            template = next((t for t in templates if t.center == citizen.center), templates[0])
            status = statuses[i % len(statuses)]
            
            # Issue date between 0 and 3 years ago
            days_ago = random.randint(0, 3*365)
            issue_date = date.today() - timedelta(days=days_ago)
            
            # Expiry date between 1 and 5 years from issue date
            expiry_days = random.randint(1*365, 5*365)
            expiry_date = issue_date + timedelta(days=expiry_days)
            
            card = IDCard.objects.create(
                citizen=citizen,
                template=template,
                issue_date=issue_date,
                expiry_date=expiry_date,
                status=status,
                created_by=users[i % len(users)],
                approved_by=users[(i+1) % len(users)] if status in ['APPROVED', 'PRINTED', 'ISSUED'] else None,
                card_data={
                    'custom_field1': f"Value {i+1}",
                    'custom_field2': f"Value {i+2}"
                }
            )
            self.stdout.write(self.style.SUCCESS(f'Created ID card: {card.card_number} for {citizen.full_name}'))
        
        self.stdout.write(self.style.SUCCESS('Sample data creation completed successfully!'))
