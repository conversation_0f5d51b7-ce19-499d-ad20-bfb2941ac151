import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Box,
  Container,
  Typography,
  TextField,
  Button,
  Paper,
  Grid,
  FormControlLabel,
  Checkbox,
  Alert,
  CircularProgress
} from '@mui/material';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import SaveIcon from '@mui/icons-material/Save';
import PersonAddIcon from '@mui/icons-material/PersonAdd';
import PageBanner from '../components/PageBanner';

const AddSpouse: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();

  // State for form data
  const [formData, setFormData] = useState({
    first_name: '',
    middle_name: '',
    last_name: '',
    phone: '',
    is_resident: false
  });

  // State for loading and error
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState(false);

  // Get the current tenant and authentication info from localStorage
  const tenantString = localStorage.getItem('tenant');
  const tenant = tenantString ? JSON.parse(tenantString) : null;
  const jwtToken = localStorage.getItem('jwt_access_token');
  const schemaName = localStorage.getItem('schema_name');

  // Handle form input changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, checked, type } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      // Use the tenant-specific API endpoint
      let schema = schemaName || tenant?.schema_name || '';
      const encodedSchema = encodeURIComponent(schema);
      const url = `http://localhost:8000/api/tenant/${encodedSchema}/citizens/${id}/spouse/`;

      // Use the JWT token from localStorage
      if (!jwtToken) {
        throw new Error('No authentication token found. Please log in again.');
      }

      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${jwtToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(formData),
        credentials: 'include'
      });

      if (!response.ok) {
        try {
          const errorData = await response.json();
          throw new Error(errorData.detail || 'Failed to add spouse');
        } catch (jsonError) {
          // If the response is not valid JSON, use the status text
          const errorMessage = `${response.status} ${response.statusText}`;
          throw new Error(`Failed to add spouse: ${errorMessage}`);
        }
      }

      setSuccess(true);
      setTimeout(() => {
        navigate(`/citizens/${id}`);
      }, 2000);
    } catch (error: any) {
      console.error('Error adding spouse:', error);

      // Provide more helpful error messages
      if (error.message.includes('No authentication token found')) {
        setError('Authentication error: Please log in again to continue.');
      } else if (error.message.includes('401')) {
        setError('Authentication error: Your session may have expired. Please log in again.');
      } else if (error.message.includes('500')) {
        setError('Server error: The server encountered an issue. Please try again later or contact support.');
      } else {
        setError(error.message || 'Failed to add spouse');
      }
    } finally {
      setLoading(false);
    }
  };

  // Handle cancel
  const handleCancel = () => {
    navigate(`/citizens/${id}`);
  };

  return (
    <Box sx={{ py: 4 }}>
      {/* Banner */}
      <PageBanner
        title="Add Spouse Information"
        subtitle="Add spouse details for the citizen"
        icon={<PersonAddIcon sx={{ fontSize: 50, color: 'white' }} />}
      />

      <Container maxWidth="md">
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            mb: 4,
            mt: 1
          }}
        >
          <Button
            startIcon={<ArrowBackIcon />}
            onClick={handleCancel}
            variant="outlined"
            sx={{ borderRadius: 2 }}
          >
            Back to Citizen
          </Button>
        </Box>

        {/* Error message */}
        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}

        {/* Success message */}
        {success && (
          <Alert severity="success" sx={{ mb: 3 }}>
            Spouse information added successfully! Redirecting...
          </Alert>
        )}

        <Paper
          component="form"
          onSubmit={handleSubmit}
          sx={{
            p: 4,
            borderRadius: 3,
            boxShadow: '0 8px 24px rgba(0,0,0,0.12)',
            mb: 4
          }}
        >
          <Typography variant="h5" sx={{ mb: 3, fontWeight: 600 }}>
            Spouse Information
          </Typography>

          <Grid container spacing={3}>
            <Grid item xs={12} md={4}>
              <TextField
                label="First Name"
                name="first_name"
                value={formData.first_name}
                onChange={handleChange}
                fullWidth
                required
                variant="outlined"
              />
            </Grid>

            <Grid item xs={12} md={4}>
              <TextField
                label="Middle Name"
                name="middle_name"
                value={formData.middle_name}
                onChange={handleChange}
                fullWidth
                variant="outlined"
              />
            </Grid>

            <Grid item xs={12} md={4}>
              <TextField
                label="Last Name"
                name="last_name"
                value={formData.last_name}
                onChange={handleChange}
                fullWidth
                required
                variant="outlined"
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                label="Phone Number"
                name="phone"
                value={formData.phone}
                onChange={handleChange}
                fullWidth
                variant="outlined"
              />
            </Grid>

            <Grid item xs={12}>
              <FormControlLabel
                control={
                  <Checkbox
                    name="is_resident"
                    checked={formData.is_resident}
                    onChange={handleChange}
                    color="primary"
                  />
                }
                label="Is a resident of this Kebele"
              />
            </Grid>
          </Grid>

          <Box sx={{ mt: 4, display: 'flex', justifyContent: 'flex-end', gap: 2 }}>
            <Button
              variant="outlined"
              onClick={handleCancel}
              sx={{ borderRadius: 2, px: 3, py: 1.2 }}
            >
              Cancel
            </Button>

            <Button
              type="submit"
              variant="contained"
              color="primary"
              startIcon={loading ? <CircularProgress size={20} color="inherit" /> : <SaveIcon />}
              disabled={loading}
              sx={{ borderRadius: 2, px: 3, py: 1.2 }}
            >
              {loading ? 'Saving...' : 'Save Spouse Information'}
            </Button>
          </Box>
        </Paper>
      </Container>
    </Box>
  );
};

export default AddSpouse;
