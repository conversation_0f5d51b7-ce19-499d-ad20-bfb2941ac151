/**
 * Authentication Interceptor
 *
 * This module intercepts all fetch requests and adds the Authorization header with the token.
 * It also adds the X-Schema-Name header with the schema name.
 *
 * This is a more reliable approach than relying on the fetchWithAuth function.
 */

import { getAccessTokenForSchema, getCurrentSchema } from '../services/tokenService';

// Store the original fetch function
const originalFetch = window.fetch;

// Override the fetch function with our interceptor
window.fetch = async function(input: RequestInfo | URL, init?: RequestInit) {
  // Get the URL as a string
  const url = typeof input === 'string' ? input : input.toString();

  // Extract schema from URL if it's a tenant-specific URL
  let schemaFromUrl: string | null = null;
  if (url.includes('/api/tenant/')) {
    const match = url.match(/\/api\/tenant\/([^\/]+)\//);
    if (match && match[1]) {
      try {
        schemaFromUrl = decodeURIComponent(match[1]);
        console.log('authInterceptor: Extracted schema from URL:', schemaFromUrl);
      } catch (error) {
        console.error('authInterceptor: Error decoding schema from URL:', error);
      }
    }
  }

  // Get the schema name from various sources with proper priority
  let schemaName = schemaFromUrl;

  // If no schema from URL, try to get it from the unified token service
  if (!schemaName) {
    schemaName = getCurrentSchema();
  }

  // If still no schema, try cookie
  if (!schemaName) {
    const schemaCookie = document.cookie.split('; ')
      .find(row => row.startsWith('schema_name='));
    if (schemaCookie) {
      schemaName = decodeURIComponent(schemaCookie.split('=')[1]);
    }
  }

  // If we have a schema name, log it
  if (schemaName) {
    console.log('authInterceptor: Using schema name:', schemaName);
  }

  // Get the JWT token for the current schema
  const token = schemaName ? getAccessTokenForSchema(schemaName) : null;

  // Create a new init object with the headers
  const newInit = { ...init };

  // Create headers if they don't exist
  if (!newInit.headers) {
    newInit.headers = {};
  }

  // Convert headers to a plain object if it's a Headers instance
  let headersObj: Record<string, string> = {};
  if (newInit.headers instanceof Headers) {
    newInit.headers.forEach((value, key) => {
      headersObj[key] = value;
    });
  } else if (Array.isArray(newInit.headers)) {
    // Handle array of header entries
    newInit.headers.forEach(([key, value]) => {
      headersObj[key] = value;
    });
  } else if (typeof newInit.headers === 'object') {
    // Already an object
    headersObj = newInit.headers as Record<string, string>;
  }

  // Set the headers to our object
  newInit.headers = headersObj;

  // Add the Authorization header if we have a token, but not for login or refresh token requests
  if (token && !url.includes('/login/') && !url.includes('/api/jwt/login/') && !url.includes('/api/jwt/refresh-token/')) {
    // Only add Authorization header if it doesn't already exist
    if (!('Authorization' in newInit.headers)) {
      // Use Bearer prefix for JWT tokens
      newInit.headers['Authorization'] = `Bearer ${token}`;
      console.log('authInterceptor: Added Authorization header with Bearer prefix');
    } else {
      console.log('authInterceptor: Authorization header already exists, not overriding:', newInit.headers['Authorization']);
    }

    // Remove x-auth-token and X-Auth-Token if they exist to avoid CORS issues
    if ('x-auth-token' in newInit.headers) {
      delete newInit.headers['x-auth-token'];
      console.log('authInterceptor: Removed x-auth-token header to avoid CORS issues');
    }
    if ('X-Auth-Token' in newInit.headers) {
      delete newInit.headers['X-Auth-Token'];
      console.log('authInterceptor: Removed X-Auth-Token header to avoid CORS issues');
    }
  } else if (url.includes('/login/') || url.includes('/api/jwt/login/') || url.includes('/api/jwt/refresh-token/')) {
    console.log('authInterceptor: Skipping Authorization header for login/refresh request');

    // For login/refresh requests, remove any existing Authorization header
    if ('Authorization' in newInit.headers) {
      delete newInit.headers['Authorization'];
      console.log('authInterceptor: Removed existing Authorization header for login/refresh request');
    }
  }

  // Add the X-Schema-Name header if we have a schema name, but not for login requests
  if (schemaName && schemaName !== 'public' && !url.includes('/login/') && !url.includes('/api/jwt/login/')) {
    // For refresh token requests, we DO want to include the X-Schema-Name header
    // This is important for the server to know which tenant's refresh token to use
    newInit.headers['X-Schema-Name'] = schemaName;
    console.log('authInterceptor: Added X-Schema-Name header with schema name:', schemaName);

    // Also set the schema_name cookie
    document.cookie = `schema_name=${encodeURIComponent(schemaName)}; path=/; SameSite=Lax`;
  } else if (url.includes('/login/') || url.includes('/api/jwt/login/')) {
    console.log('authInterceptor: Skipping X-Schema-Name header for login request to allow domain-based detection');

    // For login requests, remove any existing X-Schema-Name header
    if ('X-Schema-Name' in newInit.headers) {
      delete newInit.headers['X-Schema-Name'];
      console.log('authInterceptor: Removed existing X-Schema-Name header for login request');
    }

    // For login requests, also remove any schema_name cookie to allow domain-based detection
    document.cookie = 'schema_name=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
    console.log('authInterceptor: Cleared schema_name cookie for login request to allow domain-based detection');
  } else if (schemaName === 'public') {
    console.log('authInterceptor: Not adding X-Schema-Name header for public schema to allow domain-based detection');

    // For public schema, remove any existing X-Schema-Name header to allow domain-based detection
    if ('X-Schema-Name' in newInit.headers) {
      delete newInit.headers['X-Schema-Name'];
      console.log('authInterceptor: Removed existing X-Schema-Name header for public schema');
    }
  }

  // Always include credentials
  newInit.credentials = 'include';

  // No CSRF token handling needed - using JWT authentication only

  // Log the request details
  console.log(`authInterceptor: Intercepted request to ${url}`);
  console.log('authInterceptor: Headers:', newInit.headers);

  // Debug: Log the Authorization header specifically
  if (newInit.headers && 'Authorization' in newInit.headers) {
    console.log('authInterceptor: Authorization header:', newInit.headers['Authorization']);
  } else {
    console.log('authInterceptor: No Authorization header found');
  }

  // Call the original fetch function with the new init object
  return originalFetch(input, newInit);
};

// Export a function to initialize the interceptor
export const initAuthInterceptor = async () => {
  console.log('Auth interceptor initialized');
  // No CSRF token handling needed - using JWT authentication only
};

