import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import {
  refreshJWTTokens,
  getAccessTokenForSchema,
  storeTokensForSchema,
  clearAllTokens
} from '../services/tokenService';

interface User {
  id: number;
  email: string;
  first_name: string;
  last_name: string;
  role: string;
}

interface Tenant {
  id?: number;
  schema_name: string;
  name: string;
  type: string;
  parent_id?: number | null;
  logo_url?: string;
}

interface AuthContextType {
  accessToken: string | null;
  user: User | null;
  tenant: Tenant | null;
  schemaName: string | null;
  isAuthenticated: boolean;
  loginWithJWT: (accessToken: string, refreshToken: string, user: User, tenant: Tenant, schemaName: string) => void;
  logout: () => void;
  updateAuthState: () => void;
  refreshToken: () => Promise<boolean>;
  getAuthHeaders: () => Record<string, string>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [accessToken, setAccessToken] = useState<string | null>(localStorage.getItem('jwt_access_token'));
  const [user, setUser] = useState<User | null>(null);
  const [tenant, setTenant] = useState<Tenant | null>(null);
  const [schemaName, setSchemaName] = useState<string | null>(localStorage.getItem('schema_name'));
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(!!accessToken && !!schemaName);

  // Load user and tenant from localStorage on component mount
  useEffect(() => {
    updateAuthState();

    // Set up token refresh interval
    const refreshInterval = setInterval(async () => {
      if (schemaName && isAuthenticated) {
        await refreshToken();
      }
    }, 10 * 60 * 1000); // Refresh every 10 minutes

    return () => clearInterval(refreshInterval);
  }, [schemaName, isAuthenticated]);

  // Function to refresh the JWT token
  const refreshToken = async (): Promise<boolean> => {
    try {
      if (!schemaName) {
        console.error('No schema name available for token refresh');
        return false;
      }

      console.log(`Attempting to refresh token for schema: ${schemaName}`);
      const result = await refreshJWTTokens(schemaName);

      if (result && result.access_token) {
        console.log('Token refreshed successfully');
        setAccessToken(result.access_token);
        localStorage.setItem('jwt_access_token', result.access_token);
        return true;
      } else {
        console.error('Failed to refresh token');
        return false;
      }
    } catch (error) {
      console.error('Error refreshing token:', error);
      return false;
    }
  };

  // Function to get authentication headers
  const getAuthHeaders = (): Record<string, string> => {
    if (!accessToken) return {};
    return { 'Authorization': `Bearer ${accessToken}` };
  };

  const updateAuthState = () => {
    const storedAccessToken = localStorage.getItem('jwt_access_token');
    const storedSchemaName = localStorage.getItem('schema_name');
    const storedUser = localStorage.getItem('user');
    const storedTenant = localStorage.getItem('tenant');

    console.log('AuthProvider - Updating auth state');
    console.log('JWT Access Token:', storedAccessToken ? 'exists' : 'not found');
    console.log('Schema:', storedSchemaName);
    console.log('User exists:', !!storedUser);
    console.log('Tenant exists:', !!storedTenant);

    setAccessToken(storedAccessToken);
    setSchemaName(storedSchemaName);

    // Parse user data if available
    if (storedUser) {
      try {
        const parsedUser = JSON.parse(storedUser);
        setUser(parsedUser);
      } catch (error) {
        console.error('Error parsing user data:', error);

        // Create a default user if parsing fails
        if (storedAccessToken && storedSchemaName) {
          const defaultUser = {
            id: 1,
            email: '<EMAIL>',
            first_name: 'Test',
            last_name: 'User',
            role: 'ADMIN'
          };
          localStorage.setItem('user', JSON.stringify(defaultUser));
          setUser(defaultUser);
        } else {
          setUser(null);
        }
      }
    } else if (storedAccessToken && storedSchemaName) {
      // Create a default user if token and schema exist but no user
      const defaultUser = {
        id: 1,
        email: '<EMAIL>',
        first_name: 'Test',
        last_name: 'User',
        role: 'ADMIN'
      };
      localStorage.setItem('user', JSON.stringify(defaultUser));
      setUser(defaultUser);
    } else {
      setUser(null);
    }

    // Parse tenant data if available
    if (storedTenant) {
      try {
        const parsedTenant = JSON.parse(storedTenant);
        setTenant(parsedTenant);
      } catch (error) {
        console.error('Error parsing tenant data:', error);

        // Create a default tenant if parsing fails
        if (storedAccessToken && storedSchemaName) {
          const defaultTenant = {
            id: 1,
            name: storedSchemaName.charAt(0).toUpperCase() + storedSchemaName.slice(1),
            schema_name: storedSchemaName,
            type: storedSchemaName.includes('kebele') ? 'KEBELE' : 'SUBCITY',
            parent_id: null
          };
          localStorage.setItem('tenant', JSON.stringify(defaultTenant));
          setTenant(defaultTenant);
        } else {
          setTenant(null);
        }
      }
    } else if (storedAccessToken && storedSchemaName) {
      // Create a default tenant if token and schema exist but no tenant
      const defaultTenant = {
        id: 1,
        name: storedSchemaName.charAt(0).toUpperCase() + storedSchemaName.slice(1),
        schema_name: storedSchemaName,
        type: storedSchemaName.includes('kebele') ? 'KEBELE' : 'SUBCITY',
        parent_id: null
      };
      localStorage.setItem('tenant', JSON.stringify(defaultTenant));
      setTenant(defaultTenant);
    } else {
      setTenant(null);
    }

    // Update authentication state
    setIsAuthenticated(!!storedAccessToken && !!storedSchemaName);
  };

  const loginWithJWT = (
    newAccessToken: string,
    newRefreshToken: string,
    newUser: User,
    newTenant: Tenant,
    newSchemaName: string
  ) => {
    // Store in localStorage
    localStorage.setItem('jwt_access_token', newAccessToken);
    localStorage.setItem('user', JSON.stringify(newUser));
    localStorage.setItem('tenant', JSON.stringify(newTenant));
    localStorage.setItem('schema_name', newSchemaName);

    // Store tokens for schema
    storeTokensForSchema(newSchemaName, newAccessToken, newRefreshToken);

    // Update state
    setAccessToken(newAccessToken);
    setUser(newUser);
    setTenant(newTenant);
    setSchemaName(newSchemaName);
    setIsAuthenticated(true);

    console.log('JWT Login successful');
  };

  const logout = () => {
    // Clear all tokens
    clearAllTokens();

    // Clear localStorage
    localStorage.removeItem('jwt_access_token');
    localStorage.removeItem('user');
    localStorage.removeItem('tenant');
    localStorage.removeItem('schema_name');
    localStorage.removeItem('logo_url');

    // Update state
    setAccessToken(null);
    setUser(null);
    setTenant(null);
    setSchemaName(null);
    setIsAuthenticated(false);

    // Clear JWT cookies
    document.cookie = 'jwt_refresh_token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
    document.cookie = 'refresh_token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';

    console.log('Logout successful');
  };

  return (
    <AuthContext.Provider
      value={{
        accessToken,
        user,
        tenant,
        schemaName,
        isAuthenticated,
        loginWithJWT,
        logout,
        updateAuthState,
        refreshToken,
        getAuthHeaders
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
