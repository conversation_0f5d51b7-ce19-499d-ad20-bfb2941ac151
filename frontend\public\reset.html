<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reset Application State</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        button {
            background-color: #4CAF50;
            border: none;
            color: white;
            padding: 10px 20px;
            text-align: center;
            text-decoration: none;
            display: inline-block;
            font-size: 16px;
            margin: 10px 2px;
            cursor: pointer;
            border-radius: 4px;
        }
        .warning {
            background-color: #f44336;
        }
        .info {
            background-color: #2196F3;
        }
        pre {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
        .result {
            margin-top: 20px;
            padding: 10px;
            background-color: #e8f5e9;
            border-radius: 4px;
            display: none;
        }
    </style>
</head>
<body>
    <h1>Reset Application State</h1>
    <p>This page will help you reset the application state to fix the infinite refresh loop issue.</p>
    
    <h2>Current Storage Data</h2>
    <p>Here's what's currently stored in your browser:</p>
    
    <h3>Local Storage</h3>
    <pre id="localStorageData">Loading...</pre>
    
    <h3>Session Storage</h3>
    <pre id="sessionStorageData">Loading...</pre>
    
    <h2>Reset Options</h2>
    <p>Choose one of the following options:</p>
    
    <button id="clearAll" class="warning">Clear All Storage Data</button>
    <button id="clearSession" class="info">Clear Only Session Storage</button>
    <button id="clearTokens" class="info">Clear Only Token Data</button>
    
    <div id="result" class="result">
        <h3>Result</h3>
        <p id="resultMessage"></p>
    </div>
    
    <h2>After Resetting</h2>
    <p>After resetting, please:</p>
    <ol>
        <li>Close all browser tabs with the application</li>
        <li>Open the application in a new tab</li>
        <li>Try logging in again</li>
    </ol>
    
    <script>
        // Display current storage data
        function displayStorageData() {
            try {
                const localStorageData = document.getElementById('localStorageData');
                const sessionStorageData = document.getElementById('sessionStorageData');
                
                // Format localStorage data
                let localData = {};
                for (let i = 0; i < localStorage.length; i++) {
                    const key = localStorage.key(i);
                    try {
                        // Try to parse as JSON
                        const value = localStorage.getItem(key);
                        try {
                            localData[key] = JSON.parse(value);
                        } catch {
                            // If not JSON, store as string
                            localData[key] = value;
                        }
                    } catch (e) {
                        localData[key] = "Error reading value";
                    }
                }
                
                // Format sessionStorage data
                let sessionData = {};
                for (let i = 0; i < sessionStorage.length; i++) {
                    const key = sessionStorage.key(i);
                    try {
                        // Try to parse as JSON
                        const value = sessionStorage.getItem(key);
                        try {
                            sessionData[key] = JSON.parse(value);
                        } catch {
                            // If not JSON, store as string
                            sessionData[key] = value;
                        }
                    } catch (e) {
                        sessionData[key] = "Error reading value";
                    }
                }
                
                // Display the data
                localStorageData.textContent = JSON.stringify(localData, null, 2);
                sessionStorageData.textContent = JSON.stringify(sessionData, null, 2);
            } catch (error) {
                console.error('Error displaying storage data:', error);
            }
        }
        
        // Clear all storage data
        document.getElementById('clearAll').addEventListener('click', function() {
            try {
                localStorage.clear();
                sessionStorage.clear();
                
                // Show result
                const result = document.getElementById('result');
                const resultMessage = document.getElementById('resultMessage');
                result.style.display = 'block';
                resultMessage.textContent = 'All storage data has been cleared successfully. Please close all browser tabs with the application and open it in a new tab.';
                
                // Update displayed data
                displayStorageData();
            } catch (error) {
                console.error('Error clearing all storage data:', error);
                alert('Error clearing storage data: ' + error.message);
            }
        });
        
        // Clear only session storage
        document.getElementById('clearSession').addEventListener('click', function() {
            try {
                sessionStorage.clear();
                
                // Show result
                const result = document.getElementById('result');
                const resultMessage = document.getElementById('resultMessage');
                result.style.display = 'block';
                resultMessage.textContent = 'Session storage data has been cleared successfully. Please close all browser tabs with the application and open it in a new tab.';
                
                // Update displayed data
                displayStorageData();
            } catch (error) {
                console.error('Error clearing session storage data:', error);
                alert('Error clearing session storage data: ' + error.message);
            }
        });
        
        // Clear only token data
        document.getElementById('clearTokens').addEventListener('click', function() {
            try {
                // Clear token-related data from localStorage
                localStorage.removeItem('token');
                localStorage.removeItem('tokenStore');
                localStorage.removeItem('schema_name');
                localStorage.removeItem('tenant');
                
                // Clear token-related data from sessionStorage
                sessionStorage.removeItem('token_expired');
                sessionStorage.removeItem('permissions_initialized');
                sessionStorage.removeItem('tenant_indicator_initialized');
                sessionStorage.removeItem('tenant_layout_initialized');
                sessionStorage.removeItem('last_theme_path');
                sessionStorage.removeItem('flags_reset');
                
                // Show result
                const result = document.getElementById('result');
                const resultMessage = document.getElementById('resultMessage');
                result.style.display = 'block';
                resultMessage.textContent = 'Token data has been cleared successfully. Please close all browser tabs with the application and open it in a new tab.';
                
                // Update displayed data
                displayStorageData();
            } catch (error) {
                console.error('Error clearing token data:', error);
                alert('Error clearing token data: ' + error.message);
            }
        });
        
        // Initialize
        displayStorageData();
    </script>
</body>
</html>
