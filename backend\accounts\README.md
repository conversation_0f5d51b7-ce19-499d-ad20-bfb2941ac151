# JWT Token Rotation Implementation

This document describes the JWT token rotation implementation in the application.

## Overview

Token rotation is a security mechanism that ensures refresh tokens can only be used once. After a refresh token is used to obtain a new access token, it is invalidated (blacklisted) and a new refresh token is issued. This prevents refresh token reuse and reduces the risk of token theft.

## Implementation Details

### Backend

1. **BlacklistedToken Model**
   - Stores used refresh tokens to prevent reuse
   - Includes expiration time to allow cleanup of old tokens
   - Linked to the user who owned the token

2. **Token Refresh Process**
   - When a refresh token is used, it is validated
   - If valid, new access and refresh tokens are generated
   - The used refresh token is added to the blacklist
   - The new tokens are returned to the client

3. **Token Validation**
   - All tokens are checked against the blacklist during validation
   - If a token is found in the blacklist, it is considered invalid
   - This prevents replay attacks where an attacker tries to use a stolen token

4. **Cleanup Process**
   - A management command (`cleanup_blacklisted_tokens`) removes expired tokens from the blacklist
   - This should be run periodically (e.g., daily) to prevent the blacklist from growing too large

### Frontend

1. **Token Storage**
   - Tokens are stored in memory and localStorage
   - When new tokens are received during refresh, they replace the old ones

2. **Token Refresh Handling**
   - When a refresh token is used, the frontend expects to receive new tokens
   - If token refresh fails with a 401 error, the tokens for that schema are cleared
   - This forces the user to log in again if their refresh token has been compromised

3. **API Request Handling**
   - If an API request fails with a 401 error, a token refresh is attempted
   - If the refresh succeeds, the request is retried with the new token
   - If the refresh fails, the tokens are cleared and the user is redirected to login

## Security Benefits

1. **Prevents Token Reuse**
   - If a refresh token is stolen and used by an attacker, it becomes invalid after the first use
   - This limits the window of opportunity for an attacker

2. **Reduces Impact of Token Theft**
   - Even if a refresh token is stolen, it can only be used once
   - The legitimate user will receive a new refresh token during their next refresh
   - If the attacker uses the token first, the legitimate user will be forced to log in again

3. **Enables Token Revocation**
   - Administrators can add tokens to the blacklist to immediately revoke access
   - This is useful for handling security incidents

## Usage

### Running Token Cleanup

To clean up expired blacklisted tokens, run:

```bash
python manage.py cleanup_blacklisted_tokens
```

This should be scheduled to run regularly (e.g., using a cron job) to prevent the blacklist from growing too large.

### Blacklisting a Token Manually

To blacklist a token manually (e.g., for security reasons), you can use the Django admin interface or the Django shell:

```python
from accounts.models import BlacklistedToken, User
from django.utils import timezone
import datetime

# Get the user
user = User.objects.get(email='<EMAIL>')

# Create a blacklist entry
BlacklistedToken.objects.create(
    token='token_to_blacklist',
    user=user,
    expires_at=timezone.now() + datetime.timedelta(days=7),
    token_type='refresh'
)
```
