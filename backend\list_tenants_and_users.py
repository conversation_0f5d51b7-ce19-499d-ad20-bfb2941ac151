import os
import sys
import django

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'neocamelot.settings')
django.setup()

from django.contrib.auth import get_user_model
from django_tenants.utils import tenant_context
from centers.models import Client

User = get_user_model()

def main():
    # Get all tenants
    tenants = Client.objects.all().order_by('schema_type', 'name')
    
    print(f"Found {tenants.count()} tenants\n")
    
    # Print tenant information
    print("TENANTS:")
    print("{:<20} {:<30} {:<15} {:<20} {:<10}".format(
        "Schema Name", "Name", "Type", "Parent", "Status"
    ))
    print("-" * 100)
    
    for tenant in tenants:
        print("{:<20} {:<30} {:<15} {:<20} {:<10}".format(
            tenant.schema_name,
            tenant.name,
            tenant.schema_type,
            tenant.parent.name if tenant.parent else "None",
            "Active" if tenant.is_active else "Inactive"
        ))
    
    # For each tenant, get users
    print("\nUSERS BY TENANT:")
    
    for tenant in tenants:
        print(f"\nTenant: {tenant.name} ({tenant.schema_name})")
        
        try:
            with tenant_context(tenant):
                users = User.objects.all().order_by('email')
                
                if users.exists():
                    print("{:<40} {:<30} {:<20} {:<10} {:<10} {:<10}".format(
                        "Email", "Name", "Role", "Active", "Superuser", "Staff"
                    ))
                    print("-" * 120)
                    
                    for user in users:
                        print("{:<40} {:<30} {:<20} {:<10} {:<10} {:<10}".format(
                            user.email,
                            user.get_full_name() or "N/A",
                            user.role,
                            "Yes" if user.is_active else "No",
                            "Yes" if user.is_superuser else "No",
                            "Yes" if user.is_staff else "No"
                        ))
                else:
                    print("  No users found in this tenant")
        except Exception as e:
            print(f"  Error accessing tenant {tenant.schema_name}: {str(e)}")

if __name__ == "__main__":
    main()
