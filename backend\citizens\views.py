import logging
from rest_framework import viewsets, permissions, filters, status
from rest_framework.decorators import action, authentication_classes
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend

# Configure logging
logger = logging.getLogger(__name__)
from .models import Citizen
from .serializers import CitizenSerializer, CitizenListSerializer, CitizenDetailSerializer
from .authentication import CitizenJ<PERSON><PERSON>uthentication
from accounts.role_permissions import (
    IsCenterStaff, CanViewCitizens, CanRegisterCitizens
)

class CitizenViewSet(viewsets.ModelViewSet):
    """ViewSet for viewing and editing Citizen instances."""
    queryset = Citizen.tenant_objects.all()
    serializer_class = CitizenSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['center', 'gender', 'is_active', 'nationality']
    search_fields = ['first_name', 'last_name', 'registration_number', 'id_number', 'email', 'phone']
    ordering_fields = ['first_name', 'last_name', 'date_of_birth', 'created_at']
    authentication_classes = [CitizenJWTAuthentication]

    def get_permissions(self):
        """Set custom permissions for different actions based on user roles."""
        # Log request details for debugging
        logger.info(f"CitizenViewSet permission check for action: {self.action}")
        logger.info(f"Request method: {self.request.method}")
        logger.info(f"Request path: {self.request.path}")
        logger.info(f"Query params: {dict(self.request.query_params)}")
        logger.info(f"Headers: {dict(self.request.headers)}")

        # Check if the request has a 'detail' query parameter set to 'true'
        # or if 'detail=true' is in the URL path
        # If so, allow unauthenticated access for all actions
        has_detail_param = (self.request.query_params.get('detail') == 'true' or
                           'detail=true' in self.request.get_full_path())

        if has_detail_param:
            # Allow unauthenticated access for all actions when detail=true is present
            logger.info(f"Allowing unauthenticated access due to detail=true parameter for action: {self.action}")
            return [permissions.AllowAny()]

        # Regular permission checks for other cases
        if self.action in ['create', 'update', 'partial_update', 'destroy']:
            # Only CENTER_STAFF can create, update, or delete citizens
            logger.info("Requiring CanRegisterCitizens permission")
            return [CanRegisterCitizens()]
        elif self.action in ['list', 'retrieve']:
            # CENTER_STAFF, KEBELE_LEADER, SUBCITY_ADMIN, CITY_ADMIN can view citizens
            logger.info("Requiring CanViewCitizens permission")
            return [CanViewCitizens()]
        elif self.action == 'generate_id':
            # Only CENTER_STAFF can generate ID numbers
            logger.info("Requiring IsCenterStaff permission")
            return [IsCenterStaff()]
        # Default to authenticated users for other actions
        logger.info("Requiring IsAuthenticated permission (default)")
        return [permissions.IsAuthenticated()]

    def get_serializer_class(self):
        """Return appropriate serializer class based on action."""
        if self.action == 'list':
            return CitizenListSerializer
        elif self.action == 'retrieve':
            return CitizenDetailSerializer
        return CitizenSerializer

    def dispatch(self, request, *args, **kwargs):
        """Override dispatch to add CORS headers for unauthenticated access."""
        # Handle OPTIONS requests explicitly
        if request.method == 'OPTIONS':
            logger.info("Handling OPTIONS request")
            response = Response()
            origin = request.headers.get('Origin', '*')
            response['Access-Control-Allow-Origin'] = origin
            response['Access-Control-Allow-Methods'] = 'GET, POST, PUT, PATCH, DELETE, OPTIONS'
            response['Access-Control-Allow-Headers'] = 'Content-Type, Authorization, X-Requested-With, X-Schema-Name'
            response['Access-Control-Allow-Credentials'] = 'true'
            response['Access-Control-Max-Age'] = '86400'  # 24 hours
            return response

        # For other requests, proceed with normal dispatch
        response = super().dispatch(request, *args, **kwargs)

        # Add CORS headers for unauthenticated access
        if request.query_params.get('detail') == 'true' or 'detail=true' in request.get_full_path():
            logger.info("Adding CORS headers for unauthenticated access")
            origin = request.headers.get('Origin', '*')
            response['Access-Control-Allow-Origin'] = origin
            response['Access-Control-Allow-Methods'] = 'GET, OPTIONS'
            response['Access-Control-Allow-Headers'] = 'Content-Type, Authorization, X-Requested-With, X-Schema-Name'
            response['Access-Control-Allow-Credentials'] = 'true'
            response['Access-Control-Max-Age'] = '86400'  # 24 hours

            # Log the response headers for debugging
            logger.info(f"Response headers: {dict(response.headers)}")

        return response

    def get_queryset(self):
        """Filter citizens based on user role and center."""
        user = self.request.user

        # Check for schema query parameter
        schema = self.request.query_params.get('schema')

        # Also check for schema in headers
        schema_header = self.request.headers.get('X-Schema-Name')
        if schema_header and not schema:
            schema = schema_header
            print(f"Using schema from X-Schema-Name header: {schema}")

        # Also check for schema in cookies
        schema_cookie = self.request.COOKIES.get('schema_name')
        if schema_cookie and not schema:
            schema = schema_cookie
            print(f"Using schema from schema_name cookie: {schema}")

        # Check if schema is in the URL path
        if not schema and 'tenant' in self.request.path:
            # Try to extract schema from URL path like /api/tenant/kebele16/citizens/
            path_parts = self.request.path.split('/')
            if len(path_parts) > 3 and path_parts[2] == 'tenant' and len(path_parts) > 3:
                schema = path_parts[3]
                print(f"Extracted schema from URL path: {schema}")

                # Convert underscores to spaces if needed
                if '_' in schema:
                    schema_with_spaces = schema.replace('_', ' ')
                    print(f"Converted schema with underscores to spaces: {schema_with_spaces}")

                    # Check if this schema exists
                    from centers.models import Client
                    try:
                        Client.objects.get(schema_name=schema_with_spaces)
                        schema = schema_with_spaces
                        print(f"Using schema with spaces: {schema}")
                    except Client.DoesNotExist:
                        print(f"Schema with spaces not found: {schema_with_spaces}, using original: {schema}")
                    except Exception as e:
                        print(f"Error checking schema with spaces: {str(e)}")

        # Initialize tenant variable to None to avoid UnboundLocalError
        tenant = None

        if schema:
            # If schema is provided, try to set the tenant context
            from centers.models import Client
            from django.db import connection
            from django_tenants.utils import get_public_schema_name

            if schema != get_public_schema_name():
                try:
                    # Get the tenant by schema name
                    tenant = Client.objects.get(schema_name=schema)

                    # Set the tenant for this request
                    connection.set_tenant(tenant)
                    self.request.tenant = tenant

                    # Set tenant attributes based on type
                    if tenant.schema_type == 'CITY':
                        self.request.city = tenant
                        self.request.subcity = None
                        self.request.center = None
                    elif tenant.schema_type == 'SUBCITY':
                        self.request.subcity = tenant
                        self.request.city = tenant.parent if tenant.parent and tenant.parent.schema_type == 'CITY' else None
                        self.request.center = None
                    elif tenant.schema_type == 'KEBELE':
                        self.request.center = tenant
                        self.request.subcity = tenant.parent if tenant.parent and tenant.parent.schema_type == 'SUBCITY' else None
                        self.request.city = self.request.subcity.parent if self.request.subcity and self.request.subcity.parent and self.request.subcity.parent.schema_type == 'CITY' else None

                    print(f"Set tenant to {tenant.name} (schema: {tenant.schema_name})")
                except Client.DoesNotExist:
                    print(f"Tenant with schema {schema} does not exist")

                    # Try with schema variations
                    try:
                        # Try with underscores instead of spaces
                        alt_schema = schema.replace(' ', '_')
                        tenant = Client.objects.get(schema_name=alt_schema)

                        # Set the tenant for this request
                        connection.set_tenant(tenant)
                        self.request.tenant = tenant

                        # Set tenant attributes based on type
                        if tenant.schema_type == 'CITY':
                            self.request.city = tenant
                            self.request.subcity = None
                            self.request.center = None
                        elif tenant.schema_type == 'SUBCITY':
                            self.request.subcity = tenant
                            self.request.city = tenant.parent if tenant.parent and tenant.parent.schema_type == 'CITY' else None
                            self.request.center = None
                        elif tenant.schema_type == 'KEBELE':
                            self.request.center = tenant
                            self.request.subcity = tenant.parent if tenant.parent and tenant.parent.schema_type == 'SUBCITY' else None
                            self.request.city = self.request.subcity.parent if self.request.subcity and self.request.subcity.parent and self.request.subcity.parent.schema_type == 'CITY' else None

                        print(f"Found tenant with schema using underscore: {alt_schema}")
                    except Client.DoesNotExist:
                        # Try with spaces instead of underscores
                        alt_schema = schema.replace('_', ' ')
                        try:
                            tenant = Client.objects.get(schema_name=alt_schema)

                            # Set the tenant for this request
                            connection.set_tenant(tenant)
                            self.request.tenant = tenant

                            # Set tenant attributes based on type
                            if tenant.schema_type == 'CITY':
                                self.request.city = tenant
                                self.request.subcity = None
                                self.request.center = None
                            elif tenant.schema_type == 'SUBCITY':
                                self.request.subcity = tenant
                                self.request.city = tenant.parent if tenant.parent and tenant.parent.schema_type == 'CITY' else None
                                self.request.center = None
                            elif tenant.schema_type == 'KEBELE':
                                self.request.center = tenant
                                self.request.subcity = tenant.parent if tenant.parent and tenant.parent.schema_type == 'SUBCITY' else None
                                self.request.city = self.request.subcity.parent if self.request.subcity and self.request.subcity.parent and self.request.subcity.parent.schema_type == 'CITY' else None

                            print(f"Found tenant with schema using spaces: {alt_schema}")
                        except Client.DoesNotExist:
                            print(f"Tenant with schema {schema} or variations not found")
                    except Exception as e:
                        print(f"Error setting tenant with schema variation: {str(e)}")
                except Exception as e:
                    print(f"Error setting tenant: {str(e)}")

        # Check if this is a schema generation request from Swagger
        if getattr(self, 'swagger_fake_view', False):
            # Return a simple queryset for Swagger schema generation
            return Citizen.objects.none()

        if hasattr(user, 'is_super_admin') and user.is_super_admin:
            # Super admins can see all citizens
            return Citizen.objects.all()
        # For other users, the tenant_objects manager will automatically filter by center
        return Citizen.tenant_objects.all()

    def perform_create(self, serializer):
        """Set created_by to current user and center if provided."""
        data = serializer.validated_data

        # Check if this is an unauthenticated request with detail=true
        has_detail_param = (self.request.query_params.get('detail') == 'true' or
                           'detail=true' in self.request.get_full_path())

        # Get the user if authenticated
        user = self.request.user if self.request.user.is_authenticated else None

        # Log the request details
        logger.info(f"perform_create called with authenticated user: {user is not None}")
        logger.info(f"Request data: {self.request.data}")
        logger.info(f"Validated data: {data}")

        # If no center is specified and user has a center, use user's center
        if not data.get('center') and user and hasattr(user, 'center') and user.center:
            data['center'] = user.center
            logger.info(f"Using user's center: {user.center}")

        # Handle ketena_name if provided in the request
        ketena_name = self.request.data.get('ketena_name')
        if ketena_name and not data.get('ketena'):
            # Try to find an existing ketena with this name for the kebele
            from centers.models import Ketena

            # Get kebele_id from data or user's center
            kebele_id = data.get('kebele')
            if not kebele_id and user and hasattr(user, 'center') and user.center:
                kebele_id = user.center.id

            logger.info(f"Using kebele_id for ketena: {kebele_id}")

            if kebele_id:
                # Try to find the ketena
                try:
                    ketena = Ketena.objects.filter(name=ketena_name, kebele_id=kebele_id).first()

                    if not ketena:
                        # Create a new ketena
                        code = ketena_name.replace('Ketena ', '').zfill(2)
                        ketena = Ketena.objects.create(
                            name=ketena_name,
                            code=code,
                            kebele_id=kebele_id
                        )
                        logger.info(f"Created new ketena: {ketena.name} with code {ketena.code}")

                    # Set the ketena in the validated data
                    data['ketena'] = ketena
                    logger.info(f"Using ketena: {ketena.name}")
                except Exception as e:
                    logger.error(f"Error handling ketena_name: {str(e)}")

        # Save with created_by set to the authenticated user if available
        if user and user.is_authenticated:
            logger.info(f"Saving citizen with created_by: {user}")
            serializer.save(created_by=user)
        else:
            logger.info("Saving citizen without created_by (unauthenticated request)")
            serializer.save()

    @action(detail=True, methods=['get'])
    def id_cards(self, request, pk=None):
        """Return all ID cards for a specific citizen."""
        citizen = self.get_object()
        from idcards.serializers import IDCardSerializer
        serializer = IDCardSerializer(citizen.id_cards.all(), many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def generate_id(self, request, pk=None):
        """Generate ID number for a citizen."""
        citizen = self.get_object()

        # Check if ID number already exists
        if citizen.id_number:
            return Response({
                'message': 'ID number already exists',
                'id_number': citizen.id_number
            })

        # Generate a unique ID number using the format: first letter of city + first 2 letters of subcity + kebele number + 6 digit sequence
        # Get the city, subcity, and kebele information
        city_prefix = ''
        subcity_prefix = ''
        kebele_num = '01'  # Default kebele number

        # Get city prefix from subcity's parent
        if citizen.subcity and hasattr(citizen.subcity, 'city') and citizen.subcity.city:
            city = citizen.subcity.city
            if hasattr(city, 'city_name') and city.city_name:
                city_prefix = city.city_name[0].upper()

        # Get subcity prefix
        subcity_prefix = 'XX'  # Default value

        # Try to get the subcity name from the current schema
        from django.db import connection
        from centers.models import Client

        # Get the current schema name
        current_schema = connection.schema_name
        print(f"\n\nCurrent schema: {current_schema}\n\n")

        # Try to get the tenant from the current schema
        try:
            # Get the tenant for the current schema
            tenant = Client.objects.get(schema_name=current_schema)
            if tenant and hasattr(tenant, 'parent') and tenant.parent:
                subcity_name = tenant.parent.name
                print(f"\n\nTenant parent name (subcity): {subcity_name}\n\n")
                # Some subcity names might be like "Azezo (Default City)" - extract just the first part
                subcity_name = subcity_name.split('(')[0].strip()
                # Get the first two letters
                subcity_prefix = subcity_name[:2].upper()
        except Exception as e:
            print(f"\n\nError getting tenant: {e}\n\n")
            # Fallback to the subcity from the citizen record
            if citizen.subcity and hasattr(citizen.subcity, 'name') and citizen.subcity.name:
                # Some subcity names might be like "Azezo (Default City)" - extract just the first part
                subcity_name = citizen.subcity.name.split('(')[0].strip()
                print(f"\n\nSubcity name from citizen record: {subcity_name}\n\n")
                # Get the first two letters
                subcity_prefix = subcity_name[:2].upper()

        # Get kebele number from current schema
        from django.db import connection
        current_schema = connection.schema_name
        print(f"\n\nCurrent schema for kebele number: {current_schema}\n\n")

        # Try to extract kebele number from schema name
        import re
        schema_digits = re.findall(r'\d+', current_schema)
        if schema_digits:
            kebele_num = schema_digits[-1].zfill(2)[:2]  # Ensure it's 2 digits with leading zeros
            print(f"\n\nExtracted kebele number from schema: {kebele_num}\n\n")
        # Fallback to kebele name if schema extraction fails
        elif citizen.kebele and hasattr(citizen.kebele, 'name') and citizen.kebele.name:
            digits = re.findall(r'\d+', citizen.kebele.name)
            if digits:
                kebele_num = digits[-1].zfill(2)[:2]  # Ensure it's 2 digits with leading zeros
                print(f"\n\nExtracted kebele number from kebele name: {kebele_num}\n\n")

        # If we couldn't get the city prefix, use a default
        if not city_prefix:
            city_prefix = 'G'

        # If we couldn't get the subcity prefix, use a default
        if not subcity_prefix:
            subcity_prefix = 'XX'

        # Generate a sequential 6-digit number
        import time
        timestamp = int(time.time() * 1000) % 1000000  # Last 6 digits of current timestamp in milliseconds
        random_suffix = f"{timestamp:06d}"  # Ensure it's 6 digits with leading zeros

        # Format: First letter of city + First 2 letters of subcity + Kebele number + 6 digit sequence
        citizen.id_number = f"{city_prefix}{subcity_prefix}{kebele_num}{random_suffix}"

        # Save the citizen object
        Citizen.objects.filter(pk=citizen.pk).update(id_number=citizen.id_number)

        return Response({
            'message': 'ID number generated successfully',
            'id_number': citizen.id_number
        })

    @action(detail=False, methods=['get'])
    def statistics(self, request):
        """Return statistics for citizens based on user's center."""
        user = request.user
        queryset = self.get_queryset()

        stats = {
            'total': queryset.count(),
            'active': queryset.filter(is_active=True).count(),
            'inactive': queryset.filter(is_active=False).count(),
            'male': queryset.filter(gender='M').count(),
            'female': queryset.filter(gender='F').count(),
            'other': queryset.filter(gender='O').count(),
        }

        return Response(stats)
