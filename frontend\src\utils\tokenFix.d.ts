/**
 * Token Fix Utility Type Definitions
 *
 * This file provides TypeScript type definitions for the tokenFix.js module.
 */

/**
 * Result of a token storage fix operation
 */
export interface TokenFixResult {
  success: boolean;
  error?: string;
  schema?: string;
  accessTokenLength?: number;
  refreshTokenLength?: number;
  tokenSources?: {
    accessTokenFromSchemaSpecific: boolean;
    accessTokenFromGeneral: boolean;
    refreshTokenFromSchemaSpecific: boolean;
    refreshTokenFromGeneral: boolean;
    refreshTokenFromCookie: boolean;
  };
}

/**
 * Result of a cookie preservation operation
 */
export interface CookiePreservationResult {
  success: boolean;
  error?: string;
  jwtCookieSet?: boolean;
  refreshCookieSet?: boolean;
}

/**
 * Result of a token storage protection operation
 */
export interface TokenProtectionResult {
  success: boolean;
  error?: string;
  protected?: string[];
}

/**
 * Token information result
 */
export interface TokenInfoResult {
  success: boolean;
  error?: string;
  schema?: string;
  accessToken?: {
    payload: any;
    expiration: string;
    isExpired: boolean;
    sources: {
      schemaSpecific: boolean;
      general: boolean;
    };
  };
  refreshToken?: {
    payload: any;
    expiration: string;
    isExpired: boolean;
    sources: {
      schemaSpecific: boolean;
      general: boolean;
      cookie: boolean;
    };
  } | null;
}

/**
 * Fix token storage inconsistencies
 * @returns Result of the fix operation
 */
export function fixTokenStorage(): TokenFixResult;

/**
 * Preserve cookies when navigating between pages
 * This function should be called before navigation
 * @returns Result of the preservation operation
 */
export function preserveCookies(): CookiePreservationResult;

/**
 * Protect token storage from being cleared
 * @returns Result of the protection operation
 */
export function protectTokenStorage(): TokenProtectionResult;

/**
 * Show token information
 * @param schema Optional schema name
 * @returns Token information
 */
export function showTokenInfo(schema?: string): TokenInfoResult;

/**
 * Default export containing all functions
 */
declare const tokenFix: {
  fixTokenStorage: typeof fixTokenStorage;
  protectTokenStorage: typeof protectTokenStorage;
  preserveCookies: typeof preserveCookies;
  showTokenInfo: typeof showTokenInfo;
};

export default tokenFix;
