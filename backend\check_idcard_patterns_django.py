import os
import django
import json

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'neocamelot.settings')
django.setup()

from django.db import connection

def check_idcard_patterns():
    """Check the patterns of ID cards in all schemas."""
    print("=== Checking ID Card Patterns ===")
    
    # Get all schemas in the database
    with connection.cursor() as cursor:
        cursor.execute("""
            SELECT schema_name
            FROM information_schema.schemata
            WHERE schema_name NOT IN ('information_schema', 'pg_catalog', 'pg_toast', 'pg_temp_1', 'pg_toast_temp_1')
            ORDER BY schema_name
        """)
        schemas = cursor.fetchall()
        
        print(f"Found {len(schemas)} schemas in the database")
        
        # Process each schema
        for schema in schemas:
            schema_name = schema[0]
            print(f"\nProcessing schema: {schema_name}")
            
            # Check if the idcards_idcard table exists in this schema
            cursor.execute(f"""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_schema = '{schema_name}' 
                    AND table_name = 'idcards_idcard'
                )
            """)
            table_exists = cursor.fetchone()[0]
            
            if not table_exists:
                print(f"  idcards_idcard table does not exist in schema {schema_name}")
                continue
            
            # Check if the kebele_pattern column exists in this schema
            cursor.execute(f"""
                SELECT EXISTS (
                    SELECT FROM information_schema.columns 
                    WHERE table_schema = '{schema_name}' 
                    AND table_name = 'idcards_idcard'
                    AND column_name = 'kebele_pattern'
                )
            """)
            column_exists = cursor.fetchone()[0]
            
            if not column_exists:
                print(f"  kebele_pattern column does not exist in schema {schema_name}")
                continue
            
            # Get all ID cards with status 'PENDING_SUBCITY' in this schema
            cursor.execute(f"""
                SELECT id, status, kebele_pattern
                FROM "{schema_name}".idcards_idcard
                WHERE status = 'PENDING_SUBCITY'
                ORDER BY id
            """)
            id_cards = cursor.fetchall()
            
            print(f"  Found {len(id_cards)} ID cards with status 'PENDING_SUBCITY' in schema {schema_name}")
            
            # Check each ID card
            for id_card in id_cards:
                card_id, status, kebele_pattern = id_card
                print(f"  ID Card {card_id}:")
                print(f"    Status: {status}")
                print(f"    Kebele Pattern: {kebele_pattern is not None}")
                
                if kebele_pattern:
                    try:
                        # Try to parse the pattern as JSON
                        pattern_data = json.loads(kebele_pattern)
                        print(f"    Pattern Color: {pattern_data.get('color')}")
                        print(f"    Pattern Lines: {len(pattern_data.get('lines', []))}")
                        print(f"    Pattern Dots: {len(pattern_data.get('dots', []))}")
                    except Exception as e:
                        print(f"    Error parsing pattern: {str(e)}")

if __name__ == "__main__":
    check_idcard_patterns()
