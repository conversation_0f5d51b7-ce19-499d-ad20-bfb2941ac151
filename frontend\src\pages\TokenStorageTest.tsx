import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Typography,
  Button,
  Paper,
  TextField,
  Grid,
  List,
  ListItem,
  ListItemText,
  Divider,
  Alert,
  Snackbar,
} from '@mui/material';
import * as tokenStorage from '../services/tokenStorage';

const TokenStorageTest: React.FC = () => {
  const [currentSchema, setCurrentSchema] = useState<string | null>(null);
  const [schemaInput, setSchemaInput] = useState<string>('');
  const [accessToken, setAccessToken] = useState<string>('');
  const [refreshToken, setRefreshToken] = useState<string>('');
  const [message, setMessage] = useState<string>('');
  const [showMessage, setShowMessage] = useState<boolean>(false);
  const [storageInfo, setStorageInfo] = useState<any>(null);

  // Get the current schema on load
  useEffect(() => {
    const schema = tokenStorage.getCurrentSchema();
    setCurrentSchema(schema);
    
    if (schema) {
      setSchemaInput(schema);
      
      // Get tokens for the current schema
      const access = tokenStorage.getAccessToken(schema);
      const refresh = tokenStorage.getRefreshToken(schema);
      
      if (access) setAccessToken(access);
      if (refresh) setRefreshToken(refresh);
    }
    
    // Get storage info
    updateStorageInfo();
  }, []);
  
  // Update storage info
  const updateStorageInfo = () => {
    const info = tokenStorage.debugTokenStorage();
    setStorageInfo(info);
  };
  
  // Set current schema
  const handleSetCurrentSchema = () => {
    if (!schemaInput) {
      showMessageAlert('Schema cannot be empty');
      return;
    }
    
    tokenStorage.setCurrentSchema(schemaInput);
    setCurrentSchema(schemaInput);
    showMessageAlert(`Current schema set to: ${schemaInput}`);
    updateStorageInfo();
  };
  
  // Set access token
  const handleSetAccessToken = () => {
    if (!currentSchema) {
      showMessageAlert('No current schema selected');
      return;
    }
    
    if (!accessToken) {
      showMessageAlert('Access token cannot be empty');
      return;
    }
    
    tokenStorage.setAccessToken(accessToken, currentSchema);
    showMessageAlert(`Access token set for schema: ${currentSchema}`);
    updateStorageInfo();
  };
  
  // Set refresh token
  const handleSetRefreshToken = () => {
    if (!currentSchema) {
      showMessageAlert('No current schema selected');
      return;
    }
    
    if (!refreshToken) {
      showMessageAlert('Refresh token cannot be empty');
      return;
    }
    
    tokenStorage.setRefreshToken(refreshToken, currentSchema);
    showMessageAlert(`Refresh token set for schema: ${currentSchema}`);
    updateStorageInfo();
  };
  
  // Clear tokens
  const handleClearTokens = () => {
    if (!currentSchema) {
      showMessageAlert('No current schema selected');
      return;
    }
    
    tokenStorage.clearTokens(currentSchema);
    setAccessToken('');
    setRefreshToken('');
    showMessageAlert(`Tokens cleared for schema: ${currentSchema}`);
    updateStorageInfo();
  };
  
  // Fix token storage
  const handleFixTokenStorage = () => {
    if (typeof window !== 'undefined' && (window as any).tokenDebug) {
      (window as any).tokenDebug.fix();
      showMessageAlert('Token storage fixed');
      updateStorageInfo();
    } else {
      showMessageAlert('Token debug utilities not available');
    }
  };
  
  // Show token info
  const handleShowToken = () => {
    if (typeof window !== 'undefined' && (window as any).tokenDebug) {
      (window as any).tokenDebug.showToken(currentSchema || undefined);
      showMessageAlert('Token info shown in console');
    } else {
      showMessageAlert('Token debug utilities not available');
    }
  };
  
  // Show message alert
  const showMessageAlert = (msg: string) => {
    setMessage(msg);
    setShowMessage(true);
  };
  
  // Handle close message
  const handleCloseMessage = () => {
    setShowMessage(false);
  };

  return (
    <Container maxWidth="md">
      <Paper sx={{ p: 3, my: 3 }}>
        <Typography variant="h4" gutterBottom>
          Token Storage Test
        </Typography>
        
        <Typography variant="body1" paragraph>
          This page allows you to test the token storage functionality.
        </Typography>
        
        <Box sx={{ mb: 3 }}>
          <Typography variant="subtitle1">
            Current Schema: {currentSchema || 'None'}
          </Typography>
        </Box>
        
        <Grid container spacing={2}>
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label="Schema"
              value={schemaInput}
              onChange={(e) => setSchemaInput(e.target.value)}
              margin="normal"
            />
            <Button 
              variant="contained" 
              color="primary" 
              onClick={handleSetCurrentSchema}
              sx={{ mt: 1 }}
            >
              Set Current Schema
            </Button>
          </Grid>
          
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label="Access Token"
              value={accessToken}
              onChange={(e) => setAccessToken(e.target.value)}
              margin="normal"
              multiline
              rows={3}
            />
            <Button 
              variant="contained" 
              color="primary" 
              onClick={handleSetAccessToken}
              sx={{ mt: 1, mr: 1 }}
            >
              Set Access Token
            </Button>
            
            <TextField
              fullWidth
              label="Refresh Token"
              value={refreshToken}
              onChange={(e) => setRefreshToken(e.target.value)}
              margin="normal"
              multiline
              rows={3}
            />
            <Button 
              variant="contained" 
              color="primary" 
              onClick={handleSetRefreshToken}
              sx={{ mt: 1 }}
            >
              Set Refresh Token
            </Button>
          </Grid>
        </Grid>
        
        <Box sx={{ mt: 3 }}>
          <Button 
            variant="outlined" 
            color="secondary" 
            onClick={handleClearTokens}
            sx={{ mr: 1 }}
          >
            Clear Tokens
          </Button>
          
          <Button 
            variant="outlined" 
            color="info" 
            onClick={updateStorageInfo}
            sx={{ mr: 1 }}
          >
            Refresh Info
          </Button>
          
          <Button 
            variant="outlined" 
            color="success" 
            onClick={handleFixTokenStorage}
            sx={{ mr: 1 }}
          >
            Fix Token Storage
          </Button>
          
          <Button 
            variant="outlined" 
            color="warning" 
            onClick={handleShowToken}
          >
            Show Token Info
          </Button>
        </Box>
        
        {storageInfo && (
          <Box sx={{ mt: 3 }}>
            <Typography variant="h6" gutterBottom>
              Storage Information
            </Typography>
            
            <Typography variant="subtitle1">
              Current Schema: {storageInfo.currentSchema || 'None'}
            </Typography>
            
            <Typography variant="subtitle1">
              All Schemas: {storageInfo.allSchemas.join(', ') || 'None'}
            </Typography>
            
            <Typography variant="subtitle1" sx={{ mt: 2 }}>
              Token Store:
            </Typography>
            
            <List>
              {Object.entries(storageInfo.tokenStore).map(([schema, tokens]: [string, any]) => (
                <React.Fragment key={schema}>
                  <ListItem>
                    <ListItemText
                      primary={`Schema: ${schema}`}
                      secondary={
                        <>
                          <Typography component="span" variant="body2">
                            Access Token: {tokens.accessToken ? `${tokens.accessToken.substring(0, 20)}...` : 'None'}
                          </Typography>
                          <br />
                          <Typography component="span" variant="body2">
                            Refresh Token: {tokens.refreshToken ? `${tokens.refreshToken.substring(0, 20)}...` : 'None'}
                          </Typography>
                        </>
                      }
                    />
                  </ListItem>
                  <Divider />
                </React.Fragment>
              ))}
            </List>
          </Box>
        )}
      </Paper>
      
      <Snackbar
        open={showMessage}
        autoHideDuration={6000}
        onClose={handleCloseMessage}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert onClose={handleCloseMessage} severity="info" sx={{ width: '100%' }}>
          {message}
        </Alert>
      </Snackbar>
    </Container>
  );
};

export default TokenStorageTest;
