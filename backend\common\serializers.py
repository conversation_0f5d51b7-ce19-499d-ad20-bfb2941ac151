from rest_framework import serializers
from .models import Religion, CitizenStatus, MaritalStatus, EmploymentType, RelationshipType, EmployeeType, Country, Region, Ketena, DocumentType
from centers.models import Subcity, Kebele


class ReligionSerializer(serializers.ModelSerializer):
    """
    Serializer for the Religion model.
    """
    class Meta:
        model = Religion
        fields = ['id', 'name']


class CitizenStatusSerializer(serializers.ModelSerializer):
    """
    Serializer for the CitizenStatus model.
    """
    class Meta:
        model = CitizenStatus
        fields = ['id', 'name']


class MaritalStatusSerializer(serializers.ModelSerializer):
    """
    Serializer for the MaritalStatus model.
    """
    class Meta:
        model = MaritalStatus
        fields = ['id', 'name']


class EmploymentTypeSerializer(serializers.ModelSerializer):
    """
    Serializer for the EmploymentType model.
    """
    class Meta:
        model = EmploymentType
        fields = ['id', 'name']


class RelationshipTypeSerializer(serializers.ModelSerializer):
    """
    Serializer for the RelationshipType model.
    """
    class Meta:
        model = RelationshipType
        fields = ['id', 'name']
        ref_name = "CommonRelationshipType"


class EmployeeTypeSerializer(serializers.ModelSerializer):
    """
    Serializer for the EmployeeType model.
    """
    class Meta:
        model = EmployeeType
        fields = ['id', 'name']


class CountrySerializer(serializers.ModelSerializer):
    """
    Serializer for the Country model.
    """
    class Meta:
        model = Country
        fields = ['id', 'name', 'code']


class RegionSerializer(serializers.ModelSerializer):
    """
    Serializer for the Region model.
    """
    country_name = serializers.CharField(source='country.name', read_only=True)

    class Meta:
        model = Region
        fields = ['id', 'name', 'code', 'country', 'country_name']


class KetenaSerializer(serializers.ModelSerializer):
    """
    Serializer for the Ketena model.
    """
    class Meta:
        model = Ketena
        fields = ['id', 'name', 'code', 'kebele_id']


class DocumentTypeSerializer(serializers.ModelSerializer):
    """
    Serializer for the DocumentType model.
    """
    class Meta:
        model = DocumentType
        fields = ['id', 'name', 'description']
        ref_name = "CommonDocumentType"


class SubcitySerializer(serializers.ModelSerializer):
    """
    Serializer for the Subcity model.
    """
    city_name = serializers.CharField(source='city.city_name', read_only=True)

    class Meta:
        model = Subcity
        fields = ['id', 'name', 'code', 'city', 'city_name']
        ref_name = "CommonSubcity"


class CenterSerializer(serializers.ModelSerializer):
    """
    Serializer for the Kebele model (which represents Kebeles).
    """
    subcity_name = serializers.CharField(source='subcity.name', read_only=True)

    class Meta:
        model = Kebele
        fields = ['id', 'name', 'code', 'subcity', 'subcity_name']
        ref_name = "CommonCenter"
