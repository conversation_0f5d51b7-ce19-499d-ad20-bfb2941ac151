/**
 * Generate security patterns for ID cards
 */

// Import the castle image from assets
import { castleImageBase64 } from '../assets/castle-watermark';

/**
 * Generate a stylized castle-like watermark pattern for the ID card
 *
 * @param type - Type of pattern ('kebele' or 'subcity')
 * @param half - Which half of the pattern to generate ('left' or 'right')
 * @param tenantName - The name of the tenant (kebele or subcity)
 * @returns A data URL for the half pattern with castle-like design
 */
export const generateCastleWatermarkDataUrl = (type: 'kebele' | 'subcity', half: 'left' | 'right', tenantName?: string): string => {
  console.log(`generateCastleWatermarkDataUrl called with type: ${type}, half: ${half}, tenantName: ${tenantName || 'not provided'}`);

  // Define colors for text and overlays
  const castleColor = 'rgba(210, 170, 90, 0.5)'; // Sandy/golden color for text with higher opacity

  // Create a new SVG with the castle image and appropriate coloring
  const svgWidth = 500;
  const svgHeight = 300;
  const halfStartX = half === 'left' ? 0 : svgWidth / 2;
  const halfEndX = half === 'left' ? svgWidth / 2 : svgWidth;

  // Define colors based on the sandy/golden castle color
  const primaryColor = 'rgba(210, 170, 90, 0.15)'; // Sandy/golden color for castle
  const secondaryColor = 'rgba(210, 170, 90, 0.10)'; // Lighter sandy/golden color

  // Calculate center positions for text placement
  const centerX = half === 'left' ? svgWidth / 4 : (svgWidth * 3) / 4;
  const centerY = svgHeight / 2;

  // Create a SVG that uses the actual castle image as a watermark
  // Add the tenant name if provided
  const displayText = tenantName || (type === 'kebele' ? 'ቀበሌ ማረጋገጫ' : 'ክ/ከተማ ማረጋገጫ');

  const svg = `<svg xmlns="http://www.w3.org/2000/svg" width="${svgWidth}" height="${svgHeight}" viewBox="0 0 ${svgWidth} ${svgHeight}">
    <defs>
      <clipPath id="clip-path">
        <rect x="${halfStartX}" y="0" width="${halfEndX - halfStartX}" height="${svgHeight}" />
      </clipPath>
      <filter id="opacity-filter">
        <feColorMatrix type="matrix" values="
          1 0 0 0 0
          0 1 0 0 0
          0 0 1 0 0
          0 0 0 0.4 0
        "/>
      </filter>
    </defs>
    <g clip-path="url(#clip-path)">
      <rect width="${svgWidth}" height="${svgHeight}" fill="none" />

      <!-- Castle Image as Watermark -->
      <image
        href="${castleImageBase64}"
        x="${half === 'left' ? 0 : svgWidth/2 - 250}"
        y="0"
        width="500"
        height="300"
        filter="url(#opacity-filter)"
        preserveAspectRatio="xMidYMid meet"
      />

      <!-- Tenant Name Text -->
      <text x="${centerX}" y="${svgHeight - 50}" font-family="sans-serif" font-size="16" font-weight="bold" fill="${castleColor}" text-anchor="middle">
        ${displayText}
      </text>
    </g>
  </svg>`;

  // Convert to data URL
  try {
    const dataUrl = `data:image/svg+xml;base64,${btoa(svg)}`;
    console.log(`Generated dataUrl for ${type} ${half} (length: ${dataUrl.length})`);
    return dataUrl;
  } catch (e) {
    console.warn('Base64 encoding failed, falling back to URI encoding', e);
    const dataUrl = `data:image/svg+xml,${encodeURIComponent(svg)}`;
    console.log(`Generated fallback dataUrl for ${type} ${half} (length: ${dataUrl.length})`);
    return dataUrl;
  }
};

/**
 * Generate a half watermark pattern for the ID card
 *
 * @param seed - Seed value for consistent pattern generation
 * @param type - Type of pattern ('kebele' or 'subcity')
 * @param half - Which half of the pattern to generate ('left' or 'right')
 * @param tenantName - The name of the tenant (kebele or subcity)
 * @returns A data URL for the half pattern
 */
export const generateHalfWatermarkPatternDataUrl = (
  seed: number,
  type: 'kebele' | 'subcity',
  half: 'left' | 'right',
  tenantName?: string
): string => {
  // Use the castle watermark instead of the generated pattern
  return generateCastleWatermarkDataUrl(type, half, tenantName);
};

/**
 * Generate a half custom pattern for the ID card
 *
 * @param seed - Seed value for consistent pattern generation
 * @param isKebele - Whether this is for kebele (true) or subcity (false)
 * @param half - Which half of the pattern to generate ('left' or 'right')
 * @param tenantName - The name of the tenant (kebele or subcity)
 * @returns A data URL for the half pattern
 */
export const generateHalfCustomPatternDataUrl = (
  seed: number,
  isKebele: boolean,
  half: 'left' | 'right',
  tenantName?: string
): string => {
  // Convert to the castle watermark pattern
  const type = isKebele ? 'kebele' : 'subcity';
  return generateCastleWatermarkDataUrl(type, half, tenantName);
};

/**
 * Generate a watermark pattern for the entire ID card
 *
 * @param seed - Seed value for consistent pattern generation
 * @param type - Type of pattern ('kebele' or 'subcity')
 * @param tenantName - The name of the tenant (kebele or subcity)
 * @returns A data URL for the pattern
 */
export const generateWatermarkPatternDataUrl = (
  seed: number,
  type: 'kebele' | 'subcity',
  tenantName?: string
): string => {
  // For full card patterns, we'll use the left half pattern
  return generateCastleWatermarkDataUrl(type, 'left', tenantName);
};

/**
 * Generate both kebele and subcity patterns for an ID card
 *
 * @param seed - Seed value for consistent pattern generation
 * @param kebeleName - The name of the kebele tenant
 * @param subcityName - The name of the subcity tenant
 * @returns An object containing both pattern data URLs
 */
export const generatePatterns = (seed: number, kebeleName?: string, subcityName?: string) => {
  const kebelePatternUrl = generateWatermarkPatternDataUrl(seed, 'kebele', kebeleName);
  const subcityPatternUrl = generateWatermarkPatternDataUrl(seed, 'subcity', subcityName);

  return { kebelePatternUrl, subcityPatternUrl };
};

/**
 * Generate split patterns for an ID card based on the ID card data
 *
 * @param idCard - The ID card data object
 * @returns An object containing kebele and subcity pattern data URLs
 */
export const generateSplitPatternDataUrls = (idCard: any) => {
  console.log('generateSplitPatternDataUrls called with idCard:', idCard);

  // Generate a seed from the ID card number or a fallback
  const seed = idCard.citizen_id_number
    ? idCard.citizen_id_number.split('').reduce((acc: number, char: string) => acc + char.charCodeAt(0), 0)
    : 12345;

  console.log('Generated seed:', seed);

  // Extract tenant names if available
  const kebeleName = idCard.kebele_name || idCard.tenant_name || '';
  const subcityName = idCard.subcity_name || idCard.parent_tenant_name || '';

  console.log('Using tenant names - Kebele:', kebeleName, 'Subcity:', subcityName);

  // Generate the half patterns for kebele and subcity
  const kebelePatternUrl = generateHalfWatermarkPatternDataUrl(seed, 'kebele', 'left', kebeleName);
  const subcityPatternUrl = generateHalfWatermarkPatternDataUrl(seed, 'subcity', 'right', subcityName);

  console.log('Generated kebelePatternUrl:', kebelePatternUrl ? 'URL generated (length: ' + kebelePatternUrl.length + ')' : 'None');
  console.log('Generated subcityPatternUrl:', subcityPatternUrl ? 'URL generated (length: ' + subcityPatternUrl.length + ')' : 'None');

  return { kebelePatternUrl, subcityPatternUrl };
};

/**
 * Generate a pattern data URL from kebele and subcity pattern data
 *
 * @param kebelePattern - The kebele pattern data (JSON string or object)
 * @param subcityPattern - The subcity pattern data (JSON string or object)
 * @returns A data URL for the combined pattern
 */
export const generatePatternDataUrl = (kebelePattern: any, subcityPattern: any): string => {
  // Parse pattern data if it's a string
  let kebelePatternData = kebelePattern;
  let subcityPatternData = subcityPattern;

  try {
    if (typeof kebelePattern === 'string') {
      kebelePatternData = JSON.parse(kebelePattern);
    }
    if (typeof subcityPattern === 'string') {
      subcityPatternData = JSON.parse(subcityPattern);
    }
  } catch (e) {
    console.warn('Error parsing pattern data:', e);
  }

  // Generate a seed from the pattern data or use a default
  const kebeleSeed = kebelePatternData?.seed || Date.now();
  const subcitySeed = subcityPatternData?.seed || Date.now() + 1000;

  // Extract tenant names if available
  const kebeleName = kebelePatternData?.tenant_name || '';
  const subcityName = subcityPatternData?.tenant_name || '';

  // Generate the half patterns
  const kebelePatternUrl = kebelePatternData ? generateHalfWatermarkPatternDataUrl(kebeleSeed, 'kebele', 'left', kebeleName) : '';
  const subcityPatternUrl = subcityPatternData ? generateHalfWatermarkPatternDataUrl(subcitySeed, 'subcity', 'right', subcityName) : '';

  // Return the first available pattern URL, or an empty string if none are available
  return kebelePatternUrl || subcityPatternUrl || '';
};

/**
 * Determine the pattern status based on the ID card data
 *
 * @param idCard - The ID card data object
 * @returns An object with pattern status flags
 */
export const getPatternStatus = (idCard: any): { kebele: boolean, subcity: boolean, kebeleApproved?: boolean, subcityApproved?: boolean } => {
  // Check if patterns exist
  const hasKebelePattern = !!idCard.kebele_pattern;
  const hasSubcityPattern = !!idCard.subcity_pattern;

  // Check approval status
  const kebeleApproved = idCard.kebele_approval_status === 'APPROVED';
  const subcityApproved = idCard.subcity_approval_status === 'APPROVED';

  return {
    kebele: hasKebelePattern,
    subcity: hasSubcityPattern,
    kebeleApproved,
    subcityApproved
  };
};
