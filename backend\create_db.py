import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT

# Connect to PostgreSQL server
conn = psycopg2.connect(
    dbname='postgres',
    user='postgres',
    password='postgres',
    host='localhost',
    port='5432'
)

conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
cursor = conn.cursor()

# Create database
db_name = 'neocamelot'
try:
    cursor.execute(f"CREATE DATABASE {db_name}")
    print(f"Database '{db_name}' created successfully")
except psycopg2.errors.DuplicateDatabase:
    print(f"Database '{db_name}' already exists")
except Exception as e:
    print(f"Error creating database: {e}")

# Close connection
cursor.close()
conn.close()
