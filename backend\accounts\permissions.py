from rest_framework import permissions

class IsCenterAdmin(permissions.BasePermission):
    """Permission to allow only center admins to perform certain actions."""

    def has_permission(self, request, view):
        return request.user.is_authenticated and request.user.is_center_admin

class IsAdminOrSuperAdmin(permissions.BasePermission):
    """Permission to allow center admins or super admins."""

    def has_permission(self, request, view):
        return request.user.is_authenticated and (request.user.is_center_admin or request.user.is_super_admin)

class IsKebeleLevel(permissions.BasePermission):
    """Permission to allow only kebele level tenants to access certain features."""

    def has_permission(self, request, view):
        # Check if the user is authenticated and has a tenant
        if not request.user.is_authenticated or not hasattr(request, 'tenant'):
            return False

        # Allow access only if the tenant is of type KEBELE
        return request.tenant and request.tenant.schema_type == 'KEBELE'

class IsSubcityLevel(permissions.BasePermission):
    """Permission to allow only subcity level tenants to access certain features."""

    def has_permission(self, request, view):
        # Check if the user is authenticated and has a tenant
        if not request.user.is_authenticated or not hasattr(request, 'tenant'):
            return False

        # Allow access only if the tenant is of type SUBCITY
        return request.tenant and request.tenant.schema_type == 'SUBCITY'

class IsCityLevel(permissions.BasePermission):
    """Permission to allow only city level tenants to access certain features."""

    def has_permission(self, request, view):
        # Check if the user is authenticated and has a tenant
        if not request.user.is_authenticated or not hasattr(request, 'tenant'):
            return False

        # Allow access only if the tenant is of type CITY
        return request.tenant and request.tenant.schema_type == 'CITY'

class IsKebeleLevelOrSuperAdmin(permissions.BasePermission):
    """Permission to allow kebele level tenants or super admins."""

    def has_permission(self, request, view):
        # Super admins can access everything
        if request.user.is_authenticated and request.user.is_super_admin:
            return True

        # Check if the user is authenticated and has a tenant
        if not request.user.is_authenticated or not hasattr(request, 'tenant'):
            return False

        # Allow access only if the tenant is of type KEBELE
        return request.tenant and request.tenant.schema_type == 'KEBELE'

class IsKebeleLeader(permissions.BasePermission):
    """Permission to allow only kebele leaders (liqe menber) to perform certain actions."""

    def has_permission(self, request, view):
        # Check if the user is authenticated and is a kebele leader
        if not request.user.is_authenticated:
            return False

        return request.user.is_kebele_leader

class IsKebeleLeaderOrAdmin(permissions.BasePermission):
    """Permission to allow kebele leaders or center admins."""

    def has_permission(self, request, view):
        # Check if the user is authenticated
        if not request.user.is_authenticated:
            return False

        # Allow access if the user is a kebele leader or center admin
        return request.user.is_kebele_leader or request.user.is_center_admin or request.user.is_super_admin
