import os
import django
import random

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'neocamelot.settings')
django.setup()

from centers.models import Center, CenterType

def update_centers():
    """Update existing centers with center types and additional information."""
    # Get all center types
    center_types = list(CenterType.objects.all())
    
    # Get all centers
    centers = Center.objects.all()
    
    for center in centers:
        # Assign a random center type
        center.type = random.choice(center_types)
        
        # Update additional fields
        center.description = f"This is the {center.name} center for ID card issuance."
        center.city = random.choice(['Addis Ababa', '<PERSON><PERSON> Dawa', '<PERSON><PERSON> Dar', '<PERSON><PERSON><PERSON>', 'Mekelle'])
        center.state = random.choice(['Oromia', 'Amhara', 'Tigray', 'SNNPR', 'Addis Ababa'])
        center.postal_code = f"{random.randint(1000, 9999)}"
        center.website = f"https://www.{center.slug}.gov.et"
        center.header_color = random.choice(['#3498db', '#2ecc71', '#e74c3c', '#f39c12', '#9b59b6'])
        center.accent_color = random.choice(['#2980b9', '#27ae60', '#c0392b', '#d35400', '#8e44ad'])
        
        # Update administrative information
        center.admin_name = f"Admin {center.name}"
        center.admin_email = f"admin@{center.slug}.gov.et"
        center.admin_phone = f"+251-9{random.randint(10000000, 99999999)}"
        
        # Update system settings
        center.max_users = random.choice([5, 10, 20, 50])
        center.max_citizens = random.choice([500, 1000, 2000, 5000])
        center.max_id_cards = random.choice([500, 1000, 2000, 5000])
        
        # Update subscription status
        center.subscription_status = random.choice(['trial', 'basic', 'premium', 'enterprise'])
        
        # Save the center
        center.save()
        print(f"Updated center: {center.name} (Type: {center.type.name})")

if __name__ == '__main__':
    update_centers()
    print("Done updating centers.")
