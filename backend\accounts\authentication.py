"""
Legacy token authentication classes (deprecated).

This module previously contained token-based authentication classes that have been
replaced by JWT authentication. These classes are kept as stubs for backward
compatibility but are no longer used.
"""

import logging
from django.contrib.auth import get_user_model
from rest_framework import authentication

logger = logging.getLogger(__name__)
User = get_user_model()

class TenantTokenAuthentication(authentication.BaseAuthentication):
    """
    DEPRECATED: This class has been replaced by JWT authentication.

    This stub is kept for backward compatibility but is no longer used.
    """

    def authenticate(self, request):
        """
        Always returns None to indicate that this authentication method is not available.
        """
        logger.warning("TenantTokenAuthentication is deprecated and no longer used. Use JWT authentication instead.")
        return None


class MultiTenantTokenAuthentication(authentication.BaseAuthentication):
    """
    DEPRECATED: This class has been replaced by JWT authentication.

    This stub is kept for backward compatibility but is no longer used.
    """

    def authenticate(self, request):
        """
        Always returns None to indicate that this authentication method is not available.
        """
        logger.warning("MultiTenantTokenAuthentication is deprecated and no longer used. Use JWT authentication instead.")
        return None


class TokenAuthentication(authentication.BaseAuthentication):
    """
    DEPRECATED: This class has been replaced by JWT authentication.

    This stub is kept for backward compatibility but is no longer used.
    """

    def authenticate(self, request):
        """
        Always returns None to indicate that this authentication method is not available.
        """
        logger.warning("TokenAuthentication is deprecated and no longer used. Use JWT authentication instead.")
        return None
