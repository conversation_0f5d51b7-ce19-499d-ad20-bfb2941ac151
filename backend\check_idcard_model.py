import os
import django

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'neocamelot.settings')
django.setup()

from idcards.models import IDCard
from django.db import connection

# Check the IDCard model fields
print("=== IDCard Model Fields ===")
for field in IDCard._meta.get_fields():
    print(f"{field.name}: {field.get_internal_type()}")

# Check the database schema for the idcards_idcard table
print("\n=== Database Schema for idcards_idcard ===")
with connection.cursor() as cursor:
    cursor.execute("""
        SELECT column_name, data_type 
        FROM information_schema.columns 
        WHERE table_name = 'idcards_idcard'
    """)
    for column in cursor.fetchall():
        print(f"{column[0]}: {column[1]}")

# Check the pattern_status field specifically
print("\n=== Pattern Status Field ===")
try:
    # Check if there's a pattern_status field
    pattern_status_field = IDCard._meta.get_field('pattern_status')
    print(f"pattern_status field exists: {pattern_status_field.get_internal_type()}")
except Exception as e:
    print(f"Error getting pattern_status field: {str(e)}")

# Check if there are separate fields for kebele_pattern and subcity_pattern
print("\n=== Kebele and Subcity Pattern Fields ===")
try:
    kebele_pattern_field = IDCard._meta.get_field('kebele_pattern')
    print(f"kebele_pattern field exists: {kebele_pattern_field.get_internal_type()}")
except Exception as e:
    print(f"Error getting kebele_pattern field: {str(e)}")

try:
    subcity_pattern_field = IDCard._meta.get_field('subcity_pattern')
    print(f"subcity_pattern field exists: {subcity_pattern_field.get_internal_type()}")
except Exception as e:
    print(f"Error getting subcity_pattern field: {str(e)}")

# Check the actual data in the database
print("\n=== ID Cards with Pattern Status ===")
id_cards = IDCard.objects.all()[:5]  # Get first 5 ID cards
for card in id_cards:
    print(f"ID Card {card.id}:")
    print(f"  Status: {card.status}")
    print(f"  Kebele Approval Status: {card.kebele_approval_status}")
    
    # Check if pattern_status is a property or a field
    if hasattr(card, 'pattern_status'):
        print(f"  Pattern Status: {card.pattern_status}")
    
    # Check if kebele_pattern and subcity_pattern are properties or fields
    if hasattr(card, 'kebele_pattern'):
        print(f"  Kebele Pattern: {card.kebele_pattern}")
    
    if hasattr(card, 'subcity_pattern'):
        print(f"  Subcity Pattern: {card.subcity_pattern}")
    
    print()

# Check the security_patterns module
print("\n=== Security Patterns Module ===")
try:
    from idcards.security_patterns import apply_kebele_pattern, apply_subcity_pattern
    print("Security patterns module imported successfully")
    print(f"apply_kebele_pattern function: {apply_kebele_pattern}")
    print(f"apply_subcity_pattern function: {apply_subcity_pattern}")
except Exception as e:
    print(f"Error importing security_patterns module: {str(e)}")
