# JWT Authentication Implementation

This document describes the JWT authentication implementation in the NeoCamelot application.

## Overview

The application now uses JWT (JSON Web Tokens) exclusively for authentication in a multi-tenant system. Legacy token-based authentication has been completely removed to simplify the codebase and improve security.

## Changes Made

1. Removed all token-based authentication classes from Django settings
2. Replaced legacy token authentication classes with stub implementations that return None
3. Updated the admin authentication middleware to use JWT
4. Simplified Swagger settings to remove token authentication option
5. Updated the frontend to use only JWT authentication

## Backend Implementation

### Authentication Classes

The Django REST Framework authentication classes have been simplified to use only JWT authentication:

```python
REST_FRAMEWORK['DEFAULT_AUTHENTICATION_CLASSES'] = [
    'accounts.jwt_authentication.JWTAuthentication',  # JWT authentication as the only option
    'rest_framework.authentication.BasicAuthentication',  # Keep basic auth for development/testing purposes
]
```

### JWT Authentication Class

The `JWTAuthentication` class in `accounts/jwt_authentication.py` provides JWT authentication for Django REST Framework. It:

1. Extracts the JWT token from the request
2. Validates the token
3. Gets the user from the token payload
4. Sets the tenant context based on the schema in the token

### JWT Middleware

The `JWTSchemaMiddleware` in `accounts/jwt_middleware.py` sets the tenant schema for requests based on the JWT token. This middleware runs early in the request cycle to ensure the correct tenant context is set.

### Admin Authentication

The admin authentication middleware has been updated to use JWT instead of token-based authentication. It now:

1. Extracts the JWT token from the request
2. Validates the token
3. Gets the user from the token payload
4. Checks if the user is a superuser or staff member
5. Sets the user in the request

## Frontend Implementation

### Token Storage

Tokens are stored in:
- In-memory `tokenStore` object
- localStorage (both general and schema-specific keys)
- HTTP-only cookies for refresh tokens

### Token Management

The `tokenService.ts` file provides a centralized way to manage JWT tokens:

1. `storeTokensForSchema`: Stores access and refresh tokens for a specific schema
2. `getAccessTokenForSchema`: Gets the access token for a specific schema
3. `getRefreshTokenForSchema`: Gets the refresh token for a specific schema
4. `clearTokensForSchema`: Clears tokens for a specific schema
5. `clearAllTokens`: Clears all tokens
6. `getAuthHeaders`: Gets authentication headers for API requests
7. `loginWithJWT`: Logs in with JWT
8. `refreshJWTTokens`: Refreshes JWT tokens
9. `validateJWTToken`: Validates a JWT token

### Authentication Context

The `AuthContext.tsx` file provides an authentication context for the application. It:

1. Manages authentication state
2. Provides login and logout functions
3. Validates and refreshes tokens
4. Provides authentication headers for API requests

## Best Practices

1. Always use the `tokenService.ts` for token management
2. Always include the `X-Schema-Name` header in API requests
3. Always use the Bearer prefix for JWT tokens
4. Use HTTP-only cookies for refresh tokens
5. Implement token refresh before expiration
6. Use the `getAuthHeaders` function to get authentication headers for API requests

## Security Considerations

1. JWT tokens are short-lived (15 minutes) to minimize the impact of token theft
2. Refresh tokens are longer-lived (7 days) but are stored in HTTP-only cookies
3. Token rotation is implemented for refresh tokens to prevent replay attacks
4. Tokens are validated on every request
5. Tokens include the schema name to prevent cross-tenant access

## API Endpoints

1. `/api/jwt/login/`: Login endpoint that returns access and refresh tokens
2. `/api/jwt/refresh-token/`: Refresh token endpoint that returns new access and refresh tokens
3. `/api/jwt/validate-token/`: Validate token endpoint that checks if a token is valid

## Token Format

JWT tokens include the following claims:

1. `sub`: User ID
2. `email`: User email
3. `name`: User name
4. `is_superuser`: Whether the user is a superuser
5. `is_staff`: Whether the user is a staff member
6. `role`: User role
7. `tenant_level`: Tenant level (city, subcity, kebele)
8. `schema`: Schema name
9. `exp`: Expiration time
10. `iat`: Issued at time
11. `token_type`: Token type (access or refresh)
