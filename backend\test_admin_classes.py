import os
import django

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'neocamelot.settings')
django.setup()

# Import models and admin
from django.contrib import admin
from citizens.models import Citizen
from idcards.models import IDCard, IDCardTemplate

# Get the admin classes for each model
citizen_admin = admin.site._registry.get(Citizen)
template_admin = admin.site._registry.get(IDCardTemplate)
card_admin = admin.site._registry.get(IDCard)

print("Testing admin classes...")

# Test the Citizen admin class
print("\nTesting Citizen admin class...")
if citizen_admin:
    citizens = citizen_admin.get_queryset(None)
    print(f"Found {len(citizens)} citizens across all tenants")
    
    # Print the first 5 citizens
    print("First 5 citizens:")
    for i, citizen in enumerate(citizens[:5]):
        print(f"{i+1}. {citizen.first_name} {citizen.last_name} ({citizen.registration_number}) - Tenant: {getattr(citizen, 'schema_name', 'Unknown')}")
else:
    print("Citizen model is not registered with the admin site")

# Test the IDCardTemplate admin class
print("\nTesting IDCardTemplate admin class...")
if template_admin:
    templates = template_admin.get_queryset(None)
    print(f"Found {len(templates)} ID card templates across all tenants")
    
    # Print the first 5 templates
    print("First 5 templates:")
    for i, template in enumerate(templates[:5]):
        print(f"{i+1}. {template.name} (Default: {template.is_default}) - Tenant: {getattr(template, 'schema_name', 'Unknown')}")
else:
    print("IDCardTemplate model is not registered with the admin site")

# Test the IDCard admin class
print("\nTesting IDCard admin class...")
if card_admin:
    cards = card_admin.get_queryset(None)
    print(f"Found {len(cards)} ID cards across all tenants")
    
    # Print the first 5 cards
    print("First 5 ID cards:")
    for i, card in enumerate(cards[:5]):
        citizen_name = f"{card.citizen.first_name} {card.citizen.last_name}" if card.citizen else "Unknown"
        print(f"{i+1}. {card.card_number} - {citizen_name} - Tenant: {getattr(card, 'schema_name', 'Unknown')}")
else:
    print("IDCard model is not registered with the admin site")

print("\nDone!")
