import React, { useEffect, useState } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthProvider';
import { CircularProgress, Box, Typography } from '@mui/material';

interface ProtectedRouteProps {
  children: React.ReactNode;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ children }) => {
  const { isAuthenticated, updateAuthState } = useAuth();
  const location = useLocation();
  const [isInitializing, setIsInitializing] = useState(true);

  useEffect(() => {
    const initializeAuth = async () => {
      console.log('ProtectedRoute: Initializing authentication state for path:', location.pathname);
      setIsInitializing(true);

      try {
        // First, synchronize cookies with localStorage
        const { tokenManager } = await import('../../services/tokenManager');
        console.log('ProtectedRoute: Synchronizing cookies');
        tokenManager.synchronizeCookies();

        // Then update auth state
        updateAuthState();

        // Check if we're already authenticated
        if (isAuthenticated) {
          console.log('ProtectedRoute: Already authenticated, skipping further checks');
          setIsInitializing(false);
          return;
        }

        // Check for tokens in cookies that might not be in localStorage yet
        const cookies: Record<string, string> = {};
        document.cookie.split(';').forEach(cookie => {
          const [name, value] = cookie.trim().split('=');
          if (name) {
            cookies[name] = decodeURIComponent(value || '');
          }
        });

        // If we have auth cookies but aren't authenticated, try one more updateAuthState
        if ((cookies['jwt_access_token'] || cookies['jwt_access_token_frontend']) &&
            (cookies['jwt_refresh_token'] || cookies['refresh_token'])) {
          console.log('ProtectedRoute: Found auth cookies, updating auth state again');
          updateAuthState();
        }
      } catch (error) {
        console.error('ProtectedRoute: Error initializing auth state:', error);
        // Fall back to basic updateAuthState
        updateAuthState();
      } finally {
        setIsInitializing(false);
      }
    };

    initializeAuth();
  }, [location.pathname, updateAuthState, isAuthenticated]);

  // Show loading while initializing
  if (isInitializing) {
    return (
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          height: '100vh'
        }}
      >
        <CircularProgress size={40} />
        <Typography variant="body1" sx={{ mt: 2 }}>
          Verifying authentication...
        </Typography>
      </Box>
    );
  }

  // If not authenticated, redirect to login
  if (!isAuthenticated) {
    console.log('ProtectedRoute: Not authenticated, redirecting to login');
    // Store the current location to redirect back after login
    localStorage.setItem('redirectAfterLogin', location.pathname);
    return <Navigate to="/login" replace state={{ from: location }} />;
  }

  // If authenticated, render children
  return <>{children}</>;
};

export default ProtectedRoute;
