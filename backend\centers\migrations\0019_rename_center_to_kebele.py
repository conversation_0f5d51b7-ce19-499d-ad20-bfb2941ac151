# Generated by Django 4.2.7 on 2025-05-25 07:27

from django.conf import settings
from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('citizens', '0028_parent_relationship_type'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('idcards', '0009_rename_center_to_kebele'),
        ('accounts', '0011_remove_token_auth_completely'),
        ('centers', '0018_client_custom_domain'),
    ]

    operations = [
        migrations.RenameModel(
            old_name='Center',
            new_name='Kebele',
        ),
        migrations.AlterModelOptions(
            name='ketena',
            options={'ordering': ['kebele', 'name'], 'verbose_name': 'Ketena', 'verbose_name_plural': 'Ketenes'},
        ),
        migrations.RenameField(
            model_name='ketena',
            old_name='center',
            new_name='kebele',
        ),
    ]
