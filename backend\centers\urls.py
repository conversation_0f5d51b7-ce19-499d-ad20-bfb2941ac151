from django.urls import path
from .views_simple import centers_json, center_types_json, create_center
from .tenant_api import tenant_citizens, tenant_idcards, tenant_idcard_templates, tenant_citizen_generate_id, tenant_idcard_detail
from .tenant_citizen_detail import tenant_citizen_detail
from .tenant_family_api import tenant_family_members
from .tenant_document_api import tenant_documents
from .tenant_biometric_api import tenant_biometrics, tenant_photos
from .child_tenant_api import get_child_tenants, get_tenant_users, create_tenant_user, update_tenant_user, delete_tenant_user
from .city_tenant_api import get_child_subcities, get_subcity_users, create_subcity_user, update_subcity_user, delete_subcity_user
from .subcity_idcard_api import get_child_kebele_idcards, subcity_approve_idcard
from .tenant_idcard_actions import (
    tenant_idcard_kebele_approve,
    tenant_idcard_kebele_reject,
    tenant_idcard_apply_kebele_pattern,
    tenant_idcard_apply_subcity_pattern,
    tenant_idcard_send_to_subcity,
    tenant_idcard_subcity_approve
)
from .tenant_idcard_statistics import tenant_idcard_statistics
from .test_token_view import test_token_view
from .idcard_statistics_view import idcard_statistics_view

urlpatterns = [
    path('test-token/', test_token_view, name='test-token'),
    path('test-token/<str:schema_name>/', test_token_view, name='test-token-schema'),
    path('idcards/statistics/', idcard_statistics_view, name='idcard-statistics'),
    path('centers-simple/', centers_json, name='centers-simple'),
    path('center-types-simple/', center_types_json, name='center-types-simple'),
    path('centers-create-simple/', create_center, name='create-center-simple'),

    # Tenant-specific API endpoints
    path('tenant/<str:schema_name>/citizens/', tenant_citizens, name='tenant-citizens'),
    path('tenant/<str:schema_name>/citizens/<int:citizen_id>/', tenant_citizen_detail, name='tenant-citizen-detail'),
    path('tenant/<str:schema_name>/citizens/<int:citizen_id>/generate-id/', tenant_citizen_generate_id, name='tenant-citizen-generate-id'),
    path('tenant/<str:schema_name>/idcards/', tenant_idcards, name='tenant-idcards'),
    path('tenant/<str:schema_name>/idcards/<int:id>/', tenant_idcard_detail, name='tenant-idcard-detail'),
    path('tenant/<str:schema_name>/idcards/<int:id>/kebele_approve/', tenant_idcard_kebele_approve, name='tenant-idcard-kebele-approve'),
    path('tenant/<str:schema_name>/idcards/<int:id>/send_to_subcity/', tenant_idcard_send_to_subcity, name='tenant-idcard-send-to-subcity'),
    path('tenant/<str:schema_name>/idcards/<int:id>/kebele_reject/', tenant_idcard_kebele_reject, name='tenant-idcard-kebele-reject'),
    path('tenant/<str:schema_name>/idcards/<int:id>/apply_kebele_pattern/', tenant_idcard_apply_kebele_pattern, name='tenant-idcard-apply-kebele-pattern'),
    path('tenant/<str:schema_name>/idcards/<int:id>/apply_subcity_pattern/', tenant_idcard_apply_subcity_pattern, name='tenant-idcard-apply-subcity-pattern'),
    path('tenant/<str:schema_name>/idcards/<int:id>/subcity_approve/', tenant_idcard_subcity_approve, name='tenant-idcard-subcity-approve'),
    path('tenant/<str:schema_name>/idcards/statistics/', tenant_idcard_statistics, name='tenant-idcard-statistics'),
    path('tenant/<str:schema_name>/idcard-templates/', tenant_idcard_templates, name='tenant-idcard-templates'),

    # Family-related endpoints
    path('tenant/<str:schema_name>/citizens/<int:citizen_id>/children/',
         tenant_family_members, {'relation_type': 'children'}, name='tenant-citizen-children'),
    path('tenant/<str:schema_name>/citizens/<int:citizen_id>/parents/',
         tenant_family_members, {'relation_type': 'parents'}, name='tenant-citizen-parents'),
    path('tenant/<str:schema_name>/citizens/<int:citizen_id>/emergency-contacts/',
         tenant_family_members, {'relation_type': 'emergency_contacts'}, name='tenant-citizen-emergency-contacts'),
    path('tenant/<str:schema_name>/citizens/<int:citizen_id>/spouses/',
         tenant_family_members, {'relation_type': 'spouses'}, name='tenant-citizen-spouses'),

    # Document-related endpoints
    path('tenant/<str:schema_name>/citizens/<int:citizen_id>/documents/',
         tenant_documents, name='tenant-citizen-documents'),
    path('tenant/<str:schema_name>/documents/',
         tenant_documents, name='tenant-documents'),

    # Biometric-related endpoints
    path('tenant/<str:schema_name>/citizens/<int:citizen_id>/biometrics/',
         tenant_biometrics, name='tenant-citizen-biometrics'),
    path('tenant/<str:schema_name>/biometrics/',
         tenant_biometrics, name='tenant-biometrics'),
    path('tenant/<str:schema_name>/citizens/<int:citizen_id>/photos/',
         tenant_photos, name='tenant-citizen-photos'),
    path('tenant/<str:schema_name>/photos/',
         tenant_photos, name='tenant-photos'),

    # Child tenant management endpoints (for subcity admins)
    path('child-tenants/', get_child_tenants, name='child-tenants'),
    path('child-tenants/<str:schema_name>/users/', get_tenant_users, name='tenant-users'),
    path('child-tenants/<str:schema_name>/users/create/', create_tenant_user, name='create-tenant-user'),
    path('child-tenants/<str:schema_name>/users/<int:user_id>/', update_tenant_user, name='update-tenant-user'),
    path('child-tenants/<str:schema_name>/users/<int:user_id>/delete/', delete_tenant_user, name='delete-tenant-user'),

    # Child subcity management endpoints (for city admins)
    path('child-subcities/', get_child_subcities, name='child-subcities'),
    path('child-subcities/<str:schema_name>/users/', get_subcity_users, name='subcity-users'),
    path('child-subcities/<str:schema_name>/users/create/', create_subcity_user, name='create-subcity-user'),
    path('child-subcities/<str:schema_name>/users/<int:user_id>/', update_subcity_user, name='update-subcity-user'),
    path('child-subcities/<str:schema_name>/users/<int:user_id>/delete/', delete_subcity_user, name='delete-subcity-user'),

    # Child kebele ID card management endpoints (for subcity admins)
    path('child-kebele-idcards/', get_child_kebele_idcards, name='child-kebele-idcards'),
    path('child-kebele-idcards/<str:schema_name>/<int:idcard_id>/approve/', subcity_approve_idcard, name='subcity-approve-idcard'),
]
