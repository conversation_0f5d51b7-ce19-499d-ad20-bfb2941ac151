/**
 * Direct Authentication Utilities
 * 
 * This module provides direct authentication utilities for making authenticated requests.
 * It bypasses the normal authentication flow and directly makes requests with the token.
 */

/**
 * Make an authenticated request to the backend
 * @param url The URL to fetch
 * @param options The fetch options
 * @returns The response
 */
export const directAuthFetch = async (url: string, options: RequestInit = {}): Promise<Response> => {
  console.log('directAuthFetch: Making authenticated request to', url);
  
  // Get all possible tokens
  const token = localStorage.getItem('token');
  const adminToken = document.cookie
    .split('; ')
    .find(row => row.startsWith('adminToken='))
    ?.split('=')[1];
  
  // Get all possible schema names
  const schemaFromStorage = localStorage.getItem('schema_name');
  const schemaFromCookie = document.cookie
    .split('; ')
    .find(row => row.startsWith('schema_name='))
    ?.split('=')[1];
  
  // Try to extract schema from URL
  let schemaFromUrl: string | null = null;
  if (url.includes('/api/tenant/')) {
    const match = url.match(/\/api\/tenant\/([^\/]+)\//);
    if (match && match[1]) {
      try {
        schemaFromUrl = decodeURIComponent(match[1]);
      } catch (error) {
        console.error('Error decoding schema from URL:', error);
      }
    }
  }
  
  // Get the schema name to use
  const schemaName = schemaFromUrl || schemaFromStorage || schemaFromCookie;
  
  console.log('directAuthFetch: Using schema name:', schemaName);
  console.log('directAuthFetch: Using token:', token ? 'Token exists' : 'No token');
  console.log('directAuthFetch: Using admin token:', adminToken ? 'Admin token exists' : 'No admin token');
  
  // Create headers
  const headers = new Headers(options.headers || {});
  
  // Add the token to the headers
  if (token) {
    headers.set('Authorization', `Token ${token}`);
  } else if (adminToken) {
    headers.set('Authorization', `Token ${adminToken}`);
  }
  
  // Add the schema name to the headers
  if (schemaName) {
    headers.set('X-Schema-Name', schemaName);
    
    // Also set the schema name as a cookie
    document.cookie = `schema_name=${encodeURIComponent(schemaName)}; path=/; SameSite=Lax`;
  }
  
  // Log the headers
  console.log('directAuthFetch: Headers:', Object.fromEntries(headers.entries()));
  
  // Make the request
  try {
    const response = await fetch(url, {
      ...options,
      headers,
      credentials: 'include'
    });
    
    // Log the response
    console.log('directAuthFetch: Response status:', response.status);
    
    return response;
  } catch (error) {
    console.error('directAuthFetch: Error making request:', error);
    throw error;
  }
};

/**
 * Make a direct authenticated GET request
 * @param url The URL to fetch
 * @returns The response data
 */
export const directAuthGet = async <T>(url: string): Promise<T> => {
  const response = await directAuthFetch(url, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json'
    }
  });
  
  if (!response.ok) {
    throw new Error(`Error fetching ${url}: ${response.status} ${response.statusText}`);
  }
  
  return response.json();
};

/**
 * Make a direct authenticated POST request
 * @param url The URL to fetch
 * @param data The data to send
 * @returns The response data
 */
export const directAuthPost = async <T>(url: string, data: any): Promise<T> => {
  const response = await directAuthFetch(url, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(data)
  });
  
  if (!response.ok) {
    throw new Error(`Error posting to ${url}: ${response.status} ${response.statusText}`);
  }
  
  return response.json();
};

/**
 * Make a direct authenticated POST request with FormData
 * @param url The URL to fetch
 * @param formData The FormData to send
 * @returns The response data
 */
export const directAuthPostFormData = async <T>(url: string, formData: FormData): Promise<T> => {
  const response = await directAuthFetch(url, {
    method: 'POST',
    body: formData
  });
  
  if (!response.ok) {
    throw new Error(`Error posting form data to ${url}: ${response.status} ${response.statusText}`);
  }
  
  return response.json();
};
