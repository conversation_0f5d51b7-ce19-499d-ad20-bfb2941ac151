import React from 'react';
import { Box, Typography, Paper } from '@mui/material';
import { generateCastleWatermarkDataUrl } from '../utils/patternUtils';

const TestPatternDisplay: React.FC = () => {
  // Generate test patterns with tenant names
  const kebelePatternUrl = generateCastleWatermarkDataUrl('kebele', 'left', 'ገብርኤል ቀበሌ');
  const subcityPatternUrl = generateCastleWatermarkDataUrl('subcity', 'right', 'ዞብል ክ/ከተማ');

  console.log('TestPatternDisplay - kebelePatternUrl:', kebelePatternUrl ? 'Generated' : 'None');
  console.log('TestPatternDisplay - subcityPatternUrl:', subcityPatternUrl ? 'Generated' : 'None');

  return (
    <Paper sx={{ p: 3, m: 2 }}>
      <Typography variant="h5" gutterBottom>
        Test Pattern Display
      </Typography>

      <Typography variant="subtitle1" gutterBottom>
        <PERSON><PERSON><PERSON> (Left Half)
      </Typography>
      <Box
        sx={{
          width: '100%',
          height: '150px',
          border: '1px solid #ccc',
          backgroundImage: `url(${kebelePatternUrl})`,
          backgroundSize: 'cover',
          backgroundPosition: 'left center',
          mb: 2
        }}
      />

      <Typography variant="subtitle1" gutterBottom>
        Subcity Pattern (Right Half)
      </Typography>
      <Box
        sx={{
          width: '100%',
          height: '150px',
          border: '1px solid #ccc',
          backgroundImage: `url(${subcityPatternUrl})`,
          backgroundSize: 'cover',
          backgroundPosition: 'right center',
          mb: 2
        }}
      />

      <Typography variant="subtitle1" gutterBottom>
        Combined Pattern
      </Typography>
      <Box sx={{ display: 'flex', mb: 2 }}>
        <Box
          sx={{
            width: '50%',
            height: '150px',
            border: '1px solid #ccc',
            backgroundImage: `url(${kebelePatternUrl})`,
            backgroundSize: 'cover',
            backgroundPosition: 'left center'
          }}
        />
        <Box
          sx={{
            width: '50%',
            height: '150px',
            border: '1px solid #ccc',
            backgroundImage: `url(${subcityPatternUrl})`,
            backgroundSize: 'cover',
            backgroundPosition: 'right center'
          }}
        />
      </Box>

      <Typography variant="subtitle1" gutterBottom>
        Simple Test Pattern (Sandy/Golden Castle Color)
      </Typography>
      <Box
        sx={{
          width: '100%',
          height: '150px',
          border: '1px solid #ccc',
          backgroundImage: `url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSI1MDAiIGhlaWdodD0iMzAwIiB2aWV3Qm94PSIwIDAgNTAwIDMwMCI+PHJlY3Qgd2lkdGg9IjUwMCIgaGVpZ2h0PSIzMDAiIGZpbGw9InJnYmEoMjEwLCAxNzAsIDkwLCAwLjA1KSIvPjx0ZXh0IHg9IjI1MCIgeT0iMTUwIiBmb250LWZhbWlseT0ic2Fucy1zZXJpZiIgZm9udC1zaXplPSIyMCIgZmlsbD0icmdiYSgyMTAsIDE3MCwgOTAsIDAuMikiPlNhbmR5L0dvbGRlbiBDYXN0bGU8L3RleHQ+PC9zdmc+)`,
          backgroundSize: 'cover',
          backgroundPosition: 'center'
        }}
      />
    </Paper>
  );
};

export default TestPatternDisplay;
