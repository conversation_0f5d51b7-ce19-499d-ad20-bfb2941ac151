import React, { useState, useEffect } from 'react';
import { Container, Typography, Box, Paper, Divider } from '@mui/material';
import PageBanner from '../components/PageBanner';

import UpdateTenantLogo from '../components/tenant/UpdateTenantLogo';
import UpdateTenantColors from '../components/tenant/UpdateTenantColors';

const Settings: React.FC = () => {
  const [tenant, setTenant] = useState<any>(null);

  useEffect(() => {
    // Get tenant from localStorage
    const tenantStr = localStorage.getItem('tenant');
    if (tenantStr) {
      setTenant(JSON.parse(tenantStr));
    }
  }, []);

  // Handle logo update
  const handleLogoUpdated = (newLogoUrl: string) => {
    // Update the tenant state with the new logo URL
    setTenant(prev => {
      if (prev) {
        return { ...prev, logo_url: newLogoUrl };
      }
      return prev;
    });

    // Force reload the page to update the logo in the header
    window.location.reload();
  };

  // Handle colors update
  const handleColorsUpdated = (headerColor: string, accentColor: string) => {
    // Update the tenant state with the new colors
    setTenant(prev => {
      if (prev) {
        return { ...prev, header_color: headerColor, accent_color: accentColor };
      }
      return prev;
    });

    // The component will handle the reload
  };

  return (
    <>
      <PageBanner title="Settings" subtitle="Manage your tenant settings" />

      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Paper elevation={3} sx={{ p: 3, mb: 4 }}>
          <Typography variant="h5" gutterBottom>
            Tenant Settings
          </Typography>
          <Divider sx={{ mb: 3 }} />

          <Box sx={{ mb: 4 }}>
            <Typography variant="h6" gutterBottom>
              Tenant Information
            </Typography>
            <Box sx={{ pl: 2 }}>
              <Typography variant="body1">
                <strong>Name:</strong> {tenant?.name || 'N/A'}
              </Typography>
              <Typography variant="body1">
                <strong>Type:</strong> {tenant?.schema_type || 'N/A'}
              </Typography>
              <Typography variant="body1">
                <strong>Schema Name:</strong> {tenant?.schema_name || 'N/A'}
              </Typography>
            </Box>
          </Box>

          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
            <UpdateTenantLogo onLogoUpdated={handleLogoUpdated} />
            <UpdateTenantColors onColorsUpdated={handleColorsUpdated} />
          </Box>
        </Paper>
      </Container>
    </>
  );
};

export default Settings;
