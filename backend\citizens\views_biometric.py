from rest_framework import viewsets, permissions, filters, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from .models_biometric import Biometric, Photo
from .serializers_biometric import BiometricSerializer, PhotoSerializer
from accounts.permissions import IsCenterAdmin, IsAdminOrSuperAdmin, IsKebeleLevelOrSuperAdmin

class BiometricViewSet(viewsets.ModelViewSet):
    """ViewSet for viewing and editing Biometric instances."""
    queryset = Biometric.objects.all()
    serializer_class = BiometricSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['citizen']
    search_fields = ['citizen__first_name', 'citizen__last_name']
    ordering_fields = ['created_at']
    
    def get_permissions(self):
        """Set custom permissions for different actions."""
        if self.action in ['create', 'update', 'partial_update', 'destroy']:
            return [IsKebeleLevelOrSuperAdmin()]
        return [permissions.IsAuthenticated()]
    
    @action(detail=False, methods=['get'])
    def by_citizen(self, request):
        """Get biometric data for a specific citizen."""
        citizen_id = request.query_params.get('citizen_id')
        if not citizen_id:
            return Response({'error': 'citizen_id is required'}, status=status.HTTP_400_BAD_REQUEST)
        
        try:
            biometric = Biometric.objects.get(citizen_id=citizen_id)
            serializer = self.get_serializer(biometric)
            return Response(serializer.data)
        except Biometric.DoesNotExist:
            return Response({'error': 'Biometric data not found for this citizen'}, status=status.HTTP_404_NOT_FOUND)

class PhotoViewSet(viewsets.ModelViewSet):
    """ViewSet for viewing and editing Photo instances."""
    queryset = Photo.objects.all()
    serializer_class = PhotoSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['citizen']
    search_fields = ['citizen__first_name', 'citizen__last_name']
    ordering_fields = ['upload_date', 'created_at']
    
    def get_permissions(self):
        """Set custom permissions for different actions."""
        if self.action in ['create', 'update', 'partial_update', 'destroy']:
            return [IsKebeleLevelOrSuperAdmin()]
        return [permissions.IsAuthenticated()]
    
    @action(detail=False, methods=['get'])
    def by_citizen(self, request):
        """Get photo for a specific citizen."""
        citizen_id = request.query_params.get('citizen_id')
        if not citizen_id:
            return Response({'error': 'citizen_id is required'}, status=status.HTTP_400_BAD_REQUEST)
        
        try:
            photo = Photo.objects.get(citizen_id=citizen_id)
            serializer = self.get_serializer(photo)
            return Response(serializer.data)
        except Photo.DoesNotExist:
            return Response({'error': 'Photo not found for this citizen'}, status=status.HTTP_404_NOT_FOUND)
