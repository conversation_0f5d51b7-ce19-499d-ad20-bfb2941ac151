# Force Token Creation

This document provides instructions on how to force create tokens in the system when the normal token creation process fails.

## Background

The system uses two different authentication mechanisms:

1. **Token-based authentication** (using `authtoken_token` table)
2. **JWT-based authentication** (using JWT tokens with refresh tokens stored in cookies)

The issue is that tokens are not being properly created in the `authtoken_token` table, which is causing authentication failures. This document provides solutions to address this issue.

## Solution 1: Force Create Tokens Script

We've created a script to force create tokens in the `authtoken_token` table. This script bypasses the normal token creation process and directly creates tokens in the database.

### Usage

1. Copy the `force_create_tokens.py` script to the root directory of your backend project.

2. List all available schemas:

```bash
python force_create_tokens.py list_schemas
```

3. Verify if tokens exist for all users in a schema:

```bash
python force_create_tokens.py verify <schema_name>
```

Example:
```bash
python force_create_tokens.py verify kebele14
```

4. Force create a token for a specific user in a schema:

```bash
python force_create_tokens.py create_for_user <email> <schema_name>
```

Example:
```bash
python force_create_tokens.py create_for_user <EMAIL> kebele14
```

5. Force create tokens for all users in a schema:

```bash
python force_create_tokens.py create_for_all <schema_name>
```

Example:
```bash
python force_create_tokens.py create_for_all kebele14
```

## Solution 2: Direct Token Creation API

We've created a new API endpoint for directly creating tokens without authentication. This endpoint can be used by the frontend to create tokens when the normal token creation process fails.

### Backend Setup

1. Copy the `direct_token_create_view.py` file to the `accounts` directory of your backend project.

2. Add the view to your `accounts/views.py` file:

```python
from .direct_token_create_view import direct_token_create_view
```

3. Add the URL to your `neocamelot/urls.py` file:

```python
path('api/direct-token-create/', accounts_views.direct_token_create_view, name='direct-token-create'),
```

### Frontend Usage

The frontend code has been updated to use this endpoint when the normal token creation process fails. The `createNewToken` function now tries:

1. JWT login
2. Regular token login
3. Direct token creation
4. Hardcoded token as a last resort

## Solution 3: Hardcoded Token

As a last resort, you can use a hardcoded token. This is a temporary solution for development/testing only and should not be used in production.

### Usage

1. Force create a token for a user:

```bash
python force_create_tokens.py create_for_user <EMAIL> kebele14
```

2. Copy the token from the output.

3. Store the token in localStorage:

```javascript
localStorage.setItem('hardcoded_token', 'your_token_here');
```

You can do this in the browser's developer console.

## Example Workflow

If you're experiencing authentication issues in a specific tenant (e.g., kebele14), follow these steps:

1. List all schemas to confirm the schema name:
```bash
python force_create_tokens.py list_schemas
```

2. Verify if tokens exist for all users in the schema:
```bash
python force_create_tokens.py verify kebele14
```

3. Force create tokens for all users in the schema:
```bash
python force_create_tokens.py create_for_all kebele14
```

4. Set up the direct token creation API as described above.

5. If all else fails, use a hardcoded token as described above.

6. Refresh the application and try again.

## Troubleshooting

If you're still experiencing authentication issues after applying these solutions:

1. Check the database to confirm that tokens were created:
```sql
-- Connect to the tenant schema
SET search_path TO kebele14;

-- Check if tokens were created
SELECT * FROM authtoken_token;
```

2. Check the browser's localStorage and cookies:
   - Open the browser's developer tools
   - Go to the Application tab
   - Check localStorage for tokens
   - Check cookies for JWT tokens

3. Try logging out and logging in again.

4. Check the server logs for any errors related to token creation.

## Long-term Solution

The long-term solution is to:

1. Fix the normal token creation process
2. Complete the transition to JWT-based authentication
3. Remove the token-based authentication entirely
4. Ensure that the frontend consistently uses the JWT authentication flow

This will prevent conflicts between the two authentication systems and ensure consistent authentication behavior.

## Security Considerations

The direct token creation API and hardcoded token solutions are temporary workarounds and should not be used in production. They bypass normal authentication processes and can pose security risks.

Once the authentication issues are resolved, these temporary solutions should be removed.

## Support

If you continue to experience authentication issues, please contact the development team for assistance.
