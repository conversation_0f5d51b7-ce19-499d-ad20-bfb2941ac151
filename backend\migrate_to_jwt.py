"""
Migration script to help users migrate from legacy token authentication to JWT.

This script:
1. Finds all users with legacy tokens
2. Creates JWT tokens for them
3. Outputs the JWT tokens for manual distribution

Usage:
python migrate_to_jwt.py
"""

import os
import sys
import django
import datetime

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'neocamelot.settings')
django.setup()

from django.db import connection
from django_tenants.utils import tenant_context, get_public_schema_name
from centers.models import Client
from django.contrib.auth import get_user_model
from accounts.jwt_utils import generate_jwt_token

User = get_user_model()

def migrate_users_to_jwt():
    """Migrate all users with legacy tokens to JWT."""
    print("Starting migration from legacy tokens to JWT...")
    
    # First check the public schema
    connection.set_schema_to_public()
    
    # Get all users in the public schema
    users = User.objects.all()
    print(f"Found {len(users)} users in public schema")
    
    # Create JWT tokens for each user
    for user in users:
        print(f"\nCreating JWT tokens for user: {user.email} (ID: {user.id})")
        
        # Generate JWT tokens
        access_token = generate_jwt_token(user, get_public_schema_name(), is_refresh=False)
        refresh_token = generate_jwt_token(user, get_public_schema_name(), is_refresh=True)
        
        print(f"Access token: {access_token}")
        print(f"Refresh token: {refresh_token}")
    
    # Now check all tenant schemas
    tenants = Client.objects.exclude(schema_name=get_public_schema_name())
    for tenant in tenants:
        print(f"\nChecking tenant: {tenant.name} (Schema: {tenant.schema_name})")
        
        with tenant_context(tenant):
            # Get all users in this tenant
            users = User.objects.all()
            print(f"Found {len(users)} users in tenant {tenant.schema_name}")
            
            # Create JWT tokens for each user
            for user in users:
                print(f"\nCreating JWT tokens for user: {user.email} (ID: {user.id})")
                
                # Generate JWT tokens
                access_token = generate_jwt_token(user, tenant.schema_name, is_refresh=False)
                refresh_token = generate_jwt_token(user, tenant.schema_name, is_refresh=True)
                
                print(f"Access token: {access_token}")
                print(f"Refresh token: {refresh_token}")
    
    print("\nMigration complete!")

if __name__ == "__main__":
    migrate_users_to_jwt()
