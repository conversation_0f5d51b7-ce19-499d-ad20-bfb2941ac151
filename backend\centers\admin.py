from django.contrib import admin
from django.http import JsonResponse
from django.urls import path
from django import forms
from django.db.models.query import QuerySet
from .models import City, Subcity, Kebele, CenterType, Client, Domain, Ketena
from .proxy_models import GlobalCitizen, GlobalIDCardTemplate, GlobalIDCard
from django_tenants.utils import tenant_context
from itertools import chain

# Custom QuerySet-like class for multi-tenant admin
class MultiTenantQuerySet(list):
    """A custom QuerySet-like class that wraps a list but provides the methods
    that Django's admin site expects from a QuerySet."""

    def __init__(self, data=None):
        super().__init__(data or [])
        self._result_cache = None

    def distinct(self, *fields):
        # Simple implementation that removes duplicates based on object identity
        # For more complex scenarios, you might need to implement a more sophisticated approach
        return MultiTenantQuerySet(list(set(self)))

    def order_by(self, *fields):
        # Simple implementation that doesn't actually sort
        # In a real implementation, you would sort the list based on the fields
        return self

    def none(self):
        return MultiTenantQuerySet()

    def all(self):
        return MultiTenantQuerySet(self)

    def filter(self, **kwargs):
        # Simple implementation that doesn't actually filter
        # In a real implementation, you would filter the list based on the kwargs
        return self

    def exclude(self, **kwargs):
        # Simple implementation that doesn't actually exclude
        # In a real implementation, you would exclude items based on the kwargs
        return self

    def count(self):
        return len(self)

    def __getitem__(self, k):
        if isinstance(k, slice):
            return MultiTenantQuerySet(super().__getitem__(k))
        return super().__getitem__(k)

class ClientAdminForm(forms.ModelForm):
    id_card_format = forms.CharField(
        required=False,
        help_text="Format for ID card numbers. Available placeholders: {CENTER_PREFIX}, {TENANT_PREFIX}, {YEAR}, {RANDOM8}, etc.",
        widget=forms.TextInput(attrs={'size': '60'})
    )

    class Meta:
        model = Client
        fields = '__all__'

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Get the current ID card format from settings
        if self.instance and hasattr(self.instance, 'settings') and self.instance.settings:
            settings = self.instance.settings
            if isinstance(settings, dict) and 'id_card_format' in settings:
                self.fields['id_card_format'].initial = settings['id_card_format']

    def save(self, commit=True):
        instance = super().save(commit=False)

        # Initialize settings if it doesn't exist
        if not instance.settings:
            instance.settings = {}

        # Save the ID card format to settings
        if self.cleaned_data.get('id_card_format'):
            instance.settings['id_card_format'] = self.cleaned_data['id_card_format']
        elif 'id_card_format' in instance.settings:
            # Remove the setting if the field is empty
            del instance.settings['id_card_format']

        if commit:
            instance.save()
        return instance

@admin.register(Client)
class ClientAdmin(admin.ModelAdmin):
    form = ClientAdminForm
    list_display = ('name', 'schema_name', 'schema_type', 'is_active', 'created_at')
    list_filter = ('schema_type', 'is_active')
    search_fields = ('name', 'schema_name', 'description', 'admin_name', 'admin_email')
    readonly_fields = ('created_at', 'updated_at', 'created_by')
    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'schema_name', 'schema_type', 'description', 'parent')
        }),
        ('Contact Information', {
            'fields': ('address', 'phone', 'email', 'website')
        }),
        ('Visual Identity', {
            'fields': ('logo', 'header_color', 'accent_color')
        }),
        ('Administrative Information', {
            'fields': ('admin_name', 'admin_email', 'admin_phone')
        }),
        ('Status', {
            'fields': ('is_active',)
        }),
        ('Subcity-specific Fields', {
            'fields': ('has_printing_facility', 'printing_capacity'),
            'classes': ('collapse',)
        }),
        ('Kebele-specific Fields', {
            'fields': ('center_type', 'is_verified', 'subscription_status', 'subscription_expiry',
                     'max_users', 'max_citizens', 'max_id_cards', 'id_card_format', 'settings'),
            'classes': ('collapse',)
        }),
        ('System Information', {
            'fields': ('created_at', 'updated_at', 'created_by'),
            'classes': ('collapse',)
        }),
    )

    def save_model(self, request, obj, form, change):
        if not change:  # Only set created_by when creating a new object
            obj.created_by = request.user
        super().save_model(request, obj, form, change)

@admin.register(Domain)
class DomainAdmin(admin.ModelAdmin):
    list_display = ('domain', 'tenant', 'is_primary')
    list_filter = ('is_primary',)
    search_fields = ('domain', 'tenant__name')
    raw_id_fields = ('tenant',)

@admin.register(City)
class CityAdmin(admin.ModelAdmin):
    list_display = ('city_name', 'city_code', 'region', 'country', 'is_active', 'created_at')
    list_filter = ('is_active', 'region', 'country', 'is_resident')
    search_fields = ('city_name', 'city_code', 'mayor_name', 'contact_email', 'contact_phone')
    readonly_fields = ('city_code', 'created_at', 'updated_at', 'created_by')
    fieldsets = (
        ('Basic Information', {
            'fields': ('city_name', 'city_code', 'country', 'region')
        }),
        ('City Details', {
            'fields': ('motto_slogan', 'city_intro', 'established_date', 'area_sq_km', 'elevation_meters')
        }),
        ('Administration', {
            'fields': ('mayor_name', 'deputy_mayor')
        }),
        ('Contact Information', {
            'fields': ('contact_email', 'contact_phone', 'website', 'google_maps_url', 'headquarter_address', 'postal_code')
        }),
        ('Visual Identity', {
            'fields': ('logo',)
        }),
        ('Status', {
            'fields': ('is_active', 'is_resident')
        }),
        ('System Information', {
            'fields': ('created_at', 'updated_at', 'created_by'),
            'classes': ('collapse',)
        }),
    )

    def save_model(self, request, obj, form, change):
        if not change:  # Only set created_by when creating a new object
            obj.created_by = request.user
        super().save_model(request, obj, form, change)

@admin.register(Subcity)
class SubcityAdmin(admin.ModelAdmin):
    list_display = ('name', 'city', 'code', 'has_printing_facility', 'is_active', 'created_at')
    list_filter = ('city', 'is_active', 'has_printing_facility')
    search_fields = ('name', 'code', 'description', 'admin_name', 'admin_email')
    prepopulated_fields = {'slug': ('name',)}
    readonly_fields = ('code', 'created_at', 'updated_at', 'created_by')
    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'slug', 'code', 'description', 'city')
        }),
        ('Contact Information', {
            'fields': ('address', 'phone', 'email', 'website')
        }),
        ('Visual Identity', {
            'fields': ('logo', 'header_color', 'accent_color')
        }),
        ('Administrative Information', {
            'fields': ('admin_name', 'admin_email', 'admin_phone')
        }),
        ('Printing Capabilities', {
            'fields': ('has_printing_facility', 'printing_capacity')
        }),
        ('Status', {
            'fields': ('is_active',)
        }),

        ('System Information', {
            'fields': ('created_at', 'updated_at', 'created_by'),
            'classes': ('collapse',)
        }),
    )

    def save_model(self, request, obj, form, change):
        if not change:  # Only set created_by when creating a new object
            obj.created_by = request.user
        super().save_model(request, obj, form, change)

@admin.register(CenterType)
class CenterTypeAdmin(admin.ModelAdmin):
    list_display = ('name', 'is_active', 'created_at')
    list_filter = ('is_active',)
    search_fields = ('name', 'description')
    readonly_fields = ('created_at', 'updated_at')
    fieldsets = (
        (None, {
            'fields': ('name', 'description', 'is_active')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

class CenterAdminForm(forms.ModelForm):
    id_card_format = forms.CharField(
        required=False,
        help_text="Format for ID card numbers. Available placeholders: {CENTER_PREFIX}, {TENANT_PREFIX}, {YEAR}, {RANDOM8}, etc.",
        widget=forms.TextInput(attrs={'size': '60'})
    )

    class Meta:
        model = Kebele
        fields = '__all__'

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Get the current ID card format from settings
        if self.instance and hasattr(self.instance, 'settings') and self.instance.settings:
            settings = self.instance.settings
            if isinstance(settings, dict) and 'id_card_format' in settings:
                self.fields['id_card_format'].initial = settings['id_card_format']

    def save(self, commit=True):
        instance = super().save(commit=False)

        # Initialize settings if it doesn't exist
        if not instance.settings:
            instance.settings = {}

        # Save the ID card format to settings
        if self.cleaned_data.get('id_card_format'):
            instance.settings['id_card_format'] = self.cleaned_data['id_card_format']
        elif 'id_card_format' in instance.settings:
            # Remove the setting if the field is empty
            del instance.settings['id_card_format']

        if commit:
            instance.save()
        return instance

@admin.register(Ketena)
class KetenaAdmin(admin.ModelAdmin):
    list_display = ('name', 'code', 'kebele', 'is_active', 'created_at')
    list_filter = ('is_active', 'kebele', 'kebele__subcity')
    search_fields = ('name', 'code', 'description')
    readonly_fields = ('created_at', 'updated_at')
    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'code', 'description', 'kebele')
        }),
        ('Status', {
            'fields': ('is_active',)
        }),
        ('System Information', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def save_model(self, request, obj, form, change):
        super().save_model(request, obj, form, change)


@admin.register(Kebele)
class CenterAdmin(admin.ModelAdmin):
    form = CenterAdminForm
    list_display = ('name', 'subcity', 'code', 'type', 'subscription_status', 'is_active', 'is_verified', 'created_at')
    list_filter = ('subcity__city', 'subcity', 'is_active', 'is_verified', 'type', 'subscription_status')
    search_fields = ('name', 'code', 'email', 'address', 'admin_name', 'admin_email')
    prepopulated_fields = {'slug': ('name',)}
    readonly_fields = ('code', 'created_at', 'updated_at', 'created_by')
    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'slug', 'code', 'type', 'description', 'subcity')
        }),
        ('Contact Information', {
            'fields': ('address', 'city', 'state', 'postal_code', 'country', 'phone', 'alternate_phone', 'email', 'website')
        }),
        ('Visual Identity', {
            'fields': ('logo', 'header_color', 'accent_color')
        }),
        ('Administrative Information', {
            'fields': ('admin_name', 'admin_email', 'admin_phone')
        }),
        ('System Settings', {
            'fields': ('max_users', 'max_citizens', 'max_id_cards', 'settings', 'id_card_format')
        }),
        ('Status', {
            'fields': ('is_active', 'is_verified', 'subscription_status', 'subscription_expiry')
        }),

        ('System Information', {
            'fields': ('created_at', 'updated_at', 'created_by'),
            'classes': ('collapse',)
        }),
    )

    def save_model(self, request, obj, form, change):
        if not change:  # Only set created_by when creating a new object
            obj.created_by = request.user
        super().save_model(request, obj, form, change)

    def get_urls(self):
        urls = super().get_urls()
        custom_urls = [
            path('api/centers-json/', self.admin_site.admin_view(self.centers_json), name='centers-json'),
            path('api/center-types-json/', self.admin_site.admin_view(self.center_types_json), name='center-types-json'),
        ]
        return custom_urls + urls

    def centers_json(self, request):
        """Return all centers as JSON."""
        centers = Kebele.objects.all()
        data = [
            {
                'id': center.id,
                'name': center.name,
                'code': center.code,
                'slug': center.slug,
                'type': center.type.name if center.type else None,
                'subcity': center.subcity.name,
                'subcity_id': center.subcity.id,
                'city': center.subcity.city.city_name,
                'city_id': center.subcity.city.id,
                'description': center.description,
                'address': center.address,
                'email': center.email,
                'phone': center.phone,
                'website': center.website,
                'is_active': center.is_active,
                'is_verified': center.is_verified,
            }
            for center in centers
        ]
        return JsonResponse(data, safe=False)

    def center_types_json(self, request):
        """Return all center types as JSON."""
        types = CenterType.objects.all()
        data = [
            {
                'id': type_obj.id,
                'name': type_obj.name,
                'description': type_obj.description,
                'is_active': type_obj.is_active,
            }
            for type_obj in types
        ]
        return JsonResponse(data, safe=False)

    def get_form(self, request, obj=None, **kwargs):
        form = super().get_form(request, obj, **kwargs)
        if 'subcity' in form.base_fields:
            # Filter subcities to only show active ones
            form.base_fields['subcity'].queryset = Subcity.objects.filter(is_active=True)
        return form


# Disable admin interface for global models
# These are proxy models and not essential for the application to function
# The admin interface for these models is causing issues with the 'distinct' method
# If you need to access these models, you can create a custom view or API endpoint
