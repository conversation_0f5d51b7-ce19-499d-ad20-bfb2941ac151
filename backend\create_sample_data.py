import os
import django
import random
from datetime import datetime, timedelta

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'neocamelot.settings')
django.setup()

# Import models
from django.contrib.auth import get_user_model
from centers.models import Center, Client, City, Subcity
from citizens.models import Citizen
from idcards.models import IDCardTemplate, IDCard
from django_tenants.utils import tenant_context, schema_context

User = get_user_model()

# Create sample data for a tenant
def create_sample_data_for_tenant(tenant_schema):
    print(f"\nCreating sample data for tenant: {tenant_schema}")
    try:
        # Get the tenant
        tenant = Client.objects.get(schema_name=tenant_schema)

        with tenant_context(tenant):
            # Create a center if it doesn't exist
            centers = Center.objects.all()
            if centers.exists():
                center = centers.first()
                print(f"Using existing center: {center.name}")
            else:
                # Get the subcity for this tenant
                subcity = None
                if tenant_schema.startswith('kebele'):
                    # This is a kebele tenant, find a subcity to use
                    subcities = Subcity.objects.all()
                    if subcities.exists():
                        subcity = subcities.first()
                        print(f"Using subcity: {subcity.name}")
                    else:
                        # Create a subcity
                        city = City.objects.first()
                        if not city:
                            # Create a city
                            city = City.objects.create(
                                name="Gondar",
                                slug="gondar",
                                is_active=True
                            )
                            print(f"Created city: {city.name}")

                        subcity = Subcity.objects.create(
                            name="Azezo",
                            slug="azezo",
                            city=city,
                            is_active=True
                        )
                        print(f"Created subcity: {subcity.name}")

                # Create a center
                center = Center.objects.create(
                    name=f"Sample Center for {tenant.name}",
                    slug=f"sample-center-{tenant.schema_name}",
                    subcity=subcity,
                    is_active=True
                )
                print(f"Created center: {center.name}")

            # Create an ID card template if it doesn't exist
            templates = IDCardTemplate.objects.filter(center=center)
            if templates.exists():
                template = templates.first()
                print(f"Using existing template: {template.name}")
            else:
                # Create a template
                template = IDCardTemplate.objects.create(
                    name="Default Template",
                    center=center,
                    is_default=True,
                    front_layout={
                        'title': {'x': 10, 'y': 10, 'font_size': 18},
                        'photo': {'x': 20, 'y': 40, 'width': 100, 'height': 120},
                        'name': {'x': 130, 'y': 50, 'font_size': 14},
                        'id_number': {'x': 130, 'y': 70, 'font_size': 12},
                    },
                    back_layout={
                        'address': {'x': 10, 'y': 10, 'font_size': 12},
                        'issue_date': {'x': 10, 'y': 30, 'font_size': 12},
                        'expiry_date': {'x': 10, 'y': 50, 'font_size': 12},
                    }
                )
                print(f"Created template: {template.name}")

            # Create sample citizens if they don't exist
            citizens_count = Citizen.objects.count()
            if citizens_count > 0:
                print(f"Found {citizens_count} existing citizens")
            else:
                # Create sample citizens
                first_names = ["Abebe", "Kebede", "Almaz", "Tigist", "Yonas", "Hanna", "Dawit", "Meron", "Solomon", "Rahel"]
                last_names = ["Bekele", "Tadesse", "Haile", "Girma", "Tesfaye", "Negash", "Alemu", "Gebre", "Mengistu", "Assefa"]
                genders = ["M", "F"]
                nationalities = ["Ethiopian", "Eritrean", "Kenyan", "Sudanese", "Somalian"]
                occupations = ["Teacher", "Doctor", "Engineer", "Farmer", "Driver", "Merchant", "Student", "Nurse", "Accountant", "Lawyer"]

                for i in range(10):
                    # Generate random data
                    first_name = random.choice(first_names)
                    last_name = random.choice(last_names)
                    gender = random.choice(genders)
                    nationality = random.choice(nationalities)
                    occupation = random.choice(occupations)
                    date_of_birth = datetime.now() - timedelta(days=random.randint(6570, 25550))  # 18-70 years

                    # Create citizen
                    citizen = Citizen.objects.create(
                        center=center,
                        first_name=first_name,
                        last_name=last_name,
                        gender=gender,
                        date_of_birth=date_of_birth,
                        nationality=nationality,
                        occupation=occupation,
                        address=f"Sample Address {i+1}",
                        phone=f"+251911{random.randint(100000, 999999)}",
                        email=f"{first_name.lower()}.{last_name.lower()}@example.com",
                        is_active=True
                    )
                    print(f"Created citizen: {citizen.first_name} {citizen.last_name}")

                    # Create ID card for this citizen
                    issue_date = datetime.now() - timedelta(days=random.randint(0, 365))
                    expiry_date = issue_date + timedelta(days=365 * 5)  # 5 years validity

                    id_card = IDCard.objects.create(
                        citizen=citizen,
                        template=template,
                        issue_date=issue_date,
                        expiry_date=expiry_date,
                        status=random.choice(["PENDING", "APPROVED", "PRINTED", "DELIVERED"]),
                        card_data={}
                    )
                    print(f"Created ID card: {id_card.card_number}")

                print(f"Created {10} sample citizens and ID cards")

    except Exception as e:
        print(f"Error creating sample data: {str(e)}")

# Create sample data for all kebele tenants
print("Creating sample data for all kebele tenants:")
kebele_tenants = Client.objects.filter(schema_name__startswith='kebele')
for tenant in kebele_tenants:
    create_sample_data_for_tenant(tenant.schema_name)

print("\nDone!")
