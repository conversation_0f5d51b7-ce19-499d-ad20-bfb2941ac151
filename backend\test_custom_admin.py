import os
import django

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'neocamelot.settings')
django.setup()

# Import the custom admin site
from neocamelot.admin_site import admin_site, GlobalCitizenAdmin, GlobalIDCardTemplateAdmin, GlobalIDCardAdmin
from citizens.models import Citizen
from idcards.models import IDCard, IDCardTemplate

# Check if the models are registered in the custom admin site
registered_models = [model.__name__ for model, _ in admin_site._registry.items()]
print(f"Models registered in custom admin site: {registered_models}")

# Test the global admin classes
print("\nTesting GlobalCitizenAdmin...")
citizen_admin = GlobalCitizenAdmin(Citizen, admin_site)
citizens = citizen_admin.get_queryset(None)
print(f"Found {len(citizens)} citizens across all tenants")

print("\nTesting GlobalIDCardTemplateAdmin...")
template_admin = GlobalIDCardTemplateAdmin(IDCardTemplate, admin_site)
templates = template_admin.get_queryset(None)
print(f"Found {len(templates)} ID card templates across all tenants")

print("\nTesting GlobalIDCardAdmin...")
card_admin = GlobalIDCardAdmin(IDCard, admin_site)
cards = card_admin.get_queryset(None)
print(f"Found {len(cards)} ID cards across all tenants")

print("\nDone!")
print("You can now access the custom admin site at /neocamelot-admin/")
print("Please restart the Django server for the changes to take effect.")
