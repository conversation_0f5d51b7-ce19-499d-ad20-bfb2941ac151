import os
import django

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'neocamelot.settings')
django.setup()

from centers.models import Client

def main():
    # Get all tenants
    tenants = Client.objects.all().order_by('schema_type', 'name')
    
    print(f"Found {tenants.count()} tenants\n")
    
    # Print tenant information
    print("TENANTS:")
    print("{:<20} {:<30} {:<15} {:<20} {:<10}".format(
        "Schema Name", "Name", "Type", "Parent", "Status"
    ))
    print("-" * 100)
    
    for tenant in tenants:
        parent_name = tenant.parent.name if tenant.parent else "None"
        print("{:<20} {:<30} {:<15} {:<20} {:<10}".format(
            tenant.schema_name,
            tenant.name,
            tenant.schema_type,
            parent_name,
            "Active" if tenant.is_active else "Inactive"
        ))

if __name__ == "__main__":
    main()
