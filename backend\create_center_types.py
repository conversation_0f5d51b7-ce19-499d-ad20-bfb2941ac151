import os
import django

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'neocamelot.settings')
django.setup()

from centers.models import CenterType

def create_center_types():
    """Create some center types for testing."""
    types = [
        {
            'name': 'Government Office',
            'description': 'Official government office for ID card issuance'
        },
        {
            'name': 'Municipal Center',
            'description': 'Local municipal center for citizen services'
        },
        {
            'name': 'District Office',
            'description': 'District-level administrative office'
        },
        {
            'name': 'Community Center',
            'description': 'Community-based service center'
        },
        {
            'name': 'Mobile Unit',
            'description': 'Mobile ID card issuance unit'
        }
    ]
    
    for type_data in types:
        CenterType.objects.get_or_create(
            name=type_data['name'],
            defaults={'description': type_data['description']}
        )
        print(f"Created center type: {type_data['name']}")

if __name__ == '__main__':
    create_center_types()
    print("Done creating center types.")
