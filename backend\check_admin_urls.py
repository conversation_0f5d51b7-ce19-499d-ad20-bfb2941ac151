import os
import django

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'neocamelot.settings')
django.setup()

# Import URL patterns
from django.urls import get_resolver
from neocamelot.urls import urlpatterns as public_urlpatterns
from neocamelot.urls_tenants import urlpatterns as tenant_urlpatterns

# Check admin URLs
print("Admin URLs in public schema:")
for pattern in public_urlpatterns:
    if hasattr(pattern, 'pattern') and 'admin' in str(pattern.pattern):
        print(f"- {pattern.pattern}")

print("\nAdmin URLs in tenant schemas:")
for pattern in tenant_urlpatterns:
    if hasattr(pattern, 'pattern') and 'admin' in str(pattern.pattern):
        print(f"- {pattern.pattern}")
