/**
 * Token Debugger Utility
 * 
 * This utility helps diagnose token-related issues by checking the state of tokens
 * in localStorage, sessionStorage, and cookies.
 */

import { getAccessTokenForSchema, getCurrentSchema } from '../services/tokenService';

/**
 * Debug token information
 * @returns Object with token debug information
 */
export const debugTokens = (): Record<string, any> => {
  // Get current schema
  const currentSchema = getCurrentSchema();
  
  // Get token for current schema
  const token = currentSchema ? getAccessTokenForSchema(currentSchema) : null;
  
  // Check localStorage for tokens
  const localStorageTokens: Record<string, string> = {};
  for (let i = 0; i < localStorage.length; i++) {
    const key = localStorage.key(i);
    if (key && (
      key.includes('token') || 
      key.includes('Token') || 
      key.includes('jwt') || 
      key.includes('JWT') ||
      key.includes('schema')
    )) {
      localStorageTokens[key] = localStorage.getItem(key) || '';
    }
  }
  
  // Check sessionStorage for tokens
  const sessionStorageTokens: Record<string, string> = {};
  for (let i = 0; i < sessionStorage.length; i++) {
    const key = sessionStorage.key(i);
    if (key && (
      key.includes('token') || 
      key.includes('Token') || 
      key.includes('jwt') || 
      key.includes('JWT') ||
      key.includes('schema')
    )) {
      sessionStorageTokens[key] = sessionStorage.getItem(key) || '';
    }
  }
  
  // Check cookies for tokens
  const cookies: Record<string, string> = {};
  document.cookie.split(';').forEach(cookie => {
    const [key, value] = cookie.trim().split('=');
    if (key && (
      key.includes('token') || 
      key.includes('Token') || 
      key.includes('jwt') || 
      key.includes('JWT') ||
      key.includes('schema')
    )) {
      cookies[key] = value;
    }
  });
  
  // Get user and tenant info
  const userInfo = localStorage.getItem('user');
  const tenantInfo = localStorage.getItem('tenant');
  
  // Create headers that would be used for API requests
  const headers: Record<string, string> = {
    'Content-Type': 'application/json',
  };
  
  if (token) {
    // Try both formats
    headers['Authorization-Bearer'] = `Bearer ${token}`;
    headers['Authorization-Token'] = `Token ${token}`;
    headers['X-Auth-Token'] = token;
  }
  
  if (currentSchema) {
    headers['X-Schema-Name'] = currentSchema;
  }
  
  return {
    currentSchema,
    token: token ? `${token.substring(0, 10)}...` : null,
    localStorageTokens,
    sessionStorageTokens,
    cookies,
    user: userInfo ? JSON.parse(userInfo) : null,
    tenant: tenantInfo ? JSON.parse(tenantInfo) : null,
    headers,
  };
};

/**
 * Log token debug information to console
 */
export const logTokenDebugInfo = (): void => {
  const debugInfo = debugTokens();
  console.log('===== TOKEN DEBUG INFO =====');
  console.log('Current Schema:', debugInfo.currentSchema);
  console.log('Token:', debugInfo.token);
  console.log('Headers that would be used:');
  console.log(debugInfo.headers);
  console.log('localStorage Tokens:', debugInfo.localStorageTokens);
  console.log('sessionStorage Tokens:', debugInfo.sessionStorageTokens);
  console.log('Cookies:', debugInfo.cookies);
  console.log('User Info:', debugInfo.user);
  console.log('Tenant Info:', debugInfo.tenant);
  console.log('===== END TOKEN DEBUG INFO =====');
};

export default {
  debugTokens,
  logTokenDebugInfo,
};
