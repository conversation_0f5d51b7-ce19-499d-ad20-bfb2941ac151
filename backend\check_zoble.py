import os
import django

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'neocamelot.settings')
django.setup()

# Import models
from centers.models import Client

# Check <PERSON>ob<PERSON>'s schema_type
print("\n=== Checking <PERSON><PERSON><PERSON>'s Schema Type ===")
try:
    zoble = Client.objects.get(name='Zoble')
    print(f"Name: {zoble.name}")
    print(f"Schema Name: {zoble.schema_name}")
    print(f"Schema Type: {zoble.schema_type}")
    print(f"Schema Type Type: {type(zoble.schema_type)}")
    print(f"Is Schema Type 'SUBCITY'?: {zoble.schema_type == 'SUBCITY'}")
    print(f"Is Schema Type 'CITY'?: {zoble.schema_type == 'CITY'}")
    if zoble.parent:
        print(f"Parent: {zoble.parent.name} ({zoble.parent.schema_type})")
    else:
        print("Parent: None")
except Client.DoesNotExist:
    print("Zoble not found in the database")
except Exception as e:
    print(f"Error: {str(e)}")

# Test the API endpoint
print("\n=== Testing API Endpoint for Subcity Registration ===")
import requests
try:
    response = requests.get("http://localhost:8000/api/available-tenants/?type=SUBCITY")
    print(f"Status Code: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        print(f"Number of tenants returned: {len(data)}")
        for tenant in data:
            print(f"- {tenant['name']} (ID: {tenant['id']}, Type: {tenant['schema_type']}, Schema: {tenant['schema_name']})")
    else:
        print(f"Error: {response.text}")
except Exception as e:
    print(f"Error testing API: {str(e)}")
