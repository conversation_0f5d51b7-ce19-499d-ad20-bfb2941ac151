/**
 * Token refresh utility for handling authentication token refresh and validation
 */

// Token refresh interval in milliseconds (5 minutes)
const TOKEN_REFRESH_INTERVAL = 5 * 60 * 1000;

// Token expiration time in milliseconds (30 minutes)
const TOKEN_EXPIRATION_TIME = 30 * 60 * 1000;

// Last token refresh timestamp
let lastTokenRefreshTime = 0;

// Flag to track if a refresh is in progress
let isRefreshingToken = false;

/**
 * Refresh the authentication token
 * @returns Promise<boolean> True if token refresh was successful
 */
export const refreshToken = async (): Promise<boolean> => {
  // Prevent multiple simultaneous refresh attempts
  if (isRefreshingToken) {
    console.log('Token refresh already in progress, skipping');
    return false;
  }

  isRefreshingToken = true;

  try {
    console.log('Attempting to refresh authentication token');

    // Get the current token
    const currentToken = localStorage.getItem('token');
    if (!currentToken) {
      console.error('No token found in localStorage, cannot refresh');
      return false;
    }

    // Call the token refresh endpoint
    // Try multiple endpoints since we're not sure which one is correct
    let response;

    try {
      // Import CSRF utilities
      const { addCsrfToken } = await import('./csrfUtils');

      // Create headers with CSRF token
      const headers = new Headers({
        'Content-Type': 'application/json',
        'Authorization': `Token ${currentToken}`
      });

      // Add CSRF token to headers
      addCsrfToken(headers);

      console.log('Token refresh headers:', Object.fromEntries(headers.entries()));

      // Try multiple token refresh endpoints
      // First try the standard token refresh endpoint
      try {
        response = await fetch('/api/token/refresh/', {
          method: 'POST',
          headers,
          credentials: 'include'
        });

        console.log('Token refresh response:', response.status, response.statusText);

        // If we get a CSRF error, try with a different endpoint
        if (response.status === 403 && (await response.text()).includes('CSRF')) {
          console.log('CSRF error detected, trying alternative endpoint');

          // Try the token-refresh endpoint (without trailing slash)
          response = await fetch('/api/token-refresh', {
            method: 'POST',
            headers,
            credentials: 'include'
          });

          console.log('Alternative token refresh response:', response.status, response.statusText);
        }
      } catch (error) {
        console.error('Error during token refresh:', error);

        // Try the token-refresh endpoint (without trailing slash)
        try {
          console.log('Trying alternative token refresh endpoint');
          response = await fetch('/api/token-refresh', {
            method: 'POST',
            headers,
            credentials: 'include'
          });

          console.log('Alternative token refresh response:', response.status, response.statusText);
        } catch (altError) {
          console.error('Error with alternative token refresh endpoint:', altError);
          throw altError;
        }
      }

      if (!response.ok) {
        // If token refresh fails, try to get tenant info to verify the schema
        try {
          // Import tenant utilities
          const { getTenantInfo, getCorrectSchemaForToken } = await import('./tenantUtils');

          // Get the current schema
          const currentSchema = localStorage.getItem('schema_name') || 'kebele16';

          // Try to get tenant info
          const tenantInfo = await getTenantInfo(currentSchema);

          if (tenantInfo) {
            console.log('Tenant info verified:', tenantInfo);

            // Try to find the correct schema for the token
            const correctSchema = await getCorrectSchemaForToken(currentToken);

            if (correctSchema) {
              console.log(`Found correct schema for token: ${correctSchema}`);

              // Update schema in localStorage and cookie
              localStorage.setItem('schema_name', correctSchema);
              document.cookie = `schema_name=${encodeURIComponent(correctSchema)}; path=/`;

              // Token is valid in the correct schema
              return true;
            }
          }
        } catch (error) {
          console.error('Error verifying tenant info during token refresh:', error);
        }
      }
    } catch (error) {
      console.error('Error during token refresh attempts:', error);
      return false;
    }

    if (!response.ok) {
      console.error('Token refresh failed:', response.status, response.statusText);

      // If unauthorized, clear token and redirect to login
      if (response.status === 401) {
        // Save form data before redirecting (if on a form page)
        const currentPath = window.location.pathname;
        if (currentPath.includes('register-citizen')) {
          // Import dynamically to avoid circular dependencies
          const { saveFormData } = await import('./formPersistence');
          saveFormData('citizen_registration', window['formData'] || {});
        }

        // Clear authentication data
        localStorage.removeItem('token');
        localStorage.removeItem('user');
        localStorage.removeItem('tenant');
        localStorage.removeItem('schema_name');

        // Redirect to login with return URL
        window.location.href = `/login?returnUrl=${encodeURIComponent(window.location.pathname)}`;
      }

      return false;
    }

    try {
      // Try to parse the response as JSON
      const data = await response.json();

      // Check if we got a new token
      if (data && data.token) {
        // Update the token in localStorage
        localStorage.setItem('token', data.token);
        console.log('Token refreshed successfully');

        // Update last refresh time
        lastTokenRefreshTime = Date.now();

        return true;
      } else {
        console.log('No new token received, but response was OK');

        // Still update the last refresh time to prevent constant refresh attempts
        lastTokenRefreshTime = Date.now();

        return true;
      }
    } catch (error) {
      console.error('Error parsing token refresh response:', error);

      // If we can't parse the response but it was OK, the token is probably still valid
      if (response.ok) {
        console.log('Response was OK but could not parse JSON, assuming token is still valid');

        // Update last refresh time
        lastTokenRefreshTime = Date.now();

        return true;
      }

      return false;
    }
  } catch (error) {
    console.error('Error refreshing token:', error);
    return false;
  } finally {
    isRefreshingToken = false;
  }
};

/**
 * Check if the token needs to be refreshed
 * @returns boolean True if token needs refresh
 */
export const needsTokenRefresh = (): boolean => {
  // If no last refresh time, assume token needs refresh
  if (!lastTokenRefreshTime) return true;

  // Check if enough time has passed since last refresh
  const timeSinceLastRefresh = Date.now() - lastTokenRefreshTime;
  return timeSinceLastRefresh > TOKEN_REFRESH_INTERVAL;
};

/**
 * Initialize token refresh mechanism
 * Sets up periodic token refresh
 */
export const initializeTokenRefresh = async (): Promise<void> => {
  console.log('Initializing token refresh mechanism');

  // Import the schema validator
  const { validateTokenSchema } = await import('./schemaValidator');

  // Validate the token in the current schema
  const isValid = await validateTokenSchema();
  if (!isValid) {
    console.error('Token is not valid in any schema');
    // Don't set up refresh interval for invalid tokens
    return;
  }

  // Set initial last refresh time
  lastTokenRefreshTime = Date.now();

  // Set up interval to check and refresh token
  setInterval(async () => {
    // Only refresh if needed
    if (needsTokenRefresh()) {
      await refreshToken();
    }
  }, TOKEN_REFRESH_INTERVAL / 2); // Check twice as often as the refresh interval

  // Also refresh token on page load
  refreshToken();
};

/**
 * Get a valid token, refreshing if necessary
 * @returns Promise<string|null> Valid token or null if unavailable
 */
export const getValidToken = async (): Promise<string|null> => {
  // Get current token
  const token = localStorage.getItem('token');
  if (!token) return null;

  // Check if token needs refresh
  if (needsTokenRefresh()) {
    const refreshed = await refreshToken();
    if (!refreshed) {
      // If refresh failed, return current token anyway
      // The API call will fail if the token is invalid
      return token;
    }
    // Get the refreshed token
    return localStorage.getItem('token');
  }

  // Return current token
  return token;
};

/**
 * Handle API response with authentication error
 * @param response The fetch response object
 * @returns Promise<boolean> True if handled (redirected), false otherwise
 */
export const handleAuthError = async (response: Response): Promise<boolean> => {
  if (response.status === 401) {
    console.log('Authentication error detected, attempting to refresh token');

    // Try to get response details
    try {
      const errorText = await response.clone().text();
      console.error('Error details:', errorText);

      // Check if the error is related to schema
      if (errorText.includes('not found in') || errorText.includes('schema')) {
        console.log('Schema-related error detected');

        // Import tenant utilities
        const { getTenantInfo, getCorrectSchemaForToken, updateCurrentSchema } = await import('./tenantUtils');

        // Try to get the token
        const token = localStorage.getItem('token');
        if (!token) {
          console.error('No token found in localStorage');
          return;
        }

        // Try to find the correct schema for the token
        console.log('Trying to find correct schema for token');
        const correctSchema = await getCorrectSchemaForToken(token);

        if (correctSchema) {
          console.log(`Found correct schema for token: ${correctSchema}`);

          // Update schema in localStorage and cookie
          updateCurrentSchema(correctSchema);

          console.log(`Updated schema name to: ${correctSchema}`);
        } else {
          // If we can't find the correct schema, try to extract it from the URL
          const { extractSchemaFromUrl } = await import('./schemaUtils');

          // Try to extract schema name from URL
          const url = response.url;
          const urlSchemaName = extractSchemaFromUrl(url);

          if (urlSchemaName) {
            console.log(`Extracted schema name from URL: ${urlSchemaName}`);

            // Verify the schema exists
            const tenantInfo = await getTenantInfo(urlSchemaName);

            if (tenantInfo) {
              console.log('Tenant info verified for URL schema:', tenantInfo);

              // Update schema in localStorage and cookie
              updateCurrentSchema(urlSchemaName);

              console.log(`Updated schema name to: ${urlSchemaName}`);
            } else {
              console.warn('Could not verify tenant info for URL schema, using default: kebele16');
              updateCurrentSchema('kebele16');
            }
          } else {
            console.warn('Could not extract schema from URL, using default: kebele16');
            updateCurrentSchema('kebele16');
          }
        }
      }
    } catch (e) {
      console.error('Could not read error response');
    }

    // Try to refresh the token
    const refreshed = await refreshToken();
    if (!refreshed) {
      // If refresh failed, redirect to login
      console.log('Token refresh failed, redirecting to login');

      // Save form data before redirecting (if on a form page)
      const currentPath = window.location.pathname;
      if (currentPath.includes('register-citizen')) {
        // Import dynamically to avoid circular dependencies
        const { saveFormData } = await import('./formPersistence');
        saveFormData('citizen_registration', window['formData'] || {});
      }

      // Redirect to login with return URL
      window.location.href = `/login?returnUrl=${encodeURIComponent(window.location.pathname)}`;
      return true;
    }

    // Token refreshed successfully
    return false;
  }

  // Not an auth error
  return false;
};

/**
 * Enhanced fetch function with automatic token handling
 * @param url The URL to fetch
 * @param options Fetch options
 * @returns Promise<Response> The fetch response
 */
export const fetchWithAuth = async (
  url: string,
  options: RequestInit = {}
): Promise<Response> => {
  // Get token from localStorage
  const token = localStorage.getItem('token');

  console.log('fetchWithAuth - Using token:', token ? 'Token exists' : 'No token');

  if (!token) {
    console.error('No token found in localStorage. This will cause authentication issues.');

    // Try to get the adminToken as a fallback
    const adminToken = document.cookie
      .split('; ')
      .find(row => row.startsWith('adminToken='))
      ?.split('=')[1];

    if (adminToken) {
      console.log('Using adminToken from cookie as fallback');
      // Store it in localStorage for future use
      localStorage.setItem('token', adminToken);
    } else {
      console.error('No adminToken found in cookies either. Authentication will fail.');
    }
  }

  // Import schema and CSRF utilities
  const { formatSchemaForHeader, getCurrentSchema, extractSchemaFromUrl } = await import('./schemaUtils');
  const { addCsrfToken } = await import('./csrfUtils');

  // Get schema name using the utility function
  const schemaName = getCurrentSchema();
  console.log('fetchWithAuth - Using schema name:', schemaName);

  // Always ensure the schema name is set as a cookie
  if (schemaName) {
    document.cookie = `schema_name=${encodeURIComponent(schemaName)}; path=/; SameSite=Lax`;
    console.log('fetchWithAuth - Set schema_name cookie:', schemaName);
  }

  // Prepare headers
  const headers = new Headers(options.headers || {});

  // ALWAYS include the Authorization header with the token
  // Use the token from localStorage or the adminToken from cookies
  const effectiveToken = token || document.cookie
    .split('; ')
    .find(row => row.startsWith('adminToken='))
    ?.split('=')[1];

  if (effectiveToken) {
    headers.set('Authorization', `Token ${effectiveToken}`);
    console.log('fetchWithAuth - Set Authorization header with token');
  } else {
    console.error('No token available for Authorization header. Request will likely fail.');
  }

  // Add CSRF token to headers
  addCsrfToken(headers);

  // CRITICAL: Always include a schema name
  let formattedSchema = '';

  if (schemaName) {
    // Format the schema name for the header (preserving spaces)
    formattedSchema = formatSchemaForHeader(schemaName);
    console.log('Using schema name from localStorage:', formattedSchema);
  } else {
    // If no schema name is available, try multiple sources
    console.warn('No schema name found in localStorage');

    // Try to get the schema from the tenant object
    try {
      const tenantStr = localStorage.getItem('tenant');
      if (tenantStr) {
        const tenant = JSON.parse(tenantStr);
        if (tenant && tenant.schema_name) {
          formattedSchema = formatSchemaForHeader(tenant.schema_name);
          console.log('Using schema name from tenant object:', formattedSchema);

          // Store it in localStorage for future use
          localStorage.setItem('schema_name', tenant.schema_name);

          // Also set as cookie
          document.cookie = `schema_name=${encodeURIComponent(tenant.schema_name)}; path=/; SameSite=Lax`;
        }
      }
    } catch (error) {
      console.error('Error parsing tenant from localStorage:', error);
    }

    // If still no schema, try to get it from the cookie
    if (!formattedSchema) {
      const schemaCookie = document.cookie
        .split('; ')
        .find(row => row.startsWith('schema_name='))
        ?.split('=')[1];

      if (schemaCookie) {
        try {
          const decodedSchema = decodeURIComponent(schemaCookie);
          formattedSchema = formatSchemaForHeader(decodedSchema);
          console.log('Using schema name from cookie:', formattedSchema);

          // Store it in localStorage for future use
          localStorage.setItem('schema_name', decodedSchema);
        } catch (error) {
          console.error('Error decoding schema cookie:', error);
        }
      }
    }

    // If still no schema, try to extract it from the URL
    if (!formattedSchema && url.includes('/tenant/')) {
      try {
        const extractedSchema = extractSchemaFromUrl(url);
        if (extractedSchema) {
          formattedSchema = formatSchemaForHeader(extractedSchema);
          console.log('Using schema name extracted from URL:', formattedSchema);

          // Store it in localStorage for future use
          localStorage.setItem('schema_name', extractedSchema);

          // Also set as cookie
          document.cookie = `schema_name=${encodeURIComponent(extractedSchema)}; path=/; SameSite=Lax`;
        }
      } catch (error) {
        console.error('Error extracting schema from URL:', error);
      }
    }

    // If still no schema, try to extract it from the URL
    if (!formattedSchema) {
      // Try to extract schema from URL
      const validSchemas = ['city_gondar', 'subcity_zoble', 'kebele 14', 'kebele 15', 'kebele16'];
      for (const schema of validSchemas) {
        // Convert spaces to underscores for URL matching
        const urlSchema = schema.replace(/ /g, '_');
        if (url.includes(urlSchema) || url.includes(encodeURIComponent(urlSchema))) {
          formattedSchema = formatSchemaForHeader(schema);
          console.log('Extracted schema from URL match:', formattedSchema);

          // Store it in localStorage for future use
          localStorage.setItem('schema_name', schema);

          // Also set as cookie
          document.cookie = `schema_name=${encodeURIComponent(schema)}; path=/; SameSite=Lax`;
          break;
        }
      }
    }

    // If still no schema name, we'll proceed without one
    if (!formattedSchema) {
      console.error('No schema name available, request may fail');
    }
  }

  // Set the schema name header
  headers.set('X-Schema-Name', formattedSchema);
  console.log(`Adding schema name to request: ${formattedSchema}`);

  // Also add the schema name as a cookie for backend compatibility
  document.cookie = `schema_name=${encodeURIComponent(formattedSchema)}; path=/`;

  // CRITICAL: For tenant-specific endpoints, verify the schema exists
  if (url.includes('/tenant/') && !url.includes('/tenant-info/')) {
    try {
      // Import tenant utilities
      const { getTenantInfo, updateCurrentSchema } = await import('./tenantUtils');

      // Get tenant info to verify the schema exists
      const tenantInfo = await getTenantInfo(formattedSchema);

      if (tenantInfo) {
        console.log('Tenant info verified for request:', tenantInfo);

        // If the tenant info has a different schema_name, use that
        if (tenantInfo.schema_name && tenantInfo.schema_name !== formattedSchema) {
          console.log(`Updating schema name from ${formattedSchema} to ${tenantInfo.schema_name}`);
          formattedSchema = formatSchemaForHeader(tenantInfo.schema_name);

          // Update the header
          headers.set('X-Schema-Name', formattedSchema);

          // Update localStorage and cookie
          updateCurrentSchema(tenantInfo.schema_name);
        }
      }
    } catch (error) {
      console.error('Error verifying tenant info for request:', error);
    }
  }

  console.log('Request headers:', Object.fromEntries(headers.entries()));

  // Add content type if not set and not FormData
  if (!headers.has('Content-Type') && !(options.body instanceof FormData)) {
    headers.set('Content-Type', 'application/json');
  }

  // Add accept header if not set
  if (!headers.has('Accept')) {
    headers.set('Accept', 'application/json');
  }

  // Log the request details before final checks
  console.log(`Preparing authenticated request to: ${url}`);

  // CRITICAL: Double-check that we have an Authorization header
  if (!headers.has('Authorization')) {
    console.error('No Authorization header set! Adding it now as a last resort.');

    // Try to get a token from any available source
    const lastResortToken = localStorage.getItem('token') ||
                           document.cookie.split('; ').find(row => row.startsWith('adminToken='))?.split('=')[1];

    if (lastResortToken) {
      headers.set('Authorization', `Token ${lastResortToken}`);
      console.log('Added Authorization header as last resort');
    } else {
      console.error('CRITICAL ERROR: No token available from any source. Request will fail.');
    }
  }

  // CRITICAL: Double-check that we have an X-Schema-Name header or schema_name cookie
  if (!headers.has('X-Schema-Name') && !document.cookie.includes('schema_name=')) {
    console.error('No schema name set! Adding it now as a last resort.');

    // Try to get a schema from any available source
    const lastResortSchema = localStorage.getItem('schema_name') || 'kebele16';

    if (lastResortSchema) {
      headers.set('X-Schema-Name', lastResortSchema);
      document.cookie = `schema_name=${encodeURIComponent(lastResortSchema)}; path=/; SameSite=Lax`;
      console.log('Added X-Schema-Name header and cookie as last resort');
    }
  }

  // Make the request
  const response = await fetch(url, {
    ...options,
    headers,
    credentials: 'include'
  });

  // Handle auth errors
  if (response.status === 401) {
    console.log('Received 401 Unauthorized response');

    // Try to get response details
    try {
      const errorText = await response.text();
      console.error('Error details:', errorText);
    } catch (e) {
      console.error('Could not read error response');
    }

    // Try to refresh token and retry the request
    const refreshed = await refreshToken();
    if (refreshed) {
      // Get the new token
      const newToken = localStorage.getItem('token');

      // Update the authorization header
      headers.set('Authorization', `Token ${newToken}`);

      // Import schema and CSRF utilities
      const { formatSchemaForHeader, getCurrentSchema } = await import('./schemaUtils');
      const { addCsrfToken } = await import('./csrfUtils');

      // Add CSRF token to headers
      addCsrfToken(headers);

      // CRITICAL: Always include a schema name for retry requests
      const schemaName = getCurrentSchema();
      let formattedSchema = '';

      if (schemaName) {
        // Format the schema name for the header (preserving spaces)
        formattedSchema = formatSchemaForHeader(schemaName);
      } else {
        // If no schema name is available, log a warning but don't use a hardcoded value
        console.warn('No schema name found for retry request');

        // Try to get the schema from the tenant object
        try {
          const tenantStr = localStorage.getItem('tenant');
          if (tenantStr) {
            const tenant = JSON.parse(tenantStr);
            if (tenant && tenant.schema_name) {
              formattedSchema = formatSchemaForHeader(tenant.schema_name);
              console.log('Using schema name from tenant object for retry:', formattedSchema);

              // Store it in localStorage for future use
              localStorage.setItem('schema_name', tenant.schema_name);
            }
          }
        } catch (error) {
          console.error('Error parsing tenant from localStorage for retry:', error);
        }

        // If still no schema name, we'll proceed without one
        if (!formattedSchema) {
          console.error('No schema name available for retry, request may fail');
        }
      }

      // Set the schema name header
      headers.set('X-Schema-Name', formattedSchema);
      console.log(`Adding schema name to retry request: ${formattedSchema}`);

      // Also add the schema name as a cookie for backend compatibility
      document.cookie = `schema_name=${encodeURIComponent(formattedSchema)}; path=/`;

      console.log('Retrying request with new token');
      console.log(`Retry headers: ${JSON.stringify(Object.fromEntries(headers.entries()), null, 2)}`);

      // Retry the request
      return fetch(url, {
        ...options,
        headers,
        credentials: 'include'
      });
    } else {
      console.error('Token refresh failed, cannot retry request');

      // Import schema and CSRF utilities
      const { extractSchemaFromUrl, formatSchemaForHeader, setCurrentSchema } = await import('./schemaUtils');
      const { addCsrfToken } = await import('./csrfUtils');

      // Add CSRF token to headers
      addCsrfToken(headers);

      // Try to extract schema name from the URL
      const urlSchemaName = extractSchemaFromUrl(url);
      if (urlSchemaName) {
        console.log(`Extracted schema name from URL: ${urlSchemaName}`);

        // Update the schema name in localStorage
        setCurrentSchema(urlSchemaName);

        // Format the schema name for the header (preserving spaces)
        const formattedSchema = formatSchemaForHeader(urlSchemaName);

        // Update the header
        headers.set('X-Schema-Name', formattedSchema);

        // Also add the schema name as a cookie for backend compatibility
        document.cookie = `schema_name=${encodeURIComponent(formattedSchema)}; path=/`;

        console.log('Retrying request with updated schema name');
        console.log(`Retry headers: ${JSON.stringify(Object.fromEntries(headers.entries()), null, 2)}`);

        // Retry the request
        return fetch(url, {
          ...options,
          headers,
          credentials: 'include'
        });
      }
    }

    // If refresh failed and schema extraction didn't help, let the caller handle the error
  }

  return response;
};

export default {
  refreshToken,
  needsTokenRefresh,
  initializeTokenRefresh,
  getValidToken,
  handleAuthError,
  fetchWithAuth
};
