/**
 * Storage utility functions
 *
 * This module provides a consistent interface for working with browser storage.
 * It uses localStorage for all storage operations.
 */

/**
 * Get an item from storage
 * @param key The key to get
 * @returns The value or null if not found
 */
export const getStorageItem = (key: string): string | null => {
  // Get from localStorage
  return localStorage.getItem(key);
};

/**
 * Set an item in storage
 * @param key The key to set
 * @param value The value to set
 */
export const setStorageItem = (key: string, value: string): void => {
  // Store in localStorage
  localStorage.setItem(key, value);
};

/**
 * Remove an item from storage
 * @param key The key to remove
 */
export const removeStorageItem = (key: string): void => {
  // Remove from localStorage
  localStorage.removeItem(key);
};

/**
 * Clear all items from storage
 */
export const clearStorage = (): void => {
  // Clear localStorage
  localStorage.clear();
};

/**
 * Get an object from storage
 * @param key The key to get
 * @returns The parsed object or null if not found or invalid
 */
export const getStorageObject = <T>(key: string): T | null => {
  const value = getStorageItem(key);

  if (!value) {
    return null;
  }

  try {
    return JSON.parse(value) as T;
  } catch (error) {
    console.error(`Error parsing storage object for key ${key}:`, error);
    return null;
  }
};

/**
 * Set an object in storage
 * @param key The key to set
 * @param value The object to set
 */
export const setStorageObject = <T>(key: string, value: T): void => {
  try {
    const stringValue = JSON.stringify(value);
    setStorageItem(key, stringValue);
  } catch (error) {
    console.error(`Error stringifying object for key ${key}:`, error);
  }
};

export default {
  getStorageItem,
  setStorageItem,
  removeStorageItem,
  clearStorage,
  getStorageObject,
  setStorageObject,
};
