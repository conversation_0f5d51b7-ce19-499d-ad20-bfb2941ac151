import axios, { AxiosRequestConfig, AxiosResponse, AxiosError } from 'axios';
import { getAuthHeaders, getCurrentSchema, refreshJWTTokens } from './tokenService';

// Create an Axios instance that directly connects to the backend server
// This bypasses the Vite proxy completely
const directApi = axios.create({
  baseURL: 'http://localhost:8000',  // Direct connection to backend
  timeout: 10000,
  withCredentials: true,
});

// Request interceptor to add auth headers
directApi.interceptors.request.use(
  (config) => {
    // Get the current schema from token service
    const schema = getCurrentSchema() || '';

    // Get authentication headers
    const headers = getAuthHeaders(schema);

    // Add headers to the request
    Object.assign(config.headers, headers);

    console.log('Direct API Request:', {
      url: config.url,
      method: config.method,
      headers: {
        'Content-Type': config.headers['Content-Type'],
        'Authorization': config.headers['Authorization'] ? 'Token [redacted]' : 'None',
        'X-Schema-Name': config.headers['X-Schema-Name'] || 'None',
      },
    });

    return config;
  },
  (error) => {
    console.error('Direct API Request Error:', error);
    return Promise.reject(error);
  }
);

// Response interceptor to handle errors
directApi.interceptors.response.use(
  (response) => {
    console.log('Direct API Response:', {
      status: response.status,
      url: response.config.url,
      method: response.config.method,
    });
    return response;
  },
  async (error: AxiosError) => {
    console.error('Direct API Response Error:', {
      status: error.response?.status,
      url: error.config?.url,
      method: error.config?.method,
      message: error.message,
    });

    // Handle 401 Unauthorized errors
    if (error.response?.status === 401) {
      console.log('401 Unauthorized error, trying to refresh token');

      // Try to refresh the token
      try {
        // Get the current schema from token service
        const schema = getCurrentSchema() || '';

        const refreshResult = await refreshJWTTokens(schema);

        if (refreshResult && refreshResult.access_token) {
          console.log('Token refreshed successfully, retrying request');

          // Retry the original request with the new token
          const originalRequest = error.config;
          if (originalRequest) {
            // Get new auth headers
            const headers = getAuthHeaders(schema);

            // Update the Authorization header
            Object.assign(originalRequest.headers, headers);

            // Retry the request
            return directApi(originalRequest);
          }
        }
      } catch (refreshError) {
        console.error('Error refreshing token:', refreshError);
      }
    }

    return Promise.reject(error);
  }
);

// Helper function to make API requests to tenant-specific endpoints
export const directTenantApi = {
  // Get data from a tenant-specific endpoint
  get: async <T>(tenantSchema: string, endpoint: string, config?: AxiosRequestConfig): Promise<T> => {
    try {
      // Use the direct endpoint format that matches the backend URL structure
      const url = `/api/${endpoint}`;
      console.log(`Making direct GET request to endpoint: ${url} with schema: ${tenantSchema}`);

      // Create a new config with the schema name in the headers
      const requestConfig: AxiosRequestConfig = {
        ...config,
        headers: {
          ...config?.headers,
          'X-Schema-Name': tenantSchema
        }
      };

      const response = await directApi.get<T>(url, requestConfig);
      return response.data;
    } catch (error) {
      console.error(`Error getting data from endpoint ${endpoint}:`, error);

      // Try tenant-specific endpoint format as fallback
      try {
        const tenantUrl = `/api/tenant/${tenantSchema}/${endpoint}`;
        console.log(`Trying tenant-specific endpoint format: ${tenantUrl}`);
        const altResponse = await directApi.get<T>(tenantUrl, config);
        return altResponse.data;
      } catch (altError) {
        console.error(`Error with tenant-specific endpoint format:`, altError);
        throw error; // Throw the original error
      }
    }
  },

  // Post data to a tenant-specific endpoint
  post: async <T>(tenantSchema: string, endpoint: string, data: any, config?: AxiosRequestConfig): Promise<T> => {
    try {
      // Use the direct endpoint format that matches the backend URL structure
      const url = `/api/${endpoint}`;
      console.log(`Making direct POST request to endpoint: ${url} with schema: ${tenantSchema}`);

      // Create a new config with the schema name in the headers
      const requestConfig: AxiosRequestConfig = {
        ...config,
        headers: {
          ...config?.headers,
          'X-Schema-Name': tenantSchema
        }
      };

      const response = await directApi.post<T>(url, data, requestConfig);
      return response.data;
    } catch (error) {
      console.error(`Error posting data to endpoint ${endpoint}:`, error);

      // Try tenant-specific endpoint format as fallback
      try {
        const tenantUrl = `/api/tenant/${tenantSchema}/${endpoint}`;
        console.log(`Trying tenant-specific endpoint format: ${tenantUrl}`);
        const altResponse = await directApi.post<T>(tenantUrl, data, config);
        return altResponse.data;
      } catch (altError) {
        console.error(`Error with tenant-specific endpoint format:`, altError);
        throw error; // Throw the original error
      }
    }
  },

  // Put data to a tenant-specific endpoint
  put: async <T>(tenantSchema: string, endpoint: string, data: any, config?: AxiosRequestConfig): Promise<T> => {
    try {
      // Use the direct endpoint format that matches the backend URL structure
      const url = `/api/${endpoint}`;
      console.log(`Making direct PUT request to endpoint: ${url} with schema: ${tenantSchema}`);

      // Create a new config with the schema name in the headers
      const requestConfig: AxiosRequestConfig = {
        ...config,
        headers: {
          ...config?.headers,
          'X-Schema-Name': tenantSchema
        }
      };

      const response = await directApi.put<T>(url, data, requestConfig);
      return response.data;
    } catch (error) {
      console.error(`Error putting data to endpoint ${endpoint}:`, error);

      // Try tenant-specific endpoint format as fallback
      try {
        const tenantUrl = `/api/tenant/${tenantSchema}/${endpoint}`;
        console.log(`Trying tenant-specific endpoint format: ${tenantUrl}`);
        const altResponse = await directApi.put<T>(tenantUrl, data, config);
        return altResponse.data;
      } catch (altError) {
        console.error(`Error with tenant-specific endpoint format:`, altError);
        throw error; // Throw the original error
      }
    }
  },

  // Delete data from a tenant-specific endpoint
  delete: async <T>(tenantSchema: string, endpoint: string, config?: AxiosRequestConfig): Promise<T> => {
    try {
      // Use the direct endpoint format that matches the backend URL structure
      const url = `/api/${endpoint}`;
      console.log(`Making direct DELETE request to endpoint: ${url} with schema: ${tenantSchema}`);

      // Create a new config with the schema name in the headers
      const requestConfig: AxiosRequestConfig = {
        ...config,
        headers: {
          ...config?.headers,
          'X-Schema-Name': tenantSchema
        }
      };

      const response = await directApi.delete<T>(url, requestConfig);
      return response.data;
    } catch (error) {
      console.error(`Error deleting data from endpoint ${endpoint}:`, error);

      // Try tenant-specific endpoint format as fallback
      try {
        const tenantUrl = `/api/tenant/${tenantSchema}/${endpoint}`;
        console.log(`Trying tenant-specific endpoint format: ${tenantUrl}`);
        const altResponse = await directApi.delete<T>(tenantUrl, config);
        return altResponse.data;
      } catch (altError) {
        console.error(`Error with tenant-specific endpoint format:`, altError);
        throw error; // Throw the original error
      }
    }
  },
};

// Helper function to check if the API is accessible
export const checkDirectApiConnection = async (): Promise<boolean> => {
  try {
    console.log('Checking direct API connection...');
    // Try to access the health check endpoint
    const response = await directApi.get('/api/health/', {
      timeout: 5000, // Short timeout for quick feedback
    });
    console.log('Direct API connection check result:', response.status, response.data);
    return response.status === 200 && response.data?.status === 'ok';
  } catch (error) {
    console.error('Direct API connection check failed:', error);

    // Try alternative endpoint as fallback
    try {
      console.log('Trying alternative health check endpoint...');
      const altResponse = await directApi.get('/api/debug-cors/', {
        timeout: 5000,
      });
      console.log('Alternative direct API connection check result:', altResponse.status);
      return altResponse.status === 200;
    } catch (altError) {
      console.error('Alternative direct API connection check failed:', altError);
      return false;
    }
  }
};

export default directApi;
