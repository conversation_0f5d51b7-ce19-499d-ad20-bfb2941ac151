"""
Script to check if a JWT token is valid and which user it belongs to.
"""
import os
import sys
import django

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'neocamelot.settings')
django.setup()

from django.db import connection
from centers.models import Client
from django_tenants.utils import tenant_context
from accounts.jwt_utils import validate_jwt_token
from django.contrib.auth import get_user_model

User = get_user_model()

def check_token(token_key):
    """Check if a JWT token is valid and which user it belongs to."""
    print(f"Checking JWT token: {token_key}")

    # Validate the JWT token
    payload = validate_jwt_token(token_key)

    if not payload:
        print(f"Invalid JWT token")
        return False

    print(f"JWT token is valid")
    print(f"Payload: {payload}")

    # Get user ID and schema from payload
    user_id = payload.get('sub')
    schema_name = payload.get('schema')

    if not user_id or not schema_name:
        print(f"Invalid token payload - missing user ID or schema")
        return False

    print(f"User ID: {user_id}")
    print(f"Schema: {schema_name}")

    # Get the tenant by schema name
    try:
        tenant = Client.objects.get(schema_name=schema_name)
        print(f"Found tenant: {tenant.name} (Schema: {tenant.schema_name})")

        # Set the tenant for this request
        connection.set_tenant(tenant)

        # Get the user
        with tenant_context(tenant):
            try:
                user = User.objects.get(id=user_id)
                print(f"Found user: {user.email} (ID: {user.id})")
                print(f"User details: {user.get_full_name()} (Superuser: {user.is_superuser}, Staff: {user.is_staff})")
                return True
            except User.DoesNotExist:
                print(f"User with ID {user_id} not found in tenant {schema_name}")
                return False
    except Client.DoesNotExist:
        print(f"Tenant with schema {schema_name} not found")
        return False

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("Usage: python check_token.py <jwt_token>")
        sys.exit(1)

    token_key = sys.argv[1]
    found = check_token(token_key)

    if not found:
        print(f"JWT token is not valid")
        sys.exit(1)
    else:
        print(f"JWT token is valid")
        sys.exit(0)
