from django.db import models
from centers.models_region import Timestamp
from .models_base import citizen_directory_path

class Biometric(Timestamp):
    """Model representing biometric data for a citizen."""
    citizen = models.OneToOneField('citizens.Citizen', on_delete=models.CASCADE, related_name='biometric')
    left_hand_fingerprint = models.BinaryField(blank=True, null=True)  # Store encoded left hand fingerprint
    right_hand_fingerprint = models.BinaryField(blank=True, null=True)  # Store encoded right hand fingerprint
    left_thumb_fingerprint = models.BinaryField(blank=True, null=True)  # Store encoded left thumb fingerprint
    right_thumb_fingerprint = models.BinaryField(blank=True, null=True)  # Store encoded right thumb fingerprint
    left_eye_iris_scan = models.BinaryField(blank=True, null=True)  # Store encoded left eye iris scan
    right_eye_iris_scan = models.BinaryField(blank=True, null=True)  # Store encoded right eye iris scan

    def __str__(self):
        return f"Biometric data for {self.citizen.first_name} {self.citizen.last_name}"

    class Meta:
        verbose_name = "Biometric"
        verbose_name_plural = "Biometrics"


class Photo(Timestamp):
    """Model representing a photo of a citizen."""
    citizen = models.OneToOneField('citizens.Citizen', on_delete=models.CASCADE, related_name='photo_obj')
    photo = models.ImageField(upload_to=citizen_directory_path)
    upload_date = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"Photo of {self.citizen.first_name} {self.citizen.last_name}"

    class Meta:
        verbose_name = "Photo"
        verbose_name_plural = "Photos"
