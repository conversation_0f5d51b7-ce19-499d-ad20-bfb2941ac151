import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Paper,
  Typography,
  TextField,
  Button,
  Grid,
  MenuItem,
  Divider,
  Alert,
  AlertTitle,
  CircularProgress,
  Card,
  CardContent,
  FormControl,
  InputLabel,
  Select,
  FormHelperText,
  Snackbar,
  IconButton,
  InputAdornment,
  Avatar
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { useNavigate, useLocation } from 'react-router-dom';
import { format, addYears } from 'date-fns';
import UnauthorizedAccess from '../components/tenant/UnauthorizedAccess';
import PageBanner from '../components/PageBanner';

// Icons
import CreditCardIcon from '@mui/icons-material/CreditCard';
import PersonIcon from '@mui/icons-material/Person';
import CalendarTodayIcon from '@mui/icons-material/CalendarToday';
import TemplateIcon from '@mui/icons-material/ViewCompact';
import SaveIcon from '@mui/icons-material/Save';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import SearchIcon from '@mui/icons-material/Search';
import BadgeIcon from '@mui/icons-material/Badge';

const IDCardRegistration: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();

  // Get citizen ID from URL query parameter if available
  const queryParams = new URLSearchParams(location.search);
  const citizenIdFromUrl = queryParams.get('citizen');

  // State for form data
  const [formData, setFormData] = useState({
    citizen: '',
    template: '',
    issueDate: new Date(),
    expiryDate: addYears(new Date(), 5), // Default to 5 years from now
    status: 'DRAFT',
    cardData: {}
  });

  // State for loading citizens and templates
  const [citizens, setCitizens] = useState<any[]>([]);
  const [templates, setTemplates] = useState<any[]>([]);
  const [loadingCitizens, setLoadingCitizens] = useState(false);
  const [loadingTemplates, setLoadingTemplates] = useState(false);

  // Track overall loading state
  const [initialLoading, setInitialLoading] = useState(true);

  // State for form submission
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitSuccess, setSubmitSuccess] = useState(false);
  const [submitError, setSubmitError] = useState('');
  const [snackbarOpen, setSnackbarOpen] = useState(false);

  // State for citizen search
  const [citizenSearchQuery, setCitizenSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<any[]>([]);
  const [searching, setSearching] = useState(false);

  // Get the current tenant and authentication info from localStorage
  const tenantString = localStorage.getItem('tenant');
  const tenant = tenantString ? JSON.parse(tenantString) : null;
  const schemaName = localStorage.getItem('schema_name');

  // Get JWT token from localStorage
  const token = localStorage.getItem(`jwt_access_token_${schemaName}`) ||
                localStorage.getItem('jwt_access_token') ||
                localStorage.getItem('token');

  // Redirect to login if not authenticated
  useEffect(() => {
    if (!token) {
      navigate('/login');
    }
  }, [token, navigate]);

  // Check if tenant type is allowed to access this page
  // Temporarily allow all tenant types to access this page
  const isTenantAuthorized = true; // Bypass the tenant type check

  // Log tenant information for debugging
  console.log('IDCardRegistration - Tenant information:', tenant);
  console.log('IDCardRegistration - Tenant type:', tenant?.type);
  console.log('IDCardRegistration - Tenant schema_type:', tenant?.schema_type);

  // Fetch citizens and templates on component mount
  useEffect(() => {
    if (token) {
      fetchCitizens();
      fetchTemplates();
    }
  }, [token]);

  // Set citizen ID from URL after citizens are loaded
  useEffect(() => {
    if (citizenIdFromUrl && citizens.length > 0) {
      // Check if the citizen ID exists in the loaded citizens
      const citizenExists = citizens.some(citizen => citizen.id.toString() === citizenIdFromUrl);
      if (citizenExists) {
        setFormData(prev => ({ ...prev, citizen: citizenIdFromUrl }));
      } else {
        console.warn(`Citizen with ID ${citizenIdFromUrl} not found in loaded citizens`);
      }
    }
  }, [citizenIdFromUrl, citizens]);

  // Fetch citizens from the API
  const fetchCitizens = async () => {
    if (!token) {
      setSubmitError('Authentication token not found. Please log in again.');
      return;
    }

    setLoadingCitizens(true);
    try {
      // Use the tenant-specific API endpoint
      let schema = schemaName || tenant?.schema_name || '';
      // Do NOT replace spaces with underscores in schema name
      console.log('Using schema name:', schema);
      const url = `http://localhost:8000/api/tenant/${encodeURIComponent(schema)}/citizens/`;
      console.log('Fetching citizens with URL:', url);

      console.log('Using JWT token for citizens fetch');

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Authorization': token ? `Bearer ${token}` : '',
          'Content-Type': 'application/json'
        },
        credentials: 'include'
      });

      console.log('Response status:', response.status);
      console.log('Response headers:', response.headers);

      if (!response.ok) {
        // Handle 404 Not Found as an empty array
        if (response.status === 404) {
          console.log('No citizens found (404). This is normal for a new tenant.');
          setCitizens([]);
          return;
        }

        // Handle 401 Unauthorized as an empty array
        if (response.status === 401) {
          console.log('Authentication error (401). This is normal if there are no citizens yet.');
          setCitizens([]);
          return;
        }

        throw new Error('Failed to fetch citizens');
      }

      const data = await response.json();
      setCitizens(data);
    } catch (error: any) {
      console.error('Error fetching citizens:', error);

      // Check if the error is a 401 Unauthorized or 404 Not Found error
      if (error.message && (error.message.includes('401') || error.message.includes('404'))) {
        console.log('Authentication error or no citizens found. This is normal for a new tenant.');
        setCitizens([]);
      } else {
        setSubmitError(error.message || 'Failed to load citizens');
      }
    } finally {
      setLoadingCitizens(false);
      // Check if both loading states are complete
      if (!loadingTemplates) {
        setInitialLoading(false);
      }
    }
  };

  // Fetch ID card templates from the API
  const fetchTemplates = async () => {
    if (!token) {
      setSubmitError('Authentication token not found. Please log in again.');
      return;
    }

    setLoadingTemplates(true);
    try {
      // Use the tenant-specific API endpoint
      let schema = schemaName || tenant?.schema_name || '';
      // Do NOT replace spaces with underscores in schema name
      console.log('Using schema name:', schema);
      const url = `http://localhost:8000/api/tenant/${encodeURIComponent(schema)}/idcard-templates/`;
      console.log('Fetching ID card templates with URL:', url);

      console.log('Using JWT token for templates fetch');

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Authorization': token ? `Bearer ${token}` : '',
          'Content-Type': 'application/json'
        },
        credentials: 'include'
      });

      console.log('Templates response status:', response.status);
      console.log('Templates response headers:', response.headers);

      if (!response.ok) {
        // Handle 404 Not Found as an empty array
        if (response.status === 404) {
          console.log('No ID card templates found (404). This is normal for a new tenant.');
          setTemplates([]);
          return;
        }

        // Handle 401 Unauthorized as an empty array
        if (response.status === 401) {
          console.log('Authentication error (401). This is normal if there are no ID card templates yet.');
          setTemplates([]);
          return;
        }

        // Handle 500 Internal Server Error as an empty array
        if (response.status === 500) {
          console.log('Server error (500) when fetching ID card templates. This might be due to backend configuration issues.');
          setTemplates([]);
          return;
        }

        throw new Error('Failed to fetch ID card templates');
      }

      const data = await response.json();
      setTemplates(data);

      // If templates are available, set the default template
      if (data.length > 0) {
        const defaultTemplate = data.find((t: any) => t.is_default) || data[0];
        setFormData(prev => ({ ...prev, template: defaultTemplate.id }));
      }
    } catch (error: any) {
      console.error('Error fetching templates:', error);

      // Check if the error is a 401 Unauthorized, 404 Not Found, or 500 Internal Server Error
      if (error.message && (error.message.includes('401') || error.message.includes('404') || error.message.includes('500'))) {
        console.log('Authentication error, server error, or no templates found. This is normal for a new tenant.');
        setTemplates([]);
      } else {
        // Don't show error to user, just log it and set empty templates
        console.log('Unhandled error fetching templates:', error.message);
        setTemplates([]);
      }
    } finally {
      setLoadingTemplates(false);
      // Check if both loading states are complete
      if (!loadingCitizens) {
        setInitialLoading(false);
      }
    }
  };

  // Search for citizens
  const searchCitizens = async () => {
    if (!token || !citizenSearchQuery.trim()) {
      return;
    }

    setSearching(true);
    try {
      // Use the tenant-specific API endpoint
      let schema = schemaName || tenant?.schema_name || '';
      // Do NOT replace spaces with underscores in schema name
      console.log('Using schema name:', schema);
      const url = `http://localhost:8000/api/tenant/${encodeURIComponent(schema)}/citizens/?search=${encodeURIComponent(citizenSearchQuery)}`;
      console.log('Searching citizens with URL:', url);

      console.log('Using JWT token for search');

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Authorization': token ? `Bearer ${token}` : '',
          'Content-Type': 'application/json'
        },
        credentials: 'include'
      });

      console.log('Search response status:', response.status);
      console.log('Search response headers:', response.headers);

      if (!response.ok) {
        throw new Error('Failed to search citizens');
      }

      const data = await response.json();
      setSearchResults(data);
    } catch (error: any) {
      console.error('Error searching citizens:', error);
      setSubmitError(error.message || 'Failed to search citizens');
    } finally {
      setSearching(false);
    }
  };

  // Handle form field changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | { name?: string; value: unknown }>) => {
    const { name, value } = e.target;
    if (name) {
      setFormData(prev => ({ ...prev, [name]: value }));
    }
  };

  // Handle date changes
  const handleDateChange = (name: string, date: Date | null) => {
    if (date) {
      setFormData(prev => ({ ...prev, [name]: date }));
    }
  };

  // Handle citizen search input change
  const handleSearchInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setCitizenSearchQuery(e.target.value);
  };

  // Handle citizen selection from search results
  const handleCitizenSelect = (citizenId: string) => {
    setFormData(prev => ({ ...prev, citizen: citizenId }));
    setSearchResults([]);
    setCitizenSearchQuery('');
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate form
    if (!formData.citizen) {
      setSubmitError('Please select a citizen');
      return;
    }

    if (!formData.template) {
      setSubmitError('Please select an ID card template');
      return;
    }

    if (!token) {
      setSubmitError('Authentication token not found. Please log in again.');
      return;
    }

    setIsSubmitting(true);
    setSubmitError('');

    try {
      // Prepare the data for API call
      const idCardData = {
        citizen: formData.citizen,
        template: formData.template,
        issue_date: format(formData.issueDate, 'yyyy-MM-dd'),
        expiry_date: format(formData.expiryDate, 'yyyy-MM-dd'),
        status: formData.status,
        card_data: formData.cardData,
        // Add center field - this is required by the backend
        center: 22,  // Hardcoded center ID based on check_center.py output
        // Add a temporary card number to avoid unique constraint violation
        card_number: `TEMP-${Date.now()}-${Math.floor(Math.random() * 10000)}`
      };

      // Make the API call
      // Use the tenant-specific API endpoint
      let schema = schemaName || tenant?.schema_name || '';
      // Do NOT replace spaces with underscores in schema name
      console.log('Using schema name:', schema);
      const url = `http://localhost:8000/api/tenant/${encodeURIComponent(schema)}/idcards/`;
      console.log('Creating ID card with URL:', url);

      console.log('Using JWT token for submission');
      console.log('Submitting ID card data:', JSON.stringify(idCardData));

      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Authorization': token ? `Bearer ${token}` : '',
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(idCardData),
        credentials: 'include'
      });

      console.log('Submission response status:', response.status);
      console.log('Submission response headers:', response.headers);

      if (!response.ok) {
        // If API call fails, throw an error with the response data
        try {
          const errorData = await response.json();
          console.log('Error data:', errorData);
          throw new Error(errorData.detail || errorData.error || JSON.stringify(errorData) || 'Failed to register ID card');
        } catch (jsonError) {
          console.log('Error parsing JSON:', jsonError);
          throw new Error(`HTTP error ${response.status}: ${response.statusText}`);
        }
      }

      // Success
      const responseData = await response.json();
      setSubmitSuccess(true);
      setSnackbarOpen(true);

      // Reset form
      setFormData({
        citizen: '',
        template: templates.length > 0 ? templates[0].id : '',
        issueDate: new Date(),
        expiryDate: addYears(new Date(), 5),
        status: 'DRAFT',
        cardData: {}
      });
    } catch (error: any) {
      console.error('Error registering ID card:', error);
      setSubmitError(error.message || 'An error occurred during ID card registration. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle snackbar close
  const handleSnackbarClose = () => {
    setSnackbarOpen(false);
  };

  // Navigate back to ID cards list
  const handleBackToList = () => {
    navigate('/id-cards');
  };

  // Note: ID generation is now handled on the backend when creating the ID card

  // If tenant is not authorized, show unauthorized access component
  if (!isTenantAuthorized) {
    return (
      <UnauthorizedAccess
        message="Your tenant type does not have permission to register ID cards."
        tenantType={tenant?.type}
      />
    );
  }

  // Show loading indicator while initial data is being fetched
  if (initialLoading) {
    return (
      <Box sx={{
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'center',
        minHeight: '50vh',
        py: 4
      }}>
        <CircularProgress size={60} />
        <Typography variant="h6" sx={{ mt: 3, color: 'text.secondary' }}>
          Loading ID Card Registration...
        </Typography>
      </Box>
    );
  }

  return (
      <Box sx={{ py: 4 }}>
        {/* Banner */}
        <PageBanner
          title="Register New ID Card"
          subtitle={`Create a new ID card for a citizen in ${tenant?.name || 'your center'}`}
          icon={<CreditCardIcon sx={{ fontSize: 50, color: 'white' }} />}
          actionContent={
            <Box sx={{ textAlign: 'center', width: '100%' }}>
              <Typography variant="body1" sx={{ color: 'white', fontWeight: 500 }}>
                Fill out the form below to register a new ID card. The card will be created in draft status and can be submitted for approval later.
              </Typography>
            </Box>
          }
        />

        <Container maxWidth="xl">
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
            <Button
              startIcon={<ArrowBackIcon />}
              onClick={handleBackToList}
              variant="outlined"
              sx={{
                borderRadius: 2,
                px: 3,
                py: 1,
                boxShadow: '0 2px 8px rgba(0,0,0,0.05)',
                '&:hover': {
                  boxShadow: '0 4px 12px rgba(0,0,0,0.1)',
                  backgroundColor: 'rgba(63, 81, 181, 0.04)'
                }
              }}
            >
              Back to ID Cards
            </Button>

            <Typography variant="subtitle1" color="text.secondary">
              All fields marked with <Box component="span" sx={{ color: 'error.main' }}>*</Box> are required
            </Typography>
          </Box>

          <Paper
            elevation={0}
            sx={{
              p: 4,
              borderRadius: 3,
              boxShadow: '0 6px 18px rgba(0,0,0,0.1)',
              border: '1px solid rgba(0, 0, 0, 0.05)',
              bgcolor: '#FFFFFF',
              minHeight: 'calc(100vh - 300px)',
              display: 'flex',
              flexDirection: 'column',
              overflow: 'hidden'
            }}
          >
            {/* Error message */}
            {submitError && (
              <Alert severity="error" sx={{ mb: 3 }}>
                <AlertTitle>Error</AlertTitle>
                {submitError}
              </Alert>
            )}

            {/* Form */}
            <form onSubmit={handleSubmit} style={{ display: 'flex', flexDirection: 'column', flexGrow: 1 }}>
              <Box sx={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 4, flexGrow: 1 }}>
              <Card sx={{
                mb: 4,
                height: '100%',
                width: '100%',
                display: 'flex',
                flexDirection: 'column',
                transition: 'transform 0.3s, box-shadow 0.3s',
                '&:hover': {
                  transform: 'translateY(-8px)',
                  boxShadow: '0 12px 20px rgba(0, 0, 0, 0.15)',
                },
                borderRadius: 3,
                overflow: 'hidden',
                border: 'none',
                boxShadow: '0 6px 12px rgba(0, 0, 0, 0.08)',
                position: 'relative',
                '&::before': {
                  content: '""',
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  width: '100%',
                  height: '4px',
                  backgroundColor: 'primary.main',
                  zIndex: 1
                }
              }}>
                <Box sx={{
                  p: 3,
                  display: 'flex',
                  alignItems: 'center',
                  bgcolor: 'white',
                  color: 'text.primary',
                  borderBottom: '1px solid rgba(0, 0, 0, 0.05)'
                }}>
                  <Box
                    sx={{
                      mr: 2,
                      bgcolor: 'primary.main15', // 15% opacity of the color
                      color: 'primary.main',
                      p: 1.5,
                      borderRadius: 2,
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center'
                    }}
                  >
                    <PersonIcon />
                  </Box>
                  <Typography variant="h6" sx={{ fontWeight: 600 }}>
                    Citizen Information
                  </Typography>
                </Box>
                <CardContent sx={{ p: 3, flexGrow: 1 }}>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                    Select the citizen for whom you want to create an ID card.
                  </Typography>

                  <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
                    <Box>
                      <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                        Search for a citizen by name, ID, or registration number
                      </Typography>
                    <Box sx={{
                      display: 'flex',
                      alignItems: 'center',
                      bgcolor: 'white',
                      borderRadius: '10px',
                      overflow: 'hidden',
                      boxShadow: '0 2px 8px rgba(0,0,0,0.05)',
                      border: '1px solid rgba(0, 0, 0, 0.1)',
                      transition: 'all 0.3s',
                      '&:hover': {
                        boxShadow: '0 4px 12px rgba(0,0,0,0.1)',
                        borderColor: 'primary.main'
                      },
                      '&:focus-within': {
                        boxShadow: '0 0 0 3px rgba(63, 81, 181, 0.2)',
                        borderColor: 'primary.main'
                      }
                    }}>
                      <TextField
                        fullWidth
                        placeholder="Type to search..."
                        value={citizenSearchQuery}
                        onChange={handleSearchInputChange}
                        InputProps={{
                          sx: { height: '56px', borderRadius: 0, '& fieldset': { border: 'none' } },
                          startAdornment: (
                            <InputAdornment position="start">
                              <SearchIcon color="action" />
                            </InputAdornment>
                          )
                        }}
                      />
                      <Button
                        variant="contained"
                        onClick={searchCitizens}
                        disabled={searching || !citizenSearchQuery.trim()}
                        sx={{
                          borderRadius: '0 10px 10px 0',
                          boxShadow: 'none',
                          height: '56px',
                          px: 3,
                          fontWeight: 600,
                          transition: 'all 0.3s',
                          '&:hover': {
                            bgcolor: 'primary.dark'
                          }
                        }}
                      >
                        {searching ? <CircularProgress size={20} color="inherit" /> : 'Search'}
                      </Button>
                    </Box>

                    {/* Search Results */}
                    {searchResults.length > 0 && (
                      <Paper
                        elevation={3}
                        sx={{
                          mt: 1,
                          maxHeight: 250,
                          overflow: 'auto',
                          p: 0,
                          borderRadius: '10px',
                          border: '1px solid rgba(0, 0, 0, 0.05)',
                          boxShadow: '0 6px 16px rgba(0,0,0,0.1)',
                          animation: 'fadeIn 0.3s ease-out',
                          '@keyframes fadeIn': {
                            '0%': { opacity: 0, transform: 'translateY(10px)' },
                            '100%': { opacity: 1, transform: 'translateY(0)' }
                          }
                        }}
                      >
                        {searchResults.map((citizen) => (
                          <Box
                            key={citizen.id}
                            sx={{
                              p: 2,
                              cursor: 'pointer',
                              '&:hover': {
                                bgcolor: 'rgba(63, 81, 181, 0.08)',
                                transform: 'translateX(5px)'
                              },
                              borderBottom: '1px solid rgba(0, 0, 0, 0.05)',
                              transition: 'all 0.2s',
                              display: 'flex',
                              alignItems: 'center'
                            }}
                            onClick={() => handleCitizenSelect(citizen.id)}
                          >
                            <Avatar
                              src={citizen.photo}
                              alt={`${citizen.first_name} ${citizen.last_name}`}
                              sx={{
                                width: 40,
                                height: 40,
                                mr: 2,
                                bgcolor: 'primary.light',
                                boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
                                border: '2px solid white'
                              }}
                            >
                              {citizen.first_name?.[0]}
                            </Avatar>
                            <Box>
                              <Typography variant="body1" sx={{ fontWeight: 600 }}>
                                {citizen.first_name} {citizen.last_name}
                              </Typography>
                              <Typography variant="body2" color="text.secondary">
                                {citizen.registration_number}
                              </Typography>
                            </Box>
                          </Box>
                        ))}
                      </Paper>
                    )}
                    </Box>

                    <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 2 }}>
                      <FormControl
                        fullWidth
                        required
                      >
                      <InputLabel id="citizen-select-label">Citizen</InputLabel>
                      <Select
                        labelId="citizen-select-label"
                        id="citizen-select"
                        name="citizen"
                        value={citizens.length > 0 ? formData.citizen : ''}
                        onChange={handleChange}
                        label="Citizen"
                        disabled={loadingCitizens}
                        startAdornment={<InputAdornment position="start"><PersonIcon color="action" /></InputAdornment>}
                      >
                        {loadingCitizens ? (
                          <MenuItem disabled>Loading citizens...</MenuItem>
                        ) : citizens.length === 0 ? (
                          <MenuItem disabled>No citizens available</MenuItem>
                        ) : (
                          citizens.map((citizen) => (
                            <MenuItem
                              key={citizen.id}
                              value={citizen.id}
                            >
                              {citizen.first_name} {citizen.last_name} ({citizen.registration_number || citizen.id_number || 'No ID'})
                            </MenuItem>
                          ))
                        )}
                      </Select>
                      <FormHelperText>Select the citizen for whom this ID card is being created</FormHelperText>
                      </FormControl>
                    </Box>
                  </Box>
                </CardContent>
              </Card>

              <Card sx={{
                mb: 4,
                height: '100%',
                width: '100%',
                display: 'flex',
                flexDirection: 'column',
                transition: 'transform 0.3s, box-shadow 0.3s',
                '&:hover': {
                  transform: 'translateY(-8px)',
                  boxShadow: '0 12px 20px rgba(0, 0, 0, 0.15)',
                },
                borderRadius: 3,
                overflow: 'hidden',
                border: 'none',
                boxShadow: '0 6px 12px rgba(0, 0, 0, 0.08)',
                position: 'relative',
                '&::before': {
                  content: '""',
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  width: '100%',
                  height: '4px',
                  backgroundColor: 'secondary.main',
                  zIndex: 1
                }
              }}>
                <Box sx={{
                  p: 3,
                  display: 'flex',
                  alignItems: 'center',
                  bgcolor: 'white',
                  color: 'text.primary',
                  borderBottom: '1px solid rgba(0, 0, 0, 0.05)'
                }}>
                  <Box
                    sx={{
                      mr: 2,
                      bgcolor: 'secondary.main15', // 15% opacity of the color
                      color: 'secondary.main',
                      p: 1.5,
                      borderRadius: 2,
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center'
                    }}
                  >
                    <CreditCardIcon />
                  </Box>
                  <Typography variant="h6" sx={{ fontWeight: 600 }}>
                    ID Card Details
                  </Typography>
                </Box>
                <CardContent sx={{ p: 3, flexGrow: 1 }}>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                    Provide the details for the ID card.
                  </Typography>

                  <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
                    <FormControl
                      fullWidth
                      required
                    >
                      <InputLabel id="template-select-label">ID Card Template</InputLabel>
                      <Select
                        labelId="template-select-label"
                        id="template-select"
                        name="template"
                        value={templates.length > 0 ? formData.template : ''}
                        onChange={handleChange}
                        label="ID Card Template"
                        disabled={loadingTemplates}
                        startAdornment={<InputAdornment position="start"><CreditCardIcon color="action" /></InputAdornment>}
                      >
                        {loadingTemplates ? (
                          <MenuItem disabled>Loading templates...</MenuItem>
                        ) : templates.length === 0 ? (
                          <MenuItem disabled>No templates available</MenuItem>
                        ) : (
                          templates.map((template) => (
                            <MenuItem
                              key={template.id}
                              value={template.id}
                            >
                              {template.name} {template.is_default ? '(Default)' : ''}
                            </MenuItem>
                          ))
                        )}
                      </Select>
                      <FormHelperText>Select the template to use for this ID card</FormHelperText>
                    </FormControl>
                    <Box sx={{
                      display: 'grid',
                      gridTemplateColumns: '1fr 1fr',
                      gap: 3,
                      '& > *': { width: '100%' }
                    }}>
                      <DatePicker
                        label="Issue Date"
                        value={formData.issueDate}
                        onChange={(date) => handleDateChange('issueDate', date)}
                        slotProps={{
                          textField: {
                            fullWidth: true,
                            required: true,
                            helperText: 'Date when the ID card is issued',
                            InputProps: {
                              startAdornment: (
                                <InputAdornment position="start">
                                  <CalendarTodayIcon color="action" />
                                </InputAdornment>
                              ),
                            }
                          }
                        }}
                      />
                      <DatePicker
                        label="Expiry Date"
                        value={formData.expiryDate}
                        onChange={(date) => handleDateChange('expiryDate', date)}
                        slotProps={{
                          textField: {
                            fullWidth: true,
                            required: true,
                            helperText: 'Date when the ID card will expire',
                            InputProps: {
                              startAdornment: (
                                <InputAdornment position="start">
                                  <CalendarTodayIcon color="action" />
                                </InputAdornment>
                              ),
                            }
                          }
                        }}
                      />
                    </Box>
                  </Box>
                </CardContent>
              </Card>

              </Box>

              <Divider sx={{ my: 4 }} />

              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Button
                  variant="outlined"
                  onClick={handleBackToList}
                  startIcon={<ArrowBackIcon />}
                  sx={{
                    borderRadius: '10px',
                    px: 3,
                    py: 1.5,
                    fontWeight: 600,
                    transition: 'all 0.3s',
                    borderWidth: '2px',
                    '&:hover': {
                      backgroundColor: 'rgba(63, 81, 181, 0.04)',
                      borderWidth: '2px',
                      transform: 'translateY(-2px)',
                      boxShadow: '0 4px 8px rgba(0,0,0,0.05)'
                    }
                  }}
                >
                  Cancel
                </Button>

                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  {submitSuccess && (
                    <Paper
                      elevation={0}
                      sx={{
                        mr: 2,
                        px: 2,
                        py: 1,
                        bgcolor: 'success.light',
                        color: 'white',
                        borderRadius: '10px',
                        display: 'flex',
                        alignItems: 'center',
                        animation: 'fadeIn 0.5s ease-in-out',
                        '@keyframes fadeIn': {
                          '0%': { opacity: 0, transform: 'translateY(10px)' },
                          '100%': { opacity: 1, transform: 'translateY(0)' }
                        }
                      }}
                    >
                      <Box component="span" sx={{
                        display: 'inline-flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        bgcolor: 'white',
                        color: 'success.main',
                        borderRadius: '50%',
                        width: 24,
                        height: 24,
                        mr: 1,
                        fontSize: 16,
                        fontWeight: 'bold'
                      }}>✓</Box>
                      <Typography variant="body2" sx={{ fontWeight: 600 }}>
                        ID Card registered successfully!
                      </Typography>
                    </Paper>
                  )}

                  <Button
                    type="submit"
                    variant="contained"
                    color="primary"
                    startIcon={isSubmitting ? <CircularProgress size={20} color="inherit" /> : <SaveIcon />}
                    disabled={isSubmitting || !formData.citizen || !formData.template}
                    sx={{
                      borderRadius: '10px',
                      px: 4,
                      py: 1.5,
                      fontWeight: 600,
                      boxShadow: '0 4px 10px rgba(63, 81, 181, 0.2)',
                      transition: 'all 0.3s',
                      '&:hover': {
                        boxShadow: '0 6px 15px rgba(63, 81, 181, 0.3)',
                        transform: 'translateY(-2px)'
                      },
                      '&:active': {
                        transform: 'translateY(0)',
                        boxShadow: '0 2px 5px rgba(63, 81, 181, 0.2)'
                      },
                      '&.Mui-disabled': {
                        bgcolor: 'rgba(63, 81, 181, 0.7)'
                      }
                    }}
                  >
                    {isSubmitting ? 'Registering...' : 'Register ID Card'}
                  </Button>
                </Box>
              </Box>
            </form>
          </Paper>
        </Container>

        {/* Success Snackbar */}
        <Snackbar
          open={snackbarOpen}
          autoHideDuration={6000}
          onClose={handleSnackbarClose}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
          sx={{
            '& .MuiSnackbarContent-root': {
              bgcolor: 'success.dark',
              minWidth: '300px',
              borderRadius: 2,
              boxShadow: '0 4px 20px rgba(0,0,0,0.15)'
            }
          }}
        >
          <Alert
            severity="success"
            variant="filled"
            sx={{
              width: '100%',
              alignItems: 'center',
              '& .MuiAlert-icon': { fontSize: 24 }
            }}
            action={
              <Button
                color="inherit"
                size="small"
                variant="outlined"
                onClick={() => navigate('/id-cards')}
                sx={{
                  borderRadius: 1.5,
                  borderColor: 'rgba(255,255,255,0.5)',
                  '&:hover': { borderColor: 'white', bgcolor: 'rgba(255,255,255,0.1)' }
                }}
              >
                View ID Cards
              </Button>
            }
          >
            ID Card registered successfully
          </Alert>
        </Snackbar>
      </Box>
  );
};

export default IDCardRegistration;
