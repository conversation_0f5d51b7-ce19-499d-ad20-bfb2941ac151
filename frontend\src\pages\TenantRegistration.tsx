import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Paper,
  Typography,
  TextField,
  Button,
  Grid,
  FormControl,
  FormLabel,
  RadioGroup,
  FormControlLabel,
  Radio,
  MenuItem,
  Divider,
  Stepper,
  Step,
  StepLabel,
  Alert,
  AlertTitle,
  CircularProgress,
  Tooltip,
  IconButton,
  InputAdornment,
  Card,
  CardContent,
  useTheme,
  useMediaQuery,
  Chip,
  Link,
  Switch
} from '@mui/material';
import PageBanner from '../components/PageBanner';

// Icons
import HelpOutlineIcon from '@mui/icons-material/HelpOutline';
import BusinessIcon from '@mui/icons-material/Business';
import ApartmentIcon from '@mui/icons-material/Apartment';
import HomeWorkIcon from '@mui/icons-material/HomeWork';
import AdminPanelSettingsIcon from '@mui/icons-material/AdminPanelSettings';
import EmailIcon from '@mui/icons-material/Email';
import PhoneIcon from '@mui/icons-material/Phone';
import LocationOnIcon from '@mui/icons-material/LocationOn';
import BadgeIcon from '@mui/icons-material/Badge';
import CodeIcon from '@mui/icons-material/Code';
import PrintIcon from '@mui/icons-material/Print';
import PersonIcon from '@mui/icons-material/Person';
import VisibilityIcon from '@mui/icons-material/Visibility';
import VisibilityOffIcon from '@mui/icons-material/VisibilityOff';
import CheckCircleOutlineIcon from '@mui/icons-material/CheckCircleOutline';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import ArrowForwardIcon from '@mui/icons-material/ArrowForward';
import CheckIcon from '@mui/icons-material/Check';
import LocationCityIcon from '@mui/icons-material/LocationCity';
import InfoIcon from '@mui/icons-material/Info';
import LanguageIcon from '@mui/icons-material/Language';
import CloudUploadIcon from '@mui/icons-material/CloudUpload';
import MapIcon from '@mui/icons-material/Map';

// Tenant types
const TENANT_TYPES = [
  { value: 'city', label: 'City' },
  { value: 'subcity', label: 'Subcity' },
  { value: 'kebele', label: 'Kebele' }
];

// Steps for the registration process
const STEPS = ['Tenant Information', 'Administrator Account', 'Review & Submit'];

const TenantRegistration: React.FC = () => {
  // State for active step
  const [activeStep, setActiveStep] = useState(0);

  // State for form data
  const [formData, setFormData] = useState({
    // Tenant Information
    tenantType: 'kebele',
    tenantName: '',
    tenantCode: '',
    address: '',
    phone: '',
    email: '',
    parentTenant: '',
    customDomain: '',

    // City-specific fields
    cityCode: '',
    country: '',
    region: '',
    googleMapsUrl: '',
    mottoSlogan: '',
    cityIntro: '',
    mayorName: '',
    deputyMayor: '',
    establishedDate: '',
    areaSqKm: '',
    elevationMeters: '',
    population: '',
    website: '',
    logo: null as File | null,
    logoPreview: '',
    headerColor: '#3f51b5',
    accentColor: '#f50057',

    // Subcity-specific fields
    subcityCode: '',
    subcityDescription: '',
    subcityAdminName: '',

    // Administrator Account
    adminFirstName: '',
    adminLastName: '',
    adminEmail: '',
    adminPhone: '',
    adminUsername: '',
    adminPassword: '',
    adminConfirmPassword: '',
  });

  // State for form submission
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitSuccess, setSubmitSuccess] = useState(false);
  const [submitError, setSubmitError] = useState('');

  // State for available parent tenants
  const [availableParentTenants, setAvailableParentTenants] = useState<Array<{
    id: number;
    name: string;
    schema_name: string;
    schema_type: string;
  }>>([]);

  // State for loading parent tenants
  const [loadingParentTenants, setLoadingParentTenants] = useState(false);
  const [parentTenantsError, setParentTenantsError] = useState('');

  // State for regions
  const [regions, setRegions] = useState<Array<{
    id: number;
    name: string;
    code: string;
    country_id: number;
  }>>([]);
  const [loadingRegions, setLoadingRegions] = useState(false);
  const [regionsError, setRegionsError] = useState('');

  // Fetch regions when component mounts
  useEffect(() => {
    const fetchRegions = async () => {
      setLoadingRegions(true);
      setRegionsError('');

      try {
        const response = await fetch('http://localhost:8000/api/regions/');

        if (!response.ok) {
          throw new Error('Failed to fetch regions');
        }

        const data = await response.json();
        setRegions(data);
      } catch (error) {
        console.error('Error fetching regions:', error);
        setRegionsError('Failed to load regions. Please try again.');
      } finally {
        setLoadingRegions(false);
      }
    };

    fetchRegions();
  }, []);

  // Fetch available parent tenants when tenant type changes
  useEffect(() => {
    const fetchParentTenants = async () => {
      if (formData.tenantType === 'city') {
        // City doesn't need a parent tenant
        setAvailableParentTenants([]);
        setFormData(prev => ({ ...prev, parentTenant: '' })); // Clear parent tenant when city is selected
        return;
      }

      setLoadingParentTenants(true);
      setParentTenantsError('');
      // Clear parent tenant when tenant type changes
      setFormData(prev => ({ ...prev, parentTenant: '' }));

      try {
        // Determine the type of parent to fetch based on tenant type
        // For kebele, we need to use 'SUBCITY' as the parameter
        const parentType = formData.tenantType === 'subcity' ? 'CITY' : 'SUBCITY';
        console.log(`Requesting parent tenants with type=${parentType} for ${formData.tenantType}`);

        // Fetch parent tenants from the API
        const response = await fetch(`http://localhost:8000/api/available-tenants/?type=${parentType}`);

        if (!response.ok) {
          throw new Error('Failed to fetch parent tenants');
        }

        const data = await response.json();
        console.log(`Available parent tenants for ${formData.tenantType}:`, data);
        console.log('API Response Headers:', response.headers);
        console.log('API Response Status:', response.status);

        // Check if we have any parents
        if (data.length > 0) {
          // For kebele, we expect SUBCITY parents
          // For subcity, we expect CITY parents
          // This should match the parentType we're requesting from the API
          const expectedType = formData.tenantType === 'subcity' ? 'CITY' : 'SUBCITY';
          console.log(`Expected parent type for ${formData.tenantType}: ${expectedType}`);

          // Double-check that we only have the correct type of parents
          const validParents = data.filter(tenant =>
            tenant.schema_type.toUpperCase() === expectedType.toUpperCase()
          );

          console.log('Filtered parent tenants:', validParents);

          console.log(`Filtering for ${expectedType} parents:`);
          data.forEach(tenant => {
            console.log(`- ${tenant.name}: Type=${tenant.schema_type}, Expected=${expectedType}, Valid=${tenant.schema_type.toUpperCase() === expectedType.toUpperCase()}`);
          });

          if (validParents.length > 0) {
            setAvailableParentTenants(validParents);
            console.log('Valid parent tenants set:', validParents);
          } else {
            console.warn(`No valid ${expectedType} parents found`);
            setParentTenantsError(
              formData.tenantType === 'subcity'
                ? 'No city tenants found. Please create a city first.'
                : 'No subcity tenants found. Please create a subcity first.'
            );
            setAvailableParentTenants([]);
          }
        } else {
          console.warn(`No ${parentType} parents found`);
          setParentTenantsError(
            formData.tenantType === 'subcity'
              ? 'No city tenants found. Please create a city first.'
              : 'No subcity tenants found. Please create a subcity first.'
          );
          setAvailableParentTenants([]);
        }
      } catch (error) {
        console.error('Error fetching parent tenants:', error);
        setParentTenantsError('Failed to load parent tenants. Please try again.');
      } finally {
        setLoadingParentTenants(false);
      }
    };

    fetchParentTenants();
  }, [formData.tenantType]);

  // Handle form field changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;

    // Special handling for tenant type changes
    if (name === 'tenantType') {
      // Clear tenant code if not kebele
      if (value !== 'kebele') {
        setFormData(prev => ({
          ...prev,
          [name]: value,
          tenantCode: ''
        }));
        return;
      }
    }

    // Normal field update
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle logo file upload
  const handleLogoChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];

      // Create a preview URL for the image
      const reader = new FileReader();
      reader.onload = (event) => {
        if (event.target && event.target.result) {
          setFormData(prev => ({
            ...prev,
            logo: file,
            logoPreview: event.target.result as string
          }));
        }
      };
      reader.readAsDataURL(file);
    }
  };

  // Handle next step
  const handleNext = () => {
    setActiveStep(prevStep => prevStep + 1);
  };

  // Handle back step
  const handleBack = () => {
    setActiveStep(prevStep => prevStep - 1);
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Set submitting state
    setIsSubmitting(true);
    setSubmitError('');

    try {
      // Prepare the data for API call
      // Keep the email as is, we'll modify the username instead

      // Extract email domain from the email address
      let emailDomain = '';
      if (formData.email && formData.email.includes('@')) {
        emailDomain = formData.email.split('@')[1];
      }

      const tenantData = {
        // Use tenant code for kebele level, generate from name for city and subcity
        schema_name: formData.tenantType === 'kebele'
          ? formData.tenantCode.toLowerCase()
          : formData.tenantType + '_' + formData.tenantName.toLowerCase().replace(/\s+/g, '_'),
        name: formData.tenantName,
        schema_type: formData.tenantType.toUpperCase(),
        description: `${formData.tenantType} for ${formData.tenantName}`,
        parent_tenant: formData.parentTenant || null, // This will be the schema_name or ID of the parent tenant
        address: formData.address,
        phone: formData.phone,
        email: formData.email,
        email_domain: emailDomain, // Add email domain
        custom_domain: formData.customDomain || null, // Add custom domain for tenant detection
        admin_name: `${formData.adminFirstName} ${formData.adminLastName}`,
        admin_email: formData.adminEmail, // Use the original email as is
        admin_phone: formData.adminPhone,
        admin_username: formData.adminUsername,
        admin_password: formData.adminPassword,
        admin_first_name: formData.adminFirstName,
        admin_last_name: formData.adminLastName,
        is_active: true,

        // City-specific fields (only included for city tenant type)
        ...(formData.tenantType === 'city' && {
          city_code: formData.cityCode || formData.tenantName.substring(0, 3).toUpperCase(),
          mayor_name: formData.mayorName,
          deputy_mayor: formData.deputyMayor,
          motto_slogan: formData.mottoSlogan,
          city_intro: formData.cityIntro,
          established_date: formData.establishedDate,
          area_sq_km: formData.areaSqKm ? parseFloat(formData.areaSqKm) : null,
          elevation_meters: formData.elevationMeters ? parseFloat(formData.elevationMeters) : null,
          population: formData.population ? parseInt(formData.population) : null,
          website: formData.website,
          google_maps_url: formData.googleMapsUrl,
          region: formData.region,
          header_color: formData.headerColor,
          accent_color: formData.accentColor
        }),

        // Subcity-specific fields (only included for subcity tenant type)
        ...(formData.tenantType === 'subcity' && {
          subcity_code: formData.subcityCode || formData.tenantName.substring(0, 3).toUpperCase(),
          description: formData.subcityDescription,
          has_printing_facility: true,  // Always true since printing is done at subcity level
          printing_capacity: 100,  // Default printing capacity
          website: formData.website,
          header_color: formData.headerColor,
          accent_color: formData.accentColor
        })
      };

      // Create FormData for file upload if logo exists
      let apiResponse;

      if (formData.tenantType === 'city' && formData.logo) {
        // Use FormData to handle file upload
        const formDataObj = new FormData();

        // Add all tenant data as JSON
        formDataObj.append('tenant_data', JSON.stringify(tenantData));

        // Add the logo file
        formDataObj.append('logo', formData.logo);

        // Make API call with FormData
        apiResponse = await fetch('http://localhost:8000/api/register-tenant/', {
          method: 'POST',
          mode: 'cors',
          credentials: 'omit',  // Changed from 'include' to 'omit' to avoid CSRF issues
          body: formDataObj,
        });
      } else {
        // Make the regular API call without file upload
        apiResponse = await fetch('http://localhost:8000/api/register-tenant/', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
          },
          mode: 'cors',
          credentials: 'omit',  // Changed from 'include' to 'omit' to avoid CSRF issues
          body: JSON.stringify(tenantData),
        });
      }

      const response = apiResponse;

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to register tenant');
      }

      // Success
      setSubmitSuccess(true);
      setActiveStep(STEPS.length); // Move to completion step
    } catch (error: any) {
      // Handle error
      setSubmitError(error.message || 'An error occurred during registration. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Reset the form
  const handleReset = () => {
    setActiveStep(0);
    setFormData({
      tenantType: 'kebele',
      tenantName: '',
      tenantCode: '',
      address: '',
      phone: '',
      email: '',
      parentTenant: '',
      customDomain: '',

      // City-specific fields
      cityCode: '',
      country: '',
      region: '',
      googleMapsUrl: '',
      mottoSlogan: '',
      cityIntro: '',
      mayorName: '',
      deputyMayor: '',
      establishedDate: '',
      areaSqKm: '',
      elevationMeters: '',
      population: '',
      website: '',
      logo: null as File | null,
      logoPreview: '',
      headerColor: '#3f51b5',
      accentColor: '#f50057',

      // Subcity-specific fields
      subcityCode: '',
      subcityDescription: '',
      subcityAdminName: '',

      adminFirstName: '',
      adminLastName: '',
      adminEmail: '',
      adminPhone: '',
      adminUsername: '',
      adminPassword: '',
      adminConfirmPassword: '',
    });
    setSubmitSuccess(false);
    setSubmitError('');
  };

  // Validate current step
  const validateStep = () => {
    if (activeStep === 0) {
      // Validate tenant information
      const basicValidation = (
        formData.tenantName.trim() !== '' &&
        // Only require tenant code for kebele level
        (formData.tenantType !== 'kebele' || formData.tenantCode.trim() !== '') &&
        formData.address.trim() !== '' &&
        formData.phone.trim() !== '' &&
        formData.email.trim() !== '' &&
        (formData.tenantType === 'city' || formData.parentTenant.trim() !== '')
      );

      // Additional validation for city tenant type
      if (formData.tenantType === 'city') {
        return (
          basicValidation &&
          formData.cityCode.trim() !== ''
        );
      }

      // Additional validation for subcity tenant type
      if (formData.tenantType === 'subcity') {
        return (
          basicValidation &&
          formData.subcityCode.trim() !== ''
        );
      }

      return basicValidation;
    } else if (activeStep === 1) {
      // Validate administrator account
      return (
        formData.adminFirstName.trim() !== '' &&
        formData.adminLastName.trim() !== '' &&
        formData.adminEmail.trim() !== '' &&
        formData.adminPhone.trim() !== '' &&
        formData.adminUsername.trim() !== '' &&
        formData.adminPassword.trim() !== '' &&
        formData.adminPassword === formData.adminConfirmPassword
      );
    }

    return true;
  };

  // Render tenant information form
  const renderTenantInformationForm = () => (
    <Box component="form" sx={{ mt: 3 }}>
      {/* Tenant Type Selection Card */}
      <Card sx={{ mb: 4, borderRadius: 2, overflow: 'hidden' }}>
        <Box sx={{
          p: 2,
          bgcolor: 'primary.main',
          color: 'white',
          display: 'flex',
          alignItems: 'center'
        }}>
          <BusinessIcon sx={{ mr: 1 }} />
          <Typography variant="h6" sx={{ fontWeight: 600 }}>
            Select Tenant Type
          </Typography>
        </Box>
        <CardContent sx={{ p: 3 }}>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
            Choose the type of tenant you want to register in the system. This determines the hierarchy level and permissions.
          </Typography>

          <Box sx={{
            display: 'flex',
            flexWrap: 'wrap',
            gap: 2,
            justifyContent: 'center',
            mb: 2
          }}>
            {TENANT_TYPES.map(type => {
              const isSelected = formData.tenantType === type.value;
              let Icon;
              switch(type.value) {
                case 'city': Icon = LocationCityIcon; break;
                case 'subcity': Icon = ApartmentIcon; break;
                case 'kebele': Icon = HomeWorkIcon; break;
                default: Icon = BusinessIcon;
              }

              return (
                <Box
                  key={type.value}
                  onClick={() => {
                    setFormData(prev => ({
                      ...prev,
                      tenantType: type.value,
                      parentTenant: ''
                    }));
                  }}
                  sx={{
                    flex: { xs: '1 0 100%', sm: '1 0 30%' },
                    p: 2,
                    border: 2,
                    borderColor: isSelected ? 'primary.main' : 'divider',
                    borderRadius: 2,
                    bgcolor: isSelected ? 'primary.lighter' : 'background.paper',
                    cursor: 'pointer',
                    transition: 'all 0.2s',
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    justifyContent: 'center',
                    textAlign: 'center',
                    position: 'relative',
                    boxShadow: isSelected ? '0 4px 12px rgba(63, 81, 181, 0.2)' : 'none',
                    '&:hover': {
                      borderColor: 'primary.main',
                      boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
                      bgcolor: isSelected ? 'primary.lighter' : 'action.hover'
                    }
                  }}
                >
                  {isSelected && (
                    <Box sx={{
                      position: 'absolute',
                      top: 10,
                      right: 10,
                      bgcolor: 'primary.main',
                      color: 'white',
                      borderRadius: '50%',
                      width: 24,
                      height: 24,
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center'
                    }}>
                      <CheckIcon fontSize="small" />
                    </Box>
                  )}
                  <Icon
                    sx={{
                      fontSize: 48,
                      mb: 1,
                      color: isSelected ? 'primary.main' : 'text.secondary'
                    }}
                  />
                  <Typography
                    variant="subtitle1"
                    sx={{
                      fontWeight: isSelected ? 600 : 500,
                      color: isSelected ? 'primary.main' : 'text.primary'
                    }}
                  >
                    {type.label}
                  </Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ mt: 0.5 }}>
                    {type.value === 'city' ? 'Top level administration' :
                     type.value === 'subcity' ? 'Middle level administration' : 'Local level administration'}
                  </Typography>
                </Box>
              );
            })}
          </Box>
        </CardContent>
      </Card>

      {/* Tenant Details Card */}
      <Card sx={{ mb: 4, borderRadius: 2, overflow: 'hidden' }}>
        <Box sx={{
          p: 2,
          bgcolor: 'primary.main',
          color: 'white',
          display: 'flex',
          alignItems: 'center'
        }}>
          <BadgeIcon sx={{ mr: 1 }} />
          <Typography variant="h6" sx={{ fontWeight: 600 }}>
            Tenant Details
          </Typography>
        </Box>
        <CardContent sx={{ p: 3 }}>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
            {/* Row 1: Tenant Name and Code */}
            <Box sx={{
              display: 'grid',
              gridTemplateColumns: formData.tenantType === 'kebele' ? '1fr 1fr' : '1fr',
              gap: 3,
              '& > *': { width: '100%' }
            }}>
              <TextField
                required
                fullWidth
                label="Tenant Name"
                name="tenantName"
                value={formData.tenantName}
                onChange={handleChange}
                placeholder={`Enter ${formData.tenantType} name (e.g., Gondar, Maraki, Azezo 03)`}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <BadgeIcon color="action" />
                    </InputAdornment>
                  ),
                  endAdornment: (
                    <InputAdornment position="end">
                      <Tooltip title={`The official name of the ${formData.tenantType}`}>
                        <IconButton size="small" edge="end">
                          <HelpOutlineIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                    </InputAdornment>
                  )
                }}
              />

              {/* Only show tenant code field for kebele level */}
              {formData.tenantType === 'kebele' && (
                <TextField
                  required
                  fullWidth
                  label="Tenant Code (ID Card Prefix)"
                  name="tenantCode"
                  value={formData.tenantCode}
                  onChange={handleChange}
                  placeholder="Enter a unique code (e.g., AZ03, MK01)"
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <CodeIcon color="action" />
                      </InputAdornment>
                    ),
                    endAdornment: (
                      <InputAdornment position="end">
                        <Tooltip title="A unique identifier code for this kebele. Used as a prefix for ID card numbers.">
                          <IconButton size="small" edge="end">
                            <HelpOutlineIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                      </InputAdornment>
                    )
                  }}
                />
              )}
            </Box>

            {/* Row 2: Custom Domain */}
            <TextField
              fullWidth
              label="Custom Domain"
              name="customDomain"
              value={formData.customDomain}
              onChange={handleChange}
              placeholder="Enter a custom domain (e.g., gondar.gov.et)"
              helperText="Optional: Enter a custom domain for this tenant. This will be used for domain-based tenant detection."
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <LanguageIcon color="action" />
                  </InputAdornment>
                ),
                endAdornment: (
                  <InputAdornment position="end">
                    <Tooltip title="A custom domain for this tenant. Users with email addresses from this domain will be automatically directed to this tenant.">
                      <IconButton size="small" edge="end">
                        <HelpOutlineIcon fontSize="small" />
                      </IconButton>
                    </Tooltip>
                  </InputAdornment>
                )
              }}
            />

            {/* Row 3: Parent Tenant (conditional) */}
            {formData.tenantType !== 'city' && (
              <Box>
                <TextField
                  required
                  fullWidth
                  select
                  label="Parent Tenant"
                  name="parentTenant"
                  value={formData.parentTenant}
                  onChange={handleChange}
                  helperText={
                    formData.tenantType === 'subcity'
                      ? "Select the city this subcity belongs to. Subcities must have a city as parent."
                      : "Select the subcity this kebele belongs to. Kebeles must have a subcity as parent."
                  }
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        {formData.tenantType === 'subcity' ?
                          <BusinessIcon color="action" /> :
                          <ApartmentIcon color="action" />}
                      </InputAdornment>
                    )
                  }}
                  error={parentTenantsError !== ''}
                >
                  {loadingParentTenants ? (
                    <MenuItem disabled>Loading...</MenuItem>
                  ) : parentTenantsError ? (
                    <MenuItem disabled>Error loading parent tenants</MenuItem>
                  ) : availableParentTenants.length === 0 ? (
                    <MenuItem disabled>
                      {formData.tenantType === 'subcity'
                        ? "No cities available. Please create a city first."
                        : "No subcities available. Please create a subcity first."}
                    </MenuItem>
                  ) : (
                    availableParentTenants.map(tenant => (
                      <MenuItem key={tenant.id} value={tenant.schema_name}>
                        {tenant.name} {tenant.schema_type === 'CITY' ? '(City)' : tenant.schema_type === 'SUBCITY' ? '(Subcity)' : `(${tenant.schema_type})`}
                      </MenuItem>
                    ))
                  )}
                </TextField>
                {formData.tenantType === 'subcity' && (
                  <Typography variant="caption" color="primary" sx={{ display: 'block', mt: 1 }}>
                    <InfoIcon fontSize="small" sx={{ verticalAlign: 'middle', mr: 0.5 }} />
                    Subcities can only have cities as parents. Currently available cities: {availableParentTenants.length}
                  </Typography>
                )}
                {formData.tenantType === 'kebele' && (
                  <Typography variant="caption" color="primary" sx={{ display: 'block', mt: 1 }}>
                    <InfoIcon fontSize="small" sx={{ verticalAlign: 'middle', mr: 0.5 }} />
                    Kebeles can only have subcities as parents. Currently available subcities: {availableParentTenants.length}
                  </Typography>
                )}
                {parentTenantsError && (
                  <Typography variant="caption" color="error" sx={{ display: 'block', mt: 1 }}>
                    <InfoIcon fontSize="small" sx={{ verticalAlign: 'middle', mr: 0.5 }} />
                    {parentTenantsError}
                  </Typography>
                )}
                {formData.tenantType === 'subcity' && availableParentTenants.length === 0 && (
                  <Alert severity="info" sx={{ mt: 2 }}>
                    <AlertTitle>No Cities Available</AlertTitle>
                    You need to create a city first before you can create a subcity.
                    <br />
                    <Button
                      variant="outlined"
                      size="small"
                      sx={{ mt: 1 }}
                      onClick={() => {
                        setFormData(prev => ({ ...prev, tenantType: 'city' }));
                      }}
                    >
                      Switch to City Registration
                    </Button>
                  </Alert>
                )}
                {formData.tenantType === 'kebele' && availableParentTenants.length === 0 && (
                  <Alert severity="info" sx={{ mt: 2 }}>
                    <AlertTitle>No Subcities Available</AlertTitle>
                    You need to create a subcity first before you can create a kebele.
                    <br />
                    <Button
                      variant="outlined"
                      size="small"
                      sx={{ mt: 1 }}
                      onClick={() => {
                        setFormData(prev => ({ ...prev, tenantType: 'subcity' }));
                      }}
                    >
                      Switch to Subcity Registration
                    </Button>
                  </Alert>
                )}
              </Box>
            )}

            {/* City-specific fields (only shown when city is selected) */}
            {formData.tenantType === 'city' && (
              <Card sx={{ mb: 4, borderRadius: 2, overflow: 'hidden' }}>
                <Box sx={{
                  p: 2,
                  bgcolor: 'primary.main',
                  color: 'white',
                  display: 'flex',
                  alignItems: 'center'
                }}>
                  <LocationCityIcon sx={{ mr: 1 }} />
                  <Typography variant="h6" sx={{ fontWeight: 600 }}>
                    City Administration Details
                  </Typography>
                </Box>
                <CardContent sx={{ p: 3 }}>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                    Please provide additional information about the city administration.
                  </Typography>

                  <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
                    {/* Row 1: City Code */}
                    <TextField
                      required
                      fullWidth
                      label="City Code"
                      name="cityCode"
                      value={formData.cityCode}
                      onChange={handleChange}
                      placeholder="Enter a unique code for the city (e.g., GDR)"
                      inputProps={{ maxLength: 20 }}
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <CodeIcon color="action" />
                          </InputAdornment>
                        ),
                        endAdornment: (
                          <InputAdornment position="end">
                            <Tooltip title="A unique identifier code for this city. Used for administrative purposes.">
                              <IconButton size="small" edge="end">
                                <HelpOutlineIcon fontSize="small" />
                              </IconButton>
                            </Tooltip>
                          </InputAdornment>
                        )
                      }}
                    />

                    {/* Row 2: Region and Google Maps URL */}
                    <Box sx={{
                      display: 'grid',
                      gridTemplateColumns: '1fr 1fr',
                      gap: 3,
                      '& > *': { width: '100%' }
                    }}>
                      <TextField
                        fullWidth
                        select
                        label="Region"
                        name="region"
                        value={formData.region}
                        onChange={handleChange}
                        placeholder="Select the region"
                        InputProps={{
                          startAdornment: (
                            <InputAdornment position="start">
                              <LocationOnIcon color="action" />
                            </InputAdornment>
                          )
                        }}
                        helperText={regionsError || (loadingRegions ? "Loading regions..." : "Select the region where this city is located")}
                        error={!!regionsError}
                        disabled={loadingRegions}
                      >
                        {regions.length === 0 && !loadingRegions ? (
                          <MenuItem disabled value="">
                            No regions available
                          </MenuItem>
                        ) : (
                          regions.map((region) => (
                            <MenuItem key={region.id} value={region.id.toString()}>
                              {region.name}
                            </MenuItem>
                          ))
                        )}
                      </TextField>

                      <TextField
                        fullWidth
                        label="Google Maps URL"
                        name="googleMapsUrl"
                        value={formData.googleMapsUrl}
                        onChange={handleChange}
                        placeholder="Enter Google Maps URL for the city"
                        InputProps={{
                          startAdornment: (
                            <InputAdornment position="start">
                              <MapIcon color="action" />
                            </InputAdornment>
                          )
                        }}
                      />
                    </Box>

                    {/* Row 3: Mayor and Deputy Mayor */}
                    <Box sx={{
                      display: 'grid',
                      gridTemplateColumns: '1fr 1fr',
                      gap: 3,
                      '& > *': { width: '100%' }
                    }}>
                      <TextField
                        fullWidth
                        label="Mayor Name"
                        name="mayorName"
                        value={formData.mayorName}
                        onChange={handleChange}
                        placeholder="Enter the name of the mayor"
                        InputProps={{
                          startAdornment: (
                            <InputAdornment position="start">
                              <PersonIcon color="action" />
                            </InputAdornment>
                          )
                        }}
                      />

                      <TextField
                        fullWidth
                        label="Deputy Mayor Name"
                        name="deputyMayor"
                        value={formData.deputyMayor}
                        onChange={handleChange}
                        placeholder="Enter the name of the deputy mayor"
                        InputProps={{
                          startAdornment: (
                            <InputAdornment position="start">
                              <PersonIcon color="action" />
                            </InputAdornment>
                          )
                        }}
                      />
                    </Box>

                    {/* Row 3: Motto/Slogan */}
                    <TextField
                      fullWidth
                      label="City Motto/Slogan"
                      name="mottoSlogan"
                      value={formData.mottoSlogan}
                      onChange={handleChange}
                      placeholder="Enter the city's motto or slogan"
                      multiline
                      rows={2}
                    />

                    {/* Row 4: City Introduction */}
                    <TextField
                      fullWidth
                      label="City Introduction"
                      name="cityIntro"
                      value={formData.cityIntro}
                      onChange={handleChange}
                      placeholder="Enter a brief introduction about the city"
                      multiline
                      rows={3}
                    />

                    {/* Row 5: Established Date and Population */}
                    <Box sx={{
                      display: 'grid',
                      gridTemplateColumns: '1fr 1fr',
                      gap: 3,
                      '& > *': { width: '100%' }
                    }}>
                      <TextField
                        fullWidth
                        label="Established Date"
                        name="establishedDate"
                        type="date"
                        value={formData.establishedDate}
                        onChange={handleChange}
                        InputLabelProps={{ shrink: true }}
                      />

                      <TextField
                        fullWidth
                        label="Population"
                        name="population"
                        type="number"
                        value={formData.population}
                        onChange={handleChange}
                        placeholder="Enter the city's population"
                      />
                    </Box>

                    {/* Row 6: Area and Elevation */}
                    <Box sx={{
                      display: 'grid',
                      gridTemplateColumns: '1fr 1fr',
                      gap: 3,
                      '& > *': { width: '100%' }
                    }}>
                      <TextField
                        fullWidth
                        label="Area (sq km)"
                        name="areaSqKm"
                        type="number"
                        value={formData.areaSqKm}
                        onChange={handleChange}
                        placeholder="Enter the city's area in square kilometers"
                      />

                      <TextField
                        fullWidth
                        label="Elevation (meters)"
                        name="elevationMeters"
                        type="number"
                        value={formData.elevationMeters}
                        onChange={handleChange}
                        placeholder="Enter the city's elevation in meters"
                      />
                    </Box>

                    {/* Row 7: Website */}
                    <TextField
                      fullWidth
                      label="Website"
                      name="website"
                      value={formData.website}
                      onChange={handleChange}
                      placeholder="Enter the city's official website"
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <LanguageIcon color="action" />
                          </InputAdornment>
                        )
                      }}
                    />

                    {/* Row 8: Logo Upload */}
                    <Box sx={{ mb: 2 }}>
                      <Typography variant="subtitle2" gutterBottom>
                        City Logo
                      </Typography>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                        <Button
                          variant="outlined"
                          component="label"
                          startIcon={<CloudUploadIcon />}
                        >
                          Upload Logo
                          <input
                            type="file"
                            hidden
                            accept="image/*"
                            onChange={handleLogoChange}
                          />
                        </Button>
                        {formData.logoPreview && (
                          <Box sx={{
                            width: 100,
                            height: 100,
                            border: '1px solid #ddd',
                            borderRadius: 1,
                            overflow: 'hidden',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center'
                          }}>
                            <img
                              src={formData.logoPreview}
                              alt="City Logo Preview"
                              style={{ maxWidth: '100%', maxHeight: '100%' }}
                            />
                          </Box>
                        )}
                        {formData.logo && (
                          <Typography variant="caption" color="text.secondary">
                            {formData.logo.name} ({Math.round(formData.logo.size / 1024)} KB)
                          </Typography>
                        )}
                      </Box>
                      <Typography variant="caption" color="text.secondary">
                        Upload a logo for the city. This will be displayed on the login page and throughout the system.
                      </Typography>
                    </Box>

                    {/* Row 9: Theme Colors */}
                    <Box sx={{
                      display: 'grid',
                      gridTemplateColumns: '1fr 1fr',
                      gap: 3,
                      '& > *': { width: '100%' }
                    }}>
                      <TextField
                        fullWidth
                        label="Header Color"
                        name="headerColor"
                        type="color"
                        value={formData.headerColor}
                        onChange={handleChange}
                        InputLabelProps={{ shrink: true }}
                        helperText="Select a color for the city's header"
                      />

                      <TextField
                        fullWidth
                        label="Accent Color"
                        name="accentColor"
                        type="color"
                        value={formData.accentColor}
                        onChange={handleChange}
                        InputLabelProps={{ shrink: true }}
                        helperText="Select an accent color for the city"
                      />
                    </Box>
                  </Box>
                </CardContent>
              </Card>
            )}

            {/* Subcity-specific fields (only shown when subcity is selected) */}
            {formData.tenantType === 'subcity' && (
              <Card sx={{ mb: 4, borderRadius: 2, overflow: 'hidden' }}>
                <Box sx={{
                  p: 2,
                  bgcolor: 'primary.main',
                  color: 'white',
                  display: 'flex',
                  alignItems: 'center'
                }}>
                  <ApartmentIcon sx={{ mr: 1 }} />
                  <Typography variant="h6" sx={{ fontWeight: 600 }}>
                    Subcity Administration Details
                  </Typography>
                </Box>
                <CardContent sx={{ p: 3 }}>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                    Please provide additional information about the subcity administration.
                  </Typography>

                  <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
                    {/* Row 1: Subcity Code */}
                    <TextField
                      required
                      fullWidth
                      label="Subcity Code"
                      name="subcityCode"
                      value={formData.subcityCode}
                      onChange={handleChange}
                      placeholder="Enter a unique code for the subcity (e.g., AR01)"
                      inputProps={{ maxLength: 20 }}
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <CodeIcon color="action" />
                          </InputAdornment>
                        ),
                        endAdornment: (
                          <InputAdornment position="end">
                            <Tooltip title="A unique identifier code for this subcity. Used for administrative purposes.">
                              <IconButton size="small" edge="end">
                                <HelpOutlineIcon fontSize="small" />
                              </IconButton>
                            </Tooltip>
                          </InputAdornment>
                        )
                      }}
                    />

                    {/* Row 2: Description */}
                    <TextField
                      fullWidth
                      multiline
                      rows={3}
                      label="Description"
                      name="subcityDescription"
                      value={formData.subcityDescription}
                      onChange={handleChange}
                      placeholder="Enter a description for the subcity"
                    />

                    {/* Note about printing capabilities */}
                    <Box sx={{ mb: 2 }}>
                      <Typography variant="body2" color="text.secondary">
                        <InfoIcon fontSize="small" sx={{ verticalAlign: 'middle', mr: 0.5 }} />
                        Subcities are automatically configured with printing capabilities since ID card printing is done at the subcity level.
                      </Typography>
                    </Box>

                    {/* Row 4: Admin Name */}
                    <TextField
                      fullWidth
                      label="Administrator Name"
                      name="subcityAdminName"
                      value={formData.subcityAdminName}
                      onChange={handleChange}
                      placeholder="Enter the name of the subcity administrator"
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <PersonIcon color="action" />
                          </InputAdornment>
                        )
                      }}
                    />
                  </Box>
                </CardContent>
              </Card>
            )}

            {/* Row 3: Address */}
            <TextField
              required
              fullWidth
              label="Address"
              name="address"
              value={formData.address}
              onChange={handleChange}
              placeholder="Physical address"
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <LocationOnIcon color="action" />
                  </InputAdornment>
                )
              }}
            />

            {/* Row 4: Phone and Email */}
            <Box sx={{
              display: 'grid',
              gridTemplateColumns: '1fr 1fr',
              gap: 3,
              '& > *': { width: '100%' }
            }}>
              <TextField
                required
                fullWidth
                label="Phone Number"
                name="phone"
                value={formData.phone}
                onChange={handleChange}
                placeholder="Office phone number"
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <PhoneIcon color="action" />
                    </InputAdornment>
                  )
                }}
              />

              <TextField
                required
                fullWidth
                label="Email"
                name="email"
                type="email"
                value={formData.email}
                onChange={handleChange}
                placeholder="Official email address"
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <EmailIcon color="action" />
                    </InputAdornment>
                  )
                }}
              />
            </Box>

            {/* Row 5: Custom Domain */}
            <TextField
              fullWidth
              label="Custom Domain"
              name="customDomain"
              value={formData.customDomain}
              onChange={handleChange}
              placeholder="example.gov.et"
              helperText="Enter a domain name for automatic tenant detection during login (e.g., example.gov.et)"
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <LanguageIcon color="action" />
                  </InputAdornment>
                ),
                endAdornment: (
                  <InputAdornment position="end">
                    <Tooltip title="This domain will be used to automatically detect the tenant when users log in with an email from this domain.">
                      <IconButton size="small" edge="end">
                        <HelpOutlineIcon fontSize="small" />
                      </IconButton>
                    </Tooltip>
                  </InputAdornment>
                )
              }}
            />
          </Box>
        </CardContent>
      </Card>
    </Box>
  );

  // Render administrator account form
  const renderAdministratorForm = () => (
    <Box component="form" sx={{ mt: 3 }}>
      <Card sx={{ mb: 4, borderRadius: 2, overflow: 'hidden' }}>
        <Box sx={{
          p: 2,
          bgcolor: 'primary.main',
          color: 'white',
          display: 'flex',
          alignItems: 'center'
        }}>
          <AdminPanelSettingsIcon sx={{ mr: 1 }} />
          <Typography variant="h6" sx={{ fontWeight: 600 }}>
            Administrator Account Information
          </Typography>
        </Box>
        <CardContent sx={{ p: 3 }}>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
            This account will have full administrative privileges for this tenant.
          </Typography>

          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
            {/* Row 1: First Name and Last Name */}
            <Box sx={{
              display: 'grid',
              gridTemplateColumns: '1fr 1fr',
              gap: 3,
              '& > *': { width: '100%' }
            }}>
              <TextField
                required
                fullWidth
                label="First Name"
                name="adminFirstName"
                value={formData.adminFirstName}
                onChange={handleChange}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <PersonIcon color="action" />
                    </InputAdornment>
                  )
                }}
              />

              <TextField
                required
                fullWidth
                label="Last Name"
                name="adminLastName"
                value={formData.adminLastName}
                onChange={handleChange}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <PersonIcon color="action" />
                    </InputAdornment>
                  )
                }}
              />
            </Box>

            {/* Row 2: Email and Phone */}
            <Box sx={{
              display: 'grid',
              gridTemplateColumns: '1fr 1fr',
              gap: 3,
              '& > *': { width: '100%' }
            }}>
              <TextField
                required
                fullWidth
                label="Email"
                name="adminEmail"
                type="email"
                value={formData.adminEmail}
                onChange={handleChange}
                helperText="Enter the administrator's email address for communication"
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <EmailIcon color="action" />
                    </InputAdornment>
                  )
                }}
              />

              <TextField
                required
                fullWidth
                label="Phone Number"
                name="adminPhone"
                value={formData.adminPhone}
                onChange={handleChange}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <PhoneIcon color="action" />
                    </InputAdornment>
                  )
                }}
              />
            </Box>

            {/* Login Credentials Divider */}
            <Box sx={{ width: '100%', my: 1 }}>
              <Divider>
                <Chip
                  icon={<BadgeIcon />}
                  label="Login Credentials"
                  color="primary"
                  variant="outlined"
                />
              </Divider>
            </Box>

            {/* Row 3: Username */}
            <TextField
              required
              fullWidth
              label="Username"
              name="adminUsername"
              value={formData.adminUsername}
              onChange={handleChange}
              helperText={formData.customDomain ?
                `Domain will be appended for login: ${formData.adminUsername}@${formData.customDomain}` :
                "Enter the administrator's username for login"}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <BadgeIcon color="action" />
                  </InputAdornment>
                ),
                endAdornment: formData.customDomain ? (
                  <InputAdornment position="end">
                    <Tooltip title={`The domain ${formData.customDomain} will be appended to this username for login purposes`}>
                      <IconButton size="small" edge="end">
                        <HelpOutlineIcon fontSize="small" />
                      </IconButton>
                    </Tooltip>
                  </InputAdornment>
                ) : null
              }}
            />

            {/* Row 4: Password and Confirm Password */}
            <Box sx={{
              display: 'grid',
              gridTemplateColumns: '1fr 1fr',
              gap: 3,
              '& > *': { width: '100%' }
            }}>
              <TextField
                required
                fullWidth
                label="Password"
                name="adminPassword"
                type={showPassword ? 'text' : 'password'}
                value={formData.adminPassword}
                onChange={handleChange}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <BadgeIcon color="action" />
                    </InputAdornment>
                  ),
                  endAdornment: (
                    <InputAdornment position="end">
                      <IconButton
                        aria-label="toggle password visibility"
                        onClick={togglePasswordVisibility}
                        edge="end"
                      >
                        {showPassword ? <VisibilityOffIcon /> : <VisibilityIcon />}
                      </IconButton>
                    </InputAdornment>
                  )
                }}
              />

              <TextField
                required
                fullWidth
                label="Confirm Password"
                name="adminConfirmPassword"
                type={showConfirmPassword ? 'text' : 'password'}
                value={formData.adminConfirmPassword}
                onChange={handleChange}
                error={
                  formData.adminPassword !== formData.adminConfirmPassword &&
                  formData.adminConfirmPassword !== ''
                }
                helperText={
                  formData.adminPassword !== formData.adminConfirmPassword &&
                  formData.adminConfirmPassword !== ''
                    ? "Passwords don't match"
                    : ""
                }
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <BadgeIcon color="action" />
                    </InputAdornment>
                  ),
                  endAdornment: (
                    <InputAdornment position="end">
                      <IconButton
                        aria-label="toggle password visibility"
                        onClick={toggleConfirmPasswordVisibility}
                        edge="end"
                      >
                        {showConfirmPassword ? <VisibilityOffIcon /> : <VisibilityIcon />}
                      </IconButton>
                    </InputAdornment>
                  )
                }}
              />
            </Box>
          </Box>
        </CardContent>
      </Card>
    </Box>
  );

  // Render review form
  const renderReviewForm = () => (
    <Box sx={{ mt: 3 }}>
      <Card sx={{ mb: 4, borderRadius: 2, overflow: 'hidden' }}>
        <Box sx={{
          p: 2,
          bgcolor: 'primary.main',
          color: 'white',
          display: 'flex',
          alignItems: 'center'
        }}>
          <CheckCircleOutlineIcon sx={{ mr: 1 }} />
          <Typography variant="h6" sx={{ fontWeight: 600 }}>
            Review Your Information
          </Typography>
        </Box>
        <CardContent sx={{ p: 3 }}>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
            Please review the information below before submitting. Make sure all details are correct.
          </Typography>

          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 4 }}>
            {/* Tenant Details Section */}
            <Box>
              <Box sx={{
                display: 'flex',
                alignItems: 'center',
                mb: 2,
                pb: 1,
                borderBottom: '1px solid #e0e0e0'
              }}>
                <BusinessIcon color="primary" sx={{ mr: 1 }} />
                <Typography variant="subtitle1" sx={{ fontWeight: 600 }}>
                  Tenant Details
                </Typography>
              </Box>

              <Box sx={{
                display: 'grid',
                gridTemplateColumns: { xs: '1fr', sm: '120px 1fr' },
                rowGap: 2,
                columnGap: 3
              }}>
                <Typography variant="body2" color="text.secondary">
                  Tenant Type:
                </Typography>
                <Typography variant="body2" fontWeight={500}>
                  {TENANT_TYPES.find(t => t.value === formData.tenantType)?.label}
                </Typography>

                <Typography variant="body2" color="text.secondary">
                  Tenant Name:
                </Typography>
                <Typography variant="body2" fontWeight={500}>
                  {formData.tenantName}
                </Typography>

                {formData.tenantType === 'kebele' && (
                  <>
                    <Typography variant="body2" color="text.secondary">
                      Tenant Code (ID Card Prefix):
                    </Typography>
                    <Typography variant="body2" fontWeight={500}>
                      {formData.tenantCode}
                    </Typography>
                  </>
                )}

                {formData.tenantType !== 'city' && (
                  <>
                    <Typography variant="body2" color="text.secondary">
                      {formData.tenantType === 'kebele' ? 'Parent Subcity:' : 'Parent City:'}
                    </Typography>
                    <Typography variant="body2" fontWeight={500}>
                      {/* Find the parent tenant in the availableParentTenants array */}
                      {availableParentTenants.find(tenant => tenant.schema_name === formData.parentTenant)?.name || formData.parentTenant}
                    </Typography>
                  </>
                )}

                <Typography variant="body2" color="text.secondary">
                  Address:
                </Typography>
                <Typography variant="body2" fontWeight={500}>
                  {formData.address}
                </Typography>

                <Typography variant="body2" color="text.secondary">
                  Phone:
                </Typography>
                <Typography variant="body2" fontWeight={500}>
                  {formData.phone}
                </Typography>

                <Typography variant="body2" color="text.secondary">
                  Email:
                </Typography>
                <Typography variant="body2" fontWeight={500}>
                  {formData.email}
                </Typography>

                {formData.customDomain && (
                  <>
                    <Typography variant="body2" color="text.secondary">
                      Custom Domain:
                    </Typography>
                    <Typography variant="body2" fontWeight={500}>
                      {formData.customDomain}
                    </Typography>
                  </>
                )}
              </Box>
            </Box>

            {/* City-specific details (only shown for city tenant type) */}
            {formData.tenantType === 'city' && (
              <Box sx={{ mt: 4 }}>
                <Box sx={{
                  display: 'flex',
                  alignItems: 'center',
                  mb: 2,
                  pb: 1,
                  borderBottom: '1px solid #e0e0e0'
                }}>
                  <LocationCityIcon color="primary" sx={{ mr: 1 }} />
                  <Typography variant="subtitle1" sx={{ fontWeight: 600 }}>
                    City Administration Details
                  </Typography>
                </Box>

                <Box sx={{
                  display: 'grid',
                  gridTemplateColumns: { xs: '1fr', sm: '120px 1fr' },
                  rowGap: 2,
                  columnGap: 3
                }}>
                  {/* Logo Preview */}
                  {formData.logoPreview && (
                    <>
                      <Typography variant="body2" color="text.secondary">
                        Logo:
                      </Typography>
                      <Box sx={{
                        width: 100,
                        height: 100,
                        border: '1px solid #ddd',
                        borderRadius: 1,
                        overflow: 'hidden',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        mb: 2
                      }}>
                        <img
                          src={formData.logoPreview}
                          alt="City Logo Preview"
                          style={{ maxWidth: '100%', maxHeight: '100%' }}
                        />
                      </Box>
                    </>
                  )}

                  <Typography variant="body2" color="text.secondary">
                    City Code:
                  </Typography>
                  <Typography variant="body2" fontWeight={500}>
                    {formData.cityCode}
                  </Typography>

                  {formData.mayorName && (
                    <>
                      <Typography variant="body2" color="text.secondary">
                        Mayor:
                      </Typography>
                      <Typography variant="body2" fontWeight={500}>
                        {formData.mayorName}
                      </Typography>
                    </>
                  )}

                  {formData.deputyMayor && (
                    <>
                      <Typography variant="body2" color="text.secondary">
                        Deputy Mayor:
                      </Typography>
                      <Typography variant="body2" fontWeight={500}>
                        {formData.deputyMayor}
                      </Typography>
                    </>
                  )}

                  {formData.mottoSlogan && (
                    <>
                      <Typography variant="body2" color="text.secondary">
                        Motto/Slogan:
                      </Typography>
                      <Typography variant="body2" fontWeight={500}>
                        {formData.mottoSlogan}
                      </Typography>
                    </>
                  )}

                  {formData.establishedDate && (
                    <>
                      <Typography variant="body2" color="text.secondary">
                        Established:
                      </Typography>
                      <Typography variant="body2" fontWeight={500}>
                        {formData.establishedDate}
                      </Typography>
                    </>
                  )}

                  {formData.population && (
                    <>
                      <Typography variant="body2" color="text.secondary">
                        Population:
                      </Typography>
                      <Typography variant="body2" fontWeight={500}>
                        {formData.population}
                      </Typography>
                    </>
                  )}

                  {formData.areaSqKm && (
                    <>
                      <Typography variant="body2" color="text.secondary">
                        Area:
                      </Typography>
                      <Typography variant="body2" fontWeight={500}>
                        {formData.areaSqKm} sq km
                      </Typography>
                    </>
                  )}

                  {formData.elevationMeters && (
                    <>
                      <Typography variant="body2" color="text.secondary">
                        Elevation:
                      </Typography>
                      <Typography variant="body2" fontWeight={500}>
                        {formData.elevationMeters} meters
                      </Typography>
                    </>
                  )}



                  {formData.region && (
                    <>
                      <Typography variant="body2" color="text.secondary">
                        Region:
                      </Typography>
                      <Typography variant="body2" fontWeight={500}>
                        {regions.find(r => r.id.toString() === formData.region)?.name || formData.region}
                      </Typography>
                    </>
                  )}

                  {formData.googleMapsUrl && (
                    <>
                      <Typography variant="body2" color="text.secondary">
                        Google Maps:
                      </Typography>
                      <Typography variant="body2" fontWeight={500}>
                        <Link href={formData.googleMapsUrl} target="_blank" rel="noopener">
                          View on Google Maps
                        </Link>
                      </Typography>
                    </>
                  )}

                  {formData.website && (
                    <>
                      <Typography variant="body2" color="text.secondary">
                        Website:
                      </Typography>
                      <Typography variant="body2" fontWeight={500}>
                        <Link href={formData.website} target="_blank" rel="noopener">
                          {formData.website}
                        </Link>
                      </Typography>
                    </>
                  )}

                  <Typography variant="body2" color="text.secondary">
                    Theme Colors:
                  </Typography>
                  <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
                    <Box sx={{
                      width: 24,
                      height: 24,
                      bgcolor: formData.headerColor,
                      borderRadius: 1,
                      border: '1px solid #ddd'
                    }} />
                    <Box sx={{
                      width: 24,
                      height: 24,
                      bgcolor: formData.accentColor,
                      borderRadius: 1,
                      border: '1px solid #ddd'
                    }} />
                  </Box>
                </Box>
              </Box>
            )}

            {/* Administrator Account Section */}
            <Box>
              <Box sx={{
                display: 'flex',
                alignItems: 'center',
                mb: 2,
                pb: 1,
                borderBottom: '1px solid #e0e0e0'
              }}>
                <AdminPanelSettingsIcon color="primary" sx={{ mr: 1 }} />
                <Typography variant="subtitle1" sx={{ fontWeight: 600 }}>
                  Administrator Account
                </Typography>
              </Box>

              <Box sx={{
                display: 'grid',
                gridTemplateColumns: { xs: '1fr', sm: '120px 1fr' },
                rowGap: 2,
                columnGap: 3
              }}>
                <Typography variant="body2" color="text.secondary">
                  Name:
                </Typography>
                <Typography variant="body2" fontWeight={500}>
                  {formData.adminFirstName} {formData.adminLastName}
                </Typography>

                <Typography variant="body2" color="text.secondary">
                  Email:
                </Typography>
                <Typography variant="body2" fontWeight={500}>
                  {formData.adminEmail}
                </Typography>

                <Typography variant="body2" color="text.secondary">
                  Phone:
                </Typography>
                <Typography variant="body2" fontWeight={500}>
                  {formData.adminPhone}
                </Typography>

                <Typography variant="body2" color="text.secondary">
                  Username:
                </Typography>
                <Typography variant="body2" fontWeight={500}>
                  {formData.customDomain ?
                    `${formData.adminUsername}@${formData.customDomain}` :
                    formData.adminUsername}
                  {formData.customDomain && (
                    <Typography variant="caption" color="primary.main" sx={{ display: 'block', mt: 0.5 }}>
                      <InfoIcon sx={{ fontSize: 12, mr: 0.5, verticalAlign: 'middle' }} />
                      Domain will be appended for login purposes
                    </Typography>
                  )}
                </Typography>
              </Box>
            </Box>
          </Box>

          {/* Terms and Conditions */}
          <Box sx={{ mt: 4, p: 2, bgcolor: 'rgba(0, 0, 0, 0.02)', borderRadius: 1 }}>
            <Typography variant="body2" align="center">
              By clicking "Register Tenant", you agree to the <Link component="span" sx={{ cursor: 'pointer' }}>Terms of Service</Link> and acknowledge that you have read our <Link component="span" sx={{ cursor: 'pointer' }}>Privacy Policy</Link>.
            </Typography>
          </Box>

          {submitError && (
            <Alert severity="error" sx={{ mt: 3 }}>
              {submitError}
            </Alert>
          )}
        </CardContent>
      </Card>
    </Box>
  );

  // Render completion message
  const renderCompletionMessage = () => (
    <Box sx={{ textAlign: 'center', py: 4 }}>
      <Typography variant="h5" gutterBottom color="primary">
        Registration Successful!
      </Typography>
      <Typography variant="body1" paragraph>
        The tenant has been successfully registered in the system.
      </Typography>
      <Typography variant="body2" color="text.secondary" paragraph>
        An email with login instructions has been sent to the administrator's email address.
      </Typography>
      <Button
        variant="contained"
        color="primary"
        onClick={handleReset}
        sx={{ mt: 2 }}
      >
        Register Another Tenant
      </Button>
    </Box>
  );

  // Render the current step content
  const renderStepContent = () => {
    switch (activeStep) {
      case 0:
        return renderTenantInformationForm();
      case 1:
        return renderAdministratorForm();
      case 2:
        return renderReviewForm();
      default:
        return renderCompletionMessage();
    }
  };

  // Use theme and media query for responsive design
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  // State for password visibility
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  // Toggle password visibility
  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  // Toggle confirm password visibility
  const toggleConfirmPasswordVisibility = () => {
    setShowConfirmPassword(!showConfirmPassword);
  };

  return (
    <Box
      sx={{
        bgcolor: '#f8f9fa',
        minHeight: 'calc(100vh - 64px)', // Adjust based on your header height
        display: 'flex',
        flexDirection: 'column'
      }}
    >
      {/* Banner Section */}
      <PageBanner
        title="Tenant Registration"
        subtitle={`Register a new ${formData.tenantType} in the Gondar ID Card Management System`}
        icon={<AdminPanelSettingsIcon sx={{ fontSize: 50, color: 'white' }} />}
        actionContent={
          <Box sx={{ textAlign: 'center', width: '100%' }}>
            <Typography variant="body1" sx={{ color: 'white', fontWeight: 500 }}>
              Complete the form below to create a new tenant and administrator account.
            </Typography>
          </Box>
        }
      >
        {/* ID Card Overlapping Elements */}
        <Box
          sx={{
            display: { xs: 'none', md: 'block' },
            position: 'absolute',
            right: '-15%',
            top: '50%',
            transform: 'translateY(-50%)',
            width: '250px',
            height: '180px',
            zIndex: 5
          }}
        >
          <Box
            sx={{
              position: 'absolute',
              top: 0,
              right: 0,
              width: '200px',
              height: '120px',
              borderRadius: '10px',
              background: 'rgba(255,255,255,0.9)',
              boxShadow: '0 10px 20px rgba(0,0,0,0.1)',
              transform: 'rotate(5deg)',
              overflow: 'hidden'
            }}
          >
            <Box
              sx={{
                height: '30px',
                background: '#3f51b5',
                display: 'flex',
                alignItems: 'center',
                px: 2
              }}
            >
              <Typography variant="caption" sx={{ color: 'white', fontWeight: 600 }}>
                GONDAR RESIDENT ID
              </Typography>
            </Box>
            <Box sx={{ p: 1, display: 'flex' }}>
              <Box
                sx={{
                  width: '40px',
                  height: '50px',
                  borderRadius: '4px',
                  background: '#e0e0e0',
                  mr: 1
                }}
              />
              <Box>
                <Typography variant="caption" sx={{ fontWeight: 600, fontSize: '8px', display: 'block' }}>
                  John Doe
                </Typography>
                <Typography variant="caption" sx={{ color: 'text.secondary', fontSize: '6px', display: 'block' }}>
                  Kebele: Azezo 03
                </Typography>
                <Typography variant="caption" sx={{ color: 'text.secondary', fontSize: '6px', display: 'block' }}>
                  ID: GDR-12345678
                </Typography>
              </Box>
            </Box>
          </Box>

          <Box
            sx={{
              position: 'absolute',
              bottom: 0,
              left: 0,
              width: '180px',
              height: '100px',
              borderRadius: '10px',
              background: 'rgba(255,255,255,0.9)',
              boxShadow: '0 10px 20px rgba(0,0,0,0.1)',
              transform: 'rotate(-8deg)',
              overflow: 'hidden'
            }}
          >
            <Box
              sx={{
                height: '30px',
                background: '#5c6bc0',
                display: 'flex',
                alignItems: 'center',
                px: 2
              }}
            >
              <Typography variant="caption" sx={{ color: 'white', fontWeight: 600 }}>
                GONDAR CITY ID
              </Typography>
            </Box>
            <Box sx={{ p: 1, display: 'flex' }}>
              <Box
                sx={{
                  width: '40px',
                  height: '50px',
                  borderRadius: '4px',
                  background: '#e0e0e0',
                  mr: 1
                }}
              />
              <Box>
                <Typography variant="caption" sx={{ fontWeight: 600, fontSize: '8px', display: 'block' }}>
                  Jane Smith
                </Typography>
                <Typography variant="caption" sx={{ color: 'text.secondary', fontSize: '6px', display: 'block' }}>
                  Subcity: Maraki
                </Typography>
                <Typography variant="caption" sx={{ color: 'text.secondary', fontSize: '6px', display: 'block' }}>
                  ID: GDR-87654321
                </Typography>
              </Box>
            </Box>
          </Box>
        </Box>
      </PageBanner>

      <Container maxWidth="md" sx={{ mt: -3, mb: 6, position: 'relative', zIndex: 5 }}>
        <Paper
          elevation={3}
          sx={{
            p: 4,
            borderRadius: 2,
            boxShadow: '0 4px 20px rgba(0,0,0,0.08)'
          }}
        >

          {/* Stepper */}
          {activeStep < STEPS.length && (
            <Stepper activeStep={activeStep} sx={{ mb: 4, mt: 3 }}>
              {STEPS.map(label => (
                <Step key={label}>
                  <StepLabel>{label}</StepLabel>
                </Step>
              ))}
            </Stepper>
          )}

          {/* Error message */}
          {submitError && (
            <Alert severity="error" sx={{ mb: 3 }}>
              {submitError}
            </Alert>
          )}

          {/* Form content */}
          {renderStepContent()}

          {/* Navigation buttons */}
          {activeStep < STEPS.length && (
            <Box sx={{ mt: 4 }}>
              <Box
                sx={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  mb: 2
                }}
              >
                <Button
                  variant="outlined"
                  onClick={handleBack}
                  disabled={activeStep === 0}
                  startIcon={<ArrowBackIcon />}
                  sx={{ px: 3 }}
                >
                  Back
                </Button>

                {activeStep === STEPS.length - 1 ? (
                  <Button
                    variant="contained"
                    color="primary"
                    onClick={handleSubmit}
                    disabled={!validateStep() || isSubmitting}
                    startIcon={isSubmitting ? <CircularProgress size={20} color="inherit" /> : <CheckCircleOutlineIcon />}
                    sx={{
                      px: 4,
                      py: 1.2,
                      bgcolor: '#3f51b5',
                      borderRadius: 2,
                      textTransform: 'none',
                      fontWeight: 600,
                      fontSize: '1rem',
                      boxShadow: '0 4px 10px rgba(63, 81, 181, 0.25)',
                      '&:hover': {
                        bgcolor: '#303f9f',
                        boxShadow: '0 6px 15px rgba(63, 81, 181, 0.35)'
                      }
                    }}
                  >
                    {isSubmitting ? 'Submitting...' : 'Register Tenant'}
                  </Button>
                ) : (
                  <Button
                    variant="contained"
                    color="primary"
                    onClick={handleNext}
                    disabled={!validateStep()}
                    endIcon={<ArrowForwardIcon />}
                    sx={{
                      px: 3,
                      py: 1,
                      bgcolor: '#3f51b5',
                      borderRadius: 2,
                      textTransform: 'none',
                      fontWeight: 600,
                      '&:hover': {
                        bgcolor: '#303f9f'
                      }
                    }}
                  >
                    Next Step
                  </Button>
                )}
              </Box>

              {/* Progress indicator */}
              <Box sx={{ display: 'flex', justifyContent: 'center', mt: 3 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  {STEPS.map((step, index) => (
                    <React.Fragment key={step}>
                      <Box
                        sx={{
                          width: 10,
                          height: 10,
                          borderRadius: '50%',
                          bgcolor: index <= activeStep ? 'primary.main' : 'grey.300',
                          transition: 'all 0.3s'
                        }}
                      />
                      {index < STEPS.length - 1 && (
                        <Box
                          sx={{
                            width: 30,
                            height: 2,
                            bgcolor: index < activeStep ? 'primary.main' : 'grey.300',
                            transition: 'all 0.3s'
                          }}
                        />
                      )}
                    </React.Fragment>
                  ))}
                </Box>
              </Box>
            </Box>
          )}
        </Paper>
      </Container>
    </Box>
  );
};

export default TenantRegistration;
