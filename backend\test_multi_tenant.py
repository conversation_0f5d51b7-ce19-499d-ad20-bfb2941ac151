import os
import django
import json
import requests

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'neocamelot.settings')
django.setup()

from centers.middleware import set_current_tenant
from centers.models import Center
from citizens.models import Citizen
from idcards.models import IDCard

def test_tenant_isolation():
    """Test that tenant isolation works correctly."""
    print("Testing tenant isolation...")

    # Get all centers
    centers = Center.objects.all()
    if not centers.exists():
        print("No centers found. Please create some centers first.")
        return

    # Test with each center as the current tenant
    for center in centers:
        print(f"\nTesting with center: {center.name}")

        # Set the current tenant
        set_current_tenant(center)

        # Get citizens for this center using the tenant-aware manager
        citizens = Citizen.tenant_objects.all()
        print(f"  Found {citizens.count()} citizens for this center")

        # Verify that all citizens belong to this center
        for citizen in citizens:
            assert citizen.center == center, f"Citizen {citizen.id} belongs to {citizen.center.name}, not {center.name}"

        # Get ID cards for this center using the tenant-aware manager
        id_cards = IDCard.tenant_objects.all()
        print(f"  Found {id_cards.count()} ID cards for this center")

        # Verify that all ID cards belong to this center
        for id_card in id_cards:
            assert id_card.center == center, f"ID card {id_card.id} belongs to {id_card.center.name}, not {center.name}"

    # Clear the current tenant
    set_current_tenant(None)
    print("\nTenant isolation test passed!")

def test_api_tenant_isolation():
    """Test that API tenant isolation works correctly."""
    print("\nTesting API tenant isolation...")

    # Base URL
    base_url = 'http://127.0.0.1:8000'

    # Login to get token
    def login(email, password):
        url = base_url + '/api/login/'
        data = {'email': email, 'password': password}
        response = requests.post(url, json=data)
        if response.status_code == 200:
            return response.json().get('token')
        return None

    # Get citizens for a center
    def get_citizens(token, center_slug=None):
        url = base_url + '/api/citizens/'
        headers = {'Authorization': f'Token {token}'}
        if center_slug:
            headers['X-Center'] = center_slug
        response = requests.get(url, headers=headers)
        return response

    # Login as super admin
    token = login('<EMAIL>', 'admin123')
    if not token:
        print("Failed to login as super admin")
        return

    # Get all centers
    centers = Center.objects.all()
    if not centers.exists():
        print("No centers found. Please create some centers first.")
        return

    # Test with each center
    for center in centers:
        print(f"\nTesting API with center: {center.name}")

        # Get citizens for this center
        response = get_citizens(token, center.slug)
        if response.status_code != 200:
            print(f"  Failed to get citizens: {response.status_code}")
            continue

        citizens = response.json().get('results', [])
        print(f"  Found {len(citizens)} citizens for this center via API")

        # Verify that all citizens belong to this center
        center_citizens = []
        for citizen in citizens:
            if citizen['center_name'] == center.name:
                center_citizens.append(citizen)

        print(f"  Verified {len(center_citizens)} citizens belong to this center")
        assert len(center_citizens) > 0, f"No citizens found for center {center.name}"

    print("\nAPI tenant isolation test passed!")

if __name__ == '__main__':
    test_tenant_isolation()
    # Test API tenant isolation (requires server to be running)
    test_api_tenant_isolation()
