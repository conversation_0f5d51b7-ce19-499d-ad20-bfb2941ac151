/**
 * Citizen Authentication Service
 *
 * This service provides authentication functions specifically for the citizens endpoints.
 * It handles token creation, validation, and refresh for citizen-related API calls.
 */

import { API_BASE_URL } from '../config/apiConfig';

/**
 * Get a valid token for citizen API calls
 * This function tries multiple sources to get a valid token
 */
export const getCitizenApiToken = async (): Promise<string | null> => {
  console.log('Getting token for citizen API call');

  // Try to get JWT token from localStorage first (preferred)
  const jwtToken = localStorage.getItem('jwt_token');
  if (jwtToken) {
    console.log('Found JWT token in localStorage');
    return jwtToken;
  }

  // Try to get token from localStorage (legacy, only if it's a JWT token)
  const localStorageToken = localStorage.getItem('token');
  if (localStorageToken && localStorageToken.startsWith('eyJ')) {
    console.log('Found JWT token in localStorage (token key)');
    return localStorageToken;
  }

  // Try to get JWT token from cookies
  const jwtCookieToken = document.cookie
    .split('; ')
    .find(row => row.startsWith('jwt_token='))
    ?.split('=')[1];

  if (jwtCookieToken) {
    console.log('Found JWT token in cookies');
    // Store it in localStorage for future use
    localStorage.setItem('jwt_token', jwtCookieToken);
    return jwtCookieToken;
  }

  // Try to get token from cookies (only if it's a JWT token)
  const adminToken = document.cookie
    .split('; ')
    .find(row => row.startsWith('adminToken='))
    ?.split('=')[1];

  if (adminToken && adminToken.startsWith('eyJ')) {
    console.log('Found JWT token in adminToken cookie');
    // Store it in localStorage for future use
    localStorage.setItem('jwt_token', adminToken);
    return adminToken;
  }

  // Try to get access_token from localStorage (only if it's a JWT token)
  const accessToken = localStorage.getItem('access_token');
  if (accessToken && accessToken.startsWith('eyJ')) {
    console.log('Found JWT token in access_token localStorage');
    // Store it in the correct location
    localStorage.setItem('jwt_token', accessToken);
    return accessToken;
  }

  // Try to get access_token from cookies (only if it's a JWT token)
  const accessTokenCookie = document.cookie
    .split('; ')
    .find(row => row.startsWith('access_token='))
    ?.split('=')[1];

  if (accessTokenCookie && accessTokenCookie.startsWith('eyJ')) {
    console.log('Found JWT token in access_token cookie');
    // Store it in localStorage for future use
    localStorage.setItem('jwt_token', accessTokenCookie);
    return accessTokenCookie;
  }

  // Try to get auth_token from localStorage (only if it's a JWT token)
  const authToken = localStorage.getItem('auth_token');
  if (authToken && authToken.startsWith('eyJ')) {
    console.log('Found JWT token in auth_token localStorage');
    // Store it in the correct location
    localStorage.setItem('jwt_token', authToken);
    return authToken;
  }

  // If no token found, try to create a new one
  console.log('No token found, attempting to create a new one');
  return await createNewCitizenToken();
};

/**
 * Create a new token for citizen API calls
 */
export const createNewCitizenToken = async (): Promise<string | null> => {
  console.log('Creating new token for citizen API calls');

  // Get username and password from localStorage
  let username = localStorage.getItem('username');
  let password = localStorage.getItem('password');

  // If no username or password found, use hardcoded values for development/testing
  if (!username || !password) {
    console.warn('No username or password found in localStorage, using hardcoded values');
    username = '<EMAIL>';
    password = 'password123';

    // Store them in localStorage for future use
    localStorage.setItem('username', username);
    localStorage.setItem('password', password);
  }

  // Get schema name from localStorage or cookies
  const schema = localStorage.getItem('schema_name') ||
                document.cookie
                  .split('; ')
                  .find(row => row.startsWith('schema_name='))
                  ?.split('=')[1] ||
                'kebele14';  // Default fallback

  console.log('Using schema for token creation:', schema);

  try {
    // Try JWT login first
    console.log('Attempting JWT login');
    const jwtLoginUrl = `${API_BASE_URL}/api/jwt/login/`;

    // Log the request data for debugging
    console.log('JWT login request data:', {
      email: username,
      password: '********', // Don't log actual password
      schema_name: schema
    });

    const jwtResponse = await fetch(jwtLoginUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Schema-Name': schema
      },
      body: JSON.stringify({
        email: username,
        password,
        schema_name: schema
      }),
      credentials: 'include'
    });

    // Log the response status for debugging
    console.log('JWT login response status:', jwtResponse.status);

    if (jwtResponse.ok) {
      const jwtData = await jwtResponse.json();
      console.log('JWT login response data:', jwtData);

      if (jwtData.access_token) {
        console.log('Successfully created new JWT token via login');

        // Store the token in localStorage with consistent naming
        localStorage.setItem('jwt_token', jwtData.access_token);
        localStorage.setItem('jwt_access_token', jwtData.access_token);

        // Also store the refresh token if available
        if (jwtData.refresh_token) {
          localStorage.setItem('jwt_refresh_token', jwtData.refresh_token);
        }

        return jwtData.access_token;
      }
    } else {
      // Try to get error details
      try {
        const errorData = await jwtResponse.json();
        console.error('JWT login failed with error:', errorData);
      } catch (e) {
        try {
          const errorText = await jwtResponse.text();
          console.error('JWT login failed with error text:', errorText);
        } catch (textError) {
          console.error('JWT login failed, could not read error details');
        }
      }
    }

    console.error('JWT login failed');
    console.error('JWT login failed. The application now uses JWT authentication exclusively.');
    console.error('Please make sure the backend is properly configured for JWT authentication.');

    // Return null to indicate failure
    return null;
  } catch (error) {
    console.error('Error creating new token:', error);
    return null;
  }
};
