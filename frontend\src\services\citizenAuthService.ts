/**
 * Citizen Authentication Service
 *
 * This service provides authentication functions specifically for the citizens endpoints.
 * It handles token creation, validation, and refresh for citizen-related API calls.
 */

import { API_BASE_URL } from '../config/apiConfig';

/**
 * Get a valid token for citizen API calls
 * This function tries multiple sources to get a valid token
 */
export const getCitizenApiToken = async (): Promise<string | null> => {
  console.log('Getting token for citizen API call');

  // Try to get JWT token from localStorage with consistent naming
  const jwtAccessToken = localStorage.getItem('jwt_access_token');
  if (jwtAccessToken) {
    console.log('Found JWT access token in localStorage');
    return jwtAccessToken;
  }

  // Try alternative JWT token storage locations
  const jwtToken = localStorage.getItem('jwt_token');
  if (jwtToken) {
    console.log('Found JWT token in localStorage');
    // Store it with the standard name for consistency
    localStorage.setItem('jwt_access_token', jwtToken);
    return jwtToken;
  }

  // Get the current schema
  const schema = localStorage.getItem('schema_name') ||
                document.cookie
                  .split('; ')
                  .find(row => row.startsWith('schema_name='))
                  ?.split('=')[1];

  // Try schema-specific token if we have a schema
  if (schema) {
    const schemaToken = localStorage.getItem(`jwt_access_token_${schema}`);
    if (schemaToken) {
      console.log(`Found schema-specific JWT token for ${schema}`);
      // Store it with the standard name for consistency
      localStorage.setItem('jwt_access_token', schemaToken);
      return schemaToken;
    }
  }

  // Try to get JWT token from cookies
  const jwtCookieToken = document.cookie
    .split('; ')
    .find(row => row.startsWith('jwt_access_token='))
    ?.split('=')[1];

  if (jwtCookieToken) {
    console.log('Found JWT token in cookies');
    // Store it in localStorage for future use
    localStorage.setItem('jwt_access_token', jwtCookieToken);
    return jwtCookieToken;
  }

  // If no token found, try to create a new one
  console.log('No valid JWT token found, attempting to create a new one');
  return await createNewCitizenToken();
};

/**
 * Create a new token for citizen API calls
 */
export const createNewCitizenToken = async (): Promise<string | null> => {
  console.log('Creating new token for citizen API calls');

  // Get username and password from localStorage
  let username = localStorage.getItem('username');
  let password = localStorage.getItem('password');

  // If no username or password found, use hardcoded values for development/testing
  if (!username || !password) {
    console.warn('No username or password found in localStorage, using hardcoded values');
    username = '<EMAIL>';
    password = 'password123';

    // Store them in localStorage for future use
    localStorage.setItem('username', username);
    localStorage.setItem('password', password);
  }

  // Get schema name from localStorage or cookies
  let schema = localStorage.getItem('schema_name') ||
                document.cookie
                  .split('; ')
                  .find(row => row.startsWith('schema_name='))
                  ?.split('=')[1];

  // If no schema found, try to get it from tenant
  if (!schema) {
    try {
      const tenantJson = localStorage.getItem('tenant');
      if (tenantJson) {
        const tenant = JSON.parse(tenantJson);
        schema = tenant.schema_name;
      }
    } catch (e) {
      console.error('Error parsing tenant from localStorage:', e);
    }
  }

  // Use default fallback if still no schema
  if (!schema) {
    schema = 'kebele14';
  }

  console.log('Using schema for token creation:', schema);

  try {
    // Try JWT login first
    console.log('Attempting JWT login');
    const jwtLoginUrl = `${API_BASE_URL}/api/jwt/login/`;

    // Set schema cookie for the request
    document.cookie = `schema_name=${encodeURIComponent(schema)}; path=/; SameSite=Lax`;

    // Log the request data for debugging
    console.log('JWT login request data:', {
      email: username,
      password: '********', // Don't log actual password
      schema_name: schema
    });

    const jwtResponse = await fetch(jwtLoginUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Schema-Name': schema
      },
      body: JSON.stringify({
        email: username,
        password,
        schema_name: schema
      }),
      credentials: 'include'
    });

    // Log the response status for debugging
    console.log('JWT login response status:', jwtResponse.status);

    if (jwtResponse.ok) {
      const jwtData = await jwtResponse.json();
      console.log('JWT login response data:', jwtData);

      if (jwtData.access_token) {
        console.log('Successfully created new JWT token via login');

        // Store the token in localStorage with consistent naming
        localStorage.setItem('jwt_token', jwtData.access_token);
        localStorage.setItem('jwt_access_token', jwtData.access_token);

        // Store schema information
        if (jwtData.tenant && jwtData.tenant.schema_name) {
          localStorage.setItem('schema_name', jwtData.tenant.schema_name);

          // Store schema-specific token
          localStorage.setItem(`jwt_access_token_${jwtData.tenant.schema_name}`, jwtData.access_token);

          // Store tenant information
          localStorage.setItem('tenant', JSON.stringify(jwtData.tenant));
        } else {
          // Store schema from request if not in response
          localStorage.setItem('schema_name', schema);
          localStorage.setItem(`jwt_access_token_${schema}`, jwtData.access_token);
        }

        // Store user information if available
        if (jwtData.user) {
          localStorage.setItem('user', JSON.stringify(jwtData.user));
        }

        // Also store the refresh token if available
        if (jwtData.refresh_token) {
          localStorage.setItem('jwt_refresh_token', jwtData.refresh_token);

          // Store schema-specific refresh token
          if (jwtData.tenant && jwtData.tenant.schema_name) {
            localStorage.setItem(`jwt_refresh_token_${jwtData.tenant.schema_name}`, jwtData.refresh_token);
          } else {
            localStorage.setItem(`jwt_refresh_token_${schema}`, jwtData.refresh_token);
          }
        }

        return jwtData.access_token;
      }
    } else {
      // Try to get error details
      try {
        const errorData = await jwtResponse.json();
        console.error('JWT login failed with error:', errorData);

        // Check for specific error messages
        if (errorData.error && typeof errorData.error === 'string') {
          if (errorData.error.includes('not found in any schema')) {
            console.error('User not found in the specified tenant. Please check your credentials or tenant selection.');
          } else if (errorData.error.includes('Invalid credentials')) {
            console.error('Invalid credentials for the specified tenant. Please check your username and password.');
          } else if (errorData.error.includes('Tenant with schema')) {
            console.error('Tenant not found. Please check your tenant selection.');
          }
        }
      } catch (e) {
        try {
          const errorText = await jwtResponse.text();
          console.error('JWT login failed with error text:', errorText);
        } catch (textError) {
          console.error('JWT login failed, could not read error details');
        }
      }
    }

    console.error('JWT login failed. The application now uses JWT authentication exclusively.');
    console.error('Please make sure the backend is properly configured for JWT authentication.');

    // Return null to indicate failure
    return null;
  } catch (error) {
    console.error('Error creating new token:', error);
    return null;
  }
};
