import os
import django

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'neocamelot.settings')
django.setup()

# Import URL patterns
from neocamelot.urls import router as public_router
from neocamelot.urls_tenants import router as tenant_router

print("Public API endpoints:")
for prefix, viewset, basename in public_router.registry:
    print(f"- {prefix}: {viewset.__name__}")

print("\nTenant API endpoints:")
for prefix, viewset, basename in tenant_router.registry:
    print(f"- {prefix}: {viewset.__name__}")
