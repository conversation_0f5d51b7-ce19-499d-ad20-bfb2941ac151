import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Typography,
  Button,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Chip,
  IconButton,
  CircularProgress,
  Alert,
  AlertTitle,
  TextField,
  InputAdornment,
  Card,
  CardContent,
  Grid
} from '@mui/material';
import { fetchWithAuth, validateAuth, handleAuthError } from '../utils/authUtils';
import { useNavigate } from 'react-router-dom';
import UnauthorizedAccess from '../components/tenant/UnauthorizedAccess';
import PageBanner from '../components/PageBanner';
import { createTenantApiUrl, getDefaultHeaders } from '../config/api';

// Icons
import AddIcon from '@mui/icons-material/Add';
import SearchIcon from '@mui/icons-material/Search';
import VisibilityIcon from '@mui/icons-material/Visibility';
import PrintIcon from '@mui/icons-material/Print';
import FilterListIcon from '@mui/icons-material/FilterList';
import CreditCardIcon from '@mui/icons-material/CreditCard';
import HourglassEmptyIcon from '@mui/icons-material/HourglassEmpty';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import CancelIcon from '@mui/icons-material/Cancel';
import PendingIcon from '@mui/icons-material/Pending';
import AssignmentTurnedInIcon from '@mui/icons-material/AssignmentTurnedIn';

// Status chip colors
const statusColors: Record<string, string> = {
  'DRAFT': 'default',
  'PENDING': 'warning',
  'APPROVED': 'success',
  'PRINTED': 'info',
  'ISSUED': 'primary',
  'EXPIRED': 'error',
  'REVOKED': 'error'
};

const IDCardsList: React.FC = () => {
  const navigate = useNavigate();

  // State for ID cards data
  const [idCards, setIdCards] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  // State for pagination
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);

  // State for search
  const [searchQuery, setSearchQuery] = useState('');

  // Get the current tenant and authentication info from localStorage
  const tenantString = localStorage.getItem('tenant');
  const tenant = tenantString ? JSON.parse(tenantString) : null;
  const token = localStorage.getItem('token');
  const schemaName = localStorage.getItem('schema_name');

  // Don't redirect to login if not authenticated
  // We'll try to fetch data even without authentication
  useEffect(() => {
    console.log('IDCardsList - Token status:', token ? 'Present' : 'Not present');
    // We'll continue even without a token
  }, [token]);

  // Check if tenant type is allowed to access this page
  // Temporarily allow all tenant types to access this page
  const isTenantAuthorized = true; // Bypass the tenant type check

  // Log tenant information for debugging
  console.log('IDCardsList - Tenant information:', tenant);
  console.log('IDCardsList - Tenant type:', tenant?.type);
  console.log('IDCardsList - Tenant schema_type:', tenant?.schema_type);

  // Fetch ID cards on component mount
  useEffect(() => {
    // Don't use isTokenValid() here as it will redirect to login
    if (token) {
      fetchIDCards();
    }
  }, [token]);

  // Fetch ID cards from the API
  const fetchIDCards = async () => {
    // Don't use isTokenValid() here as it will redirect to login

    setLoading(true);
    setError(''); // Clear any previous errors

    console.log('Fetching ID card data from database');

    try {
      // Use the tenant-specific API endpoint
      let schema = schemaName || tenant?.schema_name || '';
      if (!schema) {
        setError('No tenant schema found. Please select a tenant or log in again.');
        setLoading(false);
        return;
      }

      // Use our API configuration to create the URL
      const url = createTenantApiUrl(schema, 'idcards/');
      console.log('Fetching ID cards with URL:', url);
      console.log('Using schema:', schema);

      // Debug information
      console.log('Request headers:', getDefaultHeaders());

      // Try to fetch with a timeout
      let response;
      try {
        // Create an AbortController with a timeout
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 second timeout

        response = await fetch(url, {
          method: 'GET',
          headers: getDefaultHeaders(),
          credentials: 'include',
          signal: controller.signal
        });

        clearTimeout(timeoutId); // Clear the timeout if the fetch completes
      } catch (fetchError: any) {
        console.error('Fetch error:', fetchError);
        setLoading(false);

        if (fetchError.name === 'AbortError') {
          setError('Request timed out. The server took too long to respond. Please try again later.');
        } else {
          setError('Cannot connect to the server. Please check if the backend server is running.');
        }
        return;
      }

      console.log('Response status:', response.status);
      console.log('Response headers:', Object.fromEntries([...response.headers.entries()]));

      // Get the response text first for debugging
      const responseText = await response.text();
      console.log('Raw response:', responseText);

      if (!response.ok) {
        // If API call fails with 401, try to continue without authentication
        if (response.status === 401) {
          console.log('Authentication failed (401), but continuing without authentication');

          try {
            // Try to get response body for more details
            let responseBody = '';
            try {
              responseBody = responseText;
              console.log('Response body from 401:', responseBody);
            } catch (e) {
              console.log('Could not read response body:', e);
            }

            // Try to make the request without authentication headers
            console.log('Trying request without authentication headers');
            const noAuthResponse = await fetch(url, {
              method: 'GET',
              headers: {
                'Content-Type': 'application/json',
                'X-Schema-Name': schema
              },
              credentials: 'include'
            });

            if (noAuthResponse.ok) {
              console.log('Request without authentication succeeded');
              const noAuthResponseText = await noAuthResponse.text();
              console.log('ID Cards data from API (no auth):', noAuthResponseText);

              // Parse the response text as JSON
              let responseData;
              try {
                responseData = noAuthResponseText ? JSON.parse(noAuthResponseText) : [];
                console.log('ID Cards data from API (no auth, parsed):', responseData);

                // Handle different response formats
                let data;
                if (responseData && typeof responseData === 'object') {
                  // If the response is an object with a data property that is an array
                  if (responseData.data && Array.isArray(responseData.data)) {
                    data = responseData.data;
                    console.log('Found data array in response object');
                  }
                  // If the response is an array directly
                  else if (Array.isArray(responseData)) {
                    data = responseData;
                    console.log('Response is an array');
                  }
                  // If the response has results property that is an array (common in DRF pagination)
                  else if (responseData.results && Array.isArray(responseData.results)) {
                    data = responseData.results;
                    console.log('Found results array in response object');
                  }
                  // If the response is an object with ID card data
                  else if (responseData.id || responseData.card_number) {
                    data = [responseData];
                    console.log('Response is a single ID card object, converting to array');
                  }
                  // Unknown format
                  else {
                    console.error('Unknown response format:', responseData);
                    data = [];
                  }
                } else {
                  console.error('Response is not an object or array:', responseData);
                  data = [];
                }

                // Check if data is empty
                if (!data || data.length === 0) {
                  console.log('No ID cards data found or empty array returned');
                  setIdCards([]);
                  setLoading(false);
                  return;
                }

                console.log(`Received ${data.length} ID cards without authentication`);
                setIdCards(data);
                setLoading(false);
                return;
              } catch (jsonError) {
                console.error('Error parsing JSON response:', jsonError);
                setLoading(false);
                setError('Could not parse server response. Please try again later.');
                return;
              }
            } else {
              console.log('Request without authentication also failed:', noAuthResponse.status);

              // If both authenticated and unauthenticated requests fail, show error
              try {
                // Try to parse the response text as JSON
                const errorData = responseText ? JSON.parse(responseText) : {};
                console.log('Error data:', errorData);
                throw new Error(errorData.detail || errorData.error || JSON.stringify(errorData) || `Failed to fetch ID cards: ${response.status} ${response.statusText}`);
              } catch (jsonError) {
                console.log('Error parsing JSON:', jsonError);
                setLoading(false);
                setError(`Server error: ${response.status} ${response.statusText}`);
                return;
              }
            }
          } catch (error: any) {
            console.error('Error handling 401:', error);
            setError('Authentication error. Please try again.');
            setLoading(false);
            return;
          }
        } else {
          // For non-401 errors, handle as before
          try {
            // Try to parse the response text as JSON
            const errorData = responseText ? JSON.parse(responseText) : {};
            console.log('Error data:', errorData);
            throw new Error(errorData.detail || errorData.error || JSON.stringify(errorData) || `Failed to fetch ID cards: ${response.status} ${response.statusText}`);
          } catch (jsonError) {
            console.log('Error parsing JSON:', jsonError);
            setLoading(false);
            setError(`Server error: ${response.status} ${response.statusText}`);
            return;
          }
        }
      }

      // Parse the response text as JSON
      let responseData;
      try {
        responseData = responseText ? JSON.parse(responseText) : [];
        console.log('ID Cards data from API:', responseData);
      } catch (jsonError) {
        console.error('Error parsing JSON response:', jsonError);
        setLoading(false);
        setError('Could not parse server response. Please try again later.');
        return;
      }

      // Handle different response formats
      let data;
      if (responseData && typeof responseData === 'object') {
        // If the response is an object with a data property that is an array
        if (responseData.data && Array.isArray(responseData.data)) {
          data = responseData.data;
          console.log('Found data array in response object');
        }
        // If the response is an array directly
        else if (Array.isArray(responseData)) {
          data = responseData;
          console.log('Response is an array');
        }
        // If the response has results property that is an array (common in DRF pagination)
        else if (responseData.results && Array.isArray(responseData.results)) {
          data = responseData.results;
          console.log('Found results array in response object');
        }
        // If the response is an object with ID card data
        else if (responseData.id || responseData.card_number) {
          data = [responseData];
          console.log('Response is a single ID card object, converting to array');
        }
        // Unknown format
        else {
          console.error('Unknown response format:', responseData);
          data = [];
        }
      } else {
        console.error('Response is not an object or array:', responseData);
        data = [];
      }

      // Check if data is empty
      if (!data || data.length === 0) {
        console.log('No ID cards data found or empty array returned');
        setIdCards([]);
        return;
      }

      console.log(`Received ${data.length} ID cards`);
      setIdCards(data);
    } catch (error: any) {
      console.error('Error fetching ID cards:', error);
      setError(error.message || 'An error occurred while fetching ID cards');
    } finally {
      setLoading(false);
    }
  };

  // Handle search input change
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
  };

  // Handle search submit
  const handleSearch = async () => {
    // Don't use isTokenValid() here as it will redirect to login

    setLoading(true);
    setError(''); // Clear any previous errors

    try {
      // Use the tenant-specific API endpoint
      let schema = schemaName || tenant?.schema_name || '';
      if (!schema) {
        setError('No tenant schema found. Please select a tenant or log in again.');
        setLoading(false);
        return;
      }

      // Use our API configuration to create the URL with search parameter
      const url = createTenantApiUrl(schema, `idcards/?search=${encodeURIComponent(searchQuery)}`);
      console.log('Searching ID cards with URL:', url);
      console.log('Using schema for search:', schema);

      // Debug information
      console.log('Search request headers:', getDefaultHeaders());

      // Try to fetch with a timeout
      let response;
      try {
        // Create an AbortController with a timeout
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 second timeout

        response = await fetch(url, {
          method: 'GET',
          headers: getDefaultHeaders(),
          credentials: 'include',
          signal: controller.signal
        });

        clearTimeout(timeoutId); // Clear the timeout if the fetch completes
      } catch (fetchError: any) {
        console.error('Fetch error:', fetchError);
        setLoading(false);

        if (fetchError.name === 'AbortError') {
          setError('Request timed out. The server took too long to respond. Please try again later.');
        } else {
          setError('Cannot connect to the server. Please check if the backend server is running.');
        }
        return;
      }

      console.log('Search response status:', response.status);
      console.log('Search response headers:', Object.fromEntries([...response.headers.entries()]));

      // Get the response text first for debugging
      const responseText = await response.text();
      console.log('Raw search response:', responseText);

      if (!response.ok) {
        // If API call fails with 401, try to continue without authentication
        if (response.status === 401) {
          console.log('Authentication failed during search (401), but continuing without authentication');

          try {
            // Try to get response body for more details
            let responseBody = '';
            try {
              responseBody = responseText;
              console.log('Search response body from 401:', responseBody);
            } catch (e) {
              console.log('Could not read search response body:', e);
            }

            // Try to make the request without authentication headers
            console.log('Trying search request without authentication headers');
            const noAuthResponse = await fetch(url, {
              method: 'GET',
              headers: {
                'Content-Type': 'application/json',
                'X-Schema-Name': schema
              },
              credentials: 'include'
            });

            if (noAuthResponse.ok) {
              console.log('Search request without authentication succeeded');
              const noAuthResponseText = await noAuthResponse.text();
              console.log('Search results from API (no auth):', noAuthResponseText);

              // Parse the response text as JSON
              let responseData;
              try {
                responseData = noAuthResponseText ? JSON.parse(noAuthResponseText) : [];
                console.log('Search results from API (no auth, parsed):', responseData);

                // Handle different response formats
                let data;
                if (responseData && typeof responseData === 'object') {
                  // If the response is an object with a data property that is an array
                  if (responseData.data && Array.isArray(responseData.data)) {
                    data = responseData.data;
                    console.log('Found data array in response object');
                  }
                  // If the response is an array directly
                  else if (Array.isArray(responseData)) {
                    data = responseData;
                    console.log('Response is an array');
                  }
                  // If the response has results property that is an array (common in DRF pagination)
                  else if (responseData.results && Array.isArray(responseData.results)) {
                    data = responseData.results;
                    console.log('Found results array in response object');
                  }
                  // If the response is an object with ID card data
                  else if (responseData.id || responseData.card_number) {
                    data = [responseData];
                    console.log('Response is a single ID card object, converting to array');
                  }
                  // Unknown format
                  else {
                    console.error('Unknown response format:', responseData);
                    data = [];
                  }
                } else {
                  console.error('Response is not an object or array:', responseData);
                  data = [];
                }

                // Check if data is empty
                if (!data || data.length === 0) {
                  console.log('No search results found or empty array returned');
                  setIdCards([]);
                  setLoading(false);
                  return;
                }

                console.log(`Received ${data.length} search results without authentication`);
                setIdCards(data);
                setLoading(false);
                return;
              } catch (jsonError) {
                console.error('Error parsing search JSON response:', jsonError);
                setLoading(false);
                setError('Could not parse server response. Please try again later.');
                return;
              }
            } else {
              console.log('Search request without authentication also failed:', noAuthResponse.status);

              // If both authenticated and unauthenticated requests fail, show error
              try {
                // Try to parse the response text as JSON
                const errorData = responseText ? JSON.parse(responseText) : {};
                console.log('Search error data:', errorData);
                throw new Error(errorData.detail || errorData.error || JSON.stringify(errorData) || `Failed to search ID cards: ${response.status} ${response.statusText}`);
              } catch (jsonError) {
                console.log('Error parsing search JSON:', jsonError);
                setLoading(false);
                setError(`Server error: ${response.status} ${response.statusText}`);
                return;
              }
            }
          } catch (error: any) {
            console.error('Error handling search 401:', error);
            setError('Authentication error during search. Please try again.');
            setLoading(false);
            return;
          }
        } else {
          // For non-401 errors, handle as before
          try {
            // Try to parse the response text as JSON
            const errorData = responseText ? JSON.parse(responseText) : {};
            console.log('Search error data:', errorData);
            throw new Error(errorData.detail || errorData.error || JSON.stringify(errorData) || `Failed to search ID cards: ${response.status} ${response.statusText}`);
          } catch (jsonError) {
            console.log('Error parsing search JSON:', jsonError);
            setLoading(false);
            setError(`Server error: ${response.status} ${response.statusText}`);
            return;
          }
        }
      }

      // Parse the response text as JSON
      let responseData;
      try {
        responseData = responseText ? JSON.parse(responseText) : [];
        console.log('Search results from API:', responseData);
      } catch (jsonError) {
        console.error('Error parsing JSON search response:', jsonError);
        setLoading(false);
        setError('Could not parse server response. Please try again later.');
        return;
      }

      // Handle different response formats
      let data;
      if (responseData && typeof responseData === 'object') {
        // If the response is an object with a data property that is an array
        if (responseData.data && Array.isArray(responseData.data)) {
          data = responseData.data;
          console.log('Found data array in response object');
        }
        // If the response is an array directly
        else if (Array.isArray(responseData)) {
          data = responseData;
          console.log('Response is an array');
        }
        // If the response has results property that is an array (common in DRF pagination)
        else if (responseData.results && Array.isArray(responseData.results)) {
          data = responseData.results;
          console.log('Found results array in response object');
        }
        // If the response is an object with ID card data
        else if (responseData.id || responseData.card_number) {
          data = [responseData];
          console.log('Response is a single ID card object, converting to array');
        }
        // Unknown format
        else {
          console.error('Unknown response format:', responseData);
          data = [];
        }
      } else {
        console.error('Response is not an object or array:', responseData);
        data = [];
      }

      // Check if data is empty
      if (!data || data.length === 0) {
        console.log('No search results found or empty array returned');
        setIdCards([]);
        return;
      }

      console.log(`Received ${data.length} search results`);
      setIdCards(data);
    } catch (error: any) {
      console.error('Error searching ID cards:', error);
      setError(error.message || 'An error occurred while searching ID cards');
    } finally {
      setLoading(false);
    }
  };

  // Handle page change
  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };

  // Handle rows per page change
  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  // Navigate to ID card registration page
  const handleRegisterIDCard = () => {
    navigate('/id-cards/new');
  };

  // Navigate to ID card details page
  const handleViewIDCard = (id: number) => {
    navigate(`/id-cards/${id}`);
  };

  // Filter ID cards based on search query (client-side filtering for already loaded data)
  const filteredIDCards = idCards.filter((card) => {
    if (!searchQuery) return true;

    const query = searchQuery.toLowerCase();
    return (
      card.card_number?.toLowerCase().includes(query) ||
      card.citizen_name?.toLowerCase().includes(query) ||
      card.status?.toLowerCase().includes(query)
    );
  });

  // Get current page of ID cards
  const currentIDCards = filteredIDCards.slice(
    page * rowsPerPage,
    page * rowsPerPage + rowsPerPage
  );

  // If tenant is not authorized, show unauthorized access component
  if (!isTenantAuthorized) {
    return (
      <UnauthorizedAccess
        message="Your tenant type does not have permission to register or manage ID cards."
        tenantType={tenant?.type}
      />
    );
  }

  return (
    <Box sx={{ py: 4 }}>
      {/* Banner */}
      <PageBanner
        title="ID Cards Management"
        subtitle={`Manage ID cards for citizens in ${tenant?.name || 'your center'}`}
        icon={<CreditCardIcon sx={{ fontSize: 50, color: 'white' }} />}
        actionContent={
          <Box sx={{ textAlign: 'center', width: '100%' }}>
            <Typography variant="body1" sx={{ color: 'white', fontWeight: 500 }}>
              Register, view, and manage ID cards for citizens. Track the status of ID cards from draft to issuance.
            </Typography>
          </Box>
        }
      />

      <Container maxWidth="lg">
        {/* Dashboard Cards */}
        <Grid container spacing={2} sx={{ mb: 4, display: 'flex', justifyContent: 'space-between', pb: 2 }}>
          <Grid item xs={12} sm={6} md={3}>
            <Card
              sx={{
                height: '100%',
                minHeight: '180px',
                width: '100%',
                display: 'flex',
                flexDirection: 'column',
                transition: 'transform 0.3s, box-shadow 0.3s',
                '&:hover': {
                  transform: 'translateY(-8px)',
                  boxShadow: '0 12px 20px rgba(0, 0, 0, 0.15)',
                },
                borderRadius: 3,
                overflow: 'hidden',
                border: 'none',
                boxShadow: '0 6px 12px rgba(0, 0, 0, 0.08)',
                position: 'relative',
                '&::before': {
                  content: '""',
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  width: '100%',
                  height: '4px',
                  backgroundColor: 'primary.main',
                  zIndex: 1
                }
              }}
            >
              <Box
                sx={{
                  p: 3,
                  display: 'flex',
                  alignItems: 'center',
                  bgcolor: 'white',
                  color: 'text.primary',
                  borderBottom: '1px solid rgba(0, 0, 0, 0.05)'
                }}
              >
                <Box
                  sx={{
                    mr: 2,
                    bgcolor: 'primary.main15', // 15% opacity of the color
                    color: 'primary.main',
                    p: 1.5,
                    borderRadius: 2,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}
                >
                  <CreditCardIcon />
                </Box>
                <Typography variant="h6" component="div" fontWeight="500">
                  Total ID Cards
                </Typography>
                <Box
                  sx={{
                    ml: 'auto',
                    bgcolor: 'primary.main15', // 15% opacity of the color
                    color: 'primary.main',
                    borderRadius: '50%',
                    width: 40,
                    height: 40,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    fontWeight: 'bold'
                  }}
                >
                  <Typography variant="body1" fontWeight="bold">
                    {loading ? <CircularProgress size={20} /> : idCards.length}
                  </Typography>
                </Box>
              </Box>
              <CardContent sx={{ flexGrow: 1, p: 3, minHeight: '80px', display: 'flex', alignItems: 'center', justifyContent: 'center', textAlign: 'center' }}>
                <Typography variant="body1" color="text.secondary" sx={{ lineHeight: 1.6 }}>
                  Total ID cards in the system
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card
              sx={{
                height: '100%',
                minHeight: '180px',
                width: '100%',
                display: 'flex',
                flexDirection: 'column',
                transition: 'transform 0.3s, box-shadow 0.3s',
                '&:hover': {
                  transform: 'translateY(-8px)',
                  boxShadow: '0 12px 20px rgba(0, 0, 0, 0.15)',
                },
                borderRadius: 3,
                overflow: 'hidden',
                border: 'none',
                boxShadow: '0 6px 12px rgba(0, 0, 0, 0.08)',
                position: 'relative',
                '&::before': {
                  content: '""',
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  width: '100%',
                  height: '4px',
                  backgroundColor: 'warning.main',
                  zIndex: 1
                }
              }}
            >
              <Box
                sx={{
                  p: 3,
                  display: 'flex',
                  alignItems: 'center',
                  bgcolor: 'white',
                  color: 'text.primary',
                  borderBottom: '1px solid rgba(0, 0, 0, 0.05)'
                }}
              >
                <Box
                  sx={{
                    mr: 2,
                    bgcolor: 'warning.main15', // 15% opacity of the color
                    color: 'warning.main',
                    p: 1.5,
                    borderRadius: 2,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}
                >
                  <HourglassEmptyIcon />
                </Box>
                <Typography variant="h6" component="div" fontWeight="500">
                  Pending Approval
                </Typography>
                <Box
                  sx={{
                    ml: 'auto',
                    bgcolor: 'warning.main15', // 15% opacity of the color
                    color: 'warning.main',
                    borderRadius: '50%',
                    width: 40,
                    height: 40,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    fontWeight: 'bold'
                  }}
                >
                  <Typography variant="body1" fontWeight="bold">
                    {loading ? <CircularProgress size={20} /> :
                      idCards.filter(card => card.status === 'PENDING').length}
                  </Typography>
                </Box>
              </Box>
              <CardContent sx={{ flexGrow: 1, p: 3, minHeight: '80px', display: 'flex', alignItems: 'center', justifyContent: 'center', textAlign: 'center' }}>
                <Typography variant="body1" color="text.secondary" sx={{ lineHeight: 1.6 }}>
                  Awaiting approval from authorities
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card
              sx={{
                height: '100%',
                minHeight: '180px',
                width: '100%',
                display: 'flex',
                flexDirection: 'column',
                transition: 'transform 0.3s, box-shadow 0.3s',
                '&:hover': {
                  transform: 'translateY(-8px)',
                  boxShadow: '0 12px 20px rgba(0, 0, 0, 0.15)',
                },
                borderRadius: 3,
                overflow: 'hidden',
                border: 'none',
                boxShadow: '0 6px 12px rgba(0, 0, 0, 0.08)',
                position: 'relative',
                '&::before': {
                  content: '""',
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  width: '100%',
                  height: '4px',
                  backgroundColor: 'success.main',
                  zIndex: 1
                }
              }}
            >
              <Box
                sx={{
                  p: 3,
                  display: 'flex',
                  alignItems: 'center',
                  bgcolor: 'white',
                  color: 'text.primary',
                  borderBottom: '1px solid rgba(0, 0, 0, 0.05)'
                }}
              >
                <Box
                  sx={{
                    mr: 2,
                    bgcolor: 'success.main15', // 15% opacity of the color
                    color: 'success.main',
                    p: 1.5,
                    borderRadius: 2,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}
                >
                  <CheckCircleIcon />
                </Box>
                <Typography variant="h6" component="div" fontWeight="500">
                  Approved
                </Typography>
                <Box
                  sx={{
                    ml: 'auto',
                    bgcolor: 'success.main15', // 15% opacity of the color
                    color: 'success.main',
                    borderRadius: '50%',
                    width: 40,
                    height: 40,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    fontWeight: 'bold'
                  }}
                >
                  <Typography variant="body1" fontWeight="bold">
                    {loading ? <CircularProgress size={20} /> :
                      idCards.filter(card => card.status === 'APPROVED').length}
                  </Typography>
                </Box>
              </Box>
              <CardContent sx={{ flexGrow: 1, p: 3, minHeight: '80px', display: 'flex', alignItems: 'center', justifyContent: 'center', textAlign: 'center' }}>
                <Typography variant="body1" color="text.secondary" sx={{ lineHeight: 1.6 }}>
                  Ready for printing
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card
              sx={{
                height: '100%',
                minHeight: '180px',
                width: '100%',
                display: 'flex',
                flexDirection: 'column',
                transition: 'transform 0.3s, box-shadow 0.3s',
                '&:hover': {
                  transform: 'translateY(-8px)',
                  boxShadow: '0 12px 20px rgba(0, 0, 0, 0.15)',
                },
                borderRadius: 3,
                overflow: 'hidden',
                border: 'none',
                boxShadow: '0 6px 12px rgba(0, 0, 0, 0.08)',
                position: 'relative',
                '&::before': {
                  content: '""',
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  width: '100%',
                  height: '4px',
                  backgroundColor: 'info.main',
                  zIndex: 1
                }
              }}
            >
              <Box
                sx={{
                  p: 3,
                  display: 'flex',
                  alignItems: 'center',
                  bgcolor: 'white',
                  color: 'text.primary',
                  borderBottom: '1px solid rgba(0, 0, 0, 0.05)'
                }}
              >
                <Box
                  sx={{
                    mr: 2,
                    bgcolor: 'info.main15', // 15% opacity of the color
                    color: 'info.main',
                    p: 1.5,
                    borderRadius: 2,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}
                >
                  <AssignmentTurnedInIcon />
                </Box>
                <Typography variant="h6" component="div" fontWeight="500">
                  Issued
                </Typography>
                <Box
                  sx={{
                    ml: 'auto',
                    bgcolor: 'info.main15', // 15% opacity of the color
                    color: 'info.main',
                    borderRadius: '50%',
                    width: 40,
                    height: 40,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    fontWeight: 'bold'
                  }}
                >
                  <Typography variant="body1" fontWeight="bold">
                    {loading ? <CircularProgress size={20} /> :
                      idCards.filter(card => card.status === 'ISSUED').length}
                  </Typography>
                </Box>
              </Box>
              <CardContent sx={{ flexGrow: 1, p: 3, minHeight: '80px', display: 'flex', alignItems: 'center', justifyContent: 'center', textAlign: 'center' }}>
                <Typography variant="body1" color="text.secondary" sx={{ lineHeight: 1.6 }}>
                  Successfully issued to citizens
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Actions and Search Bar */}
        <Paper
          elevation={0}
          sx={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            mb: 3,
            p: 2,
            borderRadius: 2,
            bgcolor: 'rgba(0, 0, 0, 0.02)',
            border: '1px solid rgba(0, 0, 0, 0.05)'
          }}
        >
          <Button
            variant="contained"
            color="primary"
            startIcon={<AddIcon />}
            onClick={handleRegisterIDCard}
            sx={{
              borderRadius: 2,
              px: 3,
              py: 1,
              boxShadow: '0 4px 10px rgba(63, 81, 181, 0.2)',
              '&:hover': {
                boxShadow: '0 6px 15px rgba(63, 81, 181, 0.3)'
              }
            }}
          >
            Register New ID Card
          </Button>

          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Box sx={{ display: 'flex', alignItems: 'center', bgcolor: 'white', borderRadius: 2, overflow: 'hidden', boxShadow: '0 2px 8px rgba(0,0,0,0.05)', mr: 2 }}>
              <TextField
                size="small"
                placeholder="Search by card number or citizen..."
                value={searchQuery}
                onChange={handleSearchChange}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon color="action" />
                    </InputAdornment>
                  ),
                  sx: { borderRadius: 0, '& fieldset': { border: 'none' } }
                }}
                sx={{ width: 250 }}
              />
              <Button
                variant="contained"
                onClick={handleSearch}
                disabled={loading}
                sx={{
                  borderRadius: '0 8px 8px 0',
                  boxShadow: 'none',
                  height: '40px',
                  px: 2
                }}
              >
                Search
              </Button>
            </Box>
            <Button
              variant="outlined"
              startIcon={<FilterListIcon />}
              sx={{ borderRadius: 2, height: '40px', mr: 1 }}
            >
              Filter by Status
            </Button>
            <Button
              variant="contained"
              color="secondary"
              onClick={fetchIDCards}
              disabled={loading}
              sx={{ borderRadius: 2, height: '40px' }}
            >
              {loading ? 'Refreshing...' : 'Refresh List'}
            </Button>
          </Box>
        </Paper>

        {/* Error message */}
        {error && (
          <Alert
            severity="error"
            sx={{
              mb: 3,
              borderRadius: 2,
              boxShadow: '0 4px 12px rgba(211, 47, 47, 0.15)'
            }}
            action={
              <Button color="inherit" size="small" onClick={() => setError('')}>
                DISMISS
              </Button>
            }
          >
            <AlertTitle>Error Loading ID Cards</AlertTitle>
            <Typography variant="body1">{error}</Typography>
            <Typography variant="body2" sx={{ mt: 1, fontStyle: 'italic' }}>
              Try refreshing the page or check your network connection.
            </Typography>
          </Alert>
        )}

        {/* ID Cards Table */}
        <Paper
          sx={{
            width: '100%',
            overflow: 'hidden',
            borderRadius: 2,
            boxShadow: '0 4px 12px rgba(0,0,0,0.1)',
            '& .MuiTableRow-root:hover': {
              backgroundColor: 'rgba(63, 81, 181, 0.04)',
              transition: 'background-color 0.2s ease'
            },
            '& .MuiTableCell-root': {
              padding: '16px',
              borderBottom: '1px solid rgba(224, 224, 224, 0.5)'
            }
          }}
        >
          <TableContainer sx={{ maxHeight: 440 }}>
            <Table stickyHeader>
              <TableHead>
                <TableRow sx={{
                  '& th': {
                    fontWeight: 700,
                    bgcolor: 'rgba(63, 81, 181, 0.08)',
                    color: 'rgba(0, 0, 0, 0.7)',
                    fontSize: '0.875rem',
                    textTransform: 'uppercase',
                    letterSpacing: '0.5px'
                  }
                }}>
                  <TableCell>Card Number</TableCell>
                  <TableCell>Citizen</TableCell>
                  <TableCell>Issue Date</TableCell>
                  <TableCell>Expiry Date</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell>Kebele Approval</TableCell>
                  <TableCell align="right">Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {loading ? (
                  <TableRow>
                    <TableCell colSpan={7} align="center" sx={{ py: 5 }}>
                      <CircularProgress />
                      <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
                        Loading ID cards data...
                      </Typography>
                    </TableCell>
                  </TableRow>
                ) : currentIDCards.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={7} align="center" sx={{ py: 5 }}>
                      <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
                        <CreditCardIcon sx={{ fontSize: 60, color: 'text.disabled', mb: 2 }} />
                        <Typography variant="h6" color="text.secondary" gutterBottom>
                          No ID cards found
                        </Typography>
                        <Typography variant="body2" color="text.secondary" sx={{ mb: 2, maxWidth: 400, textAlign: 'center' }}>
                          {searchQuery ? 'Try adjusting your search criteria' : 'Start by registering your first ID card'}
                        </Typography>
                        <Button
                          variant="contained"
                          color="primary"
                          startIcon={<AddIcon />}
                          onClick={handleRegisterIDCard}
                          sx={{ mt: 1, borderRadius: 2 }}
                        >
                          Register New ID Card
                        </Button>
                      </Box>
                    </TableCell>
                  </TableRow>
                ) : (
                  currentIDCards.map((card) => (
                    <TableRow
                      key={card.id}
                      hover
                      sx={{
                        cursor: 'pointer',
                        transition: 'all 0.2s',
                        '&:hover': {
                          '& .action-buttons': {
                            opacity: 1
                          },
                          transform: 'translateY(-2px)',
                          boxShadow: '0 4px 8px rgba(0,0,0,0.05)'
                        }
                      }}
                      onClick={() => handleViewIDCard(card.id)}
                    >
                      <TableCell>
                        <Typography variant="body2" sx={{ fontWeight: 600, color: 'primary.main' }}>
                          {card.card_number}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body1" sx={{ fontWeight: 600 }}>
                          {card.citizen_name}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2" sx={{ fontWeight: 500 }}>
                          {new Date(card.issue_date).toLocaleDateString()}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2" sx={{ fontWeight: 500 }}>
                          {new Date(card.expiry_date).toLocaleDateString()}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={card.status}
                          color={statusColors[card.status] as any || 'default'}
                          size="small"
                          sx={{
                            fontWeight: 600,
                            borderRadius: '16px',
                            boxShadow: '0 2px 4px rgba(0,0,0,0.05)',
                            padding: '4px 0'
                          }}
                        />
                      </TableCell>
                      <TableCell>
                        <Chip
                          icon={
                            card.kebele_approval_status === 'APPROVED' ? <CheckCircleIcon /> :
                            card.kebele_approval_status === 'REJECTED' ? <CancelIcon /> :
                            <PendingIcon />
                          }
                          label={card.kebele_approval_status || 'PENDING'}
                          color={
                            card.kebele_approval_status === 'APPROVED' ? 'success' :
                            card.kebele_approval_status === 'REJECTED' ? 'error' :
                            'warning'
                          }
                          size="small"
                          sx={{
                            fontWeight: 600,
                            borderRadius: '16px',
                            boxShadow: '0 2px 4px rgba(0,0,0,0.05)',
                            padding: '4px 0'
                          }}
                        />
                      </TableCell>
                      <TableCell align="right" onClick={(e) => e.stopPropagation()}>
                        <Box
                          className="action-buttons"
                          sx={{
                            opacity: { xs: 1, md: 0 },
                            transition: 'opacity 0.2s',
                            display: 'flex',
                            justifyContent: 'flex-end'
                          }}
                        >
                          <IconButton
                            size="small"
                            onClick={() => handleViewIDCard(card.id)}
                            title="View Details"
                            sx={{
                              bgcolor: 'rgba(0, 0, 0, 0.04)',
                              mr: 1,
                              '&:hover': { bgcolor: 'rgba(0, 0, 0, 0.08)' }
                            }}
                          >
                            <VisibilityIcon fontSize="small" />
                          </IconButton>
                          {card.status === 'APPROVED' && (
                            <IconButton
                              size="small"
                              title="Print ID Card"
                              color="primary"
                              sx={{
                                bgcolor: 'rgba(63, 81, 181, 0.08)',
                                '&:hover': { bgcolor: 'rgba(63, 81, 181, 0.16)' }
                              }}
                            >
                              <PrintIcon fontSize="small" />
                            </IconButton>
                          )}
                        </Box>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </TableContainer>
          <TablePagination
            rowsPerPageOptions={[5, 10, 25]}
            component="div"
            count={filteredIDCards.length}
            rowsPerPage={rowsPerPage}
            page={page}
            onPageChange={handleChangePage}
            onRowsPerPageChange={handleChangeRowsPerPage}
          />
        </Paper>
      </Container>
    </Box>
  );
};

export default IDCardsList;
