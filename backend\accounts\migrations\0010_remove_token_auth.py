"""
Migration to remove token-based authentication tables.
"""

from django.db import migrations


class Migration(migrations.Migration):
    """
    Migration to remove token-based authentication tables.
    """

    dependencies = [
        ('accounts', '0005_user_username'),
    ]

    operations = [
        migrations.RunSQL(
            # SQL to run when applying the migration
            """
            -- Drop the accounts_expiringtoken table first
            DROP TABLE IF EXISTS accounts_expiringtoken;

            -- Then drop the authtoken_token table
            DROP TABLE IF EXISTS authtoken_token CASCADE;
            """,
            # SQL to run when unapplying the migration
            """
            -- Recreate the authtoken_token table
            CREATE TABLE IF NOT EXISTS authtoken_token (
                key varchar(40) NOT NULL PRIMARY KEY,
                created timestamp with time zone NOT NULL,
                user_id integer NOT NULL UNIQUE REFERENCES auth_user(id)
            );

            -- Recreate the accounts_expiringtoken table
            CREATE TABLE IF NOT EXISTS accounts_expiringtoken (
                token_ptr_id varchar(40) NOT NULL PRIMARY KEY REFERENCES authtoken_token(key),
                expiry timestamp with time zone NOT NULL
            );
            """
        ),
    ]
