"""
Simple view for ID card statistics.
"""
from rest_framework.decorators import api_view, authentication_classes, permission_classes
from rest_framework.authentication import TokenAuthentication
from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework.response import Response
from rest_framework import status
from django.db import connection
from .models import IDCard

@api_view(['GET'])
@authentication_classes([TokenAuthentication])
@permission_classes([AllowAny])  # We'll handle authentication manually
def idcard_statistics(request):
    """
    Get statistics for ID cards.
    """
    # Print detailed authentication information
    print("\n=== ID Card Statistics Authentication ===")
    print(f"User: {request.user}")
    print(f"Is authenticated: {request.user.is_authenticated}")
    print(f"Auth: {request.auth}")
    print(f"X-Schema-Name header: {request.headers.get('X-Schema-Name')}")
    print(f"Schema name from cookie: {request.COOKIES.get('schema_name')}")
    print(f"Effective schema name: {connection.schema_name}")
    print(f"Authorization header: {request.headers.get('Authorization')}")
    print("=" * 50)

    # Get all ID cards
    queryset = IDCard.objects.all()

    # Calculate statistics
    stats = {
        'total': queryset.count(),
        'draft': queryset.filter(status='DRAFT').count(),
        'pending': queryset.filter(status='PENDING').count(),
        'approved': queryset.filter(status='APPROVED').count(),
        'printed': queryset.filter(status='PRINTED').count(),
        'issued': queryset.filter(status='ISSUED').count(),
        'expired': queryset.filter(status='EXPIRED').count(),
        'revoked': queryset.filter(status='REVOKED').count(),
        'kebele_pending': queryset.filter(kebele_approval_status='PENDING').count(),
        'kebele_approved': queryset.filter(kebele_approval_status='APPROVED').count(),
        'kebele_rejected': queryset.filter(kebele_approval_status='REJECTED').count(),
    }

    return Response(stats)
