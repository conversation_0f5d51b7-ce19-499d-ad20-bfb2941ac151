from django.db import models


class Religion(models.Model):
    """
    Religion model for storing common religion data in the public schema.
    This data is shared across all tenants.
    """
    name = models.CharField(max_length=100, unique=True)

    class Meta:
        verbose_name = 'Religion'
        verbose_name_plural = 'Religions'
        ordering = ['name']

    def __str__(self):
        return self.name


class CitizenStatus(models.Model):
    """
    CitizenStatus model for storing common citizen status data in the public schema.
    This data is shared across all tenants.
    """
    name = models.CharField(max_length=100, unique=True)

    class Meta:
        verbose_name = 'Citizen Status'
        verbose_name_plural = 'Citizen Statuses'
        ordering = ['name']

    def __str__(self):
        return self.name


class MaritalStatus(models.Model):
    """
    MaritalStatus model for storing common marital status data in the public schema.
    This data is shared across all tenants.
    """
    id = models.CharField(max_length=20, primary_key=True)  # e.g., 'SINGLE', 'MARRIED'
    name = models.CharField(max_length=100, unique=True)

    class Meta:
        verbose_name = 'Marital Status'
        verbose_name_plural = 'Marital Statuses'
        ordering = ['name']

    def __str__(self):
        return self.name


class EmploymentType(models.Model):
    """
    EmploymentType model for storing common employment type data in the public schema.
    This data is shared across all tenants.
    """
    name = models.CharField(max_length=100, unique=True)

    class Meta:
        verbose_name = 'Employment Type'
        verbose_name_plural = 'Employment Types'
        ordering = ['name']

    def __str__(self):
        return self.name


class RelationshipType(models.Model):
    """
    RelationshipType model for storing common relationship type data in the public schema.
    This data is shared across all tenants.
    """
    name = models.CharField(max_length=100, unique=True)

    class Meta:
        verbose_name = 'Relationship Type'
        verbose_name_plural = 'Relationship Types'
        ordering = ['name']

    def __str__(self):
        return self.name


class EmployeeType(models.Model):
    """
    EmployeeType model for storing common employee type data in the public schema.
    This data is shared across all tenants.
    """
    name = models.CharField(max_length=100, unique=True)

    class Meta:
        verbose_name = 'Employee Type'
        verbose_name_plural = 'Employee Types'
        ordering = ['name']

    def __str__(self):
        return self.name


class Country(models.Model):
    """
    Country model for storing common country data in the public schema.
    This data is shared across all tenants.
    """
    name = models.CharField(max_length=100, unique=True)
    code = models.CharField(max_length=3, unique=True, help_text="ISO 3-letter country code")

    class Meta:
        verbose_name = 'Country'
        verbose_name_plural = 'Countries'
        ordering = ['name']

    def __str__(self):
        return self.name


class Region(models.Model):
    """
    Region model for storing common region data in the public schema.
    This data is shared across all tenants.
    """
    name = models.CharField(max_length=100)
    country = models.ForeignKey(Country, on_delete=models.CASCADE, related_name='regions')
    code = models.CharField(max_length=5, help_text="Region code")

    class Meta:
        verbose_name = 'Region'
        verbose_name_plural = 'Regions'
        ordering = ['country', 'name']
        unique_together = ['name', 'country']

    def __str__(self):
        return f"{self.name} ({self.country.name})"


class Ketena(models.Model):
    """
    Ketena model for storing common ketena data in the public schema.
    This data is shared across all tenants.

    Ketena is a subdivision of a kebele (the smallest administrative unit).
    """
    name = models.CharField(max_length=100)
    code = models.CharField(max_length=5, help_text="Ketena code")
    kebele_id = models.CharField(max_length=50, help_text="ID of the kebele this ketena belongs to")

    class Meta:
        verbose_name = 'Ketena'
        verbose_name_plural = 'Ketenas'
        ordering = ['name']
        unique_together = ['code', 'kebele_id']

    def __str__(self):
        return f"{self.name} (Kebele: {self.kebele_id})"


class DocumentType(models.Model):
    """
    DocumentType model for storing common document type data in the public schema.
    This data is shared across all tenants.
    """
    name = models.CharField(max_length=100, unique=True)
    description = models.TextField(blank=True, null=True)

    class Meta:
        verbose_name = 'Document Type'
        verbose_name_plural = 'Document Types'
        ordering = ['name']

    def __str__(self):
        return self.name
