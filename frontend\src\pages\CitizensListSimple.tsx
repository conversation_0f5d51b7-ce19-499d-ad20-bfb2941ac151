import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Container,
  Typography,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Button,
  IconButton,
  TextField,
  InputAdornment,
  CircularProgress,
  Alert,
  Chip,
  Avatar,
  Snackbar
} from '@mui/material';
import {
  Add as AddIcon,
  Search as SearchIcon,
  Visibility as VisibilityIcon,
  FilterList as FilterListIcon,
  Male as MaleIcon,
  Female as FemaleIcon,
  Refresh as RefreshIcon,
  CardMembership as CardMembershipIcon
} from '@mui/icons-material';

// Import services
import { tenantApi } from '../services/api';
import { API_BASE_URL } from '../config/apiConfig';

// Import components
import PageBanner from '../components/common/PageBanner';
import UnauthorizedAccess from '../components/common/UnauthorizedAccess';
import PersonIcon from '@mui/icons-material/Person';

// Define citizen interface
interface Citizen {
  id: number;
  first_name: string;
  last_name: string;
  middle_name?: string;
  gender: string;
  date_of_birth: string;
  phone?: string;
  phone_number?: string;
  id_number?: string;
  photo?: string;
}

const CitizensListSimple: React.FC = () => {
  // Navigation
  const navigate = useNavigate();

  // State
  const [citizens, setCitizens] = useState<Citizen[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [page, setPage] = useState<number>(0);
  const [rowsPerPage, setRowsPerPage] = useState<number>(10);
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [isTenantAuthorized, setIsTenantAuthorized] = useState<boolean>(true);
  const [snackbarOpen, setSnackbarOpen] = useState<boolean>(false);
  const [snackbarMessage, setSnackbarMessage] = useState<string>('');

  // Get authentication info from localStorage
  const schemaString = localStorage.getItem('schema_name');
  const schema = schemaString || '';

  // Get JWT token from localStorage
  const token = localStorage.getItem('jwt_access_token');

  // Function to show a message in the snackbar
  const showMessage = (message: string) => {
    setSnackbarMessage(message);
    setSnackbarOpen(true);
  };

  // Handle snackbar close
  const handleSnackbarClose = () => {
    setSnackbarOpen(false);
  };

  // Calculate age from date of birth
  const calculateAge = (dateOfBirth: string) => {
    const today = new Date();
    const birthDate = new Date(dateOfBirth);
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();

    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }

    return age;
  };

  // Get phone number from citizen object
  const getPhoneNumber = (citizen: Citizen) => {
    return citizen.phone || citizen.phone_number || '';
  };

  // Handle search input change
  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(event.target.value);
  };

  // Handle page change
  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };

  // Handle rows per page change
  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  // Navigate to citizen registration page
  const handleRegisterCitizen = () => {
    navigate('/citizens/new');
  };

  // Navigate to citizen details page
  const handleViewCitizen = (id: number) => {
    navigate(`/citizens/${id}`);
  };

  // Navigate to ID card registration for a specific citizen
  const handleRegisterIDCard = (citizenId: number) => {
    navigate(`/id-cards/new?citizen=${citizenId}`);
  };

  // Fetch citizens data from API
  const fetchCitizens = async () => {
    setLoading(true);
    setError(null);

    try {
      if (!schema) {
        console.error('No schema found in fetchCitizens');
        setError('No tenant selected. Please log in again.');
        setLoading(false);
        return;
      }

      console.log(`Fetching citizens with schema: ${schema}`);

      // Format schema for API endpoint (replace spaces with underscores if needed)
      const formattedSchema = schema.replace(/\s+/g, '_');

      // Ensure the schema is stored consistently
      localStorage.setItem('jwt_schema', formattedSchema);
      localStorage.setItem('schema_name', formattedSchema);

      console.log(`Using formatted schema: ${formattedSchema}`);

      // Use the tenantApi client to fetch citizens data
      console.log('Making API call to fetch citizens data');
      const responseData = await tenantApi.get<any>(
        formattedSchema,
        'citizens/?detail=true'
      );
      console.log('API call successful');

      // Handle different response formats
      let data: Citizen[] = [];
      if (responseData && typeof responseData === 'object') {
        // If the response is an object with a data property that is an array
        if ('data' in responseData && Array.isArray(responseData.data)) {
          data = responseData.data;
          console.log('Found data array in response object');
        }
        // If the response is an array directly
        else if (Array.isArray(responseData)) {
          data = responseData;
          console.log('Response is an array');
        }
        // If the response has results property that is an array (common in DRF pagination)
        else if ('results' in responseData && Array.isArray(responseData.results)) {
          data = responseData.results;
          console.log('Found results array in response object');
        }
        // If the response is an object with citizen data
        else if ('id' in responseData || 'first_name' in responseData) {
          data = [responseData];
          console.log('Response is a single citizen object, converting to array');
        }
      }

      console.log(`Fetched ${data.length} citizens`);
      setCitizens(data);
      setLoading(false);
      setError(null);
    } catch (error: any) {
      console.error('Error fetching citizens:', error);

      // Check if this is an authentication error
      if (error.response && error.response.status === 401) {
        console.warn('Authentication error (401). You may need to log in again.');
        setError('Your session has expired. Please log in again.');
      } else {
        const errorMessage = error.message || 'Unknown error';
        console.error(`Error details: ${errorMessage}`);
        setError(`Error fetching citizens: ${errorMessage}`);
      }

      setLoading(false);
    }
  };

  // Handle search submit
  const handleSearch = async () => {
    // If search is empty, fetch all citizens
    if (!searchQuery.trim()) {
      fetchCitizens();
      return;
    }

    setLoading(true);
    setError(null);

    try {
      if (!schema) {
        console.error('No schema found in handleSearch');
        setError('No tenant selected. Please log in again.');
        setLoading(false);
        return;
      }

      console.log(`Searching citizens with schema: ${schema}`);

      // Format schema for API endpoint (replace spaces with underscores if needed)
      const formattedSchema = schema.replace(/\s+/g, '_');

      // Ensure the schema is stored consistently
      localStorage.setItem('jwt_schema', formattedSchema);
      localStorage.setItem('schema_name', formattedSchema);

      console.log(`Using formatted schema: ${formattedSchema}`);

      // Use the tenantApi client to search citizens
      console.log(`Making API call to search citizens with query: "${searchQuery}"`);
      const responseData = await tenantApi.get<any>(
        formattedSchema,
        `citizens/?detail=true&search=${encodeURIComponent(searchQuery)}`
      );
      console.log('Search API call successful');

      // Handle different response formats
      let data: Citizen[] = [];
      if (responseData && typeof responseData === 'object') {
        // If the response is an object with a data property that is an array
        if ('data' in responseData && Array.isArray(responseData.data)) {
          data = responseData.data;
          console.log('Found data array in response object');
        }
        // If the response is an array directly
        else if (Array.isArray(responseData)) {
          data = responseData;
          console.log('Response is an array');
        }
        // If the response has results property that is an array (common in DRF pagination)
        else if ('results' in responseData && Array.isArray(responseData.results)) {
          data = responseData.results;
          console.log('Found results array in response object');
        }
        // If the response is an object with citizen data
        else if ('id' in responseData || 'first_name' in responseData) {
          data = [responseData];
          console.log('Response is a single citizen object, converting to array');
        }
      }

      console.log(`Found ${data.length} citizens matching search query`);
      setCitizens(data);
      setLoading(false);
      setError(null);
    } catch (error: any) {
      console.error('Error searching citizens:', error);

      // Check if this is an authentication error
      if (error.response && error.response.status === 401) {
        console.warn('Authentication error (401) during search. You may need to log in again.');
        setError('Your session has expired. Please log in again.');
      } else {
        const errorMessage = error.message || 'Unknown error';
        console.error(`Error details: ${errorMessage}`);
        setError(`Error searching citizens: ${errorMessage}`);
      }

      setLoading(false);
    }
  };

  // Fetch citizens on component mount
  useEffect(() => {
    if (schema && token) {
      console.log('Component mounted, fetching citizens data');
      fetchCitizens();
    } else {
      console.warn('Missing schema or token, cannot fetch citizens');
      setError('Missing authentication information. Please log in again.');
      setLoading(false);
    }
  }, [schema, token]);

  // Get user role and tenant type from localStorage
  const userRole = localStorage.getItem('user_role');
  const storedTenant = localStorage.getItem('tenant');
  const tenantFromStorage = storedTenant ? JSON.parse(storedTenant) : null;
  const tenantType = tenantFromStorage?.type || localStorage.getItem('tenant_type');

  // Filter citizens based on search query if there's a search query but no API search
  const filteredCitizens = searchQuery.trim() ?
    citizens.filter((citizen) => {
      const query = searchQuery.toLowerCase();
      return (
        citizen.first_name?.toLowerCase().includes(query) ||
        citizen.last_name?.toLowerCase().includes(query) ||
        citizen.id_number?.toLowerCase().includes(query) ||
        (citizen.phone && citizen.phone.toLowerCase().includes(query))
      );
    }) : citizens;

  // Get current page of citizens
  const currentCitizens = filteredCitizens.slice(
    page * rowsPerPage,
    page * rowsPerPage + rowsPerPage
  );

  // If tenant is not authorized, show unauthorized access component
  if (!isTenantAuthorized) {
    return (
      <UnauthorizedAccess
        message="Your tenant type does not have permission to register or manage citizens."
        tenantType={tenantType}
      />
    );
  }

  return (
    <Box sx={{ py: 4 }}>
      {/* Banner */}
      <PageBanner
        title="Citizens Management"
        subtitle={`Manage citizens in your center`}
        icon={<PersonIcon sx={{ fontSize: 50, color: 'white' }} />}
        actionContent={
          <Box sx={{ textAlign: 'center', width: '100%' }}>
            <Typography variant="body1" sx={{ color: 'white', fontWeight: 500 }}>
              Register, view, and manage citizens. Track citizen information and issue ID cards.
            </Typography>
          </Box>
        }
      />

      <Container maxWidth="xl">
        {/* Error message */}
        {error && (
          <Alert severity="error" sx={{ mb: 3, mt: 2 }}>
            {error}
          </Alert>
        )}

        {/* Search and actions bar */}
        <Box sx={{ mb: 3, display: 'flex', justifyContent: 'space-between', flexWrap: 'wrap', gap: 2 }}>
          <TextField
            placeholder="Search citizens..."
            variant="outlined"
            size="small"
            value={searchQuery}
            onChange={handleSearchChange}
            onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon />
                </InputAdornment>
              ),
              endAdornment: (
                <InputAdornment position="end">
                  <IconButton onClick={handleSearch} size="small">
                    <SearchIcon />
                  </IconButton>
                </InputAdornment>
              ),
              sx: { borderRadius: 2 }
            }}
            sx={{ width: { xs: '100%', sm: '300px' } }}
          />

          <Button
            variant="contained"
            color="primary"
            startIcon={<AddIcon />}
            onClick={handleRegisterCitizen}
            sx={{ borderRadius: 2, textTransform: 'none' }}
          >
            Register New Citizen
          </Button>
        </Box>

        {/* Citizens Table */}
        <Paper
          sx={{
            width: '100%',
            overflow: 'hidden',
            borderRadius: 2,
            boxShadow: '0 4px 12px rgba(0,0,0,0.1)',
            mb: 4
          }}
        >
          <Box sx={{ p: 3, borderBottom: '1px solid rgba(0, 0, 0, 0.08)' }}>
            <Typography variant="h5" gutterBottom sx={{ fontWeight: 600, color: 'text.primary' }}>
              Citizens List
            </Typography>
            <Typography variant="body2" color="text.secondary">
              View and manage citizens in the system
            </Typography>
          </Box>

          <TableContainer sx={{ maxHeight: 'calc(100vh - 300px)' }}>
            <Table stickyHeader>
              <TableHead>
                <TableRow sx={{
                  '& th': {
                    fontWeight: 'bold',
                    backgroundColor: 'rgba(0, 0, 0, 0.04)'
                  }
                }}>
                  <TableCell>Name</TableCell>
                  <TableCell>Gender</TableCell>
                  <TableCell>Age</TableCell>
                  <TableCell>ID Number</TableCell>
                  <TableCell>Phone</TableCell>
                  <TableCell align="right">Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {loading ? (
                  <TableRow>
                    <TableCell colSpan={6} align="center" sx={{ py: 5 }}>
                      <CircularProgress />
                      <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
                        Loading citizens data...
                      </Typography>
                    </TableCell>
                  </TableRow>
                ) : currentCitizens.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={6} align="center" sx={{ py: 5 }}>
                      <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
                        <PersonIcon sx={{ fontSize: 60, color: 'text.disabled', mb: 2 }} />
                        <Typography variant="h6" color="text.secondary" gutterBottom>
                          No citizens found
                        </Typography>
                        <Typography variant="body2" color="text.secondary" sx={{ mb: 2, maxWidth: 400, textAlign: 'center' }}>
                          {searchQuery ? 'Try adjusting your search criteria' : 'Start by registering your first citizen'}
                        </Typography>
                        <Button
                          variant="contained"
                          color="primary"
                          startIcon={<AddIcon />}
                          onClick={handleRegisterCitizen}
                          sx={{ mt: 1, borderRadius: 2 }}
                        >
                          Register New Citizen
                        </Button>
                      </Box>
                    </TableCell>
                  </TableRow>
                ) : (
                  currentCitizens.map((citizen) => (
                    <TableRow
                      key={citizen.id}
                      hover
                      sx={{
                        cursor: 'pointer',
                        '&:hover': {
                          backgroundColor: 'rgba(0, 0, 0, 0.04)'
                        }
                      }}
                      onClick={() => handleViewCitizen(citizen.id)}
                    >
                      <TableCell>
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          {citizen.photo ? (
                            <Avatar src={citizen.photo} alt={`${citizen.first_name} ${citizen.last_name}`} sx={{ mr: 2 }} />
                          ) : (
                            <Avatar sx={{ mr: 2, bgcolor: citizen.gender === 'Male' ? 'primary.main' : 'secondary.main' }}>
                              {citizen.gender === 'Male' ? <MaleIcon /> : <FemaleIcon />}
                            </Avatar>
                          )}
                          <Box>
                            <Typography variant="body1" sx={{ fontWeight: 600 }}>
                              {`${citizen.first_name} ${citizen.last_name}`}
                            </Typography>
                            {citizen.middle_name && (
                              <Typography variant="body2" color="text.secondary">
                                {citizen.middle_name}
                              </Typography>
                            )}
                          </Box>
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Chip
                          icon={citizen.gender === 'Male' ? <MaleIcon /> : <FemaleIcon />}
                          label={citizen.gender}
                          color={citizen.gender === 'Male' ? 'primary' : 'secondary'}
                          size="small"
                          sx={{ fontWeight: 500 }}
                        />
                      </TableCell>
                      <TableCell>
                        {calculateAge(citizen.date_of_birth)}
                      </TableCell>
                      <TableCell>
                        {citizen.id_number || 'Not assigned'}
                      </TableCell>
                      <TableCell>
                        {getPhoneNumber(citizen) || 'Not provided'}
                      </TableCell>
                      <TableCell align="right">
                        <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 1 }}>
                          <Button
                            variant="outlined"
                            size="small"
                            color="primary"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleRegisterIDCard(citizen.id);
                            }}
                            startIcon={<CardMembershipIcon />}
                            sx={{ borderRadius: 2, textTransform: 'none' }}
                          >
                            ID Card
                          </Button>
                          <IconButton
                            color="primary"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleViewCitizen(citizen.id);
                            }}
                            size="small"
                          >
                            <VisibilityIcon />
                          </IconButton>
                        </Box>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </TableContainer>

          {/* Pagination */}
          <TablePagination
            rowsPerPageOptions={[10, 25, 50, 100]}
            component="div"
            count={filteredCitizens.length}
            rowsPerPage={rowsPerPage}
            page={page}
            onPageChange={handleChangePage}
            onRowsPerPageChange={handleChangeRowsPerPage}
          />
        </Paper>
      </Container>

      {/* Snackbar for messages */}
      <Snackbar
        open={snackbarOpen}
        autoHideDuration={6000}
        onClose={handleSnackbarClose}
        message={snackbarMessage}
      />
    </Box>
  );
};

export default CitizensListSimple;
