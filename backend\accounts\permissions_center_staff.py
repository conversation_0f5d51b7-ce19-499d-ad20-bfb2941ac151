from rest_framework import permissions

class IsCenterStaffOr<PERSON>igher(permissions.BasePermission):
    """
    Permission class that allows CENTER_STAFF or higher roles to access resources.
    
    This permission allows:
    - CENTER_STAFF
    - KEBELE_LEADER
    - CENTER_ADMIN
    - SUBCITY_ADMIN
    - CITY_ADMIN
    - SUPER_ADMIN
    
    to access the protected resources.
    """
    
    def has_permission(self, request, view):
        # Check if the user is authenticated
        if not request.user.is_authenticated:
            return False
            
        # Check if the user has one of the allowed roles
        return (
            request.user.is_center_staff or
            request.user.is_kebele_leader or
            request.user.is_center_admin or
            request.user.is_subcity_admin or
            request.user.is_city_admin or
            request.user.is_super_admin
        )
