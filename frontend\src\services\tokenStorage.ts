/**
 * Token Storage Service
 *
 * This service provides a centralized way to store and retrieve tokens.
 * It implements a clear hierarchy for token storage:
 * 1. Primary: HttpOnly cookies for refresh tokens, localStorage for access tokens
 * 2. Secondary: In-memory tokenStore object as a cache
 * 3. Fallback: Additional localStorage keys with schema-specific names
 */

// In-memory token store for quick access
interface TokenStore {
  [schema: string]: {
    accessToken: string;
    refreshToken: string;
    expiresAt?: number;
  };
}

// Initialize the token store
const tokenStore: TokenStore = {};

// Constants for token storage
const TOKEN_KEYS = {
  ACCESS_TOKEN: 'jwt_access_token',
  REFRESH_TOKEN: 'jwt_refresh_token',
  CURRENT_SCHEMA: 'current_schema',
  SCHEMA_NAME: 'schema_name',  // Alternative key, kept for compatibility
  JWT_SCHEMA: 'jwt_schema',    // Alternative key, kept for compatibility
};

/**
 * Format a schema name for consistent storage
 * @param schema The schema name to format
 * @returns The formatted schema name
 */
export const formatSchema = (schema: string): string => {
  if (!schema) return '';
  return schema.replace(/\s+/g, '_');
};

/**
 * Get the current schema
 * @returns The current schema or null if not found
 */
export const getCurrentSchema = (): string | null => {
  // Try to get the schema from the primary location
  const schema = localStorage.getItem(TOKEN_KEYS.CURRENT_SCHEMA);
  if (schema) return schema;

  // Fall back to legacy locations
  const legacySchema = localStorage.getItem(TOKEN_KEYS.JWT_SCHEMA) ||
                       localStorage.getItem(TOKEN_KEYS.SCHEMA_NAME);
  if (legacySchema) {
    // Migrate to the new location
    setCurrentSchema(legacySchema);
    return legacySchema;
  }

  return null;
};

/**
 * Set the current schema
 * @param schema The schema to set as current
 */
export const setCurrentSchema = (schema: string): void => {
  if (!schema) return;

  const formattedSchema = formatSchema(schema);

  // Store in primary location
  localStorage.setItem(TOKEN_KEYS.CURRENT_SCHEMA, formattedSchema);

  // Also update legacy locations for compatibility
  localStorage.setItem(TOKEN_KEYS.JWT_SCHEMA, formattedSchema);
  localStorage.setItem(TOKEN_KEYS.SCHEMA_NAME, formattedSchema);

  console.log(`Current schema set to: ${formattedSchema}`);
};

/**
 * Get the schema-specific key for a token
 * @param baseKey The base key (e.g., 'jwt_access_token')
 * @param schema The schema name
 * @returns The schema-specific key
 */
const getSchemaKey = (baseKey: string, schema: string): string => {
  const formattedSchema = formatSchema(schema);
  return `${baseKey}_${formattedSchema}`;
};

/**
 * Get an access token for a schema
 * @param schema The schema name (optional, uses current schema if not provided)
 * @returns The access token or null if not found
 */
export const getAccessToken = (schema?: string): string | null => {
  // Get the schema to use
  const schemaToUse = schema || getCurrentSchema();
  if (!schemaToUse) return null;

  const formattedSchema = formatSchema(schemaToUse);

  // Check in-memory store first (fastest)
  if (tokenStore[formattedSchema]?.accessToken) {
    return tokenStore[formattedSchema].accessToken;
  }

  // Check localStorage for schema-specific token
  const schemaToken = localStorage.getItem(getSchemaKey(TOKEN_KEYS.ACCESS_TOKEN, formattedSchema));
  if (schemaToken) {
    // Cache in memory for faster access next time
    if (!tokenStore[formattedSchema]) {
      tokenStore[formattedSchema] = { accessToken: '', refreshToken: '' };
    }
    tokenStore[formattedSchema].accessToken = schemaToken;
    return schemaToken;
  }

  // Check localStorage for current token (if this is the current schema)
  if (formattedSchema === formatSchema(getCurrentSchema() || '')) {
    const currentToken = localStorage.getItem(TOKEN_KEYS.ACCESS_TOKEN);
    if (currentToken) {
      // Cache in memory for faster access next time
      if (!tokenStore[formattedSchema]) {
        tokenStore[formattedSchema] = { accessToken: '', refreshToken: '' };
      }
      tokenStore[formattedSchema].accessToken = currentToken;
      return currentToken;
    }
  }

  return null;
};

/**
 * Get a refresh token for a schema
 * @param schema The schema name (optional, uses current schema if not provided)
 * @returns The refresh token or null if not found
 */
export const getRefreshToken = (schema?: string): string | null => {
  // Import the tokenManager
  import('./tokenManager').then(({ tokenManager }) => {
    // Synchronize cookies to ensure they're available
    tokenManager.synchronizeCookies();
  }).catch(error => {
    console.error('Error importing tokenManager:', error);
  });

  // Get the schema to use
  const schemaToUse = schema || getCurrentSchema();
  if (!schemaToUse) {
    console.log('getRefreshToken: No schema provided or current schema found');
    return null;
  }

  const formattedSchema = formatSchema(schemaToUse);
  console.log(`getRefreshToken: Looking for refresh token for schema: ${formattedSchema}`);

  // Check for cookies first (not directly accessible via JS)
  // We can only check if they exist, not their values
  const hasJwtRefreshTokenCookie = document.cookie.includes(TOKEN_KEYS.REFRESH_TOKEN);
  const hasRefreshTokenCookie = document.cookie.includes('refresh_token');
  const hasCookie = hasJwtRefreshTokenCookie || hasRefreshTokenCookie;

  console.log('getRefreshToken: Cookie check:');
  console.log(`- jwt_refresh_token cookie: ${hasJwtRefreshTokenCookie ? 'Present' : 'Missing'}`);
  console.log(`- refresh_token cookie: ${hasRefreshTokenCookie ? 'Present' : 'Missing'}`);

  // Check in-memory store
  if (tokenStore[formattedSchema]?.refreshToken) {
    console.log('getRefreshToken: Found token in memory store');
    return tokenStore[formattedSchema].refreshToken;
  }

  // Check localStorage for schema-specific token
  const schemaToken = localStorage.getItem(getSchemaKey(TOKEN_KEYS.REFRESH_TOKEN, formattedSchema));
  if (schemaToken) {
    console.log(`getRefreshToken: Found token in localStorage with key: ${getSchemaKey(TOKEN_KEYS.REFRESH_TOKEN, formattedSchema)}`);
    // Cache in memory for faster access next time
    if (!tokenStore[formattedSchema]) {
      tokenStore[formattedSchema] = { accessToken: '', refreshToken: '' };
    }
    tokenStore[formattedSchema].refreshToken = schemaToken;
    return schemaToken;
  }

  // Check localStorage for current token (if this is the current schema)
  if (formattedSchema === formatSchema(getCurrentSchema() || '')) {
    const currentToken = localStorage.getItem(TOKEN_KEYS.REFRESH_TOKEN);
    if (currentToken) {
      console.log(`getRefreshToken: Found token in localStorage with key: ${TOKEN_KEYS.REFRESH_TOKEN}`);
      // Cache in memory for faster access next time
      if (!tokenStore[formattedSchema]) {
        tokenStore[formattedSchema] = { accessToken: '', refreshToken: '' };
      }
      tokenStore[formattedSchema].refreshToken = currentToken;
      return currentToken;
    }
  }

  // If we have a cookie but no token in storage, return a placeholder
  // This indicates that we should use the cookie for refresh
  if (hasCookie) {
    console.log('getRefreshToken: No token in storage, but found cookie. Using placeholder.');
    if (hasRefreshTokenCookie) {
      return 'REFRESH_TOKEN_COOKIE';
    } else {
      return 'HTTPONLY_COOKIE';
    }
  }

  console.log('getRefreshToken: No refresh token found in any location');
  return null;
};

/**
 * Set an access token for a schema
 * @param token The access token to set
 * @param schema The schema name (optional, uses current schema if not provided)
 */
export const setAccessToken = (token: string, schema?: string): void => {
  if (!token) return;

  // Get the schema to use
  const schemaToUse = schema || getCurrentSchema();
  if (!schemaToUse) {
    console.error('Cannot set access token: no schema provided or current schema found');
    return;
  }

  const formattedSchema = formatSchema(schemaToUse);

  // Store in memory
  if (!tokenStore[formattedSchema]) {
    tokenStore[formattedSchema] = { accessToken: '', refreshToken: '' };
  }
  tokenStore[formattedSchema].accessToken = token;

  // Store in localStorage (schema-specific)
  localStorage.setItem(getSchemaKey(TOKEN_KEYS.ACCESS_TOKEN, formattedSchema), token);

  // If this is the current schema, also store in the current token key
  if (formattedSchema === formatSchema(getCurrentSchema() || '')) {
    localStorage.setItem(TOKEN_KEYS.ACCESS_TOKEN, token);
  }

  console.log(`Access token set for schema: ${formattedSchema}`);
};

/**
 * Set a refresh token for a schema
 * @param token The refresh token to set
 * @param schema The schema name (optional, uses current schema if not provided)
 * @param setCookie Whether to set an HttpOnly cookie (default: true)
 */
export const setRefreshToken = (token: string, schema?: string, setCookie: boolean = true): void => {
  if (!token) return;

  // Get the schema to use
  const schemaToUse = schema || getCurrentSchema();
  if (!schemaToUse) {
    console.error('Cannot set refresh token: no schema provided or current schema found');
    return;
  }

  const formattedSchema = formatSchema(schemaToUse);

  // Store in memory
  if (!tokenStore[formattedSchema]) {
    tokenStore[formattedSchema] = { accessToken: '', refreshToken: '' };
  }
  tokenStore[formattedSchema].refreshToken = token;

  // Store in localStorage (schema-specific)
  localStorage.setItem(getSchemaKey(TOKEN_KEYS.REFRESH_TOKEN, formattedSchema), token);

  // If this is the current schema, also store in the current token key
  if (formattedSchema === formatSchema(getCurrentSchema() || '')) {
    localStorage.setItem(TOKEN_KEYS.REFRESH_TOKEN, token);
  }

  // Use TokenManager to store the token in all locations
  import('./tokenManager').then(({ tokenManager }) => {
    tokenManager.storeRefreshToken(token, formattedSchema);
  }).catch(error => {
    console.error('Error importing tokenManager:', error);

    // Fallback to traditional cookie setting if TokenManager fails
    if (setCookie) {
      // Set cookie with a long expiration (30 days)
      const expirationDate = new Date();
      expirationDate.setDate(expirationDate.getDate() + 30);

      document.cookie = `${TOKEN_KEYS.REFRESH_TOKEN}=${encodeURIComponent(token)}; path=/; expires=${expirationDate.toUTCString()}; SameSite=Lax`;
      document.cookie = `refresh_token=${encodeURIComponent(token)}; path=/; expires=${expirationDate.toUTCString()}; SameSite=Lax`;
      console.log('Refresh token cookies set with 30-day expiration (fallback method)');
    }
  });

  console.log(`Refresh token set for schema: ${formattedSchema}`);
};

/**
 * Clear tokens for a schema
 * @param schema The schema name (optional, uses current schema if not provided)
 */
export const clearTokens = (schema?: string): void => {
  // Get the schema to use
  const schemaToUse = schema || getCurrentSchema();
  if (!schemaToUse) return;

  const formattedSchema = formatSchema(schemaToUse);

  // Clear from memory
  delete tokenStore[formattedSchema];

  // Clear from localStorage (schema-specific)
  localStorage.removeItem(getSchemaKey(TOKEN_KEYS.ACCESS_TOKEN, formattedSchema));
  localStorage.removeItem(getSchemaKey(TOKEN_KEYS.REFRESH_TOKEN, formattedSchema));

  // If this is the current schema, also clear the current token keys
  if (formattedSchema === formatSchema(getCurrentSchema() || '')) {
    localStorage.removeItem(TOKEN_KEYS.ACCESS_TOKEN);
    localStorage.removeItem(TOKEN_KEYS.REFRESH_TOKEN);
  }

  // Use TokenManager to clear all tokens
  import('./tokenManager').then(({ tokenManager }) => {
    tokenManager.clearAllTokens();
  }).catch(error => {
    console.error('Error importing tokenManager:', error);

    // Fallback to traditional cookie clearing if TokenManager fails
    document.cookie = `${TOKEN_KEYS.REFRESH_TOKEN}=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT; SameSite=Lax`;
    document.cookie = `refresh_token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT; SameSite=Lax`;
  });

  console.log(`Tokens cleared for schema: ${formattedSchema}`);
};

/**
 * Check if tokens exist for a schema
 * @param schema The schema name (optional, uses current schema if not provided)
 * @returns Whether tokens exist for the schema
 */
export const hasTokens = (schema?: string): boolean => {
  return !!getAccessToken(schema) && !!getRefreshToken(schema);
};

/**
 * Get authentication headers for a schema
 * @param schema The schema name (optional, uses current schema if not provided)
 * @returns Headers object with authentication
 */
export const getAuthHeaders = (schema?: string): Record<string, string> => {
  const headers: Record<string, string> = {
    'Content-Type': 'application/json',
  };

  // Get the schema to use
  const schemaToUse = schema || getCurrentSchema();
  if (!schemaToUse) return headers;

  const formattedSchema = formatSchema(schemaToUse);

  // Add schema header
  headers['X-Schema-Name'] = formattedSchema;

  // Add authorization header if we have an access token
  const accessToken = getAccessToken(formattedSchema);
  if (accessToken) {
    // Ensure the token is using the Bearer prefix
    const tokenWithPrefix = accessToken.startsWith('Bearer ') ? accessToken : `Bearer ${accessToken}`;
    headers['Authorization'] = tokenWithPrefix;
    console.log('TokenStorage: Added Authorization header with Bearer token');
  }

  return headers;
};

// Export the token store for debugging
export const getTokenStore = (): TokenStore => {
  return { ...tokenStore };
};

// Debug utilities
export const debugTokenStorage = () => {
  const currentSchema = getCurrentSchema();
  const allSchemas: string[] = [];

  // Find all schema-specific tokens
  for (const key of Object.keys(localStorage)) {
    if (key.startsWith('jwt_access_token_') || key.startsWith('jwt_refresh_token_')) {
      const schema = key.split('_').slice(3).join('_');
      if (schema && !allSchemas.includes(schema)) {
        allSchemas.push(schema);
      }
    }
  }

  console.group('Token Storage Debug Information');
  console.log('Current Schema:', currentSchema);
  console.log('All Schemas:', allSchemas);
  console.log('In-Memory Token Store:', getTokenStore());

  console.group('Schema Details');
  allSchemas.forEach(schema => {
    const accessToken = getAccessToken(schema);
    const refreshToken = getRefreshToken(schema);
    console.group(`Schema: ${schema}`);
    console.log('Access Token:', accessToken ? `${accessToken.substring(0, 10)}...` : 'None');
    console.log('Refresh Token:', refreshToken ? `${refreshToken.substring(0, 10)}...` : 'None');
    console.log('Has Cookie:', document.cookie.includes('jwt_refresh_token'));
    console.groupEnd();
  });
  console.groupEnd();

  console.group('Storage Keys');
  console.log('localStorage Keys:');
  Object.keys(localStorage).forEach(key => {
    if (key.includes('jwt') || key.includes('token') || key.includes('schema')) {
      const value = localStorage.getItem(key);
      console.log(`- ${key}: ${value ? `${value.substring(0, 10)}...` : 'None'}`);
    }
  });
  console.groupEnd();

  console.groupEnd();

  return {
    currentSchema,
    allSchemas,
    tokenStore: getTokenStore(),
  };
};

// Add debug utilities to window for easy access
if (typeof window !== 'undefined') {
  (window as any).tokenDebug = {
    ...(window as any).tokenDebug || {},
    diagnose: debugTokenStorage,
    fix: () => {
      const currentSchema = getCurrentSchema();
      if (!currentSchema) {
        console.error('No current schema found, cannot fix tokens');
        return;
      }

      // Find all tokens for the current schema
      const accessToken = getAccessToken(currentSchema);
      const refreshToken = getRefreshToken(currentSchema);

      if (!accessToken || !refreshToken) {
        console.error('Missing tokens for current schema, cannot fix');
        return;
      }

      // Clear all tokens first
      for (const key of Object.keys(localStorage)) {
        if (key.startsWith('jwt_access_token_') || key.startsWith('jwt_refresh_token_')) {
          localStorage.removeItem(key);
        }
      }

      // Set tokens using the centralized storage
      setCurrentSchema(currentSchema);
      setAccessToken(accessToken, currentSchema);
      setRefreshToken(refreshToken, currentSchema);

      console.log('Token storage fixed for schema:', currentSchema);
      debugTokenStorage();
    },
    showToken: (schema?: string) => {
      const schemaToUse = schema || getCurrentSchema();
      if (!schemaToUse) {
        console.error('No schema provided or current schema found');
        return;
      }

      const accessToken = getAccessToken(schemaToUse);
      if (!accessToken) {
        console.error('No access token found for schema:', schemaToUse);
        return;
      }

      try {
        // JWT tokens are in the format header.payload.signature
        const parts = accessToken.split('.');
        if (parts.length !== 3) {
          console.error('Invalid JWT token format');
          return;
        }

        // Decode the payload (middle part)
        const payload = JSON.parse(atob(parts[1]));
        console.group('JWT Token Information');
        console.log('Schema:', schemaToUse);
        console.log('Payload:', payload);

        // Check expiration
        if (payload.exp) {
          const expirationDate = new Date(payload.exp * 1000);
          const now = new Date();
          const isExpired = expirationDate < now;
          console.log('Expiration:', expirationDate.toLocaleString());
          console.log('Is Expired:', isExpired);
          console.log('Time Remaining:', isExpired ? 'Expired' : `${Math.floor((expirationDate.getTime() - now.getTime()) / 1000)} seconds`);
        }

        console.groupEnd();
        return payload;
      } catch (error) {
        console.error('Error decoding JWT token:', error);
      }
    }
  };
}

export default {
  getCurrentSchema,
  setCurrentSchema,
  getAccessToken,
  getRefreshToken,
  setAccessToken,
  setRefreshToken,
  clearTokens,
  hasTokens,
  getAuthHeaders,
  formatSchema,
  getTokenStore,
  debugTokenStorage,
};
