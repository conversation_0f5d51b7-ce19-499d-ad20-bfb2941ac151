import os
import django

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'neocamelot.settings')
django.setup()

# Import models and admin
from django.contrib import admin
from citizens.models import Citizen
from idcards.models import IDCard, IDCardTemplate
from centers.models import Client
from django_tenants.utils import tenant_context

print("Creating global admin classes...")

# Create a global admin class for Citizen that works across all tenants
class GlobalCitizenAdmin(admin.ModelAdmin):
    list_display = ('schema_name', 'registration_number', 'first_name', 'last_name', 'gender', 'date_of_birth', 'is_active')
    list_filter = ('is_active', 'gender', 'nationality')
    search_fields = ('first_name', 'last_name', 'registration_number', 'id_number', 'email', 'phone')
    readonly_fields = ('schema_name', 'registration_number', 'id_number', 'created_at', 'updated_at', 'created_by')

    def get_queryset(self, request):
        # Get all tenants
        tenants = Client.objects.exclude(schema_name='public')

        # Initialize an empty list to store citizens from all tenants
        all_citizens = []

        # Iterate through each tenant and get their citizens
        for tenant in tenants:
            try:
                with tenant_context(tenant):
                    # Get citizens for this tenant
                    citizens = list(Citizen.objects.all())

                    # Add schema_name attribute to each citizen
                    for citizen in citizens:
                        citizen.schema_name = tenant.schema_name

                    # Add citizens to the list
                    all_citizens.extend(citizens)
            except Exception as e:
                print(f"Error accessing tenant {tenant.schema_name}: {str(e)}")

        # Return a queryset-like object
        return all_citizens

    def schema_name(self, obj):
        return obj.schema_name if hasattr(obj, 'schema_name') else 'Unknown'
    schema_name.short_description = 'Tenant'

    def has_add_permission(self, request):
        return False

    def has_change_permission(self, request, obj=None):
        return False

    def has_delete_permission(self, request, obj=None):
        return False

# Create a global admin class for IDCardTemplate that works across all tenants
class GlobalIDCardTemplateAdmin(admin.ModelAdmin):
    list_display = ('schema_name', 'name', 'is_default', 'created_at')
    list_filter = ('is_default',)
    search_fields = ('name',)
    readonly_fields = ('schema_name', 'created_at', 'updated_at')

    def get_queryset(self, request):
        # Get all tenants
        tenants = Client.objects.exclude(schema_name='public')

        # Initialize an empty list to store templates from all tenants
        all_templates = []

        # Iterate through each tenant and get their templates
        for tenant in tenants:
            try:
                with tenant_context(tenant):
                    # Get templates for this tenant
                    templates = list(IDCardTemplate.objects.all())

                    # Add schema_name attribute to each template
                    for template in templates:
                        template.schema_name = tenant.schema_name

                    # Add templates to the list
                    all_templates.extend(templates)
            except Exception as e:
                print(f"Error accessing tenant {tenant.schema_name}: {str(e)}")

        # Return a queryset-like object
        return all_templates

    def schema_name(self, obj):
        return obj.schema_name if hasattr(obj, 'schema_name') else 'Unknown'
    schema_name.short_description = 'Tenant'

    def has_add_permission(self, request):
        return False

    def has_change_permission(self, request, obj=None):
        return False

    def has_delete_permission(self, request, obj=None):
        return False

# Create a global admin class for IDCard that works across all tenants
class GlobalIDCardAdmin(admin.ModelAdmin):
    list_display = ('schema_name', 'card_number', 'citizen_name', 'issue_date', 'expiry_date', 'status')
    list_filter = ('status', 'issue_date')
    search_fields = ('card_number',)
    readonly_fields = ('schema_name', 'card_number', 'created_at', 'updated_at', 'created_by', 'approved_by')

    def get_queryset(self, request):
        # Get all tenants
        tenants = Client.objects.exclude(schema_name='public')

        # Initialize an empty list to store ID cards from all tenants
        all_cards = []

        # Iterate through each tenant and get their ID cards
        for tenant in tenants:
            try:
                with tenant_context(tenant):
                    # Get ID cards for this tenant
                    cards = list(IDCard.objects.all())

                    # Add schema_name attribute to each card
                    for card in cards:
                        card.schema_name = tenant.schema_name

                    # Add cards to the list
                    all_cards.extend(cards)
            except Exception as e:
                print(f"Error accessing tenant {tenant.schema_name}: {str(e)}")

        # Return a queryset-like object
        return all_cards

    def schema_name(self, obj):
        return obj.schema_name if hasattr(obj, 'schema_name') else 'Unknown'
    schema_name.short_description = 'Tenant'

    def citizen_name(self, obj):
        try:
            return f"{obj.citizen.first_name} {obj.citizen.last_name}" if obj.citizen else 'Unknown'
        except Exception:
            return f"Unknown (ID: {obj.citizen_id if hasattr(obj, 'citizen_id') else 'N/A'})"
    citizen_name.short_description = 'Citizen'

    def has_add_permission(self, request):
        return False

    def has_change_permission(self, request, obj=None):
        return False

    def has_delete_permission(self, request, obj=None):
        return False

print("Updating citizens/admin.py...")

# Create citizens/admin.py content
citizens_admin_content = """from django.contrib import admin
from .models import Citizen
from centers.models import Client
from django_tenants.utils import tenant_context

# Create a tenant-specific admin class for Citizen
class CitizenAdmin(admin.ModelAdmin):
    list_display = ('registration_number', 'first_name', 'last_name', 'gender', 'date_of_birth',
                    'is_active', 'created_at')
    list_filter = ('is_active', 'gender', 'nationality')
    search_fields = ('first_name', 'last_name', 'registration_number', 'id_number', 'email', 'phone')
    readonly_fields = ('registration_number', 'id_number', 'created_at', 'updated_at', 'created_by')

    fieldsets = (
        ('Basic Information', {
            'fields': ('center', 'registration_number', 'id_number', 'first_name', 'last_name',
                      'date_of_birth', 'gender', 'nationality')
        }),
        ('Contact Information', {
            'fields': ('address', 'phone', 'email')
        }),
        ('Additional Information', {
            'fields': ('occupation', 'photo')
        }),
        ('Status', {
            'fields': ('is_active',)
        }),
        ('System Information', {
            'fields': ('created_at', 'updated_at', 'created_by'),
            'classes': ('collapse',)
        }),
    )

    def save_model(self, request, obj, form, change):
        if not change:  # Only set created_by when creating a new object
            obj.created_by = request.user
        super().save_model(request, obj, form, change)

# Create a global admin class for Citizen that works across all tenants
class GlobalCitizenAdmin(admin.ModelAdmin):
    list_display = ('schema_name', 'registration_number', 'first_name', 'last_name', 'gender', 'date_of_birth', 'is_active')
    list_filter = ('is_active', 'gender', 'nationality')
    search_fields = ('first_name', 'last_name', 'registration_number', 'id_number', 'email', 'phone')
    readonly_fields = ('schema_name', 'registration_number', 'id_number', 'created_at', 'updated_at', 'created_by')

    def get_queryset(self, request):
        # Get all tenants
        tenants = Client.objects.exclude(schema_name='public')

        # Initialize an empty list to store citizens from all tenants
        all_citizens = []

        # Iterate through each tenant and get their citizens
        for tenant in tenants:
            try:
                with tenant_context(tenant):
                    # Get citizens for this tenant
                    citizens = list(Citizen.objects.all())

                    # Add schema_name attribute to each citizen
                    for citizen in citizens:
                        citizen.schema_name = tenant.schema_name

                    # Add citizens to the list
                    all_citizens.extend(citizens)
            except Exception as e:
                print(f"Error accessing tenant {{tenant.schema_name}}: {{str(e)}}")

        # Return a queryset-like object
        return all_citizens

    def schema_name(self, obj):
        return obj.schema_name if hasattr(obj, 'schema_name') else 'Unknown'
    schema_name.short_description = 'Tenant'

    def has_add_permission(self, request):
        return False

    def has_change_permission(self, request, obj=None):
        return False

    def has_delete_permission(self, request, obj=None):
        return False

# Register the models with the admin site
try:
    admin.site.unregister(Citizen)
except admin.sites.NotRegistered:
    pass

# Register with the appropriate admin class based on the current schema
from django.db import connection
if connection.schema_name == 'public':
    admin.site.register(Citizen, GlobalCitizenAdmin)
else:
    admin.site.register(Citizen, CitizenAdmin)
"""

# Save the citizens/admin.py file
with open('citizens/admin.py', 'w') as f:
    f.write(citizens_admin_content)

print("Updating idcards/admin.py...")

# Create idcards/admin.py content
idcards_admin_content = """from django.contrib import admin
from .models import IDCard, IDCardTemplate
from centers.models import Client
from django_tenants.utils import tenant_context

# Create tenant-specific admin classes
class IDCardTemplateAdmin(admin.ModelAdmin):
    list_display = ('name', 'is_default', 'created_at')
    list_filter = ('is_default',)
    search_fields = ('name',)
    readonly_fields = ('created_at', 'updated_at')

    fieldsets = (
        (None, {
            'fields': ('name', 'center', 'is_default', 'background_image')
        }),
        ('Layout Configuration', {
            'fields': ('front_layout', 'back_layout'),
            'classes': ('collapse',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

class IDCardAdmin(admin.ModelAdmin):
    list_display = ('card_number', 'citizen', 'template', 'issue_date', 'expiry_date', 'status')
    list_filter = ('status', 'issue_date')
    search_fields = ('card_number', 'citizen__first_name', 'citizen__last_name', 'citizen__registration_number')
    readonly_fields = ('card_number', 'created_at', 'updated_at', 'created_by', 'approved_by')

    fieldsets = (
        ('Card Information', {
            'fields': ('citizen', 'template', 'card_number', 'issue_date', 'expiry_date', 'status')
        }),
        ('Card Data', {
            'fields': ('card_data',),
            'classes': ('collapse',)
        }),
        ('System Information', {
            'fields': ('created_at', 'updated_at', 'created_by', 'approved_by'),
            'classes': ('collapse',)
        }),
    )

    def save_model(self, request, obj, form, change):
        if not change:  # Only set created_by when creating a new object
            obj.created_by = request.user
        super().save_model(request, obj, form, change)

# Create global admin classes
class GlobalIDCardTemplateAdmin(admin.ModelAdmin):
    list_display = ('schema_name', 'name', 'is_default', 'created_at')
    list_filter = ('is_default',)
    search_fields = ('name',)
    readonly_fields = ('schema_name', 'created_at', 'updated_at')

    def get_queryset(self, request):
        # Get all tenants
        tenants = Client.objects.exclude(schema_name='public')

        # Initialize an empty list to store templates from all tenants
        all_templates = []

        # Iterate through each tenant and get their templates
        for tenant in tenants:
            try:
                with tenant_context(tenant):
                    # Get templates for this tenant
                    templates = list(IDCardTemplate.objects.all())

                    # Add schema_name attribute to each template
                    for template in templates:
                        template.schema_name = tenant.schema_name

                    # Add templates to the list
                    all_templates.extend(templates)
            except Exception as e:
                print(f"Error accessing tenant {{tenant.schema_name}}: {{str(e)}}")

        # Return a queryset-like object
        return all_templates

    def schema_name(self, obj):
        return obj.schema_name if hasattr(obj, 'schema_name') else 'Unknown'
    schema_name.short_description = 'Tenant'

    def has_add_permission(self, request):
        return False

    def has_change_permission(self, request, obj=None):
        return False

    def has_delete_permission(self, request, obj=None):
        return False

class GlobalIDCardAdmin(admin.ModelAdmin):
    list_display = ('schema_name', 'card_number', 'citizen_name', 'issue_date', 'expiry_date', 'status')
    list_filter = ('status', 'issue_date')
    search_fields = ('card_number',)
    readonly_fields = ('schema_name', 'card_number', 'created_at', 'updated_at', 'created_by', 'approved_by')

    def get_queryset(self, request):
        # Get all tenants
        tenants = Client.objects.exclude(schema_name='public')

        # Initialize an empty list to store ID cards from all tenants
        all_cards = []

        # Iterate through each tenant and get their ID cards
        for tenant in tenants:
            try:
                with tenant_context(tenant):
                    # Get ID cards for this tenant
                    cards = list(IDCard.objects.all())

                    # Add schema_name attribute to each card
                    for card in cards:
                        card.schema_name = tenant.schema_name

                    # Add cards to the list
                    all_cards.extend(cards)
            except Exception as e:
                print(f"Error accessing tenant {{tenant.schema_name}}: {{str(e)}}")

        # Return a queryset-like object
        return all_cards

    def schema_name(self, obj):
        return obj.schema_name if hasattr(obj, 'schema_name') else 'Unknown'
    schema_name.short_description = 'Tenant'

    def citizen_name(self, obj):
        try:
            return f"{{obj.citizen.first_name}} {{obj.citizen.last_name}}" if obj.citizen else 'Unknown'
        except Exception:
            return f"Unknown (ID: {{obj.citizen_id if hasattr(obj, 'citizen_id') else 'N/A'}})"
    citizen_name.short_description = 'Citizen'

    def has_add_permission(self, request):
        return False

    def has_change_permission(self, request, obj=None):
        return False

    def has_delete_permission(self, request, obj=None):
        return False

# Unregister models if they are already registered
try:
    admin.site.unregister(IDCardTemplate)
except admin.sites.NotRegistered:
    pass

try:
    admin.site.unregister(IDCard)
except admin.sites.NotRegistered:
    pass

# Register with the appropriate admin class based on the current schema
from django.db import connection
if connection.schema_name == 'public':
    admin.site.register(IDCardTemplate, GlobalIDCardTemplateAdmin)
    admin.site.register(IDCard, GlobalIDCardAdmin)
else:
    admin.site.register(IDCardTemplate, IDCardTemplateAdmin)
    admin.site.register(IDCard, IDCardAdmin)
"""

# Save the idcards/admin.py file
with open('idcards/admin.py', 'w') as f:
    f.write(idcards_admin_content)

print("Admin files updated successfully!")

# Unregister models if they are already registered
try:
    admin.site.unregister(Citizen)
    print("Unregistered Citizen model")
except admin.sites.NotRegistered:
    print("Citizen model was not registered")

try:
    admin.site.unregister(IDCardTemplate)
    print("Unregistered IDCardTemplate model")
except admin.sites.NotRegistered:
    print("IDCardTemplate model was not registered")

try:
    admin.site.unregister(IDCard)
    print("Unregistered IDCard model")
except admin.sites.NotRegistered:
    print("IDCard model was not registered")

# Register models with global admin classes
admin.site.register(Citizen, GlobalCitizenAdmin)
print("Registered Citizen model with GlobalCitizenAdmin")

admin.site.register(IDCardTemplate, GlobalIDCardTemplateAdmin)
print("Registered IDCardTemplate model with GlobalIDCardTemplateAdmin")

admin.site.register(IDCard, GlobalIDCardAdmin)
print("Registered IDCard model with GlobalIDCardAdmin")

# Test the global admin classes
print("\nTesting GlobalCitizenAdmin...")
citizen_admin = GlobalCitizenAdmin(Citizen, admin.site)
citizens = citizen_admin.get_queryset(None)
print(f"Found {len(citizens)} citizens across all tenants")

print("\nTesting GlobalIDCardTemplateAdmin...")
template_admin = GlobalIDCardTemplateAdmin(IDCardTemplate, admin.site)
templates = template_admin.get_queryset(None)
print(f"Found {len(templates)} ID card templates across all tenants")

print("\nTesting GlobalIDCardAdmin...")
card_admin = GlobalIDCardAdmin(IDCard, admin.site)
cards = card_admin.get_queryset(None)
print(f"Found {len(cards)} ID cards across all tenants")

print("\nDone! The models are now registered in the admin panel.")
print("You can now access citizens, ID cards, and ID card templates in the admin panel.")
print("Please restart the Django server for the changes to take effect.")
