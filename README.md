# NeoCamelot - Digital ID Card System

NeoCamelot is a multi-tenant Django-based digital ID card system for managing citizen identification across multiple centers.

## Features

- Multi-tenant architecture with hierarchical structure (city -> subcity -> kebele)
- Unique login/dashboard per tenant
- Citizen registration per kebele
- Digital ID card generation, approval workflow, and printing/exporting (PDF)
- Role-based access control (clerks, kebele leaders, subcity admins, city admins)
- Comprehensive reporting and statistics

## Project Structure

- **Backend**: Django REST Framework API with PostgreSQL database
- **Frontend**: React with Material UI

## Backend Setup

### Prerequisites

- Python 3.8+
- Django 4.2+
- Django REST Framework
- PostgreSQL 12+

### Installation

1. Clone the repository:
   ```
   git clone https://github.com/yourusername/neocamelot.git
   cd neocamelot
   ```

2. Create a virtual environment:
   ```
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. Install dependencies:
   ```
   cd backend
   pip install -r requirements.txt
   ```

4. Configure PostgreSQL database in `settings.py`

5. Run migrations:
   ```
   python manage.py makemigrations
   python manage.py migrate
   ```

6. Create a superuser:
   ```
   python manage.py create_superuser --email=<EMAIL> --password=yourpassword
   ```

7. (Optional) Create sample data:
   ```
   python manage.py create_sample_data
   ```

8. Run the development server:
   ```
   python manage.py runserver
   ```

## Frontend Setup

1. Install dependencies:
   ```
   cd frontend
   npm install
   ```

2. Start the development server:
   ```
   npm run dev
   ```

## API Endpoints

- `/api/cities/` - City management
- `/api/subcities/` - Subcity management
- `/api/users/` - User management
- `/api/citizens/` - Citizen registration and management
- `/api/idcards/` - ID card generation and management
- `/api/idcard-templates/` - ID card template management
- `/api/common/` - Common reference data (regions, religions, etc.)

## Authentication

- JWT-based authentication
- Login endpoint: `/api/jwt/login/`
- Refresh token endpoint: `/api/jwt/refresh-token/`
- Validate token endpoint: `/api/jwt/validate-token/`

## Multi-tenancy

The system uses a schema-based approach for multi-tenancy:

- Each tenant (city, subcity, kebele) has its own schema in the database
- Tenant data is isolated using PostgreSQL schemas
- Parent-child relationships between tenants (city -> subcity -> kebele)
- Common reference data is shared across all tenants

## Known Issues and Solutions

### Authentication Issues

If you experience logout when navigating to certain pages (e.g., Citizens page), it may be due to token validation issues. The following solutions have been implemented:

1. **Token Validation**: The system now validates tokens before navigation, not just after
2. **Token Refresh**: Improved token refresh mechanism with better error handling
3. **Schema Handling**: Better handling of schema variations (spaces vs. underscores)

If you still experience issues:

1. Check browser console for authentication errors
2. Verify that the backend server is running and accessible
3. Clear browser cookies and local storage, then log in again
4. Ensure the correct schema name is being used for your tenant

## License

[MIT License](LICENSE)
