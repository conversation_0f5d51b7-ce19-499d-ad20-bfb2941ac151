# Generated by Django 5.1.7 on 2025-04-14 06:48

import datetime
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('centers', '0001_initial'),
        ('citizens', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='IDCardTemplate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('background_image', models.ImageField(blank=True, null=True, upload_to='id_templates/')),
                ('is_default', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('front_layout', models.J<PERSON><PERSON>ield(default=dict)),
                ('back_layout', models.J<PERSON><PERSON>ield(default=dict)),
                ('center', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='id_templates', to='centers.center')),
            ],
            options={
                'unique_together': {('center', 'name')},
            },
        ),
        migrations.CreateModel(
            name='IDCard',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('card_number', models.CharField(blank=True, max_length=50, unique=True)),
                ('issue_date', models.DateField(default=datetime.date.today)),
                ('expiry_date', models.DateField(blank=True, null=True)),
                ('status', models.CharField(choices=[('DRAFT', 'Draft'), ('PENDING', 'Pending Approval'), ('APPROVED', 'Approved'), ('PRINTED', 'Printed'), ('ISSUED', 'Issued'), ('EXPIRED', 'Expired'), ('REVOKED', 'Revoked')], default='DRAFT', max_length=20)),
                ('card_data', models.JSONField(default=dict)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('approved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_cards', to=settings.AUTH_USER_MODEL)),
                ('citizen', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='id_cards', to='citizens.citizen')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_cards', to=settings.AUTH_USER_MODEL)),
                ('template', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='id_cards', to='idcards.idcardtemplate')),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
    ]
