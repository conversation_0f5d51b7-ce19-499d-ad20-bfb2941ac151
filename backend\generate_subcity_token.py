import os
import django
import sys

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'neocamelot.settings')
django.setup()

from django.db import connection
from django_tenants.utils import tenant_context, get_public_schema_name
from centers.models import Client
from django.contrib.auth import get_user_model
User = get_user_model()
from rest_framework.authtoken.models import Token

# Find subcity tenants
connection.set_schema_to_public()
subcity_tenants = Client.objects.filter(schema_type='SUBCITY')

print("\n=== Subcity Tenants ===")
for tenant in subcity_tenants:
    print(f"ID: {tenant.id}")
    print(f"Name: {tenant.name}")
    print(f"Schema Name: {tenant.schema_name}")
    print("-" * 30)

# Generate tokens for subcity users
for tenant in subcity_tenants:
    print(f"\nGenerating token for users in {tenant.name} ({tenant.schema_name})")
    
    with tenant_context(tenant):
        users = User.objects.all()
        
        if not users.exists():
            print(f"No users found in {tenant.schema_name}")
            continue
        
        for user in users:
            print(f"User: {user.email} (ID: {user.id}, Role: {user.role})")
            
            # Generate token
            token, created = Token.objects.get_or_create(user=user)
            if created:
                print(f"Created new token: {token.key}")
            else:
                print(f"Found existing token: {token.key}")
            
            print(f"Use this token with schema name '{tenant.schema_name}'")
            print(f"Authorization: Token {token.key}")
            print(f"X-Schema-Name: {tenant.schema_name}")
            print("-" * 30)

# Also check for admin users in the public schema
print("\n=== Public Schema Users ===")
connection.set_schema_to_public()
admin_users = User.objects.filter(is_staff=True)

for user in admin_users:
    print(f"User: {user.email} (ID: {user.id}, Staff: {user.is_staff}, Superuser: {user.is_superuser})")
    
    # Generate token
    token, created = Token.objects.get_or_create(user=user)
    if created:
        print(f"Created new token: {token.key}")
    else:
        print(f"Found existing token: {token.key}")
    
    print(f"Use this token for public schema or any tenant")
    print(f"Authorization: Token {token.key}")
    print("-" * 30)
