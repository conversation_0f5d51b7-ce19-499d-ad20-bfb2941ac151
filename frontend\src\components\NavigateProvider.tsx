import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';

/**
 * NavigateProvider component
 * 
 * This component provides the navigate function from react-router-dom
 * to non-component code by storing it in the window object.
 */
const NavigateProvider: React.FC = () => {
  const navigate = useNavigate();
  
  useEffect(() => {
    // Store the navigate function in the window object
    (window as any).__navigateFunction = navigate;
    
    return () => {
      // Clean up when the component unmounts
      delete (window as any).__navigateFunction;
    };
  }, [navigate]);
  
  return null;
};

export default NavigateProvider;
