# Page Banner Usage Guide

This guide explains how to use the `PageBanner` component to create consistent, professional-looking banners across all pages in the system.

## Overview

The `PageBanner` component provides a standardized way to create attractive page headers with a consistent look and feel. It includes support for:

- Page title and subtitle
- Icon display
- Action content (buttons, search fields, etc.)
- Color customization

## Installation

The `PageBanner` component is already included in the project at `frontend/src/components/PageBanner.tsx`.

## Basic Usage

Here's how to use the `PageBanner` component in your pages:

```tsx
import React from 'react';
import { Box, Container, Typography } from '@mui/material';
import PageBanner from '../components/PageBanner';
import PersonIcon from '@mui/icons-material/Person';

const YourPage: React.FC = () => {
  return (
    <Box sx={{ py: 4 }}>
      {/* Banner */}
      <PageBanner
        title="Your Page Title"
        subtitle="Your page subtitle or description"
        icon={<PersonIcon sx={{ fontSize: 50, color: 'white' }} />}
      />

      <Container maxWidth="lg">
        {/* Your page content goes here */}
      </Container>
    </Box>
  );
};

export default YourPage;
```

## Props

The `PageBanner` component accepts the following props:

| Prop | Type | Description | Default |
|------|------|-------------|---------|
| `title` | string | The main title displayed in the banner | Required |
| `subtitle` | string | A subtitle or description | Optional |
| `icon` | React.ReactNode | An icon to display next to the title | Optional |
| `actionContent` | React.ReactNode | Content to display in the action area | Optional |
| `color` | 'primary' \| 'secondary' \| 'custom' | The color scheme to use | 'primary' |
| `customColor` | string | A custom color to use when color is set to 'custom' | Optional |

## Examples

### Basic Banner

```tsx
<PageBanner
  title="Dashboard"
  subtitle="Welcome to your dashboard"
  icon={<DashboardIcon sx={{ fontSize: 50, color: 'white' }} />}
/>
```

### Banner with Action Content

```tsx
<PageBanner
  title="Citizens Management"
  subtitle="Manage citizens in your center"
  icon={<PersonIcon sx={{ fontSize: 50, color: 'white' }} />}
  actionContent={
    <Box sx={{ display: 'flex', gap: 2 }}>
      <Button
        variant="contained"
        color="secondary"
        startIcon={<AddIcon />}
        sx={{ bgcolor: 'rgba(255,255,255,0.2)', color: 'white' }}
      >
        Add New Citizen
      </Button>
      <TextField
        placeholder="Search citizens..."
        variant="outlined"
        size="small"
        sx={{
          bgcolor: 'rgba(255,255,255,0.2)',
          borderRadius: 1,
          '& .MuiOutlinedInput-root': {
            color: 'white',
            '& fieldset': { border: 'none' },
          },
          '& .MuiInputBase-input::placeholder': {
            color: 'rgba(255,255,255,0.7)',
            opacity: 1,
          },
        }}
        InputProps={{
          startAdornment: (
            <InputAdornment position="start">
              <SearchIcon sx={{ color: 'white' }} />
            </InputAdornment>
          ),
        }}
      />
    </Box>
  }
/>
```

### Banner with Custom Color

```tsx
<PageBanner
  title="Error Page"
  subtitle="Something went wrong"
  icon={<ErrorIcon sx={{ fontSize: 50, color: 'white' }} />}
  color="custom"
  customColor="#d32f2f"
/>
```

## Best Practices

1. **Consistent Icons**: Use icons that are relevant to the page content and maintain a consistent size (recommended: 50px).

2. **Concise Titles**: Keep titles short and descriptive.

3. **Helpful Subtitles**: Use subtitles to provide additional context about the page's purpose.

4. **Action Content**: Only include the most important actions in the banner. Less is more.

5. **Color Usage**: Stick to the primary color for most pages. Use secondary or custom colors only for specific sections or to indicate different areas of the application.

## Troubleshooting

If the banner doesn't appear correctly:

1. Make sure you've imported the component correctly: `import PageBanner from '../components/PageBanner';`
2. Check that you're providing at least the required `title` prop
3. Verify that the component is placed at the top of your page content
4. Ensure you're not overriding the component's styles unintentionally

## Migration Guide

To migrate existing pages to use the new `PageBanner` component:

1. Import the `PageBanner` component
2. Replace the existing banner JSX with the `PageBanner` component
3. Move the title, subtitle, and icon from the old banner to the appropriate props
4. If you had custom content in the banner, move it to the `actionContent` prop

## Example Migration

### Before:

```tsx
<Box
  sx={{
    width: '100%',
    background: 'linear-gradient(135deg, #3f51b5 0%, #5c6bc0 70%, #7986cb 100%)',
    // ... other styles
  }}
>
  <Container maxWidth="lg">
    <Box sx={{ textAlign: 'center', position: 'relative', zIndex: 2 }}>
      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', mb: 2 }}>
        <PersonIcon sx={{ fontSize: 40, color: 'white', mr: 2 }} />
        <Typography variant="h4" sx={{ color: 'white', fontWeight: 700 }}>
          Citizens Management
        </Typography>
      </Box>
      <Typography variant="h6" sx={{ color: 'rgba(255,255,255,0.9)', mb: 1 }}>
        Manage citizens in your center
      </Typography>
      <Typography variant="body1" sx={{ color: 'rgba(255,255,255,0.8)' }}>
        Register, view, and manage citizens. Create ID cards for registered citizens.
      </Typography>
    </Box>
  </Container>
</Box>
```

### After:

```tsx
<PageBanner
  title="Citizens Management"
  subtitle="Manage citizens in your center"
  icon={<PersonIcon sx={{ fontSize: 50, color: 'white' }} />}
  actionContent={
    <Box sx={{ textAlign: 'center', width: '100%' }}>
      <Typography variant="body1" sx={{ color: 'white', fontWeight: 500 }}>
        Register, view, and manage citizens. Create ID cards for registered citizens.
      </Typography>
    </Box>
  }
/>
```
