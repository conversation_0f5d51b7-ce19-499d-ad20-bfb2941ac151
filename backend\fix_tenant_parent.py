"""
<PERSON><PERSON>t to fix parent-child relationships between tenants.
This script will update the parent field for a specified tenant.
"""
import os
import sys
import django

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'neocamelot.settings')
django.setup()

from centers.models import Client
from django.db import transaction

def fix_tenant_parent(tenant_name, parent_name):
    """
    Fix the parent-child relationship for a tenant.
    
    Args:
        tenant_name (str): The name of the tenant to update
        parent_name (str): The name of the parent tenant
    """
    try:
        # Find the tenant and parent by name
        tenant = Client.objects.filter(name=tenant_name).first()
        parent = Client.objects.filter(name=parent_name).first()
        
        if not tenant:
            print(f"Error: Tenant '{tenant_name}' not found.")
            return False
        
        if not parent:
            print(f"Error: Parent tenant '{parent_name}' not found.")
            return False
        
        # Print current state
        print(f"Current state:")
        print(f"  Tenant: {tenant.name} (ID: {tenant.id}, Schema: {tenant.schema_name})")
        print(f"  Current parent: {tenant.parent.name if tenant.parent else 'None'}")
        print(f"  Setting parent to: {parent.name} (ID: {parent.id}, Schema: {parent.schema_name})")
        
        # Update the parent
        with transaction.atomic():
            tenant.parent = parent
            tenant.save()
        
        # Verify the update
        tenant.refresh_from_db()
        print(f"Updated state:")
        print(f"  Tenant: {tenant.name} (ID: {tenant.id}, Schema: {tenant.schema_name})")
        print(f"  New parent: {tenant.parent.name if tenant.parent else 'None'}")
        
        return True
    
    except Exception as e:
        print(f"Error updating tenant parent: {str(e)}")
        return False

def list_tenants():
    """List all tenants in the system."""
    try:
        tenants = Client.objects.all()
        print(f"Found {len(tenants)} tenants:")
        for tenant in tenants:
            parent_name = tenant.parent.name if tenant.parent else 'None'
            print(f"  {tenant.name} (ID: {tenant.id}, Schema: {tenant.schema_name}, Type: {tenant.schema_type}, Parent: {parent_name})")
        return True
    except Exception as e:
        print(f"Error listing tenants: {str(e)}")
        return False

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("Usage:")
        print("  python fix_tenant_parent.py list")
        print("  python fix_tenant_parent.py fix <tenant_name> <parent_name>")
        sys.exit(1)
    
    command = sys.argv[1]
    
    if command == "list":
        list_tenants()
    elif command == "fix" and len(sys.argv) == 4:
        tenant_name = sys.argv[2]
        parent_name = sys.argv[3]
        fix_tenant_parent(tenant_name, parent_name)
    else:
        print("Invalid command or missing arguments.")
        print("Usage:")
        print("  python fix_tenant_parent.py list")
        print("  python fix_tenant_parent.py fix <tenant_name> <parent_name>")
        sys.exit(1)
