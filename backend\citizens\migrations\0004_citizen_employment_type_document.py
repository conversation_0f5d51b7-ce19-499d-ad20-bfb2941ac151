# Generated by Django 5.1.7 on 2025-04-17 15:19

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('centers', '0012_documenttype_employmenttype'),
        ('citizens', '0003_citizen_citizen_status_citizen_marital_status_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='citizen',
            name='employment_type',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='centers.employmenttype'),
        ),
        migrations.CreateModel(
            name='Document',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('document_file', models.FileField(upload_to='citizen_documents/')),
                ('issue_date', models.DateField(blank=True, null=True)),
                ('expiry_date', models.DateField(blank=True, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('citizen', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='documents', to='citizens.citizen')),
                ('document_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='centers.documenttype')),
            ],
            options={
                'verbose_name': 'Document',
                'verbose_name_plural': 'Documents',
            },
        ),
    ]
