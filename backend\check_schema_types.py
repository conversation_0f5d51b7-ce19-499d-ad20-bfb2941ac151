import os
import django

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'neocamelot.settings')
django.setup()

# Import models
from centers.models import Client, Domain

# Check schema types
print("\n=== Checking Schema Types ===")
tenants = Client.objects.all()
for tenant in tenants:
    print(f"Name: {tenant.name}")
    print(f"Schema Name: {tenant.schema_name}")
    print(f"Schema Type: {tenant.schema_type}")
    print(f"Schema Type Type: {type(tenant.schema_type)}")
    print(f"Is Schema Type 'SUBCITY'?: {tenant.schema_type == 'SUBCITY'}")
    print(f"Is Schema Type 'CITY'?: {tenant.schema_type == 'CITY'}")
    print("-" * 30)

# Check available tenants for kebele
print("\n=== Available Tenants for Kebele ===")
subcity_tenants = Client.objects.filter(schema_type='SUBCITY')
print(f"Number of subcity tenants: {subcity_tenants.count()}")
for tenant in subcity_tenants:
    print(f"Name: {tenant.name}")
    print(f"Schema Name: {tenant.schema_name}")
    print(f"Schema Type: {tenant.schema_type}")
    print("-" * 30)
