import os
import django

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'neocamelot.settings')
django.setup()

# Import models and admin
from django.contrib import admin
from citizens.models import Citizen
from idcards.models import IDCard, IDCardTemplate
from django_tenants.utils import tenant_context, schema_context
from centers.models import Client
from django.db import connection

# Import the global admin classes
from global_admin import GlobalCitizenAdmin, GlobalIDCardTemplateAdmin, GlobalIDCardAdmin

print("Current schema:", connection.schema_name)

# Unregister models if they are already registered
try:
    admin.site.unregister(Citizen)
    print("Unregistered Citizen model")
except admin.sites.NotRegistered:
    print("Citizen model was not registered")

try:
    admin.site.unregister(IDCardTemplate)
    print("Unregistered IDCardTemplate model")
except admin.sites.NotRegistered:
    print("IDCardTemplate model was not registered")

try:
    admin.site.unregister(IDCard)
    print("Unregistered IDCard model")
except admin.sites.NotRegistered:
    print("IDCard model was not registered")

# We'll use the global admin classes instead of defining new ones

# Register models with global admin classes
admin.site.register(Citizen, GlobalCitizenAdmin)
print("Registered Citizen model with GlobalCitizenAdmin")

admin.site.register(IDCardTemplate, GlobalIDCardTemplateAdmin)
print("Registered IDCardTemplate model with GlobalIDCardTemplateAdmin")

admin.site.register(IDCard, GlobalIDCardAdmin)
print("Registered IDCard model with GlobalIDCardAdmin")

# Check registration
registered_models = [model.__name__ for model, _ in admin.site._registry.items()]
print(f"\nRegistered models in admin site: {registered_models}")

# Create a default ID card template for each tenant
print("\nCreating default ID card templates for each tenant:")
tenants = Client.objects.exclude(schema_name='public')
for tenant in tenants:
    print(f"\nChecking tenant: {tenant.name} (schema: {tenant.schema_name})")
    try:
        with tenant_context(tenant):
            from centers.models import Center

            # Get all centers in this tenant
            centers = Center.objects.all()
            print(f"Found {centers.count()} centers in {tenant.schema_name}")

            # Create a default template for each center
            for center in centers:
                # Check if the center already has a template
                templates = IDCardTemplate.objects.filter(center=center)
                if templates.exists():
                    print(f"Center '{center.name}' already has {templates.count()} templates")
                else:
                    # Create a default template
                    template = IDCardTemplate.objects.create(
                        name="Default Template",
                        center=center,
                        is_default=True,
                        front_layout={
                            'title': {'x': 10, 'y': 10, 'font_size': 18},
                            'photo': {'x': 20, 'y': 40, 'width': 100, 'height': 120},
                            'name': {'x': 130, 'y': 50, 'font_size': 14},
                            'id_number': {'x': 130, 'y': 70, 'font_size': 12},
                        },
                        back_layout={
                            'address': {'x': 10, 'y': 10, 'font_size': 12},
                            'issue_date': {'x': 10, 'y': 30, 'font_size': 12},
                            'expiry_date': {'x': 10, 'y': 50, 'font_size': 12},
                        }
                    )
                    print(f"Created default template for center '{center.name}'")
    except Exception as e:
        print(f"Error accessing tenant schema: {str(e)}")

print("\nDone!")
