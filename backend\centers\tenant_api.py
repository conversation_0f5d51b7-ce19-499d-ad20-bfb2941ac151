from rest_framework.decorators import api_view, permission_classes, authentication_classes
from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework.response import Response
from rest_framework import status
from django.db.models import Q
from django_tenants.utils import tenant_context, get_public_schema_name, get_tenant
from django.contrib.auth import get_user_model
from .models import Client
from citizens.models import Citizen
from citizens.serializers import CitizenListSerializer, CitizenSerializer, CitizenDetailSerializer
from citizens.models_family import Child, Parent, EmergencyContact, Spouse
from citizens.models_document import Document
from citizens.models_biometric import Photo
from citizens.serializers_family import ChildSerializer, ParentSerializer, EmergencyContactSerializer, SpouseSerializer, RelationshipTypeSerializer
from citizens.serializers_document import DocumentSerializer
from citizens.serializers_biometric import PhotoSerializer
from idcards.models import IDCard, IDCardTemplate
from idcards.serializers import IDCardListSerializer, IDCardSerializer, IDCardTemplateListSerializer
import logging
import uuid

logger = logging.getLogger(__name__)

@api_view(['GET', 'POST', 'OPTIONS'])
@authentication_classes([])  # No authentication classes - we'll handle it manually
@permission_classes([AllowAny])  # Allow any user to access this endpoint
def tenant_citizens(request, schema_name):
    """
    Get or create citizens for a specific tenant by schema name.
    """
    # Handle OPTIONS request for CORS preflight
    if request.method == 'OPTIONS':
        response = Response()
        response["Access-Control-Allow-Origin"] = "*"
        response["Access-Control-Allow-Methods"] = "GET, POST, OPTIONS"
        response["Access-Control-Allow-Headers"] = "Content-Type, Authorization, X-Schema-Name, X-Auth-Token"
        response["Access-Control-Max-Age"] = "86400"  # 24 hours
        return response
    # Log the request for debugging
    print(f"\n\nRequest to tenant_citizens for schema: {schema_name}\n\n")
    print(f"\n\nRequest headers: {request.headers}\n\n")

    # Debug schema name variations
    print(f"\n\nTrying to normalize schema name: {schema_name}\n\n")

    # Try different variations of the schema name
    variations = [schema_name]

    # Add variations with spaces and underscores
    if '_' in schema_name:
        variations.append(schema_name.replace('_', ' '))
    if ' ' in schema_name:
        variations.append(schema_name.replace(' ', '_'))

    # For kebele schemas, add more variations
    if schema_name.startswith('kebele'):
        # Extract the kebele number if present
        import re
        match = re.match(r'kebele[_ ]?(\d+)', schema_name)
        if match:
            kebele_number = match.group(1)
            variations.append(f'kebele {kebele_number}')
            variations.append(f'kebele_{kebele_number}')
            variations.append(f'kebele{kebele_number}')

    print(f"\n\nSchema variations to try: {variations}\n\n")

    # Get authentication header
    auth_header = request.headers.get('Authorization', '')
    print(f"\n\nAUTH HEADER: {auth_header}\n\n")

    # Check for token in X-Auth-Token header (fallback)
    x_auth_token = request.headers.get('X-Auth-Token')
    if x_auth_token:
        print(f"\n\nFound token in X-Auth-Token header: {x_auth_token[:10]}...\n\n")
        # Use this token if Authorization header is missing
        if not auth_header:
            auth_header = f"Token {x_auth_token}"
            print(f"\n\nUsing token from X-Auth-Token header: {auth_header}\n\n")

    # Check for refresh token in cookies
    refresh_token = request.COOKIES.get('refresh_token')
    if refresh_token:
        print(f"\n\nFound refresh_token in cookies\n\n")

    # Extract token from Authorization header
    token_key = None

    # Import necessary models and utilities
    from django.contrib.auth import get_user_model
    from accounts.jwt_utils import validate_jwt_token

    User = get_user_model()

    # Try to extract token from Authorization header
    if auth_header:
        if auth_header.startswith('Bearer '):
            token_key = auth_header.split(' ')[1]
            print(f"\n\nExtracted Bearer token: {token_key[:10]}...\n\n")
        else:
            # Try to use the header value directly
            token_key = auth_header
            print(f"\n\nUsing raw Authorization header as token: {token_key[:10]}...\n\n")

    # If no token from Authorization header, try X-Auth-Token
    if not token_key and x_auth_token:
        token_key = x_auth_token
        print(f"\n\nUsing X-Auth-Token: {token_key[:10]}...\n\n")

    # If still no token, check if user is already authenticated
    if not token_key and request.user and request.user.is_authenticated:
        print(f"\n\nUser already authenticated: {request.user.username}\n\n")
        # Skip token validation since user is already authenticated
    elif not token_key:
        # No token found, but we'll allow unauthenticated access for GET requests
        if request.method == 'GET':
            print(f"\n\nNo authentication token found, but allowing GET request\n\n")
            # Continue without authentication for GET requests
        else:
            # For non-GET requests, require authentication
            print(f"\n\nNo authentication token found for non-GET request\n\n")
            return Response({"error": "Authentication credentials were not provided."},
                          status=status.HTTP_401_UNAUTHORIZED)
    # Skip token refresh for GET requests - always allow them without authentication
    elif request.method == 'GET':
        print(f"\n\nAllowing GET request without authentication\n\n")
        # Continue without authentication for GET requests
    elif refresh_token:
        # Only use refresh token for non-GET requests
        try:
            from accounts.jwt_utils import refresh_jwt_token

            # Try to refresh the token
            print(f"\n\nTrying to use refresh token\n\n")

            # Call the refresh function and handle the result safely
            try:
                refresh_result = refresh_jwt_token(refresh_token)

                # Check if refresh was successful
                if refresh_result and isinstance(refresh_result, tuple) and len(refresh_result) == 2:
                    new_access_token, new_refresh_token = refresh_result
                else:
                    print(f"\n\nInvalid result from refresh_jwt_token: {refresh_result}\n\n")
                    new_access_token, new_refresh_token = None, None
            except Exception as e:
                print(f"\n\nError during token refresh: {str(e)}\n\n")
                new_access_token, new_refresh_token = None, None

            if new_access_token and new_refresh_token:
                print(f"\n\nToken refreshed successfully\n\n")

                # Create a response with the new tokens
                response = Response({
                    "message": "Token refreshed",
                    "access_token": new_access_token,
                    "refresh_token": new_refresh_token
                })

                # Set cookies for the new tokens
                response.set_cookie(
                    'refresh_token',
                    new_refresh_token,
                    httponly=True,
                    samesite='Lax',
                    path='/',
                    max_age=7 * 24 * 60 * 60  # 7 days
                )

                return response
            else:
                print(f"\n\nFailed to use refresh token\n\n")
                # For non-GET requests, require authentication
                return Response({"error": "Authentication credentials were not provided."}, status=status.HTTP_401_UNAUTHORIZED)
        except Exception as e:
            print(f"\n\nException during token refresh: {str(e)}\n\n")
            # For non-GET requests, require authentication
            return Response({"error": f"Authentication error: {str(e)}"}, status=status.HTTP_401_UNAUTHORIZED)
    else:
        print(f"\n\nNo authentication credentials provided\n\n")
        # Allow GET requests without authentication
        if request.method == 'GET':
            print(f"\n\nAllowing GET request without authentication\n\n")
            # Continue without authentication for GET requests
        else:
            # For non-GET requests, require authentication
            return Response({"error": "Authentication credentials were not provided."}, status=status.HTTP_401_UNAUTHORIZED)

    # Debug: JWT authentication is now the only authentication method
    print("\n\nUsing JWT authentication only\n\n")

    # Debug: List all users in the public schema
    from django.contrib.auth import get_user_model
    User = get_user_model()
    all_users = User.objects.all()
    print(f"\n\nALL USERS IN PUBLIC SCHEMA: {[u.username for u in all_users]}\n\n")
    print(f"\n\nTOTAL USERS IN PUBLIC SCHEMA: {all_users.count()}\n\n")

    # Check for X-Auth-Token header again (might have been added by middleware)
    x_auth_token = request.headers.get('X-Auth-Token')
    if x_auth_token and not token_key:
        print(f"\n\nUsing X-Auth-Token as fallback: {x_auth_token[:10]}...\n\n")
        token_key = x_auth_token

    try:
        # Import schema utilities
        from centers.schema_utils import normalize_schema_name, get_tenant_for_schema
        from django.db import connection
        from django.contrib.auth import get_user_model
        User = get_user_model()

        # Try to find the tenant directly first
        from centers.models import Client

        # List all tenants for debugging
        all_tenants = Client.objects.all()
        print(f"\n\nAll tenants in database: {[t.schema_name for t in all_tenants]}\n\n")

        # Check PostgreSQL schemas directly
        from django.db import connection
        with connection.cursor() as cursor:
            cursor.execute("SELECT schema_name FROM information_schema.schemata")
            schemas = [row[0] for row in cursor.fetchall()]
            print(f"\n\nAll PostgreSQL schemas: {schemas}\n\n")

        # Try each variation directly
        tenant = None
        for variation in variations:
            try:
                tenant = Client.objects.get(schema_name=variation)
                print(f"\n\nFound tenant directly with schema: {variation}\n\n")
                normalized_schema = variation
                break
            except Client.DoesNotExist:
                print(f"\n\nNo tenant found directly for schema: {variation}\n\n")

        # If not found directly, try the normalize function
        if not tenant:
            # Normalize the schema name (replace underscores with spaces)
            normalized_schema = normalize_schema_name(schema_name)
            print(f"\n\nNormalized schema name: {normalized_schema}\n\n")

            if not normalized_schema:
                print(f"\n\nCould not normalize schema name: {schema_name}\n\n")
                return Response({"error": f"Invalid schema name: {schema_name}"}, status=status.HTTP_400_BAD_REQUEST)

            # Get the tenant
            tenant = get_tenant_for_schema(normalized_schema)

        if not tenant:
            print(f"\n\nTenant not found for schema: {normalized_schema}\n\n")

            # For kebele schemas, try to create the tenant if it doesn't exist
            if schema_name.startswith('kebele'):
                try:
                    # Extract the kebele number
                    import re
                    match = re.match(r'kebele[_ ]?(\d+)', schema_name)
                    if match:
                        kebele_number = match.group(1)
                        schema_to_use = f'kebele {kebele_number}'

                        print(f"\n\nAttempting to create tenant with schema: {schema_to_use}\n\n")

                        # Check if the schema exists in PostgreSQL
                        from django.db import connection
                        with connection.cursor() as cursor:
                            cursor.execute(f"SELECT 1 FROM information_schema.schemata WHERE schema_name = '{schema_to_use}'")
                            schema_exists = cursor.fetchone() is not None

                        # Always create the tenant in Django
                        # If the schema doesn't exist in PostgreSQL, django-tenants will create it
                        print(f"\n\nCreating tenant with schema: {schema_to_use}\n\n")
                        if schema_exists:
                            print(f"\n\nSchema {schema_to_use} exists in PostgreSQL but not in Django\n\n")

                        # Create the tenant in Django
                        tenant = Client.objects.create(
                            schema_name=schema_to_use,
                            name=f'Kebele {kebele_number}',
                            paid_until='2099-12-31',
                            on_trial=False,
                            tenant_type='kebele'
                        )

                        # Create a domain for the tenant
                        from centers.models import Domain
                        domain = Domain.objects.create(
                            domain=f'kebele{kebele_number}.localhost',
                            tenant=tenant,
                            is_primary=True
                        )

                        print(f"\n\nCreated tenant: {tenant.name} (schema: {tenant.schema_name})\n\n")
                        print(f"\n\nCreated domain: {domain.domain} for tenant: {tenant.name}\n\n")
                        normalized_schema = schema_to_use
                except Exception as e:
                    print(f"\n\nError creating tenant: {str(e)}\n\n")

            # If still no tenant, return error
            if not tenant:
                # Create response with CORS headers
                response = Response(
                    {"error": f"Tenant not found for schema: {schema_name}"},
                    status=status.HTTP_404_NOT_FOUND
                )

                # Add CORS headers
                response["Access-Control-Allow-Origin"] = "*"
                response["Access-Control-Allow-Methods"] = "GET, POST, OPTIONS"
                response["Access-Control-Allow-Headers"] = "Content-Type, Authorization, X-Schema-Name, X-Auth-Token"

                return response

        print(f"\n\nFound tenant: {tenant.name} (schema: {tenant.schema_name})\n\n")

        # Set the tenant for this request
        connection.set_tenant(tenant)
        request.tenant = tenant

        # Set the current tenant in thread-local storage
        from .middleware import set_current_tenant
        set_current_tenant(tenant)

        # Use tenant_context to ensure we're querying the right database
        with tenant_context(tenant):
            # If we have a token, try to authenticate the user with JWT
            if token_key:
                try:
                    # Try to validate the JWT token
                    from accounts.jwt_utils import validate_jwt_token
                    payload = validate_jwt_token(token_key)

                    if payload:
                        # Get user from payload
                        user_id = payload.get('user_id')
                        if user_id:
                            try:
                                user = User.objects.get(id=user_id)
                                request.user = user
                                print(f"\n\nAuthenticated user with JWT: {user.username} (ID: {user.id})\n\n")
                            except User.DoesNotExist:
                                # Try to find user by email if present in payload
                                email = payload.get('email')
                                if email:
                                    try:
                                        user = User.objects.get(email=email)
                                        request.user = user
                                        print(f"\n\nAuthenticated user with JWT by email: {user.username} (ID: {user.id})\n\n")
                                    except User.DoesNotExist:
                                        print(f"\n\nUser not found in tenant schema for JWT token\n\n")
                                        # Continue without authentication - we'll handle permissions later
                                else:
                                    print(f"\n\nNo email in JWT payload\n\n")
                                    # Continue without authentication - we'll handle permissions later
                        else:
                            print(f"\n\nNo user_id in JWT payload\n\n")
                            # Continue without authentication - we'll handle permissions later
                    else:
                        print(f"\n\nInvalid JWT token\n\n")
                        # Continue without authentication - we'll handle permissions later
                except Exception as e:
                    print(f"\n\nError during JWT authentication: {str(e)}\n\n")
                    # Continue without authentication - we'll handle permissions later

            # Now we have the correct tenant context (and possibly an authenticated user)
            # Get all citizens
            try:
                # Import necessary models and serializers
                from citizens.models import Citizen
                from citizens.serializers import CitizenListSerializer, CitizenDetailSerializer
                from django.db.models import Q

                # Get all citizens
                citizens = Citizen.objects.all()
                print(f"\n\nFound {citizens.count()} citizens in tenant {tenant.schema_name}\n\n")

                # Apply search filter if provided
                search_query = request.query_params.get('search', '')
                if search_query:
                    print(f"\n\nApplying search filter: {search_query}\n\n")
                    citizens = citizens.filter(
                        Q(first_name__icontains=search_query) |
                        Q(last_name__icontains=search_query) |
                        Q(registration_number__icontains=search_query) |
                        Q(id_number__icontains=search_query) |
                        Q(email__icontains=search_query) |
                        Q(phone__icontains=search_query)
                    )
                    print(f"\n\nFound {citizens.count()} citizens after search filter\n\n")

                # Handle GET request
                if request.method == 'GET':
                    # Check if detail view is requested
                    detail = request.query_params.get('detail', 'false').lower() == 'true'
                    print(f"\n\nDetail view requested: {detail}\n\n")

                    if detail:
                        # Use the detailed serializer
                        serializer = CitizenDetailSerializer(citizens, many=True, context={'request': request})
                    else:
                        # Use the list serializer
                        serializer = CitizenListSerializer(citizens, many=True)

                    print(f"\n\nReturning {len(serializer.data)} citizens\n\n")

                    # Create response with data
                    response_data = serializer.data

                    # Debug the response data
                    print(f"\n\nResponse data type: {type(response_data)}")
                    print(f"\n\nResponse data: {response_data[:100] if isinstance(response_data, list) else response_data}")

                    # Create response
                    response = Response(response_data)

                    # Add CORS headers
                    response["Access-Control-Allow-Origin"] = "*"
                    response["Access-Control-Allow-Methods"] = "GET, POST, OPTIONS"
                    response["Access-Control-Allow-Headers"] = "Content-Type, Authorization, X-Schema-Name, X-Auth-Token"

                    return response
            except Exception as e:
                print(f"\n\nError retrieving citizens: {str(e)}\n\n")
                import traceback
                traceback.print_exc()
                return Response({"error": f"Error retrieving citizens: {str(e)}"},
                               status=status.HTTP_500_INTERNAL_SERVER_ERROR)

            # Handle POST request
            if request.method == 'POST':
                try:
                    from citizens.serializers import CitizenSerializer
                    print(f"\n\nPOST request to register citizen in tenant {schema_name}\n\n")
                    print(f"\n\nRequest data: {request.data}\n\n")

                    # Create serializer
                    serializer = CitizenSerializer(data=request.data)

                    # Check if serializer is valid
                    if serializer.is_valid():
                        print(f"\n\nSerializer is valid\n\n")

                        # Set created_by to current user if authenticated
                        if hasattr(request, 'user') and request.user and request.user.is_authenticated:
                            print(f"\n\nSaving citizen with created_by={request.user.username}\n\n")
                            citizen = serializer.save(created_by=request.user)
                        else:
                            # If no authenticated user, just save without created_by
                            print(f"\n\nSaving citizen without created_by\n\n")
                            citizen = serializer.save()

                        print(f"\n\nCitizen saved successfully: {citizen.id}\n\n")

                        # Create response with data
                        response = Response(serializer.data, status=status.HTTP_201_CREATED)

                        # Add CORS headers
                        response["Access-Control-Allow-Origin"] = "*"
                        response["Access-Control-Allow-Methods"] = "GET, POST, OPTIONS"
                        response["Access-Control-Allow-Headers"] = "Content-Type, Authorization, X-Schema-Name, X-Auth-Token"

                        return response
                    else:
                        print(f"\n\nSerializer errors: {serializer.errors}\n\n")

                        # Create error response
                        response = Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

                        # Add CORS headers
                        response["Access-Control-Allow-Origin"] = "*"
                        response["Access-Control-Allow-Methods"] = "GET, POST, OPTIONS"
                        response["Access-Control-Allow-Headers"] = "Content-Type, Authorization, X-Schema-Name, X-Auth-Token"

                        return response
                except Exception as e:
                    print(f"\n\nError creating citizen: {str(e)}\n\n")
                    import traceback
                    traceback.print_exc()

                    # Create error response
                    response = Response(
                        {"error": f"Error creating citizen: {str(e)}"},
                        status=status.HTTP_500_INTERNAL_SERVER_ERROR
                    )

                    # Add CORS headers
                    response["Access-Control-Allow-Origin"] = "*"
                    response["Access-Control-Allow-Methods"] = "GET, POST, OPTIONS"
                    response["Access-Control-Allow-Headers"] = "Content-Type, Authorization, X-Schema-Name, X-Auth-Token"

                    return response
    except Client.DoesNotExist:
        # Create error response
        response = Response(
            {"error": f"Tenant with schema {schema_name} does not exist"},
            status=status.HTTP_404_NOT_FOUND
        )

        # Add CORS headers
        response["Access-Control-Allow-Origin"] = "*"
        response["Access-Control-Allow-Methods"] = "GET, POST, OPTIONS"
        response["Access-Control-Allow-Headers"] = "Content-Type, Authorization, X-Schema-Name, X-Auth-Token"

        return response
    except Exception as e:
        logger.error(f"Error in tenant_citizens: {str(e)}")

        # Create error response
        response = Response(
            {"error": str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

        # Add CORS headers
        response["Access-Control-Allow-Origin"] = "*"
        response["Access-Control-Allow-Methods"] = "GET, POST, OPTIONS"
        response["Access-Control-Allow-Headers"] = "Content-Type, Authorization, X-Schema-Name, X-Auth-Token"

        return response

@api_view(['GET', 'POST', 'OPTIONS'])
@authentication_classes([])  # No authentication classes - we'll handle it manually
@permission_classes([AllowAny])  # Allow any user to access this endpoint
def tenant_idcards(request, schema_name):
    """
    Get or create ID cards for a specific tenant by schema name.
    """
    # Handle OPTIONS request for CORS preflight
    if request.method == 'OPTIONS':
        response = Response()
        response["Access-Control-Allow-Origin"] = "*"
        response["Access-Control-Allow-Methods"] = "GET, POST, OPTIONS"
        response["Access-Control-Allow-Headers"] = "Content-Type, Authorization, X-Schema-Name, X-Auth-Token"
        response["Access-Control-Max-Age"] = "86400"  # 24 hours
        return response

    # Log the request for debugging
    print(f"\n\nRequest to tenant_idcards for schema: {schema_name}\n\n")
    print(f"\n\nRequest headers: {request.headers}\n\n")
    # Log the request for debugging
    print(f"\n\nRequest to tenant_idcards for schema: {schema_name}\n\n")
    print(f"\n\nRequest headers: {request.headers}\n\n")

    # Debug schema name variations
    print(f"\n\nTrying to normalize schema name: {schema_name}\n\n")

    # Try different variations of the schema name
    variations = [schema_name]

    # Add variations with spaces and underscores
    if '_' in schema_name:
        variations.append(schema_name.replace('_', ' '))
    if ' ' in schema_name:
        variations.append(schema_name.replace(' ', '_'))

    # For kebele schemas, add more variations
    if schema_name.startswith('kebele'):
        # Extract the kebele number if present
        import re
        match = re.match(r'kebele[_ ]?(\d+)', schema_name)
        if match:
            kebele_number = match.group(1)
            variations.append(f'kebele {kebele_number}')
            variations.append(f'kebele_{kebele_number}')
            variations.append(f'kebele{kebele_number}')

    print(f"\n\nSchema variations to try: {variations}\n\n")

    # Manual authentication
    auth_header = request.headers.get('Authorization', '')
    print(f"\n\nAUTH HEADER: {auth_header}\n\n")

    # Check for refresh token in cookies
    refresh_token = request.COOKIES.get('refresh_token') or request.COOKIES.get('jwt_refresh_token')
    if refresh_token:
        print(f"\n\nFound refresh_token in cookies\n\n")

    # Initialize token_key
    token_key = None

    # First try JWT token authentication
    if auth_header and auth_header.startswith('Bearer '):
        print(f"\n\nFound Bearer token in Authorization header\n\n")
        from accounts.jwt_utils import validate_jwt_token
        # Import User model at the beginning of the block to avoid UnboundLocalError
        from django.contrib.auth import get_user_model
        User = get_user_model()

        token = auth_header.split(' ')[1]
        print(f"\n\nJWT TOKEN: {token[:20]}...\n\n")

        # Validate the JWT token
        payload = validate_jwt_token(token)
        if payload:
            print(f"\n\nJWT token is valid\n\n")

            # Get user from token
            try:
                user_id = payload.get('sub')
                # Set schema from token if available
                token_schema = payload.get('schema')
                if token_schema:
                    print(f"\n\nSchema from JWT token: {token_schema}\n\n")

                # Continue with the request
                token_key = token
            except Exception as e:
                print(f"\n\nError processing JWT token: {str(e)}\n\n")
        else:
            print(f"\n\nInvalid JWT token\n\n")

            # If JWT token is invalid but we have a refresh token, try to use it
            if refresh_token:
                from accounts.jwt_utils import refresh_jwt_token

                # Try to refresh the token
                print(f"\n\nTrying to refresh token\n\n")
                try:
                    new_access_token, new_refresh_token = refresh_jwt_token(refresh_token)

                    if new_access_token and new_refresh_token:
                        print(f"\n\nToken refreshed successfully\n\n")

                        # Create a response with the new tokens
                        response = Response({
                            "message": "Token refreshed",
                            "access_token": new_access_token,
                            "refresh_token": new_refresh_token
                        })

                        # Set cookies for the new tokens
                        response.set_cookie(
                            'jwt_refresh_token',
                            new_refresh_token,
                            httponly=True,
                            samesite='Lax',
                            path='/',
                            max_age=7 * 24 * 60 * 60  # 7 days
                        )

                        return response
                    else:
                        print(f"\n\nFailed to refresh token\n\n")
                except Exception as e:
                    print(f"\n\nError refreshing token: {str(e)}\n\n")
    elif auth_header and auth_header.startswith('Token '):
        # Legacy token authentication
        token_key = auth_header.split(' ')[1]
        print(f"\n\nTOKEN KEY: {token_key}\n\n")
    elif refresh_token:
        # Try to use refresh token to get a new access token
        from accounts.jwt_utils import refresh_jwt_token

        # Try to refresh the token
        print(f"\n\nTrying to use refresh token\n\n")
        try:
            new_access_token, new_refresh_token = refresh_jwt_token(refresh_token)

            if new_access_token and new_refresh_token:
                print(f"\n\nToken refreshed successfully\n\n")

                # Create a response with the new tokens
                response = Response({
                    "message": "Token refreshed",
                    "access_token": new_access_token,
                    "refresh_token": new_refresh_token
                })

                # Set cookies for the new tokens
                response.set_cookie(
                    'jwt_refresh_token',
                    new_refresh_token,
                    httponly=True,
                    samesite='Lax',
                    path='/',
                    max_age=7 * 24 * 60 * 60  # 7 days
                )

                return response
            else:
                print(f"\n\nFailed to use refresh token\n\n")
        except Exception as e:
            print(f"\n\nError refreshing token: {str(e)}\n\n")

    # Check X-Auth-Token header as fallback
    x_auth_token = request.headers.get('X-Auth-Token')
    if not token_key and x_auth_token:
        print(f"\n\nFound X-Auth-Token header: {x_auth_token}\n\n")
        token_key = x_auth_token

    # If still no token, check if user is already authenticated
    if not token_key and request.user and request.user.is_authenticated:
        print(f"\n\nUser already authenticated: {request.user.username}\n\n")
        # Skip token validation since user is already authenticated
    elif not token_key:
        # No token found, but we'll allow unauthenticated access for GET requests
        if request.method == 'GET':
            print(f"\n\nNo authentication token found, but allowing GET request\n\n")
            # Continue without authentication for GET requests
        else:
            # For non-GET requests, require authentication
            print(f"\n\nNo authentication token found for non-GET request\n\n")

            # Create error response with CORS headers
            response = Response(
                {"error": "Authentication credentials were not provided."},
                status=status.HTTP_401_UNAUTHORIZED
            )

            # Add CORS headers
            response["Access-Control-Allow-Origin"] = "*"
            response["Access-Control-Allow-Methods"] = "GET, POST, OPTIONS"
            response["Access-Control-Allow-Headers"] = "Content-Type, Authorization, X-Schema-Name, X-Auth-Token"

            return response

    # Import necessary modules
    from django.db import connection

    # Debug: List all tokens in the public schema
    connection.set_schema_to_public()
    from rest_framework.authtoken.models import Token
    all_tokens = Token.objects.all()
    print(f"\n\nALL TOKENS IN PUBLIC SCHEMA: {[t.key for t in all_tokens]}\n\n")
    print(f"\n\nTOTAL TOKENS IN PUBLIC SCHEMA: {all_tokens.count()}\n\n")

    # Debug: List all users in the public schema
    from django.contrib.auth import get_user_model
    User = get_user_model()
    all_users = User.objects.all()
    print(f"\n\nALL USERS IN PUBLIC SCHEMA: {[u.username for u in all_users]}\n\n")
    print(f"\n\nTOTAL USERS IN PUBLIC SCHEMA: {all_users.count()}\n\n")

    try:
        # Import necessary modules
        from centers.models import Client
        from centers.schema_utils import normalize_schema_name, get_tenant_for_schema

        # Try to find the tenant directly first
        tenant = None
        for variation in variations:
            try:
                tenant = Client.objects.get(schema_name=variation)
                print(f"\n\nFound tenant directly with schema: {variation}\n\n")
                normalized_schema = variation
                break
            except Client.DoesNotExist:
                print(f"\n\nNo tenant found directly for schema: {variation}\n\n")

        # If not found directly, try the normalize function
        if not tenant:
            # Normalize the schema name
            normalized_schema = normalize_schema_name(schema_name)
            print(f"\n\nNormalized schema name: {normalized_schema}\n\n")

            if not normalized_schema:
                print(f"\n\nCould not normalize schema name: {schema_name}\n\n")

                # Create error response with CORS headers
                response = Response(
                    {"error": f"Invalid schema name: {schema_name}"},
                    status=status.HTTP_400_BAD_REQUEST
                )

                # Add CORS headers
                response["Access-Control-Allow-Origin"] = "*"
                response["Access-Control-Allow-Methods"] = "GET, POST, OPTIONS"
                response["Access-Control-Allow-Headers"] = "Content-Type, Authorization, X-Schema-Name, X-Auth-Token"

                return response

            # Get the tenant
            tenant = get_tenant_for_schema(normalized_schema)

        if not tenant:
            print(f"\n\nTenant not found for schema: {normalized_schema}\n\n")

            # Create error response with CORS headers
            response = Response(
                {"error": f"Tenant not found for schema: {schema_name}"},
                status=status.HTTP_404_NOT_FOUND
            )

            # Add CORS headers
            response["Access-Control-Allow-Origin"] = "*"
            response["Access-Control-Allow-Methods"] = "GET, POST, OPTIONS"
            response["Access-Control-Allow-Headers"] = "Content-Type, Authorization, X-Schema-Name, X-Auth-Token"

            return response

        logger.info(f"Found tenant: {tenant.name} (schema: {tenant.schema_name})")

        # Set the tenant for this request
        connection.set_tenant(tenant)
        request.tenant = tenant

        # Set the current tenant in thread-local storage
        from .middleware import set_current_tenant
        set_current_tenant(tenant)

        # Use tenant_context to ensure we're querying the right database
        with tenant_context(tenant):
            # Authenticate the user
            User = get_user_model()

            # For GET requests, we'll allow access without authentication
            if request.method == 'GET':
                # Try to find any admin user in the tenant
                try:
                    admin_user = User.objects.filter(is_staff=True).first()
                    if admin_user:
                        request.user = admin_user
                        print(f"\n\nUsing admin user for GET request: {admin_user.username} (ID: {admin_user.id})\n\n")
                    else:
                        # If no admin user, try to get any user
                        any_user = User.objects.first()
                        if any_user:
                            request.user = any_user
                            print(f"\n\nUsing first available user for GET request: {any_user.username} (ID: {any_user.id})\n\n")
                        else:
                            # Create a temporary anonymous user
                            print(f"\n\nNo users found in tenant, using anonymous user\n\n")
                            from django.contrib.auth.models import AnonymousUser
                            request.user = AnonymousUser()
                except Exception as e:
                    print(f"\n\nError finding user for GET request: {str(e)}\n\n")
                    # Create a temporary anonymous user
                    from django.contrib.auth.models import AnonymousUser
                    request.user = AnonymousUser()
            elif auth_header and auth_header.startswith('Bearer '):
                # JWT authentication
                from accounts.jwt_utils import validate_jwt_token

                token = auth_header.split(' ')[1]
                print(f"\n\nValidating JWT token for authentication\n\n")

                # Validate the JWT token
                payload = validate_jwt_token(token)
                if payload:
                    print(f"\n\nJWT token is valid\n\n")

                    # Get user from token
                    try:
                        user_id = payload.get('sub')
                        if user_id:
                            # Try to find the user in the tenant's database
                            try:
                                user = User.objects.get(id=user_id)
                                request.user = user
                                print(f"\n\nAuthenticated user from JWT: {user.email} (ID: {user.id})\n\n")
                            except User.DoesNotExist:
                                # If not found in tenant, try public schema
                                connection.set_schema_to_public()
                                try:
                                    public_user = User.objects.get(id=user_id)
                                    # Switch back to tenant schema
                                    connection.set_tenant(tenant)

                                    # Try to find user by email in tenant
                                    try:
                                        user = User.objects.get(email=public_user.email)
                                        request.user = user
                                        print(f"\n\nFound matching user in tenant: {user.email}\n\n")
                                    except User.DoesNotExist:
                                        # Create user in tenant
                                        print(f"\n\nCreating user in tenant from JWT token\n\n")
                                        user = User.objects.create(
                                            id=public_user.id,
                                            email=public_user.email,
                                            username=public_user.username,
                                            first_name=public_user.first_name,
                                            last_name=public_user.last_name,
                                            is_staff=public_user.is_staff,
                                            is_superuser=public_user.is_superuser,
                                            is_active=public_user.is_active,
                                            password=public_user.password
                                        )
                                        request.user = user
                                        print(f"\n\nCreated user in tenant: {user.email}\n\n")
                                except User.DoesNotExist:
                                    print(f"\n\nUser with ID {user_id} not found in any schema\n\n")
                                    return Response({"error": "User not found"}, status=status.HTTP_401_UNAUTHORIZED)
                                except Exception as e:
                                    print(f"\n\nError finding user in public schema: {str(e)}\n\n")
                                    return Response({"error": f"Error finding user: {str(e)}"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
                        else:
                            print(f"\n\nNo user ID in JWT token\n\n")
                            return Response({"error": "Invalid token: no user ID"}, status=status.HTTP_401_UNAUTHORIZED)
                    except Exception as e:
                        print(f"\n\nError processing JWT token: {str(e)}\n\n")
                        return Response({"error": f"Error processing token: {str(e)}"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
                else:
                    print(f"\n\nInvalid JWT token\n\n")
                    return Response({"error": "Invalid token"}, status=status.HTTP_401_UNAUTHORIZED)
            else:
                print(f"\n\nInvalid JWT token and no refresh token\n\n")
                # Allow GET requests without authentication
                if request.method == 'GET':
                    print(f"\n\nAllowing GET request without authentication\n\n")
                    # Continue without authentication for GET requests
                    token_key = None
                else:
                    return Response({"error": "Invalid JWT token and no refresh token available."}, status=status.HTTP_401_UNAUTHORIZED)

            # Now we have an authenticated user and the correct tenant context

            # Handle GET request
            if request.method == 'GET':
                # Get all ID cards
                idcards = IDCard.objects.all()

                # Apply search filter if provided
                search_query = request.query_params.get('search', '')
                if search_query:
                    idcards = idcards.filter(
                        Q(card_number__icontains=search_query) |
                        Q(citizen__first_name__icontains=search_query) |
                        Q(citizen__last_name__icontains=search_query) |
                        Q(status__icontains=search_query)
                    )

                # Serialize the data
                serializer = IDCardListSerializer(idcards, many=True)

                # Create response with CORS headers
                response = Response(serializer.data)

                # Add CORS headers
                response["Access-Control-Allow-Origin"] = "*"
                response["Access-Control-Allow-Methods"] = "GET, POST, OPTIONS"
                response["Access-Control-Allow-Headers"] = "Content-Type, Authorization, X-Schema-Name, X-Auth-Token"

                return response

            # Handle POST request
            elif request.method == 'POST':
                from idcards.serializers import IDCardSerializer
                serializer = IDCardSerializer(data=request.data)
                if serializer.is_valid():
                    # Set created_by to current user
                    serializer.save(created_by=request.user)
                    return Response(serializer.data, status=status.HTTP_201_CREATED)
                return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    except Client.DoesNotExist:
        return Response(
            {"error": f"Tenant with schema {schema_name} does not exist"},
            status=status.HTTP_404_NOT_FOUND
        )
    except Exception as e:
        logger.error(f"Error in tenant_idcards: {str(e)}")
        return Response(
            {"error": str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['GET'])
@authentication_classes([])  # No authentication classes - we'll handle it manually
@permission_classes([AllowAny])  # Allow any user to access this endpoint
def tenant_idcard_templates(request, schema_name):
    """
    Get ID card templates for a specific tenant by schema name.
    """
    # Manual authentication
    auth_header = request.headers.get('Authorization', '')
    print(f"\n\nAUTH HEADER: {auth_header}\n\n")

    # Check for refresh token in cookies
    refresh_token = request.COOKIES.get('refresh_token')
    if refresh_token:
        print(f"\n\nFound refresh_token in cookies\n\n")

    # First try JWT token authentication
    if auth_header and auth_header.startswith('Bearer '):
        print(f"\n\nFound Bearer token in Authorization header\n\n")
        from accounts.jwt_utils import validate_jwt_token, get_schema_from_token
        # Import User model at the beginning of the block to avoid UnboundLocalError
        from django.contrib.auth import get_user_model
        User = get_user_model()

        token = auth_header.split(' ')[1]
        print(f"\n\nJWT TOKEN: {token[:20]}...\n\n")

        # Validate the JWT token
        payload = validate_jwt_token(token)
        if payload:
            print(f"\n\nJWT token is valid\n\n")

            # Get user from token
            try:
                user_id = payload.get('sub')
                user = User.objects.get(id=user_id)
                request.user = user
                print(f"\n\nAuthenticated user from JWT: {user.email} (ID: {user.id})\n\n")

                # Continue with the request
                token_key = token
            except User.DoesNotExist:
                print(f"\n\nUser with ID {payload.get('sub')} not found\n\n")
                return Response({"error": "User not found"}, status=status.HTTP_401_UNAUTHORIZED)
        else:
            print(f"\n\nInvalid JWT token\n\n")

            # If JWT token is invalid but we have a refresh token, try to use it
            if refresh_token:
                from accounts.jwt_utils import refresh_jwt_token

                # Try to refresh the token
                print(f"\n\nTrying to refresh token\n\n")
                new_access_token, new_refresh_token = refresh_jwt_token(refresh_token)

                if new_access_token and new_refresh_token:
                    print(f"\n\nToken refreshed successfully\n\n")

                    # Create a response with the new tokens
                    response = Response({
                        "message": "Token refreshed",
                        "access_token": new_access_token,
                        "refresh_token": new_refresh_token
                    })

                    # Set cookies for the new tokens
                    response.set_cookie(
                        'refresh_token',
                        new_refresh_token,
                        httponly=True,
                        samesite='Lax',
                        path='/',
                        max_age=7 * 24 * 60 * 60  # 7 days
                    )

                    return response
                else:
                    print(f"\n\nFailed to refresh token\n\n")

            # If we get here, authentication has failed
            print(f"\n\nAuthentication failed\n\n")
            # Allow GET requests without authentication
            if request.method == 'GET':
                print(f"\n\nAllowing GET request without authentication\n\n")
                # Continue without authentication for GET requests
                token_key = None
            else:
                return Response({"error": "Authentication credentials were not provided."}, status=status.HTTP_401_UNAUTHORIZED)
    elif refresh_token:
        # Try to use refresh token to get a new access token
        from accounts.jwt_utils import refresh_jwt_token

        # Try to refresh the token
        print(f"\n\nTrying to use refresh token\n\n")
        new_access_token, new_refresh_token = refresh_jwt_token(refresh_token)

        if new_access_token and new_refresh_token:
            print(f"\n\nToken refreshed successfully\n\n")

            # Create a response with the new tokens
            response = Response({
                "message": "Token refreshed",
                "access_token": new_access_token,
                "refresh_token": new_refresh_token
            })

            # Set cookies for the new tokens
            response.set_cookie(
                'refresh_token',
                new_refresh_token,
                httponly=True,
                samesite='Lax',
                path='/',
                max_age=7 * 24 * 60 * 60  # 7 days
            )

            return response
        else:
            print(f"\n\nFailed to use refresh token\n\n")
            # Allow GET requests without authentication
            if request.method == 'GET':
                print(f"\n\nAllowing GET request without authentication\n\n")
                # Continue without authentication for GET requests
                token_key = None
            else:
                return Response({"error": "Authentication credentials were not provided."}, status=status.HTTP_401_UNAUTHORIZED)
    else:
        print(f"\n\nNo authentication credentials provided\n\n")
        # Allow GET requests without authentication
        if request.method == 'GET':
            print(f"\n\nAllowing GET request without authentication\n\n")
            # Continue without authentication for GET requests
            token_key = None
        else:
            return Response({"error": "Authentication credentials were not provided."}, status=status.HTTP_401_UNAUTHORIZED)

    # Import necessary modules
    from django.db import connection

    # Debug: List all users in the public schema
    connection.set_schema_to_public()
    from django.contrib.auth import get_user_model
    User = get_user_model()

    try:
        all_users = User.objects.all()
        print(f"\n\nALL USERS IN PUBLIC SCHEMA: {[u.username for u in all_users]}\n\n")
        print(f"\n\nTOTAL USERS IN PUBLIC SCHEMA: {all_users.count()}\n\n")
    except Exception as e:
        print(f"\n\nError getting users from public schema: {str(e)}\n\n")

    try:
        # Import schema utilities
        from centers.schema_utils import normalize_schema_name, get_tenant_for_schema, set_tenant_for_request

        # Normalize the schema name
        normalized_schema = normalize_schema_name(schema_name)
        logger.info(f"Normalized schema name: {normalized_schema}")

        if not normalized_schema:
            logger.error(f"Could not normalize schema name: {schema_name}")
            return Response({"error": f"Invalid schema name: {schema_name}"}, status=status.HTTP_400_BAD_REQUEST)

        # Get the tenant
        tenant = get_tenant_for_schema(normalized_schema)

        if not tenant:
            logger.error(f"Tenant not found for schema: {normalized_schema}")
            return Response({"error": f"Tenant not found for schema: {schema_name}"}, status=status.HTTP_404_NOT_FOUND)

        logger.info(f"Found tenant: {tenant.name} (schema: {tenant.schema_name})")

        # Set the tenant for this request
        connection.set_tenant(tenant)
        request.tenant = tenant

        # Set the current tenant in thread-local storage
        from .middleware import set_current_tenant
        set_current_tenant(tenant)

        # Use tenant_context to ensure we're querying the right database
        with tenant_context(tenant):
            # Authenticate the user
            User = get_user_model()

            # For GET requests, we'll allow access without authentication
            if request.method == 'GET':
                # Try to find any admin user in the tenant
                try:
                    admin_user = User.objects.filter(is_staff=True).first()
                    if admin_user:
                        request.user = admin_user
                        print(f"\n\nUsing admin user for GET request: {admin_user.username} (ID: {admin_user.id})\n\n")
                    else:
                        # If no admin user, try to get any user
                        any_user = User.objects.first()
                        if any_user:
                            request.user = any_user
                            print(f"\n\nUsing first available user for GET request: {any_user.username} (ID: {any_user.id})\n\n")
                        else:
                            # Create a temporary anonymous user
                            print(f"\n\nNo users found in tenant, using anonymous user\n\n")
                            from django.contrib.auth.models import AnonymousUser
                            request.user = AnonymousUser()
                except Exception as e:
                    print(f"\n\nError finding user for GET request: {str(e)}\n\n")
                    # Create a temporary anonymous user
                    from django.contrib.auth.models import AnonymousUser
                    request.user = AnonymousUser()

            # Now we have an authenticated user and the correct tenant context
            # Get all ID card templates
            templates = IDCardTemplate.objects.all()

            # Apply search filter if provided
            search_query = request.query_params.get('search', '')
            if search_query:
                templates = templates.filter(
                    Q(name__icontains=search_query)
                )

            # Serialize the data
            serializer = IDCardTemplateListSerializer(templates, many=True)

            return Response(serializer.data)
    except Client.DoesNotExist:
        return Response(
            {"error": f"Tenant with schema {schema_name} does not exist"},
            status=status.HTTP_404_NOT_FOUND
        )
    except Exception as e:
        logger.error(f"Error in tenant_idcard_templates: {str(e)}")
        return Response(
            {"error": str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['POST'])
@authentication_classes([])  # No authentication classes - we'll handle it manually
@permission_classes([AllowAny])  # We'll handle authentication manually
def tenant_citizen_generate_id(request, schema_name, citizen_id):
    """
    Generate ID number for a specific citizen in a tenant.
    """
    # Manual authentication
    auth_header = request.headers.get('Authorization', '')
    print(f"\n\nAUTH HEADER for generate_id: {auth_header}\n\n")

    if not auth_header or not auth_header.startswith('Bearer '):
        print(f"\n\nINVALID AUTH HEADER for generate_id: {auth_header}\n\n")
        return Response({"error": "Authentication credentials were not provided."}, status=status.HTTP_401_UNAUTHORIZED)

    token_key = auth_header.split(' ')[1]

    try:
        # Import necessary modules
        from centers.models import Client
        from django.db import connection
        from centers.schema_utils import normalize_schema_name, get_tenant_for_schema, set_tenant_for_request

        # Normalize the schema name
        normalized_schema = normalize_schema_name(schema_name)
        logger.info(f"Normalized schema name: {normalized_schema}")

        if not normalized_schema:
            logger.error(f"Could not normalize schema name: {schema_name}")
            return Response({"error": f"Invalid schema name: {schema_name}"}, status=status.HTTP_400_BAD_REQUEST)

        # Get the tenant
        tenant = get_tenant_for_schema(normalized_schema)

        if not tenant:
            logger.error(f"Tenant not found for schema: {normalized_schema}")
            return Response({"error": f"Tenant not found for schema: {schema_name}"}, status=status.HTTP_404_NOT_FOUND)

        logger.info(f"Found tenant: {tenant.name} (schema: {tenant.schema_name})")

        # Set the tenant for this request
        connection.set_tenant(tenant)
        request.tenant = tenant

        # Set the current tenant in thread-local storage
        from .middleware import set_current_tenant
        set_current_tenant(tenant)

        # Use tenant_context to ensure we're querying the right database
        with tenant_context(tenant):
            # Authenticate the user with JWT
            User = get_user_model()
            from accounts.jwt_utils import validate_jwt_token

            try:
                # Validate the JWT token
                print(f"\n\nValidating JWT token for tenant {tenant.schema_name}\n\n")
                payload = validate_jwt_token(token_key)

                if not payload:
                    print(f"\n\nInvalid JWT token\n\n")
                    return Response({"error": "Invalid token"}, status=status.HTTP_401_UNAUTHORIZED)

                # Get user from token
                user_id = payload.get('sub')
                if not user_id:
                    print(f"\n\nNo user ID in token payload\n\n")
                    return Response({"error": "Invalid token payload"}, status=status.HTTP_401_UNAUTHORIZED)

                # Try to find the user in the tenant's database
                try:
                    user = User.objects.get(id=user_id)
                    request.user = user
                    print(f"\n\nAuthenticated user from JWT: {user.email} (ID: {user.id})\n\n")
                except User.DoesNotExist:
                    print(f"\n\nUser with ID {user_id} not found in tenant {tenant.schema_name}\n\n")

                    # For development purposes, try to find any admin user
                    try:
                        admin_user = User.objects.filter(is_staff=True).first()
                        if admin_user:
                            request.user = admin_user
                            print(f"\n\nUsing admin user: {admin_user.username} (ID: {admin_user.id})\n\n")
                        else:
                            # If no admin user, try to get any user
                            any_user = User.objects.first()
                            if any_user:
                                request.user = any_user
                                print(f"\n\nUsing first available user: {any_user.username} (ID: {any_user.id})\n\n")
                            else:
                                return Response({"error": "No users found in tenant"}, status=status.HTTP_401_UNAUTHORIZED)
                    except Exception as e:
                        print(f"\n\nError finding admin user: {str(e)}\n\n")
                        return Response({"error": f"Error finding admin user: {str(e)}"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
            except Exception as e:
                print(f"\n\nError authenticating user: {str(e)}\n\n")
                return Response({"error": f"Error authenticating user: {str(e)}"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

            # Get the citizen
            try:
                print(f"\n\nLooking for citizen with ID: {citizen_id}\n\n")
                citizen = Citizen.objects.get(pk=citizen_id)
                print(f"\n\nFound citizen: {citizen.first_name} {citizen.last_name} (ID: {citizen.id})\n\n")
                print(f"\n\nCurrent ID number: {citizen.id_number or 'None'}\n\n")
            except Citizen.DoesNotExist:
                print(f"\n\nCitizen with ID {citizen_id} does not exist\n\n")
                return Response({"error": f"Citizen with ID {citizen_id} does not exist"}, status=status.HTTP_404_NOT_FOUND)

            # Check if ID number already exists
            if citizen.id_number:
                print(f"\n\nID number already exists: {citizen.id_number}\n\n")
                return Response({
                    'message': 'ID number already exists',
                    'id_number': citizen.id_number
                })

            # Generate a unique ID number using the format: first letter of city + first 2 letters of subcity + kebele number + 6 digit sequence
            old_id = citizen.id_number

            # Get the city, subcity, and kebele information
            city_prefix = ''
            subcity_prefix = ''
            kebele_num = '01'  # Default kebele number

            # Get tenant information
            from django.db import connection
            from centers.models import Client

            # Get the current schema name
            current_schema = connection.schema_name
            print(f"\n\nCurrent schema: {current_schema}\n\n")

            # Try to get the tenant from the current schema
            try:
                # Get the tenant for the current schema
                tenant = Client.objects.get(schema_name=current_schema)
            except Exception as e:
                print(f"\n\nError getting tenant: {e}\n\n")
                tenant = None
            if tenant and tenant.schema_type == 'KEBELE':
                # For kebele tenant, get the subcity from parent
                if hasattr(tenant, 'parent') and tenant.parent:
                    subcity = tenant.parent
                    if hasattr(subcity, 'name') and subcity.name:
                        # Get the subcity name directly from the tenant's parent
                        subcity_name = tenant.parent.name
                        print(f"\n\nTenant parent name (subcity): {subcity_name}\n\n")
                        # Some subcity names might be like "Azezo (Default City)" - extract just the first part
                        subcity_name = subcity_name.split('(')[0].strip()
                        # Get the first two letters
                        subcity_prefix = subcity_name[:2].upper()

                    # Get city from subcity's parent
                    if hasattr(subcity, 'parent') and subcity.parent:
                        city = subcity.parent
                        if hasattr(city, 'city_name') and city.city_name:
                            city_prefix = city.city_name[0].upper()

                # Extract kebele number from tenant schema name and tenant name
                if hasattr(tenant, 'schema_name') and tenant.schema_name:
                    import re
                    # First try to extract from schema_name (e.g., "kebele 14")
                    schema_digits = re.findall(r'\d+', tenant.schema_name)
                    if schema_digits:
                        kebele_num = schema_digits[-1].zfill(2)[:2]  # Ensure it's 2 digits with leading zeros
                        print(f"\n\nExtracted kebele number from schema: {kebele_num}\n\n")
                    # If that fails, try to extract from tenant name
                    elif hasattr(tenant, 'name') and tenant.name:
                        name_digits = re.findall(r'\d+', tenant.name)
                        if name_digits:
                            kebele_num = name_digits[-1].zfill(2)[:2]  # Ensure it's 2 digits with leading zeros
                            print(f"\n\nExtracted kebele number from name: {kebele_num}\n\n")

            # If we couldn't get the city prefix, use a default
            if not city_prefix:
                city_prefix = 'G'

            # If we couldn't get the subcity prefix, use a default
            if not subcity_prefix:
                subcity_prefix = 'XX'

            # Generate a sequential 6-digit number
            import time
            timestamp = int(time.time() * 1000) % 1000000  # Last 6 digits of current timestamp in milliseconds
            random_suffix = f"{timestamp:06d}"  # Ensure it's 6 digits with leading zeros

            # Format: First letter of city + First 2 letters of subcity + Kebele number + 6 digit sequence
            new_id = f"{city_prefix}{subcity_prefix}{kebele_num}{random_suffix}"
            print(f"\n\nGenerating new ID number: {new_id} (old: {old_id or 'None'})\n\n")
            citizen.id_number = new_id

            # Save the citizen object
            print(f"\n\nSaving citizen with new ID number...\n\n")
            try:
                Citizen.objects.filter(pk=citizen.pk).update(id_number=citizen.id_number)
                print(f"\n\nCitizen saved successfully with new ID number: {citizen.id_number}\n\n")
            except Exception as e:
                print(f"\n\nError saving citizen: {str(e)}\n\n")
                return Response({"error": f"Error saving citizen: {str(e)}"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

            # Verify the ID number was saved
            try:
                updated_citizen = Citizen.objects.get(pk=citizen_id)
                print(f"\n\nVerified citizen ID number: {updated_citizen.id_number}\n\n")
            except Exception as e:
                print(f"\n\nError verifying citizen: {str(e)}\n\n")

            return Response({
                'message': 'ID number generated successfully',
                'id_number': citizen.id_number
            })
    except Client.DoesNotExist:
        return Response(
            {"error": f"Tenant with schema {schema_name} does not exist"},
            status=status.HTTP_404_NOT_FOUND
        )
    except Exception as e:
        logger.error(f"Error in tenant_citizen_generate_id: {str(e)}")
        return Response(
            {"error": str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['GET'])
@authentication_classes([])  # No authentication classes - we'll handle it manually
@permission_classes([AllowAny])  # We'll handle authentication manually
def tenant_idcard_detail(request, schema_name, id):
    """
    Get a specific ID card for a tenant by schema name and ID.
    """
    # Manual authentication
    auth_header = request.headers.get('Authorization', '')
    print(f"\n\nAUTH HEADER for idcard_detail: {auth_header}\n\n")

    if not auth_header or not auth_header.startswith('Bearer '):
        print(f"\n\nINVALID AUTH HEADER for idcard_detail: {auth_header}\n\n")
        return Response({"error": "Authentication credentials were not provided."}, status=status.HTTP_401_UNAUTHORIZED)

    token_key = auth_header.split(' ')[1]

    try:
        # Import necessary modules
        from django.db import connection
        from centers.schema_utils import normalize_schema_name, get_tenant_for_schema, set_tenant_for_request

        # Normalize the schema name
        normalized_schema = normalize_schema_name(schema_name)
        logger.info(f"Normalized schema name: {normalized_schema}")

        if not normalized_schema:
            logger.error(f"Could not normalize schema name: {schema_name}")
            return Response({"error": f"Invalid schema name: {schema_name}"}, status=status.HTTP_400_BAD_REQUEST)

        # Get the tenant
        tenant = get_tenant_for_schema(normalized_schema)

        if not tenant:
            logger.error(f"Tenant not found for schema: {normalized_schema}")
            return Response({"error": f"Tenant not found for schema: {schema_name}"}, status=status.HTTP_404_NOT_FOUND)

        logger.info(f"Found tenant: {tenant.name} (schema: {tenant.schema_name})")

        # Set the tenant for this request
        connection.set_tenant(tenant)
        request.tenant = tenant

        # Use tenant_context to ensure we're querying the right database
        with tenant_context(tenant):
            # Authenticate the user
            User = get_user_model()
            from rest_framework.authtoken.models import Token

            try:
                # First try to find the token in the tenant's database
                print(f"\n\nLooking for token {token_key} in tenant {tenant.schema_name}\n\n")
                token = Token.objects.get(key=token_key)
                user = token.user
                request.user = user
                print(f"\n\nAuthenticated user: {user.username} (ID: {user.id})\n\n")
            except Token.DoesNotExist:
                # If not found, try the public schema
                print(f"\n\nToken not found in tenant {tenant.schema_name}, trying public schema\n\n")
                connection.set_schema_to_public()
                try:
                    token = Token.objects.get(key=token_key)
                    print(f"\n\nFound token in public schema for user {token.user.username} (ID: {token.user.id})\n\n")
                    # Now switch back to the tenant schema
                    connection.set_tenant(tenant)
                    # Try to find the user in the tenant's database
                    try:
                        tenant_user = User.objects.get(email=token.user.email)
                        request.user = tenant_user
                        print(f"\n\nFound matching user in tenant: {tenant_user.username} (ID: {tenant_user.id})\n\n")
                    except User.DoesNotExist:
                        print(f"\n\nUser {token.user.email} not found in tenant {tenant.schema_name}\n\n")
                        # Use the public schema user
                        request.user = token.user
                        print(f"\n\nUsing public schema user: {token.user.username} (ID: {token.user.id})\n\n")
                except Token.DoesNotExist:
                    print(f"\n\nToken {token_key} not found in public schema\n\n")

                    # For development purposes, accept a hardcoded token
                    if token_key == '01aa7be65fbda335a0b29edd56c967ad6112fa6b':
                        print(f"\n\nUsing hardcoded development token\n\n")
                        # Find any admin user in the tenant
                        try:
                            admin_user = User.objects.filter(is_staff=True).first()
                            if admin_user:
                                request.user = admin_user
                                print(f"\n\nUsing admin user: {admin_user.username} (ID: {admin_user.id})\n\n")
                            else:
                                # If no admin user, try to get any user
                                any_user = User.objects.first()
                                if any_user:
                                    request.user = any_user
                                    print(f"\n\nUsing first available user: {any_user.username} (ID: {any_user.id})\n\n")
                                else:
                                    return Response({"error": "No users found in tenant"}, status=status.HTTP_401_UNAUTHORIZED)
                        except Exception as e:
                            print(f"\n\nError finding admin user: {str(e)}\n\n")
                            return Response({"error": f"Error finding admin user: {str(e)}"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
                    else:
                        return Response({"error": "Invalid token."}, status=status.HTTP_401_UNAUTHORIZED)
                except Exception as e:
                    print(f"\n\nError authenticating user: {str(e)}\n\n")
                    return Response({"error": f"Error authenticating user: {str(e)}"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

            # Get the specific ID card
            try:
                idcard = IDCard.objects.get(pk=id)
            except IDCard.DoesNotExist:
                return Response(
                    {"error": f"ID card with ID {id} does not exist"},
                    status=status.HTTP_404_NOT_FOUND
                )

            # Use the detailed serializer for a single ID card
            from idcards.serializers import IDCardSerializer
            serializer = IDCardSerializer(idcard)
            return Response(serializer.data)
    except Client.DoesNotExist:
        return Response(
            {"error": f"Tenant with schema {schema_name} does not exist"},
            status=status.HTTP_404_NOT_FOUND
        )
    except Exception as e:
        logger.error(f"Error in tenant_idcard_detail: {str(e)}")
        return Response(
            {"error": str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )