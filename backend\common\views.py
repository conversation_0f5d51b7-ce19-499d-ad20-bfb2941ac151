from rest_framework import viewsets
from rest_framework.permissions import AllowAny
from django.db.models import Q
from rest_framework.decorators import action
from rest_framework.response import Response
from .models import Religion, CitizenStatus, MaritalStatus, EmploymentType, RelationshipType, EmployeeType, Country, Region, Ketena, DocumentType
from .serializers import (
    ReligionSerializer, CitizenStatusSerializer, MaritalStatusSerializer,
    EmploymentTypeSerializer, RelationshipTypeSerializer, EmployeeTypeSerializer,
    CountrySerializer, RegionSerializer, KetenaSerializer, DocumentTypeSerializer,
    SubcitySerializer, CenterSerializer
)
from centers.models import Subcity, Center


class ReligionViewSet(viewsets.ReadOnlyModelViewSet):
    """
    API endpoint that allows religions to be viewed.
    """
    queryset = Religion.objects.all()
    serializer_class = ReligionSerializer
    permission_classes = [AllowAny]


class CitizenStatusViewSet(viewsets.ReadOnlyModelViewSet):
    """
    API endpoint that allows citizen statuses to be viewed.
    """
    queryset = CitizenStatus.objects.all()
    serializer_class = CitizenStatusSerializer
    permission_classes = [AllowAny]


class MaritalStatusViewSet(viewsets.ReadOnlyModelViewSet):
    """
    API endpoint that allows marital statuses to be viewed.
    """
    queryset = MaritalStatus.objects.all()
    serializer_class = MaritalStatusSerializer
    permission_classes = [AllowAny]


class EmploymentTypeViewSet(viewsets.ReadOnlyModelViewSet):
    """
    API endpoint that allows employment types to be viewed.
    """
    queryset = EmploymentType.objects.all()
    serializer_class = EmploymentTypeSerializer
    permission_classes = [AllowAny]


class RelationshipTypeViewSet(viewsets.ReadOnlyModelViewSet):
    """
    API endpoint that allows relationship types to be viewed.
    """
    queryset = RelationshipType.objects.all()
    serializer_class = RelationshipTypeSerializer
    permission_classes = [AllowAny]


class EmployeeTypeViewSet(viewsets.ReadOnlyModelViewSet):
    """
    API endpoint that allows employee types to be viewed.
    """
    queryset = EmployeeType.objects.all()
    serializer_class = EmployeeTypeSerializer
    permission_classes = [AllowAny]


class CountryViewSet(viewsets.ReadOnlyModelViewSet):
    """
    API endpoint that allows countries to be viewed.
    """
    queryset = Country.objects.all()
    serializer_class = CountrySerializer
    permission_classes = [AllowAny]


class RegionViewSet(viewsets.ReadOnlyModelViewSet):
    """
    API endpoint that allows regions to be viewed.
    """
    queryset = Region.objects.all()
    serializer_class = RegionSerializer
    permission_classes = [AllowAny]

    def get_queryset(self):
        queryset = Region.objects.all()
        country_id = self.request.query_params.get('country', None)
        if country_id is not None:
            queryset = queryset.filter(country_id=country_id)
        return queryset


class KetenaViewSet(viewsets.ModelViewSet):
    """
    API endpoint that allows ketenas to be viewed and created.
    """
    queryset = Ketena.objects.all()
    serializer_class = KetenaSerializer
    permission_classes = [AllowAny]

    def get_queryset(self):
        queryset = Ketena.objects.all()
        kebele_id = self.request.query_params.get('kebele', None)
        if kebele_id is not None:
            queryset = queryset.filter(kebele_id=kebele_id)
        return queryset


class DocumentTypeViewSet(viewsets.ReadOnlyModelViewSet):
    """
    API endpoint that allows document types to be viewed.
    """
    queryset = DocumentType.objects.all()
    serializer_class = DocumentTypeSerializer
    permission_classes = [AllowAny]


class SubcityViewSet(viewsets.ReadOnlyModelViewSet):
    """
    API endpoint that allows subcities to be viewed.
    """
    queryset = Subcity.objects.all()
    serializer_class = SubcitySerializer
    permission_classes = [AllowAny]

    def get_queryset(self):
        queryset = Subcity.objects.all()
        city_id = self.request.query_params.get('city', None)
        if city_id is not None:
            queryset = queryset.filter(city_id=city_id)
        return queryset


class CenterViewSet(viewsets.ReadOnlyModelViewSet):
    """
    API endpoint that allows centers (kebeles) to be viewed.
    """
    queryset = Center.objects.all()
    serializer_class = CenterSerializer
    permission_classes = [AllowAny]

    def get_queryset(self):
        queryset = Center.objects.all()
        subcity_id = self.request.query_params.get('subcity', None)
        if subcity_id is not None:
            queryset = queryset.filter(subcity_id=subcity_id)
        return queryset