from django.contrib.admin import AdminSite
from django.contrib import admin
from django.contrib.auth.models import Group
from django.contrib.auth.admin import GroupAdmin
from django.contrib.auth import get_user_model
from centers.models import Client, Domain, City, Subcity, Kebele, CenterType, Ketena
from citizens.models_citizen import CurrentStatus
from centers.admin import ClientAdmin, DomainAdmin, CityAdmin, SubcityAdmin, CenterAdmin, CenterTypeAdmin, KetenaAdmin
from citizens.admin_citizen import CurrentStatusAdmin
from accounts.models import User
from accounts.admin import UserAdmin
from citizens.models import Citizen
from citizens.models_document import Document
from citizens.models_family import Child, Parent, EmergencyContact, Spouse
from common.models import RelationshipType
from citizens.models_biometric import Biometric, Photo
from citizens.admin_document import DocumentAdmin
from citizens.admin_family import ChildAdmin, ParentAdmin, EmergencyContactAdmin, SpouseAdmin
from citizens.admin_biometric import BiometricAdmin, PhotoAdmin
from idcards.models import IDCard, IDCardTemplate
from centers.models import Client
from django_tenants.utils import tenant_context
from django.db.models import Cha<PERSON><PERSON>ield, Value
from django.db.models.functions import Concat

# Create a custom admin site
class NeoCamelotAdminSite(AdminSite):
    site_header = 'NeoCamelot Administration'
    site_title = 'NeoCamelot Admin'
    index_title = 'NeoCamelot Administration'

# Create a global admin class for Citizen that works across all tenants
class GlobalCitizenAdmin(admin.ModelAdmin):
    list_display = ('schema_name', 'digital_id', 'first_name', 'middle_name', 'last_name', 'gender', 'date_of_birth', 'is_active')
    list_filter = ('is_active', 'gender', 'nationality', 'nationality_country')
    search_fields = ('first_name', 'middle_name', 'last_name', 'digital_id', 'id_number', 'email', 'phone')
    readonly_fields = ('schema_name', 'digital_id', 'id_number', 'uuid', 'created_at', 'updated_at', 'created_by')

    def get_queryset(self, request):
        # Get all tenants
        tenants = Client.objects.exclude(schema_name='public')

        # Initialize an empty list to store citizens from all tenants
        all_citizens = []

        # Iterate through each tenant and get their citizens
        for tenant in tenants:
            try:
                with tenant_context(tenant):
                    # Get citizens for this tenant
                    citizens = list(Citizen.objects.all())

                    # Add schema_name attribute to each citizen
                    for citizen in citizens:
                        citizen.schema_name = tenant.schema_name

                    # Add citizens to the list
                    all_citizens.extend(citizens)
            except Exception as e:
                print(f"Error accessing tenant {tenant.schema_name}: {str(e)}")

        # Return a queryset-like object
        return all_citizens

    def schema_name(self, obj):
        return obj.schema_name if hasattr(obj, 'schema_name') else 'Unknown'
    schema_name.short_description = 'Tenant'

    def has_add_permission(self, request):
        return False

    def has_change_permission(self, request, obj=None):
        return False

    def has_delete_permission(self, request, obj=None):
        return False

# Create a global admin class for IDCardTemplate that works across all tenants
class GlobalIDCardTemplateAdmin(admin.ModelAdmin):
    list_display = ('schema_name', 'name', 'is_default', 'created_at')
    list_filter = ('is_default',)
    search_fields = ('name',)
    readonly_fields = ('schema_name', 'created_at', 'updated_at')

    def get_queryset(self, request):
        # Get all tenants
        tenants = Client.objects.exclude(schema_name='public')

        # Initialize an empty list to store templates from all tenants
        all_templates = []

        # Iterate through each tenant and get their templates
        for tenant in tenants:
            try:
                with tenant_context(tenant):
                    # Get templates for this tenant
                    templates = list(IDCardTemplate.objects.all())

                    # Add schema_name attribute to each template
                    for template in templates:
                        template.schema_name = tenant.schema_name

                    # Add templates to the list
                    all_templates.extend(templates)
            except Exception as e:
                print(f"Error accessing tenant {tenant.schema_name}: {str(e)}")

        # Return a queryset-like object
        return all_templates

    def schema_name(self, obj):
        return obj.schema_name if hasattr(obj, 'schema_name') else 'Unknown'
    schema_name.short_description = 'Tenant'

    def has_add_permission(self, request):
        return False

    def has_change_permission(self, request, obj=None):
        return False

    def has_delete_permission(self, request, obj=None):
        return False

# Create a global admin class for IDCard that works across all tenants
class GlobalIDCardAdmin(admin.ModelAdmin):
    list_display = ('schema_name', 'card_number', 'citizen_name', 'issue_date', 'expiry_date', 'status')
    list_filter = ('status', 'issue_date')
    search_fields = ('card_number',)
    readonly_fields = ('schema_name', 'card_number', 'created_at', 'updated_at', 'created_by', 'approved_by')

    def get_queryset(self, request):
        # Get all tenants
        tenants = Client.objects.exclude(schema_name='public')

        # Initialize an empty list to store ID cards from all tenants
        all_cards = []

        # Iterate through each tenant and get their ID cards
        for tenant in tenants:
            try:
                with tenant_context(tenant):
                    # Get ID cards for this tenant
                    cards = list(IDCard.objects.all())

                    # Add schema_name attribute to each card
                    for card in cards:
                        card.schema_name = tenant.schema_name

                    # Add cards to the list
                    all_cards.extend(cards)
            except Exception as e:
                print(f"Error accessing tenant {tenant.schema_name}: {str(e)}")

        # Return a queryset-like object
        return all_cards

    def schema_name(self, obj):
        return obj.schema_name if hasattr(obj, 'schema_name') else 'Unknown'
    schema_name.short_description = 'Tenant'

    def citizen_name(self, obj):
        try:
            return f"{obj.citizen.first_name} {obj.citizen.last_name}" if obj.citizen else 'Unknown'
        except Exception:
            return f"Unknown (ID: {obj.citizen_id if hasattr(obj, 'citizen_id') else 'N/A'})"
    citizen_name.short_description = 'Citizen'

    def has_add_permission(self, request):
        return False

    def has_change_permission(self, request, obj=None):
        return False

    def has_delete_permission(self, request, obj=None):
        return False

# Create an instance of the custom admin site
admin_site = NeoCamelotAdminSite(name='neocamelot_admin')

# Register models with the custom admin site
User = get_user_model()
admin_site.register(User, UserAdmin)
admin_site.register(Group, GroupAdmin)
admin_site.register(Client, ClientAdmin)
admin_site.register(Domain, DomainAdmin)

# Import common models and admin classes
from common.models import Country, Region, Religion, CitizenStatus, MaritalStatus, DocumentType, EmploymentType, EmployeeType, RelationshipType
from common.admin import CountryAdmin, RegionAdmin, ReligionAdmin, CitizenStatusAdmin, MaritalStatusAdmin, DocumentTypeAdmin, EmploymentTypeAdmin, EmployeeTypeAdmin, RelationshipTypeAdmin

# Register only the center-specific models in the admin
admin_site.register(City, CityAdmin)
admin_site.register(Subcity, SubcityAdmin)
admin_site.register(Kebele, CenterAdmin)
admin_site.register(CenterType, CenterTypeAdmin)
admin_site.register(Ketena, KetenaAdmin)

# Register citizen-related models
admin_site.register(CurrentStatus, CurrentStatusAdmin)

# Register common models
admin_site.register(Country, CountryAdmin)
admin_site.register(Region, RegionAdmin)
admin_site.register(Religion, ReligionAdmin)
admin_site.register(CitizenStatus, CitizenStatusAdmin)
admin_site.register(MaritalStatus, MaritalStatusAdmin)
admin_site.register(DocumentType, DocumentTypeAdmin)
admin_site.register(EmploymentType, EmploymentTypeAdmin)
admin_site.register(EmployeeType, EmployeeTypeAdmin)
admin_site.register(RelationshipType, RelationshipTypeAdmin)

# Register the global admin classes
admin_site.register(Citizen, GlobalCitizenAdmin)
admin_site.register(IDCardTemplate, GlobalIDCardTemplateAdmin)
admin_site.register(IDCard, GlobalIDCardAdmin)

# Register family-related models
admin_site.register(Document, DocumentAdmin)
admin_site.register(Child, ChildAdmin)
admin_site.register(Parent, ParentAdmin)
# RelationshipType is already registered above
admin_site.register(EmergencyContact, EmergencyContactAdmin)
admin_site.register(Spouse, SpouseAdmin)

# Register biometric-related models
admin_site.register(Biometric, BiometricAdmin)
admin_site.register(Photo, PhotoAdmin)
