import os
import django

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'neocamelot.settings')
django.setup()

# Import models
from centers.models import Client
from django.db import connection
from django_tenants.utils import tenant_context

def check_tenant_schema(schema_name):
    """Check the tables in a specific tenant schema."""
    try:
        # Get the tenant
        tenant = Client.objects.get(schema_name=schema_name)
        print(f"\n===== CHECKING SCHEMA: {schema_name} =====\n")

        # Set the connection to the tenant's schema
        connection.set_schema(tenant.schema_name)

        # Create a cursor
        cursor = connection.cursor()

        # Get all tables in the schema
        cursor.execute("""
            SELECT table_name
            FROM information_schema.tables
            WHERE table_schema = %s
            ORDER BY table_name
        """, [connection.schema_name])

        tables = cursor.fetchall()

        print(f"Found {len(tables)} tables in schema {schema_name}:")
        for table in tables:
            print(f"- {table[0]}")

            # Get columns for each table
            cursor.execute("""
                SELECT column_name, data_type
                FROM information_schema.columns
                WHERE table_schema = %s AND table_name = %s
                ORDER BY ordinal_position
            """, [connection.schema_name, table[0]])

            columns = cursor.fetchall()
            for column in columns:
                print(f"  - {column[0]} ({column[1]})")

        # Check specifically for auth or accounts tables
        auth_tables = [t[0] for t in tables if t[0].startswith('auth_') or t[0].startswith('accounts_')]
        if auth_tables:
            print(f"\nFound {len(auth_tables)} auth/accounts tables:")
            for table in auth_tables:
                print(f"- {table}")
        else:
            print("\nWARNING: No auth or accounts tables found in this schema!")

        # Check if migrations have been applied
        migration_tables = [t[0] for t in tables if t[0] == 'django_migrations']
        if migration_tables:
            cursor.execute("""
                SELECT app, name
                FROM django_migrations
                WHERE app IN ('auth', 'accounts')
                ORDER BY app, name
            """)

            migrations = cursor.fetchall()

            print(f"\nFound {len(migrations)} auth/accounts migrations:")
            for migration in migrations:
                print(f"- {migration[0]}.{migration[1]}")
        else:
            print("\nWARNING: No django_migrations table found in this schema!")

        print("\n===== SCHEMA CHECK COMPLETE =====\n")

    except Client.DoesNotExist:
        print(f"Tenant with schema {schema_name} does not exist")
    except Exception as e:
        print(f"Error checking schema {schema_name}: {str(e)}")

if __name__ == '__main__':
    # Get schema name from command line
    import sys
    if len(sys.argv) > 1:
        schema_name = sys.argv[1]
        check_tenant_schema(schema_name)
    else:
        print("Please provide a schema name as a command line argument")
        sys.exit(1)
