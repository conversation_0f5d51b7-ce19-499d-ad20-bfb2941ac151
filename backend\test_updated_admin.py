import os
import django

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'neocamelot.settings')
django.setup()

# Import models and admin
from django.contrib import admin
from citizens.models import Citizen
from idcards.models import IDCard, IDCardTemplate
from centers.models import Client
from django_tenants.utils import tenant_context, schema_context

# Check if the models are registered in the admin site
registered_models = [model.__name__ for model, _ in admin.site._registry.items()]
print(f"Currently registered models: {registered_models}")

# Test the admin classes
from citizens.admin import GlobalCitizenAdmin
from idcards.admin import GlobalIDCardTemplateAdmin, GlobalIDCardAdmin

print("\nTesting GlobalCitizenAdmin...")
citizen_admin = GlobalCitizenAdmin(Citizen, admin.site)
citizens = citizen_admin.get_queryset(None)
print(f"Found {len(citizens)} citizens across all tenants")

print("\nTesting GlobalIDCardTemplateAdmin...")
template_admin = GlobalIDCardTemplateAdmin(IDCardTemplate, admin.site)
templates = template_admin.get_queryset(None)
print(f"Found {len(templates)} ID card templates across all tenants")

print("\nTesting GlobalIDCardAdmin...")
card_admin = GlobalIDCardAdmin(IDCard, admin.site)
cards = card_admin.get_queryset(None)
print(f"Found {len(cards)} ID cards across all tenants")

print("\nDone!")
