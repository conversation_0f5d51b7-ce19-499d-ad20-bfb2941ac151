import React, { useEffect, useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { CircularProgress, Box, Typography, Alert } from '@mui/material';
import { redirectToLogin, handleAuthError } from '../utils/authRedirect';
import { useAuth } from '../contexts/AuthContext';
import { validateJWTToken, refreshJWTTokens, getCurrentSchema } from '../services/tokenService';

interface TokenValidatorProps {
  children: React.ReactNode;
}

/**
 * TokenValidator component
 * Validates the current token and refreshes it if needed
 * Redirects to login if token is invalid
 */
const TokenValidator: React.FC<TokenValidatorProps> = ({ children }) => {
  const navigate = useNavigate();
  const [isValidating, setIsValidating] = useState(true);
  const [validationError, setValidationError] = useState<string | null>(null);
  const auth = useAuth(); // Use the auth context

  const location = useLocation();

  useEffect(() => {
    const validateCurrentToken = async () => {
      try {
        setIsValidating(true);
        setValidationError(null);

        // Get the current schema from unified token service
        const schemaName = getCurrentSchema();

        if (!schemaName) {
          console.error('No schema name found in any storage location');

          // Try to get from tenant object in sessionStorage
          try {
            const tenantStr = sessionStorage.getItem('tenant');
            if (tenantStr) {
              const tenant = JSON.parse(tenantStr);
              if (tenant && tenant.schema_name) {
                const schemaFromTenant = tenant.schema_name;
                console.log('Using schema from tenant object:', schemaFromTenant);

                // Store it for future use
                sessionStorage.setItem('schema_name', schemaFromTenant);
                sessionStorage.setItem('jwt_schema', schemaFromTenant);
                sessionStorage.setItem('currentSchema', schemaFromTenant);

                // Continue with validation using this schema
                console.log(`Validating token for schema from tenant: ${schemaFromTenant}`);

                // Try to validate the JWT token
                const isJwtValid = await validateJWTToken(schemaFromTenant);

                if (isJwtValid) {
                  console.log('JWT token is valid with schema from tenant');
                  setIsValidating(false);
                  return;
                }
              }
            }
          } catch (e) {
            console.error('Error parsing tenant from sessionStorage:', e);
          }

          // Use the redirectToLogin utility instead of navigate
          redirectToLogin('no_token', undefined);
          return;
        }

        console.log(`Validating token for schema ${schemaName}`);

        // First try JWT validation
        try {
          // Validate JWT token
          const isJwtValid = await validateJWTToken(schemaName);

          if (isJwtValid) {
            console.log('JWT token is valid');
            setIsValidating(false);
            return;
          } else {
            console.log('JWT token is invalid, trying to refresh');

            // Try to refresh JWT token
            try {
              const refreshResult = await auth.refreshJWTToken();

              if (refreshResult) {
                console.log('JWT token refreshed successfully');
                setIsValidating(false);
                return;
              } else {
                console.log('JWT token refresh failed, falling back to legacy token');
              }
            } catch (jwtRefreshError) {
              console.error('Error refreshing JWT token:', jwtRefreshError);
              console.log('Falling back to legacy token validation');
            }
          }
        } catch (jwtError) {
          console.error('Error validating JWT token:', jwtError);
          console.log('Falling back to legacy token validation');
        }

        // No fallback to legacy token validation - we're using only JWT now
        console.error('JWT token validation failed and refresh failed');
        redirectToLogin('invalid_token', schemaName);
        return;
      } catch (error) {
        console.error('Error validating token:', error);
        setValidationError('Error validating authentication. Please try again.');
        setIsValidating(false);
      }
    };

    validateCurrentToken();
  }, [navigate, location.pathname]);

  if (isValidating) {
    return (
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          height: '100vh',
        }}
      >
        <CircularProgress size={60} />
        <Typography variant="h6" sx={{ mt: 2 }}>
          Validating authentication...
        </Typography>
      </Box>
    );
  }

  if (validationError) {
    return (
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          height: '100vh',
          p: 3,
        }}
      >
        <Alert severity="error" sx={{ mb: 2 }}>
          {validationError}
        </Alert>
        <Typography variant="body1">
          Please try refreshing the page or{' '}
          <Typography
            component="span"
            sx={{
              color: 'primary.main',
              cursor: 'pointer',
              textDecoration: 'underline',
            }}
            onClick={() => {
              // Get the current schema
              const schemaName = localStorage.getItem('schema_name');

              // Use the redirectToLogin utility
              redirectToLogin('validation_error', schemaName || undefined);
            }}
          >
            log in again
          </Typography>
          .
        </Typography>
      </Box>
    );
  }

  return <>{children}</>;
};

export default TokenValidator;
