/**
 * API Configuration
 * 
 * This file contains the configuration for the API.
 * It defines the base URL for the API and other API-related settings.
 */

// Backend API URL - use direct connection to backend server
export const API_BASE_URL = 'http://localhost:8000';

// API endpoints
export const API_ENDPOINTS = {
  // Auth endpoints
  LOGIN: '/api/jwt/login/',
  REFRESH_TOKEN: '/api/jwt/refresh-token/',
  VALIDATE_TOKEN: '/api/jwt/validate-token/',
  
  // Tenant endpoints
  AVAILABLE_TENANTS: '/api/available-tenants/',
  TENANT_INFO: '/api/tenant-info/',
  
  // Health check endpoint
  HEALTH: '/api/health/',
  DEBUG_CORS: '/api/debug-cors/',
};

/**
 * Create a full API URL
 * @param endpoint The API endpoint
 * @returns The full API URL
 */
export const createApiUrl = (endpoint: string): string => {
  // If the endpoint already starts with http, return it as is
  if (endpoint.startsWith('http')) {
    return endpoint;
  }
  
  // If the endpoint already starts with /api, append it to the base URL
  if (endpoint.startsWith('/api')) {
    return `${API_BASE_URL}${endpoint}`;
  }
  
  // Otherwise, append /api/ and the endpoint to the base URL
  return `${API_BASE_URL}/api/${endpoint}`;
};

/**
 * Create a tenant-specific API URL
 * @param schema The tenant schema
 * @param endpoint The API endpoint
 * @returns The full API URL
 */
export const createTenantApiUrl = (schema: string, endpoint: string): string => {
  // Normalize the endpoint (remove leading /api/ if present)
  const normalizedEndpoint = endpoint.replace(/^\/api\//, '');
  
  // Create the tenant-specific URL
  return `${API_BASE_URL}/api/tenant/${schema}/${normalizedEndpoint}`;
};

export default {
  API_BASE_URL,
  API_ENDPOINTS,
  createApiUrl,
  createTenantApiUrl,
};
