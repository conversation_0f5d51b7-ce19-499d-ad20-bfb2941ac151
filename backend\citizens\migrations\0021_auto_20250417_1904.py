# Generated by Django 5.1.7 on 2025-04-17 16:04

from django.db import migrations
import uuid


def fix_duplicate_uuids(apps, schema_editor):
    Citizen = apps.get_model('citizens', 'Citizen')
    # Get all citizens
    citizens = Citizen.objects.all()
    # Create a set to track UUIDs we've seen
    seen_uuids = set()
    # Iterate through citizens
    for citizen in citizens:
        # If the UUID is None or already seen, generate a new one
        if citizen.uuid is None or str(citizen.uuid) in seen_uuids:
            citizen.uuid = uuid.uuid4()
            citizen.save(update_fields=['uuid'])
        # Add the UUID to the set of seen UUIDs
        seen_uuids.add(str(citizen.uuid))


class Migration(migrations.Migration):

    dependencies = [
        ('citizens', '0020_auto_20250417_1903'),
    ]

    operations = [
        migrations.RunPython(fix_duplicate_uuids, migrations.RunPython.noop),
    ]
