import React, { createContext, useContext, useState, useEffect, useCallback, ReactNode } from 'react';
import {
  loginWithJWT as apiLoginWithJWT,
  refreshJWTTokens,
  validateJWTToken,
  clearAllTokens,
  getCurrentSchema,
  getAuthHeaders as getTokenAuthHeaders
} from '../services/tokenService';
import { initializeTokenRefresh } from '../utils/tokenRefresh';

// Define types for better type safety
interface User {
  id: number;
  username: string;
  email: string;
  role: string;
  first_name?: string;
  last_name?: string;
  [key: string]: any; // Allow for additional properties
}

interface Tenant {
  id: number;
  name: string;
  schema_name: string;
  schema_type?: string;
  type?: string;
  [key: string]: any; // Allow for additional properties
}

interface AuthContextType {
  token: string;
  user: User | null;
  schema: string;
  tenant: Tenant | null;
  setToken: React.Dispatch<React.SetStateAction<string>>;
  setUser: React.Dispatch<React.SetStateAction<User | null>>;
  setSchema: React.Dispatch<React.SetStateAction<string>>;
  isAuthenticated: boolean;
  isValidating: boolean;
  validateToken: () => Promise<boolean>;
  logout: () => void;
  loginWithJWT: (email: string, password: string, schema?: string) => Promise<boolean>;
  refreshJWTToken: () => Promise<boolean>;
  getAuthHeaders: () => HeadersInit;
}

// Storage keys for better maintainability
const STORAGE_KEYS = {
  ACCESS_TOKEN: 'jwt_access_token',
  REFRESH_TOKEN: 'jwt_refresh_token',
  JWT_SCHEMA: 'jwt_schema',
  SCHEMA_NAME: 'schema_name',
  USER: 'user',
  TENANT: 'tenant',
  USER_ROLE: 'user_role',
  TENANT_TYPE: 'tenant_type',
  LAST_VALIDATION: 'last_auth_validation_time',
  LAST_REFRESH: 'last_jwt_refresh_time'
};

// Create context with undefined initial value
const AuthContext = createContext<AuthContextType | undefined>(undefined);

/**
 * Helper function to get item from storage with proper error handling
 */
const getFromStorage = <T,>(key: string, defaultValue: T): T => {
  try {
    const item = localStorage.getItem(key) || sessionStorage.getItem(key);
    if (!item) return defaultValue;
    return JSON.parse(item) as T;
  } catch (error) {
    console.error(`Error getting ${key} from storage:`, error);
    return defaultValue;
  }
};

/**
 * Helper function to save item to storage with proper error handling
 */
const saveToStorage = (key: string, value: any): void => {
  try {
    if (value === null || value === undefined) {
      localStorage.removeItem(key);
      sessionStorage.removeItem(key);
    } else {
      const valueToStore = typeof value === 'string' ? value : JSON.stringify(value);
      localStorage.setItem(key, valueToStore);
    }
  } catch (error) {
    console.error(`Error saving ${key} to storage:`, error);
  }
};

/**
 * Helper function to clear a cookie
 */
const clearCookie = (name: string): void => {
  document.cookie = `${name}=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT`;
};

/**
 * Helper function to set a cookie
 */
const setCookie = (name: string, value: string): void => {
  document.cookie = `${name}=${encodeURIComponent(value)}; path=/; SameSite=Lax`;
};

/**
 * AuthProvider component that provides authentication context
 */
export const AuthProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  // State
  const [token, setToken] = useState<string>(localStorage.getItem(STORAGE_KEYS.ACCESS_TOKEN) || '');
  const [user, setUser] = useState<User | null>(getFromStorage<User | null>(STORAGE_KEYS.USER, null));
  const [schema, setSchema] = useState<string>(
    localStorage.getItem(STORAGE_KEYS.JWT_SCHEMA) || 
    localStorage.getItem(STORAGE_KEYS.SCHEMA_NAME) || 
    ''
  );
  const [tenant, setTenant] = useState<Tenant | null>(getFromStorage<Tenant | null>(STORAGE_KEYS.TENANT, null));
  const [isValidating, setIsValidating] = useState<boolean>(false);

  /**
   * Logout function - clears all auth data
   */
  const logout = useCallback(() => {
    // Clear state
    setToken('');
    setUser(null);
    setSchema('');
    setTenant(null);

    // Clear JWT tokens
    clearAllTokens();

    // Clear localStorage
    Object.values(STORAGE_KEYS).forEach(key => localStorage.removeItem(key));
    
    // Clear sessionStorage
    sessionStorage.clear();

    // Clear cookies
    clearCookie(STORAGE_KEYS.SCHEMA_NAME);
    clearCookie(STORAGE_KEYS.ACCESS_TOKEN);
    clearCookie(STORAGE_KEYS.REFRESH_TOKEN);

    // Clear token refresh timers
    try {
      import('../utils/jwtTokenRefresh').then(module => {
        module.clearAllTokenRefreshes();
        console.log('Cleared JWT token refresh timers');
      }).catch(error => {
        console.error('Error clearing JWT token refresh timers:', error);
      });
    } catch (error) {
      console.error('Error clearing token refresh timers:', error);
    }

    console.log('Logged out, cleared all tokens, localStorage and cookies');

    // Redirect to login page
    window.location.href = '/login';
  }, []);

  /**
   * Function to validate the current token
   */
  const validateToken = async (): Promise<boolean> => {
    // Prevent excessive validations
    const lastValidationTime = localStorage.getItem(STORAGE_KEYS.LAST_VALIDATION);
    const currentTime = Date.now();

    // If we've validated in the last 5 seconds, don't validate again
    if (lastValidationTime && (currentTime - parseInt(lastValidationTime)) < 5000) {
      console.log('Skipping token validation - recently validated');
      return true;
    }

    setIsValidating(true);
    try {
      // Record validation time
      localStorage.setItem(STORAGE_KEYS.LAST_VALIDATION, currentTime.toString());

      // Get the current schema
      const currentSchema = schema || getCurrentSchema() || '';

      if (!currentSchema) {
        console.warn('No schema name found for token validation');
        return false;
      }

      // Try to validate the JWT token
      try {
        const isValid = await validateJWTToken(currentSchema);
        if (isValid) {
          console.log('JWT token is valid');
          return true;
        }
      } catch (validationError) {
        console.error('Error validating JWT token:', validationError);
      }

      // If validation fails, try to refresh the token
      console.log('JWT token is invalid, trying to refresh');
      return await refreshJWTToken();
    } catch (error) {
      console.error('Error in token validation process:', error);
      return false;
    } finally {
      setIsValidating(false);
    }
  };

  /**
   * JWT token refresh function
   */
  const refreshJWTToken = async (): Promise<boolean> => {
    try {
      console.log('Refreshing JWT token');

      // Prevent multiple refreshes in a short period
      const lastRefreshTime = localStorage.getItem(STORAGE_KEYS.LAST_REFRESH);
      const currentTime = Date.now();

      // If we've refreshed in the last 5 seconds, don't refresh again
      if (lastRefreshTime && (currentTime - parseInt(lastRefreshTime)) < 5000) {
        console.log('Skipping token refresh - recently refreshed');
        return true;
      }

      // Record refresh time
      localStorage.setItem(STORAGE_KEYS.LAST_REFRESH, currentTime.toString());

      // Get the current schema
      const currentSchema = schema || getCurrentSchema() || '';

      if (!currentSchema) {
        console.error('No schema available for token refresh');
        return false;
      }

      try {
        // Call the JWT token refresh function
        const response = await refreshJWTTokens(currentSchema);

        if (response && response.access_token) {
          console.log('JWT token refresh successful');
          setToken(response.access_token);
          return true;
        }
      } catch (error) {
        const refreshError = error as Error;
        console.error('Error in refreshJWTTokens:', refreshError);

        // Try with schema from tenant if there's a schema-related error
        if (refreshError.message?.includes('schema')) {
          try {
            const tenantData = getFromStorage<Tenant | null>(STORAGE_KEYS.TENANT, null);
            if (tenantData?.schema_name && tenantData.schema_name !== currentSchema) {
              console.log(`Trying refresh with schema from tenant: ${tenantData.schema_name}`);
              const altResponse = await refreshJWTTokens(tenantData.schema_name);
              
              if (altResponse?.access_token) {
                console.log('JWT token refresh successful with tenant schema');
                setSchema(tenantData.schema_name);
                saveToStorage(STORAGE_KEYS.JWT_SCHEMA, tenantData.schema_name);
                saveToStorage(STORAGE_KEYS.SCHEMA_NAME, tenantData.schema_name);
                setToken(altResponse.access_token);
                return true;
              }
            }
          } catch (tenantError) {
            console.error('Error parsing tenant for schema fallback:', tenantError);
          }
        }
      }

      console.warn('JWT token refresh failed');
      return false;
    } catch (error) {
      console.error('JWT token refresh error:', error);
      return false;
    }
  };

  /**
   * JWT login function
   */
  const loginWithJWTFunction = async (email: string, password: string, schemaName?: string): Promise<boolean> => {
    try {
      console.log(`Logging in with JWT for email: ${email}`);

      // Use the schema from the parameter or the current schema
      const currentSchema = schemaName || getCurrentSchema() || '';

      // Call the JWT login function
      const response = await apiLoginWithJWT(email, password, currentSchema);

      if (response?.access_token) {
        console.log('JWT login successful');

        // Update state with user data
        if (response.user) {
          // Ensure the user has a role property
          if (!response.user.role) {
            const storedRole = localStorage.getItem(STORAGE_KEYS.USER_ROLE);
            response.user.role = storedRole || 'CLERK'; // Default role as last resort
          }
          setUser(response.user);
        }

        // Update tenant and schema
        if (response.tenant) {
          setTenant(response.tenant);
          setSchema(response.tenant.schema_name);
        } else if (currentSchema) {
          setSchema(currentSchema);
        }

        // Set the token
        setToken(response.access_token);
        return true;
      }

      return false;
    } catch (error) {
      console.error('JWT login error:', error);
      return false;
    }
  };

  /**
   * Get authentication headers
   */
  const getAuthHeaders = (): HeadersInit => {
    const currentSchema = schema || getCurrentSchema() || '';
    return getTokenAuthHeaders(currentSchema);
  };

  // Save token to localStorage whenever it changes
  useEffect(() => {
    if (token) {
      saveToStorage(STORAGE_KEYS.ACCESS_TOKEN, token);
    } else {
      localStorage.removeItem(STORAGE_KEYS.ACCESS_TOKEN);
    }
  }, [token]);

  // Save user to localStorage whenever it changes
  useEffect(() => {
    if (user) {
      saveToStorage(STORAGE_KEYS.USER, user);
      if (user.role) {
        saveToStorage(STORAGE_KEYS.USER_ROLE, user.role);
      }
    } else {
      localStorage.removeItem(STORAGE_KEYS.USER);
    }
  }, [user]);

  // Save schema to localStorage and cookie whenever it changes
  useEffect(() => {
    if (schema) {
      saveToStorage(STORAGE_KEYS.JWT_SCHEMA, schema);
      saveToStorage(STORAGE_KEYS.SCHEMA_NAME, schema);
      setCookie(STORAGE_KEYS.SCHEMA_NAME, schema);
    } else {
      localStorage.removeItem(STORAGE_KEYS.JWT_SCHEMA);
      localStorage.removeItem(STORAGE_KEYS.SCHEMA_NAME);
      clearCookie(STORAGE_KEYS.SCHEMA_NAME);
    }
  }, [schema]);

  // Save tenant to localStorage whenever it changes
  useEffect(() => {
    if (tenant) {
      saveToStorage(STORAGE_KEYS.TENANT, tenant);
    } else {
      localStorage.removeItem(STORAGE_KEYS.TENANT);
    }
  }, [tenant]);

  // Initialize token refresh on mount
  useEffect(() => {
    if (token) {
      console.log('Initializing token refresh mechanism on mount');
      initializeTokenRefresh();
    }
  }, [token]);

  return (
    <AuthContext.Provider
      value={{
        token,
        user,
        schema,
        tenant,
        setToken,
        setUser,
        setSchema,
        isAuthenticated: !!token,
        isValidating,
        validateToken,
        logout,
        loginWithJWT: loginWithJWTFunction,
        refreshJWTToken,
        getAuthHeaders
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};

/**
 * Hook to use the auth context
 */
export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
