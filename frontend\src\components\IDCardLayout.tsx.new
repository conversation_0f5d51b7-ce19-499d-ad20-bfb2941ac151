import React from 'react';
import {
  Box,
  Typography,
  Avatar,
} from '@mui/material';
import CalendarTodayIcon from '@mui/icons-material/CalendarToday';
import SecurityIcon from '@mui/icons-material/Security';
import QRCode from 'react-qr-code';
import PhoneIcon from '@mui/icons-material/Phone';
import EmailIcon from '@mui/icons-material/Email';
import LanguageIcon from '@mui/icons-material/Language';
import QrCodeIcon from '@mui/icons-material/QrCode';

// Common styles for consistent dimensions
const styles = {
  // Main card container
  card: {
    border: '1px solid rgba(0, 0, 0, 0.1)',
    borderRadius: 2,
    p: 0,
    mb: 3,
    position: 'relative',
    width: '100%',
    maxWidth: '500px',
    height: '280px', // Fixed height for both sides
    background: 'linear-gradient(to right, rgba(245,245,255,1) 0%, rgba(235,240,255,1) 50%, rgba(245,245,255,1) 100%)',
    overflow: 'hidden',
    boxShadow: '0 4px 12px rgba(0,0,0,0.1)',
    display: 'flex',
    flexDirection: 'column',
    '&::after': {
      content: '""',
      position: 'absolute',
      top: 0,
      left: 0,
      width: '100%',
      height: '100%',
      backgroundImage: 'repeating-linear-gradient(45deg, rgba(0,102,178,0.02) 0px, rgba(0,102,178,0.02) 10px, transparent 10px, transparent 20px)',
      backgroundRepeat: 'repeat',
      backgroundPosition: 'center',
      pointerEvents: 'none',
      zIndex: 1
    }
  },
  // Header styles
  header: {
    bgcolor: '#0066b2',
    color: 'white',
    py: 0.5,
    px: 1,
    display: 'flex',
    borderBottom: '1px solid #004d99',
    background: 'linear-gradient(135deg, #0066b2 0%, #0077cc 100%)',
    height: '45px' // Fixed height for both headers
  },
  // ID number bar
  idBar: {
    background: 'linear-gradient(to right, #cc0000, #e60000)',
    color: 'white',
    py: 0.3,
    px: 1.5,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    boxShadow: '0 1px 3px rgba(0,0,0,0.1)',
    height: '25px' // Fixed height
  },
  // Main content area
  content: {
    display: 'flex',
    p: 1.5,
    bgcolor: 'white',
    flexGrow: 1,
    position: 'relative',
    zIndex: 2,
    height: '185px' // Fixed height for content area
  },
  // Footer styles
  footer: {
    borderTop: '1px solid #ccc',
    p: 0.75,
    bgcolor: 'rgba(0,102,178,0.05)',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
    zIndex: 2,
    height: '25px' // Fixed height for both footers
  }
};

interface IDCardLayoutProps {
  idCard: any;
  showFront: boolean;
  formatDate: (date: string) => string;
}

const IDCardLayout: React.FC<IDCardLayoutProps> = ({ idCard, showFront, formatDate }) => {
  return (
    <>
      {showFront ? (
        /* Front Side of ID Card */
        <Box sx={styles.card}>
          {/* Card Header - Blue Bar */}
          <Box sx={styles.header}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', width: '100%' }}>
              <Box
                component="img"
                src="/src/assets/flag-icon.jpg"
                alt="Flag"
                sx={{ width: 20, height: 15 }}
              />
              <Box sx={{ textAlign: 'center', flex: 1, mx: 0.5 }}>
                <Typography variant="caption" sx={{ fontWeight: 500, fontSize: '0.55rem', lineHeight: 1.1, display: 'block' }}>
                  በኢትዮጵያ ፌደራላዊ ዲሞክራሲያዊ ሪፐብሊክ በአማራ ብሔራዊ ክልላዊ መንግሥት
                </Typography>
                <Typography variant="caption" sx={{ fontWeight: 500, fontSize: '0.55rem', lineHeight: 1.1, display: 'block' }}>
                  በጎንደር ከተማ አስተዳደር በዞብል ክ/ከተማ የገብርኤል ቀበሌ አስተዳደር ጽ/ቤት
                </Typography>
                <Typography variant="subtitle2" sx={{
                  fontWeight: 700,
                  fontSize: '0.75rem',
                  textAlign: 'center',
                  color: '#fff',
                  textShadow: '0 1px 1px rgba(0,0,0,0.3)',
                  mt: 0.25,
                  px: 0.5
                }}>
                  የነዋሪዎች መታወቂያ ካርድ
                </Typography>
              </Box>
              <Box
                component="img"
                src="/src/assets/flag-icon.jpg"
                alt="Gondar City Logo"
                sx={{ width: 35, height: 20, objectFit: 'contain' }}
              />
            </Box>
          </Box>

          {/* ID Number - Red Bar */}
          <Box sx={styles.idBar}>
            <Typography variant="caption" sx={{ fontWeight: 700, fontSize: '0.7rem', display: 'flex', alignItems: 'center' }}>
              <SecurityIcon sx={{ fontSize: 14, mr: 0.5 }} />
              ID: {idCard.citizen_id_number || 'NOT ASSIGNED'}
            </Typography>
            <Typography variant="caption" sx={{ fontSize: '0.65rem', opacity: 0.9 }}>
              {formatDate(new Date().toISOString().split('T')[0])}
            </Typography>
          </Box>

          {/* Main Content */}
          <Box sx={styles.content}>
            {/* Left Side - Photo */}
            <Box sx={{ mr: 2 }}>
              <Box sx={{
                width: 80,
                height: 90,
                border: '1px solid #ccc',
                overflow: 'hidden',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                bgcolor: '#f5f5f5',
                boxShadow: '0 2px 6px rgba(0,0,0,0.1)',
                position: 'relative'
              }}>
                <Avatar
                  src={idCard.citizen_photo}
                  alt={idCard.citizen_name}
                  variant="square"
                  sx={{
                    width: 80,
                    height: 90,
                    objectFit: 'cover'
                  }}
                >
                  {idCard.citizen_name?.[0]}
                </Avatar>
                <Box sx={{
                  position: 'absolute',
                  bottom: 0,
                  right: 0,
                  bgcolor: 'rgba(255,255,255,0.7)',
                  p: 0.25,
                  borderTopLeftRadius: 4
                }}>
                  <SecurityIcon sx={{ fontSize: 14, color: '#0066b2' }} />
                </Box>
              </Box>
            </Box>

            {/* Right Side - Information */}
            <Box sx={{ flexGrow: 1, display: 'flex', flexDirection: 'column', pl: 1 }}>
              {/* Name Field */}
              <Box sx={{ mb: 1.5 }}>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <Typography variant="caption" color="text.secondary" sx={{ fontWeight: 600, fontSize: '0.7rem', width: '60px' }}>
                    ሙሉ ስም:
                  </Typography>
                  <Typography variant="body2" sx={{ fontWeight: 600, color: '#000066', ml: 1 }}>
                    {idCard.citizen_name}
                  </Typography>
                </Box>
              </Box>

              {/* Gender Row */}
              <Box sx={{ display: 'flex', mb: 1.5 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', width: '45%' }}>
                  <Typography variant="caption" color="text.secondary" sx={{ fontWeight: 600, fontSize: '0.7rem', width: '40px' }}>
                    ፆታ:
                  </Typography>
                  <Typography variant="body2" sx={{ fontWeight: 600, color: '#000066', ml: 1 }}>
                    ወ
                  </Typography>
                </Box>
                <Box sx={{ display: 'flex', alignItems: 'center', width: '55%' }}>
                  <Typography variant="caption" color="text.secondary" sx={{ fontWeight: 600, fontSize: '0.7rem', width: '60px' }}>
                    ዜግነት:
                  </Typography>
                  <Typography variant="body2" sx={{ fontWeight: 600, color: '#000066', ml: 1 }}>
                    ኢትዮጵያዊ
                  </Typography>
                </Box>
              </Box>

              {/* Address Row */}
              <Box sx={{ display: 'flex', mb: 1.5 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', width: '100%' }}>
                  <Typography variant="caption" color="text.secondary" sx={{ fontWeight: 600, fontSize: '0.7rem', width: '60px' }}>
                    አድራሻ:
                  </Typography>
                  <Typography variant="body2" sx={{ fontWeight: 600, color: '#000066', ml: 1 }}>
                    {idCard.center_name}
                  </Typography>
                </Box>
              </Box>

              {/* Birth Date Row */}
              <Box sx={{ display: 'flex', mb: 1.5 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', width: '100%' }}>
                  <Typography variant="caption" color="text.secondary" sx={{ fontWeight: 600, fontSize: '0.7rem', width: '80px', display: 'flex', alignItems: 'center' }}>
                    <CalendarTodayIcon sx={{ fontSize: 12, mr: 0.5, color: '#0066b2' }} />
                    የትውልድ ቀን:
                  </Typography>
                  <Typography variant="body2" sx={{ fontWeight: 600, color: '#000066', ml: 1 }}>
                    {formatDate(new Date().toISOString().split('T')[0])}
                  </Typography>
                </Box>
              </Box>

              {/* Issue Date */}
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <Typography variant="caption" color="text.secondary" sx={{ fontWeight: 600, fontSize: '0.7rem', width: '80px', display: 'flex', alignItems: 'center' }}>
                  <CalendarTodayIcon sx={{ fontSize: 12, mr: 0.5, color: '#0066b2' }} />
                  የተሰጠበት ቀን:
                </Typography>
                <Typography variant="body2" sx={{ fontWeight: 600, color: '#000066', ml: 1, fontSize: '0.75rem' }}>
                  {formatDate(idCard.issue_date)}
                </Typography>
              </Box>

              {/* Expiry Date */}
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <Typography variant="caption" color="text.secondary" sx={{ fontWeight: 600, fontSize: '0.7rem', width: '80px', display: 'flex', alignItems: 'center' }}>
                  <CalendarTodayIcon sx={{ fontSize: 12, mr: 0.5, color: '#0066b2' }} />
                  የሚያበቃበት ቀን:
                </Typography>
                <Typography variant="body2" sx={{ fontWeight: 600, color: '#000066', ml: 1, fontSize: '0.75rem' }}>
                  {formatDate(idCard.expiry_date)}
                </Typography>
              </Box>
            </Box>
          </Box>

          {/* Footer */}
          <Box sx={styles.footer}>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <SecurityIcon sx={{ color: '#0066b2', fontSize: 16, mr: 0.5 }} />
              <Typography variant="caption" sx={{ fontWeight: 600, color: '#0066b2', fontSize: '0.7rem' }}>
                በጎንደር ከተማ አስተዳደር የተሰጠ ህጋዊ መታወቂያ
              </Typography>
            </Box>
          </Box>
        </Box>
      ) : (
        /* Back Side of ID Card */
        <Box sx={styles.card}>
          {/* Back Side Header */}
          <Box sx={{...styles.header, justifyContent: 'center', alignItems: 'center'}}>
            <Typography variant="subtitle2" sx={{ fontWeight: 700, fontSize: '0.8rem', textAlign: 'center' }}>
              መታወቂያ ካርድ - ጀርባ
            </Typography>
          </Box>

          {/* Main Content */}
          <Box sx={{...styles.content, height: '210px'}}> {/* Adjusted to account for no ID bar */}
            {/* QR Code Section */}
            <Box sx={{ width: '40%', display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
              <Box sx={{
                width: 120,
                height: 120,
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center',
                bgcolor: 'white',
                border: '1px solid rgba(0,0,0,0.1)',
                borderRadius: 2,
                p: 1,
                boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
              }}>
                {idCard.citizen_id_number ? (
                  <>
                    <QRCode
                      value={`https://id.gondar.gov.et/verify/${idCard.citizen_id_number}`}
                      size={100}
                      style={{ height: 'auto', maxWidth: '100%', width: '100%' }}
                      viewBox={`0 0 256 256`}
                      level="M"
                      fgColor="#000066"
                    />
                    <Typography variant="caption" sx={{ mt: 1, fontWeight: 600, color: '#0066b2', textAlign: 'center' }}>
                      ለማረጋገጥ ይስካን ያድርጉ
                    </Typography>
                  </>
                ) : (
                  <>
                    <Box sx={{ width: 100, height: 100, bgcolor: '#f5f5f5', display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center' }}>
                      <QrCodeIcon sx={{ fontSize: 60, color: '#666', mb: 1 }} />
                      <Typography variant="caption" sx={{ fontWeight: 600, color: '#666' }}>
                        QR Code
                      </Typography>
                    </Box>
                    <Typography variant="caption" sx={{ mt: 1, fontWeight: 600, color: '#666' }}>
                      Not Available
                    </Typography>
                  </>
                )}
              </Box>
            </Box>

            {/* Information Section */}
            <Box sx={{ width: '60%', pl: 2, display: 'flex', flexDirection: 'column', justifyContent: 'space-between' }}>
              {/* Important Information */}
              <Box>
                <Typography variant="subtitle2" sx={{ fontWeight: 700, color: '#0066b2', mb: 1, fontSize: '0.8rem' }}>
                  አስፈላጊ መረጃ
                </Typography>
                <Box sx={{ bgcolor: 'rgba(0,102,178,0.05)', p: 1, borderRadius: 1, mb: 1 }}>
                  <Typography variant="caption" sx={{ display: 'block', fontSize: '0.7rem', mb: 0.5 }}>
                    1. ይህ መታወቂያ ካርድ የመንግስት ንብረት ነው።
                  </Typography>
                  <Typography variant="caption" sx={{ display: 'block', fontSize: '0.7rem', mb: 0.5 }}>
                    2. ካርዱን ማበላሸት፣ ማጭበርበር ወይም ማሻሻል ህገወጥ ነው።
                  </Typography>
                  <Typography variant="caption" sx={{ display: 'block', fontSize: '0.7rem' }}>
                    3. ካርዱ ከጠፋ ወዲያውኑ ለቀበሌ አስተዳደር ማሳወቅ ይኖርብዎታል።
                  </Typography>
                </Box>
              </Box>

              {/* Contact Information */}
              <Box>
                <Typography variant="subtitle2" sx={{ fontWeight: 700, color: '#0066b2', mb: 1, fontSize: '0.8rem' }}>
                  የመገኛ አድራሻ
                </Typography>
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 0.5 }}>
                  <Typography variant="caption" sx={{ display: 'flex', alignItems: 'center', fontSize: '0.7rem' }}>
                    <PhoneIcon sx={{ fontSize: 14, mr: 0.5, color: '#0066b2' }} />
                    +251-58-119-7060
                  </Typography>
                  <Typography variant="caption" sx={{ display: 'flex', alignItems: 'center', fontSize: '0.7rem' }}>
                    <EmailIcon sx={{ fontSize: 14, mr: 0.5, color: '#0066b2' }} />
                    <EMAIL>
                  </Typography>
                  <Typography variant="caption" sx={{ display: 'flex', alignItems: 'center', fontSize: '0.7rem' }}>
                    <LanguageIcon sx={{ fontSize: 14, mr: 0.5, color: '#0066b2' }} />
                    www.gondar.gov.et
                  </Typography>
                </Box>
              </Box>
            </Box>
          </Box>

          {/* Footer */}
          <Box sx={styles.footer}>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <SecurityIcon sx={{ color: '#0066b2', fontSize: 16, mr: 0.5 }} />
              <Typography variant="caption" sx={{ fontWeight: 600, color: '#0066b2', fontSize: '0.7rem' }}>
                በጎንደር ከተማ አስተዳደር የተሰጠ ህጋዊ መታወቂያ
              </Typography>
            </Box>
          </Box>
        </Box>
      )}
    </>
  );
};

export default IDCardLayout;
