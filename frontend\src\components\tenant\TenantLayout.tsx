import React, { useEffect, useState } from 'react';
import { Box, CssBaseline, CircularProgress } from '@mui/material';
import TenantHeader from './TenantHeader';

interface User {
  id: number;
  email: string;
  first_name: string;
  last_name: string;
  role: string;
}

interface Tenant {
  schema_name: string;
  name: string;
  type?: string;
  schema_type: string;
  logo_url?: string;
  header_color?: string;
  accent_color?: string;
}

interface TenantLayoutProps {
  children: React.ReactNode;
}

const TenantLayout: React.FC<TenantLayoutProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [tenant, setTenant] = useState<Tenant | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    console.log('TenantLayout: Loading user and tenant data');

    // Set a flag in sessionStorage to prevent multiple initializations
    const isInitialized = sessionStorage.getItem('tenant_layout_initialized');

    if (isInitialized === 'true') {
      console.log('TenantLayout: Already initialized, using cached data');

      // If already initialized, just set loading to false
      setLoading(false);
      return;
    }

    // Load user and tenant from localStorage
    try {
      localStorage.setItem('tenant_layout_initialized', 'true');

      // Get user from localStorage
      const storedUser = localStorage.getItem('user');

      // Get tenant from localStorage
      const storedTenant = localStorage.getItem('tenant');

      // Get user role from localStorage
      const storedRole = localStorage.getItem('user_role');
      const storedTenantType = localStorage.getItem('tenant_type');

      console.log('Stored role from localStorage:', storedRole);
      console.log('Stored tenant type from localStorage:', storedTenantType);

      // Process user data
      if (storedUser) {
        console.log('Using user data from localStorage');
        const parsedUser = JSON.parse(storedUser);

        // If we have a role from find-user-schema, use it
        if (storedRole && (!parsedUser.role || parsedUser.role === 'CENTER_STAFF')) {
          console.log(`Updating user role from ${parsedUser.role} to ${storedRole}`);
          parsedUser.role = storedRole;
        }

        setUser(parsedUser);
      }

      // Process tenant data
      if (storedTenant) {
        console.log('Using tenant data from localStorage');
        const parsedTenant = JSON.parse(storedTenant);

        // Check if we have a logo URL stored separately
        const logoUrl = localStorage.getItem('logo_url');
        if (logoUrl && !parsedTenant.logo_url) {
          parsedTenant.logo_url = logoUrl;
        }

        // If we have a tenant type from find-user-schema, use it
        if (storedTenantType && (!parsedTenant.schema_type || !parsedTenant.type)) {
          console.log(`Updating tenant type to ${storedTenantType}`);
          parsedTenant.schema_type = storedTenantType;
          parsedTenant.type = storedTenantType;
        }
        // Ensure schema_type is set if only type is available
        else if (!parsedTenant.schema_type && parsedTenant.type) {
          parsedTenant.schema_type = parsedTenant.type;
        }
        // Ensure type is set if only schema_type is available
        else if (!parsedTenant.type && parsedTenant.schema_type) {
          parsedTenant.type = parsedTenant.schema_type;
        }

        console.log('Tenant data loaded:', parsedTenant);
        setTenant(parsedTenant);
      }
    } catch (error) {
      console.error('Error loading user or tenant data:', error);
    } finally {
      setLoading(false);
    }

    // Cleanup function
    return () => {
      console.log('TenantLayout: Cleanup');
    };
  }, []);

  if (loading) {
    return (
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          height: '100vh',
        }}
      >
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        minHeight: '100vh',
      }}
    >
      <CssBaseline />
      <TenantHeader user={user} tenant={tenant} />
      <Box
        component="main"
        sx={{
          flexGrow: 1,
          width: '100%',
          display: 'flex',
          flexDirection: 'column',
          bgcolor: '#f5f5f5',
        }}
      >
        {children}
      </Box>
    </Box>
  );
};

export default TenantLayout;
