# Generated by Django 5.1.7 on 2025-04-17 15:09

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('centers', '0009_alter_city_options_remove_city_accent_color_and_more'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='center',
            options={'ordering': ['subcity', 'name'], 'verbose_name': 'Kebele', 'verbose_name_plural': 'Kebeles'},
        ),
        migrations.AlterModelOptions(
            name='subcity',
            options={'ordering': ['city', 'name'], 'verbose_name': 'SubCity', 'verbose_name_plural': 'SubCities'},
        ),
        migrations.AlterUniqueTogether(
            name='center',
            unique_together=set(),
        ),
        migrations.AlterUniqueTogether(
            name='subcity',
            unique_together=set(),
        ),
        migrations.AlterField(
            model_name='center',
            name='code',
            field=models.CharField(blank=True, help_text='Unique code for the center', max_length=10, null=True, unique=True),
        ),
        migrations.AlterField(
            model_name='subcity',
            name='code',
            field=models.CharField(blank=True, help_text='Unique code for the subcity', max_length=10, null=True, unique=True),
        ),
        migrations.AlterField(
            model_name='subcity',
            name='name',
            field=models.CharField(max_length=100, unique=True),
        ),
        migrations.CreateModel(
            name='Ketena',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(max_length=100)),
                ('code', models.CharField(blank=True, max_length=10, null=True, unique=True)),
                ('description', models.TextField(blank=True)),
                ('is_active', models.BooleanField(default=True)),
                ('center', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='ketenes', to='centers.center')),
            ],
            options={
                'verbose_name': 'Ketena',
                'verbose_name_plural': 'Ketenes',
                'ordering': ['center', 'name'],
            },
        ),
    ]
