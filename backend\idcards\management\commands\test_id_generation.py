from django.core.management.base import BaseCommand
from django.db import connection
from centers.models import Client, Center
from citizens.models import Citizen
from idcards.utils import generate_id_card_number, get_id_card_format

class Command(BaseCommand):
    help = 'Test ID card number generation with different formats'

    def add_arguments(self, parser):
        parser.add_argument('--schema', type=str, help='Schema name to use')
        parser.add_argument('--format', type=str, help='Custom format to test')

    def handle(self, *args, **options):
        schema_name = options.get('schema', 'kebele 14')
        custom_format = options.get('format')

        # Set to public schema first
        connection.set_schema_to_public()

        try:
            # Get the tenant
            tenant = Client.objects.get(schema_name=schema_name)
            self.stdout.write(self.style.SUCCESS(f"Found tenant: {tenant.name} (ID: {tenant.id})"))
            self.stdout.write(f"Schema name: {tenant.schema_name}")
            self.stdout.write(f"Schema type: {tenant.schema_type}")

            # Set the tenant for this request
            connection.set_tenant(tenant)

            # Get a center
            centers = Center.objects.all()
            if not centers.exists():
                self.stdout.write(self.style.ERROR("No centers found in this tenant"))
                return

            center = centers.first()
            self.stdout.write(self.style.SUCCESS(f"Using center: {center.name} (ID: {center.id})"))
            self.stdout.write(f"Center code: {center.code}")
            self.stdout.write(f"Center slug: {center.slug}")

            # Get a citizen
            citizens = Citizen.objects.all()
            if not citizens.exists():
                self.stdout.write(self.style.ERROR("No citizens found in this tenant"))
                return

            citizen = citizens.first()
            self.stdout.write(self.style.SUCCESS(f"Using citizen: {citizen.full_name} (ID: {citizen.id})"))
            self.stdout.write(f"Registration number: {citizen.registration_number}")

            # Get the ID card format
            id_format = custom_format or get_id_card_format(tenant=tenant, center=center)
            self.stdout.write(self.style.SUCCESS(f"Using ID card format: {id_format}"))

            # Generate ID card numbers with different formats
            formats_to_test = [
                id_format,
                "ID-{CENTER_PREFIX}-{RANDOM8}",
                "ID-{TENANT_PREFIX}-{YEAR}-{RANDOM6}",
                "ID-{CENTER_CODE}-{CITIZEN_ID}-{RANDOM4}",
                "ID-{TENANT_TYPE}-{YEAR}{MONTH}-{RANDOM6}",
                "ID-{CENTER_SLUG}-{CITIZEN_REG}-{RANDOM4}",
                "{TENANT_CODE}-{YEAR}-{RANDOM8}"
            ]

            self.stdout.write("\nTesting different ID card formats:")
            for fmt in formats_to_test:
                id_number = generate_id_card_number(
                    citizen=citizen,
                    center=center,
                    tenant=tenant,
                    format_string=fmt
                )
                self.stdout.write(f"Format: {fmt}")
                self.stdout.write(self.style.SUCCESS(f"Generated ID: {id_number}"))
                self.stdout.write("")

        except Client.DoesNotExist:
            self.stdout.write(self.style.ERROR(f"Tenant with schema {schema_name} does not exist"))
            
            # List all tenants
            self.stdout.write("\nAll tenants:")
            for t in Client.objects.all():
                self.stdout.write(f"- {t.name} (Schema: {t.schema_name}, Type: {t.schema_type})")
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"Error: {str(e)}"))
