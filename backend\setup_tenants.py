import os
import django
import random
import time

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'neocamelot.settings')
django.setup()

from django.contrib.auth import get_user_model
from centers.models import Client, Domain
from django.db import transaction, connection
from django_tenants.utils import schema_context, tenant_context

User = get_user_model()

def setup_tenants():
    """Set up the initial tenant structure for the system."""
    print("Setting up tenant structure...")

    # Create a super admin user if it doesn't exist
    if not User.objects.filter(email='<EMAIL>').exists():
        super_admin = User.objects.create_user(
            email='<EMAIL>',
            password='admin123',
            first_name='Super',
            last_name='Admin',
            role='SUPER_ADMIN',
            is_staff=True,
            is_active=True,
            is_superuser=True
        )
        print("Created super admin user")
    else:
        super_admin = User.objects.get(email='<EMAIL>')
        print("Super admin user already exists")

    # Create public schema tenant if it doesn't exist
    public_tenant = create_public_tenant(super_admin)

    # Create city tenant
    city_tenant = create_city_tenant(super_admin, public_tenant)

    # Create subcity tenants
    subcity_tenants = create_subcity_tenants(super_admin, city_tenant)

    # Create center tenants
    create_center_tenants(super_admin, subcity_tenants)

    print("Tenant setup complete!")

def create_public_tenant(super_admin):
    """Create the public schema tenant."""
    try:
        # Check if public tenant already exists
        public_tenant = Client.objects.filter(schema_name='public').first()
        if public_tenant:
            print("Public tenant already exists")
            return public_tenant

        # Create public tenant
        with transaction.atomic():
            public_tenant = Client(
                schema_name='public',
                name='Public',
                schema_type='CITY',
                description='Public schema for shared data',
                is_active=True,
                created_by=super_admin
            )
            # For public schema, we need to set auto_create_schema=False
            # since the public schema already exists
            public_tenant.auto_create_schema = False
            public_tenant.save()

            # Create domains for public tenant
            domain1 = Domain(
                domain='localhost',
                tenant=public_tenant,
                is_primary=True
            )
            domain1.save()

            # Add 127.0.0.1 domain for local development
            domain2 = Domain(
                domain='127.0.0.1',
                tenant=public_tenant,
                is_primary=False
            )
            domain2.save()

            print("Created public tenant")
            return public_tenant
    except Exception as e:
        print(f"Error creating public tenant: {str(e)}")
        raise

def create_city_tenant(super_admin, public_tenant):
    """Create a city tenant."""
    try:
        # Check if city tenant already exists
        city_tenant = Client.objects.filter(schema_name='city_addis_ababa').first()
        if city_tenant:
            print("City tenant already exists")
            return city_tenant

        # Create city tenant
        with transaction.atomic():
            city_tenant = Client(
                schema_name='city_addis_ababa',
                name='Addis Ababa',
                schema_type='CITY',
                description='Capital city of Ethiopia',
                parent=public_tenant,
                address='Addis Ababa, Ethiopia',
                phone='+251-111-111-111',
                email='<EMAIL>',
                website='https://www.addisababa.gov.et',
                admin_name='City Administrator',
                admin_email='<EMAIL>',
                admin_phone='+251-111-111-112',
                is_active=True,
                created_by=super_admin
            )
            city_tenant.save()

            # Wait for schema to be created
            time.sleep(1)

            # Create domain for city tenant
            domain = Domain(
                domain='addisababa.localhost',
                tenant=city_tenant,
                is_primary=True
            )
            domain.save()

            # Create a city admin user in the city schema
            with tenant_context(city_tenant):
                # Check if user already exists
                email = f'admin@{city_tenant.name.lower().replace(" ", "")}.tenant.gov.et'
                if not User.objects.filter(email=email).exists():
                    city_admin = User.objects.create_user(
                        email=email,
                        password='password123',
                        first_name=city_tenant.name,
                        last_name='Admin',
                        role='CITY_ADMIN',
                        is_active=True
                    )
                    print(f"Created city admin user in {city_tenant.name} schema")
                else:
                    print(f"City admin user already exists in {city_tenant.name} schema")

            print(f"Created city tenant: {city_tenant.name}")
            return city_tenant
    except Exception as e:
        print(f"Error creating city tenant: {str(e)}")
        raise

def create_subcity_tenants(super_admin, city_tenant):
    """Create subcity tenants."""
    subcity_names = ['Arada', 'Kirkos', 'Yeka', 'Bole', 'Lideta', 'Kolfe Keranio']
    subcity_tenants = []

    for name in subcity_names:
        try:
            # Check if subcity tenant already exists
            schema_name = f'subcity_{name.lower().replace(" ", "_")}'
            subcity_tenant = Client.objects.filter(schema_name=schema_name).first()
            if subcity_tenant:
                print(f"Subcity tenant {name} already exists")
                subcity_tenants.append(subcity_tenant)
                continue

            # Create subcity tenant
            with transaction.atomic():
                subcity_tenant = Client(
                    schema_name=schema_name,
                    name=name,
                    schema_type='SUBCITY',
                    description=f'{name} Subcity in Addis Ababa',
                    parent=city_tenant,
                    address=f'{name}, Addis Ababa, Ethiopia',
                    phone=f'+251-111-{random.randint(100000, 999999)}',
                    email=f'info@{name.lower().replace(" ", "")}.addisababa.gov.et',
                    website=f'https://www.{name.lower().replace(" ", "")}.addisababa.gov.et',
                    admin_name=f'{name} Administrator',
                    admin_email=f'admin@{name.lower().replace(" ", "")}.addisababa.gov.et',
                    admin_phone=f'+251-111-{random.randint(100000, 999999)}',
                    has_printing_facility=True,
                    printing_capacity=random.choice([50, 100, 150, 200]),
                    is_active=True,
                    created_by=super_admin
                )
                subcity_tenant.save()

                # Wait for schema to be created
                time.sleep(1)

                # Create domain for subcity tenant
                domain = Domain(
                    domain=f'{name.lower().replace(" ", "")}.addisababa.localhost',
                    tenant=subcity_tenant,
                    is_primary=True
                )
                domain.save()

                # Create a subcity admin user in the subcity schema
                with tenant_context(subcity_tenant):
                    # Check if user already exists
                    email = f'admin@{name.lower().replace(" ", "")}.tenant.gov.et'
                    if not User.objects.filter(email=email).exists():
                        subcity_admin = User.objects.create_user(
                            email=email,
                            password='password123',
                            first_name=name,
                            last_name='Admin',
                            role='SUBCITY_ADMIN',
                            is_active=True
                        )
                        print(f"Created subcity admin user in {subcity_tenant.name} schema")
                    else:
                        print(f"Subcity admin user already exists in {subcity_tenant.name} schema")

                subcity_tenants.append(subcity_tenant)
                print(f"Created subcity tenant: {subcity_tenant.name}")
        except Exception as e:
            print(f"Error creating subcity tenant {name}: {str(e)}")

    return subcity_tenants

def create_center_tenants(super_admin, subcity_tenants):
    """Create center tenants."""
    center_types = ['Main Office', 'Branch Office', 'Service Center', 'Registration Center']

    for subcity_tenant in subcity_tenants:
        for i in range(1, 4):  # Create 3 centers per subcity
            try:
                # Check if center tenant already exists
                center_name = f"{subcity_tenant.name} Kebele {i}"
                schema_name = f'center_{subcity_tenant.name.lower().replace(" ", "_")}_kebele_{i}'
                center_tenant = Client.objects.filter(schema_name=schema_name).first()
                if center_tenant:
                    print(f"Center tenant {center_name} already exists")
                    continue

                # Create center tenant
                with transaction.atomic():
                    center_tenant = Client(
                        schema_name=schema_name,
                        name=center_name,
                        schema_type='KEBELE',
                        description=f'ID card registration center for {center_name}',
                        parent=subcity_tenant,
                        address=f'Kebele {i}, {subcity_tenant.name}, Addis Ababa',
                        phone=f'+251-111-{random.randint(100000, 999999)}',
                        email=f'info@kebele{i}.{subcity_tenant.name.lower().replace(" ", "")}.gov.et',
                        admin_name=f'Kebele {i} Administrator',
                        admin_email=f'admin@kebele{i}.{subcity_tenant.name.lower().replace(" ", "")}.gov.et',
                        admin_phone=f'+251-111-{random.randint(100000, 999999)}',
                        center_type=random.choice(center_types),
                        max_users=random.choice([5, 10, 15]),
                        max_citizens=random.choice([500, 1000, 1500]),
                        max_id_cards=random.choice([500, 1000, 1500]),
                        is_active=True,
                        is_verified=True,
                        subscription_status=random.choice(['trial', 'basic', 'premium']),
                        created_by=super_admin
                    )
                    center_tenant.save()

                    # Wait for schema to be created
                    time.sleep(1)

                    # Create domain for center tenant
                    domain = Domain(
                        domain=f'kebele{i}.{subcity_tenant.name.lower().replace(" ", "")}.addisababa.localhost',
                        tenant=center_tenant,
                        is_primary=True
                    )
                    domain.save()

                    # Create a center admin user in the center schema
                    with tenant_context(center_tenant):
                        # Check if user already exists
                        email = f'admin@kebele{i}.{subcity_tenant.name.lower().replace(" ", "")}.tenant.gov.et'
                        if not User.objects.filter(email=email).exists():
                            center_admin = User.objects.create_user(
                                email=email,
                                password='password123',
                                first_name=f'Kebele {i}',
                                last_name='Admin',
                                role='CENTER_ADMIN',
                                is_active=True
                            )
                            print(f"Created center admin user in {center_tenant.name} schema")
                        else:
                            print(f"Center admin user already exists in {center_tenant.name} schema")

                    print(f"Created center tenant: {center_tenant.name}")
            except Exception as e:
                print(f"Error creating center tenant {center_name}: {str(e)}")

if __name__ == '__main__':
    setup_tenants()
