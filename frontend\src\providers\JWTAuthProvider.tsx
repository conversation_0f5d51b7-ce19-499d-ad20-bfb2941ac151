/**
 * JWT Authentication Provider
 *
 * This component provides JWT authentication context to the application.
 * It manages JWT tokens, authentication state, and provides authentication functions.
 */

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  loginWithJWT,
  refreshJWTTokens,
  validateJ<PERSON>TToken,
  getAccessTokenForSchema,
  clearAllTokens,
  getAuthHeaders as getTokenAuthHeaders,
  getCurrentSchema,
  JWTTokenResponse
} from '../services/tokenService';

// Authentication context interface
interface AuthContextType {
  isAuthenticated: boolean;
  user: any | null;
  tenant: any | null;
  schema: string | null;
  login: (email: string, password: string, schema?: string) => Promise<boolean>;
  logout: () => void;
  refreshToken: () => Promise<boolean>;
  getAuthHeaders: () => HeadersInit;
}

// Create authentication context
const AuthContext = createContext<AuthContextType | null>(null);

// Authentication provider props
interface AuthProviderProps {
  children: ReactNode;
}

// JWT Authentication Provider component
export const JWTAuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const navigate = useNavigate();
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);
  const [user, setUser] = useState<any | null>(null);
  const [tenant, setTenant] = useState<any | null>(null);
  const [schema, setSchema] = useState<string | null>(null);
  const [isInitialized, setIsInitialized] = useState<boolean>(false);

  // Initialize authentication state
  useEffect(() => {
    const initializeAuth = async () => {
      try {
        console.log('Initializing authentication state...');

        // Check for cookies first (they might exist even if localStorage is cleared)
        const hasCookies = document.cookie.includes('jwt_access_token') ||
                          document.cookie.includes('jwt_refresh_token') ||
                          document.cookie.includes('jwt_access_token_frontend');

        console.log('Cookies present:', hasCookies);

        // Get schema from unified token service
        let storedSchema = getCurrentSchema();

        // If no schema but we have cookies, try to extract schema from cookies
        if (!storedSchema && hasCookies) {
          console.log('No schema in localStorage but cookies exist, trying to extract schema from cookies');

          // Try to get schema from tenant in localStorage
          const tenantStr = localStorage.getItem('tenant') || sessionStorage.getItem('tenant');
          if (tenantStr) {
            try {
              const tenant = JSON.parse(tenantStr);
              if (tenant && tenant.schema_name) {
                storedSchema = tenant.schema_name;
                console.log('Extracted schema from tenant:', storedSchema);
              }
            } catch (e) {
              console.error('Error parsing tenant data:', e);
            }
          }

          // If still no schema, check sessionStorage
          if (!storedSchema) {
            storedSchema = sessionStorage.getItem('schema_name') ||
                          sessionStorage.getItem('jwt_schema') ||
                          sessionStorage.getItem('currentSchema');

            if (storedSchema) {
              console.log('Found schema in sessionStorage:', storedSchema);
            }
          }
        }

        if (!storedSchema) {
          console.log('No schema found, user is not authenticated');
          setIsAuthenticated(false);
          setIsInitialized(true);
          return;
        }

        console.log('Using schema:', storedSchema);

        // Get user from localStorage or sessionStorage
        const userStr = localStorage.getItem('user') || sessionStorage.getItem('user');
        const user = userStr ? JSON.parse(userStr) : null;

        // Get tenant from localStorage or sessionStorage
        const tenantStr = localStorage.getItem('tenant') || sessionStorage.getItem('tenant');
        const tenant = tenantStr ? JSON.parse(tenantStr) : null;

        // Validate token
        console.log('Validating token for schema:', storedSchema);
        const isValid = await validateJWTToken(storedSchema);

        if (isValid) {
          console.log('Token is valid, setting authenticated state');
          setIsAuthenticated(true);
          setUser(user);
          setTenant(tenant);
          setSchema(storedSchema);

          // Ensure schema is stored in localStorage for consistency
          localStorage.setItem('schema_name', storedSchema);
          localStorage.setItem('jwt_schema', storedSchema);
        } else {
          console.log('Token is invalid, trying to refresh');
          // Try to refresh token
          try {
            const refreshResult = await refreshJWTTokens(storedSchema);
            if (refreshResult && refreshResult.access_token) {
              console.log('Token refreshed successfully');
              setIsAuthenticated(true);
              setUser(user);
              setTenant(tenant);
              setSchema(storedSchema);

              // Ensure schema is stored in localStorage for consistency
              localStorage.setItem('schema_name', storedSchema);
              localStorage.setItem('jwt_schema', storedSchema);
            } else {
              console.error('Failed to refresh token');
              setIsAuthenticated(false);
            }
          } catch (error) {
            console.error('Error refreshing token:', error);
            setIsAuthenticated(false);
          }
        }
      } catch (error) {
        console.error('Error initializing auth:', error);
        setIsAuthenticated(false);
      } finally {
        setIsInitialized(true);
      }
    };

    initializeAuth();
  }, []);

  // Login function
  const login = async (email: string, password: string, schema?: string): Promise<boolean> => {
    try {
      const response = await loginWithJWT(email, password, schema);

      if (response && response.access_token) {
        // Set authentication state
        setIsAuthenticated(true);
        setUser(response.user || null);
        setTenant(response.tenant || null);
        setSchema(response.tenant?.schema_name || schema || null);

        // Store user and tenant in localStorage
        if (response.user) {
          localStorage.setItem('user', JSON.stringify(response.user));
        }

        if (response.tenant) {
          localStorage.setItem('tenant', JSON.stringify(response.tenant));
        }

        if (response.tenant?.schema_name || schema) {
          localStorage.setItem('schema_name', response.tenant?.schema_name || schema || '');
        }

        return true;
      }

      return false;
    } catch (error) {
      console.error('Login error:', error);
      return false;
    }
  };

  // Logout function
  const logout = () => {
    // Clear tokens
    clearAllTokens();

    // Clear authentication state
    setIsAuthenticated(false);
    setUser(null);
    setTenant(null);
    setSchema(null);

    // Clear localStorage
    localStorage.removeItem('user');
    localStorage.removeItem('tenant');
    localStorage.removeItem('schema_name');

    // Redirect to login page
    navigate('/login');
  };

  // Refresh token function
  const refreshToken = async (): Promise<boolean> => {
    if (!schema) {
      return false;
    }

    try {
      const response = await refreshJWTTokens(schema);
      return !!response.access_token;
    } catch (error) {
      console.error('Error refreshing token:', error);
      return false;
    }
  };

  // Get authentication headers
  const getAuthHeaders = (): HeadersInit => {
    return getTokenAuthHeaders(schema || undefined);
  };

  // Context value
  const contextValue: AuthContextType = {
    isAuthenticated,
    user,
    tenant,
    schema,
    login,
    logout,
    refreshToken,
    getAuthHeaders,
  };

  // Show loading state while initializing
  if (!isInitialized) {
    return <div>Loading authentication...</div>;
  }

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
};

// Custom hook to use authentication context
export const useJWTAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useJWTAuth must be used within a JWTAuthProvider');
  }
  return context;
};

export default JWTAuthProvider;
