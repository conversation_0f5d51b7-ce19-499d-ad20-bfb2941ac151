import os
import django

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'neocamelot.settings')
django.setup()

from centers.models import Client, Domain

def add_domain():
    """Add 127.0.0.1 domain to the public tenant."""
    print("Adding 127.0.0.1 domain to the public tenant...")
    
    # Get the public tenant
    public_tenant = Client.objects.filter(schema_name='public').first()
    
    if public_tenant:
        # Create domain for 127.0.0.1
        domain, created = Domain.objects.get_or_create(
            domain='127.0.0.1',
            tenant=public_tenant,
            defaults={'is_primary': False}
        )
        if created:
            print("Created domain for 127.0.0.1")
        else:
            print("Domain for 127.0.0.1 already exists")
    else:
        print("Public tenant not found")

if __name__ == '__main__':
    add_domain()
