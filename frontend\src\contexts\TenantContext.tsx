import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

interface Tenant {
  id: number;
  schema_name: string;
  name: string;
  type: string;
  logo_url?: string;
  header_color?: string;
  accent_color?: string;
}

interface TenantContextType {
  tenant: Tenant | null;
  setTenant: React.Dispatch<React.SetStateAction<Tenant | null>>;
  schemaName: string | null;
  setSchemaName: React.Dispatch<React.SetStateAction<string | null>>;
  loading: boolean;
}

const TenantContext = createContext<TenantContextType | undefined>(undefined);

export const TenantProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [tenant, setTenant] = useState<Tenant | null>(null);
  const [schemaName, setSchemaName] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Load tenant from localStorage on component mount
    const storedTenant = localStorage.getItem('tenant');
    const storedSchemaName = localStorage.getItem('schema_name');

    if (storedTenant) {
      try {
        const parsedTenant = JSON.parse(storedTenant);
        setTenant(parsedTenant);
      } catch (error) {
        console.error('Error parsing tenant data:', error);
      }
    }

    if (storedSchemaName) {
      setSchemaName(storedSchemaName);
    }

    setLoading(false);
  }, []);

  // Save tenant to localStorage whenever it changes
  useEffect(() => {
    if (tenant) {
      localStorage.setItem('tenant', JSON.stringify(tenant));
    }
  }, [tenant]);

  // Save schema name to localStorage whenever it changes
  useEffect(() => {
    if (schemaName) {
      localStorage.setItem('schema_name', schemaName);
    }
  }, [schemaName]);

  return (
    <TenantContext.Provider value={{ tenant, setTenant, schemaName, setSchemaName, loading }}>
      {children}
    </TenantContext.Provider>
  );
};

export const useTenant = (): TenantContextType => {
  const context = useContext(TenantContext);
  if (context === undefined) {
    throw new Error('useTenant must be used within a TenantProvider');
  }
  return context;
};
