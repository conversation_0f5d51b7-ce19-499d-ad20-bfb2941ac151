from rest_framework import viewsets, permissions, filters, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from .models_document import Document
from .serializers_document import DocumentSerializer
from common.models import DocumentType
from .serializers_document import DocumentTypeSerializer
from accounts.permissions import IsCenterAdmin, IsAdminOrSuperAdmin, IsKebeleLevelOrSuperAdmin

class DocumentTypeViewSet(viewsets.ModelViewSet):
    """ViewSet for viewing and editing DocumentType instances."""
    queryset = DocumentType.objects.all()
    serializer_class = DocumentTypeSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['is_active']
    search_fields = ['name', 'description']
    ordering_fields = ['name', 'created_at']

    def get_permissions(self):
        """Set custom permissions for different actions."""
        if self.action in ['create', 'update', 'partial_update', 'destroy']:
            return [IsAdminOrSuperAdmin()]
        return [permissions.IsAuthenticated()]

class DocumentViewSet(viewsets.ModelViewSet):
    """ViewSet for viewing and editing Document instances."""
    queryset = Document.objects.all()
    serializer_class = DocumentSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['citizen', 'document_type', 'is_active']
    search_fields = ['document_type__name', 'citizen__first_name', 'citizen__last_name']
    ordering_fields = ['document_type__name', 'issue_date', 'expiry_date', 'created_at']

    def get_permissions(self):
        """Set custom permissions for different actions."""
        if self.action in ['create', 'update', 'partial_update', 'destroy']:
            return [IsKebeleLevelOrSuperAdmin()]
        return [permissions.IsAuthenticated()]

    @action(detail=False, methods=['get'])
    def by_citizen(self, request):
        """Get all documents for a specific citizen."""
        citizen_id = request.query_params.get('citizen_id')
        if not citizen_id:
            return Response({'error': 'citizen_id is required'}, status=status.HTTP_400_BAD_REQUEST)

        documents = Document.objects.filter(citizen_id=citizen_id)
        serializer = self.get_serializer(documents, many=True)
        return Response(serializer.data)
