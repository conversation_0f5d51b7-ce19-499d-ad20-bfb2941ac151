from rest_framework import status
from rest_framework.decorators import api_view, permission_classes, authentication_classes
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from citizens.models import Citizen, Spouse, Parent, Child, EmergencyContact, Document, DocumentType
from citizens.serializers import SpouseSerializer, ParentSerializer, ChildSerializer, EmergencyContactSerializer, DocumentSerializer
from citizens.authentication import CitizenJWTAuthentication
from django.shortcuts import get_object_or_404
import os
from django.conf import settings
from django.utils import timezone
import logging

# Configure logging
logger = logging.getLogger(__name__)

@api_view(['POST'])
@permission_classes([IsAuthenticated])
@authentication_classes([CitizenJWTAuthentication])
def add_spouse(request, citizen_id):
    """
    Add a spouse for a citizen
    """
    try:
        logger.info(f"Adding spouse for citizen {citizen_id}")
        logger.debug(f"Request headers: {dict(request.headers)}")
        logger.debug(f"Request user: {request.user}")

        citizen = get_object_or_404(Citizen, id=citizen_id)

        # Check if the citizen already has a spouse
        existing_spouse = Spouse.objects.filter(citizen=citizen).first()
        if existing_spouse:
            logger.warning(f"Citizen {citizen_id} already has a spouse")
            return Response({"detail": "Citizen already has a spouse"}, status=status.HTTP_400_BAD_REQUEST)

        # Create a new spouse
        serializer = SpouseSerializer(data=request.data)
        if serializer.is_valid():
            serializer.save(citizen=citizen)
            logger.info(f"Successfully added spouse for citizen {citizen_id}")
            return Response(serializer.data, status=status.HTTP_201_CREATED)

        logger.warning(f"Invalid data for spouse: {serializer.errors}")
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    except Exception as e:
        logger.error(f"Error adding spouse for citizen {citizen_id}: {str(e)}")
        return Response({"detail": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['POST'])
@permission_classes([IsAuthenticated])
@authentication_classes([CitizenJWTAuthentication])
def add_parent(request, citizen_id):
    """
    Add a parent for a citizen
    """
    try:
        logger.info(f"Adding parent for citizen {citizen_id}")
        logger.debug(f"Request headers: {dict(request.headers)}")
        logger.debug(f"Request user: {request.user}")

        citizen = get_object_or_404(Citizen, id=citizen_id)

        # Create a new parent
        serializer = ParentSerializer(data=request.data)
        if serializer.is_valid():
            serializer.save(citizen=citizen)
            logger.info(f"Successfully added parent for citizen {citizen_id}")
            return Response(serializer.data, status=status.HTTP_201_CREATED)

        logger.warning(f"Invalid data for parent: {serializer.errors}")
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    except Exception as e:
        logger.error(f"Error adding parent for citizen {citizen_id}: {str(e)}")
        return Response({"detail": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['POST'])
@permission_classes([IsAuthenticated])
@authentication_classes([CitizenJWTAuthentication])
def add_child(request, citizen_id):
    """
    Add a child for a citizen
    """
    try:
        logger.info(f"Adding child for citizen {citizen_id}")
        logger.debug(f"Request headers: {dict(request.headers)}")
        logger.debug(f"Request user: {request.user}")

        citizen = get_object_or_404(Citizen, id=citizen_id)

        # Create a new child
        serializer = ChildSerializer(data=request.data)
        if serializer.is_valid():
            serializer.save(citizen=citizen)
            logger.info(f"Successfully added child for citizen {citizen_id}")
            return Response(serializer.data, status=status.HTTP_201_CREATED)

        logger.warning(f"Invalid data for child: {serializer.errors}")
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    except Exception as e:
        logger.error(f"Error adding child for citizen {citizen_id}: {str(e)}")
        return Response({"detail": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['POST'])
@permission_classes([IsAuthenticated])
@authentication_classes([CitizenJWTAuthentication])
def add_emergency_contact(request, citizen_id):
    """
    Add an emergency contact for a citizen
    """
    try:
        logger.info(f"Adding emergency contact for citizen {citizen_id}")
        logger.debug(f"Request headers: {dict(request.headers)}")
        logger.debug(f"Request user: {request.user}")

        citizen = get_object_or_404(Citizen, id=citizen_id)

        # Create a new emergency contact
        serializer = EmergencyContactSerializer(data=request.data)
        if serializer.is_valid():
            serializer.save(citizen=citizen)
            logger.info(f"Successfully added emergency contact for citizen {citizen_id}")
            return Response(serializer.data, status=status.HTTP_201_CREATED)

        logger.warning(f"Invalid data for emergency contact: {serializer.errors}")
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    except Exception as e:
        logger.error(f"Error adding emergency contact for citizen {citizen_id}: {str(e)}")
        return Response({"detail": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['POST'])
@permission_classes([IsAuthenticated])
@authentication_classes([CitizenJWTAuthentication])
def add_document(request, citizen_id):
    """
    Add a document for a citizen
    """
    try:
        logger.info(f"Adding document for citizen {citizen_id}")
        logger.debug(f"Request headers: {dict(request.headers)}")
        logger.debug(f"Request user: {request.user}")

        citizen = get_object_or_404(Citizen, id=citizen_id)

        # Get the document type
        document_type_id = request.data.get('document_type')
        if not document_type_id:
            logger.warning(f"Document type is required for citizen {citizen_id}")
            return Response({"detail": "Document type is required"}, status=status.HTTP_400_BAD_REQUEST)

        document_type = get_object_or_404(DocumentType, id=document_type_id)
        logger.info(f"Using document type {document_type.name} (ID: {document_type.id})")

        # Create a directory for the citizen's documents if it doesn't exist
        citizen_dir = os.path.join(settings.MEDIA_ROOT, 'documents', f'citizen_{citizen.id}')
        os.makedirs(citizen_dir, exist_ok=True)
        logger.debug(f"Created document directory: {citizen_dir}")

        # Handle the file upload
        document_file = request.FILES.get('document_file')
        if not document_file:
            logger.warning(f"Document file is required for citizen {citizen_id}")
            return Response({"detail": "Document file is required"}, status=status.HTTP_400_BAD_REQUEST)

        logger.info(f"Received document file: {document_file.name}, size: {document_file.size} bytes")

        # Create a new document
        document = Document(
            citizen=citizen,
            document_type=document_type,
            document_file=document_file,
            issue_date=request.data.get('issue_date'),
            expiry_date=request.data.get('expiry_date'),
            is_active=True,
            created_at=timezone.now(),
            updated_at=timezone.now()
        )
        document.save()
        logger.info(f"Successfully saved document for citizen {citizen_id}")

        serializer = DocumentSerializer(document)
        return Response(serializer.data, status=status.HTTP_201_CREATED)
    except Exception as e:
        logger.error(f"Error adding document for citizen {citizen_id}: {str(e)}")
        return Response({"detail": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
