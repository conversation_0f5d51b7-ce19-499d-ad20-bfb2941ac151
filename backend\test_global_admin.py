import os
import django

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'neocamelot.settings')
django.setup()

# Import models and admin
from django.contrib import admin
from citizens.models import Citizen
from idcards.models import IDCard, IDCardTemplate
from centers.models import Client
from django_tenants.utils import tenant_context, schema_context

# Import the global admin classes
from global_admin import GlobalCitizenAdmin, GlobalIDCardTemplateAdmin, GlobalIDCardAdmin

# Check if the models are registered in the admin site
registered_models = [model.__name__ for model, _ in admin.site._registry.items()]
print(f"Currently registered models: {registered_models}")

# Register the models with the admin site if not already registered
if 'Citizen' not in registered_models:
    admin.site.register(Citizen, GlobalCitizenAdmin)
    print("Registered Citizen model with GlobalCitizenAdmin")

if 'IDCardTemplate' not in registered_models:
    admin.site.register(IDCardTemplate, GlobalIDCardTemplateAdmin)
    print("Registered IDCardTemplate model with GlobalIDCardTemplateAdmin")

if 'IDCard' not in registered_models:
    admin.site.register(IDCard, GlobalIDCardAdmin)
    print("Registered IDCard model with GlobalIDCardAdmin")

# Check registration again
registered_models = [model.__name__ for model, _ in admin.site._registry.items()]
print(f"Updated registered models: {registered_models}")

# Test the global admin classes
print("\nTesting GlobalCitizenAdmin...")
citizen_admin = GlobalCitizenAdmin(Citizen, admin.site)
citizens = citizen_admin.get_queryset(None)
print(f"Found {len(citizens)} citizens across all tenants")

print("\nTesting GlobalIDCardTemplateAdmin...")
template_admin = GlobalIDCardTemplateAdmin(IDCardTemplate, admin.site)
templates = template_admin.get_queryset(None)
print(f"Found {len(templates)} ID card templates across all tenants")

print("\nTesting GlobalIDCardAdmin...")
card_admin = GlobalIDCardAdmin(IDCard, admin.site)
cards = card_admin.get_queryset(None)
print(f"Found {len(cards)} ID cards across all tenants")

print("\nDone!")
