import os
import django

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'neocamelot.settings')
django.setup()

# Import models
from centers.models import Client
from django.contrib.auth import get_user_model
from django_tenants.utils import tenant_context, schema_context
from django.db import connection
from django.core.management import call_command

User = get_user_model()

def fix_tenant_admin_users():
    """Fix admin users in tenant schemas by ensuring the accounts_user table exists and creating admin users if needed."""
    print("\n===== FIXING TENANT ADMIN USERS =====\n")

    # Get all tenants except public
    tenants = Client.objects.exclude(schema_name='public')
    print(f"Found {tenants.count()} tenants to check")

    for tenant in tenants:
        print(f"\nChecking tenant: {tenant.name} (Schema: {tenant.schema_name})")

        # Set the connection to the tenant's schema
        connection.set_schema(tenant.schema_name)

        # Create a cursor
        cursor = connection.cursor()

        # Check if the accounts_user table exists
        try:
            cursor.execute(
                "SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'accounts_user')"
            )
            table_exists = cursor.fetchone()[0]

            if not table_exists:
                print(f"accounts_user table does not exist in {tenant.schema_name} schema. Creating it...")
                # Create the accounts_user table by running migrations
                call_command('migrate', 'accounts', schema_name=tenant.schema_name)
                print(f"Created accounts_user table in {tenant.schema_name} schema")
            else:
                print(f"accounts_user table exists in {tenant.schema_name} schema")

                # Check if the username column exists (it shouldn't in our custom User model)
                cursor.execute(
                    """SELECT EXISTS (
                        SELECT FROM information_schema.columns
                        WHERE table_name = 'accounts_user' AND column_name = 'username'
                    )"""
                )
                username_column_exists = cursor.fetchone()[0]

                if username_column_exists:
                    print(f"WARNING: username column exists in accounts_user table in {tenant.schema_name} schema")

            # Check if there are any admin users
            with tenant_context(tenant):
                admin_users = User.objects.filter(role__in=['CENTER_ADMIN', 'SUBCITY_ADMIN', 'CITY_ADMIN'])
                if not admin_users.exists():
                    print(f"No admin users found in {tenant.schema_name} schema. Creating one...")

                    # Determine the role based on tenant type
                    role = 'KEBELE_ADMIN' if tenant.schema_type == 'KEBELE' else \
                           'SUBCITY_ADMIN' if tenant.schema_type == 'SUBCITY' else \
                           'CITY_ADMIN'

                    # Create an admin user
                    admin_email = tenant.admin_email or f"admin@{tenant.schema_name}.neocamelot.com"
                    admin_user = User.objects.create_user(
                        email=admin_email,
                        password='password123',  # Default password, should be changed
                        first_name=tenant.name,
                        last_name='Admin',
                        role=role,
                        is_active=True
                    )
                    print(f"Created admin user {admin_user.email} in {tenant.schema_name} schema")
                else:
                    print(f"Found {admin_users.count()} admin users in {tenant.schema_name} schema")
                    for user in admin_users:
                        print(f"- {user.email} (Role: {user.role})")
        except Exception as e:
            print(f"Error fixing admin users in {tenant.schema_name} schema: {str(e)}")

    print("\n===== TENANT ADMIN USERS FIX COMPLETE =====\n")

if __name__ == '__main__':
    fix_tenant_admin_users()
