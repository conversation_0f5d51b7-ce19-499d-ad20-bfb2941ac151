"""
<PERSON><PERSON>t to create a token in the authtoken_token table for a user.
This script can be used to manually create tokens when the automatic token creation fails.
"""

import os
import sys
import django

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'neocamelot.settings')
django.setup()

from django.contrib.auth import get_user_model
from rest_framework.authtoken.models import Token
from django.db import connection
from django_tenants.utils import tenant_context, get_tenant_model
from centers.models import Client

User = get_user_model()

def create_token_for_user_in_schema(email, schema_name):
    """
    Create a token for a user in a specific schema.
    
    Args:
        email (str): The email of the user
        schema_name (str): The schema name of the tenant
        
    Returns:
        str: The token key or None if the user is not found
    """
    print(f"\n=== Creating token for user {email} in schema {schema_name} ===")
    
    try:
        # Get the tenant
        tenant = Client.objects.get(schema_name=schema_name)
        print(f"Found tenant: {tenant.name} (Schema: {tenant.schema_name})")
        
        # Switch to the tenant context
        with tenant_context(tenant):
            # Try to find the user
            try:
                user = User.objects.get(email=email)
                print(f"Found user: {user.email} (ID: {user.id})")
                
                # Delete any existing tokens for this user
                Token.objects.filter(user=user).delete()
                print(f"Deleted existing tokens for user {user.email}")
                
                # Create a new token
                token = Token.objects.create(user=user)
                print(f"Created new token: {token.key}")
                
                return token.key
            except User.DoesNotExist:
                print(f"User with email {email} not found in schema {schema_name}")
                return None
    except Client.DoesNotExist:
        print(f"Tenant with schema {schema_name} does not exist")
        return None
    except Exception as e:
        print(f"Error creating token: {str(e)}")
        return None

def create_token_for_all_users_in_schema(schema_name):
    """
    Create tokens for all users in a specific schema.
    
    Args:
        schema_name (str): The schema name of the tenant
        
    Returns:
        list: A list of created token keys
    """
    print(f"\n=== Creating tokens for all users in schema {schema_name} ===")
    
    try:
        # Get the tenant
        tenant = Client.objects.get(schema_name=schema_name)
        print(f"Found tenant: {tenant.name} (Schema: {tenant.schema_name})")
        
        # Switch to the tenant context
        with tenant_context(tenant):
            # Get all users
            users = User.objects.all()
            print(f"Found {len(users)} users in {schema_name}")
            
            if not users.exists():
                print(f"No users found in {schema_name}")
                return []
            
            tokens = []
            for user in users:
                # Delete any existing tokens for this user
                Token.objects.filter(user=user).delete()
                
                # Create a new token
                token = Token.objects.create(user=user)
                tokens.append(token.key)
                
                print(f"Created token for user {user.email}: {token.key}")
            
            return tokens
    except Client.DoesNotExist:
        print(f"Tenant with schema {schema_name} does not exist")
        return []
    except Exception as e:
        print(f"Error creating tokens: {str(e)}")
        return []

def list_all_schemas():
    """
    List all available schemas.
    
    Returns:
        list: A list of schema names
    """
    try:
        # Get all tenants
        tenants = Client.objects.all()
        schemas = [tenant.schema_name for tenant in tenants]
        return schemas
    except Exception as e:
        print(f"Error listing schemas: {str(e)}")
        return []

if __name__ == "__main__":
    # Check if we have command line arguments
    if len(sys.argv) < 2:
        print("Usage:")
        print("  python create_auth_token.py list_schemas")
        print("  python create_auth_token.py create_for_user <email> <schema_name>")
        print("  python create_auth_token.py create_for_all <schema_name>")
        sys.exit(1)
    
    command = sys.argv[1]
    
    if command == "list_schemas":
        schemas = list_all_schemas()
        print("\nAvailable schemas:")
        for schema in schemas:
            print(f"  {schema}")
    
    elif command == "create_for_user":
        if len(sys.argv) < 4:
            print("Usage: python create_auth_token.py create_for_user <email> <schema_name>")
            sys.exit(1)
        
        email = sys.argv[2]
        schema_name = sys.argv[3]
        
        token = create_token_for_user_in_schema(email, schema_name)
        if token:
            print(f"\nToken created successfully: {token}")
            print(f"Use this token in your API calls with header: Authorization: Token {token}")
    
    elif command == "create_for_all":
        if len(sys.argv) < 3:
            print("Usage: python create_auth_token.py create_for_all <schema_name>")
            sys.exit(1)
        
        schema_name = sys.argv[2]
        
        tokens = create_token_for_all_users_in_schema(schema_name)
        if tokens:
            print(f"\nCreated {len(tokens)} tokens successfully")
    
    else:
        print(f"Unknown command: {command}")
        print("Usage:")
        print("  python create_auth_token.py list_schemas")
        print("  python create_auth_token.py create_for_user <email> <schema_name>")
        print("  python create_auth_token.py create_for_all <schema_name>")
