# Generated by Django 5.1.7 on 2025-04-18 14:47

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('common', '0004_populate_new_models'),
    ]

    operations = [
        migrations.CreateModel(
            name='Ketena',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('code', models.Char<PERSON>ield(help_text='Ketena code', max_length=5)),
                ('kebele_id', models.Char<PERSON>ield(help_text='ID of the kebele this ketena belongs to', max_length=50)),
            ],
            options={
                'verbose_name': 'Ketena',
                'verbose_name_plural': 'Ketenas',
                'ordering': ['name'],
                'unique_together': {('code', 'kebele_id')},
            },
        ),
    ]
