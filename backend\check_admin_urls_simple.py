import os
import django

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'neocamelot.settings')
django.setup()

# Import URL patterns
from neocamelot.urls import urlpatterns as public_urlpatterns
from neocamelot.urls_tenants import urlpatterns as tenant_urlpatterns

# Check admin URLs
print("Admin URLs in public schema:")
for pattern in public_urlpatterns:
    print(f"- {pattern}")

print("\nAdmin URLs in tenant schemas:")
for pattern in tenant_urlpatterns:
    print(f"- {pattern}")
