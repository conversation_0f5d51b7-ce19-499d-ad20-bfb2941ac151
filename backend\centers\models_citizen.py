from django.db import models
from .models_region import Timestamp

class Religion(Timestamp):
    """Model representing a religion."""
    name = models.CharField(max_length=100)
    is_active = models.BooleanField(default=True)  # Boolean field for activity status

    def __str__(self):
        return self.name

    class Meta:
        verbose_name = "Religion"
        verbose_name_plural = "Religions"


class CitizenStatus(Timestamp):
    """Model representing a citizen's status."""
    name = models.CharField(max_length=100)  # e.g., Citizen, Resident, Visitor, Foreign National
    is_active = models.BooleanField(default=True)  # Boolean field for activity status

    def __str__(self):
        return self.name

    class Meta:
        verbose_name = "Citizen Status"
        verbose_name_plural = "Citizen Statuses"


class MaritalStatus(Timestamp):
    """Model representing a marital status."""
    name = models.CharField(max_length=50)  # e.g., Single, Married, etc.
    is_active = models.BooleanField(default=True)  # Boolean field for activity status

    def __str__(self):
        return self.name

    class Meta:
        verbose_name = "Marital Status"
        verbose_name_plural = "Marital Statuses"


class DocumentType(Timestamp):
    """Model representing a document type."""
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True, null=True)
    is_active = models.BooleanField(default=True)  # Boolean field for activity status

    def __str__(self):
        return self.name

    class Meta:
        verbose_name = "Document Type"
        verbose_name_plural = "Document Types"


class EmploymentType(Timestamp):
    """Model representing an employment type."""
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True, null=True)
    is_active = models.BooleanField(default=True)  # Boolean field for activity status

    def __str__(self):
        return self.name

    class Meta:
        verbose_name = "Employment Type"
        verbose_name_plural = "Employment Types"


class EmployeeType(Timestamp):
    """Model representing types of employees."""
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True, null=True)
    is_active = models.BooleanField(default=True)  # Boolean field for activity status

    def __str__(self):
        return self.name

    class Meta:
        verbose_name = "Employee Type"
        verbose_name_plural = "Employee Types"
