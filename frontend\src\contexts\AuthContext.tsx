import React, { createContext, useContext, useState, useEffect, useCallback, ReactNode } from 'react';
import {
  loginWithJWT as apiLoginWithJWT,
  refreshJWTTokens,
  validateJWTToken,
  clearAllTokens,
  getCurrentSchema,
  getAuthHeaders as getTokenAuthHeaders
} from '../services/tokenService';
import { initializeTokenRefresh } from '../utils/tokenRefresh';

// Define types for better type safety
interface User {
  id: number;
  username: string;
  email: string;
  role: string;
  first_name?: string;
  last_name?: string;
  [key: string]: any; // Allow for additional properties
}

interface Tenant {
  id: number;
  name: string;
  schema_name: string;
  schema_type?: string;
  type?: string;
  [key: string]: any; // Allow for additional properties
}

interface AuthContextType {
  token: string;
  user: User | null;
  schema: string;
  tenant: Tenant | null;
  setToken: React.Dispatch<React.SetStateAction<string>>;
  setUser: React.Dispatch<React.SetStateAction<User | null>>;
  setSchema: React.Dispatch<React.SetStateAction<string>>;
  isAuthenticated: boolean;
  isValidating: boolean;
  validateToken: () => Promise<boolean>;
  logout: () => void;
  loginWithJWT: (email: string, password: string, schema?: string) => Promise<boolean>;
  refreshJWTToken: () => Promise<boolean>;
  getAuthHeaders: () => HeadersInit;
}

// Storage keys for better maintainability
const STORAGE_KEYS = {
  ACCESS_TOKEN: 'jwt_access_token',
  REFRESH_TOKEN: 'jwt_refresh_token',
  JWT_SCHEMA: 'jwt_schema',
  SCHEMA_NAME: 'schema_name',
  USER: 'user',
  TENANT: 'tenant',
  USER_ROLE: 'user_role',
  TENANT_TYPE: 'tenant_type',
  LAST_VALIDATION: 'last_auth_validation_time',
  LAST_REFRESH: 'last_jwt_refresh_time'
};

// Create context with undefined initial value
const AuthContext = createContext<AuthContextType | undefined>(undefined);

/**
 * Helper function to get item from storage with proper error handling
 */
const getFromStorage = <T,>(key: string, defaultValue: T): T => {
  try {
    const item = localStorage.getItem(key);
    if (!item) return defaultValue;
    return JSON.parse(item) as T;
  } catch (error) {
    console.error(`Error getting ${key} from storage:`, error);
    return defaultValue;
  }
};

/**
 * Helper function to save item to storage with proper error handling
 */
const saveToStorage = (key: string, value: any): void => {
  try {
    if (value === null || value === undefined) {
      localStorage.removeItem(key);
    } else {
      const valueToStore = typeof value === 'string' ? value : JSON.stringify(value);
      localStorage.setItem(key, valueToStore);
    }
  } catch (error) {
    console.error(`Error saving ${key} to storage:`, error);
  }
};

/**
 * Helper function to clear a cookie
 */
const clearCookie = (name: string): void => {
  document.cookie = `${name}=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT`;
};

/**
 * Helper function to set a cookie
 */
const setCookie = (name: string, value: string): void => {
  document.cookie = `${name}=${encodeURIComponent(value)}; path=/; SameSite=Lax`;
};

/**
 * AuthProvider component that provides authentication context
 */
export const AuthProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  // State
  const [token, setToken] = useState<string>(localStorage.getItem(STORAGE_KEYS.ACCESS_TOKEN) || '');
  const [user, setUser] = useState<User | null>(getFromStorage<User | null>(STORAGE_KEYS.USER, null));
  const [schema, setSchema] = useState<string>(
    localStorage.getItem(STORAGE_KEYS.JWT_SCHEMA) ||
    localStorage.getItem(STORAGE_KEYS.SCHEMA_NAME) ||
    ''
  );
  const [tenant, setTenant] = useState<Tenant | null>(getFromStorage<Tenant | null>(STORAGE_KEYS.TENANT, null));
  const [isValidating, setIsValidating] = useState<boolean>(false);

  /**
   * Logout function - clears all auth data
   */
  const logout = useCallback(() => {
    // Clear state
    setToken('');
    setUser(null);
    setSchema('');
    setTenant(null);

    // Clear JWT tokens
    clearAllTokens();

    // Clear localStorage
    Object.values(STORAGE_KEYS).forEach(key => localStorage.removeItem(key));

    // Clear any token_expired or emergency_reset flags
    localStorage.removeItem('token_expired');
    localStorage.removeItem('emergency_reset');

    // Clear cookies
    clearCookie(STORAGE_KEYS.SCHEMA_NAME);
    clearCookie(STORAGE_KEYS.ACCESS_TOKEN);
    clearCookie(STORAGE_KEYS.REFRESH_TOKEN);

    console.log('Logged out, cleared all tokens, localStorage and cookies');

    // Redirect to login page
    window.location.href = '/login';
  }, []);

  /**
   * Function to validate the current token
   */
  const validateToken = async (): Promise<boolean> => {
    // Prevent excessive validations
    const lastValidationTime = localStorage.getItem(STORAGE_KEYS.LAST_VALIDATION);
    const currentTime = Date.now();

    // If we've validated in the last 2 seconds, don't validate again
    if (lastValidationTime && (currentTime - parseInt(lastValidationTime)) < 2000) {
      console.log('Skipping token validation - recently validated');
      return true;
    }

    setIsValidating(true);
    try {
      // Record validation time
      localStorage.setItem(STORAGE_KEYS.LAST_VALIDATION, currentTime.toString());

      // Get the current schema
      let currentSchema = schema || getCurrentSchema() || '';

      // If no schema, try to get it from localStorage
      if (!currentSchema) {
        currentSchema = localStorage.getItem(STORAGE_KEYS.SCHEMA_NAME) || '';
      }

      // If still no schema, try to get it from the tenant object
      if (!currentSchema) {
        try {
          const tenantData = getFromStorage<Tenant | null>(STORAGE_KEYS.TENANT, null);
          if (tenantData?.schema_name) {
            currentSchema = tenantData.schema_name;
            console.log(`Using schema from tenant object for validation: ${currentSchema}`);
          }
        } catch (e) {
          console.error('Error getting schema from tenant for validation:', e);
        }
      }

      if (!currentSchema) {
        console.warn('No schema name found for token validation');
        return false;
      }

      console.log(`Validating token with schema: ${currentSchema}`);

      // Try to validate the JWT token
      try {
        const isValid = await validateJWTToken(currentSchema);
        if (isValid) {
          console.log('JWT token is valid');

          // Store the schema in localStorage to ensure consistency
          localStorage.setItem(STORAGE_KEYS.JWT_SCHEMA, currentSchema);
          localStorage.setItem(STORAGE_KEYS.SCHEMA_NAME, currentSchema);

          // Update state
          setSchema(currentSchema);

          return true;
        }
      } catch (validationError) {
        console.error('Error validating JWT token:', validationError);

        // Try with schema from tenant if there's a validation error
        try {
          const tenantData = getFromStorage<Tenant | null>(STORAGE_KEYS.TENANT, null);
          if (tenantData?.schema_name && tenantData.schema_name !== currentSchema) {
            console.log(`Trying validation with schema from tenant: ${tenantData.schema_name}`);
            const isValidWithTenant = await validateJWTToken(tenantData.schema_name);

            if (isValidWithTenant) {
              console.log('JWT token is valid with tenant schema');
              setSchema(tenantData.schema_name);
              saveToStorage(STORAGE_KEYS.JWT_SCHEMA, tenantData.schema_name);
              saveToStorage(STORAGE_KEYS.SCHEMA_NAME, tenantData.schema_name);
              return true;
            }
          }
        } catch (tenantError) {
          console.error('Error validating with tenant schema:', tenantError);
        }
      }

      // If validation fails, try to refresh the token
      console.log('JWT token is invalid, trying to refresh');
      return await refreshJWTToken();
    } catch (error) {
      console.error('Error in token validation process:', error);
      return false;
    } finally {
      setIsValidating(false);
    }
  };
  /**
   * JWT token refresh function
   */
  const refreshJWTToken = async (): Promise<boolean> => {
    try {
      console.log('Refreshing JWT token');

      // Prevent multiple refreshes in a short period
      const lastRefreshTime = localStorage.getItem(STORAGE_KEYS.LAST_REFRESH);
      const currentTime = Date.now();

      // If we've refreshed in the last 2 seconds, don't refresh again
      if (lastRefreshTime && (currentTime - parseInt(lastRefreshTime)) < 2000) {
        console.log('Skipping token refresh - recently refreshed');
        return true;
      }

      // Record refresh time
      localStorage.setItem(STORAGE_KEYS.LAST_REFRESH, currentTime.toString());

      // Get the current schema
      let currentSchema = schema || getCurrentSchema() || '';

      // If no schema, try to get it from localStorage
      if (!currentSchema) {
        currentSchema = localStorage.getItem(STORAGE_KEYS.SCHEMA_NAME) || '';
      }

      // If still no schema, try to get it from the tenant object
      if (!currentSchema) {
        try {
          const tenantData = getFromStorage<Tenant | null>(STORAGE_KEYS.TENANT, null);
          if (tenantData?.schema_name) {
            currentSchema = tenantData.schema_name;
            console.log(`Using schema from tenant object: ${currentSchema}`);
          }
        } catch (e) {
          console.error('Error getting schema from tenant:', e);
        }
      }

      if (!currentSchema) {
        console.error('No schema available for token refresh');
        return false;
      }

      console.log(`Attempting to refresh token with schema: ${currentSchema}`);

      try {
        // Call the JWT token refresh function
        const response = await refreshJWTTokens(currentSchema);

        if (response && response.access_token) {
          console.log('JWT token refresh successful');

          // Store the schema in localStorage to ensure consistency
          localStorage.setItem(STORAGE_KEYS.JWT_SCHEMA, currentSchema);
          localStorage.setItem(STORAGE_KEYS.SCHEMA_NAME, currentSchema);

          // Update state
          setSchema(currentSchema);
          setToken(response.access_token);

          return true;
        }
      } catch (error) {
        const refreshError = error as Error;
        console.error('Error in refreshJWTTokens:', refreshError);

        // Try with schema from tenant if there's a schema-related error
        if (refreshError.message?.includes('schema') || refreshError.message?.includes('tenant')) {
          try {
            const tenantData = getFromStorage<Tenant | null>(STORAGE_KEYS.TENANT, null);
            if (tenantData?.schema_name && tenantData.schema_name !== currentSchema) {
              console.log(`Trying refresh with schema from tenant: ${tenantData.schema_name}`);
              const altResponse = await refreshJWTTokens(tenantData.schema_name);

              if (altResponse?.access_token) {
                console.log('JWT token refresh successful with tenant schema');
                setSchema(tenantData.schema_name);
                saveToStorage(STORAGE_KEYS.JWT_SCHEMA, tenantData.schema_name);
                saveToStorage(STORAGE_KEYS.SCHEMA_NAME, tenantData.schema_name);
                setToken(altResponse.access_token);
                return true;
              }
            }
          } catch (tenantError) {
            console.error('Error parsing tenant for schema fallback:', tenantError);
          }

          // Try all schemas in the token store as a last resort
          try {
            const tokenStore = getFromStorage<Record<string, {refresh_token?: string}>>('jwtTokenStore', {});
            if (tokenStore) {
              for (const [otherSchema, tokens] of Object.entries(tokenStore)) {
                if (otherSchema !== currentSchema && tokens && tokens.refresh_token) {
                  console.log(`Trying refresh with alternative schema from token store: ${otherSchema}`);
                  try {
                    const altResponse = await refreshJWTTokens(otherSchema);
                    if (altResponse?.access_token) {
                      console.log(`Successfully refreshed token using alternative schema: ${otherSchema}`);
                      setSchema(otherSchema);
                      saveToStorage(STORAGE_KEYS.JWT_SCHEMA, otherSchema);
                      saveToStorage(STORAGE_KEYS.SCHEMA_NAME, otherSchema);
                      setToken(altResponse.access_token);
                      return true;
                    }
                  } catch (altError) {
                    console.error(`Error refreshing with alternative schema ${otherSchema}:`, altError);
                  }
                }
              }
            }
          } catch (tokenStoreError) {
            console.error('Error accessing token store:', tokenStoreError);
          }
        }
      }

      console.warn('JWT token refresh failed after all attempts');
      return false;
    } catch (error) {
      console.error('JWT token refresh error:', error);
      return false;
    }
  };

  /**
   * JWT login function
   */
  const loginWithJWTFunction = async (email: string, password: string, schemaName?: string): Promise<boolean> => {
    try {
      console.log(`Logging in with JWT for email: ${email}`);

      // Use the schema from the parameter or the current schema
      const currentSchema = schemaName || getCurrentSchema() || '';

      // Call the JWT login function
      const response = await apiLoginWithJWT(email, password, currentSchema);

      if (response?.access_token) {
        console.log('JWT login successful');

        // Update state with user data
        if (response.user) {
          // Ensure the user has a role property
          if (!response.user.role) {
            const storedRole = localStorage.getItem(STORAGE_KEYS.USER_ROLE);
            response.user.role = storedRole || 'CENTER_STAFF'; // Default role as last resort
          }
          setUser(response.user);
        }

        // Update tenant and schema
        if (response.tenant) {
          setTenant(response.tenant);
          setSchema(response.tenant.schema_name);
        } else if (currentSchema) {
          setSchema(currentSchema);
        }

        // Set the token
        setToken(response.access_token);

        // Import and use tokenManager to synchronize cookies
        try {
          const { tokenManager } = await import('../services/tokenManager');
          console.log('Synchronizing cookies after login');
          tokenManager.synchronizeCookies();
        } catch (syncError) {
          console.error('Error synchronizing cookies after login:', syncError);
        }

        // Check if cookies were set properly
        const cookies = document.cookie;
        console.log('Cookies after login:', cookies);

        // Check for refresh token cookie
        if (!cookies.includes('jwt_refresh_token')) {
          console.warn('Refresh token cookie not found after login. Setting manually...');

          // Set refresh token cookie manually if not set by the server
          if (response.refresh_token) {
            // Set cookie with a long expiration (30 days)
            const expirationDate = new Date();
            expirationDate.setDate(expirationDate.getDate() + 30);

            document.cookie = `jwt_refresh_token=${encodeURIComponent(response.refresh_token)}; path=/; expires=${expirationDate.toUTCString()}; SameSite=Lax`;
            console.log('Manually set refresh token cookie with 30-day expiration');

            // Also ensure it's stored in localStorage as a fallback
            if (response.tenant?.schema_name) {
              localStorage.setItem(`jwt_refresh_token_${response.tenant.schema_name}`, response.refresh_token);
              console.log(`Stored refresh token in localStorage for schema ${response.tenant.schema_name}`);
            }

            // Store in the generic key as well
            localStorage.setItem('jwt_refresh_token', response.refresh_token);
          }
        }

        return true;
      }

      return false;
    } catch (error) {
      console.error('JWT login error:', error);
      return false;
    }
  };

  /**
   * Get authentication headers
   */
  const getAuthHeaders = useCallback((): HeadersInit => {
    const currentSchema = schema || getCurrentSchema() || '';
    return getTokenAuthHeaders(currentSchema);
  }, [schema]);

  // Initialize token refresh on mount
  useEffect(() => {
    if (token) {
      console.log('Initializing token refresh mechanism on mount');
      initializeTokenRefresh();
    }
  }, [token]);

  // Save user data to storage when it changes
  useEffect(() => {
    if (user) {
      saveToStorage(STORAGE_KEYS.USER, user);
      if (user.role) {
        saveToStorage(STORAGE_KEYS.USER_ROLE, user.role);
      }
    } else {
      localStorage.removeItem(STORAGE_KEYS.USER);
      localStorage.removeItem(STORAGE_KEYS.USER_ROLE);
    }
  }, [user]);

  // Save schema to storage when it changes
  useEffect(() => {
    if (schema) {
      saveToStorage(STORAGE_KEYS.JWT_SCHEMA, schema);
      saveToStorage(STORAGE_KEYS.SCHEMA_NAME, schema);
      setCookie(STORAGE_KEYS.SCHEMA_NAME, schema);
    } else {
      localStorage.removeItem(STORAGE_KEYS.JWT_SCHEMA);
      localStorage.removeItem(STORAGE_KEYS.SCHEMA_NAME);
      clearCookie(STORAGE_KEYS.SCHEMA_NAME);
    }
  }, [schema]);

  // Save tenant to storage when it changes
  useEffect(() => {
    if (tenant) {
      saveToStorage(STORAGE_KEYS.TENANT, tenant);
    } else {
      localStorage.removeItem(STORAGE_KEYS.TENANT);
    }
  }, [tenant]);

  return (
    <AuthContext.Provider
      value={{
        token,
        user,
        schema,
        tenant,
        setToken,
        setUser,
        setSchema,
        isAuthenticated: !!token,
        isValidating,
        validateToken,
        logout,
        loginWithJWT: loginWithJWTFunction,
        refreshJWTToken,
        getAuthHeaders
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};

/**
 * Hook to use the auth context
 */
export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
