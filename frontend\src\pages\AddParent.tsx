import React, { useState } from 'react';
import { usePara<PERSON>, useNavigate } from 'react-router-dom';
import {
  Box,
  Container,
  Typography,
  TextField,
  Button,
  Paper,
  Grid,
  FormControlLabel,
  Checkbox,
  Alert,
  CircularProgress,
  FormControl,
  InputLabel,
  Select,
  MenuItem
} from '@mui/material';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import SaveIcon from '@mui/icons-material/Save';
import ElderlyIcon from '@mui/icons-material/Elderly';
import PageBanner from '../components/PageBanner';

const AddParent: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  
  // State for form data
  const [formData, setFormData] = useState({
    relationship_type: '',
    first_name: '',
    middle_name: '',
    last_name: '',
    is_resident: false
  });
  
  // State for loading and error
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState(false);
  
  // Get the current tenant and authentication info from localStorage
  const tenantString = localStorage.getItem('tenant');
  const tenant = tenantString ? JSON.parse(tenantString) : null;
  const token = localStorage.getItem('token');
  const schemaName = localStorage.getItem('schema_name');
  
  // Handle form input changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | { name?: string; value: unknown }>) => {
    const { name, value, checked } = e.target as HTMLInputElement;
    if (name) {
      setFormData(prev => ({
        ...prev,
        [name]: e.target.type === 'checkbox' ? checked : value
      }));
    }
  };
  
  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');
    
    try {
      // Use the tenant-specific API endpoint
      let schema = schemaName || tenant?.schema_name || '';
      const encodedSchema = encodeURIComponent(schema);
      const url = `http://localhost:8000/api/tenant/${encodedSchema}/citizens/${id}/parent/`;
      
      // Use the hardcoded token for development purposes
      const hardcodedToken = '01aa7be65fbda335a0b29edd56c967ad6112fa6b';
      
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Authorization': `Token ${hardcodedToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(formData),
        credentials: 'include'
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || 'Failed to add parent');
      }
      
      setSuccess(true);
      setTimeout(() => {
        navigate(`/citizens/${id}`);
      }, 2000);
    } catch (error: any) {
      console.error('Error adding parent:', error);
      setError(error.message || 'Failed to add parent');
    } finally {
      setLoading(false);
    }
  };
  
  // Handle cancel
  const handleCancel = () => {
    navigate(`/citizens/${id}`);
  };
  
  return (
    <Box sx={{ py: 4 }}>
      {/* Banner */}
      <PageBanner
        title="Add Parent Information"
        subtitle="Add parent details for the citizen"
        icon={<ElderlyIcon sx={{ fontSize: 50, color: 'white' }} />}
      />
      
      <Container maxWidth="md">
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            mb: 4,
            mt: 1
          }}
        >
          <Button
            startIcon={<ArrowBackIcon />}
            onClick={handleCancel}
            variant="outlined"
            sx={{ borderRadius: 2 }}
          >
            Back to Citizen
          </Button>
        </Box>
        
        {/* Error message */}
        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}
        
        {/* Success message */}
        {success && (
          <Alert severity="success" sx={{ mb: 3 }}>
            Parent information added successfully! Redirecting...
          </Alert>
        )}
        
        <Paper
          component="form"
          onSubmit={handleSubmit}
          sx={{
            p: 4,
            borderRadius: 3,
            boxShadow: '0 8px 24px rgba(0,0,0,0.12)',
            mb: 4
          }}
        >
          <Typography variant="h5" sx={{ mb: 3, fontWeight: 600 }}>
            Parent Information
          </Typography>
          
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth required>
                <InputLabel id="relationship-type-label">Relationship Type</InputLabel>
                <Select
                  labelId="relationship-type-label"
                  name="relationship_type"
                  value={formData.relationship_type}
                  onChange={handleChange}
                  label="Relationship Type"
                >
                  <MenuItem value="Father">Father</MenuItem>
                  <MenuItem value="Mother">Mother</MenuItem>
                  <MenuItem value="Guardian">Guardian</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            
            <Grid item xs={12} md={6}>
              <FormControlLabel
                control={
                  <Checkbox
                    name="is_resident"
                    checked={formData.is_resident}
                    onChange={handleChange}
                    color="primary"
                  />
                }
                label="Is a resident of this Kebele"
              />
            </Grid>
            
            <Grid item xs={12} md={4}>
              <TextField
                label="First Name"
                name="first_name"
                value={formData.first_name}
                onChange={handleChange}
                fullWidth
                required
                variant="outlined"
              />
            </Grid>
            
            <Grid item xs={12} md={4}>
              <TextField
                label="Middle Name"
                name="middle_name"
                value={formData.middle_name}
                onChange={handleChange}
                fullWidth
                variant="outlined"
              />
            </Grid>
            
            <Grid item xs={12} md={4}>
              <TextField
                label="Last Name"
                name="last_name"
                value={formData.last_name}
                onChange={handleChange}
                fullWidth
                required
                variant="outlined"
              />
            </Grid>
          </Grid>
          
          <Box sx={{ mt: 4, display: 'flex', justifyContent: 'flex-end', gap: 2 }}>
            <Button
              variant="outlined"
              onClick={handleCancel}
              sx={{ borderRadius: 2, px: 3, py: 1.2 }}
            >
              Cancel
            </Button>
            
            <Button
              type="submit"
              variant="contained"
              color="primary"
              startIcon={loading ? <CircularProgress size={20} color="inherit" /> : <SaveIcon />}
              disabled={loading}
              sx={{ borderRadius: 2, px: 3, py: 1.2 }}
            >
              {loading ? 'Saving...' : 'Save Parent Information'}
            </Button>
          </Box>
        </Paper>
      </Container>
    </Box>
  );
};

export default AddParent;
