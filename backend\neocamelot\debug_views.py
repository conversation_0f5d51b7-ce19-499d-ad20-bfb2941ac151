from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt

@csrf_exempt
def debug_cors(request):
    """
    Debug view to check CORS headers.
    """
    # Get all request headers
    headers = {key: value for key, value in request.headers.items()}

    # Create a response with the headers
    response = JsonResponse({
        'method': request.method,
        'path': request.path,
        'headers': headers,
        'cookies': request.COOKIES,
    })

    # Add CORS headers
    response["Access-Control-Allow-Origin"] = "http://localhost:5173"
    response["Access-Control-Allow-Methods"] = "GET, POST, PUT, DELETE, OPTIONS"
    response["Access-Control-Allow-Headers"] = "Content-Type, Authorization, X-Requested-With, X-Schema-Name, X-CSRFToken"
    response["Access-Control-Allow-Credentials"] = "true"

    return response

@csrf_exempt
def health_check(request):
    """
    Simple health check endpoint.
    """
    response = JsonResponse({
        'status': 'ok',
        'message': 'API is working'
    })

    # Add CORS headers
    response["Access-Control-Allow-Origin"] = "*"
    response["Access-Control-Allow-Methods"] = "GET, POST, PUT, DELETE, OPTIONS"
    response["Access-Control-Allow-Headers"] = "Content-Type, Authorization, X-Requested-With, X-Schema-Name, X-CSRFToken"

    return response