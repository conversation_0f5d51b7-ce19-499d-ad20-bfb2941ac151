from django.db import migrations

def create_initial_ketenas(apps, schema_editor):
    """
    Create initial data for Ketena model.
    """
    Ketena = apps.get_model('common', 'Ketena')
    
    # Create ketenas for kebele 14
    kebele_14_ketenas = [
        {'name': 'Ketena 01', 'code': '01', 'kebele_id': '14'},
        {'name': 'Ketena 02', 'code': '02', 'kebele_id': '14'},
        {'name': 'Ketena 03', 'code': '03', 'kebele_id': '14'},
        {'name': 'Ketena 04', 'code': '04', 'kebele_id': '14'},
        {'name': 'Ketena 05', 'code': '05', 'kebele_id': '14'}
    ]
    for ketena_data in kebele_14_ketenas:
        Ketena.objects.create(
            name=ketena_data['name'],
            code=ketena_data['code'],
            kebele_id=ketena_data['kebele_id']
        )
    
    # Create ketenas for kebele 15
    kebele_15_ketenas = [
        {'name': 'Ketena 01', 'code': '01', 'kebele_id': '15'},
        {'name': 'Ketena 02', 'code': '02', 'kebele_id': '15'},
        {'name': 'Ketena 03', 'code': '03', 'kebele_id': '15'},
        {'name': 'Ketena 04', 'code': '04', 'kebele_id': '15'},
        {'name': 'Ketena 05', 'code': '05', 'kebele_id': '15'}
    ]
    for ketena_data in kebele_15_ketenas:
        Ketena.objects.create(
            name=ketena_data['name'],
            code=ketena_data['code'],
            kebele_id=ketena_data['kebele_id']
        )

class Migration(migrations.Migration):
    dependencies = [
        ('common', '0005_ketena'),
    ]

    operations = [
        migrations.RunPython(create_initial_ketenas),
    ]
