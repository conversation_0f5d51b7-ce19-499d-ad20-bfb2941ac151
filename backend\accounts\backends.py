"""
Custom authentication backends for the NeoCamelot system.
"""

from django.contrib.auth import get_user_model
from django.contrib.auth.backends import ModelBackend
from django.db.models import Q
import logging

logger = logging.getLogger(__name__)

User = get_user_model()

class EmailOrUsernameModelBackend(ModelBackend):
    """
    Authentication backend that allows login with either email or username.

    This backend is used to support domain-based tenant detection, where users
    can log in with either their email address or a domain-specific username.
    """

    def authenticate(self, request, username=None, password=None, **kwargs):
        """
        Authenticate a user using either email or username.

        Args:
            request: The HTTP request
            username: The username or email provided by the user
            password: The password provided by the user

        Returns:
            User: The authenticated user if credentials are valid, None otherwise
        """
        try:
            # Log authentication attempt
            logger.info(f"Authentication attempt with username/email: {username}")

            # First try to find the user by exact match (either email or username)
            try:
                # Check if the username contains an @ symbol
                if '@' in username:
                    # Try to authenticate with email first
                    try:
                        user = User.objects.get(email=username)
                        logger.info(f"Found user by email: {username}")
                    except User.DoesNotExist:
                        # If not found by email, try with domain-specific username
                        user = User.objects.get(username=username)
                        logger.info(f"Found user by domain-specific username: {username}")
                else:
                    # Try to authenticate with username
                    user = User.objects.get(username=username)
                    logger.info(f"Found user by username: {username}")

                # Check password
                if user.check_password(password):
                    logger.info(f"Password check successful for user: {username}")
                    return user
                else:
                    logger.warning(f"Password check failed for user: {username}")
                    return None

            except User.DoesNotExist:
                # If user not found by exact match, try more complex matching
                logger.info(f"User not found by exact match, trying more complex matching")

                # If username contains @, it could be a domain-specific username or email
                if '@' in username:
                    username_part, domain_part = username.split('@', 1)

                    # Try to find a user with a username that ends with this domain
                    # This handles cases where the user enters "<EMAIL>" but the username is "<EMAIL>"
                    domain_users = User.objects.filter(username__endswith=f"@{domain_part}")

                    for domain_user in domain_users:
                        if domain_user.check_password(password):
                            logger.info(f"Found user by domain match: {domain_user.username}")
                            return domain_user

                    # If no domain match, try to find by email
                    try:
                        email_user = User.objects.get(email=username)
                        if email_user.check_password(password):
                            logger.info(f"Found user by email after domain search: {email_user.email}")
                            return email_user
                    except User.DoesNotExist:
                        pass

                # No user found with any matching criteria
                logger.warning(f"Failed login attempt for {username}: User not found with any matching criteria")
                return None

        except Exception as e:
            # Log any other errors
            logger.error(f"Error during authentication for {username}: {str(e)}")
            return None
