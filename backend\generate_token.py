import os
import django
import sys

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'neocamelot.settings')
django.setup()

from django.db import connection
from django.contrib.auth import get_user_model
User = get_user_model()
from rest_framework.authtoken.models import Token

# Get the email from command line argument
if len(sys.argv) > 1:
    email = sys.argv[1]
else:
    email = '<EMAIL>'  # Default to admin email

# Set to public schema
connection.set_schema_to_public()

try:
    # Get the user
    user = User.objects.get(email=email)
    print(f"Found user: {user.email} (ID: {user.id})")

    # Get or create token
    token, created = Token.objects.get_or_create(user=user)
    if created:
        print(f"Created new token: {token.key}")
    else:
        print(f"Found existing token: {token.key}")

    # Print token for use in API calls
    print(f"\nUse this token in your API calls:")
    print(f"Token {token.key}")

except User.DoesNotExist:
    print(f"User with email {email} not found")
except Exception as e:
    print(f"Error: {str(e)}")
