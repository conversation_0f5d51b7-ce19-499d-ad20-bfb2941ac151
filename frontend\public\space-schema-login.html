<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Login with Space in Schema Name</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
      line-height: 1.6;
    }
    h1, h2 {
      color: #3f51b5;
    }
    .card {
      border: 1px solid #ddd;
      border-radius: 8px;
      padding: 20px;
      margin-bottom: 20px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    button {
      background-color: #3f51b5;
      color: white;
      border: none;
      padding: 10px 20px;
      border-radius: 4px;
      cursor: pointer;
      font-size: 16px;
      margin-right: 10px;
      margin-bottom: 10px;
    }
    button:hover {
      background-color: #303f9f;
    }
    pre {
      background-color: #f5f5f5;
      padding: 10px;
      border-radius: 4px;
      overflow-x: auto;
      white-space: pre-wrap;
      word-break: break-all;
    }
    .success { color: green; }
    .error { color: red; }
    .warning { color: orange; }
    input[type="text"],
    input[type="password"] {
      width: 100%;
      padding: 10px;
      margin: 5px 0 15px;
      border: 1px solid #ddd;
      border-radius: 4px;
      box-sizing: border-box;
    }
    label {
      font-weight: bold;
    }
    .form-group {
      margin-bottom: 15px;
    }
    .note {
      background-color: #fff3cd;
      border-left: 4px solid #ffc107;
      padding: 10px;
      margin-bottom: 15px;
    }
  </style>
</head>
<body>
  <h1>Login with Space in Schema Name</h1>

  <div class="note">
    <p><strong>Important:</strong> This page is specifically designed to handle schema names with spaces (e.g., "kebele 14").</p>
    <p>The API URL will use underscores (kebele_14), but the schema name in headers and localStorage will use spaces (kebele 14).</p>
  </div>

  <div class="card">
    <h2>Login Form</h2>
    <p>Use this form to generate a new authentication token.</p>

    <div class="form-group">
      <label for="email">Email:</label>
      <input type="text" id="email" placeholder="Enter your email">
    </div>

    <div class="form-group">
      <label for="password">Password:</label>
      <input type="password" id="password" placeholder="Enter your password">
    </div>

    <div class="form-group">
      <label for="schema">Schema Name (with space):</label>
      <input type="text" id="schema" placeholder="e.g., kebele 14" value="">
      <p><small>Use the exact schema name with spaces as it appears in your database</small></p>
    </div>

    <button id="login-btn">Login</button>

    <div id="login-result" style="margin-top: 20px; display: none;">
      <h3>Login Result</h3>
      <pre id="login-response"></pre>
    </div>
  </div>

  <div class="card">
    <h2>Test API Request</h2>
    <p>After generating a new token, test the API request to verify it works.</p>
    <button id="test-api">Test API Request</button>
    <div id="api-result" style="margin-top: 20px; display: none;">
      <h3>API Request Result</h3>
      <pre id="api-response"></pre>
    </div>
  </div>

  <div class="card">
    <h2>Navigation</h2>
    <button onclick="window.location.href = '/'">Go to Home</button>
    <button onclick="window.location.href = '/citizens'">Go to Citizens</button>
  </div>

  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // Add event listeners
      document.getElementById('login-btn').addEventListener('click', handleLogin);
      document.getElementById('test-api').addEventListener('click', testApiRequest);
    });

    async function handleLogin() {
      const email = document.getElementById('email').value.trim();
      const password = document.getElementById('password').value;
      const schema = document.getElementById('schema').value.trim();

      const loginResultDiv = document.getElementById('login-result');
      const loginResponsePre = document.getElementById('login-response');

      if (!email || !password) {
        alert('Please enter both email and password');
        return;
      }

      if (!schema) {
        alert('Please enter a schema name');
        return;
      }

      // Show the result div
      loginResultDiv.style.display = 'block';
      loginResponsePre.textContent = 'Logging in...';

      try {
        // Clear any existing authentication data
        localStorage.removeItem('token');
        localStorage.removeItem('schema_name');
        localStorage.removeItem('tokenStore');

        // Set schema cookie - use the schema with spaces
        document.cookie = `schema_name=${encodeURIComponent(schema)}; path=/; SameSite=Lax`;

        // Create request headers
        const headers = new Headers({
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'X-Schema-Name': schema
        });

        // Make the login request
        const response = await fetch('/api/login/', {
          method: 'POST',
          headers: headers,
          body: JSON.stringify({
            email: email,
            password: password,
            schema_name: schema
          }),
          credentials: 'include'
        });

        // Get response details
        const status = response.status;
        const statusText = response.statusText;

        let responseData;
        try {
          responseData = await response.json();
        } catch (e) {
          responseData = await response.text();
        }

        // Display the response
        loginResponsePre.textContent = `Status: ${status} ${statusText}\n\nResponse:\n${typeof responseData === 'object' ? JSON.stringify(responseData, null, 2) : responseData}`;

        // If login was successful, store the token
        if (response.ok && responseData && responseData.token) {
          // Clean the token (remove any spaces)
          const cleanToken = responseData.token.replace(/\s+/g, '');

          // Store the token in localStorage
          localStorage.setItem('token', cleanToken);

          // Store the schema name WITH spaces
          localStorage.setItem('schema_name', schema);

          // Store user data if available
          if (responseData.user) {
            localStorage.setItem('user', JSON.stringify(responseData.user));
          }

          // Store tenant data if available
          if (responseData.tenant) {
            localStorage.setItem('tenant', JSON.stringify(responseData.tenant));
          }

          // Update the tokenStore
          try {
            const tokenStore = {};
            tokenStore[schema] = cleanToken;
            localStorage.setItem('tokenStore', JSON.stringify(tokenStore));
          } catch (e) {
            console.error('Error updating tokenStore:', e);
          }

          loginResponsePre.textContent += '\n\nToken stored successfully!';
        }
      } catch (error) {
        loginResponsePre.textContent = `Error: ${error.message}`;
      }
    }

    async function testApiRequest() {
      const schema = document.getElementById('schema').value.trim();
      const apiResultDiv = document.getElementById('api-result');
      const apiResponsePre = document.getElementById('api-response');

      // Show the result div
      apiResultDiv.style.display = 'block';
      apiResponsePre.textContent = 'Testing API request...';

      try {
        // Get the token from localStorage
        const token = localStorage.getItem('token');
        if (!token) {
          apiResponsePre.textContent = 'No token found in localStorage. Please login first.';
          return;
        }

        // Create the URL - replace spaces with underscores for the URL
        const urlSchema = schema.replace(/\s+/g, '_');
        const url = `/api/tenant/${encodeURIComponent(urlSchema)}/citizens/?limit=1`;

        // Create headers - use the schema WITH spaces for headers
        const headers = new Headers({
          'Authorization': `Token ${token}`,
          'Content-Type': 'application/json',
          'X-Schema-Name': schema
        });

        // Set schema cookie - use the schema WITH spaces
        document.cookie = `schema_name=${encodeURIComponent(schema)}; path=/; SameSite=Lax`;

        // Make the request
        const response = await fetch(url, {
          method: 'GET',
          headers: headers,
          credentials: 'include'
        });

        // Get response details
        const status = response.status;
        const statusText = response.statusText;

        let responseData;
        try {
          responseData = await response.json();
        } catch (e) {
          responseData = await response.text();
        }

        // Display the response
        apiResponsePre.textContent = `URL: ${url}\n\nStatus: ${status} ${statusText}\n\nHeaders Sent:\n${JSON.stringify(Object.fromEntries(headers.entries()), null, 2)}\n\nResponse:\n${typeof responseData === 'object' ? JSON.stringify(responseData, null, 2) : responseData}`;

        // If successful, show a success message
        if (response.ok) {
          apiResponsePre.textContent += '\n\nAPI request successful!';
        }
      } catch (error) {
        apiResponsePre.textContent = `Error: ${error.message}`;
      }
    }
  </script>
</body>
</html>
