import React, { useState, useEffect } from 'react';
import {
  Box, Typography, Paper, Grid, Table, TableBody, TableCell,
  TableContainer, TableHead, TableRow, Button, Chip, CircularProgress
} from '@mui/material';
import PageBanner from '../components/PageBanner';
import { useNavigate } from 'react-router-dom';

interface IDCard {
  id: number;
  card_number: string;
  issue_date: string;
  expiry_date: string;
  status: string;
  kebele_approval_status?: string;
  kebele_pattern?: any;
  subcity_pattern?: any;
  citizen: {
    first_name: string;
    last_name: string;
    id_number: string;
  };
  source_schema?: string;
}

const PrintCards: React.FC = () => {
  const [idCards, setIdCards] = useState<IDCard[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const navigate = useNavigate();

  // Function to get status chip color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'APPROVED':
        return 'success';
      case 'PENDING':
        return 'warning';
      case 'PENDING_SUBCITY':
        return 'info';
      case 'PRINTED':
        return 'secondary';
      case 'ISSUED':
        return 'primary';
      case 'EXPIRED':
        return 'error';
      case 'REVOKED':
        return 'error';
      default:
        return 'default';
    }
  };

  useEffect(() => {
    fetchIDCards();
  }, []);

  const fetchIDCards = async () => {
    setLoading(true);
    setError(null);

    try {
      // Get token from localStorage
      const token = localStorage.getItem('token');

      if (!token) {
        throw new Error('No authentication token found. Please log in.');
      }

      console.log('Using token:', token);

      // Use the new API endpoint to fetch ID cards from child kebeles
      const response = await fetch('http://127.0.0.1:8000/api/child-kebele-idcards/', {
        headers: {
          'Authorization': `Token ${token}`,
          'Content-Type': 'application/json',
          'X-Schema-Name': 'subcity_zoble' // Add schema name header for the subcity tenant
        }
      });

      if (!response.ok) {
        throw new Error(`API error: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      console.log('Child kebele ID cards response:', data);

      // Set the ID cards
      setIdCards(data);
      console.log(`Total cards found: ${data.length}`);
    } catch (err) {
      console.error('Error fetching ID cards:', err);
      setError('Failed to fetch ID cards. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  const handleViewDetails = (id: number, sourceSchema: string) => {
    navigate(`/id-cards/${id}?schema=${encodeURIComponent(sourceSchema)}`);
  };

  const handleApprove = async (id: number, sourceSchema: string) => {
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('No authentication token found. Please log in.');
      }

      const encodedSchema = encodeURIComponent(sourceSchema);
      const response = await fetch(`http://127.0.0.1:8000/api/tenant/${encodedSchema}/idcards/${id}/subcity_approve/`, {
        method: 'POST',
        headers: {
          'Authorization': `Token ${token}`,
          'Content-Type': 'application/json',
          'X-Schema-Name': sourceSchema
        },
        body: JSON.stringify({ notes: 'Approved by subcity admin' })
      });

      if (response.ok) {
        // Update the card status in the UI
        setIdCards(prevCards =>
          prevCards.map(card =>
            card.id === id && card.source_schema === sourceSchema
              ? { ...card, status: 'APPROVED' }
              : card
          )
        );

        console.log(`ID card ${id} from ${sourceSchema} approved successfully`);

        // Refresh the ID cards list after a short delay
        setTimeout(() => {
          fetchIDCards();
        }, 1000);
      } else {
        console.error(`Error approving ID card ${id} from ${sourceSchema}:`, response.status, response.statusText);
      }
    } catch (error) {
      console.error('Error approving ID card:', error);
    }
  };

  return (
    <Box sx={{ width: '100%', mb: 4 }}>
      <PageBanner title="Print ID Cards" subtitle="Manage and print ID cards from child kebeles" />

      <Paper sx={{ p: 3, mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          ID Cards Pending Subcity Approval
        </Typography>
        <Typography variant="body2" color="text.secondary" paragraph>
          This page shows ID cards from child kebeles that have been approved by kebele leaders.
          You can view details, approve, and print ID cards from this page.
        </Typography>

        {loading ? (
          <Box display="flex" justifyContent="center" alignItems="center" minHeight="200px">
            <CircularProgress />
          </Box>
        ) : error ? (
          <Box sx={{ p: 2 }}>
            <Typography color="error">{error}</Typography>
          </Box>
        ) : idCards.length === 0 ? (
          <Typography>No ID cards found.</Typography>
        ) : (
          <TableContainer component={Paper}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Card Number</TableCell>
                  <TableCell>Citizen Name</TableCell>
                  <TableCell>ID Number</TableCell>
                  <TableCell>Issue Date</TableCell>
                  <TableCell>Expiry Date</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell>Kebele Approval</TableCell>
                  <TableCell>Source</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {idCards.map((card) => (
                  <TableRow key={`${card.source_schema}-${card.id}`}>
                    <TableCell>{card.card_number}</TableCell>
                    <TableCell>{`${card.citizen?.first_name} ${card.citizen?.last_name}`}</TableCell>
                    <TableCell>{card.citizen?.id_number}</TableCell>
                    <TableCell>{card.issue_date}</TableCell>
                    <TableCell>{card.expiry_date}</TableCell>
                    <TableCell>
                      <Chip
                        label={card.status}
                        color={getStatusColor(card.status) as any}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={card.kebele_approval_status}
                        color={card.kebele_approval_status === 'APPROVED' ? 'success' : 'warning' as any}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>{card.source_schema}</TableCell>
                    <TableCell>
                      <Box sx={{ display: 'flex', gap: 1 }}>
                        <Button
                          variant="outlined"
                          size="small"
                          onClick={() => handleViewDetails(card.id, card.source_schema || '')}
                        >
                          View
                        </Button>
                        {card.status !== 'APPROVED' && (
                          <Button
                            variant="contained"
                            color="primary"
                            size="small"
                            onClick={() => handleApprove(card.id, card.source_schema || '')}
                          >
                            Approve
                          </Button>
                        )}
                      </Box>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        )}
      </Paper>
    </Box>
  );
};

export default PrintCards;
