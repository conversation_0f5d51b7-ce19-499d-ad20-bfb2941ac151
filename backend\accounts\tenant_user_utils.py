"""
Tenant User Utilities

This module provides utility functions for working with users across multiple tenants.
"""

import logging
from django.contrib.auth import get_user_model
from django.db import connection
from django_tenants.utils import tenant_context, get_public_schema_name
from centers.models import Client, Domain

# Configure logging
logger = logging.getLogger(__name__)

User = get_user_model()

def find_user_in_all_tenants(email, include_public=True):
    """
    Find a user in all tenants by email.

    Args:
        email (str): The email address to search for
        include_public (bool): Whether to include the public schema in the search

    Returns:
        tuple: (user, tenant) if found, (None, None) otherwise
    """
    logger.info(f"Searching for user with email {email} across all tenants")

    # Save the current schema
    current_schema = connection.schema_name
    logger.info(f"Current schema: {current_schema}")

    try:
        # First check the public schema if requested
        if include_public and current_schema != get_public_schema_name():
            logger.info(f"Checking public schema for user {email}")
            connection.set_schema_to_public()
            try:
                user = User.objects.get(email=email)
                logger.info(f"Found user {email} in public schema")
                return user, None
            except User.DoesNotExist:
                logger.info(f"User {email} not found in public schema")
            except Exception as e:
                logger.error(f"Error searching for user in public schema: {str(e)}")

        # Get all active tenants
        tenants = Client.objects.filter(is_active=True)
        logger.info(f"Found {tenants.count()} active tenants to check")

        # Check each tenant
        for tenant in tenants:
            logger.info(f"Checking tenant {tenant.schema_name} for user {email}")
            try:
                with tenant_context(tenant):
                    try:
                        user = User.objects.get(email=email)
                        logger.info(f"Found user {email} in tenant {tenant.schema_name}")
                        return user, tenant
                    except User.DoesNotExist:
                        logger.info(f"User {email} not found in tenant {tenant.schema_name}")
                    except Exception as e:
                        logger.error(f"Error searching for user in tenant {tenant.schema_name}: {str(e)}")
            except Exception as e:
                logger.error(f"Error switching to tenant {tenant.schema_name}: {str(e)}")

        # User not found in any tenant
        logger.warning(f"User {email} not found in any tenant")
        return None, None
    finally:
        # Restore the original schema
        if connection.schema_name != current_schema:
            logger.info(f"Restoring original schema: {current_schema}")
            if current_schema == get_public_schema_name():
                connection.set_schema_to_public()
            else:
                try:
                    tenant = Client.objects.get(schema_name=current_schema)
                    connection.set_tenant(tenant)
                except Client.DoesNotExist:
                    logger.error(f"Could not restore original schema {current_schema}, schema does not exist")
                    connection.set_schema_to_public()
                except Exception as e:
                    logger.error(f"Error restoring original schema {current_schema}: {str(e)}")
                    connection.set_schema_to_public()

def find_tenant_by_email_domain(email):
    """
    Find a tenant by the domain part of an email address.

    Args:
        email (str): The email address to extract domain from

    Returns:
        Client: The tenant object if found, None otherwise
    """
    if not email or '@' not in email:
        logger.warning(f"Invalid email format: {email}")
        return None

    # Extract domain from email
    domain = email.split('@')[1].lower()
    logger.info(f"Extracted domain from email: {domain}")

    # Save the current schema
    current_schema = connection.schema_name
    logger.info(f"Current schema: {current_schema}")

    try:
        # Switch to public schema to search for domains
        connection.set_schema_to_public()

        # First try exact match with custom_domain field in Client model
        try:
            tenant = Client.objects.filter(custom_domain=domain).first()
            if tenant:
                logger.info(f"Found tenant {tenant.schema_name} with exact custom domain match: {domain}")
                return tenant
        except Exception as e:
            logger.error(f"Error searching for tenant by custom domain: {str(e)}")

        # Then try match with email_domain field in Client model
        try:
            tenant = Client.objects.filter(email_domain=domain).first()
            if tenant:
                logger.info(f"Found tenant {tenant.schema_name} with email domain match: {domain}")
                return tenant
        except Exception as e:
            logger.error(f"Error searching for tenant by email domain: {str(e)}")

        # Then try to find a Domain object with this domain
        try:
            domain_obj = Domain.objects.filter(domain__icontains=domain).first()
            if domain_obj:
                logger.info(f"Found tenant {domain_obj.tenant.schema_name} with domain: {domain_obj.domain}")
                return domain_obj.tenant
        except Exception as e:
            logger.error(f"Error searching for tenant by domain: {str(e)}")

        logger.warning(f"No tenant found for domain: {domain}")
        return None
    finally:
        # Restore the original schema
        if connection.schema_name != current_schema:
            logger.info(f"Restoring original schema: {current_schema}")
            if current_schema == get_public_schema_name():
                connection.set_schema_to_public()
            else:
                try:
                    tenant = Client.objects.get(schema_name=current_schema)
                    connection.set_tenant(tenant)
                except Client.DoesNotExist:
                    logger.error(f"Could not restore original schema {current_schema}, schema does not exist")
                    connection.set_schema_to_public()
                except Exception as e:
                    logger.error(f"Error restoring original schema {current_schema}: {str(e)}")
                    connection.set_schema_to_public()

def authenticate_user_in_tenant(request, email, password, tenant):
    """
    Authenticate a user in a specific tenant.

    Args:
        request: The request object
        email (str): The user's email
        password (str): The user's password
        tenant: The tenant object

    Returns:
        User: The authenticated user or None
    """
    from django.contrib.auth import authenticate

    logger.info(f"Authenticating user {email} in tenant {tenant.schema_name}")

    # Save the current schema
    current_schema = connection.schema_name
    logger.info(f"Current schema: {current_schema}")

    try:
        # Switch to the tenant context
        with tenant_context(tenant):
            # Authenticate the user
            user = authenticate(request, username=email, password=password)
            if user:
                logger.info(f"User {email} authenticated successfully in tenant {tenant.schema_name}")
                return user
            else:
                logger.warning(f"Authentication failed for user {email} in tenant {tenant.schema_name}")
                return None
    except Exception as e:
        logger.error(f"Error authenticating user {email} in tenant {tenant.schema_name}: {str(e)}")
        return None
    finally:
        # Restore the original schema
        if connection.schema_name != current_schema:
            logger.info(f"Restoring original schema: {current_schema}")
            if current_schema == get_public_schema_name():
                connection.set_schema_to_public()
            else:
                try:
                    tenant = Client.objects.get(schema_name=current_schema)
                    connection.set_tenant(tenant)
                except Client.DoesNotExist:
                    logger.error(f"Could not restore original schema {current_schema}, schema does not exist")
                    connection.set_schema_to_public()
                except Exception as e:
                    logger.error(f"Error restoring original schema {current_schema}: {str(e)}")
                    connection.set_schema_to_public()
