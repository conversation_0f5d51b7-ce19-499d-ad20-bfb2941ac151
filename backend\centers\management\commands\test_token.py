from django.core.management.base import BaseCommand
from django.db import connection
from django_tenants.utils import tenant_context, get_public_schema_name
from centers.models import Client
from django.contrib.auth import get_user_model
User = get_user_model()
from rest_framework.authtoken.models import Token

class Command(BaseCommand):
    help = 'Test a token in a specific tenant'

    def add_arguments(self, parser):
        parser.add_argument('--token', type=str, default='9425ec3171e23a01903152c33f7a212106916a4b', help='Token to test')
        parser.add_argument('--schema', type=str, default='subcity_zoble', help='Schema name to test')

    def handle(self, *args, **options):
        token_key = options['token']
        schema_name = options['schema']

        self.stdout.write(f"Testing token: {token_key} in schema: {schema_name}")

        try:
            # Get the tenant by schema name
            tenant = Client.objects.get(schema_name=schema_name)
            self.stdout.write(f"Found tenant: {tenant.name} (ID: {tenant.id}, Type: {tenant.schema_type})")
            
            # Set the tenant for this request
            connection.set_tenant(tenant)
            self.stdout.write(f"Set tenant context to: {connection.schema_name}")
            
            # Use tenant_context to ensure we're querying the right database
            with tenant_context(tenant):
                # Try to find the token in the tenant's database
                try:
                    token = Token.objects.get(key=token_key)
                    user = token.user
                    self.stdout.write(f"Found token in tenant {tenant.schema_name} for user {user.email} (ID: {user.id})")
                    self.stdout.write(f"User is_active: {user.is_active}")
                    self.stdout.write(f"User is_staff: {user.is_staff}")
                    self.stdout.write(f"User is_superuser: {user.is_superuser}")
                    self.stdout.write(f"User role: {user.role}")
                except Token.DoesNotExist:
                    self.stdout.write(f"Token not found in tenant {tenant.schema_name}")
                    
                    # If not found, try the public schema
                    self.stdout.write(f"Trying public schema...")
                    connection.set_schema_to_public()
                    try:
                        token = Token.objects.get(key=token_key)
                        user = token.user
                        self.stdout.write(f"Found token in public schema for user {user.email} (ID: {user.id})")
                        self.stdout.write(f"User is_active: {user.is_active}")
                        self.stdout.write(f"User is_staff: {user.is_staff}")
                        self.stdout.write(f"User is_superuser: {user.is_superuser}")
                        self.stdout.write(f"User role: {user.role}")
                        
                        # Now switch back to the tenant schema
                        connection.set_tenant(tenant)
                        self.stdout.write(f"Set tenant context back to: {connection.schema_name}")
                        
                        # Try to find the user in the tenant's database
                        try:
                            user = User.objects.get(email=token.user.email)
                            self.stdout.write(f"Found user in tenant {tenant.schema_name}: {user.email} (ID: {user.id})")
                        except User.DoesNotExist:
                            self.stdout.write(f"User not found in tenant {tenant.schema_name}")
                    except Token.DoesNotExist:
                        self.stdout.write(f"Token not found in public schema")
        except Client.DoesNotExist:
            self.stdout.write(f"Tenant with schema name '{schema_name}' not found.")
