"""
Simple view to test JWT authentication.
"""
from rest_framework.decorators import api_view, authentication_classes, permission_classes
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from rest_framework import status
from django.db import connection
from django_tenants.utils import tenant_context
from centers.models import Client
from django.contrib.auth import get_user_model
from accounts.jwt_utils import validate_jwt_token

User = get_user_model()

@api_view(['GET'])
@authentication_classes([])  # No authentication classes - we'll handle it manually
@permission_classes([AllowAny])  # We'll handle authentication manually
def test_token_view(request, schema_name=None):
    """
    Test JWT authentication.
    """
    # Get the token from the request
    auth_header = request.headers.get('Authorization', '')
    print(f"\n\nAUTH HEADER: {auth_header}\n\n")

    if not auth_header or not auth_header.startswith('Bearer '):
        print(f"\n\nINVALID AUTH HEADER: {auth_header}\n\n")
        return Response({"error": "Authentication credentials were not provided."}, status=status.HTTP_401_UNAUTHORIZED)

    token_key = auth_header.split(' ')[1]
    print(f"\n\nJWT token: {token_key[:20]}...\n\n")

    # Validate the JWT token
    payload = validate_jwt_token(token_key)

    if not payload:
        print(f"\n\nInvalid JWT token\n\n")
        return Response({"token_valid": False, "error": "Invalid JWT token"}, status=status.HTTP_401_UNAUTHORIZED)

    print(f"\n\nJWT token is valid\n\n")
    print(f"\n\nPayload: {payload}\n\n")

    # Get user ID and schema from payload
    user_id = payload.get('sub')
    schema_name = payload.get('schema')

    if not user_id or not schema_name:
        print(f"\n\nInvalid token payload - missing user ID or schema\n\n")
        return Response({"token_valid": False, "error": "Invalid token payload"}, status=status.HTTP_401_UNAUTHORIZED)

    print(f"\n\nUser ID: {user_id}\n\n")
    print(f"\n\nSchema: {schema_name}\n\n")

    # Get the tenant by schema name
    try:
        tenant = Client.objects.get(schema_name=schema_name)
        print(f"\n\nFound tenant: {tenant.name} (Schema: {tenant.schema_name})\n\n")

        # Set the tenant for this request
        connection.set_tenant(tenant)

        # Get the user
        with tenant_context(tenant):
            try:
                user = User.objects.get(id=user_id)
                print(f"\n\nFound user: {user.email} (ID: {user.id})\n\n")
                print(f"\n\nUser is_active: {user.is_active}\n\n")
                print(f"\n\nUser is_staff: {user.is_staff}\n\n")
                print(f"\n\nUser is_superuser: {user.is_superuser}\n\n")
                print(f"\n\nUser role: {getattr(user, 'role', 'N/A')}\n\n")

                return Response({
                    "token_valid": True,
                    "schema": schema_name,
                    "user": {
                        "id": user.id,
                        "email": user.email,
                        "is_active": user.is_active,
                        "is_staff": user.is_staff,
                        "is_superuser": user.is_superuser,
                        "role": getattr(user, 'role', 'N/A')
                    },
                    "token_type": payload.get('token_type', 'access'),
                    "exp": payload.get('exp')
                })
            except User.DoesNotExist:
                print(f"\n\nUser with ID {user_id} not found in tenant {schema_name}\n\n")
                return Response({"token_valid": False, "error": f"User with ID {user_id} not found in tenant {schema_name}"}, status=status.HTTP_404_NOT_FOUND)
    except Client.DoesNotExist:
        print(f"\n\nTenant with schema {schema_name} not found\n\n")
        return Response({"token_valid": False, "error": f"Tenant with schema {schema_name} not found"}, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        print(f"\n\nError: {str(e)}\n\n")
        return Response({"token_valid": False, "error": f"Error: {str(e)}"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
