import React, { useEffect } from 'react';
import { tokenManager } from '../services/tokenManager';

/**
 * CookieSynchronizer Component
 * 
 * This component runs on every render to ensure cookies are properly synchronized.
 * It checks if cookies are missing and restores them from localStorage if needed.
 * It also checks if localStorage tokens are missing and restores them from cookies.
 */
const CookieSynchronizer: React.FC = () => {
  useEffect(() => {
    console.log('CookieSynchronizer: Running cookie synchronization');
    
    // Synchronize cookies with localStorage
    tokenManager.synchronizeCookies();
    
    // Set up an interval to check cookies periodically
    const intervalId = setInterval(() => {
      tokenManager.synchronizeCookies();
    }, 30000); // Check every 30 seconds
    
    // Also synchronize before navigation
    const handleBeforeUnload = () => {
      tokenManager.synchronizeCookies();
    };
    
    window.addEventListener('beforeunload', handleBeforeUnload);
    
    // Clean up
    return () => {
      clearInterval(intervalId);
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, []);
  
  // This component doesn't render anything
  return null;
};

export default CookieSynchronizer;
