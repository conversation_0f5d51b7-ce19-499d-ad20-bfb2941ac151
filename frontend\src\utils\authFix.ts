/**
 * Authentication Fix Utility
 *
 * This module provides utilities to diagnose and fix authentication issues.
 */

import { ensureToken, ensureSchema } from './apiUtils';

/**
 * Fix authentication issues by ensuring token and schema are properly set
 * @param schema The schema name to use
 * @returns Object with success status and message
 */
export const fixAuthentication = (schema: string): { success: boolean; message: string } => {
  console.log('Fixing authentication for schema:', schema);

  // Check if token exists
  const token = localStorage.getItem('token');
  if (!token) {
    return {
      success: false,
      message: 'No authentication token found. Please log in again.'
    };
  }

  // Clean the token thoroughly - remove ALL spaces, not just leading/trailing
  const cleanToken = token.replace(/\s+/g, '');

  // Update the token in localStorage
  localStorage.setItem('token', cleanToken);
  console.log('fixAuthentication: Cleaned token and saved to localStorage:', cleanToken);

  // Ensure token is properly set
  const tokenSuccess = ensureToken();
  if (!tokenSuccess) {
    return {
      success: false,
      message: 'Failed to set authentication token. Please log in again.'
    };
  }

  // Ensure schema is properly set
  const schemaSuccess = ensureSchema(schema);
  if (!schemaSuccess) {
    return {
      success: false,
      message: 'Failed to set schema name. Please log in again.'
    };
  }

  // Set the token in the tokenStore for the schema
  try {
    // Get the tokenStore from localStorage
    const tokenStoreStr = localStorage.getItem('tokenStore');
    let tokenStore = tokenStoreStr ? JSON.parse(tokenStoreStr) : {};

    // Set the cleaned token for the schema
    tokenStore[schema] = cleanToken;

    // Save the tokenStore back to localStorage
    localStorage.setItem('tokenStore', JSON.stringify(tokenStore));

    console.log(`Token set for schema ${schema} in tokenStore`);
  } catch (error) {
    console.error('Error setting token in tokenStore:', error);
    return {
      success: false,
      message: 'Failed to set token in tokenStore. Please log in again.'
    };
  }

  // Set the tenant object if it doesn't exist
  try {
    const tenantStr = localStorage.getItem('tenant');
    if (!tenantStr) {
      // Create a basic tenant object
      const tenant = {
        schema_name: schema,
        name: schema.replace(/_/g, ' ')
      };

      // Save the tenant object to localStorage
      localStorage.setItem('tenant', JSON.stringify(tenant));

      console.log('Created basic tenant object:', tenant);
    } else {
      // Update the schema_name in the tenant object
      const tenant = JSON.parse(tenantStr);
      tenant.schema_name = schema;

      // Save the updated tenant object to localStorage
      localStorage.setItem('tenant', JSON.stringify(tenant));

      console.log('Updated schema_name in tenant object:', tenant);
    }
  } catch (error) {
    console.error('Error setting tenant object:', error);
    // This is not critical, so we'll continue
  }

  return {
    success: true,
    message: `Authentication fixed for schema ${schema}`
  };
};

/**
 * Diagnose authentication issues
 * @returns Object with diagnostic information
 */
export const diagnoseAuthentication = (): {
  token: boolean;
  schema: boolean;
  tenant: boolean;
  tokenStore: boolean;
  cookies: string;
  message: string;
} => {
  console.log('Diagnosing authentication issues...');

  // Check if token exists
  const token = localStorage.getItem('token');

  // Check if schema exists
  const schema = localStorage.getItem('schema_name');

  // Check if tenant exists
  const tenantStr = localStorage.getItem('tenant');
  let tenant = false;
  try {
    if (tenantStr) {
      const tenantObj = JSON.parse(tenantStr);
      tenant = !!tenantObj.schema_name;
    }
  } catch (error) {
    console.error('Error parsing tenant object:', error);
  }

  // Check if tokenStore exists
  const tokenStoreStr = localStorage.getItem('tokenStore');
  let tokenStore = false;
  try {
    if (tokenStoreStr) {
      const tokenStoreObj = JSON.parse(tokenStoreStr);
      tokenStore = Object.keys(tokenStoreObj).length > 0;
    }
  } catch (error) {
    console.error('Error parsing tokenStore:', error);
  }

  // Get all cookies
  const cookies = document.cookie;

  // Create diagnostic message
  let message = 'Authentication diagnosis:\n';
  message += `Token: ${token ? 'Present' : 'Missing'}\n`;
  message += `Schema: ${schema ? 'Present' : 'Missing'}\n`;
  message += `Tenant: ${tenant ? 'Valid' : 'Invalid or Missing'}\n`;
  message += `TokenStore: ${tokenStore ? 'Present' : 'Missing'}\n`;
  message += `Cookies: ${cookies || 'None'}\n`;

  return {
    token: !!token,
    schema: !!schema,
    tenant,
    tokenStore,
    cookies,
    message
  };
};

export default {
  fixAuthentication,
  diagnoseAuthentication
};
