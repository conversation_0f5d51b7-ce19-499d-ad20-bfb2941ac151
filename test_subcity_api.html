<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Subcity API Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            line-height: 1.6;
        }
        h1 {
            color: #333;
        }
        button {
            padding: 10px 15px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 10px 0;
        }
        button:hover {
            background-color: #45a049;
        }
        pre {
            background-color: #f5f5f5;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
        }
        .card {
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 15px;
            background-color: #f9f9f9;
        }
        .card h3 {
            margin-top: 0;
            color: #333;
        }
        .status {
            display: inline-block;
            padding: 5px 10px;
            border-radius: 3px;
            font-weight: bold;
        }
        .status-pending {
            background-color: #2196F3;
            color: white;
        }
        .status-approved {
            background-color: #4CAF50;
            color: white;
        }
        .actions {
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <h1>Subcity API Test</h1>
    
    <div>
        <h2>Fetch ID Cards from Subcity</h2>
        <button id="fetchCards">Fetch ID Cards</button>
        <div id="results"></div>
    </div>

    <script>
        // Hardcoded token and schema
        const token = '01aa7be65fbda335a0b29edd56c967ad6112fa6b';
        const schema = 'subcity_zoble';
        
        // Function to fetch ID cards from subcity
        async function fetchIDCards() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<p>Loading...</p>';
            
            try {
                // First try to fetch from child kebeles
                const response = await fetch(`http://localhost:8000/api/tenant/${schema}/idcards/`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Token ${token}`,
                        'Content-Type': 'application/json',
                        'X-Schema-Name': schema
                    }
                });
                
                if (!response.ok) {
                    throw new Error(`API error: ${response.status} ${response.statusText}`);
                }
                
                const data = await response.json();
                console.log('API Response:', data);
                
                let cards = [];
                if (Array.isArray(data)) {
                    cards = data;
                } else if (data.results && Array.isArray(data.results)) {
                    cards = data.results;
                }
                
                if (cards.length === 0) {
                    resultsDiv.innerHTML = '<p>No ID cards found.</p>';
                    return;
                }
                
                // Display the cards
                resultsDiv.innerHTML = `<h3>Found ${cards.length} ID cards</h3>`;
                
                cards.forEach(card => {
                    const cardDiv = document.createElement('div');
                    cardDiv.className = 'card';
                    
                    const statusClass = card.status === 'PENDING_SUBCITY' ? 'status-pending' : 
                                       card.status === 'APPROVED' ? 'status-approved' : '';
                    
                    cardDiv.innerHTML = `
                        <h3>ID Card ${card.id}</h3>
                        <p><strong>Card Number:</strong> ${card.card_number || 'N/A'}</p>
                        <p><strong>Status:</strong> <span class="status ${statusClass}">${card.status || 'N/A'}</span></p>
                        <p><strong>Kebele Approval Status:</strong> ${card.kebele_approval_status || 'N/A'}</p>
                        <p><strong>Kebele Pattern:</strong> ${card.kebele_pattern ? 'Applied' : 'Not Applied'}</p>
                        <p><strong>Issue Date:</strong> ${card.issue_date || 'N/A'}</p>
                        <p><strong>Expiry Date:</strong> ${card.expiry_date || 'N/A'}</p>
                    `;
                    
                    // Add approve button for PENDING_SUBCITY cards
                    if (card.status === 'PENDING_SUBCITY') {
                        const actionsDiv = document.createElement('div');
                        actionsDiv.className = 'actions';
                        
                        const approveButton = document.createElement('button');
                        approveButton.textContent = 'Approve';
                        approveButton.onclick = () => approveCard(card.id);
                        
                        actionsDiv.appendChild(approveButton);
                        cardDiv.appendChild(actionsDiv);
                    }
                    
                    resultsDiv.appendChild(cardDiv);
                });
            } catch (error) {
                console.error('Error fetching ID cards:', error);
                resultsDiv.innerHTML = `<p>Error: ${error.message}</p>`;
            }
        }
        
        // Function to approve an ID card
        async function approveCard(cardId) {
            try {
                const response = await fetch(`http://localhost:8000/api/tenant/${schema}/idcards/${cardId}/subcity_approve/`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Token ${token}`,
                        'Content-Type': 'application/json',
                        'X-Schema-Name': schema
                    },
                    body: JSON.stringify({
                        notes: 'Approved by subcity admin via test page'
                    })
                });
                
                if (!response.ok) {
                    throw new Error(`API error: ${response.status} ${response.statusText}`);
                }
                
                const data = await response.json();
                console.log('Approval Response:', data);
                
                alert(`ID Card ${cardId} approved successfully!`);
                
                // Refresh the cards
                fetchIDCards();
            } catch (error) {
                console.error('Error approving ID card:', error);
                alert(`Error approving ID card: ${error.message}`);
            }
        }
        
        // Add event listener to the fetch button
        document.getElementById('fetchCards').addEventListener('click', fetchIDCards);
        
        // Fetch cards on page load
        document.addEventListener('DOMContentLoaded', fetchIDCards);
    </script>
</body>
</html>
