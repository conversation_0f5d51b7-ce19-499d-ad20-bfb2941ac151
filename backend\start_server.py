import os
import sys
import subprocess

def start_server():
    """Start the Django development server."""
    print("Starting Django development server...")
    
    # Get the Python executable path
    python_executable = sys.executable
    
    # Start the server
    try:
        subprocess.run([python_executable, "manage.py", "runserver"], check=True)
    except subprocess.CalledProcessError as e:
        print(f"Error starting server: {e}")
    except KeyboardInterrupt:
        print("\nServer stopped.")

if __name__ == "__main__":
    start_server()
