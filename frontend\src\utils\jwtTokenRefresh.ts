/**
 * JWT Token Refresh Utility
 *
 * This utility provides functions for automatically refreshing JWT tokens.
 */

import { refreshJWTTokens, getAccessTokenForSchema } from '../services/tokenService';

// Function to get token expiry time
const getAccessTokenExpiry = (schema: string): number | null => {
  try {
    // Try to get from tokenStore in localStorage
    const tokenStoreStr = localStorage.getItem('jwtTokenStore');
    if (tokenStoreStr) {
      const tokenStore = JSON.parse(tokenStoreStr);
      if (tokenStore[schema]?.expiresAt) {
        return tokenStore[schema].expiresAt;
      }
    }

    // If not found, assume token expires in 15 minutes from now
    return Date.now() + 15 * 60 * 1000;
  } catch (error) {
    console.error('Error getting token expiry:', error);
    return null;
  }
};

// Token refresh timers
const refreshTimers: { [schema: string]: number } = {};

// Refresh buffer time (5 minutes in milliseconds)
const REFRESH_BUFFER_TIME = 5 * 60 * 1000;

/**
 * Initialize token refresh for a schema
 * @param schema The schema name
 */
export const initializeTokenRefresh = (schema: string): void => {
  // Clear any existing timer for this schema
  if (refreshTimers[schema]) {
    clearTimeout(refreshTimers[schema]);
    delete refreshTimers[schema];
  }

  // Get token expiry time
  const expiryTime = getAccessTokenExpiry(schema);
  if (!expiryTime) {
    console.warn(`No expiry time found for schema ${schema}`);
    return;
  }

  // Calculate time until refresh (expiry time - buffer time)
  const now = Date.now();
  const timeUntilRefresh = Math.max(0, expiryTime - now - REFRESH_BUFFER_TIME);

  console.log(`Setting up token refresh for schema ${schema} in ${Math.round(timeUntilRefresh / 1000)} seconds`);

  // Set up refresh timer
  refreshTimers[schema] = setTimeout(async () => {
    try {
      console.log(`Refreshing token for schema ${schema}`);
      const result = await refreshJWTTokens(schema);

      if (result && result.access_token) {
        console.log(`Token refreshed successfully for schema ${schema}`);

        // Set up the next refresh
        initializeTokenRefresh(schema);
      } else {
        console.error(`Failed to refresh token for schema ${schema}`);
      }
    } catch (error) {
      console.error(`Error refreshing token for schema ${schema}:`, error);
    }
  }, timeUntilRefresh);
};

/**
 * Initialize token refresh for all schemas with valid tokens
 */
export const initializeAllTokenRefreshes = (): void => {
  // Get schema from tokenService
  const schema = localStorage.getItem('jwt_schema') || localStorage.getItem('schema_name');
  if (schema) {
    // Check if we have a token for this schema
    const token = getAccessTokenForSchema(schema);
    if (token) {
      initializeTokenRefresh(schema);
    }
  }
};

/**
 * Clear all token refresh timers
 */
export const clearAllTokenRefreshes = (): void => {
  Object.keys(refreshTimers).forEach(schema => {
    clearTimeout(refreshTimers[schema]);
    delete refreshTimers[schema];
  });
};

export default {
  initializeTokenRefresh,
  initializeAllTokenRefreshes,
  clearAllTokenRefreshes
};
