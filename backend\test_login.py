import requests
import json

def test_login():
    """Test the login API endpoint."""
    print("\n=== Testing Login API ===")
    
    # Test login with valid credentials
    print("\n1. Testing login with valid credentials:")
    login_data = {
        "email": "<EMAIL>",  # This should be a superadmin in the public schema
        "password": "admin123"
    }
    
    response = requests.post(
        "http://localhost:8000/api/login/",
        headers={"Content-Type": "application/json"},
        json=login_data
    )
    
    print(f"Status Code: {response.status_code}")
    print(f"Response Headers: {json.dumps(dict(response.headers), indent=2)}")
    print(f"Response Content: {response.text}")
    
    try:
        data = response.json()
        print(f"Response JSON: {json.dumps(data, indent=2)}")
    except json.JSONDecodeError:
        print("Response is not valid JSON")

if __name__ == "__main__":
    test_login()
