import os
import django
import sys
from datetime import datetime, timedelta

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'neocamelot.settings')
django.setup()

from django.db import connection
from centers.models import Client, Center
from idcards.models import IDCardTemplate

# Get the schema name from command line argument
if len(sys.argv) > 1:
    schema_name = sys.argv[1]
else:
    print("Please provide a schema name as a command line argument")
    sys.exit(1)

# Set to public schema
connection.set_schema_to_public()

try:
    # Get the tenant by schema name
    tenant = Client.objects.get(schema_name=schema_name)
    print(f"Found tenant: {tenant.name} (ID: {tenant.id})")
    print(f"Schema name: {tenant.schema_name}")
    print(f"Schema type: {tenant.schema_type}")

    # Set the tenant for this request
    connection.set_tenant(tenant)

    # Get the center for this tenant
    centers = Center.objects.all()
    if centers.exists():
        center = centers.first()
        print(f"Using center: {center.name} (ID: {center.id})")

        # Create a template
        template = IDCardTemplate.objects.create(
            name="Standard ID Card",
            center=center,
            is_default=True,
            front_layout={
                "background_color": "#FFFFFF",
                "text_color": "#000000",
                "logo_position": "top-left",
                "fields": [
                    {"name": "full_name", "label": "Full Name", "x": 50, "y": 100},
                    {"name": "date_of_birth", "label": "Date of Birth", "x": 50, "y": 150},
                    {"name": "gender", "label": "Gender", "x": 50, "y": 200},
                    {"name": "address", "label": "Address", "x": 50, "y": 250}
                ]
            },
            back_layout={
                "background_color": "#F5F5F5",
                "text_color": "#000000",
                "signature_position": "bottom-right",
                "fields": [
                    {"name": "nationality", "label": "Nationality", "x": 50, "y": 100},
                    {"name": "issue_date", "label": "Issue Date", "x": 50, "y": 150},
                    {"name": "expiry_date", "label": "Expiry Date", "x": 50, "y": 200}
                ]
            }
        )

        print(f"Created template: {template.name} (ID: {template.id})")
        print(f"Is default: {template.is_default}")
        print(f"Created at: {template.created_at}")
        print(f"Front layout: {template.front_layout}")
        print(f"Back layout: {template.back_layout}")
    else:
        print("No centers found in this tenant. Cannot create template.")

except Client.DoesNotExist:
    print(f"Tenant with schema {schema_name} does not exist")

except Exception as e:
    print(f"Error: {str(e)}")
