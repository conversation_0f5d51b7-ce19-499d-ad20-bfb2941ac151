# DISABLED - This file conflicts with the main admin.py
# This file contains duplicate model definitions that conflict with the main models.py
# All admin registrations have been disabled to prevent conflicts

# from django.contrib import admin
# from .models_hierarchy import SubCity, <PERSON><PERSON><PERSON>, Ketena

# @admin.register(SubCity)
# class SubCityAdmin(admin.ModelAdmin):
#     list_display = ('name', 'code', 'city', 'is_active', 'created_at')
#     list_filter = ('is_active', 'city')
#     search_fields = ('name', 'code')
#     readonly_fields = ('created_at', 'updated_at')
#     fieldsets = (
#         ('Basic Information', {
#             'fields': ('name', 'code', 'city')
#         }),
#         ('Status', {
#             'fields': ('is_active',)
#         }),
#         ('System Information', {
#             'fields': ('created_at', 'updated_at'),
#             'classes': ('collapse',)
#         }),
#     )


# @admin.register(Kebele)
# class KebeleAdmin(admin.ModelAdmin):
#     list_display = ('name', 'code', 'sub_city', 'is_active', 'created_at')
#     list_filter = ('is_active', 'sub_city', 'sub_city__city')
#     search_fields = ('name', 'code')
#     readonly_fields = ('created_at', 'updated_at')
#     fieldsets = (
#         ('Basic Information', {
#             'fields': ('name', 'code', 'sub_city')
#         }),
#         ('Status', {
#             'fields': ('is_active',)
#         }),
#         ('System Information', {
#             'fields': ('created_at', 'updated_at'),
#             'classes': ('collapse',)
#         }),
#     )


# @admin.register(Ketena)
# class KetenaAdmin(admin.ModelAdmin):
#     list_display = ('name', 'code', 'kebele', 'is_active', 'created_at')
#     list_filter = ('is_active', 'kebele', 'kebele__sub_city')
#     search_fields = ('name', 'code')
#     readonly_fields = ('created_at', 'updated_at')
#     fieldsets = (
#         ('Basic Information', {
#             'fields': ('name', 'code', 'kebele')
#         }),
#         ('Status', {
#             'fields': ('is_active',)
#         }),
#         ('System Information', {
#             'fields': ('created_at', 'updated_at'),
#             'classes': ('collapse',)
#         }),
#     )
